<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" as="image" href="docs/supabase-dark.svg"/><link rel="preload" as="image" href="docs/supabase-light.svg"/><link rel="preload" as="image" href="docs/img/icons/react-icon.svg"/><link rel="preload" as="image" href="docs/img/icons/nextjs-icon.svg"/><link rel="preload" as="image" href="docs/img/icons/redwoodjs-icon.svg"/><link rel="preload" as="image" href="docs/img/icons/flutter-icon.svg"/><link rel="preload" as="image" href="docs/img/icons/kotlin-icon.svg"/><link rel="preload" as="image" href="docs/img/icons/svelte-icon.svg"/><link rel="preload" as="image" href="docs/img/icons/solidjs-icon.svg"/><link rel="preload" as="image" href="docs/img/icons/vuejs-icon.svg"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/d54bec00ea90a1ec.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/075d453bd5f7301f.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/db78b4e916a69931.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" data-precedence="next"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/webpack-ed7b7570a4b99b4f.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU"/><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/1c2ff98c-eb79f73b9d1f3503.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/6845-f5139f1ee8df1826.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/main-app-902d6f6c19f84a95.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/app/page-21b9296d3312bd31.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/app/error-e42d84999b0af1d4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/app/global-error-bcc8c40bf96d1ce4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><link rel="preload" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/6055.e0af3425405e56bd.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" as="script" fetchPriority="low"/><link rel="preload" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/4160.55505c2d7f0d4802.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" as="script" fetchPriority="low"/><link rel="preload" as="image" href="docs/img/icons/nuxt-icon.svg"/><link rel="preload" as="image" href="docs/img/icons/refine-icon.svg"/><link rel="preload" as="image" href="docs/img/icons/aws-rds-icon.svg"/><link rel="preload" as="image" href="docs/img/icons/auth0-icon-light.svg"/><link rel="preload" as="image" href="docs/img/icons/firebase-icon.svg"/><link rel="preload" as="image" href="docs/img/icons/heroku-icon.svg"/><link rel="preload" as="image" href="docs/img/icons/mssql-icon.svg"/><link rel="preload" as="image" href="docs/img/icons/mysql-icon.svg"/><link rel="preload" as="image" href="docs/img/icons/neon-icon-light.svg"/><link rel="preload" as="image" href="docs/img/icons/postgres-icon.svg"/><link rel="preload" as="image" href="docs/img/icons/render-icon.svg"/><link rel="preload" as="image" href="docs/img/icons/vercel-icon-light.svg"/><link rel="preload" href="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/6687.64fc1e00ed4f253f.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" as="script" fetchPriority="low"/><meta name="theme-color" content="#1E1E1E"/><title>Supabase Docs</title><meta name="description" content="Supabase is the Postgres development platform providing all the backend features you need to build a product."/><meta name="application-name" content="Supabase Docs"/><meta name="robots" content="index, follow"/><link rel="canonical" href="docs.html"/><meta property="og:title" content="Supabase Docs"/><meta property="og:description" content="Supabase is the Postgres development platform providing all the backend features you need to build a product."/><meta property="og:url" content="https://supabase.com/docs"/><meta property="og:image" content="https://supabase.com/docs/img/supabase-og-image.png"/><meta property="og:type" content="article"/><meta property="article:published_time" content="2025-07-31T19:13:36.294Z"/><meta property="article:modified_time" content="2025-07-31T19:13:36.295Z"/><meta property="article:author" content="Supabase"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:site" content="@supabase"/><meta name="twitter:creator" content="@supabase"/><meta name="twitter:title" content="Supabase Docs"/><meta name="twitter:description" content="Supabase is the Postgres development platform providing all the backend features you need to build a product."/><meta name="twitter:image" content="https://supabase.com/docs/img/supabase-og-image.png"/><link rel="shortcut icon" href="docs/favicon/favicon.ico"/><link rel="icon" href="docs/favicon/favicon.ico" type="image/x-icon"/><link rel="apple-touch-icon" href="docs/favicon/favicon.ico"/><link rel="apple-touch-icon-precomposed" href="docs/favicon/apple-icon-57x57.png" sizes="57x57"/><link rel="apple-touch-icon-precomposed" href="docs/favicon/apple-icon-60x60.png" sizes="60x60"/><link rel="apple-touch-icon-precomposed" href="docs/favicon/apple-icon-72x72.png" sizes="72x72"/><link rel="apple-touch-icon-precomposed" href="docs/favicon/apple-icon-76x76.png" sizes="76x76"/><link rel="apple-touch-icon-precomposed" href="docs/favicon/apple-icon-114x114.png" sizes="114x114"/><link rel="apple-touch-icon-precomposed" href="docs/favicon/apple-icon-120x120.png" sizes="120x120"/><link rel="apple-touch-icon-precomposed" href="docs/favicon/apple-icon-144x144.png" sizes="144x144"/><link rel="apple-touch-icon-precomposed" href="docs/favicon/apple-icon-152x152.png" sizes="152x152"/><link rel="icon" href="docs/favicon/favicon-16x16.png" type="image/png" sizes="16x16"/><link rel="icon" href="docs/favicon/favicon-32x32.png" type="image/png" sizes="32x32"/><link rel="icon" href="docs/favicon/favicon-48x48.png" type="image/png" sizes="48x48"/><link rel="icon" href="docs/favicon/favicon-96x96.png" type="image/png" sizes="96x96"/><link rel="icon" href="https://supabase.com/docs/favicon/favicon-128x128.png" type="image/png" sizes="128x128"/><link rel="icon" href="docs/favicon/favicon-180x180.png" type="image/png" sizes="180x180"/><link rel="icon" href="docs/favicon/favicon-196x196.png" type="image/png" sizes="196x196"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" noModule=""></script></head><body><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&true)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}if(e==='light'||e==='dark')d.style.colorScheme=e}catch(e){}}()</script><div class="flex flex-col"><div class="grow"><div class="flex flex-col h-full w-full"><div class="hidden lg:sticky w-full lg:flex top-0 left-0 right-0 z-50"><nav aria-label="top bar" class="w-full z-40 flex flex-col border-b backdrop-blur backdrop-filter bg bg-opacity-75"><div class="w-full px-5 lg:pl-10 flex justify-between h-[var(--header-height)] gap-3"><div class="hidden lg:flex h-full items-center justify-center gap-2"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="docs/supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="docs/supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a><div class="flex relative gap-2 justify-start items-end w-full h-full"><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center w-full flex justify-start h-full"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center px-6 space-x-2 h-[var(--header-height)]" dir="ltr"><li class="text-sm relative h-full"><a class="inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="" href="docs/guides/getting-started.html">Start</a></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«Rjimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«Rjimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Products<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«Rrimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«Rrimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Build<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R13imcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R13imcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Manage<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R1bimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R1bimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Reference<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rimcrlb»-trigger-radix-«R1jimcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rimcrlb»-content-radix-«R1jimcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Resources<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li></ul></div></nav></div></div><div class="w-full grow lg:w-auto flex gap-3 justify-between lg:justify-end items-center h-full"><div class="lg:hidden"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="docs/supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="docs/supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a></div><div class="flex gap-2 items-center"><button class="px-4 py-2 whitespace-nowrap border-input text-sm font-medium hover:bg-accent hover:text-accent-foreground ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group flex-grow md:w-44 xl:w-56 h-[30px] rounded-md pl-1.5 md:pl-2 pr-1 flex items-center justify-between bg-surface-100/75 text-foreground-lighter border hover:bg-opacity-100 hover:border-strong focus-visible:!outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600 transition" aria-haspopup="dialog" aria-expanded="false" aria-controls="command-menu-dialog-content"><div class="flex items-center space-x-2 text-foreground-muted"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><p class="flex text-sm pr-2">Search<span class="hidden xl:inline ml-1"> docs...</span></p></div><div class="hidden md:flex items-center space-x-1"><div aria-hidden="true" class="md:flex items-center justify-center h-full px-1 border rounded bg-surface-300 gap-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-command"><path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3"></path></svg><span class="text-[12px]">K</span></div></div></button><button title="Menu dropdown button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex lg:hidden border-default bg-surface-100/75 text-foreground-light rounded-md min-w-[30px] w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div><div class="hidden lg:flex items-center justify-end gap-3"><button title="Menu dropdown button" class="flex relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 text-foreground-light border-default w-[30px] min-w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30 hover:border-strong data-[state=open]:border-stronger hover:!bg-overlay-hover/50 bg-transparent" type="button" id="radix-«R2umcrlb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></nav></div><div class="flex flex-row h-full relative"><main id="docs-content-container" class="w-full transition-all ease-out relative lg:ml-0"><div class="flex flex-col sticky top-0"><div class="flex lg:hidden w-full top-0 left-0 right-0 z-50 sticky"><nav aria-label="top bar" class="w-full z-40 flex flex-col border-b backdrop-blur backdrop-filter bg bg-opacity-75"><div class="w-full px-5 lg:pl-10 flex justify-between h-[var(--header-height)] gap-3"><div class="hidden lg:flex h-full items-center justify-center gap-2"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="docs/supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="docs/supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a><div class="flex relative gap-2 justify-start items-end w-full h-full"><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center w-full flex justify-start h-full"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center px-6 space-x-2 h-[var(--header-height)]" dir="ltr"><li class="text-sm relative h-full"><a class="inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="" href="docs/guides/getting-started.html">Start</a></li><li class="text-sm relative h-full"><button id="radix-«Rij3qcrlb»-trigger-radix-«Rjij3qcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rij3qcrlb»-content-radix-«Rjij3qcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Products<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rij3qcrlb»-trigger-radix-«Rrij3qcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rij3qcrlb»-content-radix-«Rrij3qcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Build<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rij3qcrlb»-trigger-radix-«R13ij3qcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rij3qcrlb»-content-radix-«R13ij3qcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Manage<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rij3qcrlb»-trigger-radix-«R1bij3qcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rij3qcrlb»-content-radix-«R1bij3qcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Reference<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm relative h-full"><button id="radix-«Rij3qcrlb»-trigger-radix-«R1jij3qcrlb»" data-state="closed" aria-expanded="false" aria-controls="radix-«Rij3qcrlb»-content-radix-«R1jij3qcrlb»" class="group inline-flex items-center justify-center text-sm focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none hover:bg-accent data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 group w-max p-2 bg-transparent border-0 border-b-2 border-transparent font-normal rounded-none text-foreground-light hover:text-foreground data-[state=open]:!text-foreground data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground h-full focus-visible:rounded !shadow-none outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600" data-radix-collection-item="">Resources<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li></ul></div></nav></div></div><div class="w-full grow lg:w-auto flex gap-3 justify-between lg:justify-end items-center h-full"><div class="lg:hidden"><a class="relative justify-center cursor-pointer space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex shrink-0 items-center w-fit !bg-transparent !border-none !shadow-none" href="docs.html"><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="hidden dark:block !m-0" style="color:transparent" src="docs/supabase-dark.svg"/><img alt="Supabase wordmark" loading="eager" width="96" height="18" decoding="async" data-nimg="1" class="block dark:hidden !m-0" style="color:transparent" src="docs/supabase-light.svg"/><span class="font-mono text-sm font-medium text-brand-link mb-px">DOCS</span></a></div><div class="flex gap-2 items-center"><button class="px-4 py-2 whitespace-nowrap border-input text-sm font-medium hover:bg-accent hover:text-accent-foreground ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group flex-grow md:w-44 xl:w-56 h-[30px] rounded-md pl-1.5 md:pl-2 pr-1 flex items-center justify-between bg-surface-100/75 text-foreground-lighter border hover:bg-opacity-100 hover:border-strong focus-visible:!outline-4 focus-visible:outline-offset-1 focus-visible:outline-brand-600 transition" aria-haspopup="dialog" aria-expanded="false" aria-controls="command-menu-dialog-content"><div class="flex items-center space-x-2 text-foreground-muted"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg><p class="flex text-sm pr-2">Search<span class="hidden xl:inline ml-1"> docs...</span></p></div><div class="hidden md:flex items-center space-x-1"><div aria-hidden="true" class="md:flex items-center justify-center h-full px-1 border rounded bg-surface-300 gap-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-command"><path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3"></path></svg><span class="text-[12px]">K</span></div></div></button><button title="Menu dropdown button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover flex lg:hidden border-default bg-surface-100/75 text-foreground-light rounded-md min-w-[30px] w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div><div class="hidden lg:flex items-center justify-end gap-3"><button title="Menu dropdown button" class="flex relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border dark:bg-muted hover:bg-selection focus-visible:outline-brand-600 data-[state=open]:outline-brand-600 text-foreground-light border-default w-[30px] min-w-[30px] h-[30px] data-[state=open]:bg-overlay-hover/30 hover:border-strong data-[state=open]:border-stronger hover:!bg-overlay-hover/50 bg-transparent" type="button" id="radix-«R2uj3qcrlb»" aria-haspopup="menu" aria-expanded="false" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></nav></div><div class="sticky transition-all top-0 z-10 backdrop-blur backdrop-filter bg-background"></div><div class="grow"><article><div class="relative z-10 w-full bg-alternative border-b max-w-none mb-16 md:mb-12 xl:mb-0"><div class="max-w-7xl px-5 mx-auto py-8 sm:pb-16 sm:pt-12 xl:pt-16 flex flex-col xl:flex-row justify-between gap-12 xl:gap-12"><div class="flex flex-col sm:flex-row gap-4 sm:gap-8 items-start sm:items-center w-full max-w-xl xl:max-w-[33rem]"><div class="w-[60px] md:w-[150px] [&amp;_svg]" aria-hidden="true"><svg width="100%" height="100%" viewBox="0 0 250 228" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M124.951 5.03906V224.602" stroke="url(#paint0_radial_0_1)" stroke-opacity="0.5" stroke-width="0.7" pathLength="1" stroke-dashoffset="0px" stroke-dasharray="0px 1px"></path><path d="M125.25 191.383C167.828 191.383 202.344 156.867 202.344 114.289C202.344 71.7115 167.828 37.1953 125.25 37.1953C82.6724 37.1953 48.1562 71.7115 48.1562 114.289C48.1562 156.867 82.6724 191.383 125.25 191.383Z" stroke="hsl(var(--foreground-light))" stroke-opacity="0.1" stroke-width="0.7" stroke-miterlimit="10" stroke-linejoin="bevel" pathLength="1" stroke-dashoffset="0px" stroke-dasharray="0px 1px"></path><path d="M33.7782 58.8377C48.6666 58.8377 60.7361 46.7682 60.7361 31.8798C60.7361 16.9913 48.6666 4.92188 33.7782 4.92188C18.8898 4.92188 6.82031 16.9913 6.82031 31.8798C6.82031 46.7682 18.8898 58.8377 33.7782 58.8377Z" stroke="url(#paint1_linear_0_1)" stroke-opacity="0.5" stroke-width="0.7" stroke-miterlimit="10" stroke-linejoin="bevel" pathLength="1" stroke-dashoffset="0px" stroke-dasharray="0px 1px"></path><path d="M33.7782 223.664C48.6666 223.664 60.7361 211.594 60.7361 196.706C60.7361 181.817 48.6666 169.748 33.7782 169.748C18.8898 169.748 6.82031 181.817 6.82031 196.706C6.82031 211.594 18.8898 223.664 33.7782 223.664Z" stroke="url(#paint2_linear_0_1)" stroke-opacity="0.5" stroke-width="0.7" stroke-miterlimit="10" stroke-linejoin="bevel" pathLength="1" stroke-dashoffset="0px" stroke-dasharray="0px 1px"></path><path d="M216.724 58.8377C231.612 58.8377 243.681 46.7682 243.681 31.8798C243.681 16.9913 231.612 4.92188 216.724 4.92188C201.835 4.92188 189.766 16.9913 189.766 31.8798C189.766 46.7682 201.835 58.8377 216.724 58.8377Z" stroke="url(#paint3_linear_0_1)" stroke-opacity="0.5" stroke-width="0.7" stroke-miterlimit="10" stroke-linejoin="bevel" pathLength="1" stroke-dashoffset="0px" stroke-dasharray="0px 1px"></path><path d="M216.724 223.664C231.612 223.664 243.681 211.594 243.681 196.706C243.681 181.817 231.612 169.748 216.724 169.748C201.835 169.748 189.766 181.817 189.766 196.706C189.766 211.594 201.835 223.664 216.724 223.664Z" stroke="url(#paint4_linear_0_1)" stroke-opacity="0.5" stroke-width="0.7" stroke-miterlimit="10" stroke-linejoin="bevel" pathLength="1" stroke-dashoffset="0px" stroke-dasharray="0px 1px"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M209.29 226.982H40.6907C18.7665 226.982 1 209.51 1 187.949V40.0333C1 18.4722 18.7665 1 40.6907 1H209.29C231.214 1 248.981 18.4722 248.981 40.0333V187.969C248.981 209.51 231.214 226.982 209.29 226.982Z" stroke="url(#paint5_linear_0_1)" stroke-opacity="0.5" stroke-width="0.7" stroke-miterlimit="10" stroke-linejoin="bevel" pathLength="1" stroke-dashoffset="0px" stroke-dasharray="0px 1px"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M216.724 223.903H33.7779C18.719 223.903 6.5 211.684 6.5 196.625V31.9576C6.5 16.8987 18.719 4.67969 33.7779 4.67969H216.724C231.782 4.67969 244.001 16.8987 244.001 31.9576V196.625C244.001 211.704 231.802 223.903 216.724 223.903Z" stroke="url(#paint6_linear_0_1)" stroke-opacity="0.5" stroke-width="0.7" stroke-miterlimit="10" stroke-linejoin="bevel" pathLength="1" stroke-dashoffset="0px" stroke-dasharray="0px 1px"></path><path d="M60.7578 30.9199V197.027" stroke="url(#paint7_linear_0_1)" stroke-opacity="0.5" stroke-width="0.7" stroke-miterlimit="10" stroke-linejoin="bevel" pathLength="1" stroke-dashoffset="0px" stroke-dasharray="0px 1px"></path><path d="M125.252 5.01953V223.562" stroke="url(#paint8_radial_0_1)" stroke-opacity="0.5" stroke-width="0.7" stroke-miterlimit="10" stroke-linejoin="bevel" pathLength="1" stroke-dashoffset="0px" stroke-dasharray="0px 1px"></path><path d="M149.809 4.83984L13.5996 178.866" stroke="url(#paint9_linear_0_1)" stroke-opacity="0.5" stroke-width="0.7" stroke-miterlimit="10" stroke-linejoin="bevel" pathLength="1" stroke-dashoffset="0px" stroke-dasharray="0px 1px"></path><path d="M239.679 46.457L99.5703 223.643" stroke="url(#paint10_linear_0_1)" stroke-opacity="0.5" stroke-width="0.7" stroke-miterlimit="10" stroke-linejoin="bevel" pathLength="1" stroke-dashoffset="0px" stroke-dasharray="0px 1px"></path><path d="M189.766 30.9199V197.027" stroke="url(#paint11_linear_0_1)" stroke-opacity="0.5" stroke-width="0.7" stroke-miterlimit="10" stroke-linejoin="bevel" pathLength="1" stroke-dashoffset="0px" stroke-dasharray="0px 1px"></path><path d="M218.024 58.7344H32.498" stroke="url(#paint12_radial_0_1)" stroke-width="0.7" stroke-miterlimit="10" stroke-linejoin="bevel" pathLength="1" stroke-dashoffset="0px" stroke-dasharray="0px 1px"></path><path d="M6.76172 95.4141H243.743" stroke="url(#paint13_linear_0_1)" stroke-opacity="0.5" stroke-width="0.7" stroke-miterlimit="10" stroke-linejoin="bevel" pathLength="1" stroke-dashoffset="0px" stroke-dasharray="0px 1px"></path><path d="M6.76172 131.869H243.743" stroke="url(#paint14_linear_0_1)" stroke-opacity="0.5" stroke-width="0.7" stroke-miterlimit="10" stroke-linejoin="bevel" pathLength="1" stroke-dashoffset="0px" stroke-dasharray="0px 1px"></path><path d="M218.024 169.846H32.498" stroke="hsl(var(--foreground-light))" stroke-opacity="0.1" stroke-width="0.7" stroke-miterlimit="10" stroke-linejoin="bevel" pathLength="1" stroke-dashoffset="0px" stroke-dasharray="0px 1px"></path><path d="M126.559 4.6867L126.559 224.254" stroke="url(#paint15_linear_0_1)" stroke-opacity="0.5" stroke-width="0.7" stroke-miterlimit="10" stroke-linejoin="bevel" pathLength="1" stroke-dashoffset="0px" stroke-dasharray="0px 1px"></path><g filter="url(#filter0_d_0_1)"><path d="M125.751 174.003C125.833 179.464 134.091 181.807 137.49 177.53L188.786 112.985C194.84 105.367 189.41 94.1317 179.674 94.1317H126.964L127.046 52.5389C126.964 47.078 119.027 44.7344 115.628 49.0116L65.5801 113.556C59.5255 121.175 64.9558 132.41 74.6925 132.41H125.161L125.751 174.003Z" fill="url(#paint16_linear_0_1)" fill-opacity="0" shape-rendering="crispEdges"></path><path d="M126.964 94.6797L126.416 94.1306L126.498 52.5425C126.459 50.117 124.68 48.3327 122.422 47.6478C120.162 46.9621 117.586 47.432 116.059 49.3499C116.059 49.3508 116.058 49.3516 116.057 49.3525L66.0131 113.892L66.0091 113.897C60.2405 121.156 65.4135 131.862 74.6925 131.862H125.161L125.709 132.402L126.299 173.995L126.299 173.995M126.964 94.6797L125.751 174.003L126.299 173.995M126.964 94.6797H179.674C188.953 94.6797 194.126 105.386 188.357 112.644L137.061 177.189C135.539 179.104 132.883 179.583 130.531 178.892C128.18 178.202 126.336 176.41 126.299 173.995M126.964 94.6797L126.299 173.995" stroke="url(#paint17_linear_0_1)" stroke-width="1.09591" stroke-miterlimit="10" stroke-linejoin="bevel" shape-rendering="crispEdges" pathLength="1" stroke-dashoffset="0px" stroke-dasharray="0px 1px"></path></g><defs><filter id="filter0_d_0_1" x="58.6515" y="46.8027" width="137.062" height="141.703" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood><feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></feColorMatrix><feOffset dy="4.38364"></feOffset><feGaussianBlur stdDeviation="2.19182"></feGaussianBlur><feComposite in2="hardAlpha" operator="out"></feComposite><feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"></feColorMatrix><feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_0_1"></feBlend><feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_0_1" result="shape"></feBlend></filter><radialGradient id="paint0_radial_0_1" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(125.451 114.82) rotate(90) scale(109.781 0.5)"><stop stop-color="hsl(var(--foreground-light))" stop-opacity="0.5"></stop><stop offset="1" stop-color="hsl(var(--foreground-lighter))" stop-opacity="0"></stop></radialGradient><linearGradient id="paint1_linear_0_1" x1="38.9975" y1="58.9958" x2="18.999" y2="11.9994" gradientUnits="userSpaceOnUse"><stop stop-color="hsl(var(--foreground-light))" stop-opacity="0.5"></stop><stop offset="1" stop-color="hsl(var(--foreground-lighter))" stop-opacity="0"></stop></linearGradient><linearGradient id="paint2_linear_0_1" x1="43.9971" y1="168.986" x2="32.998" y2="204.984" gradientUnits="userSpaceOnUse"><stop stop-color="hsl(var(--foreground-light))" stop-opacity="0.5"></stop><stop offset="1" stop-color="hsl(var(--foreground-lighter))" stop-opacity="0"></stop></linearGradient><linearGradient id="paint3_linear_0_1" x1="221.943" y1="58.9958" x2="231.981" y2="13.9993" gradientUnits="userSpaceOnUse"><stop stop-color="hsl(var(--foreground-light))" stop-opacity="0.5"></stop><stop offset="1" stop-color="hsl(var(--foreground-lighter))" stop-opacity="0"></stop></linearGradient><linearGradient id="paint4_linear_0_1" x1="203.983" y1="168.986" x2="215.943" y2="204.984" gradientUnits="userSpaceOnUse"><stop stop-color="hsl(var(--foreground-light))" stop-opacity="0.5"></stop><stop offset="1" stop-color="hsl(var(--foreground-lighter))" stop-opacity="0"></stop></linearGradient><linearGradient id="paint5_linear_0_1" x1="121.402" y1="227.645" x2="121.402" y2="-7.94661" gradientUnits="userSpaceOnUse"><stop stop-color="hsl(var(--foreground-lighter))" stop-opacity="0"></stop><stop offset="0.489583" stop-color="hsl(var(--foreground-light))" stop-opacity="0.5"></stop><stop offset="1" stop-color="hsl(var(--foreground-lighter))" stop-opacity="0"></stop></linearGradient><linearGradient id="paint6_linear_0_1" x1="121.814" y1="224.545" x2="121.814" y2="-3.99932" gradientUnits="userSpaceOnUse"><stop stop-color="hsl(var(--foreground-lighter))" stop-opacity="0"></stop><stop offset="0.489583" stop-color="hsl(var(--foreground-light))" stop-opacity="0.5"></stop><stop offset="1" stop-color="hsl(var(--foreground-lighter))" stop-opacity="0"></stop></linearGradient><linearGradient id="paint7_linear_0_1" x1="60.7578" y1="30.3703" x2="60.7578" y2="199.061" gradientUnits="userSpaceOnUse"><stop stop-color="hsl(var(--foreground-lighter))" stop-opacity="0.1"></stop><stop offset="0.505208" stop-color="hsl(var(--foreground-light))" stop-opacity="0.5"></stop><stop offset="1" stop-color="hsl(var(--foreground-lighter))" stop-opacity="0.1"></stop></linearGradient><radialGradient id="paint8_radial_0_1" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(125.752 114.291) rotate(90) scale(109.271 0.5)"><stop stop-color="hsl(var(--foreground-light))" stop-opacity="0.5"></stop><stop offset="1" stop-color="hsl(var(--foreground-lighter))" stop-opacity="0"></stop></radialGradient><linearGradient id="paint9_linear_0_1" x1="6.99856" y1="0" x2="200" y2="6.31757" gradientUnits="userSpaceOnUse"><stop stop-color="hsl(var(--foreground-light))" stop-opacity="0.3"></stop><stop offset="1" stop-color="hsl(var(--foreground-lighter))" stop-opacity="0"></stop></linearGradient><linearGradient id="paint10_linear_0_1" x1="200" y1="100" x2="0" y2="200" gradientUnits="userSpaceOnUse"><stop stop-color="hsl(var(--foreground-light))" stop-opacity="0.3"></stop><stop offset="1" stop-color="hsl(var(--foreground-lighter))" stop-opacity="0"></stop></linearGradient><linearGradient id="paint11_linear_0_1" x1="189.766" y1="30.3703" x2="189.766" y2="199.061" gradientUnits="userSpaceOnUse"><stop stop-color="hsl(var(--foreground-lighter))" stop-opacity="0.1"></stop><stop offset="0.505208" stop-color="hsl(var(--foreground-light))" stop-opacity="0.5"></stop><stop offset="1" stop-color="hsl(var(--foreground-lighter))" stop-opacity="0.1"></stop></linearGradient><radialGradient id="paint12_radial_0_1" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(125.261 59.2344) rotate(90) scale(0.5 263.749)"><stop stop-color="hsl(var(--foreground-light))" stop-opacity="0.5"></stop><stop offset="1" stop-color="hsl(var(--foreground-lighter))" stop-opacity="0"></stop></radialGradient><linearGradient id="paint13_linear_0_1" x1="6.99998" y1="94.9749" x2="242.982" y2="94.9749" gradientUnits="userSpaceOnUse"><stop stop-color="hsl(var(--foreground-lighter))" stop-opacity="0.5"></stop><stop offset="0.505208" stop-color="hsl(var(--foreground-light))" stop-opacity="0.5"></stop><stop offset="1" stop-color="hsl(var(--foreground-lighter))" stop-opacity="0.5"></stop></linearGradient><linearGradient id="paint14_linear_0_1" x1="6.99998" y1="131.43" x2="242.982" y2="131.43" gradientUnits="userSpaceOnUse"><stop stop-color="hsl(var(--foreground-lighter))" stop-opacity="0.5"></stop><stop offset="0.505208" stop-color="hsl(var(--foreground-light))" stop-opacity="0.5"></stop><stop offset="1" stop-color="hsl(var(--foreground-lighter))" stop-opacity="0.5"></stop></linearGradient><linearGradient id="paint15_linear_0_1" x1="126.559" y1="224.98" x2="126.559" y2="1.99785" gradientUnits="userSpaceOnUse"><stop stop-color="hsl(var(--foreground-lighter))" stop-opacity="0.1"></stop><stop offset="0.505208" stop-color="hsl(var(--foreground-light))" stop-opacity="0.5"></stop><stop offset="1" stop-color="hsl(var(--foreground-lighter))" stop-opacity="0.1"></stop></linearGradient><linearGradient id="paint16_linear_0_1" x1="155.11" y1="46.8027" x2="126.979" y2="206.049" gradientUnits="userSpaceOnUse"><stop stop-color="#212121"></stop><stop offset="1" stop-color="white" stop-opacity="0.12"></stop></linearGradient><linearGradient id="paint17_linear_0_1" x1="191.328" y1="82.7684" x2="103.041" y2="175.889" gradientUnits="userSpaceOnUse"><stop stop-color="#868585"></stop><stop offset="0.208333" stop-color="#838383"></stop><stop offset="1" stop-color="#5A5A5A"></stop></linearGradient></defs></svg></div><div class="flex flex-col"><h1 class="m-0 mb-3 text-2xl sm:text-3xl text-foreground">Supabase Documentation</h1><p class="m-0 text-foreground-light">Learn how to get up and running with Supabase through tutorials, APIs and platform resources.</p></div></div><div class="w-full xl:max-w-[440px] -mb-40"><div class=" border bg-background relative overflow-hidden grid grid-cols-12 rounded-lg p-5 md:p-8 "><div class="col-span-full flex flex-col md:flex-row xl:flex-col justify-between gap-3"><div class="md:max-w-xs shrink w-fit xl:max-w-none"><div class="flex items-center gap-3 mb-3"><div class="shrink-0 bg-brand-200 dark:bg-brand-400 border border-brand-300 dark:border-brand-400 w-8 h-8 flex items-center justify-center rounded"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play text-brand-600 w-4" aria-hidden="true"><polygon points="6 3 20 12 6 21 6 3"></polygon></svg></div><h2 class="text-2xl m-0 text-foreground">Getting Started</h2></div><p class="text-foreground-light text-sm">Set up and connect a database in just a few minutes.</p></div><div class="shrink-0"><div class="flex flex-wrap md:grid md:grid-cols-5 gap-2 sm:gap-3"><a class="no-underline" href="docs/guides/getting-started/quickstarts/reactjs.html"><div class="relative group" data-tip="ReactJS"><div class="peer relative flex flex-col gap-6"><div class="flex items-center false"><div class="relative flex items-center justify-center shrink-0 h-16 w-16 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><img class="w-8" src="docs/img/icons/react-icon.svg" alt="ReactJS Icon"/></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div><div class="__react_component_tooltip tb0c2189a-d7a8-45be-abc2-b8922e9d19c9 place-top type-dark" id="tb0c2189a-d7a8-45be-abc2-b8922e9d19c9" data-id="tooltip"><style aria-hidden="true">.tb0c2189a-d7a8-45be-abc2-b8922e9d19c9 {
	    color: #fff;
	    background: #222;
	    border: 1px solid transparent;
	    border-radius: undefinedpx;
	    padding: 8px 21px;
  	}

  	.tb0c2189a-d7a8-45be-abc2-b8922e9d19c9.place-top {
        margin-top: -10px;
    }
    .tb0c2189a-d7a8-45be-abc2-b8922e9d19c9.place-top::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: 2;
        width: 20px;
        height: 12px;
    }
    .tb0c2189a-d7a8-45be-abc2-b8922e9d19c9.place-top::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        bottom: -6px;
        left: 50%;
        margin-left: -6px;
        transform: rotate(135deg);
    }

    .tb0c2189a-d7a8-45be-abc2-b8922e9d19c9.place-bottom {
        margin-top: 10px;
    }
    .tb0c2189a-d7a8-45be-abc2-b8922e9d19c9.place-bottom::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 18px;
        height: 10px;
    }
    .tb0c2189a-d7a8-45be-abc2-b8922e9d19c9.place-bottom::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        top: -6px;
        left: 50%;
        margin-left: -6px;
        transform: rotate(45deg);
    }

    .tb0c2189a-d7a8-45be-abc2-b8922e9d19c9.place-left {
        margin-left: -10px;
    }
    .tb0c2189a-d7a8-45be-abc2-b8922e9d19c9.place-left::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 10px;
        height: 18px;
    }
    .tb0c2189a-d7a8-45be-abc2-b8922e9d19c9.place-left::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        right: -6px;
        top: 50%;
        margin-top: -6px;
        transform: rotate(45deg);
    }

    .tb0c2189a-d7a8-45be-abc2-b8922e9d19c9.place-right {
        margin-left: 10px;
    }
    .tb0c2189a-d7a8-45be-abc2-b8922e9d19c9.place-right::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 10px;
        height: 18px;
    }
    .tb0c2189a-d7a8-45be-abc2-b8922e9d19c9.place-right::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        left: -6px;
        top: 50%;
        margin-top: -6px;
        transform: rotate(-135deg);
    }
  </style></div></a><a class="no-underline" href="docs/guides/getting-started/quickstarts/nextjs.html"><div class="relative group" data-tip="Next.js"><div class="peer relative flex flex-col gap-6"><div class="flex items-center false"><div class="relative flex items-center justify-center shrink-0 h-16 w-16 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><img class="w-8" src="docs/img/icons/nextjs-icon.svg" alt="Next.js Icon"/></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div><div class="__react_component_tooltip t195febf2-369f-4e5f-9c5e-a456f3995a36 place-top type-dark" id="t195febf2-369f-4e5f-9c5e-a456f3995a36" data-id="tooltip"><style aria-hidden="true">.t195febf2-369f-4e5f-9c5e-a456f3995a36 {
	    color: #fff;
	    background: #222;
	    border: 1px solid transparent;
	    border-radius: undefinedpx;
	    padding: 8px 21px;
  	}

  	.t195febf2-369f-4e5f-9c5e-a456f3995a36.place-top {
        margin-top: -10px;
    }
    .t195febf2-369f-4e5f-9c5e-a456f3995a36.place-top::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: 2;
        width: 20px;
        height: 12px;
    }
    .t195febf2-369f-4e5f-9c5e-a456f3995a36.place-top::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        bottom: -6px;
        left: 50%;
        margin-left: -6px;
        transform: rotate(135deg);
    }

    .t195febf2-369f-4e5f-9c5e-a456f3995a36.place-bottom {
        margin-top: 10px;
    }
    .t195febf2-369f-4e5f-9c5e-a456f3995a36.place-bottom::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 18px;
        height: 10px;
    }
    .t195febf2-369f-4e5f-9c5e-a456f3995a36.place-bottom::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        top: -6px;
        left: 50%;
        margin-left: -6px;
        transform: rotate(45deg);
    }

    .t195febf2-369f-4e5f-9c5e-a456f3995a36.place-left {
        margin-left: -10px;
    }
    .t195febf2-369f-4e5f-9c5e-a456f3995a36.place-left::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 10px;
        height: 18px;
    }
    .t195febf2-369f-4e5f-9c5e-a456f3995a36.place-left::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        right: -6px;
        top: 50%;
        margin-top: -6px;
        transform: rotate(45deg);
    }

    .t195febf2-369f-4e5f-9c5e-a456f3995a36.place-right {
        margin-left: 10px;
    }
    .t195febf2-369f-4e5f-9c5e-a456f3995a36.place-right::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 10px;
        height: 18px;
    }
    .t195febf2-369f-4e5f-9c5e-a456f3995a36.place-right::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        left: -6px;
        top: 50%;
        margin-top: -6px;
        transform: rotate(-135deg);
    }
  </style></div></a><a class="no-underline" href="docs/guides/getting-started/quickstarts/redwoodjs.html"><div class="relative group" data-tip="RedwoodJS"><div class="peer relative flex flex-col gap-6"><div class="flex items-center false"><div class="relative flex items-center justify-center shrink-0 h-16 w-16 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><img class="w-8" src="docs/img/icons/redwoodjs-icon.svg" alt="RedwoodJS Icon"/></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div><div class="__react_component_tooltip tafe09e45-3edb-41b1-953f-99f4212bcf9f place-top type-dark" id="tafe09e45-3edb-41b1-953f-99f4212bcf9f" data-id="tooltip"><style aria-hidden="true">.tafe09e45-3edb-41b1-953f-99f4212bcf9f {
	    color: #fff;
	    background: #222;
	    border: 1px solid transparent;
	    border-radius: undefinedpx;
	    padding: 8px 21px;
  	}

  	.tafe09e45-3edb-41b1-953f-99f4212bcf9f.place-top {
        margin-top: -10px;
    }
    .tafe09e45-3edb-41b1-953f-99f4212bcf9f.place-top::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: 2;
        width: 20px;
        height: 12px;
    }
    .tafe09e45-3edb-41b1-953f-99f4212bcf9f.place-top::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        bottom: -6px;
        left: 50%;
        margin-left: -6px;
        transform: rotate(135deg);
    }

    .tafe09e45-3edb-41b1-953f-99f4212bcf9f.place-bottom {
        margin-top: 10px;
    }
    .tafe09e45-3edb-41b1-953f-99f4212bcf9f.place-bottom::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 18px;
        height: 10px;
    }
    .tafe09e45-3edb-41b1-953f-99f4212bcf9f.place-bottom::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        top: -6px;
        left: 50%;
        margin-left: -6px;
        transform: rotate(45deg);
    }

    .tafe09e45-3edb-41b1-953f-99f4212bcf9f.place-left {
        margin-left: -10px;
    }
    .tafe09e45-3edb-41b1-953f-99f4212bcf9f.place-left::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 10px;
        height: 18px;
    }
    .tafe09e45-3edb-41b1-953f-99f4212bcf9f.place-left::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        right: -6px;
        top: 50%;
        margin-top: -6px;
        transform: rotate(45deg);
    }

    .tafe09e45-3edb-41b1-953f-99f4212bcf9f.place-right {
        margin-left: 10px;
    }
    .tafe09e45-3edb-41b1-953f-99f4212bcf9f.place-right::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 10px;
        height: 18px;
    }
    .tafe09e45-3edb-41b1-953f-99f4212bcf9f.place-right::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        left: -6px;
        top: 50%;
        margin-top: -6px;
        transform: rotate(-135deg);
    }
  </style></div></a><a class="no-underline" href="docs/guides/getting-started/quickstarts/flutter.html"><div class="relative group" data-tip="Flutter"><div class="peer relative flex flex-col gap-6"><div class="flex items-center false"><div class="relative flex items-center justify-center shrink-0 h-16 w-16 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><img class="w-8" src="docs/img/icons/flutter-icon.svg" alt="Flutter Icon"/></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div><div class="__react_component_tooltip t04e45e41-61b7-4053-9086-8c619efe675f place-top type-dark" id="t04e45e41-61b7-4053-9086-8c619efe675f" data-id="tooltip"><style aria-hidden="true">.t04e45e41-61b7-4053-9086-8c619efe675f {
	    color: #fff;
	    background: #222;
	    border: 1px solid transparent;
	    border-radius: undefinedpx;
	    padding: 8px 21px;
  	}

  	.t04e45e41-61b7-4053-9086-8c619efe675f.place-top {
        margin-top: -10px;
    }
    .t04e45e41-61b7-4053-9086-8c619efe675f.place-top::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: 2;
        width: 20px;
        height: 12px;
    }
    .t04e45e41-61b7-4053-9086-8c619efe675f.place-top::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        bottom: -6px;
        left: 50%;
        margin-left: -6px;
        transform: rotate(135deg);
    }

    .t04e45e41-61b7-4053-9086-8c619efe675f.place-bottom {
        margin-top: 10px;
    }
    .t04e45e41-61b7-4053-9086-8c619efe675f.place-bottom::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 18px;
        height: 10px;
    }
    .t04e45e41-61b7-4053-9086-8c619efe675f.place-bottom::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        top: -6px;
        left: 50%;
        margin-left: -6px;
        transform: rotate(45deg);
    }

    .t04e45e41-61b7-4053-9086-8c619efe675f.place-left {
        margin-left: -10px;
    }
    .t04e45e41-61b7-4053-9086-8c619efe675f.place-left::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 10px;
        height: 18px;
    }
    .t04e45e41-61b7-4053-9086-8c619efe675f.place-left::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        right: -6px;
        top: 50%;
        margin-top: -6px;
        transform: rotate(45deg);
    }

    .t04e45e41-61b7-4053-9086-8c619efe675f.place-right {
        margin-left: 10px;
    }
    .t04e45e41-61b7-4053-9086-8c619efe675f.place-right::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 10px;
        height: 18px;
    }
    .t04e45e41-61b7-4053-9086-8c619efe675f.place-right::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        left: -6px;
        top: 50%;
        margin-top: -6px;
        transform: rotate(-135deg);
    }
  </style></div></a><a class="no-underline" href="docs/guides/getting-started/quickstarts/kotlin.html"><div class="relative group" data-tip="Android Kotlin"><div class="peer relative flex flex-col gap-6"><div class="flex items-center false"><div class="relative flex items-center justify-center shrink-0 h-16 w-16 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><img class="w-8" src="docs/img/icons/kotlin-icon.svg" alt="Android Kotlin Icon"/></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div><div class="__react_component_tooltip t77ba32fb-7451-4121-a4e7-9d81a070d350 place-top type-dark" id="t77ba32fb-7451-4121-a4e7-9d81a070d350" data-id="tooltip"><style aria-hidden="true">.t77ba32fb-7451-4121-a4e7-9d81a070d350 {
	    color: #fff;
	    background: #222;
	    border: 1px solid transparent;
	    border-radius: undefinedpx;
	    padding: 8px 21px;
  	}

  	.t77ba32fb-7451-4121-a4e7-9d81a070d350.place-top {
        margin-top: -10px;
    }
    .t77ba32fb-7451-4121-a4e7-9d81a070d350.place-top::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: 2;
        width: 20px;
        height: 12px;
    }
    .t77ba32fb-7451-4121-a4e7-9d81a070d350.place-top::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        bottom: -6px;
        left: 50%;
        margin-left: -6px;
        transform: rotate(135deg);
    }

    .t77ba32fb-7451-4121-a4e7-9d81a070d350.place-bottom {
        margin-top: 10px;
    }
    .t77ba32fb-7451-4121-a4e7-9d81a070d350.place-bottom::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 18px;
        height: 10px;
    }
    .t77ba32fb-7451-4121-a4e7-9d81a070d350.place-bottom::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        top: -6px;
        left: 50%;
        margin-left: -6px;
        transform: rotate(45deg);
    }

    .t77ba32fb-7451-4121-a4e7-9d81a070d350.place-left {
        margin-left: -10px;
    }
    .t77ba32fb-7451-4121-a4e7-9d81a070d350.place-left::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 10px;
        height: 18px;
    }
    .t77ba32fb-7451-4121-a4e7-9d81a070d350.place-left::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        right: -6px;
        top: 50%;
        margin-top: -6px;
        transform: rotate(45deg);
    }

    .t77ba32fb-7451-4121-a4e7-9d81a070d350.place-right {
        margin-left: 10px;
    }
    .t77ba32fb-7451-4121-a4e7-9d81a070d350.place-right::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 10px;
        height: 18px;
    }
    .t77ba32fb-7451-4121-a4e7-9d81a070d350.place-right::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        left: -6px;
        top: 50%;
        margin-top: -6px;
        transform: rotate(-135deg);
    }
  </style></div></a><a class="no-underline" href="docs/guides/getting-started/quickstarts/sveltekit.html"><div class="relative group" data-tip="SvelteKit"><div class="peer relative flex flex-col gap-6"><div class="flex items-center false"><div class="relative flex items-center justify-center shrink-0 h-16 w-16 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><img class="w-8" src="docs/img/icons/svelte-icon.svg" alt="SvelteKit Icon"/></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div><div class="__react_component_tooltip tec1568bb-989e-48d3-8ce1-0dd2231a9e59 place-top type-dark" id="tec1568bb-989e-48d3-8ce1-0dd2231a9e59" data-id="tooltip"><style aria-hidden="true">.tec1568bb-989e-48d3-8ce1-0dd2231a9e59 {
	    color: #fff;
	    background: #222;
	    border: 1px solid transparent;
	    border-radius: undefinedpx;
	    padding: 8px 21px;
  	}

  	.tec1568bb-989e-48d3-8ce1-0dd2231a9e59.place-top {
        margin-top: -10px;
    }
    .tec1568bb-989e-48d3-8ce1-0dd2231a9e59.place-top::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: 2;
        width: 20px;
        height: 12px;
    }
    .tec1568bb-989e-48d3-8ce1-0dd2231a9e59.place-top::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        bottom: -6px;
        left: 50%;
        margin-left: -6px;
        transform: rotate(135deg);
    }

    .tec1568bb-989e-48d3-8ce1-0dd2231a9e59.place-bottom {
        margin-top: 10px;
    }
    .tec1568bb-989e-48d3-8ce1-0dd2231a9e59.place-bottom::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 18px;
        height: 10px;
    }
    .tec1568bb-989e-48d3-8ce1-0dd2231a9e59.place-bottom::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        top: -6px;
        left: 50%;
        margin-left: -6px;
        transform: rotate(45deg);
    }

    .tec1568bb-989e-48d3-8ce1-0dd2231a9e59.place-left {
        margin-left: -10px;
    }
    .tec1568bb-989e-48d3-8ce1-0dd2231a9e59.place-left::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 10px;
        height: 18px;
    }
    .tec1568bb-989e-48d3-8ce1-0dd2231a9e59.place-left::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        right: -6px;
        top: 50%;
        margin-top: -6px;
        transform: rotate(45deg);
    }

    .tec1568bb-989e-48d3-8ce1-0dd2231a9e59.place-right {
        margin-left: 10px;
    }
    .tec1568bb-989e-48d3-8ce1-0dd2231a9e59.place-right::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 10px;
        height: 18px;
    }
    .tec1568bb-989e-48d3-8ce1-0dd2231a9e59.place-right::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        left: -6px;
        top: 50%;
        margin-top: -6px;
        transform: rotate(-135deg);
    }
  </style></div></a><a class="no-underline" href="docs/guides/getting-started/quickstarts/solidjs.html"><div class="relative group" data-tip="SolidJS"><div class="peer relative flex flex-col gap-6"><div class="flex items-center false"><div class="relative flex items-center justify-center shrink-0 h-16 w-16 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><img class="w-8" src="docs/img/icons/solidjs-icon.svg" alt="SolidJS Icon"/></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div><div class="__react_component_tooltip t1901e575-6fe0-463a-b65b-373704c32473 place-top type-dark" id="t1901e575-6fe0-463a-b65b-373704c32473" data-id="tooltip"><style aria-hidden="true">.t1901e575-6fe0-463a-b65b-373704c32473 {
	    color: #fff;
	    background: #222;
	    border: 1px solid transparent;
	    border-radius: undefinedpx;
	    padding: 8px 21px;
  	}

  	.t1901e575-6fe0-463a-b65b-373704c32473.place-top {
        margin-top: -10px;
    }
    .t1901e575-6fe0-463a-b65b-373704c32473.place-top::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: 2;
        width: 20px;
        height: 12px;
    }
    .t1901e575-6fe0-463a-b65b-373704c32473.place-top::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        bottom: -6px;
        left: 50%;
        margin-left: -6px;
        transform: rotate(135deg);
    }

    .t1901e575-6fe0-463a-b65b-373704c32473.place-bottom {
        margin-top: 10px;
    }
    .t1901e575-6fe0-463a-b65b-373704c32473.place-bottom::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 18px;
        height: 10px;
    }
    .t1901e575-6fe0-463a-b65b-373704c32473.place-bottom::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        top: -6px;
        left: 50%;
        margin-left: -6px;
        transform: rotate(45deg);
    }

    .t1901e575-6fe0-463a-b65b-373704c32473.place-left {
        margin-left: -10px;
    }
    .t1901e575-6fe0-463a-b65b-373704c32473.place-left::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 10px;
        height: 18px;
    }
    .t1901e575-6fe0-463a-b65b-373704c32473.place-left::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        right: -6px;
        top: 50%;
        margin-top: -6px;
        transform: rotate(45deg);
    }

    .t1901e575-6fe0-463a-b65b-373704c32473.place-right {
        margin-left: 10px;
    }
    .t1901e575-6fe0-463a-b65b-373704c32473.place-right::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 10px;
        height: 18px;
    }
    .t1901e575-6fe0-463a-b65b-373704c32473.place-right::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        left: -6px;
        top: 50%;
        margin-top: -6px;
        transform: rotate(-135deg);
    }
  </style></div></a><a class="no-underline" href="docs/guides/getting-started/quickstarts/vue.html"><div class="relative group" data-tip="Vue"><div class="peer relative flex flex-col gap-6"><div class="flex items-center false"><div class="relative flex items-center justify-center shrink-0 h-16 w-16 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><img class="w-8" src="docs/img/icons/vuejs-icon.svg" alt="Vue Icon"/></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div><div class="__react_component_tooltip t617e26b9-8256-4c89-bcb0-b50853de807f place-top type-dark" id="t617e26b9-8256-4c89-bcb0-b50853de807f" data-id="tooltip"><style aria-hidden="true">.t617e26b9-8256-4c89-bcb0-b50853de807f {
	    color: #fff;
	    background: #222;
	    border: 1px solid transparent;
	    border-radius: undefinedpx;
	    padding: 8px 21px;
  	}

  	.t617e26b9-8256-4c89-bcb0-b50853de807f.place-top {
        margin-top: -10px;
    }
    .t617e26b9-8256-4c89-bcb0-b50853de807f.place-top::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: 2;
        width: 20px;
        height: 12px;
    }
    .t617e26b9-8256-4c89-bcb0-b50853de807f.place-top::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        bottom: -6px;
        left: 50%;
        margin-left: -6px;
        transform: rotate(135deg);
    }

    .t617e26b9-8256-4c89-bcb0-b50853de807f.place-bottom {
        margin-top: 10px;
    }
    .t617e26b9-8256-4c89-bcb0-b50853de807f.place-bottom::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 18px;
        height: 10px;
    }
    .t617e26b9-8256-4c89-bcb0-b50853de807f.place-bottom::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        top: -6px;
        left: 50%;
        margin-left: -6px;
        transform: rotate(45deg);
    }

    .t617e26b9-8256-4c89-bcb0-b50853de807f.place-left {
        margin-left: -10px;
    }
    .t617e26b9-8256-4c89-bcb0-b50853de807f.place-left::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 10px;
        height: 18px;
    }
    .t617e26b9-8256-4c89-bcb0-b50853de807f.place-left::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        right: -6px;
        top: 50%;
        margin-top: -6px;
        transform: rotate(45deg);
    }

    .t617e26b9-8256-4c89-bcb0-b50853de807f.place-right {
        margin-left: 10px;
    }
    .t617e26b9-8256-4c89-bcb0-b50853de807f.place-right::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 10px;
        height: 18px;
    }
    .t617e26b9-8256-4c89-bcb0-b50853de807f.place-right::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        left: -6px;
        top: 50%;
        margin-top: -6px;
        transform: rotate(-135deg);
    }
  </style></div></a><a class="no-underline" href="docs/guides/getting-started/quickstarts/nuxtjs.html"><div class="relative group" data-tip="Nuxt"><div class="peer relative flex flex-col gap-6"><div class="flex items-center false"><div class="relative flex items-center justify-center shrink-0 h-16 w-16 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><img class="w-8" src="docs/img/icons/nuxt-icon.svg" alt="Nuxt Icon"/></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div><div class="__react_component_tooltip t30308641-7e61-404d-871e-6fecf167db14 place-top type-dark" id="t30308641-7e61-404d-871e-6fecf167db14" data-id="tooltip"><style aria-hidden="true">.t30308641-7e61-404d-871e-6fecf167db14 {
	    color: #fff;
	    background: #222;
	    border: 1px solid transparent;
	    border-radius: undefinedpx;
	    padding: 8px 21px;
  	}

  	.t30308641-7e61-404d-871e-6fecf167db14.place-top {
        margin-top: -10px;
    }
    .t30308641-7e61-404d-871e-6fecf167db14.place-top::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: 2;
        width: 20px;
        height: 12px;
    }
    .t30308641-7e61-404d-871e-6fecf167db14.place-top::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        bottom: -6px;
        left: 50%;
        margin-left: -6px;
        transform: rotate(135deg);
    }

    .t30308641-7e61-404d-871e-6fecf167db14.place-bottom {
        margin-top: 10px;
    }
    .t30308641-7e61-404d-871e-6fecf167db14.place-bottom::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 18px;
        height: 10px;
    }
    .t30308641-7e61-404d-871e-6fecf167db14.place-bottom::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        top: -6px;
        left: 50%;
        margin-left: -6px;
        transform: rotate(45deg);
    }

    .t30308641-7e61-404d-871e-6fecf167db14.place-left {
        margin-left: -10px;
    }
    .t30308641-7e61-404d-871e-6fecf167db14.place-left::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 10px;
        height: 18px;
    }
    .t30308641-7e61-404d-871e-6fecf167db14.place-left::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        right: -6px;
        top: 50%;
        margin-top: -6px;
        transform: rotate(45deg);
    }

    .t30308641-7e61-404d-871e-6fecf167db14.place-right {
        margin-left: 10px;
    }
    .t30308641-7e61-404d-871e-6fecf167db14.place-right::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 10px;
        height: 18px;
    }
    .t30308641-7e61-404d-871e-6fecf167db14.place-right::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        left: -6px;
        top: 50%;
        margin-top: -6px;
        transform: rotate(-135deg);
    }
  </style></div></a><a class="no-underline" href="docs/guides/getting-started/quickstarts/refine.html"><div class="relative group" data-tip="refine"><div class="peer relative flex flex-col gap-6"><div class="flex items-center false"><div class="relative flex items-center justify-center shrink-0 h-16 w-16 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><img class="w-8" src="docs/img/icons/refine-icon.svg" alt="refine Icon"/></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div><div class="__react_component_tooltip te4c62f29-776e-46fc-aa49-937f8cc9ea0f place-top type-dark" id="te4c62f29-776e-46fc-aa49-937f8cc9ea0f" data-id="tooltip"><style aria-hidden="true">.te4c62f29-776e-46fc-aa49-937f8cc9ea0f {
	    color: #fff;
	    background: #222;
	    border: 1px solid transparent;
	    border-radius: undefinedpx;
	    padding: 8px 21px;
  	}

  	.te4c62f29-776e-46fc-aa49-937f8cc9ea0f.place-top {
        margin-top: -10px;
    }
    .te4c62f29-776e-46fc-aa49-937f8cc9ea0f.place-top::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: 2;
        width: 20px;
        height: 12px;
    }
    .te4c62f29-776e-46fc-aa49-937f8cc9ea0f.place-top::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        bottom: -6px;
        left: 50%;
        margin-left: -6px;
        transform: rotate(135deg);
    }

    .te4c62f29-776e-46fc-aa49-937f8cc9ea0f.place-bottom {
        margin-top: 10px;
    }
    .te4c62f29-776e-46fc-aa49-937f8cc9ea0f.place-bottom::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 18px;
        height: 10px;
    }
    .te4c62f29-776e-46fc-aa49-937f8cc9ea0f.place-bottom::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        top: -6px;
        left: 50%;
        margin-left: -6px;
        transform: rotate(45deg);
    }

    .te4c62f29-776e-46fc-aa49-937f8cc9ea0f.place-left {
        margin-left: -10px;
    }
    .te4c62f29-776e-46fc-aa49-937f8cc9ea0f.place-left::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 10px;
        height: 18px;
    }
    .te4c62f29-776e-46fc-aa49-937f8cc9ea0f.place-left::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        right: -6px;
        top: 50%;
        margin-top: -6px;
        transform: rotate(45deg);
    }

    .te4c62f29-776e-46fc-aa49-937f8cc9ea0f.place-right {
        margin-left: 10px;
    }
    .te4c62f29-776e-46fc-aa49-937f8cc9ea0f.place-right::before {
        content: "";
        background-color: inherit;
        position: absolute;
        z-index: -1;
        width: 10px;
        height: 18px;
    }
    .te4c62f29-776e-46fc-aa49-937f8cc9ea0f.place-right::after {
        content: "";
        position: absolute;
        width: 10px;
        height: 10px;
        border-top-right-radius: undefinedpx;
        border: 1px solid transparent;
        background-color: #222;
        z-index: -2;
        left: -6px;
        top: 50%;
        margin-top: -6px;
        transform: rotate(-135deg);
    }
  </style></div></a></div><a class="group w-fit rounded-full border px-3 py-1 flex gap-2 items-center text-foreground-light text-sm hover:border-brand hover:text-brand focus-visible:text-brand transition-colors mt-6" href="docs/guides/getting-started/ai-prompts.html"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-sparkles"><path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"></path><path d="M20 3v4"></path><path d="M22 5h-4"></path><path d="M4 17v2"></path><path d="M5 18H3"></path></svg>Start with Supabase AI prompts<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right group-hover:translate-x-1 transition-transform"><path d="m9 18 6-6-6-6"></path></svg></a></div></div></div></div></div></div><div class="max-w-7xl px-5 mx-auto py-8"><div class="relative transition-all ease-out duration-150 "><div class="prose max-w-none"><div class="flex flex-col"><h2 id="products">Products</h2><ul class="grid grid-cols-12 gap-6 not-prose [&amp;_svg]:text-brand-600"><li class="col-span-12 md:col-span-6"><a href="docs/guides/database/overview.html"><div class="relative h-full group cursor-pointer overflow-hidden border rounded-lg text-left hover:border-strong bg-surface-75 transition"><div class="px-8 pb-8 relative flex flex-col h-full gap-6 pt-8"><div class="flex items-center gap-3"><div class="shrink-0"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 16 16" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.5 2.99915C2.5 2.17072 3.17157 1.49915 4 1.49915H12C12.8284 1.49915 13.5 2.17072 13.5 2.99915V4.99915C13.5 5.53212 13.222 6.00017 12.8032 6.26623V9.73377C13.222 9.99983 13.5 10.4679 13.5 11.0009V13.0009C13.5 13.8293 12.8284 14.5009 12 14.5009H4C3.17157 14.5009 2.5 13.8293 2.5 13.0009V11.0009C2.5 10.4615 2.78461 9.98872 3.21183 9.72437V6.27563C2.78461 6.01128 2.5 5.53845 2.5 4.99915V2.99915ZM12.0158 5.4989H3.98422C3.71538 5.49057 3.5 5.27001 3.5 4.99915V2.99915C3.5 2.723 3.72386 2.49915 4 2.49915H12C12.2761 2.49915 12.5 2.723 12.5 2.99915V4.99915C12.5 5.27001 12.2846 5.49057 12.0158 5.4989ZM4.21183 6.49915V9.4989H11.8032V6.49915H4.21183ZM4 10.5009C3.72386 10.5009 3.5 10.7247 3.5 11.0009V13.0009C3.5 13.277 3.72386 13.5009 4 13.5009H12C12.2761 13.5009 12.5 13.277 12.5 13.0009V11.0009C12.5 10.7247 12.2761 10.5009 12 10.5009H4Z" fill="currentColor"></path></svg></div><p class="text-base text-foreground">Database</p></div><span class="text-sm text-foreground-light flex-grow">Supabase provides a full Postgres database for every project with Realtime functionality, database backups, extensions, and more.</span></div></div></a></li><li class="col-span-12 md:col-span-6"><a href="docs/guides/auth.html"><div class="relative h-full group cursor-pointer overflow-hidden border rounded-lg text-left hover:border-strong bg-surface-75 transition"><div class="px-8 pb-8 relative flex flex-col h-full gap-6 pt-8"><div class="flex items-center gap-3"><div class="shrink-0"><svg viewBox="0 0 16 16" width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.49414 9.97461H8.49414M3.49414 9.97461V11.9746H8.49414V9.97461M3.49414 9.97461V7.97461H8.49414V9.97461M10 5V3C10 1.89543 9.10457 1 8 1C6.89543 1 6 1.89543 6 3V5M3.47266 7L3.47266 12C3.47266 13.1046 4.36809 14 5.47266 14H10.4727C11.5772 14 12.4727 13.1046 12.4727 12V7C12.4727 5.89543 11.5772 5 10.4727 5L5.47266 5C4.36809 5 3.47266 5.89543 3.47266 7Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="bevel"></path></svg></div><p class="text-base text-foreground">Auth</p></div><span class="text-sm text-foreground-light flex-grow">Add and manage email and password, passwordless, OAuth, and mobile logins to your project through a suite of identity providers and APIs.</span></div></div></a></li><li class="col-span-12 md:col-span-4"><a href="docs/guides/storage.html"><div class="relative h-full group cursor-pointer overflow-hidden border rounded-lg text-left hover:border-strong bg-surface-75 transition"><div class="px-8 pb-8 relative flex flex-col h-full gap-6 pt-8"><div class="flex items-center gap-3"><div class="shrink-0"><svg viewBox="0 0 16 16" width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12.9997 7.50869V5.60119L9.38151 2.00024H3.99967C3.44739 2.00024 2.99967 2.44796 2.99967 3.00024V5.99976M12.9645 5.58447L9.38004 2L9.38004 4.58447C9.38004 5.13676 9.82776 5.58447 10.38 5.58447L12.9645 5.58447ZM4.44135 5.99976H2.97363C2.42135 5.99976 1.97363 6.44747 1.97363 6.99976V11.9998C1.97363 13.1043 2.86906 13.9998 3.97363 13.9998H11.9736C13.0782 13.9998 13.9736 13.1043 13.9736 11.9998V8.50869C13.9736 7.95641 13.5259 7.50869 12.9736 7.50869H6.79396C6.53157 7.50869 6.27968 7.40556 6.09263 7.22153L5.14268 6.28692C4.95563 6.10289 4.70375 5.99976 4.44135 5.99976Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="bevel"></path></svg></div><p class="text-base text-foreground">Storage</p></div><span class="text-sm text-foreground-light flex-grow">Store, organize, transform, and serve large files—fully integrated with your Postgres database with Row Level Security access policies.</span></div></div></a></li><li class="col-span-12 md:col-span-4"><a href="docs/guides/realtime.html"><div class="relative h-full group cursor-pointer overflow-hidden border rounded-lg text-left hover:border-strong bg-surface-75 transition"><div class="px-8 pb-8 relative flex flex-col h-full gap-6 pt-8"><div class="flex items-center gap-3"><div class="shrink-0"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 16 16" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M5.85669 1.07837C6.13284 1.07837 6.35669 1.30223 6.35669 1.57837V4.07172C6.35669 4.34786 6.13284 4.57172 5.85669 4.57172C5.58055 4.57172 5.35669 4.34786 5.35669 4.07172V1.57837C5.35669 1.30223 5.58055 1.07837 5.85669 1.07837ZM1.51143 1.51679C1.70961 1.32449 2.02615 1.32925 2.21845 1.52743L4.3494 3.72353C4.5417 3.9217 4.53694 4.23825 4.33876 4.43055C4.14058 4.62285 3.82403 4.61809 3.63173 4.41991L1.50078 2.22381C1.30848 2.02564 1.31325 1.70909 1.51143 1.51679ZM5.10709 6.49114C4.74216 5.65659 5.59204 4.80844 6.42584 5.17508L14.3557 8.66199C15.2287 9.04582 15.1201 10.3175 14.1948 10.5478L11.1563 11.3041L10.4159 14.1716C10.1783 15.0916 8.91212 15.1928 8.53142 14.3222L5.10709 6.49114ZM13.9532 9.5774L6.02332 6.09049L9.44766 13.9216L10.2625 10.7658C10.3083 10.5882 10.4478 10.4499 10.6258 10.4056L13.9532 9.5774ZM1.04663 5.79688C1.04663 5.52073 1.27049 5.29688 1.54663 5.29688H3.99057C4.26671 5.29688 4.49057 5.52073 4.49057 5.79688C4.49057 6.07302 4.26671 6.29688 3.99057 6.29688H1.54663C1.27049 6.29688 1.04663 6.07302 1.04663 5.79688Z" fill="currentColor"></path></svg></div><p class="text-base text-foreground">Realtime</p></div><span class="text-sm text-foreground-light flex-grow">Listen to database changes, store and sync user states across clients, broadcast data to clients subscribed to a channel, and more.</span></div></div></a></li><li class="col-span-12 md:col-span-4"><a href="docs/guides/functions.html"><div class="relative h-full group cursor-pointer overflow-hidden border rounded-lg text-left hover:border-strong bg-surface-75 transition"><div class="px-8 pb-8 relative flex flex-col h-full gap-6 pt-8"><div class="flex items-center gap-3"><div class="shrink-0"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 16 16" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M1.62624 10.8978C1.22391 10.0142 1 9.03261 1 8C1 4.13401 4.13401 1 8 1C9.03686 1 10.0223 1.22575 10.9087 1.63122C11.2997 1.37784 11.766 1.23071 12.2665 1.23071C13.6473 1.23071 14.7665 2.35 14.7665 3.73071C14.7665 4.23073 14.6197 4.69646 14.3669 5.08716C14.7736 5.97467 15 6.96155 15 8C15 11.866 11.866 15 8 15C6.94896 15 5.95081 14.768 5.05508 14.3521C4.67477 14.5858 4.22715 14.7206 3.74805 14.7206C2.36733 14.7206 1.24805 13.6013 1.24805 12.2206C1.24805 11.7349 1.38656 11.2815 1.62624 10.8978ZM2 8C2 4.68629 4.68629 2 8 2C8.75898 2 9.48416 2.14069 10.1515 2.39715C9.90768 2.7831 9.76654 3.24042 9.76654 3.73071C9.76654 3.77457 9.76768 3.81815 9.76991 3.86145C9.22664 3.6288 8.62833 3.5 7.99994 3.5C5.51466 3.5 3.49994 5.51472 3.49994 8C3.49994 8.61006 3.62134 9.19177 3.8413 9.72228C3.81035 9.72115 3.77927 9.72058 3.74805 9.72058C3.24584 9.72058 2.77822 9.86866 2.38647 10.1235C2.13679 9.46389 2 8.74838 2 8ZM5.83493 13.5976C6.50608 13.8574 7.23593 14 8 14C11.3137 14 14 11.3137 14 8C14 7.23965 13.8588 6.51324 13.6015 5.84486C13.2152 6.08924 12.7574 6.23071 12.2665 6.23071C12.2232 6.23071 12.1802 6.22961 12.1374 6.22743C12.3707 6.77139 12.4999 7.3706 12.4999 8C12.4999 10.4853 10.4852 12.5 7.99994 12.5C7.37809 12.5 6.78569 12.3739 6.24695 12.1458C6.24768 12.1706 6.24805 12.1956 6.24805 12.2206C6.24805 12.7294 6.09603 13.2027 5.83493 13.5976ZM10.7665 3.73071C10.7665 2.90229 11.4381 2.23071 12.2665 2.23071C13.095 2.23071 13.7665 2.90229 13.7665 3.73071C13.7665 4.55914 13.095 5.23071 12.2665 5.23071C11.4381 5.23071 10.7665 4.55914 10.7665 3.73071ZM5.40407 10.3477C5.48532 10.4196 5.56185 10.4967 5.63315 10.5785C6.25623 11.1507 7.08729 11.5 7.99994 11.5C9.93294 11.5 11.4999 9.933 11.4999 8C11.4999 6.067 9.93294 4.5 7.99994 4.5C6.06695 4.5 4.49994 6.067 4.49994 8C4.49994 8.90336 4.84218 9.72678 5.40407 10.3477ZM3.74805 10.7206C4.11285 10.7206 4.44724 10.8508 4.70725 11.0673C4.77215 11.1369 4.83923 11.2045 4.90838 11.2699C5.12065 11.5287 5.24805 11.8598 5.24805 12.2206C5.24805 13.049 4.57647 13.7206 3.74805 13.7206C2.91962 13.7206 2.24805 13.049 2.24805 12.2206C2.24805 11.3921 2.91962 10.7206 3.74805 10.7206Z" fill="currentColor"></path></svg></div><p class="text-base text-foreground">Edge Functions</p></div><span class="text-sm text-foreground-light flex-grow">Globally distributed, server-side functions to execute your code closest to your users for the lowest latency.</span></div></div></a></li></ul><div class="flex flex-col lg:grid grid-cols-12 gap-6 py-12 border-b"><div class="col-span-4"><h2 id="postgres-integrations" class="scroll-mt-24 m-0">Postgres Modules</h2></div><div class="grid col-span-8 grid-cols-12 gap-6 not-prose"><a class="col-span-6 md:col-span-4" href="docs/guides/ai.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><svg width="18" height="18" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M7.99886 7.63216V14.4892M7.99886 7.63216L14.0488 4.11804M7.99886 7.63216L1.94922 4.11819M1.94922 4.11819V8.32332M1.94922 4.11819V4.08217L5.57319 1.97717M14.049 8.36007V4.08217L10.4251 1.97717M11.8165 12.4072L7.99913 14.6245L4.18177 12.4072" stroke="currentColor" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path></svg></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">AI &amp; Vectors</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a><a class="col-span-6 md:col-span-4" href="docs/guides/cron.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-clock"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">Cron</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a><a class="col-span-6 md:col-span-4" href="docs/guides/queues.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-square-stack"><path d="M4 10c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h4c1.1 0 2 .9 2 2"></path><path d="M10 16c-1.1 0-2-.9-2-2v-4c0-1.1.9-2 2-2h4c1.1 0 2 .9 2 2"></path><rect width="8" height="8" x="14" y="14" rx="2"></rect></svg></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">Queues</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a></div></div><div class="flex flex-col lg:grid grid-cols-12 gap-6 py-12 border-b"><div class="col-span-4 flex flex-col gap-1 [&amp;_h2]:m-0 [&amp;_h3]:m-0"><div class="md:max-w-xs 2xl:max-w-none"><div class="flex items-center gap-3 mb-3 text-brand-600"><h2 id="client-libraries" class="group scroll-mt-24">Client Libraries</h2></div></div></div><div class="grid col-span-8 grid-cols-12 gap-6 not-prose"><a class="col-span-6 md:col-span-4" href="docs/reference/javascript/introduction.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><svg width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><path fill-rule="evenodd" clip-rule="evenodd" d="M2 2h12v12H2V2Zm9.173 10.06c-.556 0-.87-.29-1.112-.685l-.916.533c.33.654 1.007 1.153 2.054 1.153 1.071 0 1.869-.556 1.869-1.572 0-.941-.541-1.36-1.5-1.771l-.281-.12c-.484-.21-.693-.347-.693-.686 0-.273.209-.483.54-.483.323 0 .532.137.725.483l.878-.563c-.371-.654-.887-.903-1.604-.903-1.007 0-1.651.644-1.651 1.49 0 .917.54 1.352 1.354 1.698l.282.121c.514.225.821.362.821.749 0 .322-.299.556-.766.556Zm-4.37-.007c-.387 0-.548-.266-.726-.58l-.917.556c.265.562.788 1.03 1.691 1.03 1 0 1.684-.532 1.684-1.7V7.51H7.407v3.834c0 .564-.233.709-.604.709Z" fill="currentColor"></path></svg></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">Javascript</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a><a class="col-span-6 md:col-span-4" href="docs/reference/dart/introduction.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><svg viewBox="0 0 16 16" width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="m11.857 2-3.718.004L2.143 8l1.85 1.852 1.626-1.617L11.857 2ZM8.245 7.531c-.052-.002-.107-.005-.14.04l-3.198 3.197 1.836 1.825-.002.002 1.315 1.316a.549.549 0 0 1 .026.025c.***************.13.062.607-.002 1.214-.002 1.821-.001l1.822-.001-3.232-3.235 3.23-3.23H8.31a.39.39 0 0 1-.064 0Z" fill="currentColor"></path></svg></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">Flutter</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a><a class="col-span-6 md:col-span-4" href="docs/reference/python/introduction.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><svg viewBox="0 0 16 16" width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M6.424 1.627A9.05 9.05 0 0 1 7.923 1.5a9.885 9.885 0 0 1 1.633.127c.851.14 1.568.771 1.568 1.612v2.953a1.57 1.57 0 0 1-1.568 1.576H6.424c-1.063 0-1.959.903-1.959 1.927v1.416H3.387c-.911 0-1.443-.654-1.666-1.572-.3-1.233-.288-1.97 0-3.152.25-1.03 1.047-1.572 1.959-1.572h4.312V4.42H4.856V3.239c0-.895.241-1.38 1.568-1.612Zm.391 1.417a.592.592 0 0 0-.588-.593.59.59 0 0 0 0 *********** 0 0 0 .588-.59Zm4.7 3.148V4.815h1.177c.912 0 1.342.675 1.568 1.572.313 1.246.327 2.18 0 3.152-.317.944-.657 1.572-1.568 1.572h-4.7v.394h3.132v1.182c0 .896-.778 1.35-1.568 1.577-1.187.34-2.14.288-3.132 0-.829-.242-1.568-.736-1.568-1.576V9.733c0-.85.71-1.576 1.568-1.576h3.132c1.044 0 1.96-.898 1.96-1.966Zm-1.173 6.69a.589.589 0 1 0-1.177 0c0 .328.265.594.589.594a.59.59 0 0 0 .588-.593Z" fill="currentColor"></path></svg></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">Python</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a><a class="col-span-6 md:col-span-4" href="docs/reference/csharp/introduction.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><svg viewBox="0 0 16 16" width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="m2.242 11.3.004-.003a1.191 1.191 0 0 1-.134-.57V5.27c0-.442.185-.784.57-1.005.444-.259 1.393-.804 2.33-1.343 1.019-.587 2.024-1.165 2.35-1.356.42-.247.826-.254 1.25-.01l2.37 1.364 2.336 1.345c.**************.425.414.098.167.145.366.145.587V10.73c-.001.216-.048.407-.138.573a1.089 1.089 0 0 1-.432.428l-1.786 1.028c-.973.56-1.947 1.12-2.92 1.682-.388.228-.767.236-1.155.042a1.65 1.65 0 0 1-.103-.056c-.382-.227-1.702-.986-2.881-1.664-.747-.429-1.437-.826-1.799-1.036a1.137 1.137 0 0 1-.432-.428Zm7.452-2.351a1.95 1.95 0 0 1-1.698.994 1.94 1.94 0 0 1-1.69-.983A1.946 1.946 0 0 1 9.68 7.02l1.7-.98a3.91 3.91 0 1 0-3.385 5.866 3.913 3.913 0 0 0 3.4-1.974l-1.701-.983Zm2.151-1.88h-.388v.316h-.312v.388h.312v.468h-.312v.388h.312v.316h.388v-.316h.472v.316h.388v-.316h.316v-.388h-.316v-.468h.316v-.388h-.316V7.07h-.388v.316h-.472V7.07Zm0 1.172v-.468h.472v.468h-.472Z" fill="currentColor"></path></svg></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">C#</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a><a class="col-span-6 md:col-span-4" href="docs/reference/swift/introduction.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><svg viewBox="0 0 16 16" width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M14.364 4.774c.004.128.005.256.006.384l.001.439v5.245c-.002.128-.003.256-.007.384-.007.28-.024.561-.073.837-.05.28-.133.541-.263.796a2.673 2.673 0 0 1-1.17 1.17c-.254.13-.514.211-.795.262-.276.05-.558.066-.837.073-.128.004-.256.005-.384.006l-.408.001H5.565l-.407-.001c-.128 0-.256-.002-.384-.006a5.571 5.571 0 0 1-.837-.073c-.28-.05-.541-.133-.796-.262a2.674 2.674 0 0 1-1.17-1.17 2.824 2.824 0 0 1-.262-.796 5.582 5.582 0 0 1-.073-.837 18.375 18.375 0 0 1-.006-.384l-.001-.404V5.158c.002-.128.003-.256.007-.384.007-.28.024-.561.073-.837.05-.28.133-.541.262-.796a2.673 2.673 0 0 1 1.362-1.258c.194-.08.393-.136.604-.174.207-.037.417-.056.627-.066.07-.003.14-.006.21-.007.128-.004.256-.005.384-.006l.457-.001H10.842l.384.006c.28.008.561.024.837.074.28.05.541.133.796.262a2.671 2.671 0 0 1 1.17 1.17c.13.255.212.515.262.796.05.276.066.558.073.837Zm-2.64 4.72h.002c1.094 1.347.797 2.791.656 2.519-.572-1.114-1.638-.83-2.178-.55-.044.028-.09.053-.136.078l-.008.004a.306.306 0 0 0-.01.006l.002-.002c-1.124.597-2.632.642-4.149-.01A6.673 6.673 0 0 1 2.908 8.97c.345.255.718.48 1.114.665 1.603.75 3.213.697 4.352 0C6.753 8.386 5.4 6.762 4.361 5.446a5.644 5.644 0 0 1-.534-.736C5.07 5.85 7.033 7.277 7.737 7.672c-1.494-1.58-2.812-3.525-2.75-3.462 2.355 2.372 4.527 3.714 4.527 3.714.***************.192.113.044-.114.084-.232.118-.355.376-1.374-.047-2.946-1.004-4.243 2.184 1.311 3.474 3.802 2.946 5.91a75.282 75.282 0 0 0-.041.145Z" fill="currentColor"></path></svg></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">Swift</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a><a class="col-span-6 md:col-span-4" href="docs/reference/kotlin/introduction.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" width="18" height="18" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M14 14H2V2H14L8 8L14 14Z" fill="currentColor"></path></svg></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">Kotlin</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a></div></div><div class="flex flex-col lg:grid grid-cols-12 gap-6 py-12 border-b"><div class="col-span-4 flex flex-col gap-1 [&amp;_h2]:m-0"><h2 id="migrate-to-supabase" class="group scroll-mt-24">Migrate to Supabase</h2><p class="text-foreground-light text-sm p-0 m-0">Bring your existing data, auth and storage to Supabase following our migration guides.</p><a class="group/text-link hover:text-foreground mt-3 block cursor-pointer focus-visible:ring-2 focus-visible:outline-none focus-visible:rounded-sm focus-visible:ring-foreground-lighter focus-visible:text-foreground no-underline text-brand text-sm" target="_self" href="docs/guides/resources.html"><div class="group flex items-center gap-1"><span class="sr-only">Explore more resources about /guides/resources</span><span>Explore more resources</span><div class="transition-all group-hover:ml-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></a></div><ul class="grid col-span-8 grid-cols-12 gap-6 not-prose"><li class="col-span-6 md:col-span-4"><a href="docs/guides/platform/migrating-to-supabase/amazon-rds.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><img class="w-5" src="docs/img/icons/aws-rds-icon.svg" alt="Amazon RDS Icon"/></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">Amazon RDS</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a></li><li class="col-span-6 md:col-span-4"><a href="docs/guides/platform/migrating-to-supabase/auth0.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><img class="w-5" src="docs/img/icons/auth0-icon-light.svg" alt="Auth0 Icon"/></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">Auth0</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a></li><li class="col-span-6 md:col-span-4"><a href="docs/guides/platform/migrating-to-supabase/firebase-auth.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><img class="w-5" src="docs/img/icons/firebase-icon.svg" alt="Firebase Auth Icon"/></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">Firebase Auth</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a></li><li class="col-span-6 md:col-span-4"><a href="docs/guides/platform/migrating-to-supabase/firebase-storage.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><img class="w-5" src="docs/img/icons/firebase-icon.svg" alt="Firebase Storage Icon"/></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">Firebase Storage</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a></li><li class="col-span-6 md:col-span-4"><a href="docs/guides/platform/migrating-to-supabase/firestore-data.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><img class="w-5" src="docs/img/icons/firebase-icon.svg" alt="Firestore Data Icon"/></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">Firestore Data</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a></li><li class="col-span-6 md:col-span-4"><a href="docs/guides/platform/migrating-to-supabase/heroku.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><img class="w-5" src="docs/img/icons/heroku-icon.svg" alt="Heroku Icon"/></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">Heroku</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a></li><li class="col-span-6 md:col-span-4"><a href="docs/guides/platform/migrating-to-supabase/mssql.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><img class="w-5" src="docs/img/icons/mssql-icon.svg" alt="MSSQL Icon"/></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">MSSQL</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a></li><li class="col-span-6 md:col-span-4"><a href="docs/guides/platform/migrating-to-supabase/mysql.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><img class="w-5" src="docs/img/icons/mysql-icon.svg" alt="MySQL Icon"/></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">MySQL</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a></li><li class="col-span-6 md:col-span-4"><a href="docs/guides/platform/migrating-to-supabase/neon.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><img class="w-5" src="docs/img/icons/neon-icon-light.svg" alt="Neon Icon"/></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">Neon</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a></li><li class="col-span-6 md:col-span-4"><a href="docs/guides/platform/migrating-to-supabase/postgres.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><img class="w-5" src="docs/img/icons/postgres-icon.svg" alt="Postgres Icon"/></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">Postgres</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a></li><li class="col-span-6 md:col-span-4"><a href="docs/guides/platform/migrating-to-supabase/render.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><img class="w-5" src="docs/img/icons/render-icon.svg" alt="Render Icon"/></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">Render</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a></li><li class="col-span-6 md:col-span-4"><a href="docs/guides/platform/migrating-to-supabase/vercel-postgres.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><img class="w-5" src="docs/img/icons/vercel-icon-light.svg" alt="Vercel Postgres Icon"/></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">Vercel Postgres</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a></li></ul></div><div class="flex flex-col gap-6 py-12 border-b"><div class="col-span-4 flex flex-col gap-1 [&amp;_h2]:m-0 [&amp;_h3]:m-0"><h3 id="additional-resources" class="group scroll-mt-24">Additional resources</h3></div><ul class="grid grid-cols-12 gap-6 not-prose"><li class="col-span-12 md:col-span-6 lg:col-span-3"><a class="col-span-12 md:col-span-6 lg:col-span-3" href="docs/reference/api/introduction.html"><div class="relative h-full group cursor-pointer overflow-hidden border rounded-lg text-left border-muted hover:border-default bg-transparent transition"><div class="px-8 pb-8 relative flex flex-col h-full gap-6 pt-8"><div class="flex items-center gap-3"><div class="shrink-0"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 16 16" fill="none"><path d="M8.00018 1.98145C7.56432 1.98145 7.13983 2.02769 6.73114 2.11537C6.46114 2.1733 6.19531 2.00138 6.13738 1.73138C6.07946 1.46138 6.25138 1.19554 6.52138 1.13762C6.99858 1.03524 7.49338 0.981445 8.00018 0.981445C8.50698 0.981445 9.00178 1.03524 9.47898 1.13762C9.74898 1.19555 9.9209 1.46138 9.86297 1.73138C9.80505 2.00138 9.53921 2.1733 9.26921 2.11537C8.86053 2.02769 8.43603 1.98145 8.00018 1.98145Z" fill="currentColor"></path><path d="M1.73138 6.1372C2.00138 6.19513 2.1733 6.46096 2.11537 6.73096C2.02769 7.13965 1.98145 7.56414 1.98145 8C1.98145 8.43586 2.02769 8.86035 2.11537 9.26904C2.1733 9.53904 2.00138 9.80487 1.73138 9.8628C1.46138 9.92072 1.19555 9.7488 1.13762 9.4788C1.03524 9.0016 0.981445 8.5068 0.981445 8C0.981445 7.4932 1.03524 6.9984 1.13762 6.5212C1.19555 6.2512 1.46138 6.07928 1.73138 6.1372Z" fill="currentColor"></path><path d="M14.2686 6.1372C14.5386 6.07928 14.8045 6.2512 14.8624 6.5212C14.9648 6.9984 15.0186 7.4932 15.0186 8C15.0186 8.5068 14.9648 9.0016 14.8624 9.4788C14.8045 9.7488 14.5386 9.92072 14.2686 9.86279C13.9986 9.80487 13.8267 9.53903 13.8846 9.26904C13.9723 8.86035 14.0186 8.43586 14.0186 8C14.0186 7.56414 13.9723 7.13965 13.8846 6.73096C13.8267 6.46096 13.9986 6.19513 14.2686 6.1372Z" fill="currentColor"></path><path d="M6.13738 14.2686C6.19531 13.9986 6.46114 13.8267 6.73114 13.8846C7.13983 13.9723 7.56432 14.0186 8.00018 14.0186C8.43603 14.0186 8.86053 13.9723 9.26922 13.8846C9.53922 13.8267 9.80505 13.9986 9.86297 14.2686C9.9209 14.5386 9.74898 14.8045 9.47898 14.8624C9.00178 14.9648 8.50698 15.0186 8.00018 15.0186C7.49337 15.0186 6.99858 14.9648 6.52137 14.8624C6.25138 14.8045 6.07946 14.5386 6.13738 14.2686Z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M11.8048 1.75216C12.4795 1.0774 13.5735 1.07741 14.2483 1.75216C14.923 2.42691 14.923 3.52091 14.2483 4.19566C13.6764 4.76755 12.8033 4.85474 12.1396 4.45721L10.6936 5.90323C11.1446 6.48183 11.4133 7.20953 11.4133 7.99999C11.4133 8.76199 11.1636 9.46567 10.7416 10.0337L12.2103 11.5024C12.8642 11.152 13.6961 11.2526 14.2478 11.8043C14.9225 12.479 14.9225 13.573 14.2478 14.2478C13.573 14.9226 12.479 14.9226 11.8043 14.2478C11.2523 13.6958 11.1519 12.8633 11.503 12.2092L10.0346 10.7409C9.46646 11.1634 8.76241 11.4134 7.99997 11.4134C7.20951 11.4134 6.4818 11.1447 5.90321 10.6936L4.4574 12.1395C4.85471 12.8031 4.76747 13.676 4.19566 14.2478C3.52091 14.9226 2.42691 14.9226 1.75216 14.2478C1.0774 13.573 1.07741 12.479 1.75216 11.8043C2.28359 11.2729 3.07507 11.16 3.71698 11.4657L5.21234 9.9703C4.81821 9.4137 4.5866 8.73389 4.5866 7.99999C4.5866 7.23755 4.83658 6.5335 5.25904 5.96532L3.79073 4.497C3.13666 4.8481 2.30413 4.74766 1.75216 4.19569C1.07741 3.52093 1.07741 2.42694 1.75216 1.75218C2.42692 1.07743 3.52091 1.07743 4.19566 1.75219C4.74732 2.30385 4.84796 3.13573 4.49758 3.78964L5.96628 5.25834C6.53429 4.83631 7.23797 4.58662 7.99997 4.58662C8.73387 4.58662 9.41367 4.81824 9.97027 5.21236L11.466 3.71665C11.1605 3.0748 11.2734 2.2835 11.8048 1.75216ZM13.5412 2.45927C13.2569 2.17504 12.7961 2.17504 12.5119 2.45927C12.2277 2.7435 12.2277 3.20433 12.5119 3.48856C12.7961 3.77279 13.2569 3.77279 13.5412 3.48856C13.8254 3.20433 13.8254 2.7435 13.5412 2.45927ZM9.67618 9.73627C9.24205 10.1555 8.65112 10.4134 7.99997 10.4134C6.6671 10.4134 5.5866 9.33286 5.5866 7.99999C5.5866 6.66712 6.6671 5.58662 7.99997 5.58662C9.33283 5.58662 10.4133 6.66712 10.4133 7.99999C10.4133 8.65078 10.1557 9.24141 9.73695 9.67547C9.72611 9.68462 9.71556 9.6943 9.70535 9.70451C9.69509 9.71478 9.68536 9.72537 9.67618 9.73627ZM3.48856 2.45929C3.20433 2.17506 2.7435 2.17506 2.45927 2.45929C2.17504 2.74352 2.17504 3.20435 2.45927 3.48858C2.7435 3.77281 3.20433 3.77281 3.48856 3.48858C3.77279 3.20435 3.77279 2.74352 3.48856 2.45929ZM3.48856 12.5114C3.20433 12.2272 2.7435 12.2272 2.45927 12.5114C2.17504 12.7956 2.17504 13.2565 2.45927 13.5407C2.7435 13.8249 3.20433 13.8249 3.48856 13.5407C3.77279 13.2565 3.77279 12.7956 3.48856 12.5114ZM13.5407 12.5114C13.2564 12.2272 12.7956 12.2272 12.5114 12.5114C12.2271 12.7956 12.2271 13.2565 12.5114 13.5407C12.7956 13.8249 13.2564 13.8249 13.5407 13.5407C13.8249 13.2565 13.8249 12.7956 13.5407 12.5114Z" fill="currentColor"></path></svg></div><p class="text-base text-foreground">Management API</p></div><span class="text-sm text-foreground-light flex-grow">Manage your Supabase projects and organizations.</span></div></div></a></li><li class="col-span-12 md:col-span-6 lg:col-span-3"><a class="col-span-12 md:col-span-6 lg:col-span-3" href="docs/reference/cli/introduction.html"><div class="relative h-full group cursor-pointer overflow-hidden border rounded-lg text-left border-muted hover:border-default bg-transparent transition"><div class="px-8 pb-8 relative flex flex-col h-full gap-6 pt-8"><div class="flex items-center gap-3"><div class="shrink-0"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 16 16" fill="none"><path d="M8.68972 4.35753C8.96122 4.40797 9.14042 4.66896 9.08998 4.94045L7.9225 11.2244C7.87206 11.4959 7.61107 11.6751 7.33958 11.6246C7.06808 11.5742 6.88888 11.3132 6.93932 11.0417L8.1068 4.75779C8.15724 4.48629 8.41822 4.30709 8.68972 4.35753ZM10.0165 6.07223C10.2118 5.87695 10.5284 5.87693 10.7236 6.07218L12.2979 7.64617C12.3916 7.73993 12.4443 7.86711 12.4443 7.99973C12.4443 8.13234 12.3917 8.25953 12.2979 8.3533L10.7237 9.92752C10.5284 10.1228 10.2118 10.1228 10.0166 9.92752C9.8213 9.73226 9.8213 9.41568 10.0166 9.22042L11.2372 7.99977L10.0166 6.77933C9.82131 6.58409 9.82129 6.2675 10.0165 6.07223ZM6.01227 9.9275C5.81702 10.1228 5.50044 10.1228 5.30516 9.92755L3.73094 8.35356C3.63716 8.2598 3.58447 8.13262 3.58447 8C3.58446 7.86739 3.63714 7.7402 3.73091 7.64643L5.30514 6.07221C5.5004 5.87694 5.81698 5.87694 6.01225 6.07221C6.20751 6.26747 6.20751 6.58405 6.01225 6.77931L4.7916 7.99996L6.01222 9.2204C6.2075 9.41564 6.20752 9.73223 6.01227 9.9275ZM1.5 4C1.5 2.61929 2.61929 1.5 4 1.5H12C13.3807 1.5 14.5 2.61929 14.5 4V12C14.5 13.3807 13.3807 14.5 12 14.5H4C2.61929 14.5 1.5 13.3807 1.5 12V4ZM4 2.5C3.17157 2.5 2.5 3.17157 2.5 4V12C2.5 12.8284 3.17157 13.5 4 13.5H12C12.8284 13.5 13.5 12.8284 13.5 12V4C13.5 3.17157 12.8284 2.5 12 2.5H4Z" fill="currentColor"></path></svg></div><p class="text-base text-foreground">Supabase CLI</p></div><span class="text-sm text-foreground-light flex-grow">Use the CLI to develop, manage and deploy your projects.</span></div></div></a></li><li class="col-span-12 md:col-span-6 lg:col-span-3"><a class="col-span-12 md:col-span-6 lg:col-span-3" href="docs/guides/platform.html"><div class="relative h-full group cursor-pointer overflow-hidden border rounded-lg text-left border-muted hover:border-default bg-transparent transition"><div class="px-8 pb-8 relative flex flex-col h-full gap-6 pt-8"><div class="flex items-center gap-3"><div class="shrink-0"><svg viewBox="0 0 16 16" width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M6.7156 1.77258C7.58712 1.14341 8.76326 1.14175 9.63655 1.76846L12.0629 3.50974C12.7162 3.97855 12.8569 4.83994 12.4888 5.4786L13.0896 5.9098C13.7748 6.4015 13.8962 7.32509 13.4579 7.97059L14.1753 8.4882C15.0124 9.09219 15.0035 10.3415 14.1578 10.9335L9.42209 14.2484C8.5592 14.8524 7.41031 14.8508 6.54911 14.2444L1.83872 10.9276C0.999787 10.3368 0.988308 9.09703 1.81616 8.49086L2.80076 7.76992C2.50724 7.14639 2.66597 6.35556 3.27999 5.91229L3.88264 5.47721C3.51614 4.83981 3.65602 3.98138 4.30587 3.51223L6.7156 1.77258ZM5.00242 5.21218C4.95761 5.16341 4.90473 5.12529 4.84735 5.09839C4.61595 4.88922 4.62987 4.51169 4.8912 4.32303L7.30093 2.58337C7.82385 2.20587 8.52953 2.20487 9.0535 2.5809L11.4799 4.32218C11.7403 4.50906 11.7564 4.88309 11.5302 5.09348C11.4651 5.12213 11.4054 5.16499 11.3562 5.22122L9.03905 6.84318C8.52132 7.20559 7.83199 7.20462 7.31527 6.84078L5.00242 5.21218ZM4.62864 6.17202L3.86532 6.72308C3.58687 6.92411 3.58931 7.33956 3.87012 7.53729L7.31263 9.96135C7.82935 10.3252 8.51869 10.3262 9.03642 9.96376L12.5018 7.53808C12.7841 7.34045 12.7866 6.92317 12.5066 6.72224L11.7408 6.17267L9.6125 7.66242C8.74962 8.26643 7.60073 8.26482 6.73953 7.65841L4.62864 6.17202ZM3.49834 8.49855L2.40694 9.29769C2.13099 9.49975 2.13482 9.91301 2.41446 10.1099L7.12485 13.4268C7.64157 13.7906 8.3309 13.7916 8.84864 13.4292L13.5844 10.1143C13.8663 9.91694 13.8692 9.50049 13.5902 9.29916L12.674 8.63814L9.60987 10.783C8.74698 11.387 7.59809 11.3854 6.7369 10.779L3.49834 8.49855Z" fill="currentColor"></path></svg></div><p class="text-base text-foreground">Platform Guides</p></div><span class="text-sm text-foreground-light flex-grow">Learn more about the tools and services powering Supabase.</span></div></div></a></li><li class="col-span-12 md:col-span-6 lg:col-span-3"><a class="col-span-12 md:col-span-6 lg:col-span-3" href="docs/guides/integrations.html"><div class="relative h-full group cursor-pointer overflow-hidden border rounded-lg text-left border-muted hover:border-default bg-transparent transition"><div class="px-8 pb-8 relative flex flex-col h-full gap-6 pt-8"><div class="flex items-center gap-3"><div class="shrink-0"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 16 16" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M1.6748 3.08472C1.6748 2.25629 2.34638 1.58472 3.1748 1.58472H6.1748C7.00323 1.58472 7.6748 2.25629 7.6748 3.08472V6.08472C7.6748 6.91314 7.00323 7.58472 6.1748 7.58472H3.1748C2.34638 7.58472 1.6748 6.91314 1.6748 6.08472V3.08472ZM3.1748 2.58472C2.89866 2.58472 2.6748 2.80857 2.6748 3.08472V6.08472C2.6748 6.36086 2.89866 6.58472 3.1748 6.58472H6.1748C6.45095 6.58472 6.6748 6.36086 6.6748 6.08472V3.08472C6.6748 2.80857 6.45095 2.58472 6.1748 2.58472H3.1748ZM11.5107 1.58472C11.7869 1.58472 12.0107 1.80857 12.0107 2.08472V4.08496H14.0107C14.2869 4.08496 14.5107 4.30882 14.5107 4.58496C14.5107 4.8611 14.2869 5.08496 14.0107 5.08496H12.0107V7.08472C12.0107 7.36086 11.7869 7.58472 11.5107 7.58472C11.2346 7.58472 11.0107 7.36086 11.0107 7.08472V5.08496H9.01074C8.7346 5.08496 8.51074 4.8611 8.51074 4.58496C8.51074 4.30882 8.7346 4.08496 9.01074 4.08496H11.0107V2.08472C11.0107 1.80857 11.2346 1.58472 11.5107 1.58472ZM4.67432 9.04516C3.46754 9.04516 2.48926 10.0234 2.48926 11.2302C2.48926 12.437 3.46754 13.4153 4.67432 13.4153C5.88109 13.4153 6.85938 12.437 6.85938 11.2302C6.85938 10.0234 5.88109 9.04516 4.67432 9.04516ZM1.48926 11.2302C1.48926 9.47116 2.91526 8.04516 4.67432 8.04516C6.43338 8.04516 7.85938 9.47116 7.85938 11.2302C7.85938 12.9893 6.43338 14.4153 4.67432 14.4153C2.91526 14.4153 1.48926 12.9893 1.48926 11.2302ZM8.51074 9.83423C8.51074 9.0058 9.18232 8.33423 10.0107 8.33423H13.0107C13.8392 8.33423 14.5107 9.0058 14.5107 9.83423V12.8342C14.5107 13.6627 13.8392 14.3342 13.0107 14.3342H10.0107C9.18231 14.3342 8.51074 13.6627 8.51074 12.8342V9.83423ZM10.0107 9.33423C9.7346 9.33423 9.51074 9.55809 9.51074 9.83423V12.8342C9.51074 13.1104 9.7346 13.3342 10.0107 13.3342H13.0107C13.2869 13.3342 13.5107 13.1104 13.5107 12.8342V9.83423C13.5107 9.55809 13.2869 9.33423 13.0107 9.33423H10.0107Z" fill="currentColor"></path></svg></div><p class="text-base text-foreground">Integrations</p></div><span class="text-sm text-foreground-light flex-grow">Explore a variety of integrations from Supabase partners.</span></div></div></a></li></ul></div><div class="flex flex-col lg:grid grid-cols-12 gap-6 py-12"><div class="col-span-4 flex flex-col gap-1 [&amp;_h2]:m-0 [&amp;_h3]:m-0"><div class="md:max-w-xs 2xl:max-w-none"><div class="flex items-center gap-3 mb-3 text-brand-600"><div class="shrink-0 bg-brand-200 dark:bg-brand-400 border border-brand-300 dark:border-brand-400 w-8 h-8 flex items-center justify-center rounded"><svg viewBox="0 0 16 16" width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M3.97943 1.49988C3.151 1.49988 2.47943 2.17145 2.47943 2.99988V4.99988C2.47943 5.61515 2.84987 6.1439 3.37989 6.37526V9.62473C2.84987 9.85609 2.47943 10.3848 2.47943 11.0001V13C2.47943 13.8285 3.151 14.5 3.97943 14.5H7.51391C7.51982 14.5001 7.52575 14.5001 7.53168 14.5001H12.0206C12.849 14.5001 13.5206 13.8286 13.5206 13.0001V12.0851C13.5206 10.6081 12.6654 9.33078 11.4232 8.72137C11.946 8.26309 12.2761 7.59029 12.2761 6.84045C12.2761 6.71652 12.2671 6.5947 12.2497 6.4756C12.9491 6.34833 13.4794 5.73603 13.4794 4.99988V2.99988C13.4794 2.17145 12.8079 1.49988 11.9794 1.49988H3.97943ZM11.1343 6.20304C11.1364 6.20772 11.1386 6.21237 11.1408 6.21697C11.2277 6.40684 11.2761 6.618 11.2761 6.84045C11.2761 7.66893 10.6045 8.34062 9.77613 8.34062C8.94776 8.34062 8.27613 7.66893 8.27613 6.84045C8.27613 6.01203 8.9477 5.34045 9.77613 5.34045C10.3766 5.34045 10.8947 5.69333 11.1343 6.20304ZM11.8867 5.49988H11.9794C12.2556 5.49988 12.4794 5.27602 12.4794 4.99988V2.99988C12.4794 2.72374 12.2556 2.49988 11.9794 2.49988H3.97943C3.70329 2.49988 3.47943 2.72374 3.47943 2.99988V4.99988C3.47943 5.27602 3.70329 5.49988 3.97943 5.49988H7.66558C8.10923 4.80286 8.88868 4.34045 9.77613 4.34045C10.6636 4.34045 11.443 4.80286 11.8867 5.49988ZM7.29913 6.49988H4.37989V9.50011H7.07418C7.37693 9.18356 7.73443 8.91934 8.1315 8.72347C7.60726 8.26515 7.27613 7.59143 7.27613 6.84045C7.27613 6.72493 7.28396 6.61124 7.29913 6.49988ZM6.38668 10.5001H3.97943C3.70329 10.5001 3.47943 10.724 3.47943 11.0001V13C3.47943 13.2762 3.70329 13.5 3.97943 13.5H6.11674C6.06165 13.344 6.03168 13.176 6.03168 13.0009V12.0851C6.03168 11.5208 6.15894 10.9831 6.38668 10.5001ZM7.80624 13.5001C7.80283 13.5001 7.79941 13.5 7.79599 13.5H7.5214C7.24953 13.4946 7.03168 13.2731 7.03168 13.0009V12.0851C7.03168 10.5867 8.26234 9.34062 9.77613 9.34062C11.2918 9.34062 12.5206 10.5694 12.5206 12.0851V13.0001C12.5206 13.2763 12.2967 13.5001 12.0206 13.5001H7.80624Z" fill="currentColor"></path></svg></div><h3 id="self-hosting" class="group scroll-mt-24">Self-Hosting</h3></div><p class="text-foreground-light text-sm">Get started with self-hosting Supabase.</p><a class="group/text-link hover:text-foreground mt-3 block cursor-pointer focus-visible:ring-2 focus-visible:outline-none focus-visible:rounded-sm focus-visible:ring-foreground-lighter focus-visible:text-foreground no-underline text-brand text-sm" target="_self" href="docs/guides/self-hosting.html"><div class="group flex items-center gap-1"><span class="sr-only">More on Self-Hosting about /guides/self-hosting</span><span>More on Self-Hosting</span><div class="transition-all group-hover:ml-0.5"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></a></div></div><div class="grid col-span-8 grid-cols-12 gap-6 not-prose"><ul class="col-span-full lg:col-span-8 grid grid-cols-12 gap-6"><li class="col-span-6"><a href="docs/reference/self-hosting-auth/introduction.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><svg viewBox="0 0 16 16" width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.49414 9.97461H8.49414M3.49414 9.97461V11.9746H8.49414V9.97461M3.49414 9.97461V7.97461H8.49414V9.97461M10 5V3C10 1.89543 9.10457 1 8 1C6.89543 1 6 1.89543 6 3V5M3.47266 7L3.47266 12C3.47266 13.1046 4.36809 14 5.47266 14H10.4727C11.5772 14 12.4727 13.1046 12.4727 12V7C12.4727 5.89543 11.5772 5 10.4727 5L5.47266 5C4.36809 5 3.47266 5.89543 3.47266 7Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="bevel"></path></svg></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">Auth</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a></li><li class="col-span-6"><a href="docs/reference/self-hosting-realtime/introduction.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 16 16" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M5.85669 1.07837C6.13284 1.07837 6.35669 1.30223 6.35669 1.57837V4.07172C6.35669 4.34786 6.13284 4.57172 5.85669 4.57172C5.58055 4.57172 5.35669 4.34786 5.35669 4.07172V1.57837C5.35669 1.30223 5.58055 1.07837 5.85669 1.07837ZM1.51143 1.51679C1.70961 1.32449 2.02615 1.32925 2.21845 1.52743L4.3494 3.72353C4.5417 3.9217 4.53694 4.23825 4.33876 4.43055C4.14058 4.62285 3.82403 4.61809 3.63173 4.41991L1.50078 2.22381C1.30848 2.02564 1.31325 1.70909 1.51143 1.51679ZM5.10709 6.49114C4.74216 5.65659 5.59204 4.80844 6.42584 5.17508L14.3557 8.66199C15.2287 9.04582 15.1201 10.3175 14.1948 10.5478L11.1563 11.3041L10.4159 14.1716C10.1783 15.0916 8.91212 15.1928 8.53142 14.3222L5.10709 6.49114ZM13.9532 9.5774L6.02332 6.09049L9.44766 13.9216L10.2625 10.7658C10.3083 10.5882 10.4478 10.4499 10.6258 10.4056L13.9532 9.5774ZM1.04663 5.79688C1.04663 5.52073 1.27049 5.29688 1.54663 5.29688H3.99057C4.26671 5.29688 4.49057 5.52073 4.49057 5.79688C4.49057 6.07302 4.26671 6.29688 3.99057 6.29688H1.54663C1.27049 6.29688 1.04663 6.07302 1.04663 5.79688Z" fill="currentColor"></path></svg></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">Realtime</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a></li><li class="col-span-6"><a href="docs/reference/self-hosting-storage/introduction.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><svg viewBox="0 0 16 16" width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12.9997 7.50869V5.60119L9.38151 2.00024H3.99967C3.44739 2.00024 2.99967 2.44796 2.99967 3.00024V5.99976M12.9645 5.58447L9.38004 2L9.38004 4.58447C9.38004 5.13676 9.82776 5.58447 10.38 5.58447L12.9645 5.58447ZM4.44135 5.99976H2.97363C2.42135 5.99976 1.97363 6.44747 1.97363 6.99976V11.9998C1.97363 13.1043 2.86906 13.9998 3.97363 13.9998H11.9736C13.0782 13.9998 13.9736 13.1043 13.9736 11.9998V8.50869C13.9736 7.95641 13.5259 7.50869 12.9736 7.50869H6.79396C6.53157 7.50869 6.27968 7.40556 6.09263 7.22153L5.14268 6.28692C4.95563 6.10289 4.70375 5.99976 4.44135 5.99976Z" stroke="currentColor" stroke-miterlimit="10" stroke-linejoin="bevel"></path></svg></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">Storage</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a></li><li class="col-span-6"><a href="docs/reference/self-hosting-analytics/introduction.html"><div class="relative group"><div class="peer relative flex flex-col gap-6"><div class="flex items-center gap-3"><div class="relative flex items-center justify-center shrink-0 h-10 w-10 rounded-lg group cursor-pointer overflow-hidden border rounded-full hover:border-strong bg-surface-100 transition"><svg viewBox="0 0 16 16" width="18" height="18" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M6.7156 1.77258C7.58712 1.14341 8.76326 1.14175 9.63655 1.76846L12.0629 3.50974C12.7162 3.97855 12.8569 4.83994 12.4888 5.4786L13.0896 5.9098C13.7748 6.4015 13.8962 7.32509 13.4579 7.97059L14.1753 8.4882C15.0124 9.09219 15.0035 10.3415 14.1578 10.9335L9.42209 14.2484C8.5592 14.8524 7.41031 14.8508 6.54911 14.2444L1.83872 10.9276C0.999787 10.3368 0.988308 9.09703 1.81616 8.49086L2.80076 7.76992C2.50724 7.14639 2.66597 6.35556 3.27999 5.91229L3.88264 5.47721C3.51614 4.83981 3.65602 3.98138 4.30587 3.51223L6.7156 1.77258ZM5.00242 5.21218C4.95761 5.16341 4.90473 5.12529 4.84735 5.09839C4.61595 4.88922 4.62987 4.51169 4.8912 4.32303L7.30093 2.58337C7.82385 2.20587 8.52953 2.20487 9.0535 2.5809L11.4799 4.32218C11.7403 4.50906 11.7564 4.88309 11.5302 5.09348C11.4651 5.12213 11.4054 5.16499 11.3562 5.22122L9.03905 6.84318C8.52132 7.20559 7.83199 7.20462 7.31527 6.84078L5.00242 5.21218ZM4.62864 6.17202L3.86532 6.72308C3.58687 6.92411 3.58931 7.33956 3.87012 7.53729L7.31263 9.96135C7.82935 10.3252 8.51869 10.3262 9.03642 9.96376L12.5018 7.53808C12.7841 7.34045 12.7866 6.92317 12.5066 6.72224L11.7408 6.17267L9.6125 7.66242C8.74962 8.26643 7.60073 8.26482 6.73953 7.65841L4.62864 6.17202ZM3.49834 8.49855L2.40694 9.29769C2.13099 9.49975 2.13482 9.91301 2.41446 10.1099L7.12485 13.4268C7.64157 13.7906 8.3309 13.7916 8.84864 13.4292L13.5844 10.1143C13.8663 9.91694 13.8692 9.50049 13.5902 9.29916L12.674 8.63814L9.60987 10.783C8.74698 11.387 7.59809 11.3854 6.7369 10.779L3.49834 8.49855Z" fill="currentColor"></path></svg></div><div class="flex flex-col gap-1"><div class="flex items-center gap-3"><h5 class="text-base text-foreground m-0">Analytics</h5><div class=" transition-all ease-out -ml-1 opacity-0 text-foreground-muted group-hover:opacity-100 group-hover:ml-0"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"></path></svg></div></div></div></div></div><div class=" absolute transition-all ease-in -z-10 -inset-3 rounded-2xl bg-surface-100 opacity-0 peer-hover:opacity-100"></div></div></a></li></ul></div></div></div></div></div></div></article><div class="max-w-7xl px-5 mx-auto py-8 pt-0"><footer role="contentinfo" aria-label="footer"><div class="mt-16"><ul class="flex flex-col gap-2"><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-life-buoy" aria-hidden="true"><circle cx="12" cy="12" r="10"></circle><path d="m4.93 4.93 4.24 4.24"></path><path d="m14.83 9.17 4.24-4.24"></path><path d="m14.83 14.83 4.24 4.24"></path><path d="m9.17 14.83-4.24 4.24"></path><circle cx="12" cy="12" r="4"></circle></svg><p>Need some help?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="support.html">Contact support</a></li><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-flask-conical" aria-hidden="true"><path d="M10 2v7.527a2 2 0 0 1-.211.896L4.72 20.55a1 1 0 0 0 .9 1.45h12.76a1 1 0 0 0 .9-1.45l-5.069-10.127A2 2 0 0 1 14 9.527V2"></path><path d="M8.5 2h7"></path><path d="M7 16h10"></path></svg><p>Latest product updates?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="changelog.html">See Changelog</a></li><li class="flex items-center gap-1 text-xs text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-check-big" aria-hidden="true"><path d="M21.801 10A10 10 0 1 1 17 3.335"></path><path d="m9 11 3 3L22 4"></path></svg><p>Something&#x27;s not right?</p><a class="text-brand-link hover:underline" target="_blank" rel="noreferrer noopener" href="https://status.supabase.com/">Check system status</a></li></ul></div><hr class="border-default my-6"/><div class="flex gap-4 items-center justify-between"><div class="flex flex-col lg:flex-row gap-3 "><a class="text-xs text-foreground-lighter" href="index.html">© Supabase Inc</a><span class="text-xs text-foreground-lighter">—</span><a class="text-xs text-foreground-lighter hover:underline" href="https://github.com/supabase/supabase/blob/master/apps/docs/DEVELOPERS.md">Contributing</a><a class="text-xs text-foreground-lighter hover:underline" href="https://github.com/supabase/supabase/blob/master/apps/docs/CONTRIBUTING.md">Author Styleguide</a><a class="text-xs text-foreground-lighter hover:underline" href="open-source.html">Open Source</a><a class="text-xs text-foreground-lighter hover:underline" href="open-source/contributing/supasquad.html">SupaSquad</a><button class="text-xs text-foreground-lighter hover:underline">Privacy Settings</button></div><div class="flex items-center gap-2"><a href="https://github.com/supabase/supabase" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">GitHub</span><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.66832 1.55469C4.98649 1.55469 2.00195 4.54402 2.00195 8.23269C2.00195 11.1827 3.91187 13.686 6.56108 14.5687C6.8944 14.63 7.01573 14.424 7.01573 14.2467C7.01573 14.0887 7.0104 13.668 7.00706 13.1114C5.15248 13.5147 4.76116 12.216 4.76116 12.216C4.45851 11.444 4.0212 11.2387 4.0212 11.2387C3.41589 10.8254 4.06719 10.8334 4.06719 10.8334C4.73583 10.88 5.08782 11.5214 5.08782 11.5214C5.68246 12.5414 6.64841 12.2467 7.02773 12.076C7.08906 11.6447 7.26105 11.3507 7.45171 11.184C5.97178 11.0154 4.41518 10.442 4.41518 7.88335C4.41518 7.15469 4.67517 6.55802 5.10115 6.09135C5.03248 5.92269 4.80383 5.24335 5.16648 4.32469C5.16648 4.32469 5.72645 4.14469 6.99973 5.00869C7.54355 4.86036 8.10464 4.78482 8.66832 4.78402C9.23496 4.78669 9.80494 4.86069 10.3376 5.00869C11.6102 4.14469 12.1688 4.32402 12.1688 4.32402C12.5328 5.24335 12.3035 5.92269 12.2355 6.09135C12.6621 6.55802 12.9208 7.15469 12.9208 7.88335C12.9208 10.4487 11.3615 11.0134 9.87694 11.1787C10.1163 11.3847 10.3289 11.792 10.3289 12.4154C10.3289 13.3074 10.3209 14.028 10.3209 14.2467C10.3209 14.4254 10.4409 14.6334 10.7796 14.568C12.107 14.1228 13.261 13.2716 14.0784 12.1347C14.8958 10.9979 15.3353 9.6329 15.3347 8.23269C15.3347 4.54402 12.3495 1.55469 8.66832 1.55469Z" fill="hsl(var(--foreground-muted))"></path></svg></span></a><a href="https://twitter.com/supabase" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">Twitter</span><svg width="15" height="15" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" fill="hsl(var(--foreground-muted))"></path></svg></span></a><a href="https://discord.supabase.com/" target="_blank" rel="noreferrer noopener" data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px]"><span class="truncate"><span class="sr-only">Discord</span><svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#clip0_1182_99731)"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.8781 3.13594C12.8583 2.66802 11.7648 2.32328 10.6215 2.12583C10.6006 2.12202 10.5798 2.13154 10.5691 2.15059C10.4285 2.40073 10.2727 2.72706 10.1636 2.98356C8.93387 2.79945 7.71044 2.79945 6.50592 2.98356C6.39681 2.72136 6.23538 2.40073 6.09411 2.15059C6.08339 2.13218 6.06259 2.12265 6.04176 2.12583C4.89905 2.32265 3.80554 2.66739 2.78517 3.13594C2.77633 3.13975 2.76876 3.14611 2.76374 3.15435C0.689563 6.25326 0.12136 9.276 0.400102 12.2613C0.401363 12.2759 0.409561 12.2898 0.420913 12.2987C1.78939 13.3037 3.115 13.9139 4.41599 14.3183C4.43681 14.3247 4.45887 14.317 4.47212 14.2999C4.77987 13.8796 5.0542 13.4364 5.28941 12.9704C5.3033 12.9431 5.29005 12.9107 5.26167 12.9C4.82654 12.7349 4.4122 12.5336 4.01364 12.3051C3.98212 12.2867 3.97959 12.2416 4.00859 12.22C4.09247 12.1571 4.17636 12.0917 4.25645 12.0257C4.27094 12.0136 4.29113 12.0111 4.30816 12.0187C6.92655 13.2142 9.76126 13.2142 12.3488 12.0187C12.3658 12.0105 12.386 12.013 12.4011 12.0251C12.4812 12.0911 12.5651 12.1571 12.6496 12.22C12.6786 12.2416 12.6767 12.2867 12.6452 12.3051C12.2466 12.5381 11.8323 12.7349 11.3965 12.8993C11.3681 12.9101 11.3555 12.9431 11.3694 12.9704C11.6097 13.4358 11.884 13.879 12.1861 14.2993C12.1987 14.317 12.2214 14.3247 12.2422 14.3183C13.5495 13.9139 14.8751 13.3037 16.2436 12.2987C16.2556 12.2898 16.2631 12.2765 16.2644 12.2619C16.598 8.8106 15.7056 5.81265 13.8989 3.15498C13.8944 3.14611 13.8869 3.13975 13.8781 3.13594ZM5.68043 10.4435C4.89211 10.4435 4.24257 9.71978 4.24257 8.83093C4.24257 7.94207 4.87952 7.21831 5.68043 7.21831C6.48763 7.21831 7.13089 7.94843 7.11827 8.83093C7.11827 9.71978 6.48132 10.4435 5.68043 10.4435ZM10.9967 10.4435C10.2084 10.4435 9.55884 9.71978 9.55884 8.83093C9.55884 7.94207 10.1958 7.21831 10.9967 7.21831C11.8039 7.21831 12.4471 7.94843 12.4345 8.83093C12.4345 9.71978 11.8039 10.4435 10.9967 10.4435Z" fill="hsl(var(--foreground-muted))"></path></g><defs><clipPath id="clip0_1182_99731"><rect width="15.9993" height="16" fill="white" transform="translate(0.333984 0.222656)"></rect></clipPath></defs></svg></span></a></div></div></footer></div></div><div class="left-0 right-0 z-10 backdrop-blur-sm backdrop-filter bg-alternative/90 hidden h-0 lg:hidden"></div></div></main></div><!--$--><!--/$--><!--$--><!--/$--></div></div></div><script src="https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/chunks/webpack-ed7b7570a4b99b4f.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[39346,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"TelemetryTagManager\"]\n3:I[6372,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7177\",\"static/chunks/app/layout-"])</script><script>self.__next_f.push([1,"dd96a0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"QueryClientProvider\"]\n4:I[10556,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"AuthContainer\"]\n5:I[12287,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG"])</script><script>self.__next_f.push([1,"2nbNbFqH29ifBv5mM1txU\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"FeatureFlagProvider\"]\n6:I[68474,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"PageTelemetry\"]\n7:I[982,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\""])</script><script>self.__next_f.push([1,"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"ScrollRestoration\"]\n8:I[66533,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"ThemeProvider\"]\n9:I[48744,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8974\",\"static/chunks/app/page-21b9"])</script><script>self.__next_f.push([1,"296d3312bd31.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"TooltipProvider\"]\na:I[37052,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"CommandProvider\"]\nb:I[31072,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbF"])</script><script>self.__next_f.push([1,"qH29ifBv5mM1txU\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"default\"]\nc:I[91898,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8974\",\"static/chunks/app/page-21b9296d3312bd31.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"TopNavSkeleton\"]\nd:I[93926,[],\"\"]\ne:I[74684,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8039\",\"static/chunks/app/error-e42d84999b0af1d4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"default\"]\nf:I[6252,[],\"\"]\n10:I[91898,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862"])</script><script>self.__next_f.push([1,"b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8974\",\"static/chunks/app/page-21b9296d3312bd31.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"SidebarSkeleton\"]\n11:I[18017,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4345\",\"static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"SearchButton\"]\n12:I[35621,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8974\",\"static/chunks/app/page-21b9296d3312bd31.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"Button\"]\n13:I[15531,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2"])</script><script>self.__next_f.push([1,"555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8974\",\"static/chunks/app/page-21b9296d3312bd31.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"\"]\n14:I[18017,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4345\",\"static/chunks/app/not-found-608d18954d39befa.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"Recommendations\"]\n15:I[59160,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM"])</script><script>self.__next_f.push([1,"1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"DocsCommandMenu\"]\n16:I[94741,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8804\",\"static/chunks/8804-f531b779807559f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"91\",\"static/chunks/91-402daa7a785b5e74.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7840\",\"static/chunks/7840-7a9e87ffa5eefbe8.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7177\",\"static/chunks/app/layout-dd96a0d376dad970.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"ThemeSandbox\"]\n17:I[36132,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/"])</script><script>self.__next_f.push([1,"7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8974\",\"static/chunks/app/page-21b9296d3312bd31.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"SonnerToaster\"]\n18:I[38255,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8974\",\"static/chunks/app/page-21b9296d3312bd31.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"default\"]\n19:I[18990,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8974\",\"static/chunks/app/page-21b9296d3312bd31.js?dpl=dp"])</script><script>self.__next_f.push([1,"l_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"GlassPanel\"]\n1c:I[41693,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8974\",\"static/chunks/app/page-21b9296d3312bd31.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"IconPanel\"]\n1e:I[79155,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8974\",\"static/chunks/app/page-21b9296d3312bd31.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"default\"]\n23:I[90527,[\"2017\",\"static/chunks/2017-2dea88ab7bbf0806.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"4691\",\"static/chunks/4691-1343f9bda7ec37f4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"317\",\"static/chunks/317-1454bb561c0f2555.js?dpl=dpl_3cmAKMG2nbNb"])</script><script>self.__next_f.push([1,"FqH29ifBv5mM1txU\",\"8736\",\"static/chunks/8736-90e818752c3649eb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"9357\",\"static/chunks/9357-aa74c385602c01ae.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"2741\",\"static/chunks/2741-c02ac29299d656a3.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"7689\",\"static/chunks/7689-97e0d9e810202491.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1026\",\"static/chunks/1026-a2ae27648ea9eecb.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"1898\",\"static/chunks/1898-8e4bbdba06e0862b.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"8974\",\"static/chunks/app/page-21b9296d3312bd31.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"IconBackground\"]\n27:I[18206,[],\"MetadataBoundary\"]\n29:I[18206,[],\"OutletBoundary\"]\n2c:I[38670,[],\"AsyncMetadataOutlet\"]\n2e:I[18206,[],\"ViewportBoundary\"]\n30:I[94666,[\"4219\",\"static/chunks/app/global-error-bcc8c40bf96d1ce4.js?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\"],\"default\"]\n:HL[\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/d54bec00ea90a1ec.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/075d453bd5f7301f.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/db78b4e916a69931.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"style\"]\n:HL[\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"style\"]\n1a:T410,M5.85669 1.07837C6.13284 1.07837 6.35669 1.30223 6.35669 1.57837V4.07172C6.35669 4.34786 6.13284 4.57172 5.85669 4.57172C5.58055 4.57172 5.35669 4.34786 5.35669 4.07172V1.57837C5.35669 1.30223 5.58055 1.07837 5.85669 1.07837ZM1.51143 1.51679C1.70961 1.32449 2.02615 1.32925 2."])</script><script>self.__next_f.push([1,"21845 1.52743L4.3494 3.72353C4.5417 3.9217 4.53694 4.23825 4.33876 4.43055C4.14058 4.62285 3.82403 4.61809 3.63173 4.41991L1.50078 2.22381C1.30848 2.02564 1.31325 1.70909 1.51143 1.51679ZM5.10709 6.49114C4.74216 5.65659 5.59204 4.80844 6.42584 5.17508L14.3557 8.66199C15.2287 9.04582 15.1201 10.3175 14.1948 10.5478L11.1563 11.3041L10.4159 14.1716C10.1783 15.0916 8.91212 15.1928 8.53142 14.3222L5.10709 6.49114ZM13.9532 9.5774L6.02332 6.09049L9.44766 13.9216L10.2625 10.7658C10.3083 10.5882 10.4478 10.4499 10.6258 10.4056L13.9532 9.5774ZM1.04663 5.79688C1.04663 5.52073 1.27049 5.29688 1.54663 5.29688H3.99057C4.26671 5.29688 4.49057 5.52073 4.49057 5.79688C4.49057 6.07302 4.26671 6.29688 3.99057 6.29688H1.54663C1.27049 6.29688 1.04663 6.07302 1.04663 5.79688Z1b:T84f,"])</script><script>self.__next_f.push([1,"M1.62624 10.8978C1.22391 10.0142 1 9.03261 1 8C1 4.13401 4.13401 1 8 1C9.03686 1 10.0223 1.22575 10.9087 1.63122C11.2997 1.37784 11.766 1.23071 12.2665 1.23071C13.6473 1.23071 14.7665 2.35 14.7665 3.73071C14.7665 4.23073 14.6197 4.69646 14.3669 5.08716C14.7736 5.97467 15 6.96155 15 8C15 11.866 11.866 15 8 15C6.94896 15 5.95081 14.768 5.05508 14.3521C4.67477 14.5858 4.22715 14.7206 3.74805 14.7206C2.36733 14.7206 1.24805 13.6013 1.24805 12.2206C1.24805 11.7349 1.38656 11.2815 1.62624 10.8978ZM2 8C2 4.68629 4.68629 2 8 2C8.75898 2 9.48416 2.14069 10.1515 2.39715C9.90768 2.7831 9.76654 3.24042 9.76654 3.73071C9.76654 3.77457 9.76768 3.81815 9.76991 3.86145C9.22664 3.6288 8.62833 3.5 7.99994 3.5C5.51466 3.5 3.49994 5.51472 3.49994 8C3.49994 8.61006 3.62134 9.19177 3.8413 9.72228C3.81035 9.72115 3.77927 9.72058 3.74805 9.72058C3.24584 9.72058 2.77822 9.86866 2.38647 10.1235C2.13679 9.46389 2 8.74838 2 8ZM5.83493 13.5976C6.50608 13.8574 7.23593 14 8 14C11.3137 14 14 11.3137 14 8C14 7.23965 13.8588 6.51324 13.6015 5.84486C13.2152 6.08924 12.7574 6.23071 12.2665 6.23071C12.2232 6.23071 12.1802 6.22961 12.1374 6.22743C12.3707 6.77139 12.4999 7.3706 12.4999 8C12.4999 10.4853 10.4852 12.5 7.99994 12.5C7.37809 12.5 6.78569 12.3739 6.24695 12.1458C6.24768 12.1706 6.24805 12.1956 6.24805 12.2206C6.24805 12.7294 6.09603 13.2027 5.83493 13.5976ZM10.7665 3.73071C10.7665 2.90229 11.4381 2.23071 12.2665 2.23071C13.095 2.23071 13.7665 2.90229 13.7665 3.73071C13.7665 4.55914 13.095 5.23071 12.2665 5.23071C11.4381 5.23071 10.7665 4.55914 10.7665 3.73071ZM5.40407 10.3477C5.48532 10.4196 5.56185 10.4967 5.63315 10.5785C6.25623 11.1507 7.08729 11.5 7.99994 11.5C9.93294 11.5 11.4999 9.933 11.4999 8C11.4999 6.067 9.93294 4.5 7.99994 4.5C6.06695 4.5 4.49994 6.067 4.49994 8C4.49994 8.90336 4.84218 9.72678 5.40407 10.3477ZM3.74805 10.7206C4.11285 10.7206 4.44724 10.8508 4.70725 11.0673C4.77215 11.1369 4.83923 11.2045 4.90838 11.2699C5.12065 11.5287 5.24805 11.8598 5.24805 12.2206C5.24805 13.049 4.57647 13.7206 3.74805 13.7206C2.91962 13.7206 2.24805 13.049 2.24805 12.2206C2.24805 11.3921 2.91962 10.7206 3.74805 10.7206Z"])</script><script>self.__next_f.push([1,"1d:T58e,M14.364 4.774c.004.128.005.256.006.384l.001.439v5.245c-.002.128-.003.256-.007.384-.007.28-.024.561-.073.837-.05.28-.133.541-.263.796a2.673 2.673 0 0 1-1.17 1.17c-.254.13-.514.211-.795.262-.276.05-.558.066-.837.073-.128.004-.256.005-.384.006l-.408.001H5.565l-.407-.001c-.128 0-.256-.002-.384-.006a5.571 5.571 0 0 1-.837-.073c-.28-.05-.541-.133-.796-.262a2.674 2.674 0 0 1-1.17-1.17 2.824 2.824 0 0 1-.262-.796 5.582 5.582 0 0 1-.073-.837 18.375 18.375 0 0 1-.006-.384l-.001-.404V5.158c.002-.128.003-.256.007-.384.007-.28.024-.561.073-.837.05-.28.133-.541.262-.796a2.673 2.673 0 0 1 1.362-1.258c.194-.08.393-.136.604-.174.207-.037.417-.056.627-.066.07-.003.14-.006.21-.007.128-.004.256-.005.384-.006l.457-.001H10.842l.384.006c.28.008.561.024.837.074.28.05.541.133.796.262a2.671 2.671 0 0 1 1.17 1.17c.13.255.212.515.262.796.05.276.066.558.073.837Zm-2.64 4.72h.002c1.094 1.347.797 2.791.656 2.519-.572-1.114-1.638-.83-2.178-.55-.044.028-.09.053-.136.078l-.008.004a.306.306 0 0 0-.01.006l.002-.002c-1.124.597-2.632.642-4.149-.01A6.673 6.673 0 0 1 2.908 8.97c.345.255.718.48 1.114.665 1.603.75 3.213.697 4.352 0C6.753 8.386 5.4 6.762 4.361 5.446a5.644 5.644 0 0 1-.534-.736C5.07 5.85 7.033 7.277 7.737 7.672c-1.494-1.58-2.812-3.525-2.75-3.462 2.355 2.372 4.527 3.714 4.527 3.714.***************.192.113.044-.114.084-.232.118-.355.376-1.374-.047-2.946-1.004-4.243 2.184 1.311 3.474 3.802 2.946 5.91a75.282 75.282 0 0 0-.041.145Z1f:T98d,"])</script><script>self.__next_f.push([1,"M11.8048 1.75216C12.4795 1.0774 13.5735 1.07741 14.2483 1.75216C14.923 2.42691 14.923 3.52091 14.2483 4.19566C13.6764 4.76755 12.8033 4.85474 12.1396 4.45721L10.6936 5.90323C11.1446 6.48183 11.4133 7.20953 11.4133 7.99999C11.4133 8.76199 11.1636 9.46567 10.7416 10.0337L12.2103 11.5024C12.8642 11.152 13.6961 11.2526 14.2478 11.8043C14.9225 12.479 14.9225 13.573 14.2478 14.2478C13.573 14.9226 12.479 14.9226 11.8043 14.2478C11.2523 13.6958 11.1519 12.8633 11.503 12.2092L10.0346 10.7409C9.46646 11.1634 8.76241 11.4134 7.99997 11.4134C7.20951 11.4134 6.4818 11.1447 5.90321 10.6936L4.4574 12.1395C4.85471 12.8031 4.76747 13.676 4.19566 14.2478C3.52091 14.9226 2.42691 14.9226 1.75216 14.2478C1.0774 13.573 1.07741 12.479 1.75216 11.8043C2.28359 11.2729 3.07507 11.16 3.71698 11.4657L5.21234 9.9703C4.81821 9.4137 4.5866 8.73389 4.5866 7.99999C4.5866 7.23755 4.83658 6.5335 5.25904 5.96532L3.79073 4.497C3.13666 4.8481 2.30413 4.74766 1.75216 4.19569C1.07741 3.52093 1.07741 2.42694 1.75216 1.75218C2.42692 1.07743 3.52091 1.07743 4.19566 1.75219C4.74732 2.30385 4.84796 3.13573 4.49758 3.78964L5.96628 5.25834C6.53429 4.83631 7.23797 4.58662 7.99997 4.58662C8.73387 4.58662 9.41367 4.81824 9.97027 5.21236L11.466 3.71665C11.1605 3.0748 11.2734 2.2835 11.8048 1.75216ZM13.5412 2.45927C13.2569 2.17504 12.7961 2.17504 12.5119 2.45927C12.2277 2.7435 12.2277 3.20433 12.5119 3.48856C12.7961 3.77279 13.2569 3.77279 13.5412 3.48856C13.8254 3.20433 13.8254 2.7435 13.5412 2.45927ZM9.67618 9.73627C9.24205 10.1555 8.65112 10.4134 7.99997 10.4134C6.6671 10.4134 5.5866 9.33286 5.5866 7.99999C5.5866 6.66712 6.6671 5.58662 7.99997 5.58662C9.33283 5.58662 10.4133 6.66712 10.4133 7.99999C10.4133 8.65078 10.1557 9.24141 9.73695 9.67547C9.72611 9.68462 9.71556 9.6943 9.70535 9.70451C9.69509 9.71478 9.68536 9.72537 9.67618 9.73627ZM3.48856 2.45929C3.20433 2.17506 2.7435 2.17506 2.45927 2.45929C2.17504 2.74352 2.17504 3.20435 2.45927 3.48858C2.7435 3.77281 3.20433 3.77281 3.48856 3.48858C3.77279 3.20435 3.77279 2.74352 3.48856 2.45929ZM3.48856 12.5114C3.20433 12.2272 2.7435 12.2272 2.45927 12.5114C2.17504 12.7956 2.17504 13.2565 2.45927 13.5407C2.7435 13.8249 3.20433 13.8249 3.48856 13.5407C3.77279 13.2565 3.77279 12.7956 3.48856 12.5114ZM13.5407 12.5114C13.2564 12.2272 12.7956 12.2272 12.5114 12.5114C12.2271 12.7956 12.2271 13.2565 12.5114 13.5407C12.7956 13.8249 13.2564 13.8249 13.5407 13.5407C13.8249 13.2565 13.8249 12.7956 13.5407 12.5114Z"])</script><script>self.__next_f.push([1,"20:T4e1,M8.68972 4.35753C8.96122 4.40797 9.14042 4.66896 9.08998 4.94045L7.9225 11.2244C7.87206 11.4959 7.61107 11.6751 7.33958 11.6246C7.06808 11.5742 6.88888 11.3132 6.93932 11.0417L8.1068 4.75779C8.15724 4.48629 8.41822 4.30709 8.68972 4.35753ZM10.0165 6.07223C10.2118 5.87695 10.5284 5.87693 10.7236 6.07218L12.2979 7.64617C12.3916 7.73993 12.4443 7.86711 12.4443 7.99973C12.4443 8.13234 12.3917 8.25953 12.2979 8.3533L10.7237 9.92752C10.5284 10.1228 10.2118 10.1228 10.0166 9.92752C9.8213 9.73226 9.8213 9.41568 10.0166 9.22042L11.2372 7.99977L10.0166 6.77933C9.82131 6.58409 9.82129 6.2675 10.0165 6.07223ZM6.01227 9.9275C5.81702 10.1228 5.50044 10.1228 5.30516 9.92755L3.73094 8.35356C3.63716 8.2598 3.58447 8.13262 3.58447 8C3.58446 7.86739 3.63714 7.7402 3.73091 7.64643L5.30514 6.07221C5.5004 5.87694 5.81698 5.87694 6.01225 6.07221C6.20751 6.26747 6.20751 6.58405 6.01225 6.77931L4.7916 7.99996L6.01222 9.2204C6.2075 9.41564 6.20752 9.73223 6.01227 9.9275ZM1.5 4C1.5 2.61929 2.61929 1.5 4 1.5H12C13.3807 1.5 14.5 2.61929 14.5 4V12C14.5 13.3807 13.3807 14.5 12 14.5H4C2.61929 14.5 1.5 13.3807 1.5 12V4ZM4 2.5C3.17157 2.5 2.5 3.17157 2.5 4V12C2.5 12.8284 3.17157 13.5 4 13.5H12C12.8284 13.5 13.5 12.8284 13.5 12V4C13.5 3.17157 12.8284 2.5 12 2.5H4Z21:T5d5,M6.7156 1.77258C7.58712 1.14341 8.76326 1.14175 9.63655 1.76846L12.0629 3.50974C12.7162 3.97855 12.8569 4.83994 12.4888 5.4786L13.0896 5.9098C13.7748 6.4015 13.8962 7.32509 13.4579 7.97059L14.1753 8.4882C15.0124 9.09219 15.0035 10.3415 14.1578 10.9335L9.42209 14.2484C8.5592 14.8524 7.41031 14.8508 6.54911 14.2444L1.83872 10.9276C0.999787 10.3368 0.988308 9.09703 1.81616 8.49086L2.80076 7.76992C2.50724 7.14639 2.66597 6.35556 3.27999 5.91229L3.88264 5.47721C3.51614 4.83981 3.65602 3.98138 4.30587 3.51223L6.7156 1.77258ZM5.00242 5.21218C4.95761 5.16341 4.90473 5.12529 4.84735 5.09839C4.61595 4.88922 4.62987 4.51169 4.8912 4.32303L7.30093 2.58337C7.82385 2.20587 8.52953 2.20487 9.0535 2.5809L11.4799 4.32218C11.7403 4.50906 11.7564 4.88309 11.5302 5.09348C11.4651 5.12213 11.40"])</script><script>self.__next_f.push([1,"54 5.16499 11.3562 5.22122L9.03905 6.84318C8.52132 7.20559 7.83199 7.20462 7.31527 6.84078L5.00242 5.21218ZM4.62864 6.17202L3.86532 6.72308C3.58687 6.92411 3.58931 7.33956 3.87012 7.53729L7.31263 9.96135C7.82935 10.3252 8.51869 10.3262 9.03642 9.96376L12.5018 7.53808C12.7841 7.34045 12.7866 6.92317 12.5066 6.72224L11.7408 6.17267L9.6125 7.66242C8.74962 8.26643 7.60073 8.26482 6.73953 7.65841L4.62864 6.17202ZM3.49834 8.49855L2.40694 9.29769C2.13099 9.49975 2.13482 9.91301 2.41446 10.1099L7.12485 13.4268C7.64157 13.7906 8.3309 13.7916 8.84864 13.4292L13.5844 10.1143C13.8663 9.91694 13.8692 9.50049 13.5902 9.29916L12.674 8.63814L9.60987 10.783C8.74698 11.387 7.59809 11.3854 6.7369 10.779L3.49834 8.49855Z22:T717,M1.6748 3.08472C1.6748 2.25629 2.34638 1.58472 3.1748 1.58472H6.1748C7.00323 1.58472 7.6748 2.25629 7.6748 3.08472V6.08472C7.6748 6.91314 7.00323 7.58472 6.1748 7.58472H3.1748C2.34638 7.58472 1.6748 6.91314 1.6748 6.08472V3.08472ZM3.1748 2.58472C2.89866 2.58472 2.6748 2.80857 2.6748 3.08472V6.08472C2.6748 6.36086 2.89866 6.58472 3.1748 6.58472H6.1748C6.45095 6.58472 6.6748 6.36086 6.6748 6.08472V3.08472C6.6748 2.80857 6.45095 2.58472 6.1748 2.58472H3.1748ZM11.5107 1.58472C11.7869 1.58472 12.0107 1.80857 12.0107 2.08472V4.08496H14.0107C14.2869 4.08496 14.5107 4.30882 14.5107 4.58496C14.5107 4.8611 14.2869 5.08496 14.0107 5.08496H12.0107V7.08472C12.0107 7.36086 11.7869 7.58472 11.5107 7.58472C11.2346 7.58472 11.0107 7.36086 11.0107 7.08472V5.08496H9.01074C8.7346 5.08496 8.51074 4.8611 8.51074 4.58496C8.51074 4.30882 8.7346 4.08496 9.01074 4.08496H11.0107V2.08472C11.0107 1.80857 11.2346 1.58472 11.5107 1.58472ZM4.67432 9.04516C3.46754 9.04516 2.48926 10.0234 2.48926 11.2302C2.48926 12.437 3.46754 13.4153 4.67432 13.4153C5.88109 13.4153 6.85938 12.437 6.85938 11.2302C6.85938 10.0234 5.88109 9.04516 4.67432 9.04516ZM1.48926 11.2302C1.48926 9.47116 2.91526 8.04516 4.67432 8.04516C6.43338 8.04516 7.85938 9.47116 7.85938 11.2302C7.85938 12.9893 6.43338 14.4153 4.67432 14.4153C2.91526 14.4153 1.48926 12.9893 1.48926 1"])</script><script>self.__next_f.push([1,"1.2302ZM8.51074 9.83423C8.51074 9.0058 9.18232 8.33423 10.0107 8.33423H13.0107C13.8392 8.33423 14.5107 9.0058 14.5107 9.83423V12.8342C14.5107 13.6627 13.8392 14.3342 13.0107 14.3342H10.0107C9.18231 14.3342 8.51074 13.6627 8.51074 12.8342V9.83423ZM10.0107 9.33423C9.7346 9.33423 9.51074 9.55809 9.51074 9.83423V12.8342C9.51074 13.1104 9.7346 13.3342 10.0107 13.3342H13.0107C13.2869 13.3342 13.5107 13.1104 13.5107 12.8342V9.83423C13.5107 9.55809 13.2869 9.33423 13.0107 9.33423H10.0107Z24:T788,M3.97943 1.49988C3.151 1.49988 2.47943 2.17145 2.47943 2.99988V4.99988C2.47943 5.61515 2.84987 6.1439 3.37989 6.37526V9.62473C2.84987 9.85609 2.47943 10.3848 2.47943 11.0001V13C2.47943 13.8285 3.151 14.5 3.97943 14.5H7.51391C7.51982 14.5001 7.52575 14.5001 7.53168 14.5001H12.0206C12.849 14.5001 13.5206 13.8286 13.5206 13.0001V12.0851C13.5206 10.6081 12.6654 9.33078 11.4232 8.72137C11.946 8.26309 12.2761 7.59029 12.2761 6.84045C12.2761 6.71652 12.2671 6.5947 12.2497 6.4756C12.9491 6.34833 13.4794 5.73603 13.4794 4.99988V2.99988C13.4794 2.17145 12.8079 1.49988 11.9794 1.49988H3.97943ZM11.1343 6.20304C11.1364 6.20772 11.1386 6.21237 11.1408 6.21697C11.2277 6.40684 11.2761 6.618 11.2761 6.84045C11.2761 7.66893 10.6045 8.34062 9.77613 8.34062C8.94776 8.34062 8.27613 7.66893 8.27613 6.84045C8.27613 6.01203 8.9477 5.34045 9.77613 5.34045C10.3766 5.34045 10.8947 5.69333 11.1343 6.20304ZM11.8867 5.49988H11.9794C12.2556 5.49988 12.4794 5.27602 12.4794 4.99988V2.99988C12.4794 2.72374 12.2556 2.49988 11.9794 2.49988H3.97943C3.70329 2.49988 3.47943 2.72374 3.47943 2.99988V4.99988C3.47943 5.27602 3.70329 5.49988 3.97943 5.49988H7.66558C8.10923 4.80286 8.88868 4.34045 9.77613 4.34045C10.6636 4.34045 11.443 4.80286 11.8867 5.49988ZM7.29913 6.49988H4.37989V9.50011H7.07418C7.37693 9.18356 7.73443 8.91934 8.1315 8.72347C7.60726 8.26515 7.27613 7.59143 7.27613 6.84045C7.27613 6.72493 7.28396 6.61124 7.29913 6.49988ZM6.38668 10.5001H3.97943C3.70329 10.5001 3.47943 10.724 3.47943 11.0001V13C3.47943 13.2762 3.70329 13.5 3.97943 13.5H6.11674C6.06165 13."])</script><script>self.__next_f.push([1,"344 6.03168 13.176 6.03168 13.0009V12.0851C6.03168 11.5208 6.15894 10.9831 6.38668 10.5001ZM7.80624 13.5001C7.80283 13.5001 7.79941 13.5 7.79599 13.5H7.5214C7.24953 13.4946 7.03168 13.2731 7.03168 13.0009V12.0851C7.03168 10.5867 8.26234 9.34062 9.77613 9.34062C11.2918 9.34062 12.5206 10.5694 12.5206 12.0851V13.0001C12.5206 13.2763 12.2967 13.5001 12.0206 13.5001H7.80624Z25:T410,M5.85669 1.07837C6.13284 1.07837 6.35669 1.30223 6.35669 1.57837V4.07172C6.35669 4.34786 6.13284 4.57172 5.85669 4.57172C5.58055 4.57172 5.35669 4.34786 5.35669 4.07172V1.57837C5.35669 1.30223 5.58055 1.07837 5.85669 1.07837ZM1.51143 1.51679C1.70961 1.32449 2.02615 1.32925 2.21845 1.52743L4.3494 3.72353C4.5417 3.9217 4.53694 4.23825 4.33876 4.43055C4.14058 4.62285 3.82403 4.61809 3.63173 4.41991L1.50078 2.22381C1.30848 2.02564 1.31325 1.70909 1.51143 1.51679ZM5.10709 6.49114C4.74216 5.65659 5.59204 4.80844 6.42584 5.17508L14.3557 8.66199C15.2287 9.04582 15.1201 10.3175 14.1948 10.5478L11.1563 11.3041L10.4159 14.1716C10.1783 15.0916 8.91212 15.1928 8.53142 14.3222L5.10709 6.49114ZM13.9532 9.5774L6.02332 6.09049L9.44766 13.9216L10.2625 10.7658C10.3083 10.5882 10.4478 10.4499 10.6258 10.4056L13.9532 9.5774ZM1.04663 5.79688C1.04663 5.52073 1.27049 5.29688 1.54663 5.29688H3.99057C4.26671 5.29688 4.49057 5.52073 4.49057 5.79688C4.49057 6.07302 4.26671 6.29688 3.99057 6.29688H1.54663C1.27049 6.29688 1.04663 6.07302 1.04663 5.79688Z26:T5d5,M6.7156 1.77258C7.58712 1.14341 8.76326 1.14175 9.63655 1.76846L12.0629 3.50974C12.7162 3.97855 12.8569 4.83994 12.4888 5.4786L13.0896 5.9098C13.7748 6.4015 13.8962 7.32509 13.4579 7.97059L14.1753 8.4882C15.0124 9.09219 15.0035 10.3415 14.1578 10.9335L9.42209 14.2484C8.5592 14.8524 7.41031 14.8508 6.54911 14.2444L1.83872 10.9276C0.999787 10.3368 0.988308 9.09703 1.81616 8.49086L2.80076 7.76992C2.50724 7.14639 2.66597 6.35556 3.27999 5.91229L3.88264 5.47721C3.51614 4.83981 3.65602 3.98138 4.30587 3.51223L6.7156 1.77258ZM5.00242 5.21218C4.95761 5.16341 4.90473 5.12529 4.84735 5.09839C4.61595 4.88922 4.62987 4.5116"])</script><script>self.__next_f.push([1,"9 4.8912 4.32303L7.30093 2.58337C7.82385 2.20587 8.52953 2.20487 9.0535 2.5809L11.4799 4.32218C11.7403 4.50906 11.7564 4.88309 11.5302 5.09348C11.4651 5.12213 11.4054 5.16499 11.3562 5.22122L9.03905 6.84318C8.52132 7.20559 7.83199 7.20462 7.31527 6.84078L5.00242 5.21218ZM4.62864 6.17202L3.86532 6.72308C3.58687 6.92411 3.58931 7.33956 3.87012 7.53729L7.31263 9.96135C7.82935 10.3252 8.51869 10.3262 9.03642 9.96376L12.5018 7.53808C12.7841 7.34045 12.7866 6.92317 12.5066 6.72224L11.7408 6.17267L9.6125 7.66242C8.74962 8.26643 7.60073 8.26482 6.73953 7.65841L4.62864 6.17202ZM3.49834 8.49855L2.40694 9.29769C2.13099 9.49975 2.13482 9.91301 2.41446 10.1099L7.12485 13.4268C7.64157 13.7906 8.3309 13.7916 8.84864 13.4292L13.5844 10.1143C13.8663 9.91694 13.8692 9.50049 13.5902 9.29916L12.674 8.63814L9.60987 10.783C8.74698 11.387 7.59809 11.3854 6.7369 10.779L3.49834 8.49855Z"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"vX9AygYrcmcNOMiYTMgwB\",\"p\":\"https://frontend-assets.supabase.com/docs/0c0be801ae1d\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/4df6f4fab70c41f4.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/d54bec00ea90a1ec.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"2\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/075d453bd5f7301f.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"3\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/db78b4e916a69931.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"4\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"5\",{\"rel\":\"stylesheet\",\"href\":\"https://frontend-assets.supabase.com/docs/0c0be801ae1d/_next/static/css/bd3c93ad86eb9cae.css?dpl=dpl_3cmAKMG2nbNbFqH29ifBv5mM1txU\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"children\":[[\"$\",\"$L2\",null,{}],[\"$\",\"$L3\",null,{\"children\":[\"$\",\"$L4\",null,{\"children\":[\"$\",\"$L5\",null,{\"API_URL\":\"https://api.supabase.com\",\"enabled\":true,\"children\":[[\"$\",\"$L6\",null,{}],[\"$\",\"$L7\",null,{}],[\"$\",\"$L8\",null,{\"defaultTheme\":\"system\",\"enableSystem\":true,\"disableTransitionOnChange\":true,\"children\":[\"$\",\"$L9\",null,{\"delayDuration\":0,\"children\":[[\"$\",\"$La\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-col\",\"children\":[[\"$\",\"div\",null,{\"className\":\"grow\",\"children\":[[\"$\",\"$Lb\",null,{}],[\"$\",\"$Lc\",null,{\"children\":[\"$\",\"$Ld\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$e\",\"errorStyles\":[],\"errorScripts\":[],\"template\":[\"$\",\"$Lf\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"$L10\",null,{\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl px-5 mx-auto py-8\",\"children\":[\"$\",\"article\",null,{\"className\":\"prose max-w-[80ch]\",\"children\":[[\"$\",\"h1\",null,{\"children\":\"404: We couldn't find that page\"}],[\"$\",\"p\",null,{\"children\":\"Sorry, we couldn't find that page. It might be missing, or we had a temporary error generating it.\"}],[\"$\",\"div\",null,{\"className\":\"flex flex-wrap gap-4 pt-4\",\"children\":[[\"$\",\"$L11\",null,{}],[\"$\",\"$L12\",null,{\"type\":\"default\",\"size\":\"small\",\"className\":\"p-4\",\"asChild\":true,\"children\":[\"$\",\"$L13\",null,{\"href\":\"/\",\"className\":\"no-underline\",\"children\":\"Return to homepage\"}]}],[\"$\",\"$L12\",null,{\"type\":\"text\",\"size\":\"small\",\"asChild\":true,\"children\":[\"$\",\"$L13\",null,{\"href\":\"https://github.com/supabase/supabase/issues/new?assignees=\u0026labels=documentation\u0026projects=\u0026template=2.Improve_docs.md\",\"target\":\"_blank\",\"rel\":\"noreferrer noopener\",\"className\":\"no-underline\",\"children\":\"Report missing page\"}]}]]}],[\"$\",\"$L14\",null,{}]]}]}]}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}],[\"$\",\"$L15\",null,{}]]}],[\"$\",\"$L16\",null,{}]]}]}],[\"$\",\"$L17\",null,{\"position\":\"top-right\"}]]}]}]]}]}]}]]}]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L10\",null,{\"hideSideNav\":true,\"children\":[\"$\",\"article\",null,{\"children\":[[\"$\",\"$L18\",null,{\"title\":\"Supabase Documentation\"}],[\"$\",\"div\",null,{\"className\":\"max-w-7xl px-5 mx-auto py-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"relative transition-all ease-out duration-150 \",\"children\":[\"$\",\"div\",null,{\"className\":\"prose max-w-none\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-col\",\"children\":[[\"$\",\"h2\",null,{\"id\":\"products\",\"children\":\"Products\"}],[\"$\",\"ul\",null,{\"className\":\"grid grid-cols-12 gap-6 not-prose [\u0026_svg]:text-brand-600\",\"children\":[[\"$\",\"li\",\"Database\",{\"className\":\"col-span-12 md:col-span-6\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/guides/database/overview\",\"passHref\":true,\"children\":[\"$\",\"$L19\",null,{\"icon\":[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"className\":\"$undefined\",\"width\":18,\"height\":18,\"viewBox\":\"0 0 16 16\",\"fill\":\"none\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"clipRule\":\"evenodd\",\"d\":\"M2.5 2.99915C2.5 2.17072 3.17157 1.49915 4 1.49915H12C12.8284 1.49915 13.5 2.17072 13.5 2.99915V4.99915C13.5 5.53212 13.222 6.00017 12.8032 6.26623V9.73377C13.222 9.99983 13.5 10.4679 13.5 11.0009V13.0009C13.5 13.8293 12.8284 14.5009 12 14.5009H4C3.17157 14.5009 2.5 13.8293 2.5 13.0009V11.0009C2.5 10.4615 2.78461 9.98872 3.21183 9.72437V6.27563C2.78461 6.01128 2.5 5.53845 2.5 4.99915V2.99915ZM12.0158 5.4989H3.98422C3.71538 5.49057 3.5 5.27001 3.5 4.99915V2.99915C3.5 2.723 3.72386 2.49915 4 2.49915H12C12.2761 2.49915 12.5 2.723 12.5 2.99915V4.99915C12.5 5.27001 12.2846 5.49057 12.0158 5.4989ZM4.21183 6.49915V9.4989H11.8032V6.49915H4.21183ZM4 10.5009C3.72386 10.5009 3.5 10.7247 3.5 11.0009V13.0009C3.5 13.277 3.72386 13.5009 4 13.5009H12C12.2761 13.5009 12.5 13.277 12.5 13.0009V11.0009C12.5 10.7247 12.2761 10.5009 12 10.5009H4Z\",\"fill\":\"currentColor\"}]}],\"title\":\"Database\",\"hasLightIcon\":true,\"href\":\"/guides/database/overview\",\"description\":\"Supabase provides a full Postgres database for every project with Realtime functionality, database backups, extensions, and more.\",\"span\":\"col-span-12 md:col-span-6\",\"children\":\"Supabase provides a full Postgres database for every project with Realtime functionality, database backups, extensions, and more.\"}]}]}],[\"$\",\"li\",\"Auth\",{\"className\":\"col-span-12 md:col-span-6\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/guides/auth\",\"passHref\":true,\"children\":[\"$\",\"$L19\",null,{\"icon\":[\"$\",\"svg\",null,{\"className\":\"$undefined\",\"viewBox\":\"0 0 16 16\",\"width\":18,\"height\":18,\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"children\":[\"$\",\"path\",null,{\"d\":\"M3.49414 9.97461H8.49414M3.49414 9.97461V11.9746H8.49414V9.97461M3.49414 9.97461V7.97461H8.49414V9.97461M10 5V3C10 1.89543 9.10457 1 8 1C6.89543 1 6 1.89543 6 3V5M3.47266 7L3.47266 12C3.47266 13.1046 4.36809 14 5.47266 14H10.4727C11.5772 14 12.4727 13.1046 12.4727 12V7C12.4727 5.89543 11.5772 5 10.4727 5L5.47266 5C4.36809 5 3.47266 5.89543 3.47266 7Z\",\"stroke\":\"currentColor\",\"strokeMiterlimit\":\"10\",\"strokeLinejoin\":\"bevel\"}]}],\"title\":\"Auth\",\"hasLightIcon\":true,\"href\":\"/guides/auth\",\"description\":\"Add and manage email and password, passwordless, OAuth, and mobile logins to your project through a suite of identity providers and APIs.\",\"span\":\"col-span-12 md:col-span-6\",\"children\":\"Add and manage email and password, passwordless, OAuth, and mobile logins to your project through a suite of identity providers and APIs.\"}]}]}],[\"$\",\"li\",\"Storage\",{\"className\":\"col-span-12 md:col-span-4\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/guides/storage\",\"passHref\":true,\"children\":[\"$\",\"$L19\",null,{\"icon\":[\"$\",\"svg\",null,{\"className\":\"$undefined\",\"viewBox\":\"0 0 16 16\",\"width\":18,\"height\":18,\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"children\":[\"$\",\"path\",null,{\"d\":\"M12.9997 7.50869V5.60119L9.38151 2.00024H3.99967C3.44739 2.00024 2.99967 2.44796 2.99967 3.00024V5.99976M12.9645 5.58447L9.38004 2L9.38004 4.58447C9.38004 5.13676 9.82776 5.58447 10.38 5.58447L12.9645 5.58447ZM4.44135 5.99976H2.97363C2.42135 5.99976 1.97363 6.44747 1.97363 6.99976V11.9998C1.97363 13.1043 2.86906 13.9998 3.97363 13.9998H11.9736C13.0782 13.9998 13.9736 13.1043 13.9736 11.9998V8.50869C13.9736 7.95641 13.5259 7.50869 12.9736 7.50869H6.79396C6.53157 7.50869 6.27968 7.40556 6.09263 7.22153L5.14268 6.28692C4.95563 6.10289 4.70375 5.99976 4.44135 5.99976Z\",\"stroke\":\"currentColor\",\"strokeMiterlimit\":\"10\",\"strokeLinejoin\":\"bevel\"}]}],\"title\":\"Storage\",\"hasLightIcon\":true,\"href\":\"/guides/storage\",\"description\":\"Store, organize, transform, and serve large files—fully integrated with your Postgres database with Row Level Security access policies.\",\"children\":\"Store, organize, transform, and serve large files—fully integrated with your Postgres database with Row Level Security access policies.\"}]}]}],[\"$\",\"li\",\"Realtime\",{\"className\":\"col-span-12 md:col-span-4\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/guides/realtime\",\"passHref\":true,\"children\":[\"$\",\"$L19\",null,{\"icon\":[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"className\":\"$undefined\",\"width\":18,\"height\":18,\"viewBox\":\"0 0 16 16\",\"fill\":\"none\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"clipRule\":\"evenodd\",\"d\":\"$1a\",\"fill\":\"currentColor\"}]}],\"title\":\"Realtime\",\"hasLightIcon\":true,\"href\":\"/guides/realtime\",\"description\":\"Listen to database changes, store and sync user states across clients, broadcast data to clients subscribed to a channel, and more.\",\"children\":\"Listen to database changes, store and sync user states across clients, broadcast data to clients subscribed to a channel, and more.\"}]}]}],[\"$\",\"li\",\"Edge Functions\",{\"className\":\"col-span-12 md:col-span-4\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/guides/functions\",\"passHref\":true,\"children\":[\"$\",\"$L19\",null,{\"icon\":[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"className\":\"$undefined\",\"width\":18,\"height\":18,\"viewBox\":\"0 0 16 16\",\"fill\":\"none\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"clipRule\":\"evenodd\",\"d\":\"$1b\",\"fill\":\"currentColor\"}]}],\"title\":\"Edge Functions\",\"hasLightIcon\":true,\"href\":\"/guides/functions\",\"description\":\"Globally distributed, server-side functions to execute your code closest to your users for the lowest latency.\",\"children\":\"Globally distributed, server-side functions to execute your code closest to your users for the lowest latency.\"}]}]}]]}],[\"$\",\"div\",null,{\"className\":\"flex flex-col lg:grid grid-cols-12 gap-6 py-12 border-b\",\"children\":[[\"$\",\"div\",null,{\"className\":\"col-span-4\",\"children\":[\"$\",\"h2\",null,{\"id\":\"postgres-integrations\",\"className\":\"scroll-mt-24 m-0\",\"children\":\"Postgres Modules\"}]}],[\"$\",\"div\",null,{\"className\":\"grid col-span-8 grid-cols-12 gap-6 not-prose\",\"children\":[[\"$\",\"$L13\",\"AI \u0026 Vectors\",{\"href\":\"/guides/ai\",\"passHref\":true,\"className\":\"col-span-6 md:col-span-4\",\"children\":[\"$\",\"$L1c\",null,{\"icon\":[\"$\",\"svg\",null,{\"className\":\"$undefined\",\"width\":18,\"height\":18,\"viewBox\":\"0 0 16 16\",\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"children\":[\"$\",\"path\",null,{\"d\":\"M7.99886 7.63216V14.4892M7.99886 7.63216L14.0488 4.11804M7.99886 7.63216L1.94922 4.11819M1.94922 4.11819V8.32332M1.94922 4.11819V4.08217L5.57319 1.97717M14.049 8.36007V4.08217L10.4251 1.97717M11.8165 12.4072L7.99913 14.6245L4.18177 12.4072\",\"stroke\":\"currentColor\",\"strokeMiterlimit\":\"10\",\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\"}]}],\"title\":\"AI \u0026 Vectors\",\"href\":\"/guides/ai\",\"description\":\"AI toolkit to manage embeddings\"}]}],[\"$\",\"$L13\",\"Cron\",{\"href\":\"/guides/cron\",\"passHref\":true,\"className\":\"col-span-6 md:col-span-4\",\"children\":[\"$\",\"$L1c\",null,{\"icon\":[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":18,\"height\":18,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-clock\",\"children\":[[\"$\",\"circle\",\"1mglay\",{\"cx\":\"12\",\"cy\":\"12\",\"r\":\"10\"}],[\"$\",\"polyline\",\"68esgv\",{\"points\":\"12 6 12 12 16 14\"}],\"$undefined\"]}],\"title\":\"Cron\",\"href\":\"/guides/cron\",\"description\":\"Schedule and monitor recurring Jobs\"}]}],[\"$\",\"$L13\",\"Queues\",{\"href\":\"/guides/queues\",\"passHref\":true,\"className\":\"col-span-6 md:col-span-4\",\"children\":[\"$\",\"$L1c\",null,{\"icon\":[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":18,\"height\":18,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-square-stack\",\"children\":[[\"$\",\"path\",\"4i38lg\",{\"d\":\"M4 10c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h4c1.1 0 2 .9 2 2\"}],[\"$\",\"path\",\"mlte4a\",{\"d\":\"M10 16c-1.1 0-2-.9-2-2v-4c0-1.1.9-2 2-2h4c1.1 0 2 .9 2 2\"}],[\"$\",\"rect\",\"1fa9i4\",{\"width\":\"8\",\"height\":\"8\",\"x\":\"14\",\"y\":\"14\",\"rx\":\"2\"}],\"$undefined\"]}],\"title\":\"Queues\",\"href\":\"/guides/queues\",\"description\":\"Durable Message Queues with guaranteed delivery\"}]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"flex flex-col lg:grid grid-cols-12 gap-6 py-12 border-b\",\"children\":[[\"$\",\"div\",null,{\"className\":\"col-span-4 flex flex-col gap-1 [\u0026_h2]:m-0 [\u0026_h3]:m-0\",\"children\":[\"$\",\"div\",null,{\"className\":\"md:max-w-xs 2xl:max-w-none\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex items-center gap-3 mb-3 text-brand-600\",\"children\":[\"$\",\"h2\",null,{\"id\":\"client-libraries\",\"className\":\"group scroll-mt-24\",\"children\":\"Client Libraries\"}]}]}]}],[\"$\",\"div\",null,{\"className\":\"grid col-span-8 grid-cols-12 gap-6 not-prose\",\"children\":[[\"$\",\"$L13\",\"Javascript\",{\"href\":\"/reference/javascript/introduction\",\"passHref\":true,\"className\":\"col-span-6 md:col-span-4\",\"children\":[\"$\",\"$L1c\",null,{\"icon\":[\"$\",\"svg\",null,{\"className\":\"$undefined\",\"width\":18,\"height\":18,\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 16 16\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"clipRule\":\"evenodd\",\"d\":\"M2 2h12v12H2V2Zm9.173 10.06c-.556 0-.87-.29-1.112-.685l-.916.533c.33.654 1.007 1.153 2.054 1.153 1.071 0 1.869-.556 1.869-1.572 0-.941-.541-1.36-1.5-1.771l-.281-.12c-.484-.21-.693-.347-.693-.686 0-.273.209-.483.54-.483.323 0 .532.137.725.483l.878-.563c-.371-.654-.887-.903-1.604-.903-1.007 0-1.651.644-1.651 1.49 0 .917.54 1.352 1.354 1.698l.282.121c.514.225.821.362.821.749 0 .322-.299.556-.766.556Zm-4.37-.007c-.387 0-.548-.266-.726-.58l-.917.556c.265.562.788 1.03 1.691 1.03 1 0 1.684-.532 1.684-1.7V7.51H7.407v3.834c0 .564-.233.709-.604.709Z\",\"fill\":\"currentColor\"}]}],\"title\":\"Javascript\",\"href\":\"/reference/javascript/introduction\"}]}],[\"$\",\"$L13\",\"Flutter\",{\"href\":\"/reference/dart/introduction\",\"passHref\":true,\"className\":\"col-span-6 md:col-span-4\",\"children\":[\"$\",\"$L1c\",null,{\"icon\":[\"$\",\"svg\",null,{\"className\":\"$undefined\",\"viewBox\":\"0 0 16 16\",\"width\":18,\"height\":18,\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"clipRule\":\"evenodd\",\"d\":\"m11.857 2-3.718.004L2.143 8l1.85 1.852 1.626-1.617L11.857 2ZM8.245 7.531c-.052-.002-.107-.005-.14.04l-3.198 3.197 1.836 1.825-.002.002 1.315 1.316a.549.549 0 0 1 .026.025c.***************.13.062.607-.002 1.214-.002 1.821-.001l1.822-.001-3.232-3.235 3.23-3.23H8.31a.39.39 0 0 1-.064 0Z\",\"fill\":\"currentColor\"}]}],\"title\":\"Flutter\",\"href\":\"/reference/dart/introduction\"}]}],[\"$\",\"$L13\",\"Python\",{\"href\":\"/reference/python/introduction\",\"passHref\":true,\"className\":\"col-span-6 md:col-span-4\",\"children\":[\"$\",\"$L1c\",null,{\"icon\":[\"$\",\"svg\",null,{\"className\":\"$undefined\",\"viewBox\":\"0 0 16 16\",\"width\":18,\"height\":18,\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"clipRule\":\"evenodd\",\"d\":\"M6.424 1.627A9.05 9.05 0 0 1 7.923 1.5a9.885 9.885 0 0 1 1.633.127c.851.14 1.568.771 1.568 1.612v2.953a1.57 1.57 0 0 1-1.568 1.576H6.424c-1.063 0-1.959.903-1.959 1.927v1.416H3.387c-.911 0-1.443-.654-1.666-1.572-.3-1.233-.288-1.97 0-3.152.25-1.03 1.047-1.572 1.959-1.572h4.312V4.42H4.856V3.239c0-.895.241-1.38 1.568-1.612Zm.391 1.417a.592.592 0 0 0-.588-.593.59.59 0 0 0 0 *********** 0 0 0 .588-.59Zm4.7 3.148V4.815h1.177c.912 0 1.342.675 1.568 1.572.313 1.246.327 2.18 0 3.152-.317.944-.657 1.572-1.568 1.572h-4.7v.394h3.132v1.182c0 .896-.778 1.35-1.568 1.577-1.187.34-2.14.288-3.132 0-.829-.242-1.568-.736-1.568-1.576V9.733c0-.85.71-1.576 1.568-1.576h3.132c1.044 0 1.96-.898 1.96-1.966Zm-1.173 6.69a.589.589 0 1 0-1.177 0c0 .328.265.594.589.594a.59.59 0 0 0 .588-.593Z\",\"fill\":\"currentColor\"}]}],\"title\":\"Python\",\"href\":\"/reference/python/introduction\"}]}],[\"$\",\"$L13\",\"C#\",{\"href\":\"/reference/csharp/introduction\",\"passHref\":true,\"className\":\"col-span-6 md:col-span-4\",\"children\":[\"$\",\"$L1c\",null,{\"icon\":[\"$\",\"svg\",null,{\"className\":\"$undefined\",\"viewBox\":\"0 0 16 16\",\"width\":18,\"height\":18,\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"clipRule\":\"evenodd\",\"d\":\"m2.242 11.3.004-.003a1.191 1.191 0 0 1-.134-.57V5.27c0-.442.185-.784.57-1.005.444-.259 1.393-.804 2.33-1.343 1.019-.587 2.024-1.165 2.35-1.356.42-.247.826-.254 1.25-.01l2.37 1.364 2.336 1.345c.**************.425.414.098.167.145.366.145.587V10.73c-.001.216-.048.407-.138.573a1.089 1.089 0 0 1-.432.428l-1.786 1.028c-.973.56-1.947 1.12-2.92 1.682-.388.228-.767.236-1.155.042a1.65 1.65 0 0 1-.103-.056c-.382-.227-1.702-.986-2.881-1.664-.747-.429-1.437-.826-1.799-1.036a1.137 1.137 0 0 1-.432-.428Zm7.452-2.351a1.95 1.95 0 0 1-1.698.994 1.94 1.94 0 0 1-1.69-.983A1.946 1.946 0 0 1 9.68 7.02l1.7-.98a3.91 3.91 0 1 0-3.385 5.866 3.913 3.913 0 0 0 3.4-1.974l-1.701-.983Zm2.151-1.88h-.388v.316h-.312v.388h.312v.468h-.312v.388h.312v.316h.388v-.316h.472v.316h.388v-.316h.316v-.388h-.316v-.468h.316v-.388h-.316V7.07h-.388v.316h-.472V7.07Zm0 1.172v-.468h.472v.468h-.472Z\",\"fill\":\"currentColor\"}]}],\"title\":\"C#\",\"href\":\"/reference/csharp/introduction\"}]}],[\"$\",\"$L13\",\"Swift\",{\"href\":\"/reference/swift/introduction\",\"passHref\":true,\"className\":\"col-span-6 md:col-span-4\",\"children\":[\"$\",\"$L1c\",null,{\"icon\":[\"$\",\"svg\",null,{\"className\":\"$undefined\",\"viewBox\":\"0 0 16 16\",\"width\":18,\"height\":18,\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"clipRule\":\"evenodd\",\"d\":\"$1d\",\"fill\":\"currentColor\"}]}],\"title\":\"Swift\",\"href\":\"/reference/swift/introduction\"}]}],[\"$\",\"$L13\",\"Kotlin\",{\"href\":\"/reference/kotlin/introduction\",\"passHref\":true,\"className\":\"col-span-6 md:col-span-4\",\"children\":[\"$\",\"$L1c\",null,{\"icon\":[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"className\":\"$undefined\",\"viewBox\":\"0 0 16 16\",\"width\":18,\"height\":18,\"fill\":\"none\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"clipRule\":\"evenodd\",\"d\":\"M14 14H2V2H14L8 8L14 14Z\",\"fill\":\"currentColor\"}]}],\"title\":\"Kotlin\",\"href\":\"/reference/kotlin/introduction\"}]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"flex flex-col lg:grid grid-cols-12 gap-6 py-12 border-b\",\"children\":[[\"$\",\"div\",null,{\"className\":\"col-span-4 flex flex-col gap-1 [\u0026_h2]:m-0\",\"children\":[[\"$\",\"h2\",null,{\"id\":\"migrate-to-supabase\",\"className\":\"group scroll-mt-24\",\"children\":\"Migrate to Supabase\"}],[\"$\",\"p\",null,{\"className\":\"text-foreground-light text-sm p-0 m-0\",\"children\":\"Bring your existing data, auth and storage to Supabase following our migration guides.\"}],[\"$\",\"$L1e\",null,{\"label\":\"Explore more resources\",\"url\":\"/guides/resources\",\"className\":\"no-underline text-brand text-sm\"}]]}],[\"$\",\"ul\",null,{\"className\":\"grid col-span-8 grid-cols-12 gap-6 not-prose\",\"children\":[[\"$\",\"li\",\"Amazon RDS\",{\"className\":\"col-span-6 md:col-span-4\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/guides/platform/migrating-to-supabase/amazon-rds\",\"passHref\":true,\"children\":[\"$\",\"$L1c\",null,{\"name\":\"Amazon RDS\",\"icon\":\"/docs/img/icons/aws-rds-icon\",\"url\":\"/guides/platform/migrating-to-supabase/amazon-rds\",\"title\":\"Amazon RDS\",\"background\":true,\"showLink\":false}]}]}],[\"$\",\"li\",\"Auth0\",{\"className\":\"col-span-6 md:col-span-4\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/guides/platform/migrating-to-supabase/auth0\",\"passHref\":true,\"children\":[\"$\",\"$L1c\",null,{\"name\":\"Auth0\",\"icon\":\"/docs/img/icons/auth0-icon\",\"url\":\"/guides/platform/migrating-to-supabase/auth0\",\"hasLightIcon\":true,\"title\":\"Auth0\",\"background\":true,\"showLink\":false}]}]}],[\"$\",\"li\",\"Firebase Auth\",{\"className\":\"col-span-6 md:col-span-4\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/guides/platform/migrating-to-supabase/firebase-auth\",\"passHref\":true,\"children\":[\"$\",\"$L1c\",null,{\"name\":\"Firebase Auth\",\"icon\":\"/docs/img/icons/firebase-icon\",\"url\":\"/guides/platform/migrating-to-supabase/firebase-auth\",\"title\":\"Firebase Auth\",\"background\":true,\"showLink\":false}]}]}],[\"$\",\"li\",\"Firebase Storage\",{\"className\":\"col-span-6 md:col-span-4\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/guides/platform/migrating-to-supabase/firebase-storage\",\"passHref\":true,\"children\":[\"$\",\"$L1c\",null,{\"name\":\"Firebase Storage\",\"icon\":\"/docs/img/icons/firebase-icon\",\"url\":\"/guides/platform/migrating-to-supabase/firebase-storage\",\"title\":\"Firebase Storage\",\"background\":true,\"showLink\":false}]}]}],[\"$\",\"li\",\"Firestore Data\",{\"className\":\"col-span-6 md:col-span-4\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/guides/platform/migrating-to-supabase/firestore-data\",\"passHref\":true,\"children\":[\"$\",\"$L1c\",null,{\"name\":\"Firestore Data\",\"icon\":\"/docs/img/icons/firebase-icon\",\"url\":\"/guides/platform/migrating-to-supabase/firestore-data\",\"title\":\"Firestore Data\",\"background\":true,\"showLink\":false}]}]}],[\"$\",\"li\",\"Heroku\",{\"className\":\"col-span-6 md:col-span-4\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/guides/platform/migrating-to-supabase/heroku\",\"passHref\":true,\"children\":[\"$\",\"$L1c\",null,{\"name\":\"Heroku\",\"icon\":\"/docs/img/icons/heroku-icon\",\"url\":\"/guides/platform/migrating-to-supabase/heroku\",\"title\":\"Heroku\",\"background\":true,\"showLink\":false}]}]}],[\"$\",\"li\",\"MSSQL\",{\"className\":\"col-span-6 md:col-span-4\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/guides/platform/migrating-to-supabase/mssql\",\"passHref\":true,\"children\":[\"$\",\"$L1c\",null,{\"name\":\"MSSQL\",\"icon\":\"/docs/img/icons/mssql-icon\",\"url\":\"/guides/platform/migrating-to-supabase/mssql\",\"title\":\"MSSQL\",\"background\":true,\"showLink\":false}]}]}],[\"$\",\"li\",\"MySQL\",{\"className\":\"col-span-6 md:col-span-4\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/guides/platform/migrating-to-supabase/mysql\",\"passHref\":true,\"children\":[\"$\",\"$L1c\",null,{\"name\":\"MySQL\",\"icon\":\"/docs/img/icons/mysql-icon\",\"url\":\"/guides/platform/migrating-to-supabase/mysql\",\"title\":\"MySQL\",\"background\":true,\"showLink\":false}]}]}],[\"$\",\"li\",\"Neon\",{\"className\":\"col-span-6 md:col-span-4\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/guides/platform/migrating-to-supabase/neon\",\"passHref\":true,\"children\":[\"$\",\"$L1c\",null,{\"name\":\"Neon\",\"icon\":\"/docs/img/icons/neon-icon\",\"url\":\"/guides/platform/migrating-to-supabase/neon\",\"hasLightIcon\":true,\"title\":\"Neon\",\"background\":true,\"showLink\":false}]}]}],[\"$\",\"li\",\"Postgres\",{\"className\":\"col-span-6 md:col-span-4\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/guides/platform/migrating-to-supabase/postgres\",\"passHref\":true,\"children\":[\"$\",\"$L1c\",null,{\"name\":\"Postgres\",\"icon\":\"/docs/img/icons/postgres-icon\",\"url\":\"/guides/platform/migrating-to-supabase/postgres\",\"title\":\"Postgres\",\"background\":true,\"showLink\":false}]}]}],[\"$\",\"li\",\"Render\",{\"className\":\"col-span-6 md:col-span-4\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/guides/platform/migrating-to-supabase/render\",\"passHref\":true,\"children\":[\"$\",\"$L1c\",null,{\"name\":\"Render\",\"icon\":\"/docs/img/icons/render-icon\",\"url\":\"/guides/platform/migrating-to-supabase/render\",\"title\":\"Render\",\"background\":true,\"showLink\":false}]}]}],[\"$\",\"li\",\"Vercel Postgres\",{\"className\":\"col-span-6 md:col-span-4\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/guides/platform/migrating-to-supabase/vercel-postgres\",\"passHref\":true,\"children\":[\"$\",\"$L1c\",null,{\"name\":\"Vercel Postgres\",\"icon\":\"/docs/img/icons/vercel-icon\",\"url\":\"/guides/platform/migrating-to-supabase/vercel-postgres\",\"hasLightIcon\":true,\"title\":\"Vercel Postgres\",\"background\":true,\"showLink\":false}]}]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"flex flex-col gap-6 py-12 border-b\",\"children\":[[\"$\",\"div\",null,{\"className\":\"col-span-4 flex flex-col gap-1 [\u0026_h2]:m-0 [\u0026_h3]:m-0\",\"children\":[\"$\",\"h3\",null,{\"id\":\"additional-resources\",\"className\":\"group scroll-mt-24\",\"children\":\"Additional resources\"}]}],[\"$\",\"ul\",null,{\"className\":\"grid grid-cols-12 gap-6 not-prose\",\"children\":[[\"$\",\"li\",\"Management API\",{\"className\":\"col-span-12 md:col-span-6 lg:col-span-3\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/reference/api/introduction\",\"className\":\"col-span-12 md:col-span-6 lg:col-span-3\",\"passHref\":true,\"children\":[\"$\",\"$L19\",null,{\"icon\":[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"className\":\"$undefined\",\"width\":18,\"height\":18,\"viewBox\":\"0 0 16 16\",\"fill\":\"none\",\"children\":[[\"$\",\"path\",null,{\"d\":\"M8.00018 1.98145C7.56432 1.98145 7.13983 2.02769 6.73114 2.11537C6.46114 2.1733 6.19531 2.00138 6.13738 1.73138C6.07946 1.46138 6.25138 1.19554 6.52138 1.13762C6.99858 1.03524 7.49338 0.981445 8.00018 0.981445C8.50698 0.981445 9.00178 1.03524 9.47898 1.13762C9.74898 1.19555 9.9209 1.46138 9.86297 1.73138C9.80505 2.00138 9.53921 2.1733 9.26921 2.11537C8.86053 2.02769 8.43603 1.98145 8.00018 1.98145Z\",\"fill\":\"currentColor\"}],[\"$\",\"path\",null,{\"d\":\"M1.73138 6.1372C2.00138 6.19513 2.1733 6.46096 2.11537 6.73096C2.02769 7.13965 1.98145 7.56414 1.98145 8C1.98145 8.43586 2.02769 8.86035 2.11537 9.26904C2.1733 9.53904 2.00138 9.80487 1.73138 9.8628C1.46138 9.92072 1.19555 9.7488 1.13762 9.4788C1.03524 9.0016 0.981445 8.5068 0.981445 8C0.981445 7.4932 1.03524 6.9984 1.13762 6.5212C1.19555 6.2512 1.46138 6.07928 1.73138 6.1372Z\",\"fill\":\"currentColor\"}],[\"$\",\"path\",null,{\"d\":\"M14.2686 6.1372C14.5386 6.07928 14.8045 6.2512 14.8624 6.5212C14.9648 6.9984 15.0186 7.4932 15.0186 8C15.0186 8.5068 14.9648 9.0016 14.8624 9.4788C14.8045 9.7488 14.5386 9.92072 14.2686 9.86279C13.9986 9.80487 13.8267 9.53903 13.8846 9.26904C13.9723 8.86035 14.0186 8.43586 14.0186 8C14.0186 7.56414 13.9723 7.13965 13.8846 6.73096C13.8267 6.46096 13.9986 6.19513 14.2686 6.1372Z\",\"fill\":\"currentColor\"}],[\"$\",\"path\",null,{\"d\":\"M6.13738 14.2686C6.19531 13.9986 6.46114 13.8267 6.73114 13.8846C7.13983 13.9723 7.56432 14.0186 8.00018 14.0186C8.43603 14.0186 8.86053 13.9723 9.26922 13.8846C9.53922 13.8267 9.80505 13.9986 9.86297 14.2686C9.9209 14.5386 9.74898 14.8045 9.47898 14.8624C9.00178 14.9648 8.50698 15.0186 8.00018 15.0186C7.49337 15.0186 6.99858 14.9648 6.52137 14.8624C6.25138 14.8045 6.07946 14.5386 6.13738 14.2686Z\",\"fill\":\"currentColor\"}],[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"clipRule\":\"evenodd\",\"d\":\"$1f\",\"fill\":\"currentColor\"}]]}],\"title\":\"Management API\",\"description\":\"Manage your Supabase projects and organizations.\",\"href\":\"/reference/api/introduction\",\"background\":false,\"children\":\"Manage your Supabase projects and organizations.\"}]}]}],[\"$\",\"li\",\"Supabase CLI\",{\"className\":\"col-span-12 md:col-span-6 lg:col-span-3\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/reference/cli/introduction\",\"className\":\"col-span-12 md:col-span-6 lg:col-span-3\",\"passHref\":true,\"children\":[\"$\",\"$L19\",null,{\"icon\":[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"className\":\"$undefined\",\"width\":18,\"height\":18,\"viewBox\":\"0 0 16 16\",\"fill\":\"none\",\"children\":[\"$\",\"path\",null,{\"d\":\"$20\",\"fill\":\"currentColor\"}]}],\"title\":\"Supabase CLI\",\"description\":\"Use the CLI to develop, manage and deploy your projects.\",\"href\":\"/reference/cli/introduction\",\"background\":false,\"children\":\"Use the CLI to develop, manage and deploy your projects.\"}]}]}],[\"$\",\"li\",\"Platform Guides\",{\"className\":\"col-span-12 md:col-span-6 lg:col-span-3\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/guides/platform\",\"className\":\"col-span-12 md:col-span-6 lg:col-span-3\",\"passHref\":true,\"children\":[\"$\",\"$L19\",null,{\"icon\":[\"$\",\"svg\",null,{\"className\":\"$undefined\",\"viewBox\":\"0 0 16 16\",\"width\":18,\"height\":18,\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"clipRule\":\"evenodd\",\"d\":\"$21\",\"fill\":\"currentColor\"}]}],\"title\":\"Platform Guides\",\"description\":\"Learn more about the tools and services powering Supabase.\",\"href\":\"/guides/platform\",\"background\":false,\"children\":\"Learn more about the tools and services powering Supabase.\"}]}]}],[\"$\",\"li\",\"Integrations\",{\"className\":\"col-span-12 md:col-span-6 lg:col-span-3\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/guides/integrations\",\"className\":\"col-span-12 md:col-span-6 lg:col-span-3\",\"passHref\":true,\"children\":[\"$\",\"$L19\",null,{\"icon\":[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"className\":\"$undefined\",\"width\":18,\"height\":18,\"viewBox\":\"0 0 16 16\",\"fill\":\"none\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"clipRule\":\"evenodd\",\"d\":\"$22\",\"fill\":\"currentColor\"}]}],\"title\":\"Integrations\",\"description\":\"Explore a variety of integrations from Supabase partners.\",\"href\":\"/guides/integrations\",\"background\":false,\"children\":\"Explore a variety of integrations from Supabase partners.\"}]}]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"flex flex-col lg:grid grid-cols-12 gap-6 py-12\",\"children\":[[\"$\",\"div\",null,{\"className\":\"col-span-4 flex flex-col gap-1 [\u0026_h2]:m-0 [\u0026_h3]:m-0\",\"children\":[\"$\",\"div\",null,{\"className\":\"md:max-w-xs 2xl:max-w-none\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-center gap-3 mb-3 text-brand-600\",\"children\":[[\"$\",\"$L23\",null,{\"children\":[\"$\",\"svg\",null,{\"className\":\"$undefined\",\"viewBox\":\"0 0 16 16\",\"width\":18,\"height\":18,\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"clipRule\":\"evenodd\",\"d\":\"$24\",\"fill\":\"currentColor\"}]}]}],[\"$\",\"h3\",null,{\"id\":\"self-hosting\",\"className\":\"group scroll-mt-24\",\"children\":\"Self-Hosting\"}]]}],[\"$\",\"p\",null,{\"className\":\"text-foreground-light text-sm\",\"children\":\"Get started with self-hosting Supabase.\"}],[\"$\",\"$L1e\",null,{\"label\":\"More on Self-Hosting\",\"url\":\"/guides/self-hosting\",\"className\":\"no-underline text-brand text-sm\"}]]}]}],[\"$\",\"div\",null,{\"className\":\"grid col-span-8 grid-cols-12 gap-6 not-prose\",\"children\":[\"$\",\"ul\",null,{\"className\":\"col-span-full lg:col-span-8 grid grid-cols-12 gap-6\",\"children\":[[\"$\",\"li\",\"Auth\",{\"className\":\"col-span-6\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/reference/self-hosting-auth/introduction\",\"passHref\":true,\"children\":[\"$\",\"$L1c\",null,{\"icon\":[\"$\",\"svg\",null,{\"className\":\"$undefined\",\"viewBox\":\"0 0 16 16\",\"width\":18,\"height\":18,\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"children\":[\"$\",\"path\",null,{\"d\":\"M3.49414 9.97461H8.49414M3.49414 9.97461V11.9746H8.49414V9.97461M3.49414 9.97461V7.97461H8.49414V9.97461M10 5V3C10 1.89543 9.10457 1 8 1C6.89543 1 6 1.89543 6 3V5M3.47266 7L3.47266 12C3.47266 13.1046 4.36809 14 5.47266 14H10.4727C11.5772 14 12.4727 13.1046 12.4727 12V7C12.4727 5.89543 11.5772 5 10.4727 5L5.47266 5C4.36809 5 3.47266 5.89543 3.47266 7Z\",\"stroke\":\"currentColor\",\"strokeMiterlimit\":\"10\",\"strokeLinejoin\":\"bevel\"}]}],\"title\":\"Auth\",\"href\":\"/reference/self-hosting-auth/introduction\",\"background\":true,\"showLink\":false}]}]}],[\"$\",\"li\",\"Realtime\",{\"className\":\"col-span-6\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/reference/self-hosting-realtime/introduction\",\"passHref\":true,\"children\":[\"$\",\"$L1c\",null,{\"icon\":[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"className\":\"$undefined\",\"width\":18,\"height\":18,\"viewBox\":\"0 0 16 16\",\"fill\":\"none\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"clipRule\":\"evenodd\",\"d\":\"$25\",\"fill\":\"currentColor\"}]}],\"title\":\"Realtime\",\"href\":\"/reference/self-hosting-realtime/introduction\",\"background\":true,\"showLink\":false}]}]}],[\"$\",\"li\",\"Storage\",{\"className\":\"col-span-6\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/reference/self-hosting-storage/introduction\",\"passHref\":true,\"children\":[\"$\",\"$L1c\",null,{\"icon\":[\"$\",\"svg\",null,{\"className\":\"$undefined\",\"viewBox\":\"0 0 16 16\",\"width\":18,\"height\":18,\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"children\":[\"$\",\"path\",null,{\"d\":\"M12.9997 7.50869V5.60119L9.38151 2.00024H3.99967C3.44739 2.00024 2.99967 2.44796 2.99967 3.00024V5.99976M12.9645 5.58447L9.38004 2L9.38004 4.58447C9.38004 5.13676 9.82776 5.58447 10.38 5.58447L12.9645 5.58447ZM4.44135 5.99976H2.97363C2.42135 5.99976 1.97363 6.44747 1.97363 6.99976V11.9998C1.97363 13.1043 2.86906 13.9998 3.97363 13.9998H11.9736C13.0782 13.9998 13.9736 13.1043 13.9736 11.9998V8.50869C13.9736 7.95641 13.5259 7.50869 12.9736 7.50869H6.79396C6.53157 7.50869 6.27968 7.40556 6.09263 7.22153L5.14268 6.28692C4.95563 6.10289 4.70375 5.99976 4.44135 5.99976Z\",\"stroke\":\"currentColor\",\"strokeMiterlimit\":\"10\",\"strokeLinejoin\":\"bevel\"}]}],\"title\":\"Storage\",\"href\":\"/reference/self-hosting-storage/introduction\",\"background\":true,\"showLink\":false}]}]}],[\"$\",\"li\",\"Analytics\",{\"className\":\"col-span-6\",\"children\":[\"$\",\"$L13\",null,{\"href\":\"/reference/self-hosting-analytics/introduction\",\"passHref\":true,\"children\":[\"$\",\"$L1c\",null,{\"icon\":[\"$\",\"svg\",null,{\"className\":\"$undefined\",\"viewBox\":\"0 0 16 16\",\"width\":18,\"height\":18,\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"clipRule\":\"evenodd\",\"d\":\"$26\",\"fill\":\"currentColor\"}]}],\"title\":\"Analytics\",\"href\":\"/reference/self-hosting-analytics/introduction\",\"background\":true,\"showLink\":false}]}]}]]}]}]]}]]}]}]}]}]]}]}],[\"$\",\"$L27\",null,{\"children\":\"$L28\"}],null,[\"$\",\"$L29\",null,{\"children\":[\"$L2a\",\"$L2b\",[\"$\",\"$L2c\",null,{\"promise\":\"$@2d\"}]]}]]}],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"8QeaEMPUrEzivsp3NJ7w1\",{\"children\":[[\"$\",\"$L2e\",null,{\"children\":\"$L2f\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$30\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"31:\"$Sreact.suspense\"\n32:I[38670,[],\"AsyncMetadata\"]\n28:[\"$\",\"$31\",null,{\"fallback\":null,\"children\":[\"$\",\"$L32\",null,{\"promise\":\"$@33\"}]}]\n"])</script><script>self.__next_f.push([1,"2b:null\n"])</script><script>self.__next_f.push([1,"2f:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"2\",{\"name\":\"theme-color\",\"content\":\"#1E1E1E\"}]]\n2a:null\n"])</script><script>self.__next_f.push([1,"33:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Supabase Docs\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Supabase is the Postgres development platform providing all the backend features you need to build a product.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"application-name\",\"content\":\"Supabase Docs\"}],[\"$\",\"meta\",\"3\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"link\",\"4\",{\"rel\":\"canonical\",\"href\":\"https://supabase.com/docs\"}],[\"$\",\"meta\",\"5\",{\"property\":\"og:title\",\"content\":\"Supabase Docs\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:description\",\"content\":\"Supabase is the Postgres development platform providing all the backend features you need to build a product.\"}],[\"$\",\"meta\",\"7\",{\"property\":\"og:url\",\"content\":\"https://supabase.com/docs\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:image\",\"content\":\"https://supabase.com/docs/img/supabase-og-image.png\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:type\",\"content\":\"article\"}],[\"$\",\"meta\",\"10\",{\"property\":\"article:published_time\",\"content\":\"2025-07-31T19:13:36.294Z\"}],[\"$\",\"meta\",\"11\",{\"property\":\"article:modified_time\",\"content\":\"2025-07-31T19:13:36.295Z\"}],[\"$\",\"meta\",\"12\",{\"property\":\"article:author\",\"content\":\"Supabase\"}],[\"$\",\"meta\",\"13\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"14\",{\"name\":\"twitter:site\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"15\",{\"name\":\"twitter:creator\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:title\",\"content\":\"Supabase Docs\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:description\",\"content\":\"Supabase is the Postgres development platform providing all the backend features you need to build a product.\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:image\",\"content\":\"https://supabase.com/docs/img/supabase-og-image.png\"}],[\"$\",\"link\",\"19\",{\"rel\":\"shortcut icon\",\"href\":\"/docs/favicon/favicon.ico\"}],[\"$\",\"link\",\"20\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon.ico\",\"type\":\"image/x-icon\"}],[\"$\",\"link\",\"21\",{\"rel\":\"apple-touch-icon\",\"href\":\"/docs/favicon/favicon.ico\"}],[\"$\",\"link\",\"22\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-57x57.png\",\"sizes\":\"57x57\"}],[\"$\",\"link\",\"23\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-60x60.png\",\"sizes\":\"60x60\"}],[\"$\",\"link\",\"24\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-72x72.png\",\"sizes\":\"72x72\"}],[\"$\",\"link\",\"25\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-76x76.png\",\"sizes\":\"76x76\"}],[\"$\",\"link\",\"26\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-114x114.png\",\"sizes\":\"114x114\"}],[\"$\",\"link\",\"27\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-120x120.png\",\"sizes\":\"120x120\"}],[\"$\",\"link\",\"28\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-144x144.png\",\"sizes\":\"144x144\"}],[\"$\",\"link\",\"29\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/docs/favicon/apple-icon-152x152.png\",\"sizes\":\"152x152\"}],[\"$\",\"link\",\"30\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-16x16.png\",\"type\":\"image/png\",\"sizes\":\"16x16\"}],[\"$\",\"link\",\"31\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-32x32.png\",\"type\":\"image/png\",\"sizes\":\"32x32\"}],[\"$\",\"link\",\"32\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-48x48.png\",\"type\":\"image/png\",\"sizes\":\"48x48\"}],[\"$\",\"link\",\"33\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-96x96.png\",\"type\":\"image/png\",\"sizes\":\"96x96\"}],[\"$\",\"link\",\"34\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-128x128.png\",\"type\":\"image/png\",\"sizes\":\"128x128\"}],[\"$\",\"link\",\"35\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-180x180.png\",\"type\":\"image/png\",\"sizes\":\"180x180\"}],[\"$\",\"link\",\"36\",{\"rel\":\"icon\",\"href\":\"/docs/favicon/favicon-196x196.png\",\"type\":\"image/png\",\"sizes\":\"196x196\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"2d:{\"metadata\":\"$33:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>