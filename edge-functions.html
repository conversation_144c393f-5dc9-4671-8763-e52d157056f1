<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="rss.xml" data-next-head=""/><link rel="manifest" href="favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:type" content="website" data-next-head=""/><meta property="og:image:alt" content="Supabase Og Image" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase Edge Functions - Deploy JavaScript globally in seconds</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Execute your code closest to your users with fast deploy times and low latency." data-next-head=""/><meta property="og:title" content="Supabase Edge Functions - Deploy JavaScript globally in seconds" data-next-head=""/><meta property="og:description" content="Execute your code closest to your users with fast deploy times and low latency." data-next-head=""/><meta property="og:url" content="https://supabase.com/edge-functions" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/product/functions/functions-og.jpg" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" href="images/product/functions/globe-light.svg" as="image" data-next-head=""/><link rel="preload" href="images/product/functions/globe.svg" as="image" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/992d6cc2d8deee95.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/992d6cc2d8deee95.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/ed4ba316-4b9ff260634dad98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1903.0761eb1a72c97cbd.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1194.49e6cb8481729c6f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2263-07e8ebb96cc2b014.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2076-af870c04819e2b21.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6909.ebb205e01437922e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1904.23fba3214a11a32c.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8085.051ec5c65fdaeb0e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6336.81bff6918b9014d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2622.fb6d8a90715dbb63.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3913.a345a9c25ced5108.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/444.8df5add666091a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3935.a9899f3f43359521.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5534.e6314b63e9058005.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7537.1dbeebc3728233cd.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3393.96abc20224530633.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5656.7403abf65839d007.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1020.b934f2585370614f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6282.5836b5b7b90b32f0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3243.74a2441240622640.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/807.42e42f1cfa3b82dd.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5038.1dfc35ae1f5eb42f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5193.45a3b4407c31f4ed.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2094.332452ca09b0e846.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/edge-functions-449874ddac7c5073.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="_next/supabase-logo-wordmark--light.png 1x, _next/supabase-logo-wordmark--light.png 2x" src="_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="_next/supabase-logo-wordmark--dark.png 1x, _next/supabase-logo-wordmark--dark.png 2x" src="_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav></div><main class="relative min-h-screen"><nav class="relative z-30 hidden md:flex items-center bg-background w-full border-b"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0 flex gap-3 items-center"><a class="flex items-center gap-1.5 px-2 first:-ml-2 py-4 border-b border-transparent text-sm text-foreground-lighter hover:text-foreground focus-visible:ring-2 focus-visible:ring-foreground-lighter focus-visible:text-foreground focus-visible:outline-brand-600" href="database.html"><svg class="h-4 w-4 group-hover/menu-item:text-foreground group-focus-visible/menu-item:text-foreground" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 18"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M3.00829 6.33225H15.1778V11.6648H3.00829V6.33225Z M2 13.0839C2 12.3016 2.63418 11.6674 3.41647 11.6674H14.7483C15.5306 11.6674 16.1648 12.3016 16.1648 13.0839V15.5835C16.1648 16.3658 15.5306 17 14.7483 17H3.41647C2.63418 17 2 16.3658 2 15.5835V13.0839Z M2 2.41647C2 1.63418 2.63418 1 3.41647 1H14.7483C15.5306 1 16.1648 1.63418 16.1648 2.41647V4.9161C16.1648 5.6984 15.5306 6.33257 14.7483 6.33257H3.41647C2.63418 6.33257 2 5.6984 2 4.9161V2.41647Z" stroke="currentColor"></path></svg><p>Database</p></a><a class="flex items-center gap-1.5 px-2 first:-ml-2 py-4 border-b border-transparent text-sm text-foreground-lighter hover:text-foreground focus-visible:ring-2 focus-visible:ring-foreground-lighter focus-visible:text-foreground focus-visible:outline-brand-600" href="auth.html"><svg class="h-4 w-4 group-hover/menu-item:text-foreground group-focus-visible/menu-item:text-foreground" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 18"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M3.02644 12.0457H9.18048M3.02644 12.0457V14.5072H9.18048V12.0457M3.02644 12.0457V9.58414H9.18048V12.0457M11.0339 5.92308V3.46154C11.0339 2.10207 9.93179 1 8.57228 1C7.21277 1 6.11067 2.10207 6.11067 3.46154V5.92308M3 8.38465L3 14.5384C3 15.8979 4.10208 17 5.46157 17H11.6157C12.9752 17 14.0773 15.8979 14.0773 14.5384V8.38465C14.0773 7.02516 12.9752 5.92308 11.6157 5.92308L5.46158 5.92308C4.10209 5.92308 3 7.02516 3 8.38465Z" stroke="currentColor"></path></svg><p>Auth</p></a><a class="flex items-center gap-1.5 px-2 first:-ml-2 py-4 border-b border-transparent text-sm text-foreground-lighter hover:text-foreground focus-visible:ring-2 focus-visible:ring-foreground-lighter focus-visible:text-foreground focus-visible:outline-brand-600" href="storage.html"><svg class="h-4 w-4 group-hover/menu-item:text-foreground group-focus-visible/menu-item:text-foreground" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 18"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M15.7014 8.34507V5.80169L10.8772 1.00033H3.7014C2.96501 1.00033 2.36805 1.59728 2.36805 2.33367V6.33312M15.6545 5.77939L10.8752 1L10.8752 4.44605C10.8752 5.18243 11.4722 5.77939 12.2086 5.77939L15.6545 5.77939ZM4.29028 6.33312H2.33335C1.59696 6.33312 1 6.93008 1 7.66647V14.3333C1 15.8061 2.19392 17 3.66669 17H14.3333C15.8061 17 17 15.8061 17 14.3333V9.67842C17 8.94203 16.403 8.34507 15.6667 8.34507H7.42712C7.07725 8.34507 6.7414 8.20755 6.492 7.96218L5.2254 6.71601C4.976 6.47063 4.64015 6.33312 4.29028 6.33312Z" stroke="currentColor"></path></svg><p>Storage</p></a><a class="flex items-center gap-1.5 px-2 first:-ml-2 py-4 border-b text-sm hover:text-foreground focus-visible:ring-2 focus-visible:ring-foreground-lighter focus-visible:text-foreground focus-visible:outline-brand-600 border-foreground-light text-foreground" href="edge-functions.html"><svg class="h-4 w-4 group-hover/menu-item:text-foreground group-focus-visible/menu-item:text-foreground" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 18"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M5.31216 16.101C6.41582 16.6754 7.67021 17 9.00043 17C13.419 17 17.0009 13.4183 17.0009 9C17.0009 7.66622 16.6744 6.40868 16.0971 5.30289M12.7352 1.92336C11.6203 1.33382 10.3494 1 9.00043 1C4.58192 1 1 4.58172 1 9C1 10.3615 1.34015 11.6436 1.94012 12.766M1.94012 12.766C1.61762 13.16 1.42413 13.6637 1.42413 14.2126C1.42413 15.475 2.44753 16.4983 3.70997 16.4983C4.9724 16.4983 5.99581 15.475 5.99581 14.2126C5.99581 12.9502 4.9724 11.9269 3.70997 11.9269C2.99646 11.9269 2.35931 12.2538 1.94012 12.766ZM16.6199 3.7793C16.6199 5.04166 15.5965 6.06501 14.3341 6.06501C13.0716 6.06501 12.0482 5.04166 12.0482 3.7793C12.0482 2.51693 13.0716 1.49358 14.3341 1.49358C15.5965 1.49358 16.6199 2.51693 16.6199 3.7793ZM14.1436 9C14.1436 11.8403 11.8409 14.1429 9.00043 14.1429C6.15996 14.1429 3.8573 11.8403 3.8573 9C3.8573 6.15968 6.15996 3.85714 9.00043 3.85714C11.8409 3.85714 14.1436 6.15968 14.1436 9Z" stroke="currentColor"></path></svg><p>Edge Functions</p></a><a class="flex items-center gap-1.5 px-2 first:-ml-2 py-4 border-b border-transparent text-sm text-foreground-lighter hover:text-foreground focus-visible:ring-2 focus-visible:ring-foreground-lighter focus-visible:text-foreground focus-visible:outline-brand-600" href="realtime.html"><svg class="h-4 w-4 group-hover/menu-item:text-foreground group-focus-visible/menu-item:text-foreground" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 18"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M5.94415 1V3.85995M3.80348 3.85995L1.35903 1.34096M3.80348 5.83876H1M11.5534 11.6817L15.3703 10.7317C15.901 10.5996 15.9633 9.87023 15.4626 9.6501L6.36613 5.65053C5.8879 5.44026 5.40048 5.92671 5.60979 6.40536L9.53793 15.3879C9.75628 15.8872 10.4825 15.8291 10.6187 15.3015L11.5534 11.6817Z" stroke="currentColor"></path></svg><p>Realtime</p></a></div></nav><div class="w-full max-w-full relative mx-auto py-16 lg:py-24 border-b bg-alternative overflow-hidden"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0 grid grid-cols-12"><div class="relative z-10 col-span-12 gap-8 lg:col-span-5"><div><div class="mb-4 flex items-center gap-3"><span class="text-brand-600 dark:text-brand font-mono uppercase">Edge Functions</span></div><h1 class="h1 text-3xl md:!text-4xl lg:!text-4xl 2xl:!text-6xl tracking-[-.15px]">Deploy JavaScript<br/> globally in seconds</h1></div><div class="mb-4 md:mb-8"><p class="p lg:text-lg max-w-lg lg:max-w-none">Easily author, deploy and monitor serverless functions distributed globally at the edge, close to your users.</p></div><div class="flex flex-row md:flex-row md:items-center gap-2 mt-2"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="dashboard/org.html"><span class="truncate">Create a free account</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="docs/guides/functions.html"><span class="truncate">Read documentation</span></a></div><div class="ph-footer relative z-10 mt-4 md:mt-8 lg:mt-20 xl:mt-32 col-span-12"><div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-8 xl:gap-32"><div class="flex flex-col"><div class="relative w-full mb-4"><div class="w-12 h-12 p-2 bg-alternative rounded-lg border flex justify-center items-center"><svg class="w-7 h-7" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.8217 2.21899C9.75065 2.21899 8.07174 3.89791 8.07174 5.96896V11.783C8.07174 11.8536 8.08148 11.9218 8.0997 11.9866C8.17354 12.3232 8.47349 12.5752 8.83229 12.5752H10.5494C10.8626 12.5752 11.1749 12.8513 11.1749 13.2656V15.0638C11.1749 15.3165 11.2998 15.54 11.4913 15.6759C11.6177 15.7729 11.7759 15.8306 11.9476 15.8306H17.9997C20.0707 15.8306 21.7496 14.1517 21.7496 12.0806V5.96896C21.7496 3.89791 20.0707 2.21899 17.9997 2.21899H11.8217ZM12.6749 14.3307H17.9997C19.2423 14.3307 20.2497 13.3233 20.2497 12.0806L20.2497 5.96896C20.2497 4.7263 19.2423 3.71893 17.9997 3.71893H11.8217C10.579 3.71893 9.57167 4.7263 9.57167 5.96896V11.0752H10.5494C10.9115 11.0752 11.2478 11.1666 11.5412 11.3263C11.5507 11.3157 11.5605 11.3052 11.5708 11.295L15.5026 7.36084L12.6816 7.36066C12.2674 7.36063 11.9316 7.02482 11.9317 6.61061C11.9317 6.1964 12.2675 5.86063 12.6817 5.86066L16.543 5.86091C17.3714 5.86096 18.0429 6.53248 18.0429 7.36085L18.0431 11.2597C18.0431 11.6739 17.7073 12.0097 17.2931 12.0097C16.8789 12.0097 16.5431 11.674 16.5431 11.2598L16.543 8.44186L12.6317 12.3553C12.597 12.3901 12.5597 12.4207 12.5205 12.4472C12.6204 12.7022 12.6749 12.9791 12.6749 13.2656V14.3307Z" fill="currentColor"></path><path d="M4.53957 11.0752H6.25666C6.67087 11.0752 7.00666 11.411 7.00666 11.8252C7.00666 12.2394 6.67087 12.5752 6.25666 12.5752H4.53957C4.22636 12.5752 3.91406 12.8513 3.91406 13.2656L3.91406 15.0638C3.91406 15.4781 3.57828 15.8138 3.16406 15.8138C2.74985 15.8138 2.41406 15.4781 2.41406 15.0638V13.2656C2.41406 12.0889 3.33344 11.0752 4.53957 11.0752Z" fill="currentColor"></path><path d="M3.16406 17.0111C3.57828 17.0111 3.91406 17.3469 3.91406 17.7611L3.91406 19.5593C3.91406 19.9737 4.22636 20.2498 4.53957 20.2498L6.25666 20.2498C6.67087 20.2498 7.00666 20.5856 7.00666 20.9998C7.00666 21.414 6.67087 21.7498 6.25666 21.7498L4.53957 21.7498C3.33344 21.7498 2.41406 20.7361 2.41406 19.5593V17.7611C2.41406 17.3469 2.74985 17.0111 3.16406 17.0111Z" fill="currentColor"></path><path d="M11.9249 17.0111C12.3391 17.0111 12.6749 17.3469 12.6749 17.7611V19.5593C12.6749 20.7361 11.7555 21.7498 10.5494 21.7498H8.83229C8.41807 21.7498 8.08229 21.414 8.08229 20.9998C8.08229 20.5856 8.41807 20.2498 8.83229 20.2498H10.5494C10.8626 20.2498 11.1749 19.9737 11.1749 19.5593V17.7611C11.1749 17.3469 11.5107 17.0111 11.9249 17.0111Z" fill="currentColor"></path></svg></div></div><div class="flex flex-col gap-2"><h3 class="text-lg text-foreground">Fully managed</h3><p class="text-foreground-lighter">Edge Functions scale automatically, reducing your devops burden</p></div></div><div class="flex flex-col"><div class="relative w-full mb-4"><div class="w-12 h-12 p-2 bg-alternative rounded-lg border flex justify-center items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-globe w-7 h-7 stroke-[1.4px]"><circle cx="12" cy="12" r="10"></circle><path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"></path><path d="M2 12h20"></path></svg></div></div><div class="flex flex-col gap-2"><h3 class="text-lg text-foreground">Global deployments</h3><p class="text-foreground-lighter">Deploy worldwide for maximum resiliency and low latency</p></div></div><div class="flex flex-col"><div class="relative w-full mb-4"><div class="w-12 h-12 p-2 bg-alternative rounded-lg border flex justify-center items-center"><svg class="w-7 h-7" width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.7377 6.5363C14.145 6.61196 14.4138 7.00343 14.3381 7.41068L12.5869 16.8366C12.5112 17.2438 12.1197 17.5126 11.7125 17.437C11.3052 17.3613 11.0364 16.9698 11.1121 16.5626L12.8633 7.13669C12.939 6.72944 13.3305 6.46064 13.7377 6.5363Z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M15.7279 9.10834C16.0208 8.81543 16.4957 8.81539 16.7886 9.10826L19.1499 11.4692C19.2906 11.6099 19.3696 11.8007 19.3696 11.9996C19.3696 12.1985 19.2906 12.3893 19.15 12.5299L16.7886 14.8913C16.4957 15.1842 16.0209 15.1842 15.728 14.8913C15.4351 14.5984 15.4351 14.1235 15.728 13.8306L17.5589 11.9997L15.728 10.169C15.4351 9.87613 15.4351 9.40126 15.7279 9.10834Z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M9.72153 14.8913C9.42866 15.1842 8.95379 15.1842 8.66087 14.8913L6.29954 12.5303C6.15887 12.3897 6.07983 12.1989 6.07983 12C6.07982 11.8011 6.15884 11.6103 6.2995 11.4696L8.66083 9.10831C8.95373 8.81542 9.4286 8.81542 9.72149 9.10831C10.0144 9.4012 10.0144 9.87608 9.72149 10.169L7.89053 11.9999L9.72145 13.8306C10.0144 14.1235 10.0144 14.5983 9.72153 14.8913Z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M2.95312 6C2.95312 3.92893 4.63206 2.25 6.70313 2.25H18.7031C20.7742 2.25 22.4531 3.92893 22.4531 6V18C22.4531 20.0711 20.7742 21.75 18.7031 21.75H6.70313C4.63206 21.75 2.95312 20.0711 2.95312 18V6ZM6.70313 3.75C5.46048 3.75 4.45313 4.75736 4.45313 6V18C4.45313 19.2426 5.46048 20.25 6.70313 20.25H18.7031C19.9458 20.25 20.9531 19.2426 20.9531 18V6C20.9531 4.75736 19.9458 3.75 18.7031 3.75H6.70313Z" fill="currentColor"></path></svg></div></div><div class="flex flex-col gap-2"><h3 class="text-lg text-foreground">Secure and Scalable</h3><p class="text-foreground-lighter">Simply write your code in TypeScript and deploy</p></div></div><div class="flex flex-col"><div class="relative w-full mb-4"><div class="w-12 h-12 p-2 bg-alternative rounded-lg border flex justify-center items-center"><svg class="w-7 h-7" width="100%" height="100%" viewBox="0 0 26 28" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M11.9233 0.444469C12.6102 0.0512999 13.5049 0.049524 14.1912 0.444469C17.6439 2.39542 21.0976 4.3433 24.5497 6.29543C25.199 6.66117 25.6333 7.38378 25.6267 8.13193V19.8696C25.6315 20.6487 25.1543 21.3886 24.4693 21.746C21.028 23.6862 17.5884 25.6288 14.1477 27.569C13.4466 27.9698 12.5339 27.939 11.8547 27.5052C10.823 26.9072 9.78955 26.312 8.75774 25.7145C8.5469 25.5888 8.30922 25.4888 8.16034 25.2844C8.29195 25.107 8.52726 25.0849 8.71847 25.0074C9.14913 24.8705 9.54467 24.6507 9.9403 24.4373C10.0403 24.3689 10.1625 24.3951 10.2584 24.4564C11.1406 24.9622 12.0151 25.4828 12.9003 25.9838C13.0891 26.0928 13.2803 25.9481 13.4417 25.8581C16.8187 23.9495 20.1999 22.048 23.5763 20.1388C23.7014 20.0786 23.7706 19.9458 23.7604 19.8088C23.7628 15.9368 23.761 12.0641 23.7616 8.19212C23.7759 8.03662 23.6859 7.89366 23.5453 7.83003C20.1159 5.89871 16.6883 3.96444 13.2595 2.03263C13.2001 1.99179 13.1297 1.96988 13.0576 1.96977C12.9855 1.96967 12.9151 1.99138 12.8556 2.03204C9.42677 3.96444 5.99974 5.90049 2.57093 7.8317C2.43083 7.89544 2.3368 8.03603 2.35348 8.19212C2.35407 12.0641 2.35348 15.9368 2.35348 19.8094C2.34753 19.8763 2.36222 19.9433 2.39556 20.0016C2.4289 20.0599 2.47929 20.1065 2.53995 20.1353C3.45494 20.6541 4.37112 21.1694 5.2867 21.6864C5.80251 21.9641 6.43582 22.129 7.00412 21.9163C7.50562 21.7365 7.85715 21.2247 7.84758 20.6922C7.85232 16.8428 7.84521 12.9928 7.85113 9.14401C7.8386 8.97313 8.0007 8.83194 8.16685 8.84802C8.60649 8.84506 9.04672 8.8421 9.48636 8.84921C9.66987 8.84506 9.79616 9.02907 9.77347 9.20123C9.77169 13.075 9.7782 16.9489 9.77051 20.8227C9.77169 21.8551 9.34754 22.9784 8.39259 23.4836C7.21614 24.093 5.76206 23.9638 4.59982 23.3794C3.59366 22.8772 2.63348 22.2845 1.64518 21.7465C0.958393 21.391 0.483532 20.6482 0.488366 19.8697V8.13193C0.481164 7.36829 0.932741 6.63256 1.60226 6.27215C5.04301 4.33038 8.48316 2.38713 11.9233 0.444469Z" fill="currentColor"></path><path d="M14.9242 8.57537C16.4248 8.47878 18.0313 8.51814 19.3817 9.25742C20.4272 9.82394 21.0069 11.0129 21.0253 12.1745C20.9961 12.3311 20.8323 12.4176 20.6828 12.4068C20.2474 12.4062 19.8119 12.4127 19.3765 12.4039C19.1918 12.411 19.0844 12.2407 19.0613 12.0774C18.9362 11.5216 18.6331 10.9712 18.11 10.7031C17.3069 10.3011 16.3759 10.3213 15.5002 10.3297C14.861 10.3636 14.1736 10.419 13.6321 10.7949C13.2163 11.0796 13.09 11.6544 13.2384 12.1173C13.3783 12.4497 13.762 12.557 14.076 12.6558C15.8844 13.1288 17.8008 13.0818 19.5748 13.7042C20.3092 13.958 21.0277 14.4513 21.2791 15.2203C21.6079 16.2509 21.4638 17.4828 20.7305 18.3102C20.1359 18.991 19.2698 19.3616 18.406 19.5629C17.2569 19.8191 16.0644 19.8256 14.8974 19.7119C13.8001 19.5868 12.6582 19.2985 11.8111 18.5508C11.0867 17.9218 10.7329 16.9418 10.768 15.996C10.7764 15.8362 10.9354 15.7248 11.0885 15.7379C11.5269 15.7344 11.9654 15.7332 12.4038 15.7385C12.579 15.726 12.7088 15.8774 12.7178 16.0424C12.7987 16.5719 12.9977 17.1277 13.4594 17.4416C14.3505 18.0166 15.4687 17.9772 16.489 17.9933C17.3344 17.9558 18.2833 17.9444 18.9732 17.3857C19.3371 17.067 19.445 16.5339 19.3466 16.0752C19.24 15.6879 18.8349 15.5075 18.487 15.3895C16.7017 14.8247 14.7639 15.0297 12.9959 14.391C12.2781 14.1374 11.584 13.6578 11.3083 12.9204C10.9235 11.8767 11.0998 10.5858 11.9099 9.78635C12.6998 8.99113 13.8401 8.68498 14.9242 8.57537Z" fill="currentColor"></path></svg></div></div><div class="flex flex-col gap-2"><h3 class="text-lg text-foreground">Node.js Support</h3><p class="text-foreground-lighter">Easily migrate existing workloads, with support for Node and more than 1 million NPM modules</p></div></div></div></div></div><div class="image-container relative min-h-[300px] col-span-12 mt-8 lg:col-span-7 lg:mt-0 xl:col-span-6 xl:col-start-7"><div id="functions-hero" class=" absolute inset-0 -left-28 top-4 w-[150%] md:w-[150%] aspect-[978/678] sm:-left-32 sm:-top-2 md:-left-44 lg:-left-10 lg:-top-10 lg:w-[130%] xl:-left-32 xl:w-[130%] "><div class=" opacity-0 animate-fade-in absolute z-20 flex-1 flex items-center justify-center h-auto w-[60%] left-[25%] top-[2%] sm:w-[35%] sm:left-[34%] sm:top-[6%] md:left-[33.5%] md:w-[35%] md:top-[6%] lg:left-[26%] lg:w-[52%] lg:top-[3%] xl:left-[28%] xl:w-[48%] xl:top-[3%] 2xl:left-[32%] 2xl:w-[40%] 2xl:top-[3%] "><button class="w-full px-3 py-2 group hover:border-strong flex gap-1 sm:gap-2 items-center bg-alternative rounded-xl border"><div class="text-foreground-muted text-sm font-mono">$</div><div class="opacity-0 flex-1 text-left animate-fade-in text-foreground text-xs md:text-sm font-mono"></div><div class="text-foreground rounded p-1.5 opacity-0 group-hover:opacity-100 transition-opacity"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3.5 h-3.5"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></div></button></div><svg id="svg1" xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" fill="none" viewBox="0 0 155 284" class="absolute" style="width:15.244%;height:41.24%;left:38.8%;top:31.2%"><path stroke="url(#lg-svg1)" stroke-width="1.396" d="M.797 283.216c14.605-22.693 64.498-78.738 87.739-104.396-22.406-17.823-47.852-46.354-57.983-58.555 36.536-29.153 96.735-65.699 122.267-80.327-6.727-8.041-21.226-27.282-26.518-39.053"></path><defs><linearGradient id="lg-svg1" x1="100%" x2="100%" y1="-20%" y2="0" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="hsl(var(--foreground-default))" stop-opacity="0"></stop><stop offset="0.5" stop-color="hsl(var(--foreground-default))" stop-opacity="0.6"></stop><stop offset="1" stop-color="hsl(var(--foreground-default))" stop-opacity="0"></stop></linearGradient></defs></svg><svg id="svg2" xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" fill="none" viewBox="0 0 272 235" class="absolute" style="width:27.458%;height:34.045%;left:50.8%;top:31.4%"><path stroke="url(#lg-svg2)" stroke-width="1.396" d="M271.749 233.614C215.075 230.474 159.599 210.964 138.945 201.602C144.38 186.681 156.517 152.612 161.587 135.71C126.058 122.39 44.25 76.75 1.25 0.75"></path><defs><linearGradient id="lg-svg2" x1="100%" x2="100%" y1="-20%" y2="0" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="hsl(var(--foreground-default))" stop-opacity="0"></stop><stop offset="0.5" stop-color="hsl(var(--foreground-default))" stop-opacity="0.6"></stop><stop offset="1" stop-color="hsl(var(--foreground-default))" stop-opacity="0"></stop></linearGradient></defs></svg><svg id="svg3" xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" fill="none" viewBox="0 0 261 144" class="absolute" style="width:26.687%;height:20.49%;left:25.1%;top:31.4%"><path stroke="url(#lg-svg3)" stroke-width="1.396" d="M260.5 1.5C157.75 30.75 67.75 89 1.13281 143.202"></path><defs><linearGradient id="lg-svg3" x1="100%" x2="100%" y1="-20%" y2="0" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="hsl(var(--foreground-default))" stop-opacity="0"></stop><stop offset="0.5" stop-color="hsl(var(--foreground-default))" stop-opacity="0.6"></stop><stop offset="1" stop-color="hsl(var(--foreground-default))" stop-opacity="0"></stop></linearGradient></defs></svg><div id="dot1" style="left:50%;top:29.9%" class="absolute origin-center w-[2.5%] h-[3.6%] flex items-center justify-center opacity-0 transition-opacity animate-fade-in delay-75"><span class="absolute inset-0 w-full h-full rounded-full bg-foreground bg-opacity-20"></span><span class="absolute w-4/5 h-4/5 rounded-full bg-foreground bg-opacity-90"></span></div><div id="dot2" style="left:24.3%;top:50.2%" class="absolute origin-center w-[2.5%] h-[3.6%] flex items-center justify-center opacity-0 transition-opacity animate-fade-in delay-75"><span class="absolute inset-0 w-full h-full rounded-full bg-foreground bg-opacity-20"></span><span class="absolute w-4/5 h-4/5 rounded-full bg-foreground bg-opacity-90"></span></div><div id="dot3" style="left:77.8%;top:63.4%" class="absolute origin-center w-[2.5%] h-[3.6%] flex items-center justify-center opacity-0 transition-opacity animate-fade-in delay-75"><span class="absolute inset-0 w-full h-full rounded-full bg-foreground bg-opacity-20"></span><span class="absolute w-4/5 h-4/5 rounded-full bg-foreground bg-opacity-90"></span></div><div class="absolute left-[51.15%] top-[10%] w-px h-[20%] overflow-hidden"><span class="absolute inset-0 w-full bg-gradient-to-t from-current to-transparent h-full delay-1200 animate-slide-in"></span></div><img alt="globe wireframe" width="400" height="400" decoding="async" data-nimg="1" class="w-full h-full dark:hidden block" style="color:transparent" src="images/product/functions/globe-light.svg"/><img alt="globe wireframe" width="400" height="400" decoding="async" data-nimg="1" class="w-full h-full hidden dark:block" style="color:transparent" src="images/product/functions/globe.svg"/></div></div></div></div><div id="quote" class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 flex flex-col items-center text-center gap-8 md:gap-12 !pb-8 md:!pb-12 [&amp;_q]:max-w-2xl"><q class="text-2xl max-w-xs md:text-3xl md:max-w-xl">Supabase gave us the flexibility and scalability needed at every growth stage. It&#x27;s rare to find a tool that works just as well for startups as it does for large-scale operations.</q><a class="hover:opacity-90 transition-opacity" href="customers/resend.html"><div class="flex flex-col items-center gap-1"><figure class="text-foreground-lighter mb-4"><img draggable="false" alt="Zeno Rocha, CEO at Resend" loading="lazy" width="28" height="28" decoding="async" data-nimg="1" class="w-10 h-10 rounded-full overflow-hidden object-cover" style="color:transparent" srcSet="_next/zeno-rocha.png 1x, _next/zeno-rocha.png 2x" src="_next/zeno-rocha.png"/></figure><span class="text-foreground">Zeno Rocha</span><span class="text-foreground-lighter font-mono text-sm">CEO at Resend</span></div></a></div><div class="overflow-hidden"><div id="examples" class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 flex flex-col gap-4 lg:gap-8 !pb-0"><div id="examples" class="container"><div class="w-full flex flex-col lg:flex-row justify-between items-start lg:items-end gap-4"><h3 class="h2 !mb-0">What you can build with Edge Functions</h3><a data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px]" href="docs/guides/functions.html#examples"><span class="truncate">View all examples</span></a></div><div class="mt-8 lg:mt-10"><div class="swiper" style="overflow:visible"><div class="swiper-wrapper"><div class="swiper-slide"></div><div class="swiper-slide"></div><div class="swiper-slide"></div><div class="swiper-slide"></div><div class="swiper-slide"></div><div class="swiper-slide"></div></div></div><div class="container mx-auto mt-3 hidden flex-row justify-between md:flex"><div class="p ml-4 cursor-pointer"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left"><path d="m12 19-7-7 7-7"></path><path d="M19 12H5"></path></svg></div><div class="p mr-4 cursor-pointer"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-right"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg></div></div></div></div></div></div><div id="developer-experience" class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 flex flex-col gap-4"><div><h2 class="h2">Delightful DX from local to production</h2><p class="text-foreground-lighter lg:w-1/2">Edge Functions are developed using<!-- --> <a class="underline hover:text-foreground-light transition-colors" target="_blank" href="https://deno.com/">Deno</a>, an open source JavaScript runtime that ensures maximum power and flexibility. Migrate in and out at any time with no vendor lock-in.</p></div><div class="mt-4 md:mt-8"><div class="h-auto flex flex-col md:grid grid-cols-2 xl:grid-cols-3 gap-5 xl:flex-row local-dx-grid_local-dx-grid__ZJbrT"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center w-full group" style="grid-area:localDX"><div class="z-10 w-full h-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light relative flex flex-col min-h-[300px] xl:min-h-[300px]"><div class="relative flex-[1_1_0] h-full w-full inline-flex items-center"><div class="relative w-full h-full flex-1 flex items-center justify-center"><img alt="" aria-hidden="true" draggable="false" loading="lazy" decoding="async" data-nimg="fill" class="object-cover absolute z-0 inset-0 hidden dark:block" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" src="images/product/functions/grid-gradient-dark.svg"/><img alt="" aria-hidden="true" draggable="false" loading="lazy" decoding="async" data-nimg="fill" class="object-cover absolute z-0 inset-0 dark:hidden block" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" src="images/product/functions/grid-gradient-light.svg"/><button class="p-3 relative z-10 group hover:border-strong flex gap-2 xl:gap-4 items-center bg-alternative-200 rounded-xl border"><div class="text-foreground-muted text-sm font-mono">$</div><div class="text-foreground text-sm font-mono">supabase functions serve</div><div class="text-foreground rounded p-1.5 ml-2 opacity-0 group-hover:opacity-100 transition-opacity"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy w-3.5 h-3.5"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></div></button></div></div><div class="flex flex-col gap-1 p-4 xl:p-6 xl:!pt-0"><h3 class="text-foreground">First-class local dev experience</h3><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm text-foreground-lighter">Write code with hot code reloading, a fast Language server for autocompletion, type checking and linting</p></div></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center w-full group" style="grid-area:parity"><div class="z-10 w-full h-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light relative flex flex-col min-h-[300px] xl:min-h-[300px]"><div class="relative flex-[1_1_0] h-full w-full inline-flex items-center"><div class="relative w-full h-full flex items-center justify-center text-sm"><img alt="" aria-hidden="true" draggable="false" loading="lazy" decoding="async" data-nimg="fill" class="object-cover absolute z-0 inset-0 dark:hidden block" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" src="images/product/functions/lines-gradient-light.svg"/><img alt="" aria-hidden="true" draggable="false" loading="lazy" decoding="async" data-nimg="fill" class="object-cover absolute z-0 inset-0 hidden dark:block" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" src="images/product/functions/lines-gradient-dark.svg"/><div class="relative z-10 p-4 font-mono bg-surface-200 border-2 border-dashed rounded-2xl justify-center items-center gap-1 flex"><div class="py-2 px-4 bg-alternative-200 rounded-lg shadow border flex-col justify-center items-center"><div class="text-foreground uppercase tracking-wide">Dev</div></div><svg width="32" height="7" viewBox="0 0 32 7" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M27 5.74425C27 6.0204 27.3382 6.21566 27.5774 6.07759L31.4226 3.8575C31.6618 3.71943 31.6618 3.32891 31.4226 3.19084L27.5774 0.970752C27.3382 0.832681 27 1.02794 27 1.30409V5.74425ZM5 1.30409C5 1.02794 4.6618 0.832681 4.42265 0.970752L0.57735 3.19084C0.338204 3.32891 0.338204 3.71943 0.57735 3.8575L4.42265 6.07759C4.6618 6.21566 5 6.0204 5 5.74425V1.30409ZM27.5 3.02417H4.5V4.02417H27.5V3.02417Z" fill="currentColor"></path><path d="M5 1.30409C5 1.02794 4.6618 0.832681 4.42265 0.970752L0.57735 3.19084C0.338204 3.32891 0.338204 3.71943 0.57735 3.8575L4.42265 6.07759C4.6618 6.21566 5 6.0204 5 5.74425V1.30409ZM27 5.74425C27 6.0204 27.3382 6.21566 27.5774 6.07759L31.4226 3.8575C31.6618 3.71943 31.6618 3.32891 31.4226 3.19084L27.5774 0.970752C27.3382 0.832681 27 1.02794 27 1.30409V5.74425ZM4.5 4.02417H27.5V3.02417H4.5V4.02417Z" fill="currentColor"></path></svg><div class="py-2 px-4 bg-alternative-200 rounded-lg shadow border flex-col justify-center items-center"><div class="text-foreground uppercase tracking-wide">Prod</div></div></div></div></div><div class="flex flex-col gap-1 p-4 xl:p-6 xl:!pt-0"><h3 class="text-foreground">Dev and Prod parity</h3><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm text-foreground-lighter">The open source<!-- --> <a class="underline hover:text-foreground-light transition-colors" href="https://github.com/supabase/edge-runtime/">Edge runtime</a> <!-- -->runs your functions locally during development and the same runtime powers functions in production</p></div></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center w-full group" style="grid-area:ecosystem"><div class="z-10 w-full h-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light relative flex flex-col min-h-[300px] xl:min-h-[300px]"><div class="relative flex-[1_1_0] h-full w-full inline-flex items-center"><div class="relative w-full h-full flex justify-center overflow-hidden group"><div class="absolute flex flex-wrap items-start gap-2 opacity-80 dark:opacity-50" style="left:-50px;right:-150px"><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">fetch</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">crypto</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">ESLint</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">atob</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">moment</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">express</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">fastify</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">socket.io</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">async</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">lodash</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">underscore</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">ramda</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">validator</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">yup</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">day.js</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">date-fns</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">jsonwebtoken</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">bcrypt</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">uuid</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">fs-extra</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">rimraf</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">mkdirp</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">glob</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">shelljs</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">@supabase/supabase-js</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">js-yaml</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">typescript</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">jest</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">winston</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">debug</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">eslint</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">nodemon</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">dotenv</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">@octokit/rest</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">cross-env</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">commander</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">yargs</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">minimist</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">chalk</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">colors</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">ora</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">@aws-sdk</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">axios</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">passport</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">nodemailer</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">@supabase/auth-helpers-react</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">mongoose</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">openai</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">jwt</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">react</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">mocha</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">autoprefixer</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">@supabase/auth-ui-react</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">gray-matter</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">request</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">prop-types</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">react-dom</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">bluebird</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">vue</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">tslib</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">inquirer</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">webpack</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">classnames</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">body-parser</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">rxjs</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">babel-runtime</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">jquery</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">fetch</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">crypto</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">eslint</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">atob</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">moment</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">express</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">fastify</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">socket.io</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">async</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">lodash</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">underscore</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">ramda</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">validator</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">yup</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">day.js</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">date-fns</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">jsonwebtoken</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">bcrypt</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">minimist</span><span class="py-1 px-2 rounded-md bg-surface-75 border text-foreground-muted">chalk</span></div><div class="absolute inset-0" style="background:radial-gradient(70% 50% at 50% 50%, transparent, hsl(var(--background-surface-75))"></div><div class="absolute z-10 inset-0 flex flex-col items-center justify-center font-bold text-7xl uppercase text-foreground-light group-hover:text-foreground transition-colors"><svg width="111" height="42" viewBox="0 0 111 42" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M36.1763 37.4452H49.2591V30.8616H62.342V4.52698H36.1763V37.4452ZM49.2591 11.1106H55.8006V24.2779H49.2591V11.1106ZM67.5752 4.52698V30.8616H80.6581V11.1106H87.1995V30.8616H93.7409V11.1106H100.282V30.8616H106.824V4.52698H67.5752ZM4.77734 30.8616H17.8602V11.1106H24.4017V30.8616H30.9431V4.52698H4.77734V30.8616Z"></path></svg></div></div></div><div class="flex flex-col gap-1 p-4 xl:p-6 xl:!pt-0"><h3 class="text-foreground">Use any NPM module</h3><div class="flex-1 flex flex-col justify-between gap-2"><p class="text-sm text-foreground-lighter">Tap into the 2+ million modules in the Deno and NPM ecosystem</p></div></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center w-full group" style="grid-area:ci"><div class="z-10 w-full h-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light relative flex flex-col min-h-[300px] xl:min-h-[300px] xl:flex-row xl:items-end"><div class="relative w-full inline-flex items-center xl:order-last flex-1 h-full"><div class="w-full h-full relative pl-4 xl:-mb-0 pt-4 sm:pt-4 border-b xl:border-none overflow-hidden"><div class="relative rounded-2xl shadow-lg p-2 pt-0 w-full bg-alternative-200 border flex flex-col rounded-r-none rounded-b-none border-r-0 border-b-0 h-full !text-[13px] xl:!text-sm leading-4 [&amp;_.synthax-highlighter]:rounded-b-none [&amp;_.synthax-highlighter]:rounded-r-none [&amp;_.synthax-highlighter]:border-r-0 [&amp;_.synthax-highlighter]:border-b-0 [&amp;_.synthax-highlighter]:!pb-8 [&amp;_.synthax-highlighter]:xl:min-h-[240px] pr-0 pb-0 -bottom-2 xl:-bottom-4"><div class="w-full px-2 py-3 relative flex items-center gap-1.5 lg:gap-2"><div class="w-2 h-2 bg-border rounded-full"></div><div class="w-2 h-2 bg-border rounded-full"></div><div class="w-2 h-2 bg-border rounded-full"></div></div><div class="h-full w-full rounded-lg"></div></div></div></div><div class="flex flex-col gap-1 p-4 xl:p-6 xl:!pt-0 xl:w-2/5"><h3 class="text-foreground">Continuous Integration</h3><div class="flex flex-col justify-between gap-2 flex-auto"><p class="text-sm text-foreground-lighter">Use the<!-- --> <a class="underline hover:text-foreground-light transition-colors" href="https://supabase.com/docs/guides/functions/cicd-workflow">Supabase CLI with Github actions</a> <!-- -->to preview and deploy your functions along with the rest of your application</p></div></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></div></div></div><div id="global-presence" class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 flex flex-col gap-4 lg:gap-8"><div class="w-full flex flex-col xl:grid grid-cols-3 gap-8 md:gap-16"><p class="text-xl sm:text-2xl max-w-lg text-foreground-lighter">Edge Functions run<!-- --> <span class="text-foreground">server-side logic geographically close to users</span>, offering low latency and great performance.</p><div class="flex flex-col col-span-2 gap-4 md:gap-10 md:grid grid-cols-3 items-start"><div class="w-full flex flex-col gap-1 md:gap-2 pt-3 md:pt-0 border-t md:border-t-0 md:pl-3 md:border-l text-sm mb-1"><p class="font-mono uppercase tracking-wide text-foreground">Global presence</p><p class="text-foreground-lighter p-0 m-0">Edge functions run globally or can be<!-- --> <a class="underline hover:text-foreground-light transition-colors" href="docs/guides/functions/regional-invocation.html">pinned to your database&#x27;s proximity</a></p></div><div class="w-full flex flex-col gap-1 md:gap-2 pt-3 md:pt-0 border-t md:border-t-0 md:pl-3 md:border-l text-sm mb-1"><p class="font-mono uppercase tracking-wide text-foreground">Automatic scaling</p><p class="text-foreground-lighter p-0 m-0">Seamlessly scale with usage without any manual tuning</p></div><div class="w-full flex flex-col gap-1 md:gap-2 pt-3 md:pt-0 border-t md:border-t-0 md:pl-3 md:border-l text-sm mb-1"><p class="font-mono uppercase tracking-wide text-foreground">Secure</p><p class="text-foreground-lighter p-0 m-0">Scale with confidence with SSL, Firewall and DDOS protection built in</p></div></div></div></div><div class="overflow-hidden"><div id="observability" class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 flex flex-col gap-4 lg:gap-8"><h2 class="h2">Built-in observability</h2><div class="flex flex-col gap-8 xl:gap-32 justify-between"><div class="hidden md:flex gap-4" role="tablist"><button class="relative hover:text-foreground w-full h-[500px] text-left text-lg flex flex-col group transition-all text-foreground md:flex-[2]" aria-selected="true" role="tab"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center w-full h-full mb-4"><div class="relative z-10 w-full h-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light p-4"><div class="relative z-10 flex flex-col h-full justify-between"><h3 class="font-mono text-sm uppercase transition-colors text-foreground">Realtime logs</h3><p class="pt-2 text-foreground-lighter text-sm max-w-[220px] md:opacity-0 transition-opacity lg:opacity-100 !opacity-100">Stream logs to the dashboard in realtime. Logs are populated with rich metadata to help debugging</p></div><div class="absolute h-[calc(100%-150px)] inset-0 m-auto flex items-center justify-center transition-opacity opacity-100"><div class="absolute z-20 w-full h-full inset-0 bg-gradient-to-r from-transparent to-background-surface-100 transition-opacity pointer-events-none opacity-0"></div></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div><div class="opacity-0 animate-fade-in absolute bottom-0 w-full h-[1px] bg-border-strong group-hover:opacity-100 rounded-full overflow-hidden"><div class="absolute inset-0 w-full right-full bg-foreground h-full transition-opacity opacity-100" style="transform:translateX(-100%)"></div></div></button><button class="relative hover:text-foreground w-full h-[500px] text-left text-lg flex flex-col group transition-all text-foreground-light md:flex-[1]" aria-selected="false" role="tab"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center w-full h-full mb-4"><div class="relative z-10 w-full h-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light p-4"><div class="relative z-10 flex flex-col h-full justify-between"><h3 class="font-mono text-sm uppercase transition-colors">Query Logs via Log explorer</h3><p class="pt-2 text-foreground-lighter text-sm max-w-[220px] md:opacity-0 transition-opacity lg:opacity-100">Get deeper insights into how your functions are behaving by writing SQL queries on function logs</p></div><div class="absolute h-[calc(100%-150px)] inset-0 m-auto flex items-center justify-center transition-opacity opacity-25"><div class="absolute z-20 w-full h-full inset-0 bg-gradient-to-r from-transparent to-background-surface-100 transition-opacity pointer-events-none"></div></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></button><button class="relative hover:text-foreground w-full h-[500px] text-left text-lg flex flex-col group transition-all text-foreground-light md:flex-[1]" aria-selected="false" role="tab"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center w-full h-full mb-4"><div class="relative z-10 w-full h-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light p-4"><div class="relative z-10 flex flex-col h-full justify-between"><h3 class="font-mono text-sm uppercase transition-colors">Metrics</h3><p class="pt-2 text-foreground-lighter text-sm max-w-[220px] md:opacity-0 transition-opacity lg:opacity-100">Dashboards show the health of your functions at all times</p></div><div class="absolute h-[calc(100%-150px)] inset-0 m-auto flex items-center justify-center transition-opacity opacity-25"><div class="absolute z-20 w-full h-full inset-0 bg-gradient-to-r from-transparent to-background-surface-100 transition-opacity pointer-events-none"></div></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></button></div><div class="md:hidden relative w-full lg:w-1/2 min-h-[300px] overflow-visible"><div class="swiper" style="z-index:0;overflow:visible"><div class="swiper-wrapper"><div class="swiper-slide"><button class="relative hover:text-foreground w-full h-[500px] text-left text-lg flex flex-col group transition-all text-foreground md:flex-[2]" aria-selected="true" role="tab"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center w-full h-full mb-4"><div class="relative z-10 w-full h-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light p-4"><div class="relative z-10 flex flex-col h-full justify-between"><h3 class="font-mono text-sm uppercase transition-colors text-foreground">Realtime logs</h3><p class="pt-2 text-foreground-lighter text-sm max-w-[220px] md:opacity-0 transition-opacity lg:opacity-100 !opacity-100">Stream logs to the dashboard in realtime. Logs are populated with rich metadata to help debugging</p></div><div class="absolute h-[calc(100%-150px)] inset-0 m-auto flex items-center justify-center transition-opacity opacity-100"><div class="absolute z-20 w-full h-full inset-0 bg-gradient-to-r from-transparent to-background-surface-100 transition-opacity pointer-events-none opacity-0"></div></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div><div class="opacity-0 animate-fade-in absolute bottom-0 w-full h-[1px] bg-border-strong group-hover:opacity-100 rounded-full overflow-hidden"><div class="absolute inset-0 w-full right-full bg-foreground h-full transition-opacity opacity-100" style="transform:translateX(-100%)"></div></div></button></div><div class="swiper-slide"><button class="relative hover:text-foreground w-full h-[500px] text-left text-lg flex flex-col group transition-all text-foreground-light md:flex-[1]" aria-selected="false" role="tab"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center w-full h-full mb-4"><div class="relative z-10 w-full h-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light p-4"><div class="relative z-10 flex flex-col h-full justify-between"><h3 class="font-mono text-sm uppercase transition-colors">Query Logs via Log explorer</h3><p class="pt-2 text-foreground-lighter text-sm max-w-[220px] md:opacity-0 transition-opacity lg:opacity-100">Get deeper insights into how your functions are behaving by writing SQL queries on function logs</p></div><div class="absolute h-[calc(100%-150px)] inset-0 m-auto flex items-center justify-center transition-opacity opacity-25"><div class="absolute z-20 w-full h-full inset-0 bg-gradient-to-r from-transparent to-background-surface-100 transition-opacity pointer-events-none"></div></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></button></div><div class="swiper-slide"><button class="relative hover:text-foreground w-full h-[500px] text-left text-lg flex flex-col group transition-all text-foreground-light md:flex-[1]" aria-selected="false" role="tab"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center w-full h-full mb-4"><div class="relative z-10 w-full h-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light p-4"><div class="relative z-10 flex flex-col h-full justify-between"><h3 class="font-mono text-sm uppercase transition-colors">Metrics</h3><p class="pt-2 text-foreground-lighter text-sm max-w-[220px] md:opacity-0 transition-opacity lg:opacity-100">Dashboards show the health of your functions at all times</p></div><div class="absolute h-[calc(100%-150px)] inset-0 m-auto flex items-center justify-center transition-opacity opacity-25"><div class="absolute z-20 w-full h-full inset-0 bg-gradient-to-r from-transparent to-background-surface-100 transition-opacity pointer-events-none"></div></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></button></div></div></div></div></div></div></div><div class="overflow-hidden"><div id="integrates-with-supabase" class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 flex flex-col gap-4 lg:gap-8"><h2 class="h2">Integrates seamlessly with the Supabase ecosystem</h2><div class="flex flex-col lg:flex-row gap-8 xl:gap-24 justify-between"><div class="lg:w-1/3 gap-1 flex flex-col items-start" role="tablist"><button class="hover:text-foreground w-full text-left text-lg flex flex-col group transition-all text-foreground" aria-selected="true" role="tab"><div class="flex flex-col py-4"><h3 class="font-mono text-sm uppercase">Zero configuration</h3><div class="text-foreground-lighter text-sm inline-block overflow-hidden transition-all opacity-100" style="height:autopx"><p class="pt-2">Pre-populated environment variables required to access your supabase project</p></div></div><div class="relative w-full h-[1px] bg-border-strong opacity-80 group-hover:opacity-100 rounded-full overflow-hidden"><div class="absolute inset-0 w-full right-full bg-foreground h-full transition-opacity opacity-100" style="transform:translateX(-100%)"></div></div></button><button class="hover:text-foreground w-full text-left text-lg flex flex-col group transition-all text-foreground-light" aria-selected="false" role="tab"><div class="flex flex-col py-4"><h3 class="font-mono text-sm uppercase">Connect to your database</h3><div class="text-foreground-lighter text-sm inline-block overflow-hidden transition-all opacity-0" style="height:0"><p class="pt-2">Connect to your Postgres database from an Edge Function by using the supabase-js client</p></div></div><div class="relative w-full h-[1px] bg-border-strong opacity-80 group-hover:opacity-100 rounded-full overflow-hidden"></div></button><button class="hover:text-foreground w-full text-left text-lg flex flex-col group transition-all text-foreground-light" aria-selected="false" role="tab"><div class="flex flex-col py-4"><h3 class="font-mono text-sm uppercase">Trigger via webhook</h3><div class="text-foreground-lighter text-sm inline-block overflow-hidden transition-all opacity-0" style="height:0"><p class="pt-2">Database Webhooks allow you to send real-time data from your database to another system whenever a table event occurs</p></div></div><div class="relative w-full h-[1px] bg-border-strong opacity-80 group-hover:opacity-100 rounded-full overflow-hidden"></div></button><button class="hover:text-foreground w-full text-left text-lg flex flex-col group transition-all text-foreground-light" aria-selected="false" role="tab"><div class="flex flex-col py-4"><h3 class="font-mono text-sm uppercase">Works with Supabase Auth</h3><div class="text-foreground-lighter text-sm inline-block overflow-hidden transition-all opacity-0" style="height:0"><p class="pt-2">Edge Functions are designed to work seamlessly with<!-- --> <a class="underline hover:text-foreground-light transition-colors" href="docs/guides/functions/auth.html">Supabase Auth</a></p></div></div><div class="relative w-full h-[1px] bg-border-strong opacity-80 group-hover:opacity-100 rounded-full overflow-hidden"></div></button><button class="hover:text-foreground w-full text-left text-lg flex flex-col group transition-all text-foreground-light" aria-selected="false" role="tab"><div class="flex flex-col py-4"><h3 class="font-mono text-sm uppercase">Works with Supabase Storage</h3><div class="text-foreground-lighter text-sm inline-block overflow-hidden transition-all opacity-0" style="height:0"><p class="pt-2">Edge Functions are designed to work seamlessly with<!-- --> <a class="underline hover:text-foreground-light transition-colors" href="docs/guides/functions/storage-caching.html">Supabase Storage</a></p></div></div><div class="relative w-full h-[1px] bg-border-strong opacity-80 group-hover:opacity-100 rounded-full overflow-hidden"></div></button></div><div class="relative w-full lg:flex-1 min-h-[400px] md:overflow-hidden -mt-4"><div class="swiper" style="z-index:0;margin-right:1px;overflow:visible"><div class="swiper-wrapper"><div class="swiper-slide md:p-4"><div class="relative rounded-2xl shadow-lg p-2 pt-0 w-full h-full bg-alternative-200 border flex flex-col [&amp;_.synthax-highlighter]:md:!min-h-[300px]"><div class="w-full px-2 py-3 relative flex items-center gap-1.5 lg:gap-2"><div class="w-2 h-2 bg-border rounded-full"></div><div class="w-2 h-2 bg-border rounded-full"></div><div class="w-2 h-2 bg-border rounded-full"></div></div><div class="h-full w-full rounded-lg"></div></div></div><div class="swiper-slide md:p-4"><div class="relative rounded-2xl shadow-lg p-2 pt-0 w-full h-full bg-alternative-200 border flex flex-col [&amp;_.synthax-highlighter]:!min-h-[200px]"><div class="w-full px-2 py-3 relative flex items-center gap-1.5 lg:gap-2"><div class="w-2 h-2 bg-border rounded-full"></div><div class="w-2 h-2 bg-border rounded-full"></div><div class="w-2 h-2 bg-border rounded-full"></div></div><div class="h-full w-full rounded-lg"></div></div></div><div class="swiper-slide md:p-4"><div class="relative rounded-2xl shadow-lg p-2 pt-0 w-full h-full bg-alternative-200 border flex flex-col [&amp;_.synthax-highlighter]:md:!min-h-[300px]"><div class="w-full px-2 py-3 relative flex items-center gap-1.5 lg:gap-2"><div class="w-2 h-2 bg-border rounded-full"></div><div class="w-2 h-2 bg-border rounded-full"></div><div class="w-2 h-2 bg-border rounded-full"></div></div><div class="h-full w-full rounded-lg"></div></div></div><div class="swiper-slide md:p-4"><div class="relative rounded-2xl shadow-lg p-2 pt-0 w-full h-full bg-alternative-200 border flex flex-col"><div class="w-full px-2 py-3 relative flex items-center gap-1.5 lg:gap-2"><div class="w-2 h-2 bg-border rounded-full"></div><div class="w-2 h-2 bg-border rounded-full"></div><div class="w-2 h-2 bg-border rounded-full"></div></div><div class="h-full w-full rounded-lg"></div></div></div><div class="swiper-slide md:p-4"><div class="relative rounded-2xl shadow-lg p-2 pt-0 w-full h-full bg-alternative-200 border flex flex-col"><div class="w-full px-2 py-3 relative flex items-center gap-1.5 lg:gap-2"><div class="w-2 h-2 bg-border rounded-full"></div><div class="w-2 h-2 bg-border rounded-full"></div><div class="w-2 h-2 bg-border rounded-full"></div></div><div class="h-full w-full rounded-lg"></div></div></div></div></div></div></div></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 overflow-hidden flex flex-col xl:grid xl:grid-cols-2 gap-4 md:gap-8 xl:gap-10 !pt-0 lg:!pt-16"><div class="w-full pb-6 md:h-[120px] flex items-center justify-center text-center col-span-1"><div class="relative mx-auto w-full max-w-md grid grid-cols-3 md:flex items-center justify-center gap-y-8 md:gap-2 px-4"><div class="relative mx-auto md:w-[150px] bg-transparent group z-0" style="transform:translateX(225px)"><a class="flex w-full flex-col items-center text-center group" href="database.html"><div class="relative w-[30px] !min-w-[30px] md:!min-w-[50px] aspect-square will-change-transform bg-background rounded-xl border p-3 text-foreground-lighter group-hover:text-foreground" style="will-change:width;width:65px"><svg width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" fill-rule="evenodd" clip-rule="evenodd"><path d="M5.18625 8.66531H19.5035V15.331H5.18625V8.66531Z M4 17.0007C4 16.0804 4.7461 15.3343 5.66645 15.3343H18.9984C19.9187 15.3343 20.6648 16.0804 20.6648 17.0007V20.3335C20.6648 21.2539 19.9187 22 18.9984 22H5.66646C4.7461 22 4 21.2539 4 20.3335V17.0007Z M4 3.66646C4 2.7461 4.7461 2 5.66645 2H18.9984C19.9187 2 20.6648 2.7461 20.6648 3.66645V6.99926C20.6648 7.91962 19.9187 8.66572 18.9984 8.66572H5.66646C4.7461 8.66572 4 7.91962 4 6.99926V3.66646Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></div><div class="text-foreground flex justify-center relative opacity-70 md:absolute md:bottom-0 md:opacity-0 group-hover:opacity-100 transition-opacity md:translate-y-8 md:-left-20 md:md:-right-20 font-mono uppercase text-center text-xs mt-2">Database</div></a></div><div class="relative mx-auto md:w-[150px] bg-transparent group z-0" style="transform:translateX(134px)"><a class="flex w-full flex-col items-center text-center group" href="auth.html"><div class="relative w-[30px] !min-w-[30px] md:!min-w-[50px] aspect-square will-change-transform bg-background rounded-xl border p-3 text-foreground-lighter group-hover:text-foreground" style="will-change:width;width:65px"><svg width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" fill-rule="evenodd" clip-rule="evenodd"><path d="M5.03305 15.8071H12.7252M5.03305 15.8071V18.884H12.7252V15.8071M5.03305 15.8071V12.7302H12.7252V15.8071M15.0419 8.15385V5.07692C15.0419 3.37759 13.6643 2 11.965 2C10.2657 2 8.88814 3.37759 8.88814 5.07692V8.15385M5 11.2307L5 18.9231C5 20.6224 6.37757 22 8.07689 22H15.769C17.4683 22 18.8459 20.6224 18.8459 18.9231V11.2307C18.8459 9.53142 17.4683 8.15385 15.769 8.15385L8.07689 8.15385C6.37757 8.15385 5 9.53142 5 11.2307Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></div><div class="text-foreground flex justify-center relative opacity-70 md:absolute md:bottom-0 md:opacity-0 group-hover:opacity-100 transition-opacity md:translate-y-8 md:-left-20 md:md:-right-20 font-mono uppercase text-center text-xs mt-2">Authentication</div></a></div><div class="relative mx-auto md:w-[150px] bg-transparent group z-0" style="transform:translateX(43px)"><a class="flex w-full flex-col items-center text-center group" href="storage.html"><div class="relative w-[30px] !min-w-[30px] md:!min-w-[50px] aspect-square will-change-transform bg-background rounded-xl border p-3 text-foreground-lighter group-hover:text-foreground" style="will-change:width;width:65px"><svg width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" fill-rule="evenodd" clip-rule="evenodd"><path d="M20.4997 12.1386V9.15811L14.8463 3.53163H6.43717C5.57423 3.53163 4.87467 4.23119 4.87467 5.09413V9.78087M20.4447 9.13199L14.844 3.53125L14.844 7.56949C14.844 8.43243 15.5436 9.13199 16.4065 9.13199L20.4447 9.13199ZM7.12729 9.78087H4.83398C3.97104 9.78087 3.27148 10.4804 3.27148 11.3434V19.1559C3.27148 20.8818 4.67059 22.2809 6.39648 22.2809H18.8965C20.6224 22.2809 22.0215 20.8818 22.0215 19.1559V13.7011C22.0215 12.8381 21.3219 12.1386 20.459 12.1386H10.8032C10.3933 12.1386 9.99969 11.9774 9.70743 11.6899L8.22312 10.2296C7.93086 9.94202 7.53729 9.78087 7.12729 9.78087Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></div><div class="text-foreground flex justify-center relative opacity-70 md:absolute md:bottom-0 md:opacity-0 group-hover:opacity-100 transition-opacity md:translate-y-8 md:-left-20 md:md:-right-20 font-mono uppercase text-center text-xs mt-2">Storage</div></a></div><div class="relative mx-auto md:w-[150px] bg-transparent group z-10" style="transform:translateX(-48px)"><a class="flex w-full flex-col items-center text-center group" href="edge-functions.html"><div class="relative w-[30px] !min-w-[30px] md:!min-w-[50px] aspect-square will-change-transform bg-background rounded-xl border p-3 text-foreground-lighter group-hover:text-foreground" style="will-change:width;width:65px"><svg width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" fill-rule="evenodd" clip-rule="evenodd"><path d="M6.6594 21.8201C8.10788 22.5739 9.75418 23 11.5 23C17.299 23 22 18.299 22 12.5C22 10.7494 21.5716 9.09889 20.8139 7.64754M16.4016 3.21191C14.9384 2.43814 13.2704 2 11.5 2C5.70101 2 1 6.70101 1 12.5C1 14.287 1.44643 15.9698 2.23384 17.4428M2.23384 17.4428C1.81058 17.96 1.55664 18.6211 1.55664 19.3416C1.55664 20.9984 2.89979 22.3416 4.55664 22.3416C6.21349 22.3416 7.55664 20.9984 7.55664 19.3416C7.55664 17.6847 6.21349 16.3416 4.55664 16.3416C3.62021 16.3416 2.78399 16.7706 2.23384 17.4428ZM21.5 5.64783C21.5 7.30468 20.1569 8.64783 18.5 8.64783C16.8432 8.64783 15.5 7.30468 15.5 5.64783C15.5 3.99097 16.8432 2.64783 18.5 2.64783C20.1569 2.64783 21.5 3.99097 21.5 5.64783ZM18.25 12.5C18.25 16.2279 15.2279 19.25 11.5 19.25C7.77208 19.25 4.75 16.2279 4.75 12.5C4.75 8.77208 7.77208 5.75 11.5 5.75C15.2279 5.75 18.25 8.77208 18.25 12.5Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></div><div class="text-foreground flex justify-center relative opacity-70 md:absolute md:bottom-0 md:opacity-0 group-hover:opacity-100 transition-opacity md:translate-y-8 md:-left-20 md:md:-right-20 font-mono uppercase text-center text-xs mt-2">Edge Functions</div></a></div><div class="relative mx-auto md:w-[150px] bg-transparent group z-0" style="transform:translateX(-139px)"><a class="flex w-full flex-col items-center text-center group" href="realtime.html"><div class="relative w-[30px] !min-w-[30px] md:!min-w-[50px] aspect-square will-change-transform bg-background rounded-xl border p-3 text-foreground-lighter group-hover:text-foreground" style="will-change:width;width:65px"><svg width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" fill-rule="evenodd" clip-rule="evenodd"><path d="M9.15928 1.94531V5.84117M6.24345 5.84117L2.91385 2.40977M6.24345 8.53673H2.4248M16.7998 16.496L21.9988 15.2019C22.7217 15.022 22.8065 14.0285 22.1246 13.7286L9.73411 8.28034C9.08269 7.99391 8.41873 8.65652 8.70383 9.30851L14.0544 21.5445C14.3518 22.2247 15.341 22.1456 15.5266 21.4269L16.7998 16.496Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></div><div class="text-foreground flex justify-center relative opacity-70 md:absolute md:bottom-0 md:opacity-0 group-hover:opacity-100 transition-opacity md:translate-y-8 md:-left-20 md:md:-right-20 font-mono uppercase text-center text-xs mt-2">Realtime</div></a></div><div class="relative mx-auto md:w-[150px] bg-transparent group z-0" style="transform:translateX(-230px)"><a class="flex w-full flex-col items-center text-center group" href="https://supabase.com/vector"><div class="relative w-[30px] !min-w-[30px] md:!min-w-[50px] aspect-square will-change-transform bg-background rounded-xl border p-3 text-foreground-lighter group-hover:text-foreground" style="will-change:width;width:65px"><svg width="100%" height="100%" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" fill-rule="evenodd" clip-rule="evenodd"><path d="M11.9983 11.4482V21.7337M11.9983 11.4482L21.0732 6.17699M11.9983 11.4482L2.92383 6.17723M2.92383 6.17723V12.4849M2.92383 6.17723V6.1232L8.35978 2.9657M21.0736 12.54V6.1232L15.6376 2.9657M17.7247 18.6107L11.9987 21.9367L6.27265 18.6107" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path></svg></div><div class="text-foreground flex justify-center relative opacity-70 md:absolute md:bottom-0 md:opacity-0 group-hover:opacity-100 transition-opacity md:translate-y-8 md:-left-20 md:md:-right-20 font-mono uppercase text-center text-xs mt-2">Vector</div></a></div></div></div><div class="flex flex-col col-span-1 text-center xl:text-left xl:justify-center items-center xl:items-start"><h2 class="h2">Ready to start building?</h2><div class="flex gap-2 py-2"><a data-size="small" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm leading-4 px-3 py-2 h-full" href="dashboard/org.html"><span class="truncate">Start for free</span></a><a data-size="small" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm leading-4 px-3 py-2 h-[34px]" href="https://forms.supabase.com/enterprise"><span class="truncate">Contact Enterprise</span></a></div></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="_next/supabase-logo-wordmark--light.png 1x, _next/supabase-logo-wordmark--light.png 2x" src="_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="_next/supabase-logo-wordmark--dark.png 1x, _next/supabase-logo-wordmark--dark.png 2x" src="_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{}},"page":"/edge-functions","query":{},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","nextExport":true,"autoExport":true,"isFallback":false,"dynamicIds":[1903,1194,77210,98085,26336,42622,43913,10444,93935,95534,79918,43393,31827,91020,26282,93243,40807,95038,85193,32094],"scriptLoader":[]}</script></body></html>