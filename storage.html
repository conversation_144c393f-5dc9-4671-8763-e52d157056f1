<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="rss.xml" data-next-head=""/><link rel="manifest" href="favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:type" content="website" data-next-head=""/><meta property="og:image:alt" content="Supabase Og Image" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Storage | Store any digital content</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="An open source S3 Compatible Object Store with unlimited scalability, for any file type." data-next-head=""/><meta property="og:title" content="Storage | Store any digital content" data-next-head=""/><meta property="og:description" content="An open source S3 Compatible Object Store with unlimited scalability, for any file type." data-next-head=""/><meta property="og:url" content="https://supabase.com/storage" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/product/storage/storage-og.jpg" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/42c889b959e30ef5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/42c889b959e30ef5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1194.49e6cb8481729c6f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6909.ebb205e01437922e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9005.317122dc4eadf711.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/541.918dfe12aa544079.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2511.d9da7d317c0be94c.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script defer="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4160.345132277a07987e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2263-07e8ebb96cc2b014.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/storage-3c9c00675a749cd4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="_next/supabase-logo-wordmark--light.png 1x, _next/supabase-logo-wordmark--light.png 2x" src="_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="_next/supabase-logo-wordmark--dark.png 1x, _next/supabase-logo-wordmark--dark.png 2x" src="_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav></div><main class="relative min-h-screen"><nav class="relative z-30 hidden md:flex items-center bg-background w-full border-b"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0 flex gap-3 items-center"><a class="flex items-center gap-1.5 px-2 first:-ml-2 py-4 border-b border-transparent text-sm text-foreground-lighter hover:text-foreground focus-visible:ring-2 focus-visible:ring-foreground-lighter focus-visible:text-foreground focus-visible:outline-brand-600" href="database.html"><svg class="h-4 w-4 group-hover/menu-item:text-foreground group-focus-visible/menu-item:text-foreground" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 18"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M3.00829 6.33225H15.1778V11.6648H3.00829V6.33225Z M2 13.0839C2 12.3016 2.63418 11.6674 3.41647 11.6674H14.7483C15.5306 11.6674 16.1648 12.3016 16.1648 13.0839V15.5835C16.1648 16.3658 15.5306 17 14.7483 17H3.41647C2.63418 17 2 16.3658 2 15.5835V13.0839Z M2 2.41647C2 1.63418 2.63418 1 3.41647 1H14.7483C15.5306 1 16.1648 1.63418 16.1648 2.41647V4.9161C16.1648 5.6984 15.5306 6.33257 14.7483 6.33257H3.41647C2.63418 6.33257 2 5.6984 2 4.9161V2.41647Z" stroke="currentColor"></path></svg><p>Database</p></a><a class="flex items-center gap-1.5 px-2 first:-ml-2 py-4 border-b border-transparent text-sm text-foreground-lighter hover:text-foreground focus-visible:ring-2 focus-visible:ring-foreground-lighter focus-visible:text-foreground focus-visible:outline-brand-600" href="auth.html"><svg class="h-4 w-4 group-hover/menu-item:text-foreground group-focus-visible/menu-item:text-foreground" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 18"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M3.02644 12.0457H9.18048M3.02644 12.0457V14.5072H9.18048V12.0457M3.02644 12.0457V9.58414H9.18048V12.0457M11.0339 5.92308V3.46154C11.0339 2.10207 9.93179 1 8.57228 1C7.21277 1 6.11067 2.10207 6.11067 3.46154V5.92308M3 8.38465L3 14.5384C3 15.8979 4.10208 17 5.46157 17H11.6157C12.9752 17 14.0773 15.8979 14.0773 14.5384V8.38465C14.0773 7.02516 12.9752 5.92308 11.6157 5.92308L5.46158 5.92308C4.10209 5.92308 3 7.02516 3 8.38465Z" stroke="currentColor"></path></svg><p>Auth</p></a><a class="flex items-center gap-1.5 px-2 first:-ml-2 py-4 border-b text-sm hover:text-foreground focus-visible:ring-2 focus-visible:ring-foreground-lighter focus-visible:text-foreground focus-visible:outline-brand-600 border-foreground-light text-foreground" href="storage.html"><svg class="h-4 w-4 group-hover/menu-item:text-foreground group-focus-visible/menu-item:text-foreground" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 18"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M15.7014 8.34507V5.80169L10.8772 1.00033H3.7014C2.96501 1.00033 2.36805 1.59728 2.36805 2.33367V6.33312M15.6545 5.77939L10.8752 1L10.8752 4.44605C10.8752 5.18243 11.4722 5.77939 12.2086 5.77939L15.6545 5.77939ZM4.29028 6.33312H2.33335C1.59696 6.33312 1 6.93008 1 7.66647V14.3333C1 15.8061 2.19392 17 3.66669 17H14.3333C15.8061 17 17 15.8061 17 14.3333V9.67842C17 8.94203 16.403 8.34507 15.6667 8.34507H7.42712C7.07725 8.34507 6.7414 8.20755 6.492 7.96218L5.2254 6.71601C4.976 6.47063 4.64015 6.33312 4.29028 6.33312Z" stroke="currentColor"></path></svg><p>Storage</p></a><a class="flex items-center gap-1.5 px-2 first:-ml-2 py-4 border-b border-transparent text-sm text-foreground-lighter hover:text-foreground focus-visible:ring-2 focus-visible:ring-foreground-lighter focus-visible:text-foreground focus-visible:outline-brand-600" href="edge-functions.html"><svg class="h-4 w-4 group-hover/menu-item:text-foreground group-focus-visible/menu-item:text-foreground" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 18"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M5.31216 16.101C6.41582 16.6754 7.67021 17 9.00043 17C13.419 17 17.0009 13.4183 17.0009 9C17.0009 7.66622 16.6744 6.40868 16.0971 5.30289M12.7352 1.92336C11.6203 1.33382 10.3494 1 9.00043 1C4.58192 1 1 4.58172 1 9C1 10.3615 1.34015 11.6436 1.94012 12.766M1.94012 12.766C1.61762 13.16 1.42413 13.6637 1.42413 14.2126C1.42413 15.475 2.44753 16.4983 3.70997 16.4983C4.9724 16.4983 5.99581 15.475 5.99581 14.2126C5.99581 12.9502 4.9724 11.9269 3.70997 11.9269C2.99646 11.9269 2.35931 12.2538 1.94012 12.766ZM16.6199 3.7793C16.6199 5.04166 15.5965 6.06501 14.3341 6.06501C13.0716 6.06501 12.0482 5.04166 12.0482 3.7793C12.0482 2.51693 13.0716 1.49358 14.3341 1.49358C15.5965 1.49358 16.6199 2.51693 16.6199 3.7793ZM14.1436 9C14.1436 11.8403 11.8409 14.1429 9.00043 14.1429C6.15996 14.1429 3.8573 11.8403 3.8573 9C3.8573 6.15968 6.15996 3.85714 9.00043 3.85714C11.8409 3.85714 14.1436 6.15968 14.1436 9Z" stroke="currentColor"></path></svg><p>Edge Functions</p></a><a class="flex items-center gap-1.5 px-2 first:-ml-2 py-4 border-b border-transparent text-sm text-foreground-lighter hover:text-foreground focus-visible:ring-2 focus-visible:ring-foreground-lighter focus-visible:text-foreground focus-visible:outline-brand-600" href="realtime.html"><svg class="h-4 w-4 group-hover/menu-item:text-foreground group-focus-visible/menu-item:text-foreground" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 18"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M5.94415 1V3.85995M3.80348 3.85995L1.35903 1.34096M3.80348 5.83876H1M11.5534 11.6817L15.3703 10.7317C15.901 10.5996 15.9633 9.87023 15.4626 9.6501L6.36613 5.65053C5.8879 5.44026 5.40048 5.92671 5.60979 6.40536L9.53793 15.3879C9.75628 15.8872 10.4825 15.8291 10.6187 15.3015L11.5534 11.6817Z" stroke="currentColor"></path></svg><p>Realtime</p></a></div></nav><div class="container relative mx-auto px-6 pt-16 pb-0 sm:px-16 lg:pt-28 xl:px-20"><div class="grid grid-cols-12"><div class="col-span-12 space-y-8 lg:col-span-5"><div><div class="mb-4 flex items-center gap-3"><div class="inline-flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-md bg-foreground text-background-alternative   "><svg class="h-5 w-5 stroke-background   " xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M20.4997 12.1386V9.15811L14.8463 3.53163H6.43717C5.57423 3.53163 4.87467 4.23119 4.87467 5.09413V9.78087M20.4447 9.13199L14.844 3.53125L14.844 7.56949C14.844 8.43243 15.5436 9.13199 16.4065 9.13199L20.4447 9.13199ZM7.12729 9.78087H4.83398C3.97104 9.78087 3.27148 10.4804 3.27148 11.3434V19.1559C3.27148 20.8818 4.67059 22.2809 6.39648 22.2809H18.8965C20.6224 22.2809 22.0215 20.8818 22.0215 19.1559V13.7011C22.0215 12.8381 21.3219 12.1386 20.459 12.1386H10.8032C10.3933 12.1386 9.99969 11.9774 9.70743 11.6899L8.22312 10.2296C7.93086 9.94202 7.53729 9.78087 7.12729 9.78087Z"></path></svg></div><span class="text-foreground">Storage</span></div><h1 class="h1"><span>Store and serve<br/> any type of digital content</span></h1></div><div><p class="p lg:text-lg">An open source <b>S3 Compatible</b> Object Store, with unlimited scalability, for any file type.</p><p class="p lg:text-lg">With custom policies and permissions that are familiar and easy to implement.</p></div><div class="flex flex-row md:flex-row md:items-center"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="dashboard/org.html"><span class="truncate">Start a project</span></a><a class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px] ml-2" data-size="medium" type="button" href="docs/guides/storage.html"><div class="[&amp;_svg]:h-[20px] [&amp;_svg]:w-[20px] text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-book-open"><path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path><path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path></svg></div><span class="truncate">See documentation</span></a></div></div><div class="col-span-12 mt-8 lg:col-span-7 lg:mt-0 xl:col-span-6 xl:col-start-7"></div></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20"><div class="grid grid-cols-12"><div class="col-span-12 mb-10 lg:col-span-3 lg:mb-0"><div class="p mb-4 flex items-center space-x-2"><div class="inline-flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-md bg-foreground text-background-alternative   "><svg class="h-5 w-5 stroke-background   " xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M20.4997 12.1386V9.15811L14.8463 3.53163H6.43717C5.57423 3.53163 4.87467 4.23119 4.87467 5.09413V9.78087M20.4447 9.13199L14.844 3.53125L14.844 7.56949C14.844 8.43243 15.5436 9.13199 16.4065 9.13199L20.4447 9.13199ZM7.12729 9.78087H4.83398C3.97104 9.78087 3.27148 10.4804 3.27148 11.3434V19.1559C3.27148 20.8818 4.67059 22.2809 6.39648 22.2809H18.8965C20.6224 22.2809 22.0215 20.8818 22.0215 19.1559V13.7011C22.0215 12.8381 21.3219 12.1386 20.459 12.1386H10.8032C10.3933 12.1386 9.99969 11.9774 9.70743 11.6899L8.22312 10.2296C7.93086 9.94202 7.53729 9.78087 7.12729 9.78087Z"></path></svg></div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg><div class="inline-flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-md bg-foreground text-background-alternative   "><svg class="h-5 w-5 stroke-background   " xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M5.03305 15.8071H12.7252M5.03305 15.8071V18.884H12.7252V15.8071M5.03305 15.8071V12.7302H12.7252V15.8071M15.0419 8.15385V5.07692C15.0419 3.37759 13.6643 2 11.965 2C10.2657 2 8.88814 3.37759 8.88814 5.07692V8.15385M5 11.2307L5 18.9231C5 20.6224 6.37757 22 8.07689 22H15.769C17.4683 22 18.8459 20.6224 18.8459 18.9231V11.2307C18.8459 9.53142 17.4683 8.15385 15.769 8.15385L8.07689 8.15385C6.37757 8.15385 5 9.53142 5 11.2307Z"></path></svg></div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x"><path d="M18 6 6 18"></path><path d="m6 6 12 12"></path></svg><div class="inline-flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-md bg-foreground text-background-alternative   "><svg class="h-5 w-5 stroke-background   " xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M5.18625 8.66531H19.5035V15.331H5.18625V8.66531Z M4 17.0007C4 16.0804 4.7461 15.3343 5.66645 15.3343H18.9984C19.9187 15.3343 20.6648 16.0804 20.6648 17.0007V20.3335C20.6648 21.2539 19.9187 22 18.9984 22H5.66646C4.7461 22 4 21.2539 4 20.3335V17.0007Z M4 3.66646C4 2.7461 4.7461 2 5.66645 2H18.9984C19.9187 2 20.6648 2.7461 20.6648 3.66645V6.99926C20.6648 7.91962 19.9187 8.66572 18.9984 8.66572H5.66646C4.7461 8.66572 4 7.91962 4 6.99926V3.66646Z"></path></svg></div></div><h4 class="h4">Interoperable</h4><p class="p">Integrates well with the rest of Supabase ecosystem, including Auth and Postgres.</p></div><div class="col-span-12 mb-10 lg:col-span-3 lg:col-start-5 lg:mb-0"><div class="mb-4 flex items-center space-x-2"><div class="inline-flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-md bg-foreground text-background-alternative   "><svg class="h-5 w-5 stroke-background   " xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg></div></div><h4 class="h4">Lightning fast</h4><p class="p">Thin API server layer that leverages Postgres&#x27; permissions and performance.</p></div><div class="col-span-12 lg:col-span-3 lg:col-start-9"><div class="mb-4 flex items-center space-x-2"><div class="inline-flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-md bg-foreground text-background-alternative   "><svg class="h-5 w-5 stroke-background   " xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path></svg></div></div><h4 class="h4">Multiple Protocol Support</h4><p class="p">S3, Resumable Uploads and Standard Uploads. Enterprise-level scalability.</p></div></div></div><div id="quote" class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 flex flex-col items-center text-center gap-8 md:gap-12 [&amp;_q]:max-w-3xl"><q class="text-2xl max-w-xs md:text-3xl md:max-w-xl">Supabase is great because it has everything. I don&#x27;t need a different solution for authentication, a different solution for database, or a different solution for storage.</q><a class="hover:opacity-90 transition-opacity" href="customers/chatbase.html"><div class="flex flex-col items-center gap-1"><figure class="text-foreground-lighter mb-4"></figure><span class="text-foreground">Yasser Elsaid</span><span class="text-foreground-lighter font-mono text-sm">Founder of Chatbase</span></div></a></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 pt-16"><div class="mb-16 block"><h2 class="h3">Sleek dashboard for managing your media</h2><p class="p text-lg">A complete Object Explorer so that any of your team can use.</p><p class="p">Drag and drop uploading, moving objects, and multiple object selection. As easy as working on your desktop.</p></div><div class="grid grid-cols-12"><div class="col-span-12 w-full lg:col-span-6"><div class="sbui-tabs--alt col-span-12 lg:col-span-7"><div class="block"><div dir="ltr" data-orientation="horizontal" class="w-full justify-between space-y-4 "><div role="tablist" aria-orientation="horizontal" class="flex space-x-1 overflow-auto whitespace-nowrap no-scrollbar mask-fadeout-right" tabindex="-1" data-orientation="horizontal" style="outline:none"><button type="button" role="tab" aria-selected="true" aria-controls="radix-:Rb5kla6:-content-0" data-state="active" id="radix-:Rb5kla6:-trigger-0" class=" relative cursor-pointer flex items-center space-x-2 text-center transition shadow-sm rounded border focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-xs px-2.5 py-1  bg-selection text-foreground border-stronger " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>File previews</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:Rb5kla6:-content-1" data-state="inactive" id="radix-:Rb5kla6:-trigger-1" class=" relative cursor-pointer flex items-center space-x-2 text-center transition shadow-sm rounded border focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-xs px-2.5 py-1  bg-background border-strong hover:border-foreground-muted text-foreground-muted hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Column view</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:Rb5kla6:-content-2" data-state="inactive" id="radix-:Rb5kla6:-trigger-2" class=" relative cursor-pointer flex items-center space-x-2 text-center transition shadow-sm rounded border focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-xs px-2.5 py-1  bg-background border-strong hover:border-foreground-muted text-foreground-muted hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>List view</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:Rb5kla6:-content-3" data-state="inactive" id="radix-:Rb5kla6:-trigger-3" class=" relative cursor-pointer flex items-center space-x-2 text-center transition shadow-sm rounded border focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-xs px-2.5 py-1  bg-background border-strong hover:border-foreground-muted text-foreground-muted hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Multi select actions</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:Rb5kla6:-content-4" data-state="inactive" id="radix-:Rb5kla6:-trigger-4" class=" relative cursor-pointer flex items-center space-x-2 text-center transition shadow-sm rounded border focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-xs px-2.5 py-1  bg-background border-strong hover:border-foreground-muted text-foreground-muted hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Path navigator</span></button></div><div data-state="active" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:Rb5kla6:-trigger-0" id="radix-:Rb5kla6:-content-0" tabindex="0" class="focus:outline-none transition-height " style="animation-duration:0s"><span></span></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:Rb5kla6:-trigger-1" hidden="" id="radix-:Rb5kla6:-content-1" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:Rb5kla6:-trigger-2" hidden="" id="radix-:Rb5kla6:-content-2" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:Rb5kla6:-trigger-3" hidden="" id="radix-:Rb5kla6:-content-3" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:Rb5kla6:-trigger-4" hidden="" id="radix-:Rb5kla6:-content-4" tabindex="0" class="focus:outline-none transition-height "></div></div></div><div class="overflow-hidden rounded-md border border-control bg-border-stronger ImageCarousel_gradient-bg__UZBzE"><div class="swiper" style="z-index:0;overflow:auto;overflow-x:hidden"><div class="swiper-wrapper"><div class="swiper-slide"><img alt="File previews" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto" sizes="100vw" srcSet="_next/file-previews.png 640w, _next/file-previews.png 750w, _next/file-previews.png 828w, _next/file-previews.png 1080w, _next/file-previews.png 1200w, _next/file-previews.png 1920w, _next/file-previews.png 2048w, _next/file-previews.png 3840w" src="_next/file-previews.png"/></div><div class="swiper-slide"><img alt="Column view" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto" sizes="100vw" srcSet="_next/column-view.png 640w, _next/column-view.png 750w, _next/column-view.png 828w, _next/column-view.png 1080w, _next/column-view.png 1200w, _next/column-view.png 1920w, _next/column-view.png 2048w, _next/column-view.png 3840w" src="_next/column-view.png"/></div><div class="swiper-slide"><img alt="List view" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto" sizes="100vw" srcSet="_next/list-view.png 640w, _next/list-view.png 750w, _next/list-view.png 828w, _next/list-view.png 1080w, _next/list-view.png 1200w, _next/list-view.png 1920w, _next/list-view.png 2048w, _next/list-view.png 3840w" src="_next/list-view.png"/></div><div class="swiper-slide"><img alt="Multi select actions" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto" sizes="100vw" srcSet="_next/multi-column-selection.png 640w, _next/multi-column-selection.png 750w, _next/multi-column-selection.png 828w, _next/multi-column-selection.png 1080w, _next/multi-column-selection.png 1200w, _next/multi-column-selection.png 1920w, _next/multi-column-selection.png 2048w, _next/multi-column-selection.png 3840w" src="_next/multi-column-selection.png"/></div><div class="swiper-slide"><img alt="Path navigator" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto" sizes="100vw" srcSet="_next/path-setting.png 640w, _next/path-setting.png 750w, _next/path-setting.png 828w, _next/path-setting.png 1080w, _next/path-setting.png 1200w, _next/path-setting.png 1920w, _next/path-setting.png 2048w, _next/path-setting.png 3840w" src="_next/path-setting.png"/></div></div></div></div></div></div><div class="col-span-12 mt-8 lg:col-span-5 lg:col-start-8 lg:mt-0 xl:col-span-5 xl:col-start-8"><div class="sbui-tabs--underline-alt hidden mb-3"><div dir="ltr" data-orientation="horizontal" class="w-full justify-between space-y-4 "><div role="tablist" aria-orientation="horizontal" class=" flex items-center border-b border-secondary  overflow-auto whitespace-nowrap no-scrollbar mask-fadeout-right" tabindex="-1" data-orientation="horizontal" style="outline:none"><button type="button" role="tab" aria-selected="true" aria-controls="radix-:Rd5kla6:-content-0" data-state="active" id="radix-:Rd5kla6:-trigger-0" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  !text-foreground border-b-2 border-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>File previews</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:Rd5kla6:-content-1" data-state="inactive" id="radix-:Rd5kla6:-trigger-1" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Column view</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:Rd5kla6:-content-2" data-state="inactive" id="radix-:Rd5kla6:-trigger-2" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>List view</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:Rd5kla6:-content-3" data-state="inactive" id="radix-:Rd5kla6:-trigger-3" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Multi select actions</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:Rd5kla6:-content-4" data-state="inactive" id="radix-:Rd5kla6:-trigger-4" class=" relative cursor-pointer text-foreground-lighter flex items-center space-x-2 text-center transition focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-sm leading-4 px-3 py-2  hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Path navigator</span></button></div><div data-state="active" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:Rd5kla6:-trigger-0" id="radix-:Rd5kla6:-content-0" tabindex="0" class="focus:outline-none transition-height " style="animation-duration:0s"></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:Rd5kla6:-trigger-1" hidden="" id="radix-:Rd5kla6:-content-1" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:Rd5kla6:-trigger-2" hidden="" id="radix-:Rd5kla6:-content-2" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:Rd5kla6:-trigger-3" hidden="" id="radix-:Rd5kla6:-content-3" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:Rd5kla6:-trigger-4" hidden="" id="radix-:Rd5kla6:-content-4" tabindex="0" class="focus:outline-none transition-height "></div></div></div><div class="swiper"><div class="swiper-wrapper"><div class="swiper-slide py-4"><h4 class="text-foreground mb-4 text-xl">File previews</h4><p class="p text-base">Preview any media type, including video and audio.</p></div><div class="swiper-slide py-4"><h4 class="text-foreground mb-4 text-xl">Column view</h4><p class="p text-base">Slick Miller-column navigation for rapid folder exploration.</p></div><div class="swiper-slide py-4"><h4 class="text-foreground mb-4 text-xl">List view</h4><p class="p text-base">List View to find detailed File metadata at a glance.</p></div><div class="swiper-slide py-4"><h4 class="text-foreground mb-4 text-xl">Multi select actions</h4><p class="p text-base">Multi-import and multi-export. Select multiple files from multiple folders.</p></div><div class="swiper-slide py-4"><h4 class="text-foreground mb-4 text-xl">Path navigator</h4><p class="p text-base">If you know the exact path of your file, type it in and navigate directly.</p></div></div><div class="my-8"><span class="p mb-4 block">Check out our example app</span></div></div></div></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 pt-0"><div class="grid grid-cols-12 lg:gap-16"><div class="col-span-12 pb-8 lg:col-span-5 xl:col-span-5"><h2 class="h2">Simple and convenient APIs</h2><div class="p"><p>Built from the ground up for interoperable authentication.</p><p>Fast and easy to implement using our powerful library clients.</p></div><div class="py-8"><div class="my-8 grid grid-cols-12 gap-8 lg:gap-0 xl:gap-16"><div class="col-span-6 lg:col-span-12 lg:mb-8 xl:col-span-4 xl:mb-0"><div class="p mb-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-wifi"><path d="M12 20h.01"></path><path d="M2 8.82a15 15 0 0 1 20 0"></path><path d="M5 12.859a10 10 0 0 1 14 0"></path><path d="M8.5 16.429a5 5 0 0 1 7 0"></path></svg></div><h4 class="text-foreground mb-4 text-base">CDN</h4><p class="p">Serve from over 285 cities globally to reduce latency.</p><a data-size="small" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm leading-4 px-3 py-2 h-[34px] mt-4" href="docs/guides/storage/cdn/fundamentals.html"><div class="[&amp;_svg]:h-[18px] [&amp;_svg]:w-[18px] text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></div><span class="truncate">Explore docs</span></a></div><div class="col-span-6 lg:col-span-12 xl:col-span-4"><div class="p mb-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shuffle"><path d="M2 18h1.4c1.3 0 2.5-.6 3.3-1.7l6.1-8.6c.7-1.1 2-1.7 3.3-1.7H22"></path><path d="m18 2 4 4-4 4"></path><path d="M2 6h1.9c1.5 0 2.9.9 3.6 2.2"></path><path d="M22 18h-5.9c-1.3 0-2.6-.7-3.3-1.8l-.5-.8"></path><path d="m18 14 4 4-4 4"></path></svg></div><h4 class="text-foreground mb-4 text-base">Image Optimizations and Transformations</h4><p class="p">Resize and compress your media files on the fly.</p><a data-size="small" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm leading-4 px-3 py-2 h-[34px] mt-4" href="docs/guides/storage/serving/image-transformations.html"><div class="[&amp;_svg]:h-[18px] [&amp;_svg]:w-[18px] text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></div><span class="truncate">Explore docs</span></a></div></div></div></div><div class="sbui-tabs--alt col-span-12 lg:col-span-7 xl:col-span-6 xl:col-start-7"><div dir="ltr" data-orientation="horizontal" class="w-full justify-between space-y-4 "><div role="tablist" aria-orientation="horizontal" class="flex space-x-1 overflow-auto whitespace-nowrap no-scrollbar mask-fadeout-right" tabindex="-1" data-orientation="horizontal" style="outline:none"><button type="button" role="tab" aria-selected="true" aria-controls="radix-:R36kla6:-content-0" data-state="active" id="radix-:R36kla6:-trigger-0" class=" relative cursor-pointer flex items-center space-x-2 text-center transition shadow-sm rounded border focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-xs px-2.5 py-1  bg-selection text-foreground border-stronger " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Upload a file</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:R36kla6:-content-1" data-state="inactive" id="radix-:R36kla6:-trigger-1" class=" relative cursor-pointer flex items-center space-x-2 text-center transition shadow-sm rounded border focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-xs px-2.5 py-1  bg-background border-strong hover:border-foreground-muted text-foreground-muted hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Download a file</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:R36kla6:-content-2" data-state="inactive" id="radix-:R36kla6:-trigger-2" class=" relative cursor-pointer flex items-center space-x-2 text-center transition shadow-sm rounded border focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-xs px-2.5 py-1  bg-background border-strong hover:border-foreground-muted text-foreground-muted hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>List files</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:R36kla6:-content-3" data-state="inactive" id="radix-:R36kla6:-trigger-3" class=" relative cursor-pointer flex items-center space-x-2 text-center transition shadow-sm rounded border focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-xs px-2.5 py-1  bg-background border-strong hover:border-foreground-muted text-foreground-muted hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Move and rename files</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:R36kla6:-content-4" data-state="inactive" id="radix-:R36kla6:-trigger-4" class=" relative cursor-pointer flex items-center space-x-2 text-center transition shadow-sm rounded border focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-xs px-2.5 py-1  bg-background border-strong hover:border-foreground-muted text-foreground-muted hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Delete files</span></button></div><div data-state="active" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:R36kla6:-trigger-0" id="radix-:R36kla6:-content-0" tabindex="0" class="focus:outline-none transition-height " style="animation-duration:0s"><span></span></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:R36kla6:-trigger-1" hidden="" id="radix-:R36kla6:-content-1" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:R36kla6:-trigger-2" hidden="" id="radix-:R36kla6:-content-2" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:R36kla6:-trigger-3" hidden="" id="radix-:R36kla6:-content-3" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:R36kla6:-trigger-4" hidden="" id="radix-:R36kla6:-content-4" tabindex="0" class="focus:outline-none transition-height "></div></div><div class="overflow-hidden"><div class="swiper" style="z-index:0;margin-right:1px"><div class="swiper-wrapper"><div class="swiper-slide"></div><div class="swiper-slide"></div><div class="swiper-slide"></div><div class="swiper-slide"></div><div class="swiper-slide"></div></div></div></div></div></div></div><div class="relative"><div class="section--masked"><div class="section--bg-masked"><div class="section--bg border-t border-control"></div></div><div class="section-container pt-12 pb-0"><div class="overflow-x-hidden"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20"><div class="grid grid-cols-12 lg:gap-16"><div class="col-span-12 mb-8 lg:col-span-5"><h2 class="h3">Integrates natively <br/>with Supabase Auth</h2><p class="p">Using Postgres Row Level Security to create Object access rules.</p><p class="p">Storage Authorization is built around Postgres so that you can use any combination of SQL, Postgres functions, and even your own metadata to write policies.</p><a data-size="small" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm leading-4 px-3 py-2 h-[34px] mt-4" href="docs/reference/javascript/storage-createbucket.html"><div class="[&amp;_svg]:h-[18px] [&amp;_svg]:w-[18px] text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></div><span class="truncate">Explore documentation</span></a></div><div class="col-span-12 lg:col-span-6 lg:col-start-7"><div class="sbui-tabs--alt col-span-12 space-y-2 lg:col-span-6 lg:col-start-7"><div dir="ltr" data-orientation="horizontal" class="w-full justify-between space-y-4 "><div role="tablist" aria-orientation="horizontal" class="flex space-x-1 overflow-auto whitespace-nowrap no-scrollbar mask-fadeout-right" tabindex="-1" data-orientation="horizontal" style="outline:none"><button type="button" role="tab" aria-selected="true" aria-controls="radix-:Rd7kla6:-content-0" data-state="active" id="radix-:Rd7kla6:-trigger-0" class=" relative cursor-pointer flex items-center space-x-2 text-center transition shadow-sm rounded border focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-xs px-2.5 py-1  bg-selection text-foreground border-stronger " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Public access to a bucket</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:Rd7kla6:-content-1" data-state="inactive" id="radix-:Rd7kla6:-trigger-1" class=" relative cursor-pointer flex items-center space-x-2 text-center transition shadow-sm rounded border focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-xs px-2.5 py-1  bg-background border-strong hover:border-foreground-muted text-foreground-muted hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Public access to a folder</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:Rd7kla6:-content-2" data-state="inactive" id="radix-:Rd7kla6:-trigger-2" class=" relative cursor-pointer flex items-center space-x-2 text-center transition shadow-sm rounded border focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-xs px-2.5 py-1  bg-background border-strong hover:border-foreground-muted text-foreground-muted hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Authenticated access to a bucket</span></button><button type="button" role="tab" aria-selected="false" aria-controls="radix-:Rd7kla6:-content-3" data-state="inactive" id="radix-:Rd7kla6:-trigger-3" class=" relative cursor-pointer flex items-center space-x-2 text-center transition shadow-sm rounded border focus:outline-none focus-visible:ring focus-visible:ring-foreground-muted focus-visible:border-foreground-muted  text-xs px-2.5 py-1  bg-background border-strong hover:border-foreground-muted text-foreground-muted hover:text-foreground " tabindex="-1" data-orientation="horizontal" data-radix-collection-item=""><span>Individual access to a file</span></button></div><div data-state="active" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:Rd7kla6:-trigger-0" id="radix-:Rd7kla6:-content-0" tabindex="0" class="focus:outline-none transition-height " style="animation-duration:0s"><span></span></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:Rd7kla6:-trigger-1" hidden="" id="radix-:Rd7kla6:-content-1" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:Rd7kla6:-trigger-2" hidden="" id="radix-:Rd7kla6:-content-2" tabindex="0" class="focus:outline-none transition-height "></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-:Rd7kla6:-trigger-3" hidden="" id="radix-:Rd7kla6:-content-3" tabindex="0" class="focus:outline-none transition-height "></div></div><div class="swiper" style="z-index:0"><div class="swiper-wrapper"><div class="swiper-slide"></div><div class="swiper-slide"></div><div class="swiper-slide"></div><div class="swiper-slide"></div></div></div><div class="bg-surface-100 border-default overflow-hidden rounded-md border p-8"><div class="swiper" style="z-index:0"><div class="swiper-wrapper"><div class="swiper-slide"><p><span class="text-foreground mb-4 block text-lg">Allow public CRUD access to a bucket</span></p><p class="p mb-6 block">This will allow any user access to the bucket named &#x27;avatars&#x27;</p><div class="justify-between"><div><p class="p mr-4"></p></div></div></div><div class="swiper-slide"><p><span class="text-foreground mb-4 block text-lg">Allow public CRUD access to a folder in a bucket</span></p><p class="p mb-6 block">This will allow any user access to the folder named &#x27;public&#x27; in the bucket &#x27;avatars&#x27;</p><div class="justify-between"><div><p class="p mr-4"></p></div></div></div><div class="swiper-slide"><p><span class="text-foreground mb-4 block text-lg">Allow any authenticated user access to a folder</span></p><p class="p mb-6 block">This will allow any authenticated user access to the folder named &#x27;authenticated&#x27; in the bucket &#x27;avatars&#x27;</p><div class="justify-between"><div><p class="p mr-4"></p></div></div></div><div class="swiper-slide"><p><span class="text-foreground mb-4 block text-lg">Allow a specific user access to a file</span></p><p class="p mb-6 block">This will allow a specific user based on the user&#x27;s UID access to a file named &#x27;only_uid.jpg&#x27;</p><div class="justify-between"><div><p class="p mr-4"></p></div></div></div></div></div></div></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="_next/supabase-logo-wordmark--light.png 1x, _next/supabase-logo-wordmark--light.png 2x" src="_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="_next/supabase-logo-wordmark--dark.png 1x, _next/supabase-logo-wordmark--dark.png 2x" src="_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{}},"page":"/storage","query":{},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","nextExport":true,"autoExport":true,"isFallback":false,"dynamicIds":[1194,16909,25303,42511,14160],"scriptLoader":[]}</script></body></html>