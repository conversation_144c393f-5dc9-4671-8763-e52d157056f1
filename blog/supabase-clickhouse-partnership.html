<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">ClickHouse Partnership, improved Postgres Replication, and Disk Management</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Improving the developer experience between Postgres and ClickHouse." data-next-head=""/><meta property="og:title" content="ClickHouse Partnership, improved Postgres Replication, and Disk Management" data-next-head=""/><meta property="og:description" content="Improving the developer experience between Postgres and ClickHouse." data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-clickhouse-partnership" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2024-10-30" data-next-head=""/><meta property="article:author" content="https://github.com/kiwicopple" data-next-head=""/><meta property="article:tag" content="postgres" data-next-head=""/><meta property="article:tag" content="clickhouse" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/clickhouse-supabase/supabase-clickhouse-og.png" data-next-head=""/><meta property="og:image:alt" content="ClickHouse Partnership, improved Postgres Replication, and Disk Management thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">ClickHouse Partnership, improved Postgres Replication, and Disk Management</h1><div class="text-light flex space-x-3 text-sm"><p>30 Oct 2024</p><p>•</p><p>6 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/kiwicopple"><div class="flex items-center gap-3"><div class="w-10"><img alt="Paul Copplestone avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fkiwicopple.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fkiwicopple.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fkiwicopple.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Paul Copplestone</span><span class="text-foreground-lighter mb-0 text-xs">CEO and Co-Founder</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="ClickHouse Partnership, improved Postgres Replication, and Disk Management" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fclickhouse-supabase%2Fsupabase-clickhouse-og.png&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fclickhouse-supabase%2Fsupabase-clickhouse-og.png&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fclickhouse-supabase%2Fsupabase-clickhouse-og.png&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fclickhouse-supabase%2Fsupabase-clickhouse-og.png&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fclickhouse-supabase%2Fsupabase-clickhouse-og.png&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fclickhouse-supabase%2Fsupabase-clickhouse-og.png&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fclickhouse-supabase%2Fsupabase-clickhouse-og.png&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fclickhouse-supabase%2Fsupabase-clickhouse-og.png&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fclickhouse-supabase%2Fsupabase-clickhouse-og.png&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fclickhouse-supabase%2Fsupabase-clickhouse-og.png&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fclickhouse-supabase%2Fsupabase-clickhouse-og.png&amp;w=3840&amp;q=100"/></div><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p>We&#x27;re seeing an emerging trend for AI customers: Postgres and ClickHouse is becoming the “default data stack”.</p>
<p>This makes sense - AI companies typically generate a lot of logs and analytical data, which is better suited for an OLAP database like ClickHouse.</p>
<blockquote class="text-foreground"><p>The combination of Supabase and ClickHouse together are perfect for <a href="https://helicone.ai">Helicone.ai</a>, providing the flexibility of Postgres with the analytical power of ClickHouse — an open-source stack we can trust and customize.</p><div class="align-center m-0 flex h-8 items-center gap-3"><img alt="Justin Torre, CTO @ Helicone.ai avatar" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="h-8 w-8 rounded-full object-cover text-center m-0" style="color:transparent" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Favatars%2Fjustin-torre.png&amp;w=32&amp;q=75 1x, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Favatars%2Fjustin-torre.png&amp;w=64&amp;q=75 2x" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Favatars%2Fjustin-torre.png&amp;w=64&amp;q=75"/><figcaption style="margin-top:0" class="text-foreground-lighter font-normal not-italic not-prose"><p>Justin Torre, CTO @ Helicone.ai</p></figcaption></div></blockquote>
<h2 id="supabase--clickhouse-partnership" class="group scroll-mt-24">Supabase + ClickHouse Partnership<a href="#supabase--clickhouse-partnership" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>The partnership between Supabase and ClickHouse aims to create a seamless experience, building on the already solid Postgres + ClickHouse foundation. Today, we&#x27;re releasing new features to enhance this integration.</p>
<blockquote class="text-foreground"><p>ClickHouse is very excited to partner with Supabase to make it easy for customers to use both technologies together. Through this partnership, we aim to make it even simpler for Postgres developers to use ClickHouse in conjunction and build real-time, data-driven applications at scale.</p><div class="align-center m-0 flex h-8 items-center gap-3"><img alt="Aaron Katz, CEO @ ClickHouse Inc. avatar" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="h-8 w-8 rounded-full object-cover text-center m-0" style="color:transparent" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Favatars%2Faaron-katz.jpg&amp;w=32&amp;q=75 1x, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Favatars%2Faaron-katz.jpg&amp;w=64&amp;q=75 2x" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Favatars%2Faaron-katz.jpg&amp;w=64&amp;q=75"/><figcaption style="margin-top:0" class="text-foreground-lighter font-normal not-italic not-prose"><p>Aaron Katz, CEO @ ClickHouse Inc.</p></figcaption></div></blockquote>
<h2 id="using-postgres-and-clickhouse-together" class="group scroll-mt-24">Using Postgres and ClickHouse together<a href="#using-postgres-and-clickhouse-together" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Before diving into those changes, some context on how most customers use Supabase and ClickHouse together. While both are databases, they serve different use-cases:</p>
<ul>
<li><strong>Postgres:</strong> Ideal for storing and querying application data, powering critical transactional and web app use cases.</li>
<li><strong>ClickHouse:</strong> Optimized for analytics and reporting, supporting both customer-facing and internal applications</li>
</ul>
<p>Postgres is a row-oriented database, ClickHouse is column-oriented. The ClickHouse team have a <a href="https://clickhouse.com/docs/en/intro#row-oriented-vs-column-oriented-storage">great write up about the difference</a> between the two formats.</p>
<!-- -->
<p>To provide an interface between these, Supabase customers generally use:</p>
<ol>
<li><a href="https://supabase.com/docs/guides/database/extensions/wrappers/clickhouse">clickhouse_fdw</a> to query their ClickHouse data from their Postgres database.</li>
<li><a href="https://www.peerdb.io/">PeerDB</a> to replicate their data from Postgres to ClickHouse.</li>
</ol>
<!-- -->
<h2 id="improving-the-clickhouse--supabase-experience" class="group scroll-mt-24">Improving the ClickHouse &amp; Supabase experience<a href="#improving-the-clickhouse--supabase-experience" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We&#x27;re making a number of changes to our platform based on the feedback we&#x27;ve had from customers.</p>
<h3 id="updated-clickhouse-foreign-data-wrapper" class="group scroll-mt-24">Updated ClickHouse Foreign Data Wrapper<a href="#updated-clickhouse-foreign-data-wrapper" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Using the <a href="https://fdw.dev/catalog/clickhouse/">ClickHouse FDW</a>, you can directly query your ClickHouse database from Postgres:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>-- Connect Postgres to your ClickHouse database:</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>create foreign table user_analytics (</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>  id bigint,</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>  user_id bigint,</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>  event text</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>)</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>server clickhouse_server</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>options ( table &#x27;UserAnalytics&#x27; );</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>-- Query your ClickHouse instance from Postgres:</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>select * from user_analytics where user_id = 1;</span></div></div><br/></code></div></div>
<p>This means you can query your ClickHouse data using the Postgres tooling that you&#x27;re familiar with.</p>
<p>The Wrapper now has support for ClickHouse <a href="https://clickhouse.com/docs/en/sql-reference/statements/create/view#parameterized-view">Parameterized Views</a>. With this update, you can pass query parameters directly to ClickHouse, taking full advantage of its analytical engine::</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>create foreign table user_analytics (</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  id bigint,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  user_id bigint,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  _event text,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>)</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>server clickhouse_server</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>options ( table &#x27;(select * from UserAnalytics(event=${_event}))&#x27; );</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>select * from user_analytics where _event=&#x27;button_click&#x27;;</span></div></div><br/></code></div></div>
<h3 id="more-granular-replication-control" class="group scroll-mt-24">More granular replication control<a href="#more-granular-replication-control" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Many of our customers use <a href="https://www.peerdb.io/">PeerDB</a> to replicate data from Postgres to ClickHouse. This has occasionally presented challenges, particularly with Postgres&#x27;s default 1GB WAL size, which, for large data volumes, can result in data loss if the WAL exceeds this size.</p>
<p>To resolve this, we&#x27;ve added 13 <a href="https://supabase.com/docs/guides/database/custom-postgres-config">configurable Postgres parameters</a>, enabling you to adjust replication settings through the CLI. For example, you can increase the default WAL size to 2GB:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>supabase --experimental --project-ref xxxx-yyy \</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>postgres-config update --config max_slot_wal_keep_size=2GB</span></div></div><br/></code></div></div>
<p>The new CLI config includes the following Postgres parameters:</p>
<ol>
<li><a href="https://www.postgresql.org/docs/current/runtime-config-resource.html#GUC-LOGICAL-DECODING-WORK-MEM">logical_decoding_work_mem</a>: Controls memory used during logical decoding.</li>
<li><a href="https://www.postgresql.org/docs/current/runtime-config-connection.html#GUC-MAX-CONNECTIONS">max_connections</a>: Limits total connections to the Postgres server.</li>
<li><a href="https://www.postgresql.org/docs/current/runtime-config-locks.html#GUC-MAX-LOCKS-PER-TRANSACTION">max_locks_per_transaction</a>: Sets the maximum locks allowed in a single transaction.</li>
<li><a href="https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-REPLICATION-SLOTS">max_replication_slots</a>: Defines the number of replication slots for data streaming.</li>
<li><a href="https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-SLOT-WAL-KEEP-SIZE">max_slot_wal_keep_size</a>: Limits disk space for WAL in replication slots.</li>
<li><a href="https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-STANDBY-ARCHIVE-DELAY">max_standby_archive_delay</a>: Sets how long standby servers can wait for archive recovery.</li>
<li><a href="https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-STANDBY-STREAMING-DELAY">max_standby_streaming_delay</a>: Controls delay on standby servers for streaming replication.</li>
<li><a href="https://www.postgresql.org/docs/current/runtime-config-wal.html#GUC-MAX-WAL-SIZE">max_wal_size</a>: Specifies the maximum size of the Write Ahead Log.</li>
<li><a href="https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-WAL-SENDERS">max_wal_senders</a>: Sets the maximum number of processes sending WAL data.</li>
<li><a href="https://www.postgresql.org/docs/current/runtime-config-resource.html#GUC-MAX-WORKER-PROCESSES">max_worker_processes</a>: Defines the number of background worker processes.</li>
<li><a href="https://www.postgresql.org/docs/current/runtime-config-resource.html#GUC-SHARED-BUFFERS">shared_buffers</a>: Determines the amount of memory for shared buffers.</li>
<li><a href="https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-WAL-KEEP-SIZE">wal_keep_size</a>: Sets minimum WAL size to keep for standby servers.</li>
<li><a href="https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-WAL-SENDER-TIMEOUT">wal_sender_timeout</a>: Specifies the timeout for inactive WAL sender processes.</li>
</ol>
<h3 id="improved-disk-management" class="group scroll-mt-24">Improved Disk Management<a href="#improved-disk-management" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Supabase now provides granular control over disk usage for your Postgres database:</p>
<!-- -->
<p>This is driven directly by customers using tools like PeerDB. With adjustable WAL configuration, it&#x27;s important that developers can manage the disk as well. For example, on the Pro Plan&#x27;s 8GB disk, you can configure your project with options like:</p>
<ul>
<li>Default: 7GB database space, 1GB Write Ahead Log</li>
<li>Custom example: 6GB database space, 2 GB Write Ahead Log</li>
</ul>
<p>Additionally, we&#x27;re introducing High-performance Disks. We&#x27;ll release more details about this later.</p>
<h3 id="clickhouse-platform-updates" class="group scroll-mt-24">ClickHouse platform updates<a href="#clickhouse-platform-updates" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>The ClickHouse team have also been busy. They&#x27;ve released a number of updates to their platform, including:</p>
<ol>
<li>A native Supabase OAuth integration in PeerDB for Postgres CDC to ClickHouse.</li>
<li>Support for IPV6 in PeerDB Cloud.</li>
</ol>
<p>You can learn more about these features in the <a href="https://clickhouse.com/blog/supabase-partnership-native-postgres-replication-clickhouse-fdw">Supabase Partnership</a> post they released today.</p>
<h2 id="whats-next" class="group scroll-mt-24">What&#x27;s next?<a href="#whats-next" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Improving the experience between Postgres and ClickHouse is the first phase of this partnership. We&#x27;re already working on native platform integrations. If you&#x27;re using (or plan to use) Supabase and ClickHouse together please <a href="https://supabase.com/enterprise">reach out</a> - we&#x27;d love more design partners to help shape the future of this integration.</p>
<!-- -->
<p>If you simply want to try out the tools and updates we&#x27;ve described above, you can get started with all of them, free of charge:</p>
<ul>
<li>Supabase: <a href="https://database.new">database.new</a></li>
<li>ClickHouse: <a href="https://clickhouse.com/">clickhouse.com</a></li>
<li>PeerDB: <a href="https://www.peerdb.io/">peerdb.io</a></li>
</ul></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-clickhouse-partnership&amp;text=ClickHouse%20Partnership%2C%20improved%20Postgres%20Replication%2C%20and%20Disk%20Management"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-clickhouse-partnership&amp;text=ClickHouse%20Partnership%2C%20improved%20Postgres%20Replication%2C%20and%20Disk%20Management"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-clickhouse-partnership&amp;t=ClickHouse%20Partnership%2C%20improved%20Postgres%20Replication%2C%20and%20Disk%20Management"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="supabase-dynamic-functions.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Executing Dynamic JavaScript Code on Supabase with Edge Functions</h4><p class="small">13 November 2024</p></div></div></div></div></a></div><div><a href="https://supabase.com/blog/database-build-live-share"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Live Share: Connect to in-browser PGlite with any Postgres client</h4><p class="small">10 October 2024</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/postgres"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">postgres</div></a><a href="https://supabase.com/blog/tags/clickhouse"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">clickhouse</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#supabase--clickhouse-partnership">Supabase + ClickHouse Partnership</a></li>
<li><a href="#using-postgres-and-clickhouse-together">Using Postgres and ClickHouse together</a></li>
<li><a href="#improving-the-clickhouse--supabase-experience">Improving the ClickHouse &amp; Supabase experience</a>
<ul>
<li><a href="#updated-clickhouse-foreign-data-wrapper">Updated ClickHouse Foreign Data Wrapper</a></li>
<li><a href="#more-granular-replication-control">More granular replication control</a></li>
<li><a href="#improved-disk-management">Improved Disk Management</a></li>
<li><a href="#clickhouse-platform-updates">ClickHouse platform updates</a></li>
</ul>
</li>
<li><a href="#whats-next">What&#x27;s next?</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-clickhouse-partnership&amp;text=ClickHouse%20Partnership%2C%20improved%20Postgres%20Replication%2C%20and%20Disk%20Management"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-clickhouse-partnership&amp;text=ClickHouse%20Partnership%2C%20improved%20Postgres%20Replication%2C%20and%20Disk%20Management"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-clickhouse-partnership&amp;t=ClickHouse%20Partnership%2C%20improved%20Postgres%20Replication%2C%20and%20Disk%20Management"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"supabase-dynamic-functions","title":"Executing Dynamic JavaScript Code on Supabase with Edge Functions","description":"Learn how to execute dynamic JavaScript code on Supabase using Edge Functions.","author":"rodrigo_mansueli","image":"2024-11-13-supabase-dynamic-functions/dynamic-og.png","thumb":"2024-11-13-supabase-dynamic-functions/dynamic-og.png","categories":["engineering"],"tags":["edge-funcion","funcions","AI"],"date":"2024-11-13","toc_depth":3,"formattedDate":"13 November 2024","readingTime":"18 minute read","url":"/blog/supabase-dynamic-functions","path":"/blog/supabase-dynamic-functions"},"nextPost":{"slug":"database-build-live-share","title":"Live Share: Connect to in-browser PGlite with any Postgres client","description":"Connect any Postgres client to your postgres.new databases.","author":"jgoux,gregnr","image":"database-build-live-share/database-build-live-share-og.png","thumb":"database-build-live-share/database-build-live-share-thumb.png","categories":["developers","postgres"],"tags":["AI","postgres"],"date":"2024-10-10","toc_depth":3,"formattedDate":"10 October 2024","readingTime":"8 minute read","url":"/blog/database-build-live-share","path":"/blog/database-build-live-share"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-clickhouse-partnership","source":"\nWe're seeing an emerging trend for AI customers: Postgres and ClickHouse is becoming the “default data stack”.\n\nThis makes sense - AI companies typically generate a lot of logs and analytical data, which is better suited for an OLAP database like ClickHouse.\n\n\u003cQuote img=\"justin-torre.png\" caption=\"Justin Torre, CTO @ Helicone.ai\"\u003e\n\nThe combination of Supabase and ClickHouse together are perfect for [Helicone.ai](https://helicone.ai), providing the flexibility of Postgres with the analytical power of ClickHouse — an open-source stack we can trust and customize.\n\n\u003c/Quote\u003e\n\n## Supabase + ClickHouse Partnership\n\nThe partnership between Supabase and ClickHouse aims to create a seamless experience, building on the already solid Postgres + ClickHouse foundation. Today, we're releasing new features to enhance this integration.\n\n\u003cQuote img=\"aaron-katz.jpg\" caption=\"Aaron Katz, CEO @ ClickHouse Inc.\"\u003e\n\nClickHouse is very excited to partner with Supabase to make it easy for customers to use both technologies together. Through this partnership, we aim to make it even simpler for Postgres developers to use ClickHouse in conjunction and build real-time, data-driven applications at scale.\n\n\u003c/Quote\u003e\n\n## Using Postgres and ClickHouse together\n\nBefore diving into those changes, some context on how most customers use Supabase and ClickHouse together. While both are databases, they serve different use-cases:\n\n- **Postgres:** Ideal for storing and querying application data, powering critical transactional and web app use cases.\n- **ClickHouse:** Optimized for analytics and reporting, supporting both customer-facing and internal applications\n\nPostgres is a row-oriented database, ClickHouse is column-oriented. The ClickHouse team have a [great write up about the difference](https://clickhouse.com/docs/en/intro#row-oriented-vs-column-oriented-storage) between the two formats.\n\n\u003cImg\n  alt=\"Browser proxy diagram\"\n  src={{\n    light: '/images/blog/clickhouse-supabase/postgres-clickhouse--light.png',\n    dark: '/images/blog/clickhouse-supabase/postgres-clickhouse--dark.png',\n  }}\n/\u003e\n\nTo provide an interface between these, Supabase customers generally use:\n\n1. [clickhouse_fdw](https://supabase.com/docs/guides/database/extensions/wrappers/clickhouse) to query their ClickHouse data from their Postgres database.\n2. [PeerDB](https://www.peerdb.io/) to replicate their data from Postgres to ClickHouse.\n\n\u003cImg\n  alt=\"Browser proxy diagram\"\n  src={{\n    light: '/images/blog/clickhouse-supabase/postgres-clickhouse-integration--light.png',\n    dark: '/images/blog/clickhouse-supabase/postgres-clickhouse-integration--dark.png',\n  }}\n/\u003e\n\n## Improving the ClickHouse \u0026 Supabase experience\n\nWe're making a number of changes to our platform based on the feedback we've had from customers.\n\n### Updated ClickHouse Foreign Data Wrapper\n\nUsing the [ClickHouse FDW](https://fdw.dev/catalog/clickhouse/), you can directly query your ClickHouse database from Postgres:\n\n```sql\n-- Connect Postgres to your ClickHouse database:\ncreate foreign table user_analytics (\n  id bigint,\n  user_id bigint,\n  event text\n)\nserver clickhouse_server\noptions ( table 'UserAnalytics' );\n\n-- Query your ClickHouse instance from Postgres:\nselect * from user_analytics where user_id = 1;\n```\n\nThis means you can query your ClickHouse data using the Postgres tooling that you're familiar with.\n\nThe Wrapper now has support for ClickHouse [Parameterized Views](https://clickhouse.com/docs/en/sql-reference/statements/create/view#parameterized-view). With this update, you can pass query parameters directly to ClickHouse, taking full advantage of its analytical engine::\n\n```sql\ncreate foreign table user_analytics (\n  id bigint,\n  user_id bigint,\n  _event text,\n)\nserver clickhouse_server\noptions ( table '(select * from UserAnalytics(event=${_event}))' );\n\nselect * from user_analytics where _event='button_click';\n```\n\n### More granular replication control\n\nMany of our customers use [PeerDB](https://www.peerdb.io/) to replicate data from Postgres to ClickHouse. This has occasionally presented challenges, particularly with Postgres's default 1GB WAL size, which, for large data volumes, can result in data loss if the WAL exceeds this size.\n\nTo resolve this, we've added 13 [configurable Postgres parameters](/docs/guides/database/custom-postgres-config), enabling you to adjust replication settings through the CLI. For example, you can increase the default WAL size to 2GB:\n\n```\nsupabase --experimental --project-ref xxxx-yyy \\\npostgres-config update --config max_slot_wal_keep_size=2GB\n```\n\nThe new CLI config includes the following Postgres parameters:\n\n1. [logical_decoding_work_mem](https://www.postgresql.org/docs/current/runtime-config-resource.html#GUC-LOGICAL-DECODING-WORK-MEM): Controls memory used during logical decoding.\n2. [max_connections](https://www.postgresql.org/docs/current/runtime-config-connection.html#GUC-MAX-CONNECTIONS): Limits total connections to the Postgres server.\n3. [max_locks_per_transaction](https://www.postgresql.org/docs/current/runtime-config-locks.html#GUC-MAX-LOCKS-PER-TRANSACTION): Sets the maximum locks allowed in a single transaction.\n4. [max_replication_slots](https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-REPLICATION-SLOTS): Defines the number of replication slots for data streaming.\n5. [max_slot_wal_keep_size](https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-SLOT-WAL-KEEP-SIZE): Limits disk space for WAL in replication slots.\n6. [max_standby_archive_delay](https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-STANDBY-ARCHIVE-DELAY): Sets how long standby servers can wait for archive recovery.\n7. [max_standby_streaming_delay](https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-STANDBY-STREAMING-DELAY): Controls delay on standby servers for streaming replication.\n8. [max_wal_size](https://www.postgresql.org/docs/current/runtime-config-wal.html#GUC-MAX-WAL-SIZE): Specifies the maximum size of the Write Ahead Log.\n9. [max_wal_senders](https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-WAL-SENDERS): Sets the maximum number of processes sending WAL data.\n10. [max_worker_processes](https://www.postgresql.org/docs/current/runtime-config-resource.html#GUC-MAX-WORKER-PROCESSES): Defines the number of background worker processes.\n11. [shared_buffers](https://www.postgresql.org/docs/current/runtime-config-resource.html#GUC-SHARED-BUFFERS): Determines the amount of memory for shared buffers.\n12. [wal_keep_size](https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-WAL-KEEP-SIZE): Sets minimum WAL size to keep for standby servers.\n13. [wal_sender_timeout](https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-WAL-SENDER-TIMEOUT): Specifies the timeout for inactive WAL sender processes.\n\n### Improved Disk Management\n\nSupabase now provides granular control over disk usage for your Postgres database:\n\n\u003cImg\n  alt=\"Disk management\"\n  src={{\n    light: '/images/blog/clickhouse-supabase/disk-management.png',\n    dark: '/images/blog/clickhouse-supabase/disk-management.png',\n  }}\n/\u003e\n\nThis is driven directly by customers using tools like PeerDB. With adjustable WAL configuration, it's important that developers can manage the disk as well. For example, on the Pro Plan's 8GB disk, you can configure your project with options like:\n\n- Default: 7GB database space, 1GB Write Ahead Log\n- Custom example: 6GB database space, 2 GB Write Ahead Log\n\nAdditionally, we're introducing High-performance Disks. We'll release more details about this later.\n\n### ClickHouse platform updates\n\nThe ClickHouse team have also been busy. They've released a number of updates to their platform, including:\n\n1. A native Supabase OAuth integration in PeerDB for Postgres CDC to ClickHouse.\n2. Support for IPV6 in PeerDB Cloud.\n\nYou can learn more about these features in the [Supabase Partnership](https://clickhouse.com/blog/supabase-partnership-native-postgres-replication-clickhouse-fdw) post they released today.\n\n## What's next?\n\nImproving the experience between Postgres and ClickHouse is the first phase of this partnership. We're already working on native platform integrations. If you're using (or plan to use) Supabase and ClickHouse together please [reach out](https://supabase.com/enterprise) - we'd love more design partners to help shape the future of this integration.\n\n\u003cImg\n  alt=\"Supabase integrations\"\n  src={{\n    light: '/images/blog/clickhouse-supabase/integrations.png',\n    dark: '/images/blog/clickhouse-supabase/integrations.png',\n  }}\n/\u003e\n\nIf you simply want to try out the tools and updates we've described above, you can get started with all of them, free of charge:\n\n- Supabase: [database.new](https://database.new)\n- ClickHouse: [clickhouse.com](https://clickhouse.com/)\n- PeerDB: [peerdb.io](https://www.peerdb.io/)\n","title":"ClickHouse Partnership, improved Postgres Replication, and Disk Management","description":"Improving the developer experience between Postgres and ClickHouse.","author":"paul_copplestone","image":"clickhouse-supabase/supabase-clickhouse-og.png","thumb":"clickhouse-supabase/supabase-clickhouse-og.png","categories":["developers","postgres"],"tags":["postgres","clickhouse"],"date":"2024-10-30","toc_depth":3,"content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    a: \"a\",\n    h2: \"h2\",\n    ul: \"ul\",\n    li: \"li\",\n    strong: \"strong\",\n    ol: \"ol\",\n    h3: \"h3\"\n  }, _provideComponents(), props.components), {Quote, Img, CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  if (!Img) _missingMdxReference(\"Img\", true);\n  if (!Quote) _missingMdxReference(\"Quote\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We're seeing an emerging trend for AI customers: Postgres and ClickHouse is becoming the “default data stack”.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This makes sense - AI companies typically generate a lot of logs and analytical data, which is better suited for an OLAP database like ClickHouse.\"\n    }), \"\\n\", _jsx(Quote, {\n      img: \"justin-torre.png\",\n      caption: \"Justin Torre, CTO @ Helicone.ai\",\n      children: _jsxs(_components.p, {\n        children: [\"The combination of Supabase and ClickHouse together are perfect for \", _jsx(_components.a, {\n          href: \"https://helicone.ai\",\n          children: \"Helicone.ai\"\n        }), \", providing the flexibility of Postgres with the analytical power of ClickHouse — an open-source stack we can trust and customize.\"]\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"supabase--clickhouse-partnership\",\n      children: \"Supabase + ClickHouse Partnership\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The partnership between Supabase and ClickHouse aims to create a seamless experience, building on the already solid Postgres + ClickHouse foundation. Today, we're releasing new features to enhance this integration.\"\n    }), \"\\n\", _jsx(Quote, {\n      img: \"aaron-katz.jpg\",\n      caption: \"Aaron Katz, CEO @ ClickHouse Inc.\",\n      children: _jsx(_components.p, {\n        children: \"ClickHouse is very excited to partner with Supabase to make it easy for customers to use both technologies together. Through this partnership, we aim to make it even simpler for Postgres developers to use ClickHouse in conjunction and build real-time, data-driven applications at scale.\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"using-postgres-and-clickhouse-together\",\n      children: \"Using Postgres and ClickHouse together\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Before diving into those changes, some context on how most customers use Supabase and ClickHouse together. While both are databases, they serve different use-cases:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Postgres:\"\n        }), \" Ideal for storing and querying application data, powering critical transactional and web app use cases.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"ClickHouse:\"\n        }), \" Optimized for analytics and reporting, supporting both customer-facing and internal applications\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Postgres is a row-oriented database, ClickHouse is column-oriented. The ClickHouse team have a \", _jsx(_components.a, {\n        href: \"https://clickhouse.com/docs/en/intro#row-oriented-vs-column-oriented-storage\",\n        children: \"great write up about the difference\"\n      }), \" between the two formats.\"]\n    }), \"\\n\", _jsx(Img, {\n      alt: \"Browser proxy diagram\",\n      src: {\n        light: '/images/blog/clickhouse-supabase/postgres-clickhouse--light.png',\n        dark: '/images/blog/clickhouse-supabase/postgres-clickhouse--dark.png'\n      }\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To provide an interface between these, Supabase customers generally use:\"\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://supabase.com/docs/guides/database/extensions/wrappers/clickhouse\",\n          children: \"clickhouse_fdw\"\n        }), \" to query their ClickHouse data from their Postgres database.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://www.peerdb.io/\",\n          children: \"PeerDB\"\n        }), \" to replicate their data from Postgres to ClickHouse.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(Img, {\n      alt: \"Browser proxy diagram\",\n      src: {\n        light: '/images/blog/clickhouse-supabase/postgres-clickhouse-integration--light.png',\n        dark: '/images/blog/clickhouse-supabase/postgres-clickhouse-integration--dark.png'\n      }\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"improving-the-clickhouse--supabase-experience\",\n      children: \"Improving the ClickHouse \u0026 Supabase experience\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We're making a number of changes to our platform based on the feedback we've had from customers.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"updated-clickhouse-foreign-data-wrapper\",\n      children: \"Updated ClickHouse Foreign Data Wrapper\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Using the \", _jsx(_components.a, {\n        href: \"https://fdw.dev/catalog/clickhouse/\",\n        children: \"ClickHouse FDW\"\n      }), \", you can directly query your ClickHouse database from Postgres:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"-- Connect Postgres to your ClickHouse database:\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"create\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" foreign \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"table\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" user_analytics (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  id \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"bigint\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  user_id \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"bigint\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  event text\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"server\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" clickhouse_server\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"options ( \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"table \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'UserAnalytics'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" );\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"-- Query your ClickHouse instance from Postgres:\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"select * from\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" user_analytics \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"where\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" user_id \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"1\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This means you can query your ClickHouse data using the Postgres tooling that you're familiar with.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The Wrapper now has support for ClickHouse \", _jsx(_components.a, {\n        href: \"https://clickhouse.com/docs/en/sql-reference/statements/create/view#parameterized-view\",\n        children: \"Parameterized Views\"\n      }), \". With this update, you can pass query parameters directly to ClickHouse, taking full advantage of its analytical engine::\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"create\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" foreign \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"table\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" user_analytics (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  id \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"bigint\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  user_id \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"bigint\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  _event \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"text\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"server\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" clickhouse_server\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"options ( \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"table \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'(select * from UserAnalytics(event=${_event}))'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" );\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"select * from\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" user_analytics \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"where\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" _event\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'button_click'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"more-granular-replication-control\",\n      children: \"More granular replication control\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Many of our customers use \", _jsx(_components.a, {\n        href: \"https://www.peerdb.io/\",\n        children: \"PeerDB\"\n      }), \" to replicate data from Postgres to ClickHouse. This has occasionally presented challenges, particularly with Postgres's default 1GB WAL size, which, for large data volumes, can result in data loss if the WAL exceeds this size.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To resolve this, we've added 13 \", _jsx(_components.a, {\n        href: \"/docs/guides/database/custom-postgres-config\",\n        children: \"configurable Postgres parameters\"\n      }), \", enabling you to adjust replication settings through the CLI. For example, you can increase the default WAL size to 2GB:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"supabase --experimental --project-ref xxxx-yyy \\\\\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"postgres-config update --config max_slot_wal_keep_size=2GB\",\n              \"props\": {}\n            }]\n          }],\n          \"lang\": \"text\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The new CLI config includes the following Postgres parameters:\"\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://www.postgresql.org/docs/current/runtime-config-resource.html#GUC-LOGICAL-DECODING-WORK-MEM\",\n          children: \"logical_decoding_work_mem\"\n        }), \": Controls memory used during logical decoding.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://www.postgresql.org/docs/current/runtime-config-connection.html#GUC-MAX-CONNECTIONS\",\n          children: \"max_connections\"\n        }), \": Limits total connections to the Postgres server.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://www.postgresql.org/docs/current/runtime-config-locks.html#GUC-MAX-LOCKS-PER-TRANSACTION\",\n          children: \"max_locks_per_transaction\"\n        }), \": Sets the maximum locks allowed in a single transaction.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-REPLICATION-SLOTS\",\n          children: \"max_replication_slots\"\n        }), \": Defines the number of replication slots for data streaming.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-SLOT-WAL-KEEP-SIZE\",\n          children: \"max_slot_wal_keep_size\"\n        }), \": Limits disk space for WAL in replication slots.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-STANDBY-ARCHIVE-DELAY\",\n          children: \"max_standby_archive_delay\"\n        }), \": Sets how long standby servers can wait for archive recovery.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-STANDBY-STREAMING-DELAY\",\n          children: \"max_standby_streaming_delay\"\n        }), \": Controls delay on standby servers for streaming replication.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://www.postgresql.org/docs/current/runtime-config-wal.html#GUC-MAX-WAL-SIZE\",\n          children: \"max_wal_size\"\n        }), \": Specifies the maximum size of the Write Ahead Log.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-WAL-SENDERS\",\n          children: \"max_wal_senders\"\n        }), \": Sets the maximum number of processes sending WAL data.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://www.postgresql.org/docs/current/runtime-config-resource.html#GUC-MAX-WORKER-PROCESSES\",\n          children: \"max_worker_processes\"\n        }), \": Defines the number of background worker processes.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://www.postgresql.org/docs/current/runtime-config-resource.html#GUC-SHARED-BUFFERS\",\n          children: \"shared_buffers\"\n        }), \": Determines the amount of memory for shared buffers.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-WAL-KEEP-SIZE\",\n          children: \"wal_keep_size\"\n        }), \": Sets minimum WAL size to keep for standby servers.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-WAL-SENDER-TIMEOUT\",\n          children: \"wal_sender_timeout\"\n        }), \": Specifies the timeout for inactive WAL sender processes.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"improved-disk-management\",\n      children: \"Improved Disk Management\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Supabase now provides granular control over disk usage for your Postgres database:\"\n    }), \"\\n\", _jsx(Img, {\n      alt: \"Disk management\",\n      src: {\n        light: '/images/blog/clickhouse-supabase/disk-management.png',\n        dark: '/images/blog/clickhouse-supabase/disk-management.png'\n      }\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This is driven directly by customers using tools like PeerDB. With adjustable WAL configuration, it's important that developers can manage the disk as well. For example, on the Pro Plan's 8GB disk, you can configure your project with options like:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Default: 7GB database space, 1GB Write Ahead Log\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Custom example: 6GB database space, 2 GB Write Ahead Log\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Additionally, we're introducing High-performance Disks. We'll release more details about this later.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"clickhouse-platform-updates\",\n      children: \"ClickHouse platform updates\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The ClickHouse team have also been busy. They've released a number of updates to their platform, including:\"\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"A native Supabase OAuth integration in PeerDB for Postgres CDC to ClickHouse.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Support for IPV6 in PeerDB Cloud.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You can learn more about these features in the \", _jsx(_components.a, {\n        href: \"https://clickhouse.com/blog/supabase-partnership-native-postgres-replication-clickhouse-fdw\",\n        children: \"Supabase Partnership\"\n      }), \" post they released today.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"whats-next\",\n      children: \"What's next?\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Improving the experience between Postgres and ClickHouse is the first phase of this partnership. We're already working on native platform integrations. If you're using (or plan to use) Supabase and ClickHouse together please \", _jsx(_components.a, {\n        href: \"https://supabase.com/enterprise\",\n        children: \"reach out\"\n      }), \" - we'd love more design partners to help shape the future of this integration.\"]\n    }), \"\\n\", _jsx(Img, {\n      alt: \"Supabase integrations\",\n      src: {\n        light: '/images/blog/clickhouse-supabase/integrations.png',\n        dark: '/images/blog/clickhouse-supabase/integrations.png'\n      }\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"If you simply want to try out the tools and updates we've described above, you can get started with all of them, free of charge:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Supabase: \", _jsx(_components.a, {\n          href: \"https://database.new\",\n          children: \"database.new\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"ClickHouse: \", _jsx(_components.a, {\n          href: \"https://clickhouse.com/\",\n          children: \"clickhouse.com\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"PeerDB: \", _jsx(_components.a, {\n          href: \"https://www.peerdb.io/\",\n          children: \"peerdb.io\"\n        })]\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"Supabase + ClickHouse Partnership","slug":"supabase--clickhouse-partnership","lvl":2,"i":0,"seen":0},{"content":"Using Postgres and ClickHouse together","slug":"using-postgres-and-clickhouse-together","lvl":2,"i":1,"seen":0},{"content":"Improving the ClickHouse \u0026 Supabase experience","slug":"improving-the-clickhouse--supabase-experience","lvl":2,"i":2,"seen":0},{"content":"Updated ClickHouse Foreign Data Wrapper","slug":"updated-clickhouse-foreign-data-wrapper","lvl":3,"i":3,"seen":0},{"content":"More granular replication control","slug":"more-granular-replication-control","lvl":3,"i":4,"seen":0},{"content":"Improved Disk Management","slug":"improved-disk-management","lvl":3,"i":5,"seen":0},{"content":"ClickHouse platform updates","slug":"clickhouse-platform-updates","lvl":3,"i":6,"seen":0},{"content":"What's next?","slug":"whats-next","lvl":2,"i":7,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,2],"level":0},{"type":"inline","content":"We're seeing an emerging trend for AI customers: Postgres and ClickHouse is becoming the “default data stack”.","level":1,"lines":[1,2],"children":[{"type":"text","content":"We're seeing an emerging trend for AI customers: Postgres and ClickHouse is becoming the “default data stack”.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[3,4],"level":0},{"type":"inline","content":"This makes sense - AI companies typically generate a lot of logs and analytical data, which is better suited for an OLAP database like ClickHouse.","level":1,"lines":[3,4],"children":[{"type":"text","content":"This makes sense - AI companies typically generate a lot of logs and analytical data, which is better suited for an OLAP database like ClickHouse.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[5,6],"level":0},{"type":"inline","content":"\u003cQuote img=\"justin-torre.png\" caption=\"Justin Torre, CTO @ Helicone.ai\"\u003e","level":1,"lines":[5,6],"children":[{"type":"text","content":"\u003cQuote img=\"justin-torre.png\" caption=\"Justin Torre, CTO @ Helicone.ai\"\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[7,8],"level":0},{"type":"inline","content":"The combination of Supabase and ClickHouse together are perfect for [Helicone.ai](https://helicone.ai), providing the flexibility of Postgres with the analytical power of ClickHouse — an open-source stack we can trust and customize.","level":1,"lines":[7,8],"children":[{"type":"text","content":"The combination of Supabase and ClickHouse together are perfect for ","level":0},{"type":"link_open","href":"https://helicone.ai","title":"","level":0},{"type":"text","content":"Helicone.ai","level":1},{"type":"link_close","level":0},{"type":"text","content":", providing the flexibility of Postgres with the analytical power of ClickHouse — an open-source stack we can trust and customize.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[9,10],"level":0},{"type":"inline","content":"\u003c/Quote\u003e","level":1,"lines":[9,10],"children":[{"type":"text","content":"\u003c/Quote\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[11,12],"level":0},{"type":"inline","content":"[Supabase + ClickHouse Partnership](#supabase--clickhouse-partnership)","level":1,"lines":[11,12],"children":[{"type":"text","content":"Supabase + ClickHouse Partnership","level":0}],"lvl":2,"i":0,"seen":0,"slug":"supabase--clickhouse-partnership"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[13,14],"level":0},{"type":"inline","content":"The partnership between Supabase and ClickHouse aims to create a seamless experience, building on the already solid Postgres + ClickHouse foundation. Today, we're releasing new features to enhance this integration.","level":1,"lines":[13,14],"children":[{"type":"text","content":"The partnership between Supabase and ClickHouse aims to create a seamless experience, building on the already solid Postgres + ClickHouse foundation. Today, we're releasing new features to enhance this integration.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[15,16],"level":0},{"type":"inline","content":"\u003cQuote img=\"aaron-katz.jpg\" caption=\"Aaron Katz, CEO @ ClickHouse Inc.\"\u003e","level":1,"lines":[15,16],"children":[{"type":"text","content":"\u003cQuote img=\"aaron-katz.jpg\" caption=\"Aaron Katz, CEO @ ClickHouse Inc.\"\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[17,18],"level":0},{"type":"inline","content":"ClickHouse is very excited to partner with Supabase to make it easy for customers to use both technologies together. Through this partnership, we aim to make it even simpler for Postgres developers to use ClickHouse in conjunction and build real-time, data-driven applications at scale.","level":1,"lines":[17,18],"children":[{"type":"text","content":"ClickHouse is very excited to partner with Supabase to make it easy for customers to use both technologies together. Through this partnership, we aim to make it even simpler for Postgres developers to use ClickHouse in conjunction and build real-time, data-driven applications at scale.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[19,20],"level":0},{"type":"inline","content":"\u003c/Quote\u003e","level":1,"lines":[19,20],"children":[{"type":"text","content":"\u003c/Quote\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[21,22],"level":0},{"type":"inline","content":"[Using Postgres and ClickHouse together](#using-postgres-and-clickhouse-together)","level":1,"lines":[21,22],"children":[{"type":"text","content":"Using Postgres and ClickHouse together","level":0}],"lvl":2,"i":1,"seen":0,"slug":"using-postgres-and-clickhouse-together"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[23,24],"level":0},{"type":"inline","content":"Before diving into those changes, some context on how most customers use Supabase and ClickHouse together. While both are databases, they serve different use-cases:","level":1,"lines":[23,24],"children":[{"type":"text","content":"Before diving into those changes, some context on how most customers use Supabase and ClickHouse together. While both are databases, they serve different use-cases:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[25,28],"level":0},{"type":"list_item_open","lines":[25,26],"level":1},{"type":"paragraph_open","tight":true,"lines":[25,26],"level":2},{"type":"inline","content":"**Postgres:** Ideal for storing and querying application data, powering critical transactional and web app use cases.","level":3,"lines":[25,26],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Postgres:","level":1},{"type":"strong_close","level":0},{"type":"text","content":" Ideal for storing and querying application data, powering critical transactional and web app use cases.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[26,28],"level":1},{"type":"paragraph_open","tight":true,"lines":[26,27],"level":2},{"type":"inline","content":"**ClickHouse:** Optimized for analytics and reporting, supporting both customer-facing and internal applications","level":3,"lines":[26,27],"children":[{"type":"strong_open","level":0},{"type":"text","content":"ClickHouse:","level":1},{"type":"strong_close","level":0},{"type":"text","content":" Optimized for analytics and reporting, supporting both customer-facing and internal applications","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[28,29],"level":0},{"type":"inline","content":"Postgres is a row-oriented database, ClickHouse is column-oriented. The ClickHouse team have a [great write up about the difference](https://clickhouse.com/docs/en/intro#row-oriented-vs-column-oriented-storage) between the two formats.","level":1,"lines":[28,29],"children":[{"type":"text","content":"Postgres is a row-oriented database, ClickHouse is column-oriented. The ClickHouse team have a ","level":0},{"type":"link_open","href":"https://clickhouse.com/docs/en/intro#row-oriented-vs-column-oriented-storage","title":"","level":0},{"type":"text","content":"great write up about the difference","level":1},{"type":"link_close","level":0},{"type":"text","content":" between the two formats.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[30,37],"level":0},{"type":"inline","content":"\u003cImg\n  alt=\"Browser proxy diagram\"\n  src={{\n    light: '/images/blog/clickhouse-supabase/postgres-clickhouse--light.png',\n    dark: '/images/blog/clickhouse-supabase/postgres-clickhouse--dark.png',\n  }}\n/\u003e","level":1,"lines":[30,37],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"Browser proxy diagram\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src={{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"light: '/images/blog/clickhouse-supabase/postgres-clickhouse--light.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"dark: '/images/blog/clickhouse-supabase/postgres-clickhouse--dark.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"}}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[38,39],"level":0},{"type":"inline","content":"To provide an interface between these, Supabase customers generally use:","level":1,"lines":[38,39],"children":[{"type":"text","content":"To provide an interface between these, Supabase customers generally use:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"ordered_list_open","order":1,"lines":[40,43],"level":0},{"type":"list_item_open","lines":[40,41],"level":1},{"type":"paragraph_open","tight":true,"lines":[40,41],"level":2},{"type":"inline","content":"[clickhouse_fdw](https://supabase.com/docs/guides/database/extensions/wrappers/clickhouse) to query their ClickHouse data from their Postgres database.","level":3,"lines":[40,41],"children":[{"type":"link_open","href":"https://supabase.com/docs/guides/database/extensions/wrappers/clickhouse","title":"","level":0},{"type":"text","content":"clickhouse_fdw","level":1},{"type":"link_close","level":0},{"type":"text","content":" to query their ClickHouse data from their Postgres database.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[41,43],"level":1},{"type":"paragraph_open","tight":true,"lines":[41,42],"level":2},{"type":"inline","content":"[PeerDB](https://www.peerdb.io/) to replicate their data from Postgres to ClickHouse.","level":3,"lines":[41,42],"children":[{"type":"link_open","href":"https://www.peerdb.io/","title":"","level":0},{"type":"text","content":"PeerDB","level":1},{"type":"link_close","level":0},{"type":"text","content":" to replicate their data from Postgres to ClickHouse.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"ordered_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[43,50],"level":0},{"type":"inline","content":"\u003cImg\n  alt=\"Browser proxy diagram\"\n  src={{\n    light: '/images/blog/clickhouse-supabase/postgres-clickhouse-integration--light.png',\n    dark: '/images/blog/clickhouse-supabase/postgres-clickhouse-integration--dark.png',\n  }}\n/\u003e","level":1,"lines":[43,50],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"Browser proxy diagram\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src={{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"light: '/images/blog/clickhouse-supabase/postgres-clickhouse-integration--light.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"dark: '/images/blog/clickhouse-supabase/postgres-clickhouse-integration--dark.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"}}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[51,52],"level":0},{"type":"inline","content":"[Improving the ClickHouse \u0026 Supabase experience](#improving-the-clickhouse--supabase-experience)","level":1,"lines":[51,52],"children":[{"type":"text","content":"Improving the ClickHouse \u0026 Supabase experience","level":0}],"lvl":2,"i":2,"seen":0,"slug":"improving-the-clickhouse--supabase-experience"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[53,54],"level":0},{"type":"inline","content":"We're making a number of changes to our platform based on the feedback we've had from customers.","level":1,"lines":[53,54],"children":[{"type":"text","content":"We're making a number of changes to our platform based on the feedback we've had from customers.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[55,56],"level":0},{"type":"inline","content":"[Updated ClickHouse Foreign Data Wrapper](#updated-clickhouse-foreign-data-wrapper)","level":1,"lines":[55,56],"children":[{"type":"text","content":"Updated ClickHouse Foreign Data Wrapper","level":0}],"lvl":3,"i":3,"seen":0,"slug":"updated-clickhouse-foreign-data-wrapper"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[57,58],"level":0},{"type":"inline","content":"Using the [ClickHouse FDW](https://fdw.dev/catalog/clickhouse/), you can directly query your ClickHouse database from Postgres:","level":1,"lines":[57,58],"children":[{"type":"text","content":"Using the ","level":0},{"type":"link_open","href":"https://fdw.dev/catalog/clickhouse/","title":"","level":0},{"type":"text","content":"ClickHouse FDW","level":1},{"type":"link_close","level":0},{"type":"text","content":", you can directly query your ClickHouse database from Postgres:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql","content":"-- Connect Postgres to your ClickHouse database:\ncreate foreign table user_analytics (\n  id bigint,\n  user_id bigint,\n  event text\n)\nserver clickhouse_server\noptions ( table 'UserAnalytics' );\n\n-- Query your ClickHouse instance from Postgres:\nselect * from user_analytics where user_id = 1;\n","lines":[59,72],"level":0},{"type":"paragraph_open","tight":false,"lines":[73,74],"level":0},{"type":"inline","content":"This means you can query your ClickHouse data using the Postgres tooling that you're familiar with.","level":1,"lines":[73,74],"children":[{"type":"text","content":"This means you can query your ClickHouse data using the Postgres tooling that you're familiar with.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[75,76],"level":0},{"type":"inline","content":"The Wrapper now has support for ClickHouse [Parameterized Views](https://clickhouse.com/docs/en/sql-reference/statements/create/view#parameterized-view). With this update, you can pass query parameters directly to ClickHouse, taking full advantage of its analytical engine::","level":1,"lines":[75,76],"children":[{"type":"text","content":"The Wrapper now has support for ClickHouse ","level":0},{"type":"link_open","href":"https://clickhouse.com/docs/en/sql-reference/statements/create/view#parameterized-view","title":"","level":0},{"type":"text","content":"Parameterized Views","level":1},{"type":"link_close","level":0},{"type":"text","content":". With this update, you can pass query parameters directly to ClickHouse, taking full advantage of its analytical engine::","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql","content":"create foreign table user_analytics (\n  id bigint,\n  user_id bigint,\n  _event text,\n)\nserver clickhouse_server\noptions ( table '(select * from UserAnalytics(event=${_event}))' );\n\nselect * from user_analytics where _event='button_click';\n","lines":[77,88],"level":0},{"type":"heading_open","hLevel":3,"lines":[89,90],"level":0},{"type":"inline","content":"[More granular replication control](#more-granular-replication-control)","level":1,"lines":[89,90],"children":[{"type":"text","content":"More granular replication control","level":0}],"lvl":3,"i":4,"seen":0,"slug":"more-granular-replication-control"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[91,92],"level":0},{"type":"inline","content":"Many of our customers use [PeerDB](https://www.peerdb.io/) to replicate data from Postgres to ClickHouse. This has occasionally presented challenges, particularly with Postgres's default 1GB WAL size, which, for large data volumes, can result in data loss if the WAL exceeds this size.","level":1,"lines":[91,92],"children":[{"type":"text","content":"Many of our customers use ","level":0},{"type":"link_open","href":"https://www.peerdb.io/","title":"","level":0},{"type":"text","content":"PeerDB","level":1},{"type":"link_close","level":0},{"type":"text","content":" to replicate data from Postgres to ClickHouse. This has occasionally presented challenges, particularly with Postgres's default 1GB WAL size, which, for large data volumes, can result in data loss if the WAL exceeds this size.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[93,94],"level":0},{"type":"inline","content":"To resolve this, we've added 13 [configurable Postgres parameters](/docs/guides/database/custom-postgres-config), enabling you to adjust replication settings through the CLI. For example, you can increase the default WAL size to 2GB:","level":1,"lines":[93,94],"children":[{"type":"text","content":"To resolve this, we've added 13 ","level":0},{"type":"link_open","href":"/docs/guides/database/custom-postgres-config","title":"","level":0},{"type":"text","content":"configurable Postgres parameters","level":1},{"type":"link_close","level":0},{"type":"text","content":", enabling you to adjust replication settings through the CLI. For example, you can increase the default WAL size to 2GB:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"","content":"supabase --experimental --project-ref xxxx-yyy \\\npostgres-config update --config max_slot_wal_keep_size=2GB\n","lines":[95,99],"level":0},{"type":"paragraph_open","tight":false,"lines":[100,101],"level":0},{"type":"inline","content":"The new CLI config includes the following Postgres parameters:","level":1,"lines":[100,101],"children":[{"type":"text","content":"The new CLI config includes the following Postgres parameters:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"ordered_list_open","order":1,"lines":[102,116],"level":0},{"type":"list_item_open","lines":[102,103],"level":1},{"type":"paragraph_open","tight":true,"lines":[102,103],"level":2},{"type":"inline","content":"[logical_decoding_work_mem](https://www.postgresql.org/docs/current/runtime-config-resource.html#GUC-LOGICAL-DECODING-WORK-MEM): Controls memory used during logical decoding.","level":3,"lines":[102,103],"children":[{"type":"link_open","href":"https://www.postgresql.org/docs/current/runtime-config-resource.html#GUC-LOGICAL-DECODING-WORK-MEM","title":"","level":0},{"type":"text","content":"logical_decoding_work_mem","level":1},{"type":"link_close","level":0},{"type":"text","content":": Controls memory used during logical decoding.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[103,104],"level":1},{"type":"paragraph_open","tight":true,"lines":[103,104],"level":2},{"type":"inline","content":"[max_connections](https://www.postgresql.org/docs/current/runtime-config-connection.html#GUC-MAX-CONNECTIONS): Limits total connections to the Postgres server.","level":3,"lines":[103,104],"children":[{"type":"link_open","href":"https://www.postgresql.org/docs/current/runtime-config-connection.html#GUC-MAX-CONNECTIONS","title":"","level":0},{"type":"text","content":"max_connections","level":1},{"type":"link_close","level":0},{"type":"text","content":": Limits total connections to the Postgres server.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[104,105],"level":1},{"type":"paragraph_open","tight":true,"lines":[104,105],"level":2},{"type":"inline","content":"[max_locks_per_transaction](https://www.postgresql.org/docs/current/runtime-config-locks.html#GUC-MAX-LOCKS-PER-TRANSACTION): Sets the maximum locks allowed in a single transaction.","level":3,"lines":[104,105],"children":[{"type":"link_open","href":"https://www.postgresql.org/docs/current/runtime-config-locks.html#GUC-MAX-LOCKS-PER-TRANSACTION","title":"","level":0},{"type":"text","content":"max_locks_per_transaction","level":1},{"type":"link_close","level":0},{"type":"text","content":": Sets the maximum locks allowed in a single transaction.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[105,106],"level":1},{"type":"paragraph_open","tight":true,"lines":[105,106],"level":2},{"type":"inline","content":"[max_replication_slots](https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-REPLICATION-SLOTS): Defines the number of replication slots for data streaming.","level":3,"lines":[105,106],"children":[{"type":"link_open","href":"https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-REPLICATION-SLOTS","title":"","level":0},{"type":"text","content":"max_replication_slots","level":1},{"type":"link_close","level":0},{"type":"text","content":": Defines the number of replication slots for data streaming.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[106,107],"level":1},{"type":"paragraph_open","tight":true,"lines":[106,107],"level":2},{"type":"inline","content":"[max_slot_wal_keep_size](https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-SLOT-WAL-KEEP-SIZE): Limits disk space for WAL in replication slots.","level":3,"lines":[106,107],"children":[{"type":"link_open","href":"https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-SLOT-WAL-KEEP-SIZE","title":"","level":0},{"type":"text","content":"max_slot_wal_keep_size","level":1},{"type":"link_close","level":0},{"type":"text","content":": Limits disk space for WAL in replication slots.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[107,108],"level":1},{"type":"paragraph_open","tight":true,"lines":[107,108],"level":2},{"type":"inline","content":"[max_standby_archive_delay](https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-STANDBY-ARCHIVE-DELAY): Sets how long standby servers can wait for archive recovery.","level":3,"lines":[107,108],"children":[{"type":"link_open","href":"https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-STANDBY-ARCHIVE-DELAY","title":"","level":0},{"type":"text","content":"max_standby_archive_delay","level":1},{"type":"link_close","level":0},{"type":"text","content":": Sets how long standby servers can wait for archive recovery.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[108,109],"level":1},{"type":"paragraph_open","tight":true,"lines":[108,109],"level":2},{"type":"inline","content":"[max_standby_streaming_delay](https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-STANDBY-STREAMING-DELAY): Controls delay on standby servers for streaming replication.","level":3,"lines":[108,109],"children":[{"type":"link_open","href":"https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-STANDBY-STREAMING-DELAY","title":"","level":0},{"type":"text","content":"max_standby_streaming_delay","level":1},{"type":"link_close","level":0},{"type":"text","content":": Controls delay on standby servers for streaming replication.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[109,110],"level":1},{"type":"paragraph_open","tight":true,"lines":[109,110],"level":2},{"type":"inline","content":"[max_wal_size](https://www.postgresql.org/docs/current/runtime-config-wal.html#GUC-MAX-WAL-SIZE): Specifies the maximum size of the Write Ahead Log.","level":3,"lines":[109,110],"children":[{"type":"link_open","href":"https://www.postgresql.org/docs/current/runtime-config-wal.html#GUC-MAX-WAL-SIZE","title":"","level":0},{"type":"text","content":"max_wal_size","level":1},{"type":"link_close","level":0},{"type":"text","content":": Specifies the maximum size of the Write Ahead Log.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[110,111],"level":1},{"type":"paragraph_open","tight":true,"lines":[110,111],"level":2},{"type":"inline","content":"[max_wal_senders](https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-WAL-SENDERS): Sets the maximum number of processes sending WAL data.","level":3,"lines":[110,111],"children":[{"type":"link_open","href":"https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-MAX-WAL-SENDERS","title":"","level":0},{"type":"text","content":"max_wal_senders","level":1},{"type":"link_close","level":0},{"type":"text","content":": Sets the maximum number of processes sending WAL data.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[111,112],"level":1},{"type":"paragraph_open","tight":true,"lines":[111,112],"level":2},{"type":"inline","content":"[max_worker_processes](https://www.postgresql.org/docs/current/runtime-config-resource.html#GUC-MAX-WORKER-PROCESSES): Defines the number of background worker processes.","level":3,"lines":[111,112],"children":[{"type":"link_open","href":"https://www.postgresql.org/docs/current/runtime-config-resource.html#GUC-MAX-WORKER-PROCESSES","title":"","level":0},{"type":"text","content":"max_worker_processes","level":1},{"type":"link_close","level":0},{"type":"text","content":": Defines the number of background worker processes.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[112,113],"level":1},{"type":"paragraph_open","tight":true,"lines":[112,113],"level":2},{"type":"inline","content":"[shared_buffers](https://www.postgresql.org/docs/current/runtime-config-resource.html#GUC-SHARED-BUFFERS): Determines the amount of memory for shared buffers.","level":3,"lines":[112,113],"children":[{"type":"link_open","href":"https://www.postgresql.org/docs/current/runtime-config-resource.html#GUC-SHARED-BUFFERS","title":"","level":0},{"type":"text","content":"shared_buffers","level":1},{"type":"link_close","level":0},{"type":"text","content":": Determines the amount of memory for shared buffers.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[113,114],"level":1},{"type":"paragraph_open","tight":true,"lines":[113,114],"level":2},{"type":"inline","content":"[wal_keep_size](https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-WAL-KEEP-SIZE): Sets minimum WAL size to keep for standby servers.","level":3,"lines":[113,114],"children":[{"type":"link_open","href":"https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-WAL-KEEP-SIZE","title":"","level":0},{"type":"text","content":"wal_keep_size","level":1},{"type":"link_close","level":0},{"type":"text","content":": Sets minimum WAL size to keep for standby servers.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[114,116],"level":1},{"type":"paragraph_open","tight":true,"lines":[114,115],"level":2},{"type":"inline","content":"[wal_sender_timeout](https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-WAL-SENDER-TIMEOUT): Specifies the timeout for inactive WAL sender processes.","level":3,"lines":[114,115],"children":[{"type":"link_open","href":"https://www.postgresql.org/docs/current/runtime-config-replication.html#GUC-WAL-SENDER-TIMEOUT","title":"","level":0},{"type":"text","content":"wal_sender_timeout","level":1},{"type":"link_close","level":0},{"type":"text","content":": Specifies the timeout for inactive WAL sender processes.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"ordered_list_close","level":0},{"type":"heading_open","hLevel":3,"lines":[116,117],"level":0},{"type":"inline","content":"[Improved Disk Management](#improved-disk-management)","level":1,"lines":[116,117],"children":[{"type":"text","content":"Improved Disk Management","level":0}],"lvl":3,"i":5,"seen":0,"slug":"improved-disk-management"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[118,119],"level":0},{"type":"inline","content":"Supabase now provides granular control over disk usage for your Postgres database:","level":1,"lines":[118,119],"children":[{"type":"text","content":"Supabase now provides granular control over disk usage for your Postgres database:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[120,127],"level":0},{"type":"inline","content":"\u003cImg\n  alt=\"Disk management\"\n  src={{\n    light: '/images/blog/clickhouse-supabase/disk-management.png',\n    dark: '/images/blog/clickhouse-supabase/disk-management.png',\n  }}\n/\u003e","level":1,"lines":[120,127],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"Disk management\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src={{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"light: '/images/blog/clickhouse-supabase/disk-management.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"dark: '/images/blog/clickhouse-supabase/disk-management.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"}}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[128,129],"level":0},{"type":"inline","content":"This is driven directly by customers using tools like PeerDB. With adjustable WAL configuration, it's important that developers can manage the disk as well. For example, on the Pro Plan's 8GB disk, you can configure your project with options like:","level":1,"lines":[128,129],"children":[{"type":"text","content":"This is driven directly by customers using tools like PeerDB. With adjustable WAL configuration, it's important that developers can manage the disk as well. For example, on the Pro Plan's 8GB disk, you can configure your project with options like:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[130,133],"level":0},{"type":"list_item_open","lines":[130,131],"level":1},{"type":"paragraph_open","tight":true,"lines":[130,131],"level":2},{"type":"inline","content":"Default: 7GB database space, 1GB Write Ahead Log","level":3,"lines":[130,131],"children":[{"type":"text","content":"Default: 7GB database space, 1GB Write Ahead Log","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[131,133],"level":1},{"type":"paragraph_open","tight":true,"lines":[131,132],"level":2},{"type":"inline","content":"Custom example: 6GB database space, 2 GB Write Ahead Log","level":3,"lines":[131,132],"children":[{"type":"text","content":"Custom example: 6GB database space, 2 GB Write Ahead Log","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[133,134],"level":0},{"type":"inline","content":"Additionally, we're introducing High-performance Disks. We'll release more details about this later.","level":1,"lines":[133,134],"children":[{"type":"text","content":"Additionally, we're introducing High-performance Disks. We'll release more details about this later.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[135,136],"level":0},{"type":"inline","content":"[ClickHouse platform updates](#clickhouse-platform-updates)","level":1,"lines":[135,136],"children":[{"type":"text","content":"ClickHouse platform updates","level":0}],"lvl":3,"i":6,"seen":0,"slug":"clickhouse-platform-updates"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[137,138],"level":0},{"type":"inline","content":"The ClickHouse team have also been busy. They've released a number of updates to their platform, including:","level":1,"lines":[137,138],"children":[{"type":"text","content":"The ClickHouse team have also been busy. They've released a number of updates to their platform, including:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"ordered_list_open","order":1,"lines":[139,142],"level":0},{"type":"list_item_open","lines":[139,140],"level":1},{"type":"paragraph_open","tight":true,"lines":[139,140],"level":2},{"type":"inline","content":"A native Supabase OAuth integration in PeerDB for Postgres CDC to ClickHouse.","level":3,"lines":[139,140],"children":[{"type":"text","content":"A native Supabase OAuth integration in PeerDB for Postgres CDC to ClickHouse.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[140,142],"level":1},{"type":"paragraph_open","tight":true,"lines":[140,141],"level":2},{"type":"inline","content":"Support for IPV6 in PeerDB Cloud.","level":3,"lines":[140,141],"children":[{"type":"text","content":"Support for IPV6 in PeerDB Cloud.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"ordered_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[142,143],"level":0},{"type":"inline","content":"You can learn more about these features in the [Supabase Partnership](https://clickhouse.com/blog/supabase-partnership-native-postgres-replication-clickhouse-fdw) post they released today.","level":1,"lines":[142,143],"children":[{"type":"text","content":"You can learn more about these features in the ","level":0},{"type":"link_open","href":"https://clickhouse.com/blog/supabase-partnership-native-postgres-replication-clickhouse-fdw","title":"","level":0},{"type":"text","content":"Supabase Partnership","level":1},{"type":"link_close","level":0},{"type":"text","content":" post they released today.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[144,145],"level":0},{"type":"inline","content":"[What's next?](#whats-next)","level":1,"lines":[144,145],"children":[{"type":"text","content":"What's next?","level":0}],"lvl":2,"i":7,"seen":0,"slug":"whats-next"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[146,147],"level":0},{"type":"inline","content":"Improving the experience between Postgres and ClickHouse is the first phase of this partnership. We're already working on native platform integrations. If you're using (or plan to use) Supabase and ClickHouse together please [reach out](https://supabase.com/enterprise) - we'd love more design partners to help shape the future of this integration.","level":1,"lines":[146,147],"children":[{"type":"text","content":"Improving the experience between Postgres and ClickHouse is the first phase of this partnership. We're already working on native platform integrations. If you're using (or plan to use) Supabase and ClickHouse together please ","level":0},{"type":"link_open","href":"https://supabase.com/enterprise","title":"","level":0},{"type":"text","content":"reach out","level":1},{"type":"link_close","level":0},{"type":"text","content":" - we'd love more design partners to help shape the future of this integration.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[148,155],"level":0},{"type":"inline","content":"\u003cImg\n  alt=\"Supabase integrations\"\n  src={{\n    light: '/images/blog/clickhouse-supabase/integrations.png',\n    dark: '/images/blog/clickhouse-supabase/integrations.png',\n  }}\n/\u003e","level":1,"lines":[148,155],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"Supabase integrations\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src={{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"light: '/images/blog/clickhouse-supabase/integrations.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"dark: '/images/blog/clickhouse-supabase/integrations.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"}}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[156,157],"level":0},{"type":"inline","content":"If you simply want to try out the tools and updates we've described above, you can get started with all of them, free of charge:","level":1,"lines":[156,157],"children":[{"type":"text","content":"If you simply want to try out the tools and updates we've described above, you can get started with all of them, free of charge:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[158,161],"level":0},{"type":"list_item_open","lines":[158,159],"level":1},{"type":"paragraph_open","tight":true,"lines":[158,159],"level":2},{"type":"inline","content":"Supabase: [database.new](https://database.new)","level":3,"lines":[158,159],"children":[{"type":"text","content":"Supabase: ","level":0},{"type":"link_open","href":"https://database.new","title":"","level":0},{"type":"text","content":"database.new","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[159,160],"level":1},{"type":"paragraph_open","tight":true,"lines":[159,160],"level":2},{"type":"inline","content":"ClickHouse: [clickhouse.com](https://clickhouse.com/)","level":3,"lines":[159,160],"children":[{"type":"text","content":"ClickHouse: ","level":0},{"type":"link_open","href":"https://clickhouse.com/","title":"","level":0},{"type":"text","content":"clickhouse.com","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[160,161],"level":1},{"type":"paragraph_open","tight":true,"lines":[160,161],"level":2},{"type":"inline","content":"PeerDB: [peerdb.io](https://www.peerdb.io/)","level":3,"lines":[160,161],"children":[{"type":"text","content":"PeerDB: ","level":0},{"type":"link_open","href":"https://www.peerdb.io/","title":"","level":0},{"type":"text","content":"peerdb.io","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0}],"content":"- [Supabase + ClickHouse Partnership](#supabase--clickhouse-partnership)\n- [Using Postgres and ClickHouse together](#using-postgres-and-clickhouse-together)\n- [Improving the ClickHouse \u0026 Supabase experience](#improving-the-clickhouse--supabase-experience)\n  * [Updated ClickHouse Foreign Data Wrapper](#updated-clickhouse-foreign-data-wrapper)\n  * [More granular replication control](#more-granular-replication-control)\n  * [Improved Disk Management](#improved-disk-management)\n  * [ClickHouse platform updates](#clickhouse-platform-updates)\n- [What's next?](#whats-next)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-clickhouse-partnership"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>