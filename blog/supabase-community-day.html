<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase Community Day</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Community Day" data-next-head=""/><meta property="og:title" content="Supabase Community Day" data-next-head=""/><meta property="og:description" content="Community Day" data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-community-day" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2021-07-26" data-next-head=""/><meta property="article:author" content="https://github.com/steve-chavez" data-next-head=""/><meta property="article:tag" content="launch-week" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/launch-week-sql-day-1-community-day/launch-week-sql-day-1-community-day-og.jpg" data-next-head=""/><meta property="og:image:alt" content="Supabase Community Day thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Supabase Community Day</h1><div class="text-light flex space-x-3 text-sm"><p>26 Jul 2021</p><p>•</p><p>5 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/steve-chavez"><div class="flex items-center gap-3"><div class="w-10"><img alt="Steve Chavez avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fsteve-chavez.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fsteve-chavez.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fsteve-chavez.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Steve Chavez</span><span class="text-foreground-lighter mb-0 text-xs">Engineering &amp; PostgREST maintainer</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Supabase Community Day" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-sql-day-1-community-day%2Flaunch-week-sql-day-1-community-day-thumb.jpg&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-sql-day-1-community-day%2Flaunch-week-sql-day-1-community-day-thumb.jpg&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-sql-day-1-community-day%2Flaunch-week-sql-day-1-community-day-thumb.jpg&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-sql-day-1-community-day%2Flaunch-week-sql-day-1-community-day-thumb.jpg&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-sql-day-1-community-day%2Flaunch-week-sql-day-1-community-day-thumb.jpg&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-sql-day-1-community-day%2Flaunch-week-sql-day-1-community-day-thumb.jpg&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-sql-day-1-community-day%2Flaunch-week-sql-day-1-community-day-thumb.jpg&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-sql-day-1-community-day%2Flaunch-week-sql-day-1-community-day-thumb.jpg&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-sql-day-1-community-day%2Flaunch-week-sql-day-1-community-day-thumb.jpg&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-sql-day-1-community-day%2Flaunch-week-sql-day-1-community-day-thumb.jpg&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-sql-day-1-community-day%2Flaunch-week-sql-day-1-community-day-thumb.jpg&amp;w=3840&amp;q=100"/></div><p>Supabase has grown a lot <sup><a href="#user-content-fn-1" id="user-content-fnref-1" data-footnote-ref="true" aria-describedby="footnote-label">1</a></sup> since last <a href="https://supabase.com/blog/launch-week">Launch Week</a>,
but it wouldn&#x27;t be possible without some amazing open source tools.
Since we&#x27;re shipping a few upgrades this week we feel it&#x27;s only fair
to shine a spotlight on some tools and community efforts that make Supabase possible.</p>
<h2 id="postgresql-version-133" class="group scroll-mt-24">PostgreSQL version 13.3<a href="#postgresql-version-133" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>PostgreSQL is a big part of Supabase, and it&#x27;s also a huge inspiration - the speed that they ship, their community organization,
and their absolute dedication towards reliability. PostgreSQL 13.3 was released in June and (from today) every new Supabase project will
be on <a href="supabase-postgres-13.html">PostgreSQL version 13.3</a>.</p>
<h3 id="giving-back" class="group scroll-mt-24">Giving back<a href="#giving-back" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Supabase makes it easy to get started with Postgres, but we think you should try it even if you don&#x27;t want to use Supabase. Here&#x27;s a
few ways we make it easy to use PostgreSQL without Supabase:</p>
<ul>
<li>Supabase was the first company to put Postgres in the <a href="https://marketplace.digitalocean.com/apps/supabase-postgres">Digital Ocean Marketplace</a>.
Since then it&#x27;s been installed over 1000 times (not by us!).</li>
<li>We provide a Postgres image in the <a href="https://aws.amazon.com/marketplace/pp/prodview-lk2pfa5nafecg">AWS marketplace</a>.</li>
<li>We&#x27;re packaging the Marketplace version, <a href="supabase-postgres-13.html#postgresql-bundles">along with &quot;Bundles&quot;</a></li>
<li>We&#x27;re developing a few extensions: <a href="https://github.com/supabase/supautils">supautils</a>, <a href="https://github.com/supabase/pg_net">pg_net</a>, and a couple more soon to be announced.</li>
</ul>
<h3 id="get-involved" class="group scroll-mt-24">Get involved<a href="#get-involved" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<ul>
<li>Follow the official <a href="https://twitter.com/PostgreSQL">PostgreSQL Twitter</a> account.</li>
<li>Join the official <a href="https://www.postgresql.org/community/">mailing lists</a>.</li>
</ul>
<h2 id="postgrest-version-80" class="group scroll-mt-24">PostgREST version 8.0<a href="#postgrest-version-80" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>This new PostgREST stable version comes with the improvements we&#x27;ve made to make it truly enterprise-grade. Due to our commitment with OSS,
all of our improvements are upstreamed, so you can fully use them on your own self-hosted projects.</p>
<h3 id="highlights" class="group scroll-mt-24">Highlights<a href="#highlights" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p><strong>Improved Performance</strong>: Due to Supabase&#x27;s high throughput requirements, PostgREST 8.0 handles up to 50% more throughput on
GET requests, according to our benchmarks. We converted all SELECT queries to use prepared statements and reduced logging
verbosity to make this possible.</p>
<p><strong>Reduced downtime</strong>: we need PostgREST to be more &quot;set it and forget it&quot;, so reloading schema cache now has zero downtime.</p>
<p><strong>Dynamic Configuration</strong>: we&#x27;ve made it easier to handle PostgREST configuration at scale (we manage thousands of PostgREST instances).
This new version includes the ability to use an in-database configuration that is reloadable through a <code class="short-inline-codeblock">NOTIFY</code> command.</p>
<p><strong>Less admin burden</strong>: PostgREST previously required the <code class="short-inline-codeblock">pg_listen</code> utility to reload its schema cache. This is no longer needed.
The schema cache is reloadable with a simple <code class="short-inline-codeblock">NOTIFY</code> command.</p>
<p><strong>Better diagnostic information</strong>: in the rare cases where PostgREST fails, we want to find the exact root cause quickly.
For this we&#x27;ve improved its logging by adding logging levels and timestamps to all server errors.</p>
<p><strong>Simpler OpenAPI</strong>: showing a complete OpenAPI output used to require a highly-privileged <code class="short-inline-codeblock">anon</code> role. With the new <code class="short-inline-codeblock">openapi-mode</code>,
this is no longer needed and <code class="short-inline-codeblock">anon</code> can be kept with minimal privileges.</p>
<p>The community has made many more enhancements and bug fixes for the new version. See
<a href="https://github.com/PostgREST/postgrest/releases/tag/v8.0.0">v8.0 CHANGELOG</a> for the full list.</p>
<h3 id="get-started" class="group scroll-mt-24">Get Started<a href="#get-started" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Want to get started with PostgREST? The Supabase community has built a number of client libraries to make it simpler to use:</p>
<ul>
<li>Javascript: <a href="https://github.com/supabase/postgrest-js">postgrest-js</a></li>
<li>Rust: <a href="https://github.com/supabase/postgrest-rs">postgrest-rs</a></li>
<li>Go: <a href="https://github.com/supabase/postgrest-go">postgrest-go</a></li>
<li>Python: <a href="https://github.com/supabase/postgrest-py">postgrest-py</a></li>
<li>Dart: <a href="https://github.com/supabase/postgrest-dart">postgrest-dart</a></li>
<li>C#: <a href="https://github.com/supabase/postgrest-csharp">postgrest-csharp</a></li>
<li>Swift: <a href="https://github.com/supabase/postgrest-swift">postgrest-swift</a></li>
<li>Ruby: <a href="https://github.com/supabase/postgrest-rb">postgrest-rb</a></li>
<li>Kotlin: <a href="https://github.com/supabase/postgrest-kt">postgrest-kt</a></li>
</ul>
<h3 id="get-involved-1" class="group scroll-mt-24">Get involved<a href="#get-involved-1" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<ul>
<li>Help with <a href="https://github.com/PostgREST/postgrest/blob/main/.github/CONTRIBUTING.md">PostgREST development</a>.</li>
<li>Help with <a href="https://github.com/PostgREST/postgrest-docs/issues/393">PostgREST docs translations</a>.</li>
<li>Follow the official <a href="https://twitter.com/postgrest_org">PostgREST Twitter</a> account.</li>
</ul>
<h2 id="supabase-flutterdart-beta-release" class="group scroll-mt-24">Supabase Flutter/Dart (Beta release)<a href="#supabase-flutterdart-beta-release" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>While the Supabase team have been busy with the Javascript libraries, the community have been beavering away with Dart. They&#x27;ve even
<a href="https://www.producthunt.com/posts/spot-2d300f54-7a0a-4dbf-aee2-4a75311217cc">built fully-functional apps</a> using Supabase Auth and Storage.</p>
<p>Today the Community are releasing both the Flutter and Dart libraries in Beta (with a bit of help from the Supabase team).</p>
<h3 id="get-started-1" class="group scroll-mt-24">Get started<a href="#get-started-1" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<ul>
<li>Check out the <a href="https://supabase.com/docs/guides/with-flutter">Flutter Quickstart Guide</a></li>
<li>Check out the code:<!-- -->
<ul>
<li>Supabase Flutter: <a href="https://github.com/supabase/supabase-flutter/tree/main/packages/supabase_flutter">GitHub</a> | <a href="https://pub.dev/packages/supabase_flutter">Release</a></li>
<li>Supabase Dart: <a href="https://github.com/supabase/supabase-flutter/tree/main/packages/supabase">GitHub</a> | <a href="https://pub.dev/packages/supabase">Release</a></li>
</ul>
</li>
</ul>
<h3 id="get-involved-2" class="group scroll-mt-24">Get involved<a href="#get-involved-2" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<ul>
<li>Help <a href="../open-source/contributing/supasquad.html#author">write</a> the Flutter Docs.</li>
<li>Help <a href="../open-source/contributing/supasquad.html#maintainer">maintain</a> the <a href="https://github.com/supabase/supabase-flutter/tree/main/packages/supabase_flutter">Flutter</a>
and <a href="https://github.com/supabase/supabase-flutter/tree/main/packages/supabase">Dart</a> libraries.</li>
</ul>
<h2 id="supabase-discord" class="group scroll-mt-24">Supabase Discord<a href="#supabase-discord" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>OK, OK, we get it - GitHub <a href="https://github.com/supabase/supabase/discussions">Discussions</a> aren&#x27;t enough for y&#x27;all. While we&#x27;ve been trying
to keep the conversation contained to our GitHub org, the community has been creating <a href="https://www.reddit.com/r/Supabase/">subreddits</a>,
StackOverflow <a href="https://stackoverflow.com/questions/tagged/supabase">tags</a>, and GitHub <a href="https://github.com/topics/supabase">topics</a>.</p>
<p>A few weeks ago one of the community created a Community-led Discord, and so the team figured we might as well join the fun.</p>
<p>We&#x27;ll still be using Discussions for debugging, but if you&#x27;re looking for a place to hang out with Supabase developers, Discord is where to find it.</p>
<h3 id="get-involved-3" class="group scroll-mt-24">Get involved<a href="#get-involved-3" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Join the Supabase Community Discord: <a href="http://discord.supabase.com">discord.supabase.com</a>, say hi, and start building.</p>
<section data-footnotes="true" class="footnotes"><h2 id="footnote-label" class="sr-only">Footnotes<a href="#footnote-label" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ol>
<li id="user-content-fn-1">
<p>Supabase has been one of the fastest growing startups <a href="https://github.com/supabase/supabase">on GitHub</a> for four consecutive quarters:
207% in <a href="https://runacap.com/ross-index/q3-2020/">Q3</a> 2020; 1,373% in <a href="https://runacap.com/ross-index/q4-2020/">Q4</a> 2020;
462% in <a href="https://runacap.com/ross-index/q1-2021/">Q1</a> 2021; 1,653% in <a href="https://runacap.com/ross-index/q2-2021/">Q2</a> 2021. <a href="#user-content-fnref-1" data-footnote-backref="true" class="data-footnote-backref" aria-label="Back to content">↩</a></p>
</li>
</ol>
</section></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-community-day&amp;text=Supabase%20Community%20Day"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-community-day&amp;text=Supabase%20Community%20Day"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-community-day&amp;t=Supabase%20Community%20Day"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="../customers/epsilon3.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Epsilon3 Self-Host Supabase To Revolutionize Space Operations </h4><p class="small">26 July 2021</p></div></div></div></div></a></div><div><a href="supabase-postgres-13.html"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Supabase is now on Postgres 13.3</h4><p class="small">26 July 2021</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/launch-week"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">launch-week</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#postgresql-version-133">PostgreSQL version 13.3</a></li>
<li><a href="#postgrest-version-80">PostgREST version 8.0</a></li>
<li><a href="#supabase-flutterdart-beta-release">Supabase Flutter/Dart (Beta release)</a></li>
<li><a href="#supabase-discord">Supabase Discord</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-community-day&amp;text=Supabase%20Community%20Day"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-community-day&amp;text=Supabase%20Community%20Day"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-community-day&amp;t=Supabase%20Community%20Day"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"epsilon3-self-hosting","title":"Epsilon3 Self-Host Supabase To Revolutionize Space Operations ","description":"Learn how the team at Epsilon3 use Supabase to help teams execute secure and reliable operations in an industry that project spend runs into the billions.","author":"rory_wilding","author_url":"https://github.com/roryw10","author_image_url":"https://github.com/roryw10.png","image":"epsilon3/og-epsilon3.jpg","thumb":"epsilon3/cover-epsilon3.jpg","categories":["company"],"tags":["supabase"],"date":"2021-07-26","toc_depth":2,"formattedDate":"26 July 2021","readingTime":"4 minute read","url":"/blog/epsilon3-self-hosting","path":"/blog/epsilon3-self-hosting"},"nextPost":{"slug":"supabase-postgres-13","title":"Supabase is now on Postgres 13.3","description":"From today, new Supabase projects will be on a version of Supabase Postgres that runs on Postgres 13.3.","author":"angelico_de_los_reyes","author_url":"https://github.com/dragarcia","author_image_url":"https://github.com/dragarcia.png","image":"pg13/postgres-13-og.jpg","thumb":"pg13/postgres-13-thumb.jpg","categories":["postgres"],"tags":["launch-week","database"],"date":"2021-07-26","toc_depth":2,"formattedDate":"26 July 2021","readingTime":"5 minute read","url":"/blog/supabase-postgres-13","path":"/blog/supabase-postgres-13"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-community-day","source":"\nSupabase has grown a lot [^1] since last [Launch Week](/blog/launch-week),\nbut it wouldn't be possible without some amazing open source tools.\nSince we're shipping a few upgrades this week we feel it's only fair\nto shine a spotlight on some tools and community efforts that make Supabase possible.\n\n## PostgreSQL version 13.3\n\n![Postgres version 13 released](/images/blog/launch-week-sql-day-1-community-day/launch-week-sql-day-1-community-day-postgres-upgrade.jpg)\n\nPostgreSQL is a big part of Supabase, and it's also a huge inspiration - the speed that they ship, their community organization,\nand their absolute dedication towards reliability. PostgreSQL 13.3 was released in June and (from today) every new Supabase project will\nbe on [PostgreSQL version 13.3](/blog/supabase-postgres-13).\n\n### Giving back\n\nSupabase makes it easy to get started with Postgres, but we think you should try it even if you don't want to use Supabase. Here's a\nfew ways we make it easy to use PostgreSQL without Supabase:\n\n- Supabase was the first company to put Postgres in the [Digital Ocean Marketplace](https://marketplace.digitalocean.com/apps/supabase-postgres).\n  Since then it's been installed over 1000 times (not by us!).\n- We provide a Postgres image in the [AWS marketplace](https://aws.amazon.com/marketplace/pp/prodview-lk2pfa5nafecg).\n- We're packaging the Marketplace version, [along with \"Bundles\"](/blog/supabase-postgres-13#postgresql-bundles)\n- We're developing a few extensions: [supautils](https://github.com/supabase/supautils), [pg_net](https://github.com/supabase/pg_net), and a couple more soon to be announced.\n\n### Get involved\n\n- Follow the official [PostgreSQL Twitter](https://twitter.com/PostgreSQL) account.\n- Join the official [mailing lists](https://www.postgresql.org/community/).\n\n## PostgREST version 8.0\n\n![PostgREST version 8.0 released](/images/blog/launch-week-sql-day-1-community-day/launch-week-sql-day-1-community-day-postgrest.jpg)\n\nThis new PostgREST stable version comes with the improvements we've made to make it truly enterprise-grade. Due to our commitment with OSS,\nall of our improvements are upstreamed, so you can fully use them on your own self-hosted projects.\n\n### Highlights\n\n**Improved Performance**: Due to Supabase's high throughput requirements, PostgREST 8.0 handles up to 50% more throughput on\nGET requests, according to our benchmarks. We converted all SELECT queries to use prepared statements and reduced logging\nverbosity to make this possible.\n\n**Reduced downtime**: we need PostgREST to be more \"set it and forget it\", so reloading schema cache now has zero downtime.\n\n**Dynamic Configuration**: we've made it easier to handle PostgREST configuration at scale (we manage thousands of PostgREST instances).\nThis new version includes the ability to use an in-database configuration that is reloadable through a `NOTIFY` command.\n\n**Less admin burden**: PostgREST previously required the `pg_listen` utility to reload its schema cache. This is no longer needed.\nThe schema cache is reloadable with a simple `NOTIFY` command.\n\n**Better diagnostic information**: in the rare cases where PostgREST fails, we want to find the exact root cause quickly.\nFor this we've improved its logging by adding logging levels and timestamps to all server errors.\n\n**Simpler OpenAPI**: showing a complete OpenAPI output used to require a highly-privileged `anon` role. With the new `openapi-mode`,\nthis is no longer needed and `anon` can be kept with minimal privileges.\n\nThe community has made many more enhancements and bug fixes for the new version. See\n[v8.0 CHANGELOG](https://github.com/PostgREST/postgrest/releases/tag/v8.0.0) for the full list.\n\n### Get Started\n\nWant to get started with PostgREST? The Supabase community has built a number of client libraries to make it simpler to use:\n\n- Javascript: [postgrest-js](https://github.com/supabase/postgrest-js)\n- Rust: [postgrest-rs](https://github.com/supabase/postgrest-rs)\n- Go: [postgrest-go](https://github.com/supabase/postgrest-go)\n- Python: [postgrest-py](https://github.com/supabase/postgrest-py)\n- Dart: [postgrest-dart](https://github.com/supabase/postgrest-dart)\n- C#: [postgrest-csharp](https://github.com/supabase/postgrest-csharp)\n- Swift: [postgrest-swift](https://github.com/supabase/postgrest-swift)\n- Ruby: [postgrest-rb](https://github.com/supabase/postgrest-rb)\n- Kotlin: [postgrest-kt](https://github.com/supabase/postgrest-kt)\n\n### Get involved\n\n- Help with [PostgREST development](https://github.com/PostgREST/postgrest/blob/main/.github/CONTRIBUTING.md).\n- Help with [PostgREST docs translations](https://github.com/PostgREST/postgrest-docs/issues/393).\n- Follow the official [PostgREST Twitter](https://twitter.com/postgrest_org) account.\n\n## Supabase Flutter/Dart (Beta release)\n\n![Supabase Flutter Beta release](/images/blog/launch-week-sql-day-1-community-day/launch-week-sql-day-1-community-day-flutter-library.jpg)\n\nWhile the Supabase team have been busy with the Javascript libraries, the community have been beavering away with Dart. They've even\n[built fully-functional apps](https://www.producthunt.com/posts/spot-2d300f54-7a0a-4dbf-aee2-4a75311217cc) using Supabase Auth and Storage.\n\nToday the Community are releasing both the Flutter and Dart libraries in Beta (with a bit of help from the Supabase team).\n\n### Get started\n\n- Check out the [Flutter Quickstart Guide](/docs/guides/with-flutter)\n- Check out the code:\n  - Supabase Flutter: [GitHub](https://github.com/supabase/supabase-flutter/tree/main/packages/supabase_flutter) | [Release](https://pub.dev/packages/supabase_flutter)\n  - Supabase Dart: [GitHub](https://github.com/supabase/supabase-flutter/tree/main/packages/supabase) | [Release](https://pub.dev/packages/supabase)\n\n### Get involved\n\n- Help [write](/supasquad#author) the Flutter Docs.\n- Help [maintain](/supasquad#maintainer) the [Flutter](https://github.com/supabase/supabase-flutter/tree/main/packages/supabase_flutter)\n  and [Dart](https://github.com/supabase/supabase-flutter/tree/main/packages/supabase) libraries.\n\n## Supabase Discord\n\n![Supabase Discord](/images/blog/launch-week-sql-day-1-community-day/launch-week-sql-day-1-community-day-discord-server.jpg)\n\nOK, OK, we get it - GitHub [Discussions](https://github.com/supabase/supabase/discussions) aren't enough for y'all. While we've been trying\nto keep the conversation contained to our GitHub org, the community has been creating [subreddits](https://www.reddit.com/r/Supabase/),\nStackOverflow [tags](https://stackoverflow.com/questions/tagged/supabase), and GitHub [topics](https://github.com/topics/supabase).\n\nA few weeks ago one of the community created a Community-led Discord, and so the team figured we might as well join the fun.\n\nWe'll still be using Discussions for debugging, but if you're looking for a place to hang out with Supabase developers, Discord is where to find it.\n\n### Get involved\n\nJoin the Supabase Community Discord: [discord.supabase.com](http://discord.supabase.com), say hi, and start building.\n\n[^1]:\n    Supabase has been one of the fastest growing startups [on GitHub](https://github.com/supabase/supabase) for four consecutive quarters:\n    207% in [Q3](https://runacap.com/ross-index/q3-2020/) 2020; 1,373% in [Q4](https://runacap.com/ross-index/q4-2020/) 2020;\n    462% in [Q1](https://runacap.com/ross-index/q1-2021/) 2021; 1,653% in [Q2](https://runacap.com/ross-index/q2-2021/) 2021.\n","title":"Supabase Community Day","description":"Community Day","author":"steve_chavez","author_url":"https://github.com/steve-chavez","author_image_url":"https://github.com/steve-chavez.png","thumb":"launch-week-sql-day-1-community-day/launch-week-sql-day-1-community-day-thumb.jpg","image":"launch-week-sql-day-1-community-day/launch-week-sql-day-1-community-day-og.jpg","categories":["developers"],"tags":["launch-week"],"date":"2021-07-26","toc_depth":2,"content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    sup: \"sup\",\n    a: \"a\",\n    h2: \"h2\",\n    img: \"img\",\n    h3: \"h3\",\n    ul: \"ul\",\n    li: \"li\",\n    strong: \"strong\",\n    code: \"code\",\n    section: \"section\",\n    ol: \"ol\"\n  }, _provideComponents(), props.components);\n  return _jsxs(_Fragment, {\n    children: [_jsxs(_components.p, {\n      children: [\"Supabase has grown a lot \", _jsx(_components.sup, {\n        children: _jsx(_components.a, {\n          href: \"#user-content-fn-1\",\n          id: \"user-content-fnref-1\",\n          \"data-footnote-ref\": true,\n          \"aria-describedby\": \"footnote-label\",\n          children: \"1\"\n        })\n      }), \" since last \", _jsx(_components.a, {\n        href: \"/blog/launch-week\",\n        children: \"Launch Week\"\n      }), \",\\nbut it wouldn't be possible without some amazing open source tools.\\nSince we're shipping a few upgrades this week we feel it's only fair\\nto shine a spotlight on some tools and community efforts that make Supabase possible.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"postgresql-version-133\",\n      children: \"PostgreSQL version 13.3\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-sql-day-1-community-day/launch-week-sql-day-1-community-day-postgres-upgrade.jpg\",\n        alt: \"Postgres version 13 released\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"PostgreSQL is a big part of Supabase, and it's also a huge inspiration - the speed that they ship, their community organization,\\nand their absolute dedication towards reliability. PostgreSQL 13.3 was released in June and (from today) every new Supabase project will\\nbe on \", _jsx(_components.a, {\n        href: \"/blog/supabase-postgres-13\",\n        children: \"PostgreSQL version 13.3\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"giving-back\",\n      children: \"Giving back\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Supabase makes it easy to get started with Postgres, but we think you should try it even if you don't want to use Supabase. Here's a\\nfew ways we make it easy to use PostgreSQL without Supabase:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Supabase was the first company to put Postgres in the \", _jsx(_components.a, {\n          href: \"https://marketplace.digitalocean.com/apps/supabase-postgres\",\n          children: \"Digital Ocean Marketplace\"\n        }), \".\\nSince then it's been installed over 1000 times (not by us!).\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"We provide a Postgres image in the \", _jsx(_components.a, {\n          href: \"https://aws.amazon.com/marketplace/pp/prodview-lk2pfa5nafecg\",\n          children: \"AWS marketplace\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"We're packaging the Marketplace version, \", _jsx(_components.a, {\n          href: \"/blog/supabase-postgres-13#postgresql-bundles\",\n          children: \"along with \\\"Bundles\\\"\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"We're developing a few extensions: \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/supautils\",\n          children: \"supautils\"\n        }), \", \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/pg_net\",\n          children: \"pg_net\"\n        }), \", and a couple more soon to be announced.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"get-involved\",\n      children: \"Get involved\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Follow the official \", _jsx(_components.a, {\n          href: \"https://twitter.com/PostgreSQL\",\n          children: \"PostgreSQL Twitter\"\n        }), \" account.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Join the official \", _jsx(_components.a, {\n          href: \"https://www.postgresql.org/community/\",\n          children: \"mailing lists\"\n        }), \".\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"postgrest-version-80\",\n      children: \"PostgREST version 8.0\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-sql-day-1-community-day/launch-week-sql-day-1-community-day-postgrest.jpg\",\n        alt: \"PostgREST version 8.0 released\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This new PostgREST stable version comes with the improvements we've made to make it truly enterprise-grade. Due to our commitment with OSS,\\nall of our improvements are upstreamed, so you can fully use them on your own self-hosted projects.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"highlights\",\n      children: \"Highlights\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.strong, {\n        children: \"Improved Performance\"\n      }), \": Due to Supabase's high throughput requirements, PostgREST 8.0 handles up to 50% more throughput on\\nGET requests, according to our benchmarks. We converted all SELECT queries to use prepared statements and reduced logging\\nverbosity to make this possible.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.strong, {\n        children: \"Reduced downtime\"\n      }), \": we need PostgREST to be more \\\"set it and forget it\\\", so reloading schema cache now has zero downtime.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.strong, {\n        children: \"Dynamic Configuration\"\n      }), \": we've made it easier to handle PostgREST configuration at scale (we manage thousands of PostgREST instances).\\nThis new version includes the ability to use an in-database configuration that is reloadable through a \", _jsx(_components.code, {\n        children: \"NOTIFY\"\n      }), \" command.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.strong, {\n        children: \"Less admin burden\"\n      }), \": PostgREST previously required the \", _jsx(_components.code, {\n        children: \"pg_listen\"\n      }), \" utility to reload its schema cache. This is no longer needed.\\nThe schema cache is reloadable with a simple \", _jsx(_components.code, {\n        children: \"NOTIFY\"\n      }), \" command.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.strong, {\n        children: \"Better diagnostic information\"\n      }), \": in the rare cases where PostgREST fails, we want to find the exact root cause quickly.\\nFor this we've improved its logging by adding logging levels and timestamps to all server errors.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.strong, {\n        children: \"Simpler OpenAPI\"\n      }), \": showing a complete OpenAPI output used to require a highly-privileged \", _jsx(_components.code, {\n        children: \"anon\"\n      }), \" role. With the new \", _jsx(_components.code, {\n        children: \"openapi-mode\"\n      }), \",\\nthis is no longer needed and \", _jsx(_components.code, {\n        children: \"anon\"\n      }), \" can be kept with minimal privileges.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The community has made many more enhancements and bug fixes for the new version. See\\n\", _jsx(_components.a, {\n        href: \"https://github.com/PostgREST/postgrest/releases/tag/v8.0.0\",\n        children: \"v8.0 CHANGELOG\"\n      }), \" for the full list.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"get-started\",\n      children: \"Get Started\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Want to get started with PostgREST? The Supabase community has built a number of client libraries to make it simpler to use:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Javascript: \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/postgrest-js\",\n          children: \"postgrest-js\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Rust: \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/postgrest-rs\",\n          children: \"postgrest-rs\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Go: \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/postgrest-go\",\n          children: \"postgrest-go\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Python: \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/postgrest-py\",\n          children: \"postgrest-py\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Dart: \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/postgrest-dart\",\n          children: \"postgrest-dart\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"C#: \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/postgrest-csharp\",\n          children: \"postgrest-csharp\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Swift: \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/postgrest-swift\",\n          children: \"postgrest-swift\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Ruby: \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/postgrest-rb\",\n          children: \"postgrest-rb\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Kotlin: \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/postgrest-kt\",\n          children: \"postgrest-kt\"\n        })]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"get-involved-1\",\n      children: \"Get involved\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Help with \", _jsx(_components.a, {\n          href: \"https://github.com/PostgREST/postgrest/blob/main/.github/CONTRIBUTING.md\",\n          children: \"PostgREST development\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Help with \", _jsx(_components.a, {\n          href: \"https://github.com/PostgREST/postgrest-docs/issues/393\",\n          children: \"PostgREST docs translations\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Follow the official \", _jsx(_components.a, {\n          href: \"https://twitter.com/postgrest_org\",\n          children: \"PostgREST Twitter\"\n        }), \" account.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"supabase-flutterdart-beta-release\",\n      children: \"Supabase Flutter/Dart (Beta release)\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-sql-day-1-community-day/launch-week-sql-day-1-community-day-flutter-library.jpg\",\n        alt: \"Supabase Flutter Beta release\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"While the Supabase team have been busy with the Javascript libraries, the community have been beavering away with Dart. They've even\\n\", _jsx(_components.a, {\n        href: \"https://www.producthunt.com/posts/spot-2d300f54-7a0a-4dbf-aee2-4a75311217cc\",\n        children: \"built fully-functional apps\"\n      }), \" using Supabase Auth and Storage.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Today the Community are releasing both the Flutter and Dart libraries in Beta (with a bit of help from the Supabase team).\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"get-started-1\",\n      children: \"Get started\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Check out the \", _jsx(_components.a, {\n          href: \"/docs/guides/with-flutter\",\n          children: \"Flutter Quickstart Guide\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Check out the code:\", \"\\n\", _jsxs(_components.ul, {\n          children: [\"\\n\", _jsxs(_components.li, {\n            children: [\"Supabase Flutter: \", _jsx(_components.a, {\n              href: \"https://github.com/supabase/supabase-flutter/tree/main/packages/supabase_flutter\",\n              children: \"GitHub\"\n            }), \" | \", _jsx(_components.a, {\n              href: \"https://pub.dev/packages/supabase_flutter\",\n              children: \"Release\"\n            })]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"Supabase Dart: \", _jsx(_components.a, {\n              href: \"https://github.com/supabase/supabase-flutter/tree/main/packages/supabase\",\n              children: \"GitHub\"\n            }), \" | \", _jsx(_components.a, {\n              href: \"https://pub.dev/packages/supabase\",\n              children: \"Release\"\n            })]\n          }), \"\\n\"]\n        }), \"\\n\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"get-involved-2\",\n      children: \"Get involved\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Help \", _jsx(_components.a, {\n          href: \"/supasquad#author\",\n          children: \"write\"\n        }), \" the Flutter Docs.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Help \", _jsx(_components.a, {\n          href: \"/supasquad#maintainer\",\n          children: \"maintain\"\n        }), \" the \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase-flutter/tree/main/packages/supabase_flutter\",\n          children: \"Flutter\"\n        }), \"\\nand \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase-flutter/tree/main/packages/supabase\",\n          children: \"Dart\"\n        }), \" libraries.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"supabase-discord\",\n      children: \"Supabase Discord\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-sql-day-1-community-day/launch-week-sql-day-1-community-day-discord-server.jpg\",\n        alt: \"Supabase Discord\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"OK, OK, we get it - GitHub \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/supabase/discussions\",\n        children: \"Discussions\"\n      }), \" aren't enough for y'all. While we've been trying\\nto keep the conversation contained to our GitHub org, the community has been creating \", _jsx(_components.a, {\n        href: \"https://www.reddit.com/r/Supabase/\",\n        children: \"subreddits\"\n      }), \",\\nStackOverflow \", _jsx(_components.a, {\n        href: \"https://stackoverflow.com/questions/tagged/supabase\",\n        children: \"tags\"\n      }), \", and GitHub \", _jsx(_components.a, {\n        href: \"https://github.com/topics/supabase\",\n        children: \"topics\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"A few weeks ago one of the community created a Community-led Discord, and so the team figured we might as well join the fun.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We'll still be using Discussions for debugging, but if you're looking for a place to hang out with Supabase developers, Discord is where to find it.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"get-involved-3\",\n      children: \"Get involved\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Join the Supabase Community Discord: \", _jsx(_components.a, {\n        href: \"http://discord.supabase.com\",\n        children: \"discord.supabase.com\"\n      }), \", say hi, and start building.\"]\n    }), \"\\n\", _jsxs(_components.section, {\n      \"data-footnotes\": true,\n      className: \"footnotes\",\n      children: [_jsx(_components.h2, {\n        className: \"sr-only\",\n        id: \"footnote-label\",\n        children: \"Footnotes\"\n      }), \"\\n\", _jsxs(_components.ol, {\n        children: [\"\\n\", _jsxs(_components.li, {\n          id: \"user-content-fn-1\",\n          children: [\"\\n\", _jsxs(_components.p, {\n            children: [\"Supabase has been one of the fastest growing startups \", _jsx(_components.a, {\n              href: \"https://github.com/supabase/supabase\",\n              children: \"on GitHub\"\n            }), \" for four consecutive quarters:\\n207% in \", _jsx(_components.a, {\n              href: \"https://runacap.com/ross-index/q3-2020/\",\n              children: \"Q3\"\n            }), \" 2020; 1,373% in \", _jsx(_components.a, {\n              href: \"https://runacap.com/ross-index/q4-2020/\",\n              children: \"Q4\"\n            }), \" 2020;\\n462% in \", _jsx(_components.a, {\n              href: \"https://runacap.com/ross-index/q1-2021/\",\n              children: \"Q1\"\n            }), \" 2021; 1,653% in \", _jsx(_components.a, {\n              href: \"https://runacap.com/ross-index/q2-2021/\",\n              children: \"Q2\"\n            }), \" 2021. \", _jsx(_components.a, {\n              href: \"#user-content-fnref-1\",\n              \"data-footnote-backref\": true,\n              className: \"data-footnote-backref\",\n              \"aria-label\": \"Back to content\",\n              children: \"↩\"\n            })]\n          }), \"\\n\"]\n        }), \"\\n\"]\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"PostgreSQL version 13.3","slug":"postgresql-version-133","lvl":2,"i":0,"seen":0},{"content":"Giving back","slug":"giving-back","lvl":3,"i":1,"seen":0},{"content":"Get involved","slug":"get-involved","lvl":3,"i":2,"seen":0},{"content":"PostgREST version 8.0","slug":"postgrest-version-80","lvl":2,"i":3,"seen":0},{"content":"Highlights","slug":"highlights","lvl":3,"i":4,"seen":0},{"content":"Get Started","slug":"get-started","lvl":3,"i":5,"seen":0},{"content":"Get involved","slug":"get-involved-1","lvl":3,"i":6,"seen":1},{"content":"Supabase Flutter/Dart (Beta release)","slug":"supabase-flutterdart-beta-release","lvl":2,"i":7,"seen":0},{"content":"Get started","slug":"get-started","lvl":3,"i":8,"seen":0},{"content":"Get involved","slug":"get-involved-2","lvl":3,"i":9,"seen":2},{"content":"Supabase Discord","slug":"supabase-discord","lvl":2,"i":10,"seen":0},{"content":"Get involved","slug":"get-involved-3","lvl":3,"i":11,"seen":3}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,5],"level":0},{"type":"inline","content":"Supabase has grown a lot [^1] since last [Launch Week](/blog/launch-week),\nbut it wouldn't be possible without some amazing open source tools.\nSince we're shipping a few upgrades this week we feel it's only fair\nto shine a spotlight on some tools and community efforts that make Supabase possible.","level":1,"lines":[1,5],"children":[{"type":"text","content":"Supabase has grown a lot ","level":0},{"type":"footnote_ref","id":0,"subId":0,"level":0},{"type":"text","content":" since last ","level":0},{"type":"link_open","href":"/blog/launch-week","title":"","level":0},{"type":"text","content":"Launch Week","level":1},{"type":"link_close","level":0},{"type":"text","content":",","level":0},{"type":"softbreak","level":0},{"type":"text","content":"but it wouldn't be possible without some amazing open source tools.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Since we're shipping a few upgrades this week we feel it's only fair","level":0},{"type":"softbreak","level":0},{"type":"text","content":"to shine a spotlight on some tools and community efforts that make Supabase possible.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[6,7],"level":0},{"type":"inline","content":"[PostgreSQL version 13.3](#postgresql-version-133)","level":1,"lines":[6,7],"children":[{"type":"text","content":"PostgreSQL version 13.3","level":0}],"lvl":2,"i":0,"seen":0,"slug":"postgresql-version-133"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[8,9],"level":0},{"type":"inline","content":"![Postgres version 13 released](/images/blog/launch-week-sql-day-1-community-day/launch-week-sql-day-1-community-day-postgres-upgrade.jpg)","level":1,"lines":[8,9],"children":[{"type":"image","src":"/images/blog/launch-week-sql-day-1-community-day/launch-week-sql-day-1-community-day-postgres-upgrade.jpg","title":"","alt":"Postgres version 13 released","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[10,13],"level":0},{"type":"inline","content":"PostgreSQL is a big part of Supabase, and it's also a huge inspiration - the speed that they ship, their community organization,\nand their absolute dedication towards reliability. PostgreSQL 13.3 was released in June and (from today) every new Supabase project will\nbe on [PostgreSQL version 13.3](/blog/supabase-postgres-13).","level":1,"lines":[10,13],"children":[{"type":"text","content":"PostgreSQL is a big part of Supabase, and it's also a huge inspiration - the speed that they ship, their community organization,","level":0},{"type":"softbreak","level":0},{"type":"text","content":"and their absolute dedication towards reliability. PostgreSQL 13.3 was released in June and (from today) every new Supabase project will","level":0},{"type":"softbreak","level":0},{"type":"text","content":"be on ","level":0},{"type":"link_open","href":"/blog/supabase-postgres-13","title":"","level":0},{"type":"text","content":"PostgreSQL version 13.3","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[14,15],"level":0},{"type":"inline","content":"[Giving back](#giving-back)","level":1,"lines":[14,15],"children":[{"type":"text","content":"Giving back","level":0}],"lvl":3,"i":1,"seen":0,"slug":"giving-back"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[16,18],"level":0},{"type":"inline","content":"Supabase makes it easy to get started with Postgres, but we think you should try it even if you don't want to use Supabase. Here's a\nfew ways we make it easy to use PostgreSQL without Supabase:","level":1,"lines":[16,18],"children":[{"type":"text","content":"Supabase makes it easy to get started with Postgres, but we think you should try it even if you don't want to use Supabase. Here's a","level":0},{"type":"softbreak","level":0},{"type":"text","content":"few ways we make it easy to use PostgreSQL without Supabase:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[19,25],"level":0},{"type":"list_item_open","lines":[19,21],"level":1},{"type":"paragraph_open","tight":true,"lines":[19,21],"level":2},{"type":"inline","content":"Supabase was the first company to put Postgres in the [Digital Ocean Marketplace](https://marketplace.digitalocean.com/apps/supabase-postgres).\nSince then it's been installed over 1000 times (not by us!).","level":3,"lines":[19,21],"children":[{"type":"text","content":"Supabase was the first company to put Postgres in the ","level":0},{"type":"link_open","href":"https://marketplace.digitalocean.com/apps/supabase-postgres","title":"","level":0},{"type":"text","content":"Digital Ocean Marketplace","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Since then it's been installed over 1000 times (not by us!).","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[21,22],"level":1},{"type":"paragraph_open","tight":true,"lines":[21,22],"level":2},{"type":"inline","content":"We provide a Postgres image in the [AWS marketplace](https://aws.amazon.com/marketplace/pp/prodview-lk2pfa5nafecg).","level":3,"lines":[21,22],"children":[{"type":"text","content":"We provide a Postgres image in the ","level":0},{"type":"link_open","href":"https://aws.amazon.com/marketplace/pp/prodview-lk2pfa5nafecg","title":"","level":0},{"type":"text","content":"AWS marketplace","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[22,23],"level":1},{"type":"paragraph_open","tight":true,"lines":[22,23],"level":2},{"type":"inline","content":"We're packaging the Marketplace version, [along with \"Bundles\"](/blog/supabase-postgres-13#postgresql-bundles)","level":3,"lines":[22,23],"children":[{"type":"text","content":"We're packaging the Marketplace version, ","level":0},{"type":"link_open","href":"/blog/supabase-postgres-13#postgresql-bundles","title":"","level":0},{"type":"text","content":"along with \"Bundles\"","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[23,25],"level":1},{"type":"paragraph_open","tight":true,"lines":[23,24],"level":2},{"type":"inline","content":"We're developing a few extensions: [supautils](https://github.com/supabase/supautils), [pg_net](https://github.com/supabase/pg_net), and a couple more soon to be announced.","level":3,"lines":[23,24],"children":[{"type":"text","content":"We're developing a few extensions: ","level":0},{"type":"link_open","href":"https://github.com/supabase/supautils","title":"","level":0},{"type":"text","content":"supautils","level":1},{"type":"link_close","level":0},{"type":"text","content":", ","level":0},{"type":"link_open","href":"https://github.com/supabase/pg_net","title":"","level":0},{"type":"text","content":"pg_net","level":1},{"type":"link_close","level":0},{"type":"text","content":", and a couple more soon to be announced.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":3,"lines":[25,26],"level":0},{"type":"inline","content":"[Get involved](#get-involved)","level":1,"lines":[25,26],"children":[{"type":"text","content":"Get involved","level":0}],"lvl":3,"i":2,"seen":0,"slug":"get-involved"},{"type":"heading_close","hLevel":3,"level":0},{"type":"bullet_list_open","lines":[27,30],"level":0},{"type":"list_item_open","lines":[27,28],"level":1},{"type":"paragraph_open","tight":true,"lines":[27,28],"level":2},{"type":"inline","content":"Follow the official [PostgreSQL Twitter](https://twitter.com/PostgreSQL) account.","level":3,"lines":[27,28],"children":[{"type":"text","content":"Follow the official ","level":0},{"type":"link_open","href":"https://twitter.com/PostgreSQL","title":"","level":0},{"type":"text","content":"PostgreSQL Twitter","level":1},{"type":"link_close","level":0},{"type":"text","content":" account.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[28,30],"level":1},{"type":"paragraph_open","tight":true,"lines":[28,29],"level":2},{"type":"inline","content":"Join the official [mailing lists](https://www.postgresql.org/community/).","level":3,"lines":[28,29],"children":[{"type":"text","content":"Join the official ","level":0},{"type":"link_open","href":"https://www.postgresql.org/community/","title":"","level":0},{"type":"text","content":"mailing lists","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":2,"lines":[30,31],"level":0},{"type":"inline","content":"[PostgREST version 8.0](#postgrest-version-80)","level":1,"lines":[30,31],"children":[{"type":"text","content":"PostgREST version 8.0","level":0}],"lvl":2,"i":3,"seen":0,"slug":"postgrest-version-80"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[32,33],"level":0},{"type":"inline","content":"![PostgREST version 8.0 released](/images/blog/launch-week-sql-day-1-community-day/launch-week-sql-day-1-community-day-postgrest.jpg)","level":1,"lines":[32,33],"children":[{"type":"image","src":"/images/blog/launch-week-sql-day-1-community-day/launch-week-sql-day-1-community-day-postgrest.jpg","title":"","alt":"PostgREST version 8.0 released","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[34,36],"level":0},{"type":"inline","content":"This new PostgREST stable version comes with the improvements we've made to make it truly enterprise-grade. Due to our commitment with OSS,\nall of our improvements are upstreamed, so you can fully use them on your own self-hosted projects.","level":1,"lines":[34,36],"children":[{"type":"text","content":"This new PostgREST stable version comes with the improvements we've made to make it truly enterprise-grade. Due to our commitment with OSS,","level":0},{"type":"softbreak","level":0},{"type":"text","content":"all of our improvements are upstreamed, so you can fully use them on your own self-hosted projects.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[37,38],"level":0},{"type":"inline","content":"[Highlights](#highlights)","level":1,"lines":[37,38],"children":[{"type":"text","content":"Highlights","level":0}],"lvl":3,"i":4,"seen":0,"slug":"highlights"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[39,42],"level":0},{"type":"inline","content":"**Improved Performance**: Due to Supabase's high throughput requirements, PostgREST 8.0 handles up to 50% more throughput on\nGET requests, according to our benchmarks. We converted all SELECT queries to use prepared statements and reduced logging\nverbosity to make this possible.","level":1,"lines":[39,42],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Improved Performance","level":1},{"type":"strong_close","level":0},{"type":"text","content":": Due to Supabase's high throughput requirements, PostgREST 8.0 handles up to 50% more throughput on","level":0},{"type":"softbreak","level":0},{"type":"text","content":"GET requests, according to our benchmarks. We converted all SELECT queries to use prepared statements and reduced logging","level":0},{"type":"softbreak","level":0},{"type":"text","content":"verbosity to make this possible.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[43,44],"level":0},{"type":"inline","content":"**Reduced downtime**: we need PostgREST to be more \"set it and forget it\", so reloading schema cache now has zero downtime.","level":1,"lines":[43,44],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Reduced downtime","level":1},{"type":"strong_close","level":0},{"type":"text","content":": we need PostgREST to be more \"set it and forget it\", so reloading schema cache now has zero downtime.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[45,47],"level":0},{"type":"inline","content":"**Dynamic Configuration**: we've made it easier to handle PostgREST configuration at scale (we manage thousands of PostgREST instances).\nThis new version includes the ability to use an in-database configuration that is reloadable through a `NOTIFY` command.","level":1,"lines":[45,47],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Dynamic Configuration","level":1},{"type":"strong_close","level":0},{"type":"text","content":": we've made it easier to handle PostgREST configuration at scale (we manage thousands of PostgREST instances).","level":0},{"type":"softbreak","level":0},{"type":"text","content":"This new version includes the ability to use an in-database configuration that is reloadable through a ","level":0},{"type":"code","content":"NOTIFY","block":false,"level":0},{"type":"text","content":" command.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[48,50],"level":0},{"type":"inline","content":"**Less admin burden**: PostgREST previously required the `pg_listen` utility to reload its schema cache. This is no longer needed.\nThe schema cache is reloadable with a simple `NOTIFY` command.","level":1,"lines":[48,50],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Less admin burden","level":1},{"type":"strong_close","level":0},{"type":"text","content":": PostgREST previously required the ","level":0},{"type":"code","content":"pg_listen","block":false,"level":0},{"type":"text","content":" utility to reload its schema cache. This is no longer needed.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"The schema cache is reloadable with a simple ","level":0},{"type":"code","content":"NOTIFY","block":false,"level":0},{"type":"text","content":" command.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[51,53],"level":0},{"type":"inline","content":"**Better diagnostic information**: in the rare cases where PostgREST fails, we want to find the exact root cause quickly.\nFor this we've improved its logging by adding logging levels and timestamps to all server errors.","level":1,"lines":[51,53],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Better diagnostic information","level":1},{"type":"strong_close","level":0},{"type":"text","content":": in the rare cases where PostgREST fails, we want to find the exact root cause quickly.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"For this we've improved its logging by adding logging levels and timestamps to all server errors.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[54,56],"level":0},{"type":"inline","content":"**Simpler OpenAPI**: showing a complete OpenAPI output used to require a highly-privileged `anon` role. With the new `openapi-mode`,\nthis is no longer needed and `anon` can be kept with minimal privileges.","level":1,"lines":[54,56],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Simpler OpenAPI","level":1},{"type":"strong_close","level":0},{"type":"text","content":": showing a complete OpenAPI output used to require a highly-privileged ","level":0},{"type":"code","content":"anon","block":false,"level":0},{"type":"text","content":" role. With the new ","level":0},{"type":"code","content":"openapi-mode","block":false,"level":0},{"type":"text","content":",","level":0},{"type":"softbreak","level":0},{"type":"text","content":"this is no longer needed and ","level":0},{"type":"code","content":"anon","block":false,"level":0},{"type":"text","content":" can be kept with minimal privileges.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[57,59],"level":0},{"type":"inline","content":"The community has made many more enhancements and bug fixes for the new version. See\n[v8.0 CHANGELOG](https://github.com/PostgREST/postgrest/releases/tag/v8.0.0) for the full list.","level":1,"lines":[57,59],"children":[{"type":"text","content":"The community has made many more enhancements and bug fixes for the new version. See","level":0},{"type":"softbreak","level":0},{"type":"link_open","href":"https://github.com/PostgREST/postgrest/releases/tag/v8.0.0","title":"","level":0},{"type":"text","content":"v8.0 CHANGELOG","level":1},{"type":"link_close","level":0},{"type":"text","content":" for the full list.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[60,61],"level":0},{"type":"inline","content":"[Get Started](#get-started)","level":1,"lines":[60,61],"children":[{"type":"text","content":"Get Started","level":0}],"lvl":3,"i":5,"seen":0,"slug":"get-started"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[62,63],"level":0},{"type":"inline","content":"Want to get started with PostgREST? The Supabase community has built a number of client libraries to make it simpler to use:","level":1,"lines":[62,63],"children":[{"type":"text","content":"Want to get started with PostgREST? The Supabase community has built a number of client libraries to make it simpler to use:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[64,74],"level":0},{"type":"list_item_open","lines":[64,65],"level":1},{"type":"paragraph_open","tight":true,"lines":[64,65],"level":2},{"type":"inline","content":"Javascript: [postgrest-js](https://github.com/supabase/postgrest-js)","level":3,"lines":[64,65],"children":[{"type":"text","content":"Javascript: ","level":0},{"type":"link_open","href":"https://github.com/supabase/postgrest-js","title":"","level":0},{"type":"text","content":"postgrest-js","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[65,66],"level":1},{"type":"paragraph_open","tight":true,"lines":[65,66],"level":2},{"type":"inline","content":"Rust: [postgrest-rs](https://github.com/supabase/postgrest-rs)","level":3,"lines":[65,66],"children":[{"type":"text","content":"Rust: ","level":0},{"type":"link_open","href":"https://github.com/supabase/postgrest-rs","title":"","level":0},{"type":"text","content":"postgrest-rs","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[66,67],"level":1},{"type":"paragraph_open","tight":true,"lines":[66,67],"level":2},{"type":"inline","content":"Go: [postgrest-go](https://github.com/supabase/postgrest-go)","level":3,"lines":[66,67],"children":[{"type":"text","content":"Go: ","level":0},{"type":"link_open","href":"https://github.com/supabase/postgrest-go","title":"","level":0},{"type":"text","content":"postgrest-go","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[67,68],"level":1},{"type":"paragraph_open","tight":true,"lines":[67,68],"level":2},{"type":"inline","content":"Python: [postgrest-py](https://github.com/supabase/postgrest-py)","level":3,"lines":[67,68],"children":[{"type":"text","content":"Python: ","level":0},{"type":"link_open","href":"https://github.com/supabase/postgrest-py","title":"","level":0},{"type":"text","content":"postgrest-py","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[68,69],"level":1},{"type":"paragraph_open","tight":true,"lines":[68,69],"level":2},{"type":"inline","content":"Dart: [postgrest-dart](https://github.com/supabase/postgrest-dart)","level":3,"lines":[68,69],"children":[{"type":"text","content":"Dart: ","level":0},{"type":"link_open","href":"https://github.com/supabase/postgrest-dart","title":"","level":0},{"type":"text","content":"postgrest-dart","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[69,70],"level":1},{"type":"paragraph_open","tight":true,"lines":[69,70],"level":2},{"type":"inline","content":"C#: [postgrest-csharp](https://github.com/supabase/postgrest-csharp)","level":3,"lines":[69,70],"children":[{"type":"text","content":"C#: ","level":0},{"type":"link_open","href":"https://github.com/supabase/postgrest-csharp","title":"","level":0},{"type":"text","content":"postgrest-csharp","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[70,71],"level":1},{"type":"paragraph_open","tight":true,"lines":[70,71],"level":2},{"type":"inline","content":"Swift: [postgrest-swift](https://github.com/supabase/postgrest-swift)","level":3,"lines":[70,71],"children":[{"type":"text","content":"Swift: ","level":0},{"type":"link_open","href":"https://github.com/supabase/postgrest-swift","title":"","level":0},{"type":"text","content":"postgrest-swift","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[71,72],"level":1},{"type":"paragraph_open","tight":true,"lines":[71,72],"level":2},{"type":"inline","content":"Ruby: [postgrest-rb](https://github.com/supabase/postgrest-rb)","level":3,"lines":[71,72],"children":[{"type":"text","content":"Ruby: ","level":0},{"type":"link_open","href":"https://github.com/supabase/postgrest-rb","title":"","level":0},{"type":"text","content":"postgrest-rb","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[72,74],"level":1},{"type":"paragraph_open","tight":true,"lines":[72,73],"level":2},{"type":"inline","content":"Kotlin: [postgrest-kt](https://github.com/supabase/postgrest-kt)","level":3,"lines":[72,73],"children":[{"type":"text","content":"Kotlin: ","level":0},{"type":"link_open","href":"https://github.com/supabase/postgrest-kt","title":"","level":0},{"type":"text","content":"postgrest-kt","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":3,"lines":[74,75],"level":0},{"type":"inline","content":"[Get involved](#get-involved-1)","level":1,"lines":[74,75],"children":[{"type":"text","content":"Get involved","level":0}],"lvl":3,"i":6,"seen":1,"slug":"get-involved-1"},{"type":"heading_close","hLevel":3,"level":0},{"type":"bullet_list_open","lines":[76,80],"level":0},{"type":"list_item_open","lines":[76,77],"level":1},{"type":"paragraph_open","tight":true,"lines":[76,77],"level":2},{"type":"inline","content":"Help with [PostgREST development](https://github.com/PostgREST/postgrest/blob/main/.github/CONTRIBUTING.md).","level":3,"lines":[76,77],"children":[{"type":"text","content":"Help with ","level":0},{"type":"link_open","href":"https://github.com/PostgREST/postgrest/blob/main/.github/CONTRIBUTING.md","title":"","level":0},{"type":"text","content":"PostgREST development","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[77,78],"level":1},{"type":"paragraph_open","tight":true,"lines":[77,78],"level":2},{"type":"inline","content":"Help with [PostgREST docs translations](https://github.com/PostgREST/postgrest-docs/issues/393).","level":3,"lines":[77,78],"children":[{"type":"text","content":"Help with ","level":0},{"type":"link_open","href":"https://github.com/PostgREST/postgrest-docs/issues/393","title":"","level":0},{"type":"text","content":"PostgREST docs translations","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[78,80],"level":1},{"type":"paragraph_open","tight":true,"lines":[78,79],"level":2},{"type":"inline","content":"Follow the official [PostgREST Twitter](https://twitter.com/postgrest_org) account.","level":3,"lines":[78,79],"children":[{"type":"text","content":"Follow the official ","level":0},{"type":"link_open","href":"https://twitter.com/postgrest_org","title":"","level":0},{"type":"text","content":"PostgREST Twitter","level":1},{"type":"link_close","level":0},{"type":"text","content":" account.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":2,"lines":[80,81],"level":0},{"type":"inline","content":"[Supabase Flutter/Dart (Beta release)](#supabase-flutterdart-beta-release)","level":1,"lines":[80,81],"children":[{"type":"text","content":"Supabase Flutter/Dart (Beta release)","level":0}],"lvl":2,"i":7,"seen":0,"slug":"supabase-flutterdart-beta-release"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[82,83],"level":0},{"type":"inline","content":"![Supabase Flutter Beta release](/images/blog/launch-week-sql-day-1-community-day/launch-week-sql-day-1-community-day-flutter-library.jpg)","level":1,"lines":[82,83],"children":[{"type":"image","src":"/images/blog/launch-week-sql-day-1-community-day/launch-week-sql-day-1-community-day-flutter-library.jpg","title":"","alt":"Supabase Flutter Beta release","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[84,86],"level":0},{"type":"inline","content":"While the Supabase team have been busy with the Javascript libraries, the community have been beavering away with Dart. They've even\n[built fully-functional apps](https://www.producthunt.com/posts/spot-2d300f54-7a0a-4dbf-aee2-4a75311217cc) using Supabase Auth and Storage.","level":1,"lines":[84,86],"children":[{"type":"text","content":"While the Supabase team have been busy with the Javascript libraries, the community have been beavering away with Dart. They've even","level":0},{"type":"softbreak","level":0},{"type":"link_open","href":"https://www.producthunt.com/posts/spot-2d300f54-7a0a-4dbf-aee2-4a75311217cc","title":"","level":0},{"type":"text","content":"built fully-functional apps","level":1},{"type":"link_close","level":0},{"type":"text","content":" using Supabase Auth and Storage.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[87,88],"level":0},{"type":"inline","content":"Today the Community are releasing both the Flutter and Dart libraries in Beta (with a bit of help from the Supabase team).","level":1,"lines":[87,88],"children":[{"type":"text","content":"Today the Community are releasing both the Flutter and Dart libraries in Beta (with a bit of help from the Supabase team).","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[89,90],"level":0},{"type":"inline","content":"[Get started](#get-started)","level":1,"lines":[89,90],"children":[{"type":"text","content":"Get started","level":0}],"lvl":3,"i":8,"seen":0,"slug":"get-started"},{"type":"heading_close","hLevel":3,"level":0},{"type":"bullet_list_open","lines":[91,96],"level":0},{"type":"list_item_open","lines":[91,92],"level":1},{"type":"paragraph_open","tight":true,"lines":[91,92],"level":2},{"type":"inline","content":"Check out the [Flutter Quickstart Guide](/docs/guides/with-flutter)","level":3,"lines":[91,92],"children":[{"type":"text","content":"Check out the ","level":0},{"type":"link_open","href":"/docs/guides/with-flutter","title":"","level":0},{"type":"text","content":"Flutter Quickstart Guide","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[92,96],"level":1},{"type":"paragraph_open","tight":true,"lines":[92,93],"level":2},{"type":"inline","content":"Check out the code:","level":3,"lines":[92,93],"children":[{"type":"text","content":"Check out the code:","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"bullet_list_open","lines":[93,96],"level":2},{"type":"list_item_open","lines":[93,94],"level":3},{"type":"paragraph_open","tight":true,"lines":[93,94],"level":4},{"type":"inline","content":"Supabase Flutter: [GitHub](https://github.com/supabase/supabase-flutter/tree/main/packages/supabase_flutter) | [Release](https://pub.dev/packages/supabase_flutter)","level":5,"lines":[93,94],"children":[{"type":"text","content":"Supabase Flutter: ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase-flutter/tree/main/packages/supabase_flutter","title":"","level":0},{"type":"text","content":"GitHub","level":1},{"type":"link_close","level":0},{"type":"text","content":" | ","level":0},{"type":"link_open","href":"https://pub.dev/packages/supabase_flutter","title":"","level":0},{"type":"text","content":"Release","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":4},{"type":"list_item_close","level":3},{"type":"list_item_open","lines":[94,96],"level":3},{"type":"paragraph_open","tight":true,"lines":[94,95],"level":4},{"type":"inline","content":"Supabase Dart: [GitHub](https://github.com/supabase/supabase-flutter/tree/main/packages/supabase) | [Release](https://pub.dev/packages/supabase)","level":5,"lines":[94,95],"children":[{"type":"text","content":"Supabase Dart: ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase-flutter/tree/main/packages/supabase","title":"","level":0},{"type":"text","content":"GitHub","level":1},{"type":"link_close","level":0},{"type":"text","content":" | ","level":0},{"type":"link_open","href":"https://pub.dev/packages/supabase","title":"","level":0},{"type":"text","content":"Release","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":4},{"type":"list_item_close","level":3},{"type":"bullet_list_close","level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":3,"lines":[96,97],"level":0},{"type":"inline","content":"[Get involved](#get-involved-2)","level":1,"lines":[96,97],"children":[{"type":"text","content":"Get involved","level":0}],"lvl":3,"i":9,"seen":2,"slug":"get-involved-2"},{"type":"heading_close","hLevel":3,"level":0},{"type":"bullet_list_open","lines":[98,102],"level":0},{"type":"list_item_open","lines":[98,99],"level":1},{"type":"paragraph_open","tight":true,"lines":[98,99],"level":2},{"type":"inline","content":"Help [write](/supasquad#author) the Flutter Docs.","level":3,"lines":[98,99],"children":[{"type":"text","content":"Help ","level":0},{"type":"link_open","href":"/supasquad#author","title":"","level":0},{"type":"text","content":"write","level":1},{"type":"link_close","level":0},{"type":"text","content":" the Flutter Docs.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[99,102],"level":1},{"type":"paragraph_open","tight":true,"lines":[99,101],"level":2},{"type":"inline","content":"Help [maintain](/supasquad#maintainer) the [Flutter](https://github.com/supabase/supabase-flutter/tree/main/packages/supabase_flutter)\nand [Dart](https://github.com/supabase/supabase-flutter/tree/main/packages/supabase) libraries.","level":3,"lines":[99,101],"children":[{"type":"text","content":"Help ","level":0},{"type":"link_open","href":"/supasquad#maintainer","title":"","level":0},{"type":"text","content":"maintain","level":1},{"type":"link_close","level":0},{"type":"text","content":" the ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase-flutter/tree/main/packages/supabase_flutter","title":"","level":0},{"type":"text","content":"Flutter","level":1},{"type":"link_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"and ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase-flutter/tree/main/packages/supabase","title":"","level":0},{"type":"text","content":"Dart","level":1},{"type":"link_close","level":0},{"type":"text","content":" libraries.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":2,"lines":[102,103],"level":0},{"type":"inline","content":"[Supabase Discord](#supabase-discord)","level":1,"lines":[102,103],"children":[{"type":"text","content":"Supabase Discord","level":0}],"lvl":2,"i":10,"seen":0,"slug":"supabase-discord"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[104,105],"level":0},{"type":"inline","content":"![Supabase Discord](/images/blog/launch-week-sql-day-1-community-day/launch-week-sql-day-1-community-day-discord-server.jpg)","level":1,"lines":[104,105],"children":[{"type":"image","src":"/images/blog/launch-week-sql-day-1-community-day/launch-week-sql-day-1-community-day-discord-server.jpg","title":"","alt":"Supabase Discord","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[106,109],"level":0},{"type":"inline","content":"OK, OK, we get it - GitHub [Discussions](https://github.com/supabase/supabase/discussions) aren't enough for y'all. While we've been trying\nto keep the conversation contained to our GitHub org, the community has been creating [subreddits](https://www.reddit.com/r/Supabase/),\nStackOverflow [tags](https://stackoverflow.com/questions/tagged/supabase), and GitHub [topics](https://github.com/topics/supabase).","level":1,"lines":[106,109],"children":[{"type":"text","content":"OK, OK, we get it - GitHub ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase/discussions","title":"","level":0},{"type":"text","content":"Discussions","level":1},{"type":"link_close","level":0},{"type":"text","content":" aren't enough for y'all. While we've been trying","level":0},{"type":"softbreak","level":0},{"type":"text","content":"to keep the conversation contained to our GitHub org, the community has been creating ","level":0},{"type":"link_open","href":"https://www.reddit.com/r/Supabase/","title":"","level":0},{"type":"text","content":"subreddits","level":1},{"type":"link_close","level":0},{"type":"text","content":",","level":0},{"type":"softbreak","level":0},{"type":"text","content":"StackOverflow ","level":0},{"type":"link_open","href":"https://stackoverflow.com/questions/tagged/supabase","title":"","level":0},{"type":"text","content":"tags","level":1},{"type":"link_close","level":0},{"type":"text","content":", and GitHub ","level":0},{"type":"link_open","href":"https://github.com/topics/supabase","title":"","level":0},{"type":"text","content":"topics","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[110,111],"level":0},{"type":"inline","content":"A few weeks ago one of the community created a Community-led Discord, and so the team figured we might as well join the fun.","level":1,"lines":[110,111],"children":[{"type":"text","content":"A few weeks ago one of the community created a Community-led Discord, and so the team figured we might as well join the fun.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[112,113],"level":0},{"type":"inline","content":"We'll still be using Discussions for debugging, but if you're looking for a place to hang out with Supabase developers, Discord is where to find it.","level":1,"lines":[112,113],"children":[{"type":"text","content":"We'll still be using Discussions for debugging, but if you're looking for a place to hang out with Supabase developers, Discord is where to find it.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[114,115],"level":0},{"type":"inline","content":"[Get involved](#get-involved-3)","level":1,"lines":[114,115],"children":[{"type":"text","content":"Get involved","level":0}],"lvl":3,"i":11,"seen":3,"slug":"get-involved-3"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[116,117],"level":0},{"type":"inline","content":"Join the Supabase Community Discord: [discord.supabase.com](http://discord.supabase.com), say hi, and start building.","level":1,"lines":[116,117],"children":[{"type":"text","content":"Join the Supabase Community Discord: ","level":0},{"type":"link_open","href":"http://discord.supabase.com","title":"","level":0},{"type":"text","content":"discord.supabase.com","level":1},{"type":"link_close","level":0},{"type":"text","content":", say hi, and start building.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"footnote_block_open","level":0},{"type":"footnote_open","id":0,"level":1},{"type":"paragraph_open","tight":false,"lines":[119,122],"level":1},{"type":"inline","content":"Supabase has been one of the fastest growing startups [on GitHub](https://github.com/supabase/supabase) for four consecutive quarters:\n207% in [Q3](https://runacap.com/ross-index/q3-2020/) 2020; 1,373% in [Q4](https://runacap.com/ross-index/q4-2020/) 2020;\n462% in [Q1](https://runacap.com/ross-index/q1-2021/) 2021; 1,653% in [Q2](https://runacap.com/ross-index/q2-2021/) 2021.","level":2,"lines":[119,122],"children":[{"type":"text","content":"Supabase has been one of the fastest growing startups ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase","title":"","level":0},{"type":"text","content":"on GitHub","level":1},{"type":"link_close","level":0},{"type":"text","content":" for four consecutive quarters:","level":0},{"type":"softbreak","level":0},{"type":"text","content":"207% in ","level":0},{"type":"link_open","href":"https://runacap.com/ross-index/q3-2020/","title":"","level":0},{"type":"text","content":"Q3","level":1},{"type":"link_close","level":0},{"type":"text","content":" 2020; 1,373% in ","level":0},{"type":"link_open","href":"https://runacap.com/ross-index/q4-2020/","title":"","level":0},{"type":"text","content":"Q4","level":1},{"type":"link_close","level":0},{"type":"text","content":" 2020;","level":0},{"type":"softbreak","level":0},{"type":"text","content":"462% in ","level":0},{"type":"link_open","href":"https://runacap.com/ross-index/q1-2021/","title":"","level":0},{"type":"text","content":"Q1","level":1},{"type":"link_close","level":0},{"type":"text","content":" 2021; 1,653% in ","level":0},{"type":"link_open","href":"https://runacap.com/ross-index/q2-2021/","title":"","level":0},{"type":"text","content":"Q2","level":1},{"type":"link_close","level":0},{"type":"text","content":" 2021.","level":0}]},{"type":"footnote_anchor","id":0,"subId":0,"level":2},{"type":"paragraph_close","tight":false,"level":1},{"type":"footnote_close","level":1},{"type":"footnote_block_close","level":0}],"content":"- [PostgreSQL version 13.3](#postgresql-version-133)\n- [PostgREST version 8.0](#postgrest-version-80)\n- [Supabase Flutter/Dart (Beta release)](#supabase-flutterdart-beta-release)\n- [Supabase Discord](#supabase-discord)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-community-day"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>