<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Updates for Supabase Functions</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="The question on everyone&#x27;s mind - are we launching Supabase Functions? Well, it&#x27;s complicated." data-next-head=""/><meta property="og:title" content="Updates for Supabase Functions" data-next-head=""/><meta property="og:description" content="The question on everyone&#x27;s mind - are we launching Supabase Functions? Well, it&#x27;s complicated." data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-functions-updates" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2021-07-30" data-next-head=""/><meta property="article:author" content="https://github.com/kiwicopple" data-next-head=""/><meta property="article:tag" content="launch-week" data-next-head=""/><meta property="article:tag" content="functions" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/functions-updates/og-supabase-hooks.jpg" data-next-head=""/><meta property="og:image:alt" content="Updates for Supabase Functions thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Updates for Supabase Functions</h1><div class="text-light flex space-x-3 text-sm"><p>30 Jul 2021</p><p>•</p><p>8 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/kiwicopple"><div class="flex items-center gap-3"><div class="w-10"><img alt="Paul Copplestone avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fkiwicopple.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fkiwicopple.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fkiwicopple.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Paul Copplestone</span><span class="text-foreground-lighter mb-0 text-xs">CEO and Co-Founder</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Updates for Supabase Functions" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Ffunctions-updates%2Fthumb-supabase-hooks.jpg&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Ffunctions-updates%2Fthumb-supabase-hooks.jpg&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Ffunctions-updates%2Fthumb-supabase-hooks.jpg&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Ffunctions-updates%2Fthumb-supabase-hooks.jpg&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Ffunctions-updates%2Fthumb-supabase-hooks.jpg&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Ffunctions-updates%2Fthumb-supabase-hooks.jpg&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Ffunctions-updates%2Fthumb-supabase-hooks.jpg&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Ffunctions-updates%2Fthumb-supabase-hooks.jpg&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Ffunctions-updates%2Fthumb-supabase-hooks.jpg&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Ffunctions-updates%2Fthumb-supabase-hooks.jpg&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Ffunctions-updates%2Fthumb-supabase-hooks.jpg&amp;w=3840&amp;q=100"/></div><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p>The question on everyone&#x27;s mind - are we launching Supabase Functions? Well, it&#x27;s complicated.</p>
<p>Today we&#x27;re announcing <em>part</em> of Functions - Supabase Hooks - in Alpha, for all <strong>new</strong> projects.</p>
<p>We&#x27;re also releasing support for Postgres Functions and Triggers in our Dashboard, and some timelines for the rest of Supabase Functions.
Let&#x27;s cover the features we&#x27;re launching today before the item that everyone is waiting for: Supabase Functions.</p>
<h2 id="postgresql-functions" class="group scroll-mt-24">PostgreSQL Functions<a href="#postgresql-functions" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>(Not to be confused with Supabase Functions!)</p>
<p>Postgres has built-in support for <a href="https://www.postgresql.org/docs/current/sql-createfunction.html">SQL functions</a>. Today we&#x27;re making it even easier for developers to build PostgreSQL Functions by releasing a native Functions editor. Soon we&#x27;ll release some handy templates!</p>
<p></p>
<p>You can call PostgreSQL Functions with <code class="short-inline-codeblock">supabase-js</code> using your project API [<a href="https://supabase.com/docs/reference/javascript/rpc">Docs</a>]:</p>
<div class="ch-codegroup not-prose "><div class="ch-editor-frame"><div class="ch-frame-title-bar"><div class="ch-frame-buttons"><div class="ch-frame-button ch-frame-button-left"></div><div class="ch-frame-button-space"></div><div class="ch-frame-button ch-frame-button-middle"></div><div class="ch-frame-button-space"></div><div class="ch-frame-button ch-frame-button-right"></div></div><div title="hideCopy" data-ch-tab="north" data-active="true" class="ch-editor-tab"><div><span style="opacity:0.5"></span>hideCopy</div></div><div style="flex:1;min-width:0.8em"></div><button type="button" title="Copy code" class="ch-editor-button"><svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.6px" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg></button></div><div data-ch-panel="north" style="flex-grow:1;overflow:hidden"><div style="height:100%" class="ch-code-wrapper " data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>const { data, error } = await supabase.rpc(&#x27;best_star_wars_series&#x27;, {</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  name: &#x27;The Prequels&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>})</span></div></div><br/></code></div></div></div></div>
<h2 id="postgresql-triggers" class="group scroll-mt-24">PostgreSQL Triggers<a href="#postgresql-triggers" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p><a href="https://www.postgresql.org/docs/current/trigger-definition.html">Triggers</a> are another amazing feature of Postgres, which allows you to execute any SQL code after inserting, updating, or deleting data.</p>
<p>While triggers are a staple of Database Administrators, they can be a bit complex and hard to use. We plan to change that with a simple interface for building and managing PostgreSQL triggers.</p>
<p></p>
<h2 id="supabase-functions" class="group scroll-mt-24">Supabase Functions<a href="#supabase-functions" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>They say building a startup is like jumping off a cliff and assembling the plane on the way down. At Supabase it&#x27;s more like assembling a 747 since, although we&#x27;re still in Beta, thousands of companies depend on us to power their apps and websites.</p>
<p>For the past few months we&#x27;ve been designing Supabase Functions based on our customer feedback.</p>
<h3 id="byo-functions-zero-lock-in" class="group scroll-mt-24">BYO Functions, zero lock-in<a href="#byo-functions-zero-lock-in" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>A recurring request from our customers is the ability to trigger their <em>existing</em> Functions.
This is especially true for our Enterprise customers, but also Jamstack developers who develop API Functions directly within their stack (like Next.js <a href="https://nextjs.org/docs/api-routes/introduction">API routes</a>, or Redwood <a href="https://redwoodjs.com/docs/serverless-functions">Serverless Functions</a>).</p>
<h3 id="timeline" class="group scroll-mt-24">Timeline<a href="#timeline" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>To meet these goals, we&#x27;re releasing Supabase Functions in stages:</p>
<ul>
<li><em>Stage 1:</em> Give developers the ability to trigger external HTTP functions - today, using Database Webhooks.</li>
<li><em>Stage 2:</em> Give developers the ability to trigger their own Serverless functions on AWS and GCP - Q4 2021.</li>
<li><em>Stage 3:</em> Release our own Serverless Functions (Supabase Functions) - Q4 for Early Preview customers.</li>
</ul>
<p></p>
<h2 id="database-webhooks-alpha" class="group scroll-mt-24">Database Webhooks (Alpha)<a href="#database-webhooks-alpha" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>(Note: Database Webhooks were previously called &quot;Function Hooks&quot;)</p>
<p>Today we&#x27;re releasing Functions Hooks in <code class="short-inline-codeblock">ALPHA</code>. The <code class="short-inline-codeblock">ALPHA</code> tag means that it is NOT stable, but it&#x27;s available for testing and feedback. The API will change, so do not use it for anything critical. You have been warned.</p>
<p>Hooks? Triggers? Firestore has the concept of <a href="https://firebase.google.com/docs/functions/firestore-events">Function Triggers</a>, which are very cool.
Supabase Hooks are the same concept, just with a different name.
Postgres already has the concept of <a href="https://www.postgresql.org/docs/current/triggers.html">Triggers</a>, and we thought this would be less confusing<sup><a href="#user-content-fn-1" id="user-content-fnref-1" data-footnote-ref="true" aria-describedby="footnote-label">1</a></sup>.</p>
<h3 id="hook-events" class="group scroll-mt-24">Hook Events<a href="#hook-events" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Database Webhooks allow you to &quot;listen&quot; to any change in your tables to trigger an asynchronous Function. You can hook into a few different events:<code class="short-inline-codeblock">INSERT</code>, <code class="short-inline-codeblock">UPDATE</code>, and <code class="short-inline-codeblock">DELETE</code>. All events are fired <strong>after</strong> a database row is changed. Keen eyes will be able to spot the similarity to Postgres triggers, and that&#x27;s because Database Webhooks are just a convenience wrapper around triggers.</p>
<p></p>
<h3 id="hook-targets" class="group scroll-mt-24">Hook Targets<a href="#hook-targets" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Supabase will support several different targets.</p>
<ul>
<li>HTTP/Webhooks: Send HTTP requests directly from your Postgres Database.</li>
<li>AWS Lambda/Google Cloud Run: Provide Supabase with a restricted IAM role to trigger Serverless functions natively.</li>
<li>Supabase Functions: We&#x27;ll develop an end-to-end experience.</li>
</ul>
<p></p>
<h3 id="hook-payload" class="group scroll-mt-24">Hook Payload<a href="#hook-payload" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>If the target is a Serverless function or an HTTP <code class="short-inline-codeblock">POST</code> request, the payload is automatically generated from the underlying table data. The format matches Supabase <a href="https://supabase.com/docs/reference/javascript/subscribe">Realtime</a>, except in this case you don&#x27;t a client to &quot;listen&quot; to the changes. This provides yet another mechanism for responding to database changes.</p>
<div class="ch-codegroup not-prose "><div class="ch-editor-frame"><div class="ch-frame-title-bar"><div class="ch-frame-buttons"><div class="ch-frame-button ch-frame-button-left"></div><div class="ch-frame-button-space"></div><div class="ch-frame-button ch-frame-button-middle"></div><div class="ch-frame-button-space"></div><div class="ch-frame-button ch-frame-button-right"></div></div><div title="hideCopy" data-ch-tab="north" data-active="true" class="ch-editor-tab"><div><span style="opacity:0.5"></span>hideCopy</div></div><div style="flex:1;min-width:0.8em"></div><button type="button" title="Copy code" class="ch-editor-button"><svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.6px" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg></button></div><div data-ch-panel="north" style="flex-grow:1;overflow:hidden"><div style="height:100%" class="ch-code-wrapper " data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>type InsertPayload = {</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>  type: &#x27;INSERT&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>  table: string</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>  schema: string</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>  record: TableRecord&lt;T&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>  old_record: null</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>type UpdatePayload = {</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>  type: &#x27;UPDATE&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>  table: string</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>  schema: string</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>  record: TableRecord&lt;T&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>  old_record: TableRecord&lt;T&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>type DeletePayload = {</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>  type: &#x27;DELETE&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>  table: string</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>  schema: string</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>  record: null</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>  old_record: TableRecord&lt;T&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div></div></div>
<h2 id="hooks-technical-design-pg_net-v01" class="group scroll-mt-24">Hooks technical design: <code class="short-inline-codeblock">pg_net v0.1</code><a href="#hooks-technical-design-pg_net-v01" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>As with most of the Supabase platform, we leverage PostgreSQL&#x27;s native functionality to implement Database Webhooks (previously called &quot;Function Hooks&quot;).</p>
<p>To build hooks, we&#x27;ve released a new PostgreSQL Extension, <a href="https://github.com/supabase/pg_net/">pg_net</a>, an asynchronous networking extension with an emphasis on scalability/throughput. In its initial (unstable) release we expose:</p>
<ul>
<li>asynchronous HTTP <code class="short-inline-codeblock">GET</code> requests.</li>
<li>asynchronous HTTP <code class="short-inline-codeblock">POST</code> requests with a JSON payload.</li>
</ul>
<p>The extension is (currently) capable of &gt;300 requests per second and is the networking layer underpinning Database Webhooks. For a complete view of capabilities, check out <a href="https://supabase.github.io/pg_net/api/">the docs</a>.</p>
<h3 id="usage" class="group scroll-mt-24"><strong>Usage</strong><a href="#usage" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p><code class="short-inline-codeblock">pg_net</code> allows you to make asynchronous HTTP requests directly within your SQL queries.</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>-- Make a request</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>select</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>    net.http_post(</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>        url:=&#x27;https://httpbin.org/post&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>        body:=&#x27;{&quot;hello&quot;: &quot;world&quot;}&#x27;::jsonb</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>    );</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>-- Immediately returns a response ID</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>http_post</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>---------</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>        1</span></div></div><br/></code></div></div>
<p>After making a request, the extension will return an ID. You can use this ID to collect a response.</p>
<div class="ch-codegroup not-prose "><div class="ch-editor-frame"><div class="ch-frame-title-bar"><div class="ch-frame-buttons"><div class="ch-frame-button ch-frame-button-left"></div><div class="ch-frame-button-space"></div><div class="ch-frame-button ch-frame-button-middle"></div><div class="ch-frame-button-space"></div><div class="ch-frame-button ch-frame-button-right"></div></div><div title="hideCopy" data-ch-tab="north" data-active="true" class="ch-editor-tab"><div><span style="opacity:0.5"></span>hideCopy</div></div><div style="flex:1;min-width:0.8em"></div><button type="button" title="Copy code" class="ch-editor-button"><svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.6px" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg></button></div><div data-ch-panel="north" style="flex-grow:1;overflow:hidden"><div style="height:100%" class="ch-code-wrapper " data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>-- Collect the response from a request</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>select</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>  *</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>from</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>  net.http_collect_response(1);</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>-- Results (1 row)</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>status  | message | response</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>--------+---------+----------</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>SUCCESS        ok     (</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>                        status_code := 200,</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>                        headers     := &#x27;{&quot;date&quot;: ...}&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>                        body        := &#x27;{&quot;args&quot;: ...}&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>                      )::net.http_response_result</span></div></div><br/></code></div></div></div></div>
<p>You can cast the response to JSON within PostgreSQL:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>-- Collect the response json payload from a request</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>select</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    (response).body::json</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>from</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    net.http_collect_response(request_id:=1);</span></div></div><br/></code></div></div>
<p>Result:</p>
<div class="ch-codegroup not-prose "><div class="ch-editor-frame"><div class="ch-frame-title-bar"><div class="ch-frame-buttons"><div class="ch-frame-button ch-frame-button-left"></div><div class="ch-frame-button-space"></div><div class="ch-frame-button ch-frame-button-middle"></div><div class="ch-frame-button-space"></div><div class="ch-frame-button ch-frame-button-right"></div></div><div title="noCopy" data-ch-tab="north" data-active="true" class="ch-editor-tab"><div><span style="opacity:0.5"></span>noCopy</div></div><div style="flex:1;min-width:0.8em"></div><button type="button" title="Copy code" class="ch-editor-button"><svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.6px" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg></button></div><div data-ch-panel="north" style="flex-grow:1;overflow:hidden"><div style="height:100%" class="ch-code-wrapper " data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span> {</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>   &quot;args&quot;: {},</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>   &quot;data&quot;: &quot;{\&quot;hello\&quot;: \&quot;world\&quot;}&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>   &quot;files&quot;: {},</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>   &quot;form&quot;: {},</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>   &quot;headers&quot;: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>     &quot;Accept&quot;: &quot;*/*&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>     &quot;Content-Length&quot;: &quot;18&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>     &quot;Content-Type&quot;: &quot;application/json&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>     &quot;Host&quot;: &quot;httpbin.org&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>     &quot;User-Agent&quot;: &quot;pg_net/0.1&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>     &quot;X-Amzn-Trace-Id&quot;: &quot;Root=1-61031a5c-7e1afeae69bffa8614d8e48e&quot;</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>   },</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>   &quot;json&quot;: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>     &quot;hello&quot;: &quot;world&quot;</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>   },</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>   &quot;origin&quot;: &quot;135.63.38.488&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>   &quot;url&quot;: &quot;https://httpbin.org/post&quot;</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span> }</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>(1 row)</span></div></div><br/></code></div></div></div></div>
<h3 id="implementation" class="group scroll-mt-24">Implementation<a href="#implementation" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>To build asynchronous behavior, we use a PostgreSQL <a href="https://www.postgresql.org/docs/current/bgworker.html">background worker</a> with a <a href="https://github.com/supabase/pg_net/blob/3d52e7758909bb73bf7fa4586f42cea73ed239b6/sql/pg_net--0.1.sql#L11-L19">queue</a>. This, coupled with the <a href="https://curl.se/libcurl/c/libcurl-multi.html">libcurl multi interface</a>, enables us to do multiple simultaneous requests in the same background worker process.</p>
<p>Shout out to <a href="https://github.com/pramsey">Paul Ramsey</a>, who gave us the implementation idea in <a href="https://github.com/pramsey/pgsql-http/#to-do">pgsql-http</a>. While we originally hoped to add background workers to his extension, the implementation became too cumbersome and we decided to start with a clean slate. The advantage of being async can be seen by making some requests with both extensions:</p>
<div class="ch-codegroup not-prose "><div class="ch-editor-frame"><div class="ch-frame-title-bar"><div class="ch-frame-buttons"><div class="ch-frame-button ch-frame-button-left"></div><div class="ch-frame-button-space"></div><div class="ch-frame-button ch-frame-button-middle"></div><div class="ch-frame-button-space"></div><div class="ch-frame-button ch-frame-button-right"></div></div><div title="hideCopy" data-ch-tab="north" data-active="true" class="ch-editor-tab"><div><span style="opacity:0.5"></span>hideCopy</div></div><div style="flex:1;min-width:0.8em"></div><button type="button" title="Copy code" class="ch-editor-button"><svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.6px" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg></button></div><div data-ch-panel="north" style="flex-grow:1;overflow:hidden"><div style="height:100%" class="ch-code-wrapper " data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>\timing on</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>-- using pgsql-http to fetch from &quot;supabase.io&quot; 10 times</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>select</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>    *</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>from</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>   http_get(&#x27;https://supabase.com&#x27;)</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>cross join</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>   generate_series(1, 10) _;</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>-- Returns in 3.5 seconds</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>Time: 3501.935 ms</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>-- using pg_net to fetch from &quot;supabase.io&quot; 10 times</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>select</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>    net.http_get(&#x27;https://supabase.com&#x27;)</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>from</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>    generate_series (1,10) _;</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>-- Returns in 1.5 milliseconds</span></div></div><div><span class="ch-code-line-number">_<!-- -->21</span><div style="display:inline-block;margin-left:16px"><span>Time: 1.562 ms</span></div></div><br/></code></div></div></div></div>
<p>Of course, the sync version waits until each request is completed to return the result, taking around 3.5 seconds for 10 requests; while the async version returns almost immediately in 1.5 milliseconds. This is really important for Supabase hooks, which run requests for every event fired from a SQL trigger - potentially thousands of requests per second.</p>
<h3 id="futureroadmap" class="group scroll-mt-24">Future/Roadmap<a href="#futureroadmap" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>This is only the beginning! First we&#x27;ll thoroughly test it and make a stable release, then we expect to add support for</p>
<ul>
<li>the remaining HTTP methods (<code class="short-inline-codeblock">PUT</code> / <code class="short-inline-codeblock">PATCH</code>)</li>
<li>synchronous HTTP</li>
<li>additional protocols e.g. SMTP, FTP</li>
<li>more throughput (using epoll)</li>
</ul>
<h2 id="get-started-today" class="group scroll-mt-24">Get started today<a href="#get-started-today" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Database Webhooks is enabled today on all <a href="../dashboard/org.html">new projects</a>. Find it under Database &gt; Alpha Preview &gt; Database Webhooks.</p>
<p></p>
<section data-footnotes="true" class="footnotes"><h2 id="footnote-label" class="sr-only">Footnotes<a href="#footnote-label" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ol>
<li id="user-content-fn-1">
<p>Postgres also has the concept of <a href="https://supabase.com/blog/roles-postgres-hooks">Hooks</a>, but they&#x27;re more of an internal concept. <a href="#user-content-fnref-1" data-footnote-backref="true" class="data-footnote-backref" aria-label="Back to content">↩</a></p>
</li>
</ol>
</section></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-functions-updates&amp;text=Updates%20for%20Supabase%20Functions"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-functions-updates&amp;text=Updates%20for%20Supabase%20Functions"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-functions-updates&amp;t=Updates%20for%20Supabase%20Functions"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="1-the-supabase-hackathon.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">The Supabase Hackathon</h4><p class="small">30 July 2021</p></div></div></div></div></a></div><div><a href="supabase-swag-store.html"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Supabase Swag Store</h4><p class="small">30 July 2021</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/launch-week"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">launch-week</div></a><a href="https://supabase.com/blog/tags/functions"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">functions</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#postgresql-functions">PostgreSQL Functions</a></li>
<li><a href="#postgresql-triggers">PostgreSQL Triggers</a></li>
<li><a href="#supabase-functions">Supabase Functions</a>
<ul>
<li><a href="#byo-functions-zero-lock-in">BYO Functions, zero lock-in</a></li>
<li><a href="#timeline">Timeline</a></li>
</ul>
</li>
<li><a href="#database-webhooks-alpha">Database Webhooks (Alpha)</a>
<ul>
<li><a href="#hook-events">Hook Events</a></li>
<li><a href="#hook-targets">Hook Targets</a></li>
<li><a href="#hook-payload">Hook Payload</a></li>
</ul>
</li>
<li><a href="#hooks-technical-design-pg_net-v01">Hooks technical design: <code>pg_net v0.1</code></a>
<ul>
<li><a href="#usage"><strong>Usage</strong></a></li>
<li><a href="#implementation">Implementation</a></li>
<li><a href="#futureroadmap">Future/Roadmap</a></li>
</ul>
</li>
<li><a href="#get-started-today">Get started today</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-functions-updates&amp;text=Updates%20for%20Supabase%20Functions"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-functions-updates&amp;text=Updates%20for%20Supabase%20Functions"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-functions-updates&amp;t=Updates%20for%20Supabase%20Functions"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"1-the-supabase-hackathon","title":"The Supabase Hackathon","description":"A whole week of Hacking for Fun and Prizes.","author":"ant_wilson","author_url":"https://github.com/awalias","author_image_url":"https://github.com/awalias.png","image":"hackathon/og-supabase-hackathon.png","thumb":"hackathon/cover-supabase-hackathon.png","categories":["developers"],"tags":["launch-week","hackathon"],"date":"2021-07-30","toc_depth":3,"formattedDate":"30 July 2021","readingTime":"4 minute read","url":"/blog/1-the-supabase-hackathon","path":"/blog/1-the-supabase-hackathon"},"nextPost":{"slug":"supabase-swag-store","title":"Supabase Swag Store","description":"Today we are officially launching the Supabase Swag Store.","author":"rory_wilding","author_url":"https://github.com/roryw10","author_image_url":"https://github.com/roryw10.png","image":"swag-store/og-supabase-swag-store.jpg","thumb":"swag-store/cover-swag-store.jpg","categories":["company"],"tags":["launch-week","swag"],"date":"2021-07-30","toc_depth":3,"formattedDate":"30 July 2021","readingTime":"4 minute read","url":"/blog/supabase-swag-store","path":"/blog/supabase-swag-store"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-functions-updates","source":"\nThe question on everyone's mind - are we launching Supabase Functions? Well, it's complicated.\n\nToday we're announcing _part_ of Functions - Supabase Hooks - in Alpha, for all **new** projects.\n\nWe're also releasing support for Postgres Functions and Triggers in our Dashboard, and some timelines for the rest of Supabase Functions.\nLet's cover the features we're launching today before the item that everyone is waiting for: Supabase Functions.\n\n## PostgreSQL Functions\n\n(Not to be confused with Supabase Functions!)\n\nPostgres has built-in support for [SQL functions](https://www.postgresql.org/docs/current/sql-createfunction.html). Today we're making it even easier for developers to build PostgreSQL Functions by releasing a native Functions editor. Soon we'll release some handy templates!\n\n![Postgres Functions](/images/blog/functions-updates/postgres-functions.png)\n\nYou can call PostgreSQL Functions with `supabase-js` using your project API [[Docs](/docs/reference/javascript/rpc)]:\n\n```js hideCopy\nconst { data, error } = await supabase.rpc('best_star_wars_series', {\n  name: 'The Prequels',\n})\n```\n\n## PostgreSQL Triggers\n\n[Triggers](https://www.postgresql.org/docs/current/trigger-definition.html) are another amazing feature of Postgres, which allows you to execute any SQL code after inserting, updating, or deleting data.\n\nWhile triggers are a staple of Database Administrators, they can be a bit complex and hard to use. We plan to change that with a simple interface for building and managing PostgreSQL triggers.\n\n![Postgres Triggers](/images/blog/functions-updates/postgres-triggers.png)\n\n## Supabase Functions\n\nThey say building a startup is like jumping off a cliff and assembling the plane on the way down. At Supabase it's more like assembling a 747 since, although we're still in Beta, thousands of companies depend on us to power their apps and websites.\n\nFor the past few months we've been designing Supabase Functions based on our customer feedback.\n\n### BYO Functions, zero lock-in\n\nA recurring request from our customers is the ability to trigger their _existing_ Functions.\nThis is especially true for our Enterprise customers, but also Jamstack developers who develop API Functions directly within their stack (like Next.js [API routes](https://nextjs.org/docs/api-routes/introduction), or Redwood [Serverless Functions](https://redwoodjs.com/docs/serverless-functions)).\n\n### Timeline\n\nTo meet these goals, we're releasing Supabase Functions in stages:\n\n- _Stage 1:_ Give developers the ability to trigger external HTTP functions - today, using Database Webhooks.\n- _Stage 2:_ Give developers the ability to trigger their own Serverless functions on AWS and GCP - Q4 2021.\n- _Stage 3:_ Release our own Serverless Functions (Supabase Functions) - Q4 for Early Preview customers.\n\n![Supabase Functions timeline](/images/blog/functions-updates/functions-timeline.png)\n\n## Database Webhooks (Alpha)\n\n(Note: Database Webhooks were previously called \"Function Hooks\")\n\nToday we're releasing Functions Hooks in `ALPHA`. The `ALPHA` tag means that it is NOT stable, but it's available for testing and feedback. The API will change, so do not use it for anything critical. You have been warned.\n\nHooks? Triggers? Firestore has the concept of [Function Triggers](https://firebase.google.com/docs/functions/firestore-events), which are very cool.\nSupabase Hooks are the same concept, just with a different name.\nPostgres already has the concept of [Triggers](https://www.postgresql.org/docs/current/triggers.html), and we thought this would be less confusing[^1].\n\n### Hook Events\n\nDatabase Webhooks allow you to \"listen\" to any change in your tables to trigger an asynchronous Function. You can hook into a few different events:`INSERT`, `UPDATE`, and `DELETE`. All events are fired **after** a database row is changed. Keen eyes will be able to spot the similarity to Postgres triggers, and that's because Database Webhooks are just a convenience wrapper around triggers.\n\n![Hook Events](/images/blog/functions-updates/hook-events.png)\n\n### Hook Targets\n\nSupabase will support several different targets.\n\n- HTTP/Webhooks: Send HTTP requests directly from your Postgres Database.\n- AWS Lambda/Google Cloud Run: Provide Supabase with a restricted IAM role to trigger Serverless functions natively.\n- Supabase Functions: We'll develop an end-to-end experience.\n\n![Supabase Database Webhooks](/images/blog/functions-updates/supabase-function-hooks.png)\n\n### Hook Payload\n\nIf the target is a Serverless function or an HTTP `POST` request, the payload is automatically generated from the underlying table data. The format matches Supabase [Realtime](/docs/reference/javascript/subscribe), except in this case you don't a client to \"listen\" to the changes. This provides yet another mechanism for responding to database changes.\n\n```ts hideCopy\ntype InsertPayload = {\n  type: 'INSERT'\n  table: string\n  schema: string\n  record: TableRecord\u003cT\u003e\n  old_record: null\n}\ntype UpdatePayload = {\n  type: 'UPDATE'\n  table: string\n  schema: string\n  record: TableRecord\u003cT\u003e\n  old_record: TableRecord\u003cT\u003e\n}\ntype DeletePayload = {\n  type: 'DELETE'\n  table: string\n  schema: string\n  record: null\n  old_record: TableRecord\u003cT\u003e\n}\n```\n\n## Hooks technical design: `pg_net v0.1`\n\nAs with most of the Supabase platform, we leverage PostgreSQL's native functionality to implement Database Webhooks (previously called \"Function Hooks\").\n\nTo build hooks, we've released a new PostgreSQL Extension, [pg_net](https://github.com/supabase/pg_net/), an asynchronous networking extension with an emphasis on scalability/throughput. In its initial (unstable) release we expose:\n\n- asynchronous HTTP `GET` requests.\n- asynchronous HTTP `POST` requests with a JSON payload.\n\nThe extension is (currently) capable of \u003e300 requests per second and is the networking layer underpinning Database Webhooks. For a complete view of capabilities, check out [the docs](https://supabase.github.io/pg_net/api/).\n\n### **Usage**\n\n`pg_net` allows you to make asynchronous HTTP requests directly within your SQL queries.\n\n```sql\n-- Make a request\nselect\n    net.http_post(\n        url:='https://httpbin.org/post',\n        body:='{\"hello\": \"world\"}'::jsonb\n    );\n\n-- Immediately returns a response ID\nhttp_post\n---------\n        1\n```\n\nAfter making a request, the extension will return an ID. You can use this ID to collect a response.\n\n```sql hideCopy\n-- Collect the response from a request\nselect\n  *\nfrom\n  net.http_collect_response(1);\n\n-- Results (1 row)\nstatus  | message | response\n--------+---------+----------\nSUCCESS        ok     (\n                        status_code := 200,\n                        headers     := '{\"date\": ...}',\n                        body        := '{\"args\": ...}'\n                      )::net.http_response_result\n```\n\nYou can cast the response to JSON within PostgreSQL:\n\n```sql\n-- Collect the response json payload from a request\nselect\n    (response).body::json\nfrom\n    net.http_collect_response(request_id:=1);\n```\n\nResult:\n\n```json noCopy\n {\n   \"args\": {},\n   \"data\": \"{\\\"hello\\\": \\\"world\\\"}\",\n   \"files\": {},\n   \"form\": {},\n   \"headers\": {\n     \"Accept\": \"*/*\",\n     \"Content-Length\": \"18\",\n     \"Content-Type\": \"application/json\",\n     \"Host\": \"httpbin.org\",\n     \"User-Agent\": \"pg_net/0.1\",\n     \"X-Amzn-Trace-Id\": \"Root=1-61031a5c-7e1afeae69bffa8614d8e48e\"\n   },\n   \"json\": {\n     \"hello\": \"world\"\n   },\n   \"origin\": \"135.63.38.488\",\n   \"url\": \"https://httpbin.org/post\"\n }\n(1 row)\n```\n\n### Implementation\n\nTo build asynchronous behavior, we use a PostgreSQL [background worker](https://www.postgresql.org/docs/current/bgworker.html) with a [queue](https://github.com/supabase/pg_net/blob/3d52e7758909bb73bf7fa4586f42cea73ed239b6/sql/pg_net--0.1.sql#L11-L19). This, coupled with the [libcurl multi interface](https://curl.se/libcurl/c/libcurl-multi.html), enables us to do multiple simultaneous requests in the same background worker process.\n\nShout out to [Paul Ramsey](https://github.com/pramsey), who gave us the implementation idea in [pgsql-http](https://github.com/pramsey/pgsql-http/#to-do). While we originally hoped to add background workers to his extension, the implementation became too cumbersome and we decided to start with a clean slate. The advantage of being async can be seen by making some requests with both extensions:\n\n```sql hideCopy\n\\timing on\n\n-- using pgsql-http to fetch from \"supabase.io\" 10 times\nselect\n    *\nfrom\n   http_get('https://supabase.com')\ncross join\n   generate_series(1, 10) _;\n\n-- Returns in 3.5 seconds\nTime: 3501.935 ms\n\n-- using pg_net to fetch from \"supabase.io\" 10 times\nselect\n    net.http_get('https://supabase.com')\nfrom\n    generate_series (1,10) _;\n\n-- Returns in 1.5 milliseconds\nTime: 1.562 ms\n```\n\nOf course, the sync version waits until each request is completed to return the result, taking around 3.5 seconds for 10 requests; while the async version returns almost immediately in 1.5 milliseconds. This is really important for Supabase hooks, which run requests for every event fired from a SQL trigger - potentially thousands of requests per second.\n\n### Future/Roadmap\n\nThis is only the beginning! First we'll thoroughly test it and make a stable release, then we expect to add support for\n\n- the remaining HTTP methods (`PUT` / `PATCH`)\n- synchronous HTTP\n- additional protocols e.g. SMTP, FTP\n- more throughput (using epoll)\n\n## Get started today\n\nDatabase Webhooks is enabled today on all [new projects](https://supabase.com/dashboard). Find it under Database \u003e Alpha Preview \u003e Database Webhooks.\n\n![Enable hooks](/images/blog/functions-updates/enable-hooks.png)\n\n[^1]: Postgres also has the concept of [Hooks](/blog/roles-postgres-hooks), but they're more of an internal concept.\n","title":"Updates for Supabase Functions","description":"The question on everyone's mind - are we launching Supabase Functions? Well, it's complicated.","author":"paul_copplestone","author_url":"https://github.com/kiwicopple","author_image_url":"https://github.com/kiwicopple.png","image":"functions-updates/og-supabase-hooks.jpg","thumb":"functions-updates/thumb-supabase-hooks.jpg","categories":["product"],"tags":["launch-week","functions"],"date":"2021-07-30","toc_depth":3,"content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    em: \"em\",\n    strong: \"strong\",\n    h2: \"h2\",\n    a: \"a\",\n    img: \"img\",\n    code: \"code\",\n    h3: \"h3\",\n    ul: \"ul\",\n    li: \"li\",\n    sup: \"sup\",\n    section: \"section\",\n    ol: \"ol\"\n  }, _provideComponents(), props.components), {CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The question on everyone's mind - are we launching Supabase Functions? Well, it's complicated.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Today we're announcing \", _jsx(_components.em, {\n        children: \"part\"\n      }), \" of Functions - Supabase Hooks - in Alpha, for all \", _jsx(_components.strong, {\n        children: \"new\"\n      }), \" projects.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We're also releasing support for Postgres Functions and Triggers in our Dashboard, and some timelines for the rest of Supabase Functions.\\nLet's cover the features we're launching today before the item that everyone is waiting for: Supabase Functions.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"postgresql-functions\",\n      children: \"PostgreSQL Functions\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"(Not to be confused with Supabase Functions!)\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Postgres has built-in support for \", _jsx(_components.a, {\n        href: \"https://www.postgresql.org/docs/current/sql-createfunction.html\",\n        children: \"SQL functions\"\n      }), \". Today we're making it even easier for developers to build PostgreSQL Functions by releasing a native Functions editor. Soon we'll release some handy templates!\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/functions-updates/postgres-functions.png\",\n        alt: \"Postgres Functions\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You can call PostgreSQL Functions with \", _jsx(_components.code, {\n        children: \"supabase-js\"\n      }), \" using your project API [\", _jsx(_components.a, {\n        href: \"/docs/reference/javascript/rpc\",\n        children: \"Docs\"\n      }), \"]:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"hideCopy\"],\n        \"active\": \"hideCopy\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"hideCopy\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"data\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"error\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"rpc\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'best_star_wars_series'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  name: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'The Prequels'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"})\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"js\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"postgresql-triggers\",\n      children: \"PostgreSQL Triggers\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://www.postgresql.org/docs/current/trigger-definition.html\",\n        children: \"Triggers\"\n      }), \" are another amazing feature of Postgres, which allows you to execute any SQL code after inserting, updating, or deleting data.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"While triggers are a staple of Database Administrators, they can be a bit complex and hard to use. We plan to change that with a simple interface for building and managing PostgreSQL triggers.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/functions-updates/postgres-triggers.png\",\n        alt: \"Postgres Triggers\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"supabase-functions\",\n      children: \"Supabase Functions\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"They say building a startup is like jumping off a cliff and assembling the plane on the way down. At Supabase it's more like assembling a 747 since, although we're still in Beta, thousands of companies depend on us to power their apps and websites.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"For the past few months we've been designing Supabase Functions based on our customer feedback.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"byo-functions-zero-lock-in\",\n      children: \"BYO Functions, zero lock-in\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"A recurring request from our customers is the ability to trigger their \", _jsx(_components.em, {\n        children: \"existing\"\n      }), \" Functions.\\nThis is especially true for our Enterprise customers, but also Jamstack developers who develop API Functions directly within their stack (like Next.js \", _jsx(_components.a, {\n        href: \"https://nextjs.org/docs/api-routes/introduction\",\n        children: \"API routes\"\n      }), \", or Redwood \", _jsx(_components.a, {\n        href: \"https://redwoodjs.com/docs/serverless-functions\",\n        children: \"Serverless Functions\"\n      }), \").\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"timeline\",\n      children: \"Timeline\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To meet these goals, we're releasing Supabase Functions in stages:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.em, {\n          children: \"Stage 1:\"\n        }), \" Give developers the ability to trigger external HTTP functions - today, using Database Webhooks.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.em, {\n          children: \"Stage 2:\"\n        }), \" Give developers the ability to trigger their own Serverless functions on AWS and GCP - Q4 2021.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.em, {\n          children: \"Stage 3:\"\n        }), \" Release our own Serverless Functions (Supabase Functions) - Q4 for Early Preview customers.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/functions-updates/functions-timeline.png\",\n        alt: \"Supabase Functions timeline\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"database-webhooks-alpha\",\n      children: \"Database Webhooks (Alpha)\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"(Note: Database Webhooks were previously called \\\"Function Hooks\\\")\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Today we're releasing Functions Hooks in \", _jsx(_components.code, {\n        children: \"ALPHA\"\n      }), \". The \", _jsx(_components.code, {\n        children: \"ALPHA\"\n      }), \" tag means that it is NOT stable, but it's available for testing and feedback. The API will change, so do not use it for anything critical. You have been warned.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Hooks? Triggers? Firestore has the concept of \", _jsx(_components.a, {\n        href: \"https://firebase.google.com/docs/functions/firestore-events\",\n        children: \"Function Triggers\"\n      }), \", which are very cool.\\nSupabase Hooks are the same concept, just with a different name.\\nPostgres already has the concept of \", _jsx(_components.a, {\n        href: \"https://www.postgresql.org/docs/current/triggers.html\",\n        children: \"Triggers\"\n      }), \", and we thought this would be less confusing\", _jsx(_components.sup, {\n        children: _jsx(_components.a, {\n          href: \"#user-content-fn-1\",\n          id: \"user-content-fnref-1\",\n          \"data-footnote-ref\": true,\n          \"aria-describedby\": \"footnote-label\",\n          children: \"1\"\n        })\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"hook-events\",\n      children: \"Hook Events\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Database Webhooks allow you to \\\"listen\\\" to any change in your tables to trigger an asynchronous Function. You can hook into a few different events:\", _jsx(_components.code, {\n        children: \"INSERT\"\n      }), \", \", _jsx(_components.code, {\n        children: \"UPDATE\"\n      }), \", and \", _jsx(_components.code, {\n        children: \"DELETE\"\n      }), \". All events are fired \", _jsx(_components.strong, {\n        children: \"after\"\n      }), \" a database row is changed. Keen eyes will be able to spot the similarity to Postgres triggers, and that's because Database Webhooks are just a convenience wrapper around triggers.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/functions-updates/hook-events.png\",\n        alt: \"Hook Events\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"hook-targets\",\n      children: \"Hook Targets\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Supabase will support several different targets.\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"HTTP/Webhooks: Send HTTP requests directly from your Postgres Database.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"AWS Lambda/Google Cloud Run: Provide Supabase with a restricted IAM role to trigger Serverless functions natively.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Supabase Functions: We'll develop an end-to-end experience.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/functions-updates/supabase-function-hooks.png\",\n        alt: \"Supabase Database Webhooks\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"hook-payload\",\n      children: \"Hook Payload\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If the target is a Serverless function or an HTTP \", _jsx(_components.code, {\n        children: \"POST\"\n      }), \" request, the payload is automatically generated from the underlying table data. The format matches Supabase \", _jsx(_components.a, {\n        href: \"/docs/reference/javascript/subscribe\",\n        children: \"Realtime\"\n      }), \", except in this case you don't a client to \\\"listen\\\" to the changes. This provides yet another mechanism for responding to database changes.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"hideCopy\"],\n        \"active\": \"hideCopy\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"hideCopy\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"type \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"InsertPayload \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  type\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'INSERT'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  table\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"string\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  schema\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"string\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  record\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"TableRecord\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"T\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  old_record\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"null\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"type \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"UpdatePayload \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  type\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'UPDATE'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  table\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"string\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  schema\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"string\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  record\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"TableRecord\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"T\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  old_record\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"TableRecord\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"T\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"type \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"DeletePayload \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  type\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'DELETE'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  table\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"string\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  schema\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"string\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  record\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"null\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  old_record\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"TableRecord\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"T\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"ts\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.h2, {\n      id: \"hooks-technical-design-pg_net-v01\",\n      children: [\"Hooks technical design: \", _jsx(_components.code, {\n        children: \"pg_net v0.1\"\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"As with most of the Supabase platform, we leverage PostgreSQL's native functionality to implement Database Webhooks (previously called \\\"Function Hooks\\\").\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To build hooks, we've released a new PostgreSQL Extension, \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/pg_net/\",\n        children: \"pg_net\"\n      }), \", an asynchronous networking extension with an emphasis on scalability/throughput. In its initial (unstable) release we expose:\"]\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"asynchronous HTTP \", _jsx(_components.code, {\n          children: \"GET\"\n        }), \" requests.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"asynchronous HTTP \", _jsx(_components.code, {\n          children: \"POST\"\n        }), \" requests with a JSON payload.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The extension is (currently) capable of \u003e300 requests per second and is the networking layer underpinning Database Webhooks. For a complete view of capabilities, check out \", _jsx(_components.a, {\n        href: \"https://supabase.github.io/pg_net/api/\",\n        children: \"the docs\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"usage\",\n      children: _jsx(_components.strong, {\n        children: \"Usage\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.code, {\n        children: \"pg_net\"\n      }), \" allows you to make asynchronous HTTP requests directly within your SQL queries.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"-- Make a request\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"select\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    net\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"http_post\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        url\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'https://httpbin.org/post'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        body:\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'{\\\"hello\\\": \\\"world\\\"}'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"::jsonb\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    );\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"-- Immediately returns a response ID\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"http_post\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"---------\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        1\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"After making a request, the extension will return an ID. You can use this ID to collect a response.\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"hideCopy\"],\n        \"active\": \"hideCopy\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"hideCopy\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"-- Collect the response from a request\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"select\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  *\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"from\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  net\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"http_collect_response\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"1\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \");\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"-- Results (1 row)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"status\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"  | \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"message\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" | response\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"--------+---------+----------\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"SUCCESS        ok     (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                        status_code :\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"200\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                        headers     :\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'{\\\"date\\\": ...}'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                        body        :\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'{\\\"args\\\": ...}'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                      )::\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"net\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"http_response_result\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You can cast the response to JSON within PostgreSQL:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"-- Collect the response json payload from a request\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"select\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    (response).body::\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"json\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"from\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    net\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"http_collect_response\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"(request_id:\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"1\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \");\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Result:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"noCopy\"],\n        \"active\": \"noCopy\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"noCopy\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"   \\\"args\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": {},\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"   \\\"data\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\\\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"hello\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\\\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\\\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"world\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\\\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"}\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"   \\\"files\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": {},\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"   \\\"form\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": {},\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"   \\\"headers\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"     \\\"Accept\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"*/*\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"     \\\"Content-Length\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"18\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"     \\\"Content-Type\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"application/json\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"     \\\"Host\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"httpbin.org\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"     \\\"User-Agent\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"pg_net/0.1\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"     \\\"X-Amzn-Trace-Id\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"Root=1-61031a5c-7e1afeae69bffa8614d8e48e\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"   },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"   \\\"json\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"     \\\"hello\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"world\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"   },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"   \\\"origin\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"135.63.38.488\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"   \\\"url\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"https://httpbin.org/post\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \" }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"1\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" row)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"json\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"implementation\",\n      children: \"Implementation\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To build asynchronous behavior, we use a PostgreSQL \", _jsx(_components.a, {\n        href: \"https://www.postgresql.org/docs/current/bgworker.html\",\n        children: \"background worker\"\n      }), \" with a \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/pg_net/blob/3d52e7758909bb73bf7fa4586f42cea73ed239b6/sql/pg_net--0.1.sql#L11-L19\",\n        children: \"queue\"\n      }), \". This, coupled with the \", _jsx(_components.a, {\n        href: \"https://curl.se/libcurl/c/libcurl-multi.html\",\n        children: \"libcurl multi interface\"\n      }), \", enables us to do multiple simultaneous requests in the same background worker process.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Shout out to \", _jsx(_components.a, {\n        href: \"https://github.com/pramsey\",\n        children: \"Paul Ramsey\"\n      }), \", who gave us the implementation idea in \", _jsx(_components.a, {\n        href: \"https://github.com/pramsey/pgsql-http/#to-do\",\n        children: \"pgsql-http\"\n      }), \". While we originally hoped to add background workers to his extension, the implementation became too cumbersome and we decided to start with a clean slate. The advantage of being async can be seen by making some requests with both extensions:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"hideCopy\"],\n        \"active\": \"hideCopy\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"hideCopy\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"\\\\timing \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"on\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"-- using pgsql-http to fetch from \\\"supabase.io\\\" 10 times\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"select\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    *\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"from\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"   http_get(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'https://supabase.com'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"cross join\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"   generate_series\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"1\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"10\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \") _;\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"-- Returns in 3.5 seconds\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"Time\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"3501\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"935\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" ms\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"-- using pg_net to fetch from \\\"supabase.io\\\" 10 times\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"select\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    net\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"http_get\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'https://supabase.com'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"from\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    generate_series\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"1\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"10\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \") _;\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"-- Returns in 1.5 milliseconds\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"Time\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"1\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"562\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" ms\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Of course, the sync version waits until each request is completed to return the result, taking around 3.5 seconds for 10 requests; while the async version returns almost immediately in 1.5 milliseconds. This is really important for Supabase hooks, which run requests for every event fired from a SQL trigger - potentially thousands of requests per second.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"futureroadmap\",\n      children: \"Future/Roadmap\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This is only the beginning! First we'll thoroughly test it and make a stable release, then we expect to add support for\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"the remaining HTTP methods (\", _jsx(_components.code, {\n          children: \"PUT\"\n        }), \" / \", _jsx(_components.code, {\n          children: \"PATCH\"\n        }), \")\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"synchronous HTTP\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"additional protocols e.g. SMTP, FTP\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"more throughput (using epoll)\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"get-started-today\",\n      children: \"Get started today\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Database Webhooks is enabled today on all \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard\",\n        children: \"new projects\"\n      }), \". Find it under Database \u003e Alpha Preview \u003e Database Webhooks.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/functions-updates/enable-hooks.png\",\n        alt: \"Enable hooks\"\n      })\n    }), \"\\n\", _jsxs(_components.section, {\n      \"data-footnotes\": true,\n      className: \"footnotes\",\n      children: [_jsx(_components.h2, {\n        className: \"sr-only\",\n        id: \"footnote-label\",\n        children: \"Footnotes\"\n      }), \"\\n\", _jsxs(_components.ol, {\n        children: [\"\\n\", _jsxs(_components.li, {\n          id: \"user-content-fn-1\",\n          children: [\"\\n\", _jsxs(_components.p, {\n            children: [\"Postgres also has the concept of \", _jsx(_components.a, {\n              href: \"/blog/roles-postgres-hooks\",\n              children: \"Hooks\"\n            }), \", but they're more of an internal concept. \", _jsx(_components.a, {\n              href: \"#user-content-fnref-1\",\n              \"data-footnote-backref\": true,\n              className: \"data-footnote-backref\",\n              \"aria-label\": \"Back to content\",\n              children: \"↩\"\n            })]\n          }), \"\\n\"]\n        }), \"\\n\"]\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"PostgreSQL Functions","slug":"postgresql-functions","lvl":2,"i":0,"seen":0},{"content":"PostgreSQL Triggers","slug":"postgresql-triggers","lvl":2,"i":1,"seen":0},{"content":"Supabase Functions","slug":"supabase-functions","lvl":2,"i":2,"seen":0},{"content":"BYO Functions, zero lock-in","slug":"byo-functions-zero-lock-in","lvl":3,"i":3,"seen":0},{"content":"Timeline","slug":"timeline","lvl":3,"i":4,"seen":0},{"content":"Database Webhooks (Alpha)","slug":"database-webhooks-alpha","lvl":2,"i":5,"seen":0},{"content":"Hook Events","slug":"hook-events","lvl":3,"i":6,"seen":0},{"content":"Hook Targets","slug":"hook-targets","lvl":3,"i":7,"seen":0},{"content":"Hook Payload","slug":"hook-payload","lvl":3,"i":8,"seen":0},{"content":"Hooks technical design: `pg_net v0.1`","slug":"hooks-technical-design-pg_net-v01","lvl":2,"i":9,"seen":0},{"content":"**Usage**","slug":"usage","lvl":3,"i":10,"seen":0},{"content":"Implementation","slug":"implementation","lvl":3,"i":11,"seen":0},{"content":"Future/Roadmap","slug":"futureroadmap","lvl":3,"i":12,"seen":0},{"content":"Get started today","slug":"get-started-today","lvl":2,"i":13,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,2],"level":0},{"type":"inline","content":"The question on everyone's mind - are we launching Supabase Functions? Well, it's complicated.","level":1,"lines":[1,2],"children":[{"type":"text","content":"The question on everyone's mind - are we launching Supabase Functions? Well, it's complicated.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[3,4],"level":0},{"type":"inline","content":"Today we're announcing _part_ of Functions - Supabase Hooks - in Alpha, for all **new** projects.","level":1,"lines":[3,4],"children":[{"type":"text","content":"Today we're announcing ","level":0},{"type":"em_open","level":0},{"type":"text","content":"part","level":1},{"type":"em_close","level":0},{"type":"text","content":" of Functions - Supabase Hooks - in Alpha, for all ","level":0},{"type":"strong_open","level":0},{"type":"text","content":"new","level":1},{"type":"strong_close","level":0},{"type":"text","content":" projects.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[5,7],"level":0},{"type":"inline","content":"We're also releasing support for Postgres Functions and Triggers in our Dashboard, and some timelines for the rest of Supabase Functions.\nLet's cover the features we're launching today before the item that everyone is waiting for: Supabase Functions.","level":1,"lines":[5,7],"children":[{"type":"text","content":"We're also releasing support for Postgres Functions and Triggers in our Dashboard, and some timelines for the rest of Supabase Functions.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Let's cover the features we're launching today before the item that everyone is waiting for: Supabase Functions.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[8,9],"level":0},{"type":"inline","content":"[PostgreSQL Functions](#postgresql-functions)","level":1,"lines":[8,9],"children":[{"type":"text","content":"PostgreSQL Functions","level":0}],"lvl":2,"i":0,"seen":0,"slug":"postgresql-functions"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[10,11],"level":0},{"type":"inline","content":"(Not to be confused with Supabase Functions!)","level":1,"lines":[10,11],"children":[{"type":"text","content":"(Not to be confused with Supabase Functions!)","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[12,13],"level":0},{"type":"inline","content":"Postgres has built-in support for [SQL functions](https://www.postgresql.org/docs/current/sql-createfunction.html). Today we're making it even easier for developers to build PostgreSQL Functions by releasing a native Functions editor. Soon we'll release some handy templates!","level":1,"lines":[12,13],"children":[{"type":"text","content":"Postgres has built-in support for ","level":0},{"type":"link_open","href":"https://www.postgresql.org/docs/current/sql-createfunction.html","title":"","level":0},{"type":"text","content":"SQL functions","level":1},{"type":"link_close","level":0},{"type":"text","content":". Today we're making it even easier for developers to build PostgreSQL Functions by releasing a native Functions editor. Soon we'll release some handy templates!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[14,15],"level":0},{"type":"inline","content":"![Postgres Functions](/images/blog/functions-updates/postgres-functions.png)","level":1,"lines":[14,15],"children":[{"type":"image","src":"/images/blog/functions-updates/postgres-functions.png","title":"","alt":"Postgres Functions","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[16,17],"level":0},{"type":"inline","content":"You can call PostgreSQL Functions with `supabase-js` using your project API [[Docs](/docs/reference/javascript/rpc)]:","level":1,"lines":[16,17],"children":[{"type":"text","content":"You can call PostgreSQL Functions with ","level":0},{"type":"code","content":"supabase-js","block":false,"level":0},{"type":"text","content":" using your project API [","level":0},{"type":"link_open","href":"/docs/reference/javascript/rpc","title":"","level":0},{"type":"text","content":"Docs","level":1},{"type":"link_close","level":0},{"type":"text","content":"]:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"js hideCopy","content":"const { data, error } = await supabase.rpc('best_star_wars_series', {\n  name: 'The Prequels',\n})\n","lines":[18,23],"level":0},{"type":"heading_open","hLevel":2,"lines":[24,25],"level":0},{"type":"inline","content":"[PostgreSQL Triggers](#postgresql-triggers)","level":1,"lines":[24,25],"children":[{"type":"text","content":"PostgreSQL Triggers","level":0}],"lvl":2,"i":1,"seen":0,"slug":"postgresql-triggers"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[26,27],"level":0},{"type":"inline","content":"[Triggers](https://www.postgresql.org/docs/current/trigger-definition.html) are another amazing feature of Postgres, which allows you to execute any SQL code after inserting, updating, or deleting data.","level":1,"lines":[26,27],"children":[{"type":"link_open","href":"https://www.postgresql.org/docs/current/trigger-definition.html","title":"","level":0},{"type":"text","content":"Triggers","level":1},{"type":"link_close","level":0},{"type":"text","content":" are another amazing feature of Postgres, which allows you to execute any SQL code after inserting, updating, or deleting data.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[28,29],"level":0},{"type":"inline","content":"While triggers are a staple of Database Administrators, they can be a bit complex and hard to use. We plan to change that with a simple interface for building and managing PostgreSQL triggers.","level":1,"lines":[28,29],"children":[{"type":"text","content":"While triggers are a staple of Database Administrators, they can be a bit complex and hard to use. We plan to change that with a simple interface for building and managing PostgreSQL triggers.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[30,31],"level":0},{"type":"inline","content":"![Postgres Triggers](/images/blog/functions-updates/postgres-triggers.png)","level":1,"lines":[30,31],"children":[{"type":"image","src":"/images/blog/functions-updates/postgres-triggers.png","title":"","alt":"Postgres Triggers","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[32,33],"level":0},{"type":"inline","content":"[Supabase Functions](#supabase-functions)","level":1,"lines":[32,33],"children":[{"type":"text","content":"Supabase Functions","level":0}],"lvl":2,"i":2,"seen":0,"slug":"supabase-functions"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[34,35],"level":0},{"type":"inline","content":"They say building a startup is like jumping off a cliff and assembling the plane on the way down. At Supabase it's more like assembling a 747 since, although we're still in Beta, thousands of companies depend on us to power their apps and websites.","level":1,"lines":[34,35],"children":[{"type":"text","content":"They say building a startup is like jumping off a cliff and assembling the plane on the way down. At Supabase it's more like assembling a 747 since, although we're still in Beta, thousands of companies depend on us to power their apps and websites.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[36,37],"level":0},{"type":"inline","content":"For the past few months we've been designing Supabase Functions based on our customer feedback.","level":1,"lines":[36,37],"children":[{"type":"text","content":"For the past few months we've been designing Supabase Functions based on our customer feedback.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[38,39],"level":0},{"type":"inline","content":"[BYO Functions, zero lock-in](#byo-functions-zero-lock-in)","level":1,"lines":[38,39],"children":[{"type":"text","content":"BYO Functions, zero lock-in","level":0}],"lvl":3,"i":3,"seen":0,"slug":"byo-functions-zero-lock-in"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[40,42],"level":0},{"type":"inline","content":"A recurring request from our customers is the ability to trigger their _existing_ Functions.\nThis is especially true for our Enterprise customers, but also Jamstack developers who develop API Functions directly within their stack (like Next.js [API routes](https://nextjs.org/docs/api-routes/introduction), or Redwood [Serverless Functions](https://redwoodjs.com/docs/serverless-functions)).","level":1,"lines":[40,42],"children":[{"type":"text","content":"A recurring request from our customers is the ability to trigger their ","level":0},{"type":"em_open","level":0},{"type":"text","content":"existing","level":1},{"type":"em_close","level":0},{"type":"text","content":" Functions.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"This is especially true for our Enterprise customers, but also Jamstack developers who develop API Functions directly within their stack (like Next.js ","level":0},{"type":"link_open","href":"https://nextjs.org/docs/api-routes/introduction","title":"","level":0},{"type":"text","content":"API routes","level":1},{"type":"link_close","level":0},{"type":"text","content":", or Redwood ","level":0},{"type":"link_open","href":"https://redwoodjs.com/docs/serverless-functions","title":"","level":0},{"type":"text","content":"Serverless Functions","level":1},{"type":"link_close","level":0},{"type":"text","content":").","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[43,44],"level":0},{"type":"inline","content":"[Timeline](#timeline)","level":1,"lines":[43,44],"children":[{"type":"text","content":"Timeline","level":0}],"lvl":3,"i":4,"seen":0,"slug":"timeline"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[45,46],"level":0},{"type":"inline","content":"To meet these goals, we're releasing Supabase Functions in stages:","level":1,"lines":[45,46],"children":[{"type":"text","content":"To meet these goals, we're releasing Supabase Functions in stages:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[47,51],"level":0},{"type":"list_item_open","lines":[47,48],"level":1},{"type":"paragraph_open","tight":true,"lines":[47,48],"level":2},{"type":"inline","content":"_Stage 1:_ Give developers the ability to trigger external HTTP functions - today, using Database Webhooks.","level":3,"lines":[47,48],"children":[{"type":"em_open","level":0},{"type":"text","content":"Stage 1:","level":1},{"type":"em_close","level":0},{"type":"text","content":" Give developers the ability to trigger external HTTP functions - today, using Database Webhooks.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[48,49],"level":1},{"type":"paragraph_open","tight":true,"lines":[48,49],"level":2},{"type":"inline","content":"_Stage 2:_ Give developers the ability to trigger their own Serverless functions on AWS and GCP - Q4 2021.","level":3,"lines":[48,49],"children":[{"type":"em_open","level":0},{"type":"text","content":"Stage 2:","level":1},{"type":"em_close","level":0},{"type":"text","content":" Give developers the ability to trigger their own Serverless functions on AWS and GCP - Q4 2021.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[49,51],"level":1},{"type":"paragraph_open","tight":true,"lines":[49,50],"level":2},{"type":"inline","content":"_Stage 3:_ Release our own Serverless Functions (Supabase Functions) - Q4 for Early Preview customers.","level":3,"lines":[49,50],"children":[{"type":"em_open","level":0},{"type":"text","content":"Stage 3:","level":1},{"type":"em_close","level":0},{"type":"text","content":" Release our own Serverless Functions (Supabase Functions) - Q4 for Early Preview customers.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[51,52],"level":0},{"type":"inline","content":"![Supabase Functions timeline](/images/blog/functions-updates/functions-timeline.png)","level":1,"lines":[51,52],"children":[{"type":"image","src":"/images/blog/functions-updates/functions-timeline.png","title":"","alt":"Supabase Functions timeline","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[53,54],"level":0},{"type":"inline","content":"[Database Webhooks (Alpha)](#database-webhooks-alpha)","level":1,"lines":[53,54],"children":[{"type":"text","content":"Database Webhooks (Alpha)","level":0}],"lvl":2,"i":5,"seen":0,"slug":"database-webhooks-alpha"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[55,56],"level":0},{"type":"inline","content":"(Note: Database Webhooks were previously called \"Function Hooks\")","level":1,"lines":[55,56],"children":[{"type":"text","content":"(Note: Database Webhooks were previously called \"Function Hooks\")","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[57,58],"level":0},{"type":"inline","content":"Today we're releasing Functions Hooks in `ALPHA`. The `ALPHA` tag means that it is NOT stable, but it's available for testing and feedback. The API will change, so do not use it for anything critical. You have been warned.","level":1,"lines":[57,58],"children":[{"type":"text","content":"Today we're releasing Functions Hooks in ","level":0},{"type":"code","content":"ALPHA","block":false,"level":0},{"type":"text","content":". The ","level":0},{"type":"code","content":"ALPHA","block":false,"level":0},{"type":"text","content":" tag means that it is NOT stable, but it's available for testing and feedback. The API will change, so do not use it for anything critical. You have been warned.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[59,62],"level":0},{"type":"inline","content":"Hooks? Triggers? Firestore has the concept of [Function Triggers](https://firebase.google.com/docs/functions/firestore-events), which are very cool.\nSupabase Hooks are the same concept, just with a different name.\nPostgres already has the concept of [Triggers](https://www.postgresql.org/docs/current/triggers.html), and we thought this would be less confusing[^1].","level":1,"lines":[59,62],"children":[{"type":"text","content":"Hooks? Triggers? Firestore has the concept of ","level":0},{"type":"link_open","href":"https://firebase.google.com/docs/functions/firestore-events","title":"","level":0},{"type":"text","content":"Function Triggers","level":1},{"type":"link_close","level":0},{"type":"text","content":", which are very cool.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Supabase Hooks are the same concept, just with a different name.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Postgres already has the concept of ","level":0},{"type":"link_open","href":"https://www.postgresql.org/docs/current/triggers.html","title":"","level":0},{"type":"text","content":"Triggers","level":1},{"type":"link_close","level":0},{"type":"text","content":", and we thought this would be less confusing","level":0},{"type":"footnote_ref","id":0,"subId":0,"level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[63,64],"level":0},{"type":"inline","content":"[Hook Events](#hook-events)","level":1,"lines":[63,64],"children":[{"type":"text","content":"Hook Events","level":0}],"lvl":3,"i":6,"seen":0,"slug":"hook-events"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[65,66],"level":0},{"type":"inline","content":"Database Webhooks allow you to \"listen\" to any change in your tables to trigger an asynchronous Function. You can hook into a few different events:`INSERT`, `UPDATE`, and `DELETE`. All events are fired **after** a database row is changed. Keen eyes will be able to spot the similarity to Postgres triggers, and that's because Database Webhooks are just a convenience wrapper around triggers.","level":1,"lines":[65,66],"children":[{"type":"text","content":"Database Webhooks allow you to \"listen\" to any change in your tables to trigger an asynchronous Function. You can hook into a few different events:","level":0},{"type":"code","content":"INSERT","block":false,"level":0},{"type":"text","content":", ","level":0},{"type":"code","content":"UPDATE","block":false,"level":0},{"type":"text","content":", and ","level":0},{"type":"code","content":"DELETE","block":false,"level":0},{"type":"text","content":". All events are fired ","level":0},{"type":"strong_open","level":0},{"type":"text","content":"after","level":1},{"type":"strong_close","level":0},{"type":"text","content":" a database row is changed. Keen eyes will be able to spot the similarity to Postgres triggers, and that's because Database Webhooks are just a convenience wrapper around triggers.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[67,68],"level":0},{"type":"inline","content":"![Hook Events](/images/blog/functions-updates/hook-events.png)","level":1,"lines":[67,68],"children":[{"type":"image","src":"/images/blog/functions-updates/hook-events.png","title":"","alt":"Hook Events","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[69,70],"level":0},{"type":"inline","content":"[Hook Targets](#hook-targets)","level":1,"lines":[69,70],"children":[{"type":"text","content":"Hook Targets","level":0}],"lvl":3,"i":7,"seen":0,"slug":"hook-targets"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[71,72],"level":0},{"type":"inline","content":"Supabase will support several different targets.","level":1,"lines":[71,72],"children":[{"type":"text","content":"Supabase will support several different targets.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[73,77],"level":0},{"type":"list_item_open","lines":[73,74],"level":1},{"type":"paragraph_open","tight":true,"lines":[73,74],"level":2},{"type":"inline","content":"HTTP/Webhooks: Send HTTP requests directly from your Postgres Database.","level":3,"lines":[73,74],"children":[{"type":"text","content":"HTTP/Webhooks: Send HTTP requests directly from your Postgres Database.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[74,75],"level":1},{"type":"paragraph_open","tight":true,"lines":[74,75],"level":2},{"type":"inline","content":"AWS Lambda/Google Cloud Run: Provide Supabase with a restricted IAM role to trigger Serverless functions natively.","level":3,"lines":[74,75],"children":[{"type":"text","content":"AWS Lambda/Google Cloud Run: Provide Supabase with a restricted IAM role to trigger Serverless functions natively.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[75,77],"level":1},{"type":"paragraph_open","tight":true,"lines":[75,76],"level":2},{"type":"inline","content":"Supabase Functions: We'll develop an end-to-end experience.","level":3,"lines":[75,76],"children":[{"type":"text","content":"Supabase Functions: We'll develop an end-to-end experience.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[77,78],"level":0},{"type":"inline","content":"![Supabase Database Webhooks](/images/blog/functions-updates/supabase-function-hooks.png)","level":1,"lines":[77,78],"children":[{"type":"image","src":"/images/blog/functions-updates/supabase-function-hooks.png","title":"","alt":"Supabase Database Webhooks","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[79,80],"level":0},{"type":"inline","content":"[Hook Payload](#hook-payload)","level":1,"lines":[79,80],"children":[{"type":"text","content":"Hook Payload","level":0}],"lvl":3,"i":8,"seen":0,"slug":"hook-payload"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[81,82],"level":0},{"type":"inline","content":"If the target is a Serverless function or an HTTP `POST` request, the payload is automatically generated from the underlying table data. The format matches Supabase [Realtime](/docs/reference/javascript/subscribe), except in this case you don't a client to \"listen\" to the changes. This provides yet another mechanism for responding to database changes.","level":1,"lines":[81,82],"children":[{"type":"text","content":"If the target is a Serverless function or an HTTP ","level":0},{"type":"code","content":"POST","block":false,"level":0},{"type":"text","content":" request, the payload is automatically generated from the underlying table data. The format matches Supabase ","level":0},{"type":"link_open","href":"/docs/reference/javascript/subscribe","title":"","level":0},{"type":"text","content":"Realtime","level":1},{"type":"link_close","level":0},{"type":"text","content":", except in this case you don't a client to \"listen\" to the changes. This provides yet another mechanism for responding to database changes.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"ts hideCopy","content":"type InsertPayload = {\n  type: 'INSERT'\n  table: string\n  schema: string\n  record: TableRecord\u003cT\u003e\n  old_record: null\n}\ntype UpdatePayload = {\n  type: 'UPDATE'\n  table: string\n  schema: string\n  record: TableRecord\u003cT\u003e\n  old_record: TableRecord\u003cT\u003e\n}\ntype DeletePayload = {\n  type: 'DELETE'\n  table: string\n  schema: string\n  record: null\n  old_record: TableRecord\u003cT\u003e\n}\n","lines":[83,106],"level":0},{"type":"heading_open","hLevel":2,"lines":[107,108],"level":0},{"type":"inline","content":"[Hooks technical design: `pg_net v0.1`](#hooks-technical-design-pg_net-v01)","level":1,"lines":[107,108],"children":[{"type":"text","content":"Hooks technical design: ","level":0},{"type":"code","content":"pg_net v0.1","block":false,"level":0}],"lvl":2,"i":9,"seen":0,"slug":"hooks-technical-design-pg_net-v01"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[109,110],"level":0},{"type":"inline","content":"As with most of the Supabase platform, we leverage PostgreSQL's native functionality to implement Database Webhooks (previously called \"Function Hooks\").","level":1,"lines":[109,110],"children":[{"type":"text","content":"As with most of the Supabase platform, we leverage PostgreSQL's native functionality to implement Database Webhooks (previously called \"Function Hooks\").","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[111,112],"level":0},{"type":"inline","content":"To build hooks, we've released a new PostgreSQL Extension, [pg_net](https://github.com/supabase/pg_net/), an asynchronous networking extension with an emphasis on scalability/throughput. In its initial (unstable) release we expose:","level":1,"lines":[111,112],"children":[{"type":"text","content":"To build hooks, we've released a new PostgreSQL Extension, ","level":0},{"type":"link_open","href":"https://github.com/supabase/pg_net/","title":"","level":0},{"type":"text","content":"pg_net","level":1},{"type":"link_close","level":0},{"type":"text","content":", an asynchronous networking extension with an emphasis on scalability/throughput. In its initial (unstable) release we expose:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[113,116],"level":0},{"type":"list_item_open","lines":[113,114],"level":1},{"type":"paragraph_open","tight":true,"lines":[113,114],"level":2},{"type":"inline","content":"asynchronous HTTP `GET` requests.","level":3,"lines":[113,114],"children":[{"type":"text","content":"asynchronous HTTP ","level":0},{"type":"code","content":"GET","block":false,"level":0},{"type":"text","content":" requests.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[114,116],"level":1},{"type":"paragraph_open","tight":true,"lines":[114,115],"level":2},{"type":"inline","content":"asynchronous HTTP `POST` requests with a JSON payload.","level":3,"lines":[114,115],"children":[{"type":"text","content":"asynchronous HTTP ","level":0},{"type":"code","content":"POST","block":false,"level":0},{"type":"text","content":" requests with a JSON payload.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[116,117],"level":0},{"type":"inline","content":"The extension is (currently) capable of \u003e300 requests per second and is the networking layer underpinning Database Webhooks. For a complete view of capabilities, check out [the docs](https://supabase.github.io/pg_net/api/).","level":1,"lines":[116,117],"children":[{"type":"text","content":"The extension is (currently) capable of \u003e300 requests per second and is the networking layer underpinning Database Webhooks. For a complete view of capabilities, check out ","level":0},{"type":"link_open","href":"https://supabase.github.io/pg_net/api/","title":"","level":0},{"type":"text","content":"the docs","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[118,119],"level":0},{"type":"inline","content":"[**Usage**](#usage)","level":1,"lines":[118,119],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Usage","level":1},{"type":"strong_close","level":0}],"lvl":3,"i":10,"seen":0,"slug":"usage"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[120,121],"level":0},{"type":"inline","content":"`pg_net` allows you to make asynchronous HTTP requests directly within your SQL queries.","level":1,"lines":[120,121],"children":[{"type":"code","content":"pg_net","block":false,"level":0},{"type":"text","content":" allows you to make asynchronous HTTP requests directly within your SQL queries.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql","content":"-- Make a request\nselect\n    net.http_post(\n        url:='https://httpbin.org/post',\n        body:='{\"hello\": \"world\"}'::jsonb\n    );\n\n-- Immediately returns a response ID\nhttp_post\n---------\n        1\n","lines":[122,135],"level":0},{"type":"paragraph_open","tight":false,"lines":[136,137],"level":0},{"type":"inline","content":"After making a request, the extension will return an ID. You can use this ID to collect a response.","level":1,"lines":[136,137],"children":[{"type":"text","content":"After making a request, the extension will return an ID. You can use this ID to collect a response.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql hideCopy","content":"-- Collect the response from a request\nselect\n  *\nfrom\n  net.http_collect_response(1);\n\n-- Results (1 row)\nstatus  | message | response\n--------+---------+----------\nSUCCESS        ok     (\n                        status_code := 200,\n                        headers     := '{\"date\": ...}',\n                        body        := '{\"args\": ...}'\n                      )::net.http_response_result\n","lines":[138,154],"level":0},{"type":"paragraph_open","tight":false,"lines":[155,156],"level":0},{"type":"inline","content":"You can cast the response to JSON within PostgreSQL:","level":1,"lines":[155,156],"children":[{"type":"text","content":"You can cast the response to JSON within PostgreSQL:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql","content":"-- Collect the response json payload from a request\nselect\n    (response).body::json\nfrom\n    net.http_collect_response(request_id:=1);\n","lines":[157,164],"level":0},{"type":"paragraph_open","tight":false,"lines":[165,166],"level":0},{"type":"inline","content":"Result:","level":1,"lines":[165,166],"children":[{"type":"text","content":"Result:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"json noCopy","content":" {\n   \"args\": {},\n   \"data\": \"{\\\"hello\\\": \\\"world\\\"}\",\n   \"files\": {},\n   \"form\": {},\n   \"headers\": {\n     \"Accept\": \"*/*\",\n     \"Content-Length\": \"18\",\n     \"Content-Type\": \"application/json\",\n     \"Host\": \"httpbin.org\",\n     \"User-Agent\": \"pg_net/0.1\",\n     \"X-Amzn-Trace-Id\": \"Root=1-61031a5c-7e1afeae69bffa8614d8e48e\"\n   },\n   \"json\": {\n     \"hello\": \"world\"\n   },\n   \"origin\": \"135.63.38.488\",\n   \"url\": \"https://httpbin.org/post\"\n }\n(1 row)\n","lines":[167,189],"level":0},{"type":"heading_open","hLevel":3,"lines":[190,191],"level":0},{"type":"inline","content":"[Implementation](#implementation)","level":1,"lines":[190,191],"children":[{"type":"text","content":"Implementation","level":0}],"lvl":3,"i":11,"seen":0,"slug":"implementation"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[192,193],"level":0},{"type":"inline","content":"To build asynchronous behavior, we use a PostgreSQL [background worker](https://www.postgresql.org/docs/current/bgworker.html) with a [queue](https://github.com/supabase/pg_net/blob/3d52e7758909bb73bf7fa4586f42cea73ed239b6/sql/pg_net--0.1.sql#L11-L19). This, coupled with the [libcurl multi interface](https://curl.se/libcurl/c/libcurl-multi.html), enables us to do multiple simultaneous requests in the same background worker process.","level":1,"lines":[192,193],"children":[{"type":"text","content":"To build asynchronous behavior, we use a PostgreSQL ","level":0},{"type":"link_open","href":"https://www.postgresql.org/docs/current/bgworker.html","title":"","level":0},{"type":"text","content":"background worker","level":1},{"type":"link_close","level":0},{"type":"text","content":" with a ","level":0},{"type":"link_open","href":"https://github.com/supabase/pg_net/blob/3d52e7758909bb73bf7fa4586f42cea73ed239b6/sql/pg_net--0.1.sql#L11-L19","title":"","level":0},{"type":"text","content":"queue","level":1},{"type":"link_close","level":0},{"type":"text","content":". This, coupled with the ","level":0},{"type":"link_open","href":"https://curl.se/libcurl/c/libcurl-multi.html","title":"","level":0},{"type":"text","content":"libcurl multi interface","level":1},{"type":"link_close","level":0},{"type":"text","content":", enables us to do multiple simultaneous requests in the same background worker process.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[194,195],"level":0},{"type":"inline","content":"Shout out to [Paul Ramsey](https://github.com/pramsey), who gave us the implementation idea in [pgsql-http](https://github.com/pramsey/pgsql-http/#to-do). While we originally hoped to add background workers to his extension, the implementation became too cumbersome and we decided to start with a clean slate. The advantage of being async can be seen by making some requests with both extensions:","level":1,"lines":[194,195],"children":[{"type":"text","content":"Shout out to ","level":0},{"type":"link_open","href":"https://github.com/pramsey","title":"","level":0},{"type":"text","content":"Paul Ramsey","level":1},{"type":"link_close","level":0},{"type":"text","content":", who gave us the implementation idea in ","level":0},{"type":"link_open","href":"https://github.com/pramsey/pgsql-http/#to-do","title":"","level":0},{"type":"text","content":"pgsql-http","level":1},{"type":"link_close","level":0},{"type":"text","content":". While we originally hoped to add background workers to his extension, the implementation became too cumbersome and we decided to start with a clean slate. The advantage of being async can be seen by making some requests with both extensions:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql hideCopy","content":"\\timing on\n\n-- using pgsql-http to fetch from \"supabase.io\" 10 times\nselect\n    *\nfrom\n   http_get('https://supabase.com')\ncross join\n   generate_series(1, 10) _;\n\n-- Returns in 3.5 seconds\nTime: 3501.935 ms\n\n-- using pg_net to fetch from \"supabase.io\" 10 times\nselect\n    net.http_get('https://supabase.com')\nfrom\n    generate_series (1,10) _;\n\n-- Returns in 1.5 milliseconds\nTime: 1.562 ms\n","lines":[196,219],"level":0},{"type":"paragraph_open","tight":false,"lines":[220,221],"level":0},{"type":"inline","content":"Of course, the sync version waits until each request is completed to return the result, taking around 3.5 seconds for 10 requests; while the async version returns almost immediately in 1.5 milliseconds. This is really important for Supabase hooks, which run requests for every event fired from a SQL trigger - potentially thousands of requests per second.","level":1,"lines":[220,221],"children":[{"type":"text","content":"Of course, the sync version waits until each request is completed to return the result, taking around 3.5 seconds for 10 requests; while the async version returns almost immediately in 1.5 milliseconds. This is really important for Supabase hooks, which run requests for every event fired from a SQL trigger - potentially thousands of requests per second.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[222,223],"level":0},{"type":"inline","content":"[Future/Roadmap](#futureroadmap)","level":1,"lines":[222,223],"children":[{"type":"text","content":"Future/Roadmap","level":0}],"lvl":3,"i":12,"seen":0,"slug":"futureroadmap"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[224,225],"level":0},{"type":"inline","content":"This is only the beginning! First we'll thoroughly test it and make a stable release, then we expect to add support for","level":1,"lines":[224,225],"children":[{"type":"text","content":"This is only the beginning! First we'll thoroughly test it and make a stable release, then we expect to add support for","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[226,231],"level":0},{"type":"list_item_open","lines":[226,227],"level":1},{"type":"paragraph_open","tight":true,"lines":[226,227],"level":2},{"type":"inline","content":"the remaining HTTP methods (`PUT` / `PATCH`)","level":3,"lines":[226,227],"children":[{"type":"text","content":"the remaining HTTP methods (","level":0},{"type":"code","content":"PUT","block":false,"level":0},{"type":"text","content":" / ","level":0},{"type":"code","content":"PATCH","block":false,"level":0},{"type":"text","content":")","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[227,228],"level":1},{"type":"paragraph_open","tight":true,"lines":[227,228],"level":2},{"type":"inline","content":"synchronous HTTP","level":3,"lines":[227,228],"children":[{"type":"text","content":"synchronous HTTP","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[228,229],"level":1},{"type":"paragraph_open","tight":true,"lines":[228,229],"level":2},{"type":"inline","content":"additional protocols e.g. SMTP, FTP","level":3,"lines":[228,229],"children":[{"type":"text","content":"additional protocols e.g. SMTP, FTP","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[229,231],"level":1},{"type":"paragraph_open","tight":true,"lines":[229,230],"level":2},{"type":"inline","content":"more throughput (using epoll)","level":3,"lines":[229,230],"children":[{"type":"text","content":"more throughput (using epoll)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":2,"lines":[231,232],"level":0},{"type":"inline","content":"[Get started today](#get-started-today)","level":1,"lines":[231,232],"children":[{"type":"text","content":"Get started today","level":0}],"lvl":2,"i":13,"seen":0,"slug":"get-started-today"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[233,234],"level":0},{"type":"inline","content":"Database Webhooks is enabled today on all [new projects](https://supabase.com/dashboard). Find it under Database \u003e Alpha Preview \u003e Database Webhooks.","level":1,"lines":[233,234],"children":[{"type":"text","content":"Database Webhooks is enabled today on all ","level":0},{"type":"link_open","href":"https://supabase.com/dashboard","title":"","level":0},{"type":"text","content":"new projects","level":1},{"type":"link_close","level":0},{"type":"text","content":". Find it under Database \u003e Alpha Preview \u003e Database Webhooks.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[235,236],"level":0},{"type":"inline","content":"![Enable hooks](/images/blog/functions-updates/enable-hooks.png)","level":1,"lines":[235,236],"children":[{"type":"image","src":"/images/blog/functions-updates/enable-hooks.png","title":"","alt":"Enable hooks","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"footnote_block_open","level":0},{"type":"footnote_open","id":0,"level":1},{"type":"paragraph_open","tight":false,"lines":[237,238],"level":1},{"type":"inline","content":"Postgres also has the concept of [Hooks](/blog/roles-postgres-hooks), but they're more of an internal concept.","level":2,"lines":[237,238],"children":[{"type":"text","content":"Postgres also has the concept of ","level":0},{"type":"link_open","href":"/blog/roles-postgres-hooks","title":"","level":0},{"type":"text","content":"Hooks","level":1},{"type":"link_close","level":0},{"type":"text","content":", but they're more of an internal concept.","level":0}]},{"type":"footnote_anchor","id":0,"subId":0,"level":2},{"type":"paragraph_close","tight":false,"level":1},{"type":"footnote_close","level":1},{"type":"footnote_block_close","level":0}],"content":"- [PostgreSQL Functions](#postgresql-functions)\n- [PostgreSQL Triggers](#postgresql-triggers)\n- [Supabase Functions](#supabase-functions)\n  * [BYO Functions, zero lock-in](#byo-functions-zero-lock-in)\n  * [Timeline](#timeline)\n- [Database Webhooks (Alpha)](#database-webhooks-alpha)\n  * [Hook Events](#hook-events)\n  * [Hook Targets](#hook-targets)\n  * [Hook Payload](#hook-payload)\n- [Hooks technical design: `pg_net v0.1`](#hooks-technical-design-pg_net-v01)\n  * [**Usage**](#usage)\n  * [Implementation](#implementation)\n  * [Future/Roadmap](#futureroadmap)\n- [Get started today](#get-started-today)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-functions-updates"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>