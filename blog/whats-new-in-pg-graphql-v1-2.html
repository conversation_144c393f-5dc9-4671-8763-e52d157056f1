<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">What&#x27;s New in pg_graphql v1.2</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="New Features in the v1.2 release of pg_graphql" data-next-head=""/><meta property="og:title" content="What&#x27;s New in pg_graphql v1.2" data-next-head=""/><meta property="og:description" content="New Features in the v1.2 release of pg_graphql" data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/whats-new-in-pg-graphql-v1-2" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2023-04-21" data-next-head=""/><meta property="article:author" content="https://github.com/olirice" data-next-head=""/><meta property="article:tag" content="postgres" data-next-head=""/><meta property="article:tag" content="graphql" data-next-head=""/><meta property="article:tag" content="planetpg" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/launch-week-6/pggraphql/og-pg-graphql.png" data-next-head=""/><meta property="og:image:alt" content="What&#x27;s New in pg_graphql v1.2 thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">What&#x27;s New in pg_graphql v1.2</h1><div class="text-light flex space-x-3 text-sm"><p>21 Apr 2023</p><p>•</p><p>6 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/olirice"><div class="flex items-center gap-3"><div class="w-10"><img alt="Oliver Rice avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Folirice.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Folirice.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Folirice.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Oliver Rice</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="What&#x27;s New in pg_graphql v1.2" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-6%2Fpggraphql%2Fog-pg-graphql.png&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-6%2Fpggraphql%2Fog-pg-graphql.png&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-6%2Fpggraphql%2Fog-pg-graphql.png&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-6%2Fpggraphql%2Fog-pg-graphql.png&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-6%2Fpggraphql%2Fog-pg-graphql.png&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-6%2Fpggraphql%2Fog-pg-graphql.png&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-6%2Fpggraphql%2Fog-pg-graphql.png&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-6%2Fpggraphql%2Fog-pg-graphql.png&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-6%2Fpggraphql%2Fog-pg-graphql.png&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-6%2Fpggraphql%2Fog-pg-graphql.png&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-6%2Fpggraphql%2Fog-pg-graphql.png&amp;w=3840&amp;q=100"/></div><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p>It&#x27;s been 4 months since the <a href="https://supabase.com/blog/pg-graphql-v1">1.0.0 release of pg_graphql</a>.
Since then, we’ve pushed several features to improve the APIs that <code class="short-inline-codeblock">pg_graphql</code> produces.</p>
<p>In this article, we’ll walk through those features and show examples of each.</p>
<div class="bg-gray-300 rounded-lg px-6 py-2 italic"><p>📢 These features are only available on projects with Postgres version <code class="short-inline-codeblock">*********</code> or higher. For help with upgrading, please review the <a href="https://supabase.com/docs/guides/platform/migrating-and-upgrading-projects">migrating and upgrading projects guide</a>.</p></div>
<h3 id="view-support" class="group scroll-mt-24">View Support<a href="#view-support" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Prior to <code class="short-inline-codeblock">v1.1</code>, <code class="short-inline-codeblock">pg_graphql</code> would only reflect standard tables. Since then, views, materialized views, and foreign tables are now also reflected in the GraphQL schema.</p>
<p>For example:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>create view &quot;ProjectOwner&quot; as</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  select</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    acc.id,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    acc.name</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  from</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    account as acc</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    join role as r on r.id = acc.role_id</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  where acc.role = &#x27;project_owner&#x27;;</span></div></div><br/></code></div></div>
<p>Since all entities exposed by <code class="short-inline-codeblock">pg_graphql</code> require primary keys, we must define that constraint for the view. We do that using a comment directive:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>comment on view &quot;ProjectOwner&quot;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  is &#x27;@graphql({&quot;primary_key_columns&quot;: [&quot;id&quot;]})&#x27;;</span></div></div><br/></code></div></div>
<p>Which yields the GraphQL type:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>type ProjectOwner implements Node {</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  nodeId: ID!</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  id: UUID!</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  name: String</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<p>With associated <code class="short-inline-codeblock">Edge</code> and <code class="short-inline-codeblock">Connection</code> types. That enables querying via:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>{</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  projectOwnerCollection(first: 2) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    edges {</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>      node {</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>        nodeId</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>        name</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>      }</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    }</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<p>Additionally, simple views automatically support mutation events like inserts and updates. You might use these to migrate underlying tables while maintaining backwards compatibility with previous API versions.</p>
<h2 id="filtering" class="group scroll-mt-24">Filtering<a href="#filtering" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Filtering in SQL is endlessly flexible. We’ve taken two incremental steps to bring more of that flexibility to the GraphQL interface.</p>
<h3 id="is-null-and-is-not-null" class="group scroll-mt-24"><code class="short-inline-codeblock">is null</code> and <code class="short-inline-codeblock">is not null</code><a href="#is-null-and-is-not-null" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Handling <code class="short-inline-codeblock">null</code> values can be tricky in both SQL and GraphQL. However, there are similarities we can take advantage of.
In <code class="short-inline-codeblock">pg_graphql</code>, every scalar data type has its own filter type, such as <code class="short-inline-codeblock">IntFilter</code> and <code class="short-inline-codeblock">StringFilter</code>.
Each of these filter types now includes an <code class="short-inline-codeblock">is</code> argument, which allows you to filter based on whether a value is null or not null.
You can do this by using <code class="short-inline-codeblock">{is: NULL}</code> for <code class="short-inline-codeblock">null</code> values and <code class="short-inline-codeblock">{is: NOT_NULL}</code> for non-<code class="short-inline-codeblock">null</code> values.</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>enum FilterIs {</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    NULL</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    NOT_NULL</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>type IntFilter {</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    ...</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    is: FilterIs</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<p>For example:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>{</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  blogCollection(filter: { name: {is: NULL}}) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    ...</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<p>to return all <code class="short-inline-codeblock">blog</code>s where the <code class="short-inline-codeblock">name</code> is <code class="short-inline-codeblock">null</code>.</p>
<h3 id="like-ilike-and-startswith" class="group scroll-mt-24"><strong><code class="short-inline-codeblock">like</code></strong>, <strong><code class="short-inline-codeblock">ilike</code></strong>, and <strong><code class="short-inline-codeblock">startsWith</code></strong><a href="#like-ilike-and-startswith" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Text filtering options in <code class="short-inline-codeblock">pg_graphql</code> have historically been restricted to equality checks. The hesitation was due to concerns about exposing a default filter that is difficult to index. The combination of <a href="https://www.postgresql.org/docs/current/citext.html">citext</a> and <a href="https://supabase.com/docs/guides/database/extensions/pgroonga">PGroonga available on the platform</a> solves those scalability risks and enabled us to expand the <code class="short-inline-codeblock">StringFilter</code> with options for <code class="short-inline-codeblock">like</code> <code class="short-inline-codeblock">ilike</code> and <code class="short-inline-codeblock">startsWith</code>.</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>input StringFilter {</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  eq: String</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  ...</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  startsWith: String</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  like: String</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  ilike: String</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<p>Note that <code class="short-inline-codeblock">startsWith</code> filters should be preferred where appropriate because they can leverage simple B-Tree indexes to improve performance.</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>{</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>  generalLedgerCollection(filter: { identifierCode: { startsWith: &quot;BX1:&quot; } }) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>    edges {</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>      node {</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>        nodeId</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>        identifierCode</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>        amount</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>      }</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>    }</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<h3 id="graphql-directives-skip-and-include" class="group scroll-mt-24">GraphQL directives <code class="short-inline-codeblock">@skip</code> and <code class="short-inline-codeblock">@include</code><a href="#graphql-directives-skip-and-include" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>The GraphQL spec has evolved over time. Although the spec is clear, it is common for GraphQL servers to selectively omit some chunks of functionality. For example, some frameworks intentionally do not expose an introspection schema as a form of security through obscurity.</p>
<p><code class="short-inline-codeblock">pg_graphql</code> aims to be unopinionated and adhere exactly to the spec. The <code class="short-inline-codeblock">@skip</code> and <code class="short-inline-codeblock">@include</code> directives are part of the <a href="https://spec.graphql.org/October2021/#sec--skip">GraphQL core specification</a> and are now functional.</p>
<p>The <strong><code class="short-inline-codeblock">@skip</code></strong> directive in GraphQL is used to conditionally skip a field or fragment during query execution based on a Boolean variable. It can be used to make the query more efficient by reducing the amount of data retrieved from the server.</p>
<p>The <strong><code class="short-inline-codeblock">@include</code></strong> directive is the mirror of <strong><code class="short-inline-codeblock">@skip</code></strong> where a field or fragment is conditionally included depending on the value of a Boolean variable.</p>
<p>Here&#x27;s an example of how the <strong><code class="short-inline-codeblock">@skip</code></strong> directive can be used in a GraphQL query:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>query getBooks($includeDetails: Boolean!) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>  booksCollection {</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>    edges {</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>      node {</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>        id</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>        title</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>        description @skip(if: $includeDetails)</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>      }</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>    }</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<h3 id="user-defined-descriptions" class="group scroll-mt-24">User Defined Descriptions<a href="#user-defined-descriptions" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Users can now use the <a href="https://supabase.github.io/pg_graphql/configuration/#description">comment directive system to assign descriptions</a> to tables, views and columns.</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>create table public.book(</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    id int primary key,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    title text not null</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>);</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>comment on table public.book</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>is e&#x27;@graphql({&quot;description&quot;: &quot;a library book&quot;})&#x27;;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>comment on column public.book.title</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>is e&#x27;@graphql({&quot;description&quot;: &quot;the title of the book&quot;})&#x27;;</span></div></div><br/></code></div></div>
<p>GraphQL IDEs, such as GraphiQL render those descriptions, allowing developers to provide clearer API documentation.</p>
<!-- -->
<h2 id="roadmap" class="group scroll-mt-24">Roadmap<a href="#roadmap" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>The headline features we aim to launch in coming releases of <code class="short-inline-codeblock">pg_graphql</code> include:</p>
<ol>
<li>Support for user-defined functions: <a href="https://github.com/supabase/pg_graphql/issues/222">GitHub issue</a></li>
<li>Support for nested inserts: <a href="https://github.com/supabase/pg_graphql/issues/294">GitHub issue</a></li>
<li>An alternative approach to computed relationships based on SQL functions returning <strong><code class="short-inline-codeblock">SET OF</code></strong> rather than comment directives (compatible with PostgREST)</li>
</ol>
<h2 id="more-pg_graphql" class="group scroll-mt-24">More pg_graphql<a href="#more-pg_graphql" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ul>
<li><a href="https://supabase.com/blog/pg-graphql">Introducing pg_graphql: A GraphQL extension for PostgreSQL</a></li>
<li><a href="https://supabase.com/blog/graphql-now-available">GraphQL is now available in Supabase</a></li>
<li><a href="https://supabase.com/blog/pg-graphql-v1">pg_graphql v1.0</a></li>
<li><a href="pg-graphql-postgres-functions.html">pg_graphql: Postgres functions now supported</a></li>
</ul></div></article><div class="flex flex-col gap-3 lg:gap-4 border-t border-muted py-4 lg:py-8 mt-4 lg:mt-8"><h3 class="text-foreground text-xl mb-4">More Launch Week 7</h3><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="https://supabase.com/blog/designing-with-ai-midjourney"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Designing with AI</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="https://github.com/supabase/supavisor"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Supavisor</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="https://supabase.com/blog/supabase-logs-self-hosted"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Open Source Logging</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="https://supabase.com/blog/edge-runtime-self-hosted-deno-functions"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Self-hosted Deno Edge Functions</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="https://supabase.com/blog/storage-v3-resumable-uploads"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg max-w-[240px]">Storage v3: Resumable Uploads with support for 50GB files</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="https://supabase.com/blog/supabase-auth-sso-pkce"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg max-w-[240px]">Supabase Auth: SSO, Mobile, and Server-side support</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="https://supabase.com/blog/launch-week-7-community-highlights"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Community Highlight</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="https://supabase.com/blog/supabase-studio-2.0"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Studio Updates</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="dbdev.html"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">dbdev</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="https://supabase.com/blog/pg-tle"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Postgres TLE</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div></div><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fwhats-new-in-pg-graphql-v1-2&amp;text=What&apos;s%20New%20in%20pg_graphql%20v1.2"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fwhats-new-in-pg-graphql-v1-2&amp;text=What&apos;s%20New%20in%20pg_graphql%20v1.2"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fwhats-new-in-pg-graphql-v1-2&amp;t=What&apos;s%20New%20in%20pg_graphql%20v1.2"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="launch-week-7-hackathon-winners.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Launch Week 7 Hackathon Winners</h4><p class="small">24 April 2023</p></div></div></div></div></a></div><div><a href="dbdev.html"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">dbdev: PostgreSQL Package Manager</h4><p class="small">14 April 2023</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/postgres"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">postgres</div></a><a href="https://supabase.com/blog/tags/graphql"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">graphql</div></a><a href="https://supabase.com/blog/tags/planetpg"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">planetpg</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#view-support">View Support</a></li>
</ul>
<ul>
<li><a href="#filtering">Filtering</a>
<ul>
<li><a href="#is-null-and-is-not-null"><code>is null</code> and <code>is not null</code></a></li>
<li><a href="#like-ilike-and-startswith"><strong><code>like</code></strong>, <strong><code>ilike</code></strong>, and <strong><code>startsWith</code></strong></a></li>
<li><a href="#graphql-directives-skip-and-include">GraphQL directives <code>@skip</code> and <code>@include</code></a></li>
<li><a href="#user-defined-descriptions">User Defined Descriptions</a></li>
</ul>
</li>
<li><a href="#roadmap">Roadmap</a></li>
<li><a href="#more-pg_graphql">More pg_graphql</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fwhats-new-in-pg-graphql-v1-2&amp;text=What&apos;s%20New%20in%20pg_graphql%20v1.2"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fwhats-new-in-pg-graphql-v1-2&amp;text=What&apos;s%20New%20in%20pg_graphql%20v1.2"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fwhats-new-in-pg-graphql-v1-2&amp;t=What&apos;s%20New%20in%20pg_graphql%20v1.2"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"launch-week-7-hackathon-winners","title":"Launch Week 7 Hackathon Winners","description":"Announcing the winners of the Launch Week 7 Hackathon!","author":"tyler_shukert","image":"lw7-hackathon-winners/lw7-hackathon-winner.jpg","thumb":"lw7-hackathon-winners/lw7-hackathon-winner.jpg","categories":["developers"],"tags":["hackathon"],"date":"2023-04-24","toc_depth":2,"formattedDate":"24 April 2023","readingTime":"4 minute read","url":"/blog/launch-week-7-hackathon-winners","path":"/blog/launch-week-7-hackathon-winners"},"nextPost":{"slug":"dbdev","title":"dbdev: PostgreSQL Package Manager","description":"We're publicly previewing dbdev, a PostgreSQL package manager.","launchweek":"7","categories":["product"],"tags":["launch-week","postgres"],"date":"2023-04-14","toc_depth":3,"author":"oli_rice,daltjoh-aws","image":"launch-week-7/day-5-one-more-thing/day-5-dbdev-og.jpg","thumb":"launch-week-7/day-5-one-more-thing/day-5-dbdev-thumb.jpg","formattedDate":"14 April 2023","readingTime":"11 minute read","url":"/blog/dbdev","path":"/blog/dbdev"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"whats-new-in-pg-graphql-v1-2","source":"\nIt's been 4 months since the [1.0.0 release of pg_graphql](https://supabase.com/blog/pg-graphql-v1).\nSince then, we’ve pushed several features to improve the APIs that `pg_graphql` produces.\n\nIn this article, we’ll walk through those features and show examples of each.\n\n\u003cdiv className=\"bg-gray-300 rounded-lg px-6 py-2 italic\"\u003e\n\n📢 These features are only available on projects with Postgres version `*********` or higher. For help with upgrading, please review the [migrating and upgrading projects guide](https://supabase.com/docs/guides/platform/migrating-and-upgrading-projects).\n\n\u003c/div\u003e\n\n### View Support\n\nPrior to `v1.1`, `pg_graphql` would only reflect standard tables. Since then, views, materialized views, and foreign tables are now also reflected in the GraphQL schema.\n\nFor example:\n\n```sql\ncreate view \"ProjectOwner\" as\n  select\n    acc.id,\n    acc.name\n  from\n    account as acc\n    join role as r on r.id = acc.role_id\n  where acc.role = 'project_owner';\n```\n\nSince all entities exposed by `pg_graphql` require primary keys, we must define that constraint for the view. We do that using a comment directive:\n\n```sql\ncomment on view \"ProjectOwner\"\n  is '@graphql({\"primary_key_columns\": [\"id\"]})';\n```\n\nWhich yields the GraphQL type:\n\n```graphql\ntype ProjectOwner implements Node {\n  nodeId: ID!\n  id: UUID!\n  name: String\n}\n```\n\nWith associated `Edge` and `Connection` types. That enables querying via:\n\n```graphql\n{\n  projectOwnerCollection(first: 2) {\n    edges {\n      node {\n        nodeId\n        name\n      }\n    }\n  }\n}\n```\n\nAdditionally, simple views automatically support mutation events like inserts and updates. You might use these to migrate underlying tables while maintaining backwards compatibility with previous API versions.\n\n## Filtering\n\nFiltering in SQL is endlessly flexible. We’ve taken two incremental steps to bring more of that flexibility to the GraphQL interface.\n\n### `is null` and `is not null`\n\nHandling `null` values can be tricky in both SQL and GraphQL. However, there are similarities we can take advantage of.\nIn `pg_graphql`, every scalar data type has its own filter type, such as `IntFilter` and `StringFilter`.\nEach of these filter types now includes an `is` argument, which allows you to filter based on whether a value is null or not null.\nYou can do this by using `{is: NULL}` for `null` values and `{is: NOT_NULL}` for non-`null` values.\n\n```graphql\nenum FilterIs {\n    NULL\n    NOT_NULL\n}\n\ntype IntFilter {\n    ...\n    is: FilterIs\n}\n```\n\nFor example:\n\n```graphql\n{\n  blogCollection(filter: { name: {is: NULL}}) {\n    ...\n  }\n}\n```\n\nto return all `blog`s where the `name` is `null`.\n\n### **`like`**, **`ilike`**, and **`startsWith`**\n\nText filtering options in `pg_graphql` have historically been restricted to equality checks. The hesitation was due to concerns about exposing a default filter that is difficult to index. The combination of [citext](https://www.postgresql.org/docs/current/citext.html) and [PGroonga available on the platform](https://supabase.com/docs/guides/database/extensions/pgroonga) solves those scalability risks and enabled us to expand the `StringFilter` with options for `like` `ilike` and `startsWith`.\n\n```graphql\ninput StringFilter {\n  eq: String\n  ...\n  startsWith: String\n  like: String\n  ilike: String\n}\n```\n\nNote that `startsWith` filters should be preferred where appropriate because they can leverage simple B-Tree indexes to improve performance.\n\n```graphql\n{\n  generalLedgerCollection(filter: { identifierCode: { startsWith: \"BX1:\" } }) {\n    edges {\n      node {\n        nodeId\n        identifierCode\n        amount\n      }\n    }\n  }\n}\n```\n\n### GraphQL directives `@skip` and `@include`\n\nThe GraphQL spec has evolved over time. Although the spec is clear, it is common for GraphQL servers to selectively omit some chunks of functionality. For example, some frameworks intentionally do not expose an introspection schema as a form of security through obscurity.\n\n`pg_graphql` aims to be unopinionated and adhere exactly to the spec. The `@skip` and `@include` directives are part of the [GraphQL core specification](https://spec.graphql.org/October2021/#sec--skip) and are now functional.\n\nThe **`@skip`** directive in GraphQL is used to conditionally skip a field or fragment during query execution based on a Boolean variable. It can be used to make the query more efficient by reducing the amount of data retrieved from the server.\n\nThe **`@include`** directive is the mirror of **`@skip`** where a field or fragment is conditionally included depending on the value of a Boolean variable.\n\nHere's an example of how the **`@skip`** directive can be used in a GraphQL query:\n\n```graphql\nquery getBooks($includeDetails: Boolean!) {\n  booksCollection {\n    edges {\n      node {\n        id\n        title\n        description @skip(if: $includeDetails)\n      }\n    }\n  }\n}\n```\n\n### User Defined Descriptions\n\nUsers can now use the [comment directive system to assign descriptions](https://supabase.github.io/pg_graphql/configuration/#description) to tables, views and columns.\n\n```sql\ncreate table public.book(\n    id int primary key,\n    title text not null\n);\n\ncomment on table public.book\nis e'@graphql({\"description\": \"a library book\"})';\n\ncomment on column public.book.title\nis e'@graphql({\"description\": \"the title of the book\"})';\n```\n\nGraphQL IDEs, such as GraphiQL render those descriptions, allowing developers to provide clearer API documentation.\n\n\u003cImg\n  src=\"/images/blog/2023-04-19-pg_graphql-v1_2/description_directive.png\"\n  wide={false}\n  alt=\"description comment directive\"\n/\u003e\n\n## Roadmap\n\nThe headline features we aim to launch in coming releases of `pg_graphql` include:\n\n1. Support for user-defined functions: [GitHub issue](https://github.com/supabase/pg_graphql/issues/222)\n2. Support for nested inserts: [GitHub issue](https://github.com/supabase/pg_graphql/issues/294)\n3. An alternative approach to computed relationships based on SQL functions returning **`SET OF`** rather than comment directives (compatible with PostgREST)\n\n## More pg_graphql\n\n- [Introducing pg_graphql: A GraphQL extension for PostgreSQL](https://supabase.com/blog/pg-graphql)\n- [GraphQL is now available in Supabase](https://supabase.com/blog/graphql-now-available)\n- [pg_graphql v1.0](https://supabase.com/blog/pg-graphql-v1)\n- [pg_graphql: Postgres functions now supported](https://supabase.com/blog/pg-graphql-postgres-functions)\n","title":"What's New in pg_graphql v1.2","description":"New Features in the v1.2 release of pg_graphql","launchweek":"7","categories":["postgres"],"tags":["postgres","graphql","planetpg"],"date":"2023-04-21","toc_depth":3,"author":"oli_rice","image":"launch-week-6/pggraphql/og-pg-graphql.png","thumb":"launch-week-6/pggraphql/og-pg-graphql.png","content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    a: \"a\",\n    code: \"code\",\n    h3: \"h3\",\n    h2: \"h2\",\n    strong: \"strong\",\n    ol: \"ol\",\n    li: \"li\",\n    ul: \"ul\"\n  }, _provideComponents(), props.components), {Img, CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  if (!Img) _missingMdxReference(\"Img\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"It's been 4 months since the \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/pg-graphql-v1\",\n        children: \"1.0.0 release of pg_graphql\"\n      }), \".\\nSince then, we’ve pushed several features to improve the APIs that \", _jsx(_components.code, {\n        children: \"pg_graphql\"\n      }), \" produces.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In this article, we’ll walk through those features and show examples of each.\"\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"bg-gray-300 rounded-lg px-6 py-2 italic\",\n      children: _jsxs(_components.p, {\n        children: [\"📢 These features are only available on projects with Postgres version \", _jsx(_components.code, {\n          children: \"*********\"\n        }), \" or higher. For help with upgrading, please review the \", _jsx(_components.a, {\n          href: \"https://supabase.com/docs/guides/platform/migrating-and-upgrading-projects\",\n          children: \"migrating and upgrading projects guide\"\n        }), \".\"]\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"view-support\",\n      children: \"View Support\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Prior to \", _jsx(_components.code, {\n        children: \"v1.1\"\n      }), \", \", _jsx(_components.code, {\n        children: \"pg_graphql\"\n      }), \" would only reflect standard tables. Since then, views, materialized views, and foreign tables are now also reflected in the GraphQL schema.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"For example:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"create view\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" \\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"ProjectOwner\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\" \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"as\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  select\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    acc\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"id\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    acc\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"name\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  from\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    account \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"as\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" acc\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    join role as\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" r \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"on \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"r\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"id \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"acc\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"role_id\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  where \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"acc\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"role \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'project_owner'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Since all entities exposed by \", _jsx(_components.code, {\n        children: \"pg_graphql\"\n      }), \" require primary keys, we must define that constraint for the view. We do that using a comment directive:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"comment \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"on\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" view \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"ProjectOwner\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  is \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'@graphql({\\\"primary_key_columns\\\": [\\\"id\\\"]})'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Which yields the GraphQL type:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"type \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"ProjectOwner \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"implements \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"Node\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  nodeId\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"ID\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"!\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  id\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"UUID\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"!\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  name\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"String\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"graphql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"With associated \", _jsx(_components.code, {\n        children: \"Edge\"\n      }), \" and \", _jsx(_components.code, {\n        children: \"Connection\"\n      }), \" types. That enables querying via:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  projectOwnerCollection\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"first\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"2\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \") {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    edges\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      node\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        nodeId\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        name\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"graphql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Additionally, simple views automatically support mutation events like inserts and updates. You might use these to migrate underlying tables while maintaining backwards compatibility with previous API versions.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"filtering\",\n      children: \"Filtering\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Filtering in SQL is endlessly flexible. We’ve taken two incremental steps to bring more of that flexibility to the GraphQL interface.\"\n    }), \"\\n\", _jsxs(_components.h3, {\n      id: \"is-null-and-is-not-null\",\n      children: [_jsx(_components.code, {\n        children: \"is null\"\n      }), \" and \", _jsx(_components.code, {\n        children: \"is not null\"\n      })]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Handling \", _jsx(_components.code, {\n        children: \"null\"\n      }), \" values can be tricky in both SQL and GraphQL. However, there are similarities we can take advantage of.\\nIn \", _jsx(_components.code, {\n        children: \"pg_graphql\"\n      }), \", every scalar data type has its own filter type, such as \", _jsx(_components.code, {\n        children: \"IntFilter\"\n      }), \" and \", _jsx(_components.code, {\n        children: \"StringFilter\"\n      }), \".\\nEach of these filter types now includes an \", _jsx(_components.code, {\n        children: \"is\"\n      }), \" argument, which allows you to filter based on whether a value is null or not null.\\nYou can do this by using \", _jsx(_components.code, {\n        children: \"{is: NULL}\"\n      }), \" for \", _jsx(_components.code, {\n        children: \"null\"\n      }), \" values and \", _jsx(_components.code, {\n        children: \"{is: NOT_NULL}\"\n      }), \" for non-\", _jsx(_components.code, {\n        children: \"null\"\n      }), \" values.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"enum \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"FilterIs\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    NULL\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    NOT_NULL\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"type \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"IntFilter\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    ...\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    is\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"FilterIs\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"graphql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"For example:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  blogCollection\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"filter\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"name\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \": {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"is\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \" NULL\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"}}) {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    ...\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"graphql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"to return all \", _jsx(_components.code, {\n        children: \"blog\"\n      }), \"s where the \", _jsx(_components.code, {\n        children: \"name\"\n      }), \" is \", _jsx(_components.code, {\n        children: \"null\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.h3, {\n      id: \"like-ilike-and-startswith\",\n      children: [_jsx(_components.strong, {\n        children: _jsx(_components.code, {\n          children: \"like\"\n        })\n      }), \", \", _jsx(_components.strong, {\n        children: _jsx(_components.code, {\n          children: \"ilike\"\n        })\n      }), \", and \", _jsx(_components.strong, {\n        children: _jsx(_components.code, {\n          children: \"startsWith\"\n        })\n      })]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Text filtering options in \", _jsx(_components.code, {\n        children: \"pg_graphql\"\n      }), \" have historically been restricted to equality checks. The hesitation was due to concerns about exposing a default filter that is difficult to index. The combination of \", _jsx(_components.a, {\n        href: \"https://www.postgresql.org/docs/current/citext.html\",\n        children: \"citext\"\n      }), \" and \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/database/extensions/pgroonga\",\n        children: \"PGroonga available on the platform\"\n      }), \" solves those scalability risks and enabled us to expand the \", _jsx(_components.code, {\n        children: \"StringFilter\"\n      }), \" with options for \", _jsx(_components.code, {\n        children: \"like\"\n      }), \" \", _jsx(_components.code, {\n        children: \"ilike\"\n      }), \" and \", _jsx(_components.code, {\n        children: \"startsWith\"\n      }), \".\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"input \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"StringFilter\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  eq\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"String\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  ...\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  startsWith\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"String\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  like\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"String\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  ilike\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"String\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"graphql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Note that \", _jsx(_components.code, {\n        children: \"startsWith\"\n      }), \" filters should be preferred where appropriate because they can leverage simple B-Tree indexes to improve performance.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  generalLedgerCollection\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"filter\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"identifierCode\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \": { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"startsWith\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"BX1:\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" } }) {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    edges\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      node\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        nodeId\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        identifierCode\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        amount\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"graphql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.h3, {\n      id: \"graphql-directives-skip-and-include\",\n      children: [\"GraphQL directives \", _jsx(_components.code, {\n        children: \"@skip\"\n      }), \" and \", _jsx(_components.code, {\n        children: \"@include\"\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The GraphQL spec has evolved over time. Although the spec is clear, it is common for GraphQL servers to selectively omit some chunks of functionality. For example, some frameworks intentionally do not expose an introspection schema as a form of security through obscurity.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.code, {\n        children: \"pg_graphql\"\n      }), \" aims to be unopinionated and adhere exactly to the spec. The \", _jsx(_components.code, {\n        children: \"@skip\"\n      }), \" and \", _jsx(_components.code, {\n        children: \"@include\"\n      }), \" directives are part of the \", _jsx(_components.a, {\n        href: \"https://spec.graphql.org/October2021/#sec--skip\",\n        children: \"GraphQL core specification\"\n      }), \" and are now functional.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The \", _jsx(_components.strong, {\n        children: _jsx(_components.code, {\n          children: \"@skip\"\n        })\n      }), \" directive in GraphQL is used to conditionally skip a field or fragment during query execution based on a Boolean variable. It can be used to make the query more efficient by reducing the amount of data retrieved from the server.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The \", _jsx(_components.strong, {\n        children: _jsx(_components.code, {\n          children: \"@include\"\n        })\n      }), \" directive is the mirror of \", _jsx(_components.strong, {\n        children: _jsx(_components.code, {\n          children: \"@skip\"\n        })\n      }), \" where a field or fragment is conditionally included depending on the value of a Boolean variable.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Here's an example of how the \", _jsx(_components.strong, {\n        children: _jsx(_components.code, {\n          children: \"@skip\"\n        })\n      }), \" directive can be used in a GraphQL query:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"query \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"getBooks\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"$includeDetails\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Boolean\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"!\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \") {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  booksCollection\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    edges\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      node\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        id\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        title\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        description \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"@skip\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"$includeDetails\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"graphql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"user-defined-descriptions\",\n      children: \"User Defined Descriptions\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Users can now use the \", _jsx(_components.a, {\n        href: \"https://supabase.github.io/pg_graphql/configuration/#description\",\n        children: \"comment directive system to assign descriptions\"\n      }), \" to tables, views and columns.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"create table \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"public\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \".book(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    id \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"int primary key\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    title \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"text not null\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \");\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"comment \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"on table \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"public\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"book\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"is\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'@graphql({\\\"description\\\": \\\"a library book\\\"})'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"comment \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"on\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" column \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"public\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"book\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".title\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"is\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'@graphql({\\\"description\\\": \\\"the title of the book\\\"})'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"GraphQL IDEs, such as GraphiQL render those descriptions, allowing developers to provide clearer API documentation.\"\n    }), \"\\n\", _jsx(Img, {\n      src: \"/images/blog/2023-04-19-pg_graphql-v1_2/description_directive.png\",\n      wide: false,\n      alt: \"description comment directive\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"roadmap\",\n      children: \"Roadmap\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The headline features we aim to launch in coming releases of \", _jsx(_components.code, {\n        children: \"pg_graphql\"\n      }), \" include:\"]\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Support for user-defined functions: \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/pg_graphql/issues/222\",\n          children: \"GitHub issue\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Support for nested inserts: \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/pg_graphql/issues/294\",\n          children: \"GitHub issue\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"An alternative approach to computed relationships based on SQL functions returning \", _jsx(_components.strong, {\n          children: _jsx(_components.code, {\n            children: \"SET OF\"\n          })\n        }), \" rather than comment directives (compatible with PostgREST)\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"more-pg_graphql\",\n      children: \"More pg_graphql\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/blog/pg-graphql\",\n          children: \"Introducing pg_graphql: A GraphQL extension for PostgreSQL\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/blog/graphql-now-available\",\n          children: \"GraphQL is now available in Supabase\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/blog/pg-graphql-v1\",\n          children: \"pg_graphql v1.0\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/blog/pg-graphql-postgres-functions\",\n          children: \"pg_graphql: Postgres functions now supported\"\n        })\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"View Support","slug":"view-support","lvl":3,"i":0,"seen":0},{"content":"Filtering","slug":"filtering","lvl":2,"i":1,"seen":0},{"content":"`is null` and `is not null`","slug":"is-null-and-is-not-null","lvl":3,"i":2,"seen":0},{"content":"**`like`**, **`ilike`**, and **`startsWith`**","slug":"like-ilike-and-startswith","lvl":3,"i":3,"seen":0},{"content":"GraphQL directives `@skip` and `@include`","slug":"graphql-directives-skip-and-include","lvl":3,"i":4,"seen":0},{"content":"User Defined Descriptions","slug":"user-defined-descriptions","lvl":3,"i":5,"seen":0},{"content":"Roadmap","slug":"roadmap","lvl":2,"i":6,"seen":0},{"content":"More pg_graphql","slug":"more-pg_graphql","lvl":2,"i":7,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,3],"level":0},{"type":"inline","content":"It's been 4 months since the [1.0.0 release of pg_graphql](https://supabase.com/blog/pg-graphql-v1).\nSince then, we’ve pushed several features to improve the APIs that `pg_graphql` produces.","level":1,"lines":[1,3],"children":[{"type":"text","content":"It's been 4 months since the ","level":0},{"type":"link_open","href":"https://supabase.com/blog/pg-graphql-v1","title":"","level":0},{"type":"text","content":"1.0.0 release of pg_graphql","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Since then, we’ve pushed several features to improve the APIs that ","level":0},{"type":"code","content":"pg_graphql","block":false,"level":0},{"type":"text","content":" produces.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[4,5],"level":0},{"type":"inline","content":"In this article, we’ll walk through those features and show examples of each.","level":1,"lines":[4,5],"children":[{"type":"text","content":"In this article, we’ll walk through those features and show examples of each.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[6,7],"level":0},{"type":"inline","content":"\u003cdiv className=\"bg-gray-300 rounded-lg px-6 py-2 italic\"\u003e","level":1,"lines":[6,7],"children":[{"type":"text","content":"\u003cdiv className=\"bg-gray-300 rounded-lg px-6 py-2 italic\"\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[8,9],"level":0},{"type":"inline","content":"📢 These features are only available on projects with Postgres version `*********` or higher. For help with upgrading, please review the [migrating and upgrading projects guide](https://supabase.com/docs/guides/platform/migrating-and-upgrading-projects).","level":1,"lines":[8,9],"children":[{"type":"text","content":"📢 These features are only available on projects with Postgres version ","level":0},{"type":"code","content":"*********","block":false,"level":0},{"type":"text","content":" or higher. For help with upgrading, please review the ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/platform/migrating-and-upgrading-projects","title":"","level":0},{"type":"text","content":"migrating and upgrading projects guide","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[10,11],"level":0},{"type":"inline","content":"\u003c/div\u003e","level":1,"lines":[10,11],"children":[{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[12,13],"level":0},{"type":"inline","content":"[View Support](#view-support)","level":1,"lines":[12,13],"children":[{"type":"text","content":"View Support","level":0}],"lvl":3,"i":0,"seen":0,"slug":"view-support"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[14,15],"level":0},{"type":"inline","content":"Prior to `v1.1`, `pg_graphql` would only reflect standard tables. Since then, views, materialized views, and foreign tables are now also reflected in the GraphQL schema.","level":1,"lines":[14,15],"children":[{"type":"text","content":"Prior to ","level":0},{"type":"code","content":"v1.1","block":false,"level":0},{"type":"text","content":", ","level":0},{"type":"code","content":"pg_graphql","block":false,"level":0},{"type":"text","content":" would only reflect standard tables. Since then, views, materialized views, and foreign tables are now also reflected in the GraphQL schema.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[16,17],"level":0},{"type":"inline","content":"For example:","level":1,"lines":[16,17],"children":[{"type":"text","content":"For example:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql","content":"create view \"ProjectOwner\" as\n  select\n    acc.id,\n    acc.name\n  from\n    account as acc\n    join role as r on r.id = acc.role_id\n  where acc.role = 'project_owner';\n","lines":[18,28],"level":0},{"type":"paragraph_open","tight":false,"lines":[29,30],"level":0},{"type":"inline","content":"Since all entities exposed by `pg_graphql` require primary keys, we must define that constraint for the view. We do that using a comment directive:","level":1,"lines":[29,30],"children":[{"type":"text","content":"Since all entities exposed by ","level":0},{"type":"code","content":"pg_graphql","block":false,"level":0},{"type":"text","content":" require primary keys, we must define that constraint for the view. We do that using a comment directive:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql","content":"comment on view \"ProjectOwner\"\n  is '@graphql({\"primary_key_columns\": [\"id\"]})';\n","lines":[31,35],"level":0},{"type":"paragraph_open","tight":false,"lines":[36,37],"level":0},{"type":"inline","content":"Which yields the GraphQL type:","level":1,"lines":[36,37],"children":[{"type":"text","content":"Which yields the GraphQL type:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"graphql","content":"type ProjectOwner implements Node {\n  nodeId: ID!\n  id: UUID!\n  name: String\n}\n","lines":[38,45],"level":0},{"type":"paragraph_open","tight":false,"lines":[46,47],"level":0},{"type":"inline","content":"With associated `Edge` and `Connection` types. That enables querying via:","level":1,"lines":[46,47],"children":[{"type":"text","content":"With associated ","level":0},{"type":"code","content":"Edge","block":false,"level":0},{"type":"text","content":" and ","level":0},{"type":"code","content":"Connection","block":false,"level":0},{"type":"text","content":" types. That enables querying via:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"graphql","content":"{\n  projectOwnerCollection(first: 2) {\n    edges {\n      node {\n        nodeId\n        name\n      }\n    }\n  }\n}\n","lines":[48,60],"level":0},{"type":"paragraph_open","tight":false,"lines":[61,62],"level":0},{"type":"inline","content":"Additionally, simple views automatically support mutation events like inserts and updates. You might use these to migrate underlying tables while maintaining backwards compatibility with previous API versions.","level":1,"lines":[61,62],"children":[{"type":"text","content":"Additionally, simple views automatically support mutation events like inserts and updates. You might use these to migrate underlying tables while maintaining backwards compatibility with previous API versions.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[63,64],"level":0},{"type":"inline","content":"[Filtering](#filtering)","level":1,"lines":[63,64],"children":[{"type":"text","content":"Filtering","level":0}],"lvl":2,"i":1,"seen":0,"slug":"filtering"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[65,66],"level":0},{"type":"inline","content":"Filtering in SQL is endlessly flexible. We’ve taken two incremental steps to bring more of that flexibility to the GraphQL interface.","level":1,"lines":[65,66],"children":[{"type":"text","content":"Filtering in SQL is endlessly flexible. We’ve taken two incremental steps to bring more of that flexibility to the GraphQL interface.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[67,68],"level":0},{"type":"inline","content":"[`is null` and `is not null`](#is-null-and-is-not-null)","level":1,"lines":[67,68],"children":[{"type":"code","content":"is null","block":false,"level":0},{"type":"text","content":" and ","level":0},{"type":"code","content":"is not null","block":false,"level":0}],"lvl":3,"i":2,"seen":0,"slug":"is-null-and-is-not-null"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[69,73],"level":0},{"type":"inline","content":"Handling `null` values can be tricky in both SQL and GraphQL. However, there are similarities we can take advantage of.\nIn `pg_graphql`, every scalar data type has its own filter type, such as `IntFilter` and `StringFilter`.\nEach of these filter types now includes an `is` argument, which allows you to filter based on whether a value is null or not null.\nYou can do this by using `{is: NULL}` for `null` values and `{is: NOT_NULL}` for non-`null` values.","level":1,"lines":[69,73],"children":[{"type":"text","content":"Handling ","level":0},{"type":"code","content":"null","block":false,"level":0},{"type":"text","content":" values can be tricky in both SQL and GraphQL. However, there are similarities we can take advantage of.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"In ","level":0},{"type":"code","content":"pg_graphql","block":false,"level":0},{"type":"text","content":", every scalar data type has its own filter type, such as ","level":0},{"type":"code","content":"IntFilter","block":false,"level":0},{"type":"text","content":" and ","level":0},{"type":"code","content":"StringFilter","block":false,"level":0},{"type":"text","content":".","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Each of these filter types now includes an ","level":0},{"type":"code","content":"is","block":false,"level":0},{"type":"text","content":" argument, which allows you to filter based on whether a value is null or not null.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"You can do this by using ","level":0},{"type":"code","content":"{is: NULL}","block":false,"level":0},{"type":"text","content":" for ","level":0},{"type":"code","content":"null","block":false,"level":0},{"type":"text","content":" values and ","level":0},{"type":"code","content":"{is: NOT_NULL}","block":false,"level":0},{"type":"text","content":" for non-","level":0},{"type":"code","content":"null","block":false,"level":0},{"type":"text","content":" values.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"graphql","content":"enum FilterIs {\n    NULL\n    NOT_NULL\n}\n\ntype IntFilter {\n    ...\n    is: FilterIs\n}\n","lines":[74,85],"level":0},{"type":"paragraph_open","tight":false,"lines":[86,87],"level":0},{"type":"inline","content":"For example:","level":1,"lines":[86,87],"children":[{"type":"text","content":"For example:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"graphql","content":"{\n  blogCollection(filter: { name: {is: NULL}}) {\n    ...\n  }\n}\n","lines":[88,95],"level":0},{"type":"paragraph_open","tight":false,"lines":[96,97],"level":0},{"type":"inline","content":"to return all `blog`s where the `name` is `null`.","level":1,"lines":[96,97],"children":[{"type":"text","content":"to return all ","level":0},{"type":"code","content":"blog","block":false,"level":0},{"type":"text","content":"s where the ","level":0},{"type":"code","content":"name","block":false,"level":0},{"type":"text","content":" is ","level":0},{"type":"code","content":"null","block":false,"level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[98,99],"level":0},{"type":"inline","content":"[**`like`**, **`ilike`**, and **`startsWith`**](#like-ilike-and-startswith)","level":1,"lines":[98,99],"children":[{"type":"strong_open","level":0},{"type":"code","content":"like","block":false,"level":1},{"type":"strong_close","level":0},{"type":"text","content":", ","level":0},{"type":"strong_open","level":0},{"type":"code","content":"ilike","block":false,"level":1},{"type":"strong_close","level":0},{"type":"text","content":", and ","level":0},{"type":"strong_open","level":0},{"type":"code","content":"startsWith","block":false,"level":1},{"type":"strong_close","level":0}],"lvl":3,"i":3,"seen":0,"slug":"like-ilike-and-startswith"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[100,101],"level":0},{"type":"inline","content":"Text filtering options in `pg_graphql` have historically been restricted to equality checks. The hesitation was due to concerns about exposing a default filter that is difficult to index. The combination of [citext](https://www.postgresql.org/docs/current/citext.html) and [PGroonga available on the platform](https://supabase.com/docs/guides/database/extensions/pgroonga) solves those scalability risks and enabled us to expand the `StringFilter` with options for `like` `ilike` and `startsWith`.","level":1,"lines":[100,101],"children":[{"type":"text","content":"Text filtering options in ","level":0},{"type":"code","content":"pg_graphql","block":false,"level":0},{"type":"text","content":" have historically been restricted to equality checks. The hesitation was due to concerns about exposing a default filter that is difficult to index. The combination of ","level":0},{"type":"link_open","href":"https://www.postgresql.org/docs/current/citext.html","title":"","level":0},{"type":"text","content":"citext","level":1},{"type":"link_close","level":0},{"type":"text","content":" and ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/database/extensions/pgroonga","title":"","level":0},{"type":"text","content":"PGroonga available on the platform","level":1},{"type":"link_close","level":0},{"type":"text","content":" solves those scalability risks and enabled us to expand the ","level":0},{"type":"code","content":"StringFilter","block":false,"level":0},{"type":"text","content":" with options for ","level":0},{"type":"code","content":"like","block":false,"level":0},{"type":"text","content":" ","level":0},{"type":"code","content":"ilike","block":false,"level":0},{"type":"text","content":" and ","level":0},{"type":"code","content":"startsWith","block":false,"level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"graphql","content":"input StringFilter {\n  eq: String\n  ...\n  startsWith: String\n  like: String\n  ilike: String\n}\n","lines":[102,111],"level":0},{"type":"paragraph_open","tight":false,"lines":[112,113],"level":0},{"type":"inline","content":"Note that `startsWith` filters should be preferred where appropriate because they can leverage simple B-Tree indexes to improve performance.","level":1,"lines":[112,113],"children":[{"type":"text","content":"Note that ","level":0},{"type":"code","content":"startsWith","block":false,"level":0},{"type":"text","content":" filters should be preferred where appropriate because they can leverage simple B-Tree indexes to improve performance.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"graphql","content":"{\n  generalLedgerCollection(filter: { identifierCode: { startsWith: \"BX1:\" } }) {\n    edges {\n      node {\n        nodeId\n        identifierCode\n        amount\n      }\n    }\n  }\n}\n","lines":[114,127],"level":0},{"type":"heading_open","hLevel":3,"lines":[128,129],"level":0},{"type":"inline","content":"[GraphQL directives `@skip` and `@include`](#graphql-directives-skip-and-include)","level":1,"lines":[128,129],"children":[{"type":"text","content":"GraphQL directives ","level":0},{"type":"code","content":"@skip","block":false,"level":0},{"type":"text","content":" and ","level":0},{"type":"code","content":"@include","block":false,"level":0}],"lvl":3,"i":4,"seen":0,"slug":"graphql-directives-skip-and-include"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[130,131],"level":0},{"type":"inline","content":"The GraphQL spec has evolved over time. Although the spec is clear, it is common for GraphQL servers to selectively omit some chunks of functionality. For example, some frameworks intentionally do not expose an introspection schema as a form of security through obscurity.","level":1,"lines":[130,131],"children":[{"type":"text","content":"The GraphQL spec has evolved over time. Although the spec is clear, it is common for GraphQL servers to selectively omit some chunks of functionality. For example, some frameworks intentionally do not expose an introspection schema as a form of security through obscurity.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[132,133],"level":0},{"type":"inline","content":"`pg_graphql` aims to be unopinionated and adhere exactly to the spec. The `@skip` and `@include` directives are part of the [GraphQL core specification](https://spec.graphql.org/October2021/#sec--skip) and are now functional.","level":1,"lines":[132,133],"children":[{"type":"code","content":"pg_graphql","block":false,"level":0},{"type":"text","content":" aims to be unopinionated and adhere exactly to the spec. The ","level":0},{"type":"code","content":"@skip","block":false,"level":0},{"type":"text","content":" and ","level":0},{"type":"code","content":"@include","block":false,"level":0},{"type":"text","content":" directives are part of the ","level":0},{"type":"link_open","href":"https://spec.graphql.org/October2021/#sec--skip","title":"","level":0},{"type":"text","content":"GraphQL core specification","level":1},{"type":"link_close","level":0},{"type":"text","content":" and are now functional.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[134,135],"level":0},{"type":"inline","content":"The **`@skip`** directive in GraphQL is used to conditionally skip a field or fragment during query execution based on a Boolean variable. It can be used to make the query more efficient by reducing the amount of data retrieved from the server.","level":1,"lines":[134,135],"children":[{"type":"text","content":"The ","level":0},{"type":"strong_open","level":0},{"type":"code","content":"@skip","block":false,"level":1},{"type":"strong_close","level":0},{"type":"text","content":" directive in GraphQL is used to conditionally skip a field or fragment during query execution based on a Boolean variable. It can be used to make the query more efficient by reducing the amount of data retrieved from the server.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[136,137],"level":0},{"type":"inline","content":"The **`@include`** directive is the mirror of **`@skip`** where a field or fragment is conditionally included depending on the value of a Boolean variable.","level":1,"lines":[136,137],"children":[{"type":"text","content":"The ","level":0},{"type":"strong_open","level":0},{"type":"code","content":"@include","block":false,"level":1},{"type":"strong_close","level":0},{"type":"text","content":" directive is the mirror of ","level":0},{"type":"strong_open","level":0},{"type":"code","content":"@skip","block":false,"level":1},{"type":"strong_close","level":0},{"type":"text","content":" where a field or fragment is conditionally included depending on the value of a Boolean variable.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[138,139],"level":0},{"type":"inline","content":"Here's an example of how the **`@skip`** directive can be used in a GraphQL query:","level":1,"lines":[138,139],"children":[{"type":"text","content":"Here's an example of how the ","level":0},{"type":"strong_open","level":0},{"type":"code","content":"@skip","block":false,"level":1},{"type":"strong_close","level":0},{"type":"text","content":" directive can be used in a GraphQL query:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"graphql","content":"query getBooks($includeDetails: Boolean!) {\n  booksCollection {\n    edges {\n      node {\n        id\n        title\n        description @skip(if: $includeDetails)\n      }\n    }\n  }\n}\n","lines":[140,153],"level":0},{"type":"heading_open","hLevel":3,"lines":[154,155],"level":0},{"type":"inline","content":"[User Defined Descriptions](#user-defined-descriptions)","level":1,"lines":[154,155],"children":[{"type":"text","content":"User Defined Descriptions","level":0}],"lvl":3,"i":5,"seen":0,"slug":"user-defined-descriptions"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[156,157],"level":0},{"type":"inline","content":"Users can now use the [comment directive system to assign descriptions](https://supabase.github.io/pg_graphql/configuration/#description) to tables, views and columns.","level":1,"lines":[156,157],"children":[{"type":"text","content":"Users can now use the ","level":0},{"type":"link_open","href":"https://supabase.github.io/pg_graphql/configuration/#description","title":"","level":0},{"type":"text","content":"comment directive system to assign descriptions","level":1},{"type":"link_close","level":0},{"type":"text","content":" to tables, views and columns.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql","content":"create table public.book(\n    id int primary key,\n    title text not null\n);\n\ncomment on table public.book\nis e'@graphql({\"description\": \"a library book\"})';\n\ncomment on column public.book.title\nis e'@graphql({\"description\": \"the title of the book\"})';\n","lines":[158,170],"level":0},{"type":"paragraph_open","tight":false,"lines":[171,172],"level":0},{"type":"inline","content":"GraphQL IDEs, such as GraphiQL render those descriptions, allowing developers to provide clearer API documentation.","level":1,"lines":[171,172],"children":[{"type":"text","content":"GraphQL IDEs, such as GraphiQL render those descriptions, allowing developers to provide clearer API documentation.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[173,178],"level":0},{"type":"inline","content":"\u003cImg\n  src=\"/images/blog/2023-04-19-pg_graphql-v1_2/description_directive.png\"\n  wide={false}\n  alt=\"description comment directive\"\n/\u003e","level":1,"lines":[173,178],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"/images/blog/2023-04-19-pg_graphql-v1_2/description_directive.png\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"wide={false}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"description comment directive\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[179,180],"level":0},{"type":"inline","content":"[Roadmap](#roadmap)","level":1,"lines":[179,180],"children":[{"type":"text","content":"Roadmap","level":0}],"lvl":2,"i":6,"seen":0,"slug":"roadmap"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[181,182],"level":0},{"type":"inline","content":"The headline features we aim to launch in coming releases of `pg_graphql` include:","level":1,"lines":[181,182],"children":[{"type":"text","content":"The headline features we aim to launch in coming releases of ","level":0},{"type":"code","content":"pg_graphql","block":false,"level":0},{"type":"text","content":" include:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"ordered_list_open","order":1,"lines":[183,187],"level":0},{"type":"list_item_open","lines":[183,184],"level":1},{"type":"paragraph_open","tight":true,"lines":[183,184],"level":2},{"type":"inline","content":"Support for user-defined functions: [GitHub issue](https://github.com/supabase/pg_graphql/issues/222)","level":3,"lines":[183,184],"children":[{"type":"text","content":"Support for user-defined functions: ","level":0},{"type":"link_open","href":"https://github.com/supabase/pg_graphql/issues/222","title":"","level":0},{"type":"text","content":"GitHub issue","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[184,185],"level":1},{"type":"paragraph_open","tight":true,"lines":[184,185],"level":2},{"type":"inline","content":"Support for nested inserts: [GitHub issue](https://github.com/supabase/pg_graphql/issues/294)","level":3,"lines":[184,185],"children":[{"type":"text","content":"Support for nested inserts: ","level":0},{"type":"link_open","href":"https://github.com/supabase/pg_graphql/issues/294","title":"","level":0},{"type":"text","content":"GitHub issue","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[185,187],"level":1},{"type":"paragraph_open","tight":true,"lines":[185,186],"level":2},{"type":"inline","content":"An alternative approach to computed relationships based on SQL functions returning **`SET OF`** rather than comment directives (compatible with PostgREST)","level":3,"lines":[185,186],"children":[{"type":"text","content":"An alternative approach to computed relationships based on SQL functions returning ","level":0},{"type":"strong_open","level":0},{"type":"code","content":"SET OF","block":false,"level":1},{"type":"strong_close","level":0},{"type":"text","content":" rather than comment directives (compatible with PostgREST)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"ordered_list_close","level":0},{"type":"heading_open","hLevel":2,"lines":[187,188],"level":0},{"type":"inline","content":"[More pg_graphql](#more-pg_graphql)","level":1,"lines":[187,188],"children":[{"type":"text","content":"More pg_graphql","level":0}],"lvl":2,"i":7,"seen":0,"slug":"more-pg_graphql"},{"type":"heading_close","hLevel":2,"level":0},{"type":"bullet_list_open","lines":[189,193],"level":0},{"type":"list_item_open","lines":[189,190],"level":1},{"type":"paragraph_open","tight":true,"lines":[189,190],"level":2},{"type":"inline","content":"[Introducing pg_graphql: A GraphQL extension for PostgreSQL](https://supabase.com/blog/pg-graphql)","level":3,"lines":[189,190],"children":[{"type":"link_open","href":"https://supabase.com/blog/pg-graphql","title":"","level":0},{"type":"text","content":"Introducing pg_graphql: A GraphQL extension for PostgreSQL","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[190,191],"level":1},{"type":"paragraph_open","tight":true,"lines":[190,191],"level":2},{"type":"inline","content":"[GraphQL is now available in Supabase](https://supabase.com/blog/graphql-now-available)","level":3,"lines":[190,191],"children":[{"type":"link_open","href":"https://supabase.com/blog/graphql-now-available","title":"","level":0},{"type":"text","content":"GraphQL is now available in Supabase","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[191,192],"level":1},{"type":"paragraph_open","tight":true,"lines":[191,192],"level":2},{"type":"inline","content":"[pg_graphql v1.0](https://supabase.com/blog/pg-graphql-v1)","level":3,"lines":[191,192],"children":[{"type":"link_open","href":"https://supabase.com/blog/pg-graphql-v1","title":"","level":0},{"type":"text","content":"pg_graphql v1.0","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[192,193],"level":1},{"type":"paragraph_open","tight":true,"lines":[192,193],"level":2},{"type":"inline","content":"[pg_graphql: Postgres functions now supported](https://supabase.com/blog/pg-graphql-postgres-functions)","level":3,"lines":[192,193],"children":[{"type":"link_open","href":"https://supabase.com/blog/pg-graphql-postgres-functions","title":"","level":0},{"type":"text","content":"pg_graphql: Postgres functions now supported","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0}],"content":"  * [View Support](#view-support)\n- [Filtering](#filtering)\n  * [`is null` and `is not null`](#is-null-and-is-not-null)\n  * [**`like`**, **`ilike`**, and **`startsWith`**](#like-ilike-and-startswith)\n  * [GraphQL directives `@skip` and `@include`](#graphql-directives-skip-and-include)\n  * [User Defined Descriptions](#user-defined-descriptions)\n- [Roadmap](#roadmap)\n- [More pg_graphql](#more-pg_graphql)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"whats-new-in-pg-graphql-v1-2"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>