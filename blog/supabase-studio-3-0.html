<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase Studio 3.0: AI SQL Editor, Schema Diagrams, and new Wrappers</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Supabase Studio now comes with an AI assisted SQL Editor, schema diagrams, and much more." data-next-head=""/><meta property="og:title" content="Supabase Studio 3.0: AI SQL Editor, Schema Diagrams, and new Wrappers" data-next-head=""/><meta property="og:description" content="Supabase Studio now comes with an AI assisted SQL Editor, schema diagrams, and much more." data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-studio-3-0" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2023-08-09" data-next-head=""/><meta property="article:author" content="https://github.com/alaister" data-next-head=""/><meta property="article:author" content="https://github.com/gregnr" data-next-head=""/><meta property="article:author" content="https://github.com/joshenlim" data-next-head=""/><meta property="article:tag" content="launch-week" data-next-head=""/><meta property="article:tag" content="studio" data-next-head=""/><meta property="article:tag" content="AI" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/launch-week-8/day-3/og-day3.jpg" data-next-head=""/><meta property="og:image:alt" content="Supabase Studio 3.0: AI SQL Editor, Schema Diagrams, and new Wrappers thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Supabase Studio 3.0: AI SQL Editor, Schema Diagrams, and new Wrappers</h1><div class="text-light flex space-x-3 text-sm"><p>09 Aug 2023</p><p>•</p><p>8 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/alaister"><div class="flex items-center gap-3"><div class="w-10"><img alt="Alaister Young avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Falaister.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Falaister.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Falaister.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Alaister Young</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/gregnr"><div class="flex items-center gap-3"><div class="w-10"><img alt="Greg Richardson avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fgregnr.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fgregnr.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fgregnr.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Greg Richardson</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/joshenlim"><div class="flex items-center gap-3"><div class="w-10"><img alt="Joshen Lim avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fjoshenlim.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fjoshenlim.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fjoshenlim.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Joshen Lim</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Supabase Studio 3.0: AI SQL Editor, Schema Diagrams, and new Wrappers" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-3%2Fthumb-day3.jpg&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-3%2Fthumb-day3.jpg&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-3%2Fthumb-day3.jpg&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-3%2Fthumb-day3.jpg&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-3%2Fthumb-day3.jpg&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-3%2Fthumb-day3.jpg&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-3%2Fthumb-day3.jpg&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-3%2Fthumb-day3.jpg&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-3%2Fthumb-day3.jpg&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-3%2Fthumb-day3.jpg&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-3%2Fthumb-day3.jpg&amp;w=3840&amp;q=100"/></div><p>Supabase Studio 3.0 is here, with some huge new features, including a brand new Supabase AI, integrated right into our SQL Editor. If you hate writing SQL, you&#x27;ll love this update.</p>
<div class="video-container"><iframe class="w-full" src="https://www.youtube-nocookie.com/embed/51tCMQPiitQ" title="YouTube video player" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share"></iframe></div>
<p>Here&#x27;s the highlight reel:</p>
<ul><li><a href="#supabase-ai-right-in-the-sql-editor"><strong>Supabase AI in the SQL Editor</strong></a>: inline AI, always ready to help</li><li><a href="#schema-visualizer"><strong>Schema Visualizer</strong></a>: — see all your table schemas visually.</li><li><a href="#role-management"><strong>Role Management</strong></a>: — fine-grained access to table data</li><li><a href="#shared-sql-snippets"><strong>Shared SQL Snippets</strong></a>: — share your snippets with the team</li><li><a href="#database-migration-ui"><strong>Database Migration UI</strong></a>: — your database, with receipts</li><li><a href="#wrappers-ui"><strong>Wrappers UI</strong></a>: — easily query foreign data</li></ul>
<h2 id="supabase-ai-right-in-the-sql-editor" class="group scroll-mt-24">Supabase AI, right in the SQL Editor<a href="#supabase-ai-right-in-the-sql-editor" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<video width="99%" autoplay="" loop="" muted="" playsinline="" controls=""><source src="https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/marketing/blog/lw8/supabase-ai.mp4" type="video/mp4"/></video>
<p>In Launch Week 7, <a href="https://supabase.com/blog/supabase-studio-2.0">we added Supabase AI to the Studio</a>. Through our ⌘K menu, you could ask Supabase AI to do all sorts of common tasks — create tables, views, and indexes, write database functions, write RLS policies, and more.</p>
<p>After this release, we had two key realizations:</p>
<ol>
<li>people love having computers write their SQL for them!</li>
<li>many of you are using the SQL Editor as the heart (and engine!) of your projects.</li>
</ol>
<p>Today, we&#x27;re releasing a huge improvement to our SQL Editor. First up, we&#x27;ve added Supabase AI directly into the editor. It&#x27;s always accessible, and ready to help. As before, you can give it a prompt (<code class="short-inline-codeblock">create an orders table for me</code>) and it will return the SQL for you, but now it does so much more.</p>
<p>Supabase AI is aware of the SQL snippet in the editor and can modify it for you. You can ask it to change <code class="short-inline-codeblock">customers</code> to <code class="short-inline-codeblock">customer_orders</code>, for example. You can interact with the code the same way you would converse with ChatGPT until it&#x27;s just right.</p>
<!-- -->
<p>Next, we&#x27;ve added a diff view for changes that Supabase AI makes to your SQL snippet. You can tell Supabase AI what you want changed, and visualize it as you would a Git diff. From this view, you can accept or reject the diffs, and keep asking Supabase AI to make changes until you&#x27;re satisfied.</p>
<!-- -->
<p>We&#x27;ve wondered for a long time how to make it easier to teach developers how to use SQL. It&#x27;s fortunate we didn&#x27;t solve this problem too quickly, as it turns out that AI does a much better job than we could do ourselves.</p>
<p>With Supabase AI, you won&#x27;t even need the whole weekend to scale to millions. Head over to the <a href="https://supabase.com/dashboard/project/_/sql">SQL Editor</a> and give it a try!</p>
<p>In the coming months, we&#x27;re looking to sprinkle Supabase AI through more parts of the Studio. With Postgres under the hood, there&#x27;s so much we can do with SQL and a little bit of AI to help you move fast. Keep an eye out for the Supabase AI icon, you never know where it will pop up next.</p>
<p></p>
<hr/>
<p>Along with these huge AI features, we also added a bunch of new improvements elsewhere around the Studio. Several of these features have come either from requests from the community or are contributions by community members themselves.</p>
<div class="bg-alternative border rounded-lg px-10 py-2 italic"><p>📢 Many of the features and enhancements below came from user requests
<a href="https://github.com/orgs/supabase/discussions/categories/feature-requests">Please keep them coming</a>!</p></div>
<h2 id="schema-visualizer" class="group scroll-mt-24">Schema Visualizer<a href="#schema-visualizer" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<!-- -->
<p>For a while now, many Supabase users have been using <a href="https://github.com/zernonia">Zernonia&#x27;s</a> <a href="https://supabase-schema.vercel.app/">Supabase Schema visualization tool</a>. While this was an amazing tool, many users wanted to see something like this directly integrated into the Studio.</p>
<p>We opened an <a href="https://github.com/supabase/supabase/issues/15585">Issue for it on Github</a> and within a day or two the wheels were in motion. After a couple of weeks, the feature was polished up and merged. It&#x27;s inspiring to see the power of open source at work. This feature wasn&#x27;t trivial, and to see community members take it from feature request to production in just a couple of weeks is mind-blowing. Unquestionably, we have one of the best open source communities out there. Huge thanks to <a href="https://github.com/kamilogorek">kamilogorek</a> and <a href="https://github.com/adiologydev">adiologydev</a> for their work on this!</p>
<p>Special thanks as well to <a href="https://twitter.com/zernonia">Zernonia</a> for providing the inspiration for this great new feature! OSS FTW.</p>
<h2 id="role-management" class="group scroll-mt-24">Role Management<a href="#role-management" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Postgres has built-in support for managing users and roles, and this release, we&#x27;re happy to release a UI for it in the Studio. This is another extremely common feature request, fulfilled almost completely by a community member.</p>
<p>A few months back, we saw this <a href="https://github.com/supabase/supabase/pull/13745">PR</a> come in out of the blue from <a href="https://github.com/HTMHell">HTMHell</a>. They built the entire thing with zero help or direction from our team. We were blown away. We had some changes to make on the backend to properly accommodate the UI, and now we&#x27;re almost ready to get this out into the wild!</p>
<!-- -->
<p>Due to the security focus of this feature, we want to make sure we do a very thorough job of testing, so we&#x27;re hoping to make this generally available in the next week or so.</p>
<p>Massive thanks to <a href="https://github.com/HTMHell">HTMHell</a> (amazing handle btw) for the work on this!</p>
<h2 id="shared-sql-snippets" class="group scroll-mt-24">Shared SQL Snippets<a href="#shared-sql-snippets" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Speaking of commonly requested features, this one has to be in the all-time top 5. Your beautiful, hand-crafted SQL snippets used to be yours and yours alone. Now you can share them with team members and let them bask in your technical prowess.</p>
<!-- -->
<p>You can create a set of project-wide snippets for doing common tasks, making it faster to collaborate and build. To share a snippet, just take a personal snippet that <del>Supabase AI</del> you wrote and share it with the project. It will show up in a new Project Snippets list that&#x27;s visible to everyone on the team.</p>
<p>Teamwork makes the dream work!</p>
<h2 id="database-migration-ui" class="group scroll-mt-24">Database Migration UI<a href="#database-migration-ui" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We&#x27;re releasing a new UI for working with database migrations right from the Studio. Database migrations give you a way to update your database using version-controlled SQL files. They describe changes that you want to make to your database, and also keep track of what changes have been made to your database over time.</p>
<p>As migrations get run against your project from the CLI, you can see information in the Studio about when the migration was run, by who and what changes were made. <a href="https://supabase.com/docs/guides/deployment/database-migrations">See the documentation</a> to get started with migrations.</p>
<!-- -->
<h2 id="wrappers-ui" class="group scroll-mt-24">Wrappers UI<a href="#wrappers-ui" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>During Launch Week 6, we <a href="https://supabase.com/blog/postgres-foreign-data-wrappers-rust">announced</a> Supabase Wrappers — a framework for building foreign data wrappers with Postgres. Wrappers allow your Supabase project to act as a one-stop hub for your data.</p>
<p>When we released Wrappers, we had support for just two providers — Stripe and Firebase. We&#x27;re now up to 6! This round, we&#x27;re happy to release support for S3, ClickHouse, BigQuery, and Logflare! Wrappers add a mind-bending level of extensibility to Supabase projects. You can pull data straight into your projects as though they were normal Supabase tables — you can even query them with our client libraries. It&#x27;s a whole new world of possibilities.</p>
<!-- -->
<h2 id="wrapping-up" class="group scroll-mt-24">Wrapping Up<a href="#wrapping-up" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We hope you get a lot of value out of these new features and enhancements. As we mentioned earlier, many of the features listed here came directly from <a href="https://github.com/orgs/supabase/discussions/categories/feature-requests">Feature Requests</a> on GitHub. Thanks to everyone who has taken the time to submit these, and encourage submissions for anything else you&#x27;d like to see.</p>
<h2 id="more-launch-week-8" class="group scroll-mt-24">More Launch Week 8<a href="#more-launch-week-8" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ul>
<li><a href="supabase-local-dev.html">Supabase Local Dev: migrations, branching, and observability</a></li>
<li><a href="https://supabase.com/blog/hugging-face-supabase">Hugging Face is now supported in Supabase</a></li>
<li><a href="../launch-week.html">Launch Week 8</a></li>
<li><a href="https://supabase.com/blog/interactive-constellation-threejs-react-three-fiber">Coding the stars - an interactive constellation with Three.js and React Three Fiber</a></li>
<li><a href="https://supabase.com/blog/why-supabase-remote">Why we&#x27;ll stay remote</a></li>
<li><a href="https://github.com/supabase/postgres_lsp">Postgres Language Server</a></li>
</ul></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-studio-3-0&amp;text=Supabase%20Studio%203.0%3A%20AI%20SQL%20Editor%2C%20Schema%20Diagrams%2C%20and%20new%20Wrappers"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-studio-3-0&amp;text=Supabase%20Studio%203.0%3A%20AI%20SQL%20Editor%2C%20Schema%20Diagrams%2C%20and%20new%20Wrappers"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-studio-3-0&amp;t=Supabase%20Studio%203.0%3A%20AI%20SQL%20Editor%2C%20Schema%20Diagrams%2C%20and%20new%20Wrappers"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="using-supabase-with-vercel.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Vercel Integration and Next.js App Router Support</h4><p class="small">10 August 2023</p></div></div></div></div></a></div><div><a href="supabase-local-dev.html"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Supabase Local Dev: migrations, branching, and observability</h4><p class="small">8 August 2023</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/launch-week"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">launch-week</div></a><a href="https://supabase.com/blog/tags/studio"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">studio</div></a><a href="https://supabase.com/blog/tags/AI"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">AI</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#supabase-ai-right-in-the-sql-editor">Supabase AI, right in the SQL Editor</a></li>
<li><a href="#schema-visualizer">Schema Visualizer</a></li>
<li><a href="#role-management">Role Management</a></li>
<li><a href="#shared-sql-snippets">Shared SQL Snippets</a></li>
<li><a href="#database-migration-ui">Database Migration UI</a></li>
<li><a href="#wrappers-ui">Wrappers UI</a></li>
<li><a href="#wrapping-up">Wrapping Up</a></li>
<li><a href="#more-launch-week-8">More Launch Week 8</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-studio-3-0&amp;text=Supabase%20Studio%203.0%3A%20AI%20SQL%20Editor%2C%20Schema%20Diagrams%2C%20and%20new%20Wrappers"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-studio-3-0&amp;text=Supabase%20Studio%203.0%3A%20AI%20SQL%20Editor%2C%20Schema%20Diagrams%2C%20and%20new%20Wrappers"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-studio-3-0&amp;t=Supabase%20Studio%203.0%3A%20AI%20SQL%20Editor%2C%20Schema%20Diagrams%2C%20and%20new%20Wrappers"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"using-supabase-with-vercel","title":"Vercel Integration and Next.js App Router Support","description":"Using Supabase with Vercel and Next.js is now a lot easier.","launchweek":"8","categories":["product"],"tags":["launch-week","integrations"],"date":"2023-08-10","toc_depth":3,"author":"jonny,jonmeyers_io","image":"launch-week-8/day-4/vercel-and-supabase-og.jpg","thumb":"launch-week-8/day-4/vercel-and-supabase-thumb.jpg","formattedDate":"10 August 2023","readingTime":"6 minute read","url":"/blog/using-supabase-with-vercel","path":"/blog/using-supabase-with-vercel"},"nextPost":{"slug":"supabase-local-dev","title":"Supabase Local Dev: migrations, branching, and observability","description":"New features to streamline the interaction between CLI, code editors, and remote databases.","launchweek":"8","categories":["product"],"tags":["launch-week","announcements"],"date":"2023-08-08","toc_depth":3,"author":"qiao,soedirgo,jonny","image":"launch-week-8/day-2/OG-day2.jpg","thumb":"launch-week-8/day-2/thumb-day2.jpg","formattedDate":"8 August 2023","readingTime":"16 minute read","url":"/blog/supabase-local-dev","path":"/blog/supabase-local-dev"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-studio-3-0","source":"\nSupabase Studio 3.0 is here, with some huge new features, including a brand new Supabase AI, integrated right into our SQL Editor. If you hate writing SQL, you'll love this update.\n\n\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/51tCMQPiitQ\"\n    title=\"YouTube video player\"\n    frameBorder=\"0\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"\n    allowfullscreen\n  \u003e\u003c/iframe\u003e\n\u003c/div\u003e\n\nHere's the highlight reel:\n\n\u003cul\u003e\n  {/* prettier-ignore */}\n  \u003cli\u003e[**Supabase AI in the SQL Editor**](#supabase-ai-right-in-the-sql-editor): inline AI, always ready to help\u003c/li\u003e\n  \u003cli\u003e[**Schema Visualizer**](#schema-visualizer): — see all your table schemas visually.\u003c/li\u003e\n  \u003cli\u003e[**Role Management**](#role-management): — fine-grained access to table data\u003c/li\u003e\n  \u003cli\u003e[**Shared SQL Snippets**](#shared-sql-snippets): — share your snippets with the team\u003c/li\u003e\n  \u003cli\u003e[**Database Migration UI**](#database-migration-ui): — your database, with receipts\u003c/li\u003e\n  \u003cli\u003e[**Wrappers UI**](#wrappers-ui): — easily query foreign data\u003c/li\u003e\n\u003c/ul\u003e\n\n## Supabase AI, right in the SQL Editor\n\n\u003cvideo width=\"99%\" autoPlay loop muted playsInline controls={true}\u003e\n  \u003csource\n    src=\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/marketing/blog/lw8/supabase-ai.mp4\"\n    type=\"video/mp4\"\n  /\u003e\n\u003c/video\u003e\n\nIn Launch Week 7, [we added Supabase AI to the Studio](https://supabase.com/blog/supabase-studio-2.0). Through our ⌘K menu, you could ask Supabase AI to do all sorts of common tasks — create tables, views, and indexes, write database functions, write RLS policies, and more.\n\nAfter this release, we had two key realizations:\n\n1. people love having computers write their SQL for them!\n2. many of you are using the SQL Editor as the heart (and engine!) of your projects.\n\nToday, we're releasing a huge improvement to our SQL Editor. First up, we've added Supabase AI directly into the editor. It's always accessible, and ready to help. As before, you can give it a prompt (`create an orders table for me`) and it will return the SQL for you, but now it does so much more.\n\nSupabase AI is aware of the SQL snippet in the editor and can modify it for you. You can ask it to change `customers` to `customer_orders`, for example. You can interact with the code the same way you would converse with ChatGPT until it's just right.\n\n\u003cImg\n  src=\"/images/blog/launch-week-8/day-3/create-orders.png\"\n  alt=\"Create a table\"\n  zoomable={false}\n/\u003e\n\nNext, we've added a diff view for changes that Supabase AI makes to your SQL snippet. You can tell Supabase AI what you want changed, and visualize it as you would a Git diff. From this view, you can accept or reject the diffs, and keep asking Supabase AI to make changes until you're satisfied.\n\n\u003cImg\n  src=\"/images/blog/launch-week-8/day-3/create-orders-diff.png\"\n  alt=\"Create a table diff\"\n  zoomable={false}\n/\u003e\n\nWe've wondered for a long time how to make it easier to teach developers how to use SQL. It's fortunate we didn't solve this problem too quickly, as it turns out that AI does a much better job than we could do ourselves.\n\nWith Supabase AI, you won't even need the whole weekend to scale to millions. Head over to the [SQL Editor](https://supabase.com/dashboard/project/_/sql) and give it a try!\n\nIn the coming months, we're looking to sprinkle Supabase AI through more parts of the Studio. With Postgres under the hood, there's so much we can do with SQL and a little bit of AI to help you move fast. Keep an eye out for the Supabase AI icon, you never know where it will pop up next.\n\n![Supabase AI](/images/blog/launch-week-8/day-2/supabase-ai-loading-animation.gif)\n\n---\n\nAlong with these huge AI features, we also added a bunch of new improvements elsewhere around the Studio. Several of these features have come either from requests from the community or are contributions by community members themselves.\n\n\u003cdiv className=\"bg-alternative border rounded-lg px-10 py-2 italic\"\u003e\n\n📢 Many of the features and enhancements below came from user requests\n[Please keep them coming](https://github.com/orgs/supabase/discussions/categories/feature-requests)!\n\n\u003c/div\u003e\n\n## Schema Visualizer\n\n\u003cImg src=\"/images/blog/launch-week-8/day-3/visualizer.png\" alt=\"Schema Visualizer\" /\u003e\n\nFor a while now, many Supabase users have been using [Zernonia's](https://github.com/zernonia) [Supabase Schema visualization tool](https://supabase-schema.vercel.app/). While this was an amazing tool, many users wanted to see something like this directly integrated into the Studio.\n\nWe opened an [Issue for it on Github](https://github.com/supabase/supabase/issues/15585) and within a day or two the wheels were in motion. After a couple of weeks, the feature was polished up and merged. It's inspiring to see the power of open source at work. This feature wasn't trivial, and to see community members take it from feature request to production in just a couple of weeks is mind-blowing. Unquestionably, we have one of the best open source communities out there. Huge thanks to [kamilogorek](https://github.com/kamilogorek) and [adiologydev](https://github.com/adiologydev) for their work on this!\n\nSpecial thanks as well to [Zernonia](https://twitter.com/zernonia) for providing the inspiration for this great new feature! OSS FTW.\n\n## Role Management\n\nPostgres has built-in support for managing users and roles, and this release, we're happy to release a UI for it in the Studio. This is another extremely common feature request, fulfilled almost completely by a community member.\n\nA few months back, we saw this [PR](https://github.com/supabase/supabase/pull/13745) come in out of the blue from [HTMHell](https://github.com/HTMHell). They built the entire thing with zero help or direction from our team. We were blown away. We had some changes to make on the backend to properly accommodate the UI, and now we're almost ready to get this out into the wild!\n\n\u003cImg src=\"/images/blog/launch-week-8/day-3/role-management.png\" alt=\"Role management\" /\u003e\n\nDue to the security focus of this feature, we want to make sure we do a very thorough job of testing, so we're hoping to make this generally available in the next week or so.\n\nMassive thanks to [HTMHell](https://github.com/HTMHell) (amazing handle btw) for the work on this!\n\n## Shared SQL Snippets\n\nSpeaking of commonly requested features, this one has to be in the all-time top 5. Your beautiful, hand-crafted SQL snippets used to be yours and yours alone. Now you can share them with team members and let them bask in your technical prowess.\n\n\u003cImg\n  src=\"/images/blog/launch-week-8/day-3/share-query.png\"\n  alt=\"Share SQL Snippets\"\n  zoomable={false}\n/\u003e\n\nYou can create a set of project-wide snippets for doing common tasks, making it faster to collaborate and build. To share a snippet, just take a personal snippet that ~~Supabase AI~~ you wrote and share it with the project. It will show up in a new Project Snippets list that's visible to everyone on the team.\n\nTeamwork makes the dream work!\n\n## Database Migration UI\n\nWe're releasing a new UI for working with database migrations right from the Studio. Database migrations give you a way to update your database using version-controlled SQL files. They describe changes that you want to make to your database, and also keep track of what changes have been made to your database over time.\n\nAs migrations get run against your project from the CLI, you can see information in the Studio about when the migration was run, by who and what changes were made. [See the documentation](https://supabase.com/docs/guides/deployment/database-migrations) to get started with migrations.\n\n\u003cImg src=\"/images/blog/launch-week-8/day-3/migrations.png\" alt=\"Migrations UI\" /\u003e\n\n## Wrappers UI\n\nDuring Launch Week 6, we [announced](https://supabase.com/blog/postgres-foreign-data-wrappers-rust) Supabase Wrappers — a framework for building foreign data wrappers with Postgres. Wrappers allow your Supabase project to act as a one-stop hub for your data.\n\nWhen we released Wrappers, we had support for just two providers — Stripe and Firebase. We're now up to 6! This round, we're happy to release support for S3, ClickHouse, BigQuery, and Logflare! Wrappers add a mind-bending level of extensibility to Supabase projects. You can pull data straight into your projects as though they were normal Supabase tables — you can even query them with our client libraries. It's a whole new world of possibilities.\n\n\u003cImg src=\"/images/blog/launch-week-8/day-3/wrappers.png\" alt=\"Wrappers\" /\u003e\n\n## Wrapping Up\n\nWe hope you get a lot of value out of these new features and enhancements. As we mentioned earlier, many of the features listed here came directly from [Feature Requests](https://github.com/orgs/supabase/discussions/categories/feature-requests) on GitHub. Thanks to everyone who has taken the time to submit these, and encourage submissions for anything else you'd like to see.\n\n## More Launch Week 8\n\n- [Supabase Local Dev: migrations, branching, and observability](https://supabase.com/blog/supabase-local-dev)\n- [Hugging Face is now supported in Supabase](https://supabase.com/blog/hugging-face-supabase)\n- [Launch Week 8](https://supabase.com/launch-week)\n- [Coding the stars - an interactive constellation with Three.js and React Three Fiber](https://supabase.com/blog/interactive-constellation-threejs-react-three-fiber)\n- [Why we'll stay remote](https://supabase.com/blog/why-supabase-remote)\n- [Postgres Language Server](https://github.com/supabase/postgres_lsp)\n","title":"Supabase Studio 3.0: AI SQL Editor, Schema Diagrams, and new Wrappers","description":"Supabase Studio now comes with an AI assisted SQL Editor, schema diagrams, and much more.","launchweek":"8","categories":["product"],"tags":["launch-week","studio","AI"],"date":"2023-08-09","toc_depth":3,"author":"alaister,gregnr,joshenlim","image":"launch-week-8/day-3/og-day3.jpg","thumb":"launch-week-8/day-3/thumb-day3.jpg","content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\n/*prettier-ignore*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    a: \"a\",\n    strong: \"strong\",\n    h2: \"h2\",\n    ol: \"ol\",\n    li: \"li\",\n    code: \"code\",\n    img: \"img\",\n    hr: \"hr\",\n    del: \"del\",\n    ul: \"ul\"\n  }, _provideComponents(), props.components), {Img} = _components;\n  if (!Img) _missingMdxReference(\"Img\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(_components.p, {\n      children: \"Supabase Studio 3.0 is here, with some huge new features, including a brand new Supabase AI, integrated right into our SQL Editor. If you hate writing SQL, you'll love this update.\"\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"video-container\",\n      children: _jsx(\"iframe\", {\n        className: \"w-full\",\n        src: \"https://www.youtube-nocookie.com/embed/51tCMQPiitQ\",\n        title: \"YouTube video player\",\n        frameBorder: \"0\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\",\n        allowfullscreen: true\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Here's the highlight reel:\"\n    }), \"\\n\", _jsxs(\"ul\", {\n      children: [_jsxs(\"li\", {\n        children: [_jsx(_components.a, {\n          href: \"#supabase-ai-right-in-the-sql-editor\",\n          children: _jsx(_components.strong, {\n            children: \"Supabase AI in the SQL Editor\"\n          })\n        }), \": inline AI, always ready to help\"]\n      }), _jsxs(\"li\", {\n        children: [_jsx(_components.a, {\n          href: \"#schema-visualizer\",\n          children: _jsx(_components.strong, {\n            children: \"Schema Visualizer\"\n          })\n        }), \": — see all your table schemas visually.\"]\n      }), _jsxs(\"li\", {\n        children: [_jsx(_components.a, {\n          href: \"#role-management\",\n          children: _jsx(_components.strong, {\n            children: \"Role Management\"\n          })\n        }), \": — fine-grained access to table data\"]\n      }), _jsxs(\"li\", {\n        children: [_jsx(_components.a, {\n          href: \"#shared-sql-snippets\",\n          children: _jsx(_components.strong, {\n            children: \"Shared SQL Snippets\"\n          })\n        }), \": — share your snippets with the team\"]\n      }), _jsxs(\"li\", {\n        children: [_jsx(_components.a, {\n          href: \"#database-migration-ui\",\n          children: _jsx(_components.strong, {\n            children: \"Database Migration UI\"\n          })\n        }), \": — your database, with receipts\"]\n      }), _jsxs(\"li\", {\n        children: [_jsx(_components.a, {\n          href: \"#wrappers-ui\",\n          children: _jsx(_components.strong, {\n            children: \"Wrappers UI\"\n          })\n        }), \": — easily query foreign data\"]\n      })]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"supabase-ai-right-in-the-sql-editor\",\n      children: \"Supabase AI, right in the SQL Editor\"\n    }), \"\\n\", _jsx(\"video\", {\n      width: \"99%\",\n      autoPlay: true,\n      loop: true,\n      muted: true,\n      playsInline: true,\n      controls: true,\n      children: _jsx(\"source\", {\n        src: \"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/marketing/blog/lw8/supabase-ai.mp4\",\n        type: \"video/mp4\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In Launch Week 7, \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/supabase-studio-2.0\",\n        children: \"we added Supabase AI to the Studio\"\n      }), \". Through our ⌘K menu, you could ask Supabase AI to do all sorts of common tasks — create tables, views, and indexes, write database functions, write RLS policies, and more.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"After this release, we had two key realizations:\"\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"people love having computers write their SQL for them!\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"many of you are using the SQL Editor as the heart (and engine!) of your projects.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Today, we're releasing a huge improvement to our SQL Editor. First up, we've added Supabase AI directly into the editor. It's always accessible, and ready to help. As before, you can give it a prompt (\", _jsx(_components.code, {\n        children: \"create an orders table for me\"\n      }), \") and it will return the SQL for you, but now it does so much more.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Supabase AI is aware of the SQL snippet in the editor and can modify it for you. You can ask it to change \", _jsx(_components.code, {\n        children: \"customers\"\n      }), \" to \", _jsx(_components.code, {\n        children: \"customer_orders\"\n      }), \", for example. You can interact with the code the same way you would converse with ChatGPT until it's just right.\"]\n    }), \"\\n\", _jsx(Img, {\n      src: \"/images/blog/launch-week-8/day-3/create-orders.png\",\n      alt: \"Create a table\",\n      zoomable: false\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Next, we've added a diff view for changes that Supabase AI makes to your SQL snippet. You can tell Supabase AI what you want changed, and visualize it as you would a Git diff. From this view, you can accept or reject the diffs, and keep asking Supabase AI to make changes until you're satisfied.\"\n    }), \"\\n\", _jsx(Img, {\n      src: \"/images/blog/launch-week-8/day-3/create-orders-diff.png\",\n      alt: \"Create a table diff\",\n      zoomable: false\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We've wondered for a long time how to make it easier to teach developers how to use SQL. It's fortunate we didn't solve this problem too quickly, as it turns out that AI does a much better job than we could do ourselves.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"With Supabase AI, you won't even need the whole weekend to scale to millions. Head over to the \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard/project/_/sql\",\n        children: \"SQL Editor\"\n      }), \" and give it a try!\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In the coming months, we're looking to sprinkle Supabase AI through more parts of the Studio. With Postgres under the hood, there's so much we can do with SQL and a little bit of AI to help you move fast. Keep an eye out for the Supabase AI icon, you never know where it will pop up next.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-8/day-2/supabase-ai-loading-animation.gif\",\n        alt: \"Supabase AI\"\n      })\n    }), \"\\n\", _jsx(_components.hr, {}), \"\\n\", _jsx(_components.p, {\n      children: \"Along with these huge AI features, we also added a bunch of new improvements elsewhere around the Studio. Several of these features have come either from requests from the community or are contributions by community members themselves.\"\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"bg-alternative border rounded-lg px-10 py-2 italic\",\n      children: _jsxs(_components.p, {\n        children: [\"📢 Many of the features and enhancements below came from user requests\\n\", _jsx(_components.a, {\n          href: \"https://github.com/orgs/supabase/discussions/categories/feature-requests\",\n          children: \"Please keep them coming\"\n        }), \"!\"]\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"schema-visualizer\",\n      children: \"Schema Visualizer\"\n    }), \"\\n\", _jsx(Img, {\n      src: \"/images/blog/launch-week-8/day-3/visualizer.png\",\n      alt: \"Schema Visualizer\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"For a while now, many Supabase users have been using \", _jsx(_components.a, {\n        href: \"https://github.com/zernonia\",\n        children: \"Zernonia's\"\n      }), \" \", _jsx(_components.a, {\n        href: \"https://supabase-schema.vercel.app/\",\n        children: \"Supabase Schema visualization tool\"\n      }), \". While this was an amazing tool, many users wanted to see something like this directly integrated into the Studio.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We opened an \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/supabase/issues/15585\",\n        children: \"Issue for it on Github\"\n      }), \" and within a day or two the wheels were in motion. After a couple of weeks, the feature was polished up and merged. It's inspiring to see the power of open source at work. This feature wasn't trivial, and to see community members take it from feature request to production in just a couple of weeks is mind-blowing. Unquestionably, we have one of the best open source communities out there. Huge thanks to \", _jsx(_components.a, {\n        href: \"https://github.com/kamilogorek\",\n        children: \"kamilogorek\"\n      }), \" and \", _jsx(_components.a, {\n        href: \"https://github.com/adiologydev\",\n        children: \"adiologydev\"\n      }), \" for their work on this!\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Special thanks as well to \", _jsx(_components.a, {\n        href: \"https://twitter.com/zernonia\",\n        children: \"Zernonia\"\n      }), \" for providing the inspiration for this great new feature! OSS FTW.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"role-management\",\n      children: \"Role Management\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Postgres has built-in support for managing users and roles, and this release, we're happy to release a UI for it in the Studio. This is another extremely common feature request, fulfilled almost completely by a community member.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"A few months back, we saw this \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/supabase/pull/13745\",\n        children: \"PR\"\n      }), \" come in out of the blue from \", _jsx(_components.a, {\n        href: \"https://github.com/HTMHell\",\n        children: \"HTMHell\"\n      }), \". They built the entire thing with zero help or direction from our team. We were blown away. We had some changes to make on the backend to properly accommodate the UI, and now we're almost ready to get this out into the wild!\"]\n    }), \"\\n\", _jsx(Img, {\n      src: \"/images/blog/launch-week-8/day-3/role-management.png\",\n      alt: \"Role management\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Due to the security focus of this feature, we want to make sure we do a very thorough job of testing, so we're hoping to make this generally available in the next week or so.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Massive thanks to \", _jsx(_components.a, {\n        href: \"https://github.com/HTMHell\",\n        children: \"HTMHell\"\n      }), \" (amazing handle btw) for the work on this!\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"shared-sql-snippets\",\n      children: \"Shared SQL Snippets\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Speaking of commonly requested features, this one has to be in the all-time top 5. Your beautiful, hand-crafted SQL snippets used to be yours and yours alone. Now you can share them with team members and let them bask in your technical prowess.\"\n    }), \"\\n\", _jsx(Img, {\n      src: \"/images/blog/launch-week-8/day-3/share-query.png\",\n      alt: \"Share SQL Snippets\",\n      zoomable: false\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You can create a set of project-wide snippets for doing common tasks, making it faster to collaborate and build. To share a snippet, just take a personal snippet that \", _jsx(_components.del, {\n        children: \"Supabase AI\"\n      }), \" you wrote and share it with the project. It will show up in a new Project Snippets list that's visible to everyone on the team.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Teamwork makes the dream work!\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"database-migration-ui\",\n      children: \"Database Migration UI\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We're releasing a new UI for working with database migrations right from the Studio. Database migrations give you a way to update your database using version-controlled SQL files. They describe changes that you want to make to your database, and also keep track of what changes have been made to your database over time.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"As migrations get run against your project from the CLI, you can see information in the Studio about when the migration was run, by who and what changes were made. \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/deployment/database-migrations\",\n        children: \"See the documentation\"\n      }), \" to get started with migrations.\"]\n    }), \"\\n\", _jsx(Img, {\n      src: \"/images/blog/launch-week-8/day-3/migrations.png\",\n      alt: \"Migrations UI\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"wrappers-ui\",\n      children: \"Wrappers UI\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"During Launch Week 6, we \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/postgres-foreign-data-wrappers-rust\",\n        children: \"announced\"\n      }), \" Supabase Wrappers — a framework for building foreign data wrappers with Postgres. Wrappers allow your Supabase project to act as a one-stop hub for your data.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"When we released Wrappers, we had support for just two providers — Stripe and Firebase. We're now up to 6! This round, we're happy to release support for S3, ClickHouse, BigQuery, and Logflare! Wrappers add a mind-bending level of extensibility to Supabase projects. You can pull data straight into your projects as though they were normal Supabase tables — you can even query them with our client libraries. It's a whole new world of possibilities.\"\n    }), \"\\n\", _jsx(Img, {\n      src: \"/images/blog/launch-week-8/day-3/wrappers.png\",\n      alt: \"Wrappers\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"wrapping-up\",\n      children: \"Wrapping Up\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We hope you get a lot of value out of these new features and enhancements. As we mentioned earlier, many of the features listed here came directly from \", _jsx(_components.a, {\n        href: \"https://github.com/orgs/supabase/discussions/categories/feature-requests\",\n        children: \"Feature Requests\"\n      }), \" on GitHub. Thanks to everyone who has taken the time to submit these, and encourage submissions for anything else you'd like to see.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"more-launch-week-8\",\n      children: \"More Launch Week 8\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/blog/supabase-local-dev\",\n          children: \"Supabase Local Dev: migrations, branching, and observability\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/blog/hugging-face-supabase\",\n          children: \"Hugging Face is now supported in Supabase\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/launch-week\",\n          children: \"Launch Week 8\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/blog/interactive-constellation-threejs-react-three-fiber\",\n          children: \"Coding the stars - an interactive constellation with Three.js and React Three Fiber\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/blog/why-supabase-remote\",\n          children: \"Why we'll stay remote\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase/postgres_lsp\",\n          children: \"Postgres Language Server\"\n        })\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"Supabase AI, right in the SQL Editor","slug":"supabase-ai-right-in-the-sql-editor","lvl":2,"i":0,"seen":0},{"content":"Schema Visualizer","slug":"schema-visualizer","lvl":2,"i":1,"seen":0},{"content":"Role Management","slug":"role-management","lvl":2,"i":2,"seen":0},{"content":"Shared SQL Snippets","slug":"shared-sql-snippets","lvl":2,"i":3,"seen":0},{"content":"Database Migration UI","slug":"database-migration-ui","lvl":2,"i":4,"seen":0},{"content":"Wrappers UI","slug":"wrappers-ui","lvl":2,"i":5,"seen":0},{"content":"Wrapping Up","slug":"wrapping-up","lvl":2,"i":6,"seen":0},{"content":"More Launch Week 8","slug":"more-launch-week-8","lvl":2,"i":7,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,2],"level":0},{"type":"inline","content":"Supabase Studio 3.0 is here, with some huge new features, including a brand new Supabase AI, integrated right into our SQL Editor. If you hate writing SQL, you'll love this update.","level":1,"lines":[1,2],"children":[{"type":"text","content":"Supabase Studio 3.0 is here, with some huge new features, including a brand new Supabase AI, integrated right into our SQL Editor. If you hate writing SQL, you'll love this update.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[3,11],"level":0},{"type":"inline","content":"\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/51tCMQPiitQ\"\n    title=\"YouTube video player\"\n    frameBorder=\"0\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"\n    allowfullscreen","level":1,"lines":[3,11],"children":[{"type":"text","content":"\u003cdiv className=\"video-container\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ciframe","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"w-full\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://www.youtube-nocookie.com/embed/51tCMQPiitQ\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"title=\"YouTube video player\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"frameBorder=\"0\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allowfullscreen","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"blockquote_open","lines":[11,13],"level":0},{"type":"paragraph_open","tight":false,"lines":[11,13],"level":1},{"type":"inline","content":"\u003c/iframe\u003e\n\u003c/div\u003e","level":2,"lines":[11,13],"children":[{"type":"text","content":"\u003c/iframe\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"paragraph_open","tight":false,"lines":[14,15],"level":0},{"type":"inline","content":"Here's the highlight reel:","level":1,"lines":[14,15],"children":[{"type":"text","content":"Here's the highlight reel:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[16,25],"level":0},{"type":"inline","content":"\u003cul\u003e\n  {/* prettier-ignore */}\n  \u003cli\u003e[**Supabase AI in the SQL Editor**](#supabase-ai-right-in-the-sql-editor): inline AI, always ready to help\u003c/li\u003e\n  \u003cli\u003e[**Schema Visualizer**](#schema-visualizer): — see all your table schemas visually.\u003c/li\u003e\n  \u003cli\u003e[**Role Management**](#role-management): — fine-grained access to table data\u003c/li\u003e\n  \u003cli\u003e[**Shared SQL Snippets**](#shared-sql-snippets): — share your snippets with the team\u003c/li\u003e\n  \u003cli\u003e[**Database Migration UI**](#database-migration-ui): — your database, with receipts\u003c/li\u003e\n  \u003cli\u003e[**Wrappers UI**](#wrappers-ui): — easily query foreign data\u003c/li\u003e\n\u003c/ul\u003e","level":1,"lines":[16,25],"children":[{"type":"text","content":"\u003cul\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{/* prettier-ignore */}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cli\u003e","level":0},{"type":"link_open","href":"#supabase-ai-right-in-the-sql-editor","title":"","level":0},{"type":"strong_open","level":1},{"type":"text","content":"Supabase AI in the SQL Editor","level":2},{"type":"strong_close","level":1},{"type":"link_close","level":0},{"type":"text","content":": inline AI, always ready to help\u003c/li\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cli\u003e","level":0},{"type":"link_open","href":"#schema-visualizer","title":"","level":0},{"type":"strong_open","level":1},{"type":"text","content":"Schema Visualizer","level":2},{"type":"strong_close","level":1},{"type":"link_close","level":0},{"type":"text","content":": — see all your table schemas visually.\u003c/li\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cli\u003e","level":0},{"type":"link_open","href":"#role-management","title":"","level":0},{"type":"strong_open","level":1},{"type":"text","content":"Role Management","level":2},{"type":"strong_close","level":1},{"type":"link_close","level":0},{"type":"text","content":": — fine-grained access to table data\u003c/li\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cli\u003e","level":0},{"type":"link_open","href":"#shared-sql-snippets","title":"","level":0},{"type":"strong_open","level":1},{"type":"text","content":"Shared SQL Snippets","level":2},{"type":"strong_close","level":1},{"type":"link_close","level":0},{"type":"text","content":": — share your snippets with the team\u003c/li\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cli\u003e","level":0},{"type":"link_open","href":"#database-migration-ui","title":"","level":0},{"type":"strong_open","level":1},{"type":"text","content":"Database Migration UI","level":2},{"type":"strong_close","level":1},{"type":"link_close","level":0},{"type":"text","content":": — your database, with receipts\u003c/li\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cli\u003e","level":0},{"type":"link_open","href":"#wrappers-ui","title":"","level":0},{"type":"strong_open","level":1},{"type":"text","content":"Wrappers UI","level":2},{"type":"strong_close","level":1},{"type":"link_close","level":0},{"type":"text","content":": — easily query foreign data\u003c/li\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/ul\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[26,27],"level":0},{"type":"inline","content":"[Supabase AI, right in the SQL Editor](#supabase-ai-right-in-the-sql-editor)","level":1,"lines":[26,27],"children":[{"type":"text","content":"Supabase AI, right in the SQL Editor","level":0}],"lvl":2,"i":0,"seen":0,"slug":"supabase-ai-right-in-the-sql-editor"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[28,34],"level":0},{"type":"inline","content":"\u003cvideo width=\"99%\" autoPlay loop muted playsInline controls={true}\u003e\n  \u003csource\n    src=\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/marketing/blog/lw8/supabase-ai.mp4\"\n    type=\"video/mp4\"\n  /\u003e\n\u003c/video\u003e","level":1,"lines":[28,34],"children":[{"type":"text","content":"\u003cvideo width=\"99%\" autoPlay loop muted playsInline controls={true}\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003csource","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/marketing/blog/lw8/supabase-ai.mp4\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"type=\"video/mp4\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/video\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[35,36],"level":0},{"type":"inline","content":"In Launch Week 7, [we added Supabase AI to the Studio](https://supabase.com/blog/supabase-studio-2.0). Through our ⌘K menu, you could ask Supabase AI to do all sorts of common tasks — create tables, views, and indexes, write database functions, write RLS policies, and more.","level":1,"lines":[35,36],"children":[{"type":"text","content":"In Launch Week 7, ","level":0},{"type":"link_open","href":"https://supabase.com/blog/supabase-studio-2.0","title":"","level":0},{"type":"text","content":"we added Supabase AI to the Studio","level":1},{"type":"link_close","level":0},{"type":"text","content":". Through our ⌘K menu, you could ask Supabase AI to do all sorts of common tasks — create tables, views, and indexes, write database functions, write RLS policies, and more.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[37,38],"level":0},{"type":"inline","content":"After this release, we had two key realizations:","level":1,"lines":[37,38],"children":[{"type":"text","content":"After this release, we had two key realizations:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"ordered_list_open","order":1,"lines":[39,42],"level":0},{"type":"list_item_open","lines":[39,40],"level":1},{"type":"paragraph_open","tight":true,"lines":[39,40],"level":2},{"type":"inline","content":"people love having computers write their SQL for them!","level":3,"lines":[39,40],"children":[{"type":"text","content":"people love having computers write their SQL for them!","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[40,42],"level":1},{"type":"paragraph_open","tight":true,"lines":[40,41],"level":2},{"type":"inline","content":"many of you are using the SQL Editor as the heart (and engine!) of your projects.","level":3,"lines":[40,41],"children":[{"type":"text","content":"many of you are using the SQL Editor as the heart (and engine!) of your projects.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"ordered_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[42,43],"level":0},{"type":"inline","content":"Today, we're releasing a huge improvement to our SQL Editor. First up, we've added Supabase AI directly into the editor. It's always accessible, and ready to help. As before, you can give it a prompt (`create an orders table for me`) and it will return the SQL for you, but now it does so much more.","level":1,"lines":[42,43],"children":[{"type":"text","content":"Today, we're releasing a huge improvement to our SQL Editor. First up, we've added Supabase AI directly into the editor. It's always accessible, and ready to help. As before, you can give it a prompt (","level":0},{"type":"code","content":"create an orders table for me","block":false,"level":0},{"type":"text","content":") and it will return the SQL for you, but now it does so much more.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[44,45],"level":0},{"type":"inline","content":"Supabase AI is aware of the SQL snippet in the editor and can modify it for you. You can ask it to change `customers` to `customer_orders`, for example. You can interact with the code the same way you would converse with ChatGPT until it's just right.","level":1,"lines":[44,45],"children":[{"type":"text","content":"Supabase AI is aware of the SQL snippet in the editor and can modify it for you. You can ask it to change ","level":0},{"type":"code","content":"customers","block":false,"level":0},{"type":"text","content":" to ","level":0},{"type":"code","content":"customer_orders","block":false,"level":0},{"type":"text","content":", for example. You can interact with the code the same way you would converse with ChatGPT until it's just right.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[46,51],"level":0},{"type":"inline","content":"\u003cImg\n  src=\"/images/blog/launch-week-8/day-3/create-orders.png\"\n  alt=\"Create a table\"\n  zoomable={false}\n/\u003e","level":1,"lines":[46,51],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"/images/blog/launch-week-8/day-3/create-orders.png\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"Create a table\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"zoomable={false}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[52,53],"level":0},{"type":"inline","content":"Next, we've added a diff view for changes that Supabase AI makes to your SQL snippet. You can tell Supabase AI what you want changed, and visualize it as you would a Git diff. From this view, you can accept or reject the diffs, and keep asking Supabase AI to make changes until you're satisfied.","level":1,"lines":[52,53],"children":[{"type":"text","content":"Next, we've added a diff view for changes that Supabase AI makes to your SQL snippet. You can tell Supabase AI what you want changed, and visualize it as you would a Git diff. From this view, you can accept or reject the diffs, and keep asking Supabase AI to make changes until you're satisfied.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[54,59],"level":0},{"type":"inline","content":"\u003cImg\n  src=\"/images/blog/launch-week-8/day-3/create-orders-diff.png\"\n  alt=\"Create a table diff\"\n  zoomable={false}\n/\u003e","level":1,"lines":[54,59],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"/images/blog/launch-week-8/day-3/create-orders-diff.png\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"Create a table diff\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"zoomable={false}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[60,61],"level":0},{"type":"inline","content":"We've wondered for a long time how to make it easier to teach developers how to use SQL. It's fortunate we didn't solve this problem too quickly, as it turns out that AI does a much better job than we could do ourselves.","level":1,"lines":[60,61],"children":[{"type":"text","content":"We've wondered for a long time how to make it easier to teach developers how to use SQL. It's fortunate we didn't solve this problem too quickly, as it turns out that AI does a much better job than we could do ourselves.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[62,63],"level":0},{"type":"inline","content":"With Supabase AI, you won't even need the whole weekend to scale to millions. Head over to the [SQL Editor](https://supabase.com/dashboard/project/_/sql) and give it a try!","level":1,"lines":[62,63],"children":[{"type":"text","content":"With Supabase AI, you won't even need the whole weekend to scale to millions. Head over to the ","level":0},{"type":"link_open","href":"https://supabase.com/dashboard/project/_/sql","title":"","level":0},{"type":"text","content":"SQL Editor","level":1},{"type":"link_close","level":0},{"type":"text","content":" and give it a try!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[64,65],"level":0},{"type":"inline","content":"In the coming months, we're looking to sprinkle Supabase AI through more parts of the Studio. With Postgres under the hood, there's so much we can do with SQL and a little bit of AI to help you move fast. Keep an eye out for the Supabase AI icon, you never know where it will pop up next.","level":1,"lines":[64,65],"children":[{"type":"text","content":"In the coming months, we're looking to sprinkle Supabase AI through more parts of the Studio. With Postgres under the hood, there's so much we can do with SQL and a little bit of AI to help you move fast. Keep an eye out for the Supabase AI icon, you never know where it will pop up next.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[66,67],"level":0},{"type":"inline","content":"![Supabase AI](/images/blog/launch-week-8/day-2/supabase-ai-loading-animation.gif)","level":1,"lines":[66,67],"children":[{"type":"image","src":"/images/blog/launch-week-8/day-2/supabase-ai-loading-animation.gif","title":"","alt":"Supabase AI","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"hr","lines":[68,69],"level":0},{"type":"paragraph_open","tight":false,"lines":[70,71],"level":0},{"type":"inline","content":"Along with these huge AI features, we also added a bunch of new improvements elsewhere around the Studio. Several of these features have come either from requests from the community or are contributions by community members themselves.","level":1,"lines":[70,71],"children":[{"type":"text","content":"Along with these huge AI features, we also added a bunch of new improvements elsewhere around the Studio. Several of these features have come either from requests from the community or are contributions by community members themselves.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[72,73],"level":0},{"type":"inline","content":"\u003cdiv className=\"bg-alternative border rounded-lg px-10 py-2 italic\"\u003e","level":1,"lines":[72,73],"children":[{"type":"text","content":"\u003cdiv className=\"bg-alternative border rounded-lg px-10 py-2 italic\"\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[74,76],"level":0},{"type":"inline","content":"📢 Many of the features and enhancements below came from user requests\n[Please keep them coming](https://github.com/orgs/supabase/discussions/categories/feature-requests)!","level":1,"lines":[74,76],"children":[{"type":"text","content":"📢 Many of the features and enhancements below came from user requests","level":0},{"type":"softbreak","level":0},{"type":"link_open","href":"https://github.com/orgs/supabase/discussions/categories/feature-requests","title":"","level":0},{"type":"text","content":"Please keep them coming","level":1},{"type":"link_close","level":0},{"type":"text","content":"!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[77,78],"level":0},{"type":"inline","content":"\u003c/div\u003e","level":1,"lines":[77,78],"children":[{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[79,80],"level":0},{"type":"inline","content":"[Schema Visualizer](#schema-visualizer)","level":1,"lines":[79,80],"children":[{"type":"text","content":"Schema Visualizer","level":0}],"lvl":2,"i":1,"seen":0,"slug":"schema-visualizer"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[81,82],"level":0},{"type":"inline","content":"\u003cImg src=\"/images/blog/launch-week-8/day-3/visualizer.png\" alt=\"Schema Visualizer\" /\u003e","level":1,"lines":[81,82],"children":[{"type":"text","content":"\u003cImg src=\"/images/blog/launch-week-8/day-3/visualizer.png\" alt=\"Schema Visualizer\" /\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[83,84],"level":0},{"type":"inline","content":"For a while now, many Supabase users have been using [Zernonia's](https://github.com/zernonia) [Supabase Schema visualization tool](https://supabase-schema.vercel.app/). While this was an amazing tool, many users wanted to see something like this directly integrated into the Studio.","level":1,"lines":[83,84],"children":[{"type":"text","content":"For a while now, many Supabase users have been using ","level":0},{"type":"link_open","href":"https://github.com/zernonia","title":"","level":0},{"type":"text","content":"Zernonia's","level":1},{"type":"link_close","level":0},{"type":"text","content":" ","level":0},{"type":"link_open","href":"https://supabase-schema.vercel.app/","title":"","level":0},{"type":"text","content":"Supabase Schema visualization tool","level":1},{"type":"link_close","level":0},{"type":"text","content":". While this was an amazing tool, many users wanted to see something like this directly integrated into the Studio.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[85,86],"level":0},{"type":"inline","content":"We opened an [Issue for it on Github](https://github.com/supabase/supabase/issues/15585) and within a day or two the wheels were in motion. After a couple of weeks, the feature was polished up and merged. It's inspiring to see the power of open source at work. This feature wasn't trivial, and to see community members take it from feature request to production in just a couple of weeks is mind-blowing. Unquestionably, we have one of the best open source communities out there. Huge thanks to [kamilogorek](https://github.com/kamilogorek) and [adiologydev](https://github.com/adiologydev) for their work on this!","level":1,"lines":[85,86],"children":[{"type":"text","content":"We opened an ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase/issues/15585","title":"","level":0},{"type":"text","content":"Issue for it on Github","level":1},{"type":"link_close","level":0},{"type":"text","content":" and within a day or two the wheels were in motion. After a couple of weeks, the feature was polished up and merged. It's inspiring to see the power of open source at work. This feature wasn't trivial, and to see community members take it from feature request to production in just a couple of weeks is mind-blowing. Unquestionably, we have one of the best open source communities out there. Huge thanks to ","level":0},{"type":"link_open","href":"https://github.com/kamilogorek","title":"","level":0},{"type":"text","content":"kamilogorek","level":1},{"type":"link_close","level":0},{"type":"text","content":" and ","level":0},{"type":"link_open","href":"https://github.com/adiologydev","title":"","level":0},{"type":"text","content":"adiologydev","level":1},{"type":"link_close","level":0},{"type":"text","content":" for their work on this!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[87,88],"level":0},{"type":"inline","content":"Special thanks as well to [Zernonia](https://twitter.com/zernonia) for providing the inspiration for this great new feature! OSS FTW.","level":1,"lines":[87,88],"children":[{"type":"text","content":"Special thanks as well to ","level":0},{"type":"link_open","href":"https://twitter.com/zernonia","title":"","level":0},{"type":"text","content":"Zernonia","level":1},{"type":"link_close","level":0},{"type":"text","content":" for providing the inspiration for this great new feature! OSS FTW.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[89,90],"level":0},{"type":"inline","content":"[Role Management](#role-management)","level":1,"lines":[89,90],"children":[{"type":"text","content":"Role Management","level":0}],"lvl":2,"i":2,"seen":0,"slug":"role-management"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[91,92],"level":0},{"type":"inline","content":"Postgres has built-in support for managing users and roles, and this release, we're happy to release a UI for it in the Studio. This is another extremely common feature request, fulfilled almost completely by a community member.","level":1,"lines":[91,92],"children":[{"type":"text","content":"Postgres has built-in support for managing users and roles, and this release, we're happy to release a UI for it in the Studio. This is another extremely common feature request, fulfilled almost completely by a community member.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[93,94],"level":0},{"type":"inline","content":"A few months back, we saw this [PR](https://github.com/supabase/supabase/pull/13745) come in out of the blue from [HTMHell](https://github.com/HTMHell). They built the entire thing with zero help or direction from our team. We were blown away. We had some changes to make on the backend to properly accommodate the UI, and now we're almost ready to get this out into the wild!","level":1,"lines":[93,94],"children":[{"type":"text","content":"A few months back, we saw this ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase/pull/13745","title":"","level":0},{"type":"text","content":"PR","level":1},{"type":"link_close","level":0},{"type":"text","content":" come in out of the blue from ","level":0},{"type":"link_open","href":"https://github.com/HTMHell","title":"","level":0},{"type":"text","content":"HTMHell","level":1},{"type":"link_close","level":0},{"type":"text","content":". They built the entire thing with zero help or direction from our team. We were blown away. We had some changes to make on the backend to properly accommodate the UI, and now we're almost ready to get this out into the wild!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[95,96],"level":0},{"type":"inline","content":"\u003cImg src=\"/images/blog/launch-week-8/day-3/role-management.png\" alt=\"Role management\" /\u003e","level":1,"lines":[95,96],"children":[{"type":"text","content":"\u003cImg src=\"/images/blog/launch-week-8/day-3/role-management.png\" alt=\"Role management\" /\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[97,98],"level":0},{"type":"inline","content":"Due to the security focus of this feature, we want to make sure we do a very thorough job of testing, so we're hoping to make this generally available in the next week or so.","level":1,"lines":[97,98],"children":[{"type":"text","content":"Due to the security focus of this feature, we want to make sure we do a very thorough job of testing, so we're hoping to make this generally available in the next week or so.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[99,100],"level":0},{"type":"inline","content":"Massive thanks to [HTMHell](https://github.com/HTMHell) (amazing handle btw) for the work on this!","level":1,"lines":[99,100],"children":[{"type":"text","content":"Massive thanks to ","level":0},{"type":"link_open","href":"https://github.com/HTMHell","title":"","level":0},{"type":"text","content":"HTMHell","level":1},{"type":"link_close","level":0},{"type":"text","content":" (amazing handle btw) for the work on this!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[101,102],"level":0},{"type":"inline","content":"[Shared SQL Snippets](#shared-sql-snippets)","level":1,"lines":[101,102],"children":[{"type":"text","content":"Shared SQL Snippets","level":0}],"lvl":2,"i":3,"seen":0,"slug":"shared-sql-snippets"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[103,104],"level":0},{"type":"inline","content":"Speaking of commonly requested features, this one has to be in the all-time top 5. Your beautiful, hand-crafted SQL snippets used to be yours and yours alone. Now you can share them with team members and let them bask in your technical prowess.","level":1,"lines":[103,104],"children":[{"type":"text","content":"Speaking of commonly requested features, this one has to be in the all-time top 5. Your beautiful, hand-crafted SQL snippets used to be yours and yours alone. Now you can share them with team members and let them bask in your technical prowess.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[105,110],"level":0},{"type":"inline","content":"\u003cImg\n  src=\"/images/blog/launch-week-8/day-3/share-query.png\"\n  alt=\"Share SQL Snippets\"\n  zoomable={false}\n/\u003e","level":1,"lines":[105,110],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"/images/blog/launch-week-8/day-3/share-query.png\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"Share SQL Snippets\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"zoomable={false}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[111,112],"level":0},{"type":"inline","content":"You can create a set of project-wide snippets for doing common tasks, making it faster to collaborate and build. To share a snippet, just take a personal snippet that ~~Supabase AI~~ you wrote and share it with the project. It will show up in a new Project Snippets list that's visible to everyone on the team.","level":1,"lines":[111,112],"children":[{"type":"text","content":"You can create a set of project-wide snippets for doing common tasks, making it faster to collaborate and build. To share a snippet, just take a personal snippet that ","level":0},{"type":"del_open","level":0},{"type":"text","content":"Supabase AI","level":1},{"type":"del_close","level":0},{"type":"text","content":" you wrote and share it with the project. It will show up in a new Project Snippets list that's visible to everyone on the team.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[113,114],"level":0},{"type":"inline","content":"Teamwork makes the dream work!","level":1,"lines":[113,114],"children":[{"type":"text","content":"Teamwork makes the dream work!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[115,116],"level":0},{"type":"inline","content":"[Database Migration UI](#database-migration-ui)","level":1,"lines":[115,116],"children":[{"type":"text","content":"Database Migration UI","level":0}],"lvl":2,"i":4,"seen":0,"slug":"database-migration-ui"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[117,118],"level":0},{"type":"inline","content":"We're releasing a new UI for working with database migrations right from the Studio. Database migrations give you a way to update your database using version-controlled SQL files. They describe changes that you want to make to your database, and also keep track of what changes have been made to your database over time.","level":1,"lines":[117,118],"children":[{"type":"text","content":"We're releasing a new UI for working with database migrations right from the Studio. Database migrations give you a way to update your database using version-controlled SQL files. They describe changes that you want to make to your database, and also keep track of what changes have been made to your database over time.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[119,120],"level":0},{"type":"inline","content":"As migrations get run against your project from the CLI, you can see information in the Studio about when the migration was run, by who and what changes were made. [See the documentation](https://supabase.com/docs/guides/deployment/database-migrations) to get started with migrations.","level":1,"lines":[119,120],"children":[{"type":"text","content":"As migrations get run against your project from the CLI, you can see information in the Studio about when the migration was run, by who and what changes were made. ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/deployment/database-migrations","title":"","level":0},{"type":"text","content":"See the documentation","level":1},{"type":"link_close","level":0},{"type":"text","content":" to get started with migrations.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[121,122],"level":0},{"type":"inline","content":"\u003cImg src=\"/images/blog/launch-week-8/day-3/migrations.png\" alt=\"Migrations UI\" /\u003e","level":1,"lines":[121,122],"children":[{"type":"text","content":"\u003cImg src=\"/images/blog/launch-week-8/day-3/migrations.png\" alt=\"Migrations UI\" /\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[123,124],"level":0},{"type":"inline","content":"[Wrappers UI](#wrappers-ui)","level":1,"lines":[123,124],"children":[{"type":"text","content":"Wrappers UI","level":0}],"lvl":2,"i":5,"seen":0,"slug":"wrappers-ui"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[125,126],"level":0},{"type":"inline","content":"During Launch Week 6, we [announced](https://supabase.com/blog/postgres-foreign-data-wrappers-rust) Supabase Wrappers — a framework for building foreign data wrappers with Postgres. Wrappers allow your Supabase project to act as a one-stop hub for your data.","level":1,"lines":[125,126],"children":[{"type":"text","content":"During Launch Week 6, we ","level":0},{"type":"link_open","href":"https://supabase.com/blog/postgres-foreign-data-wrappers-rust","title":"","level":0},{"type":"text","content":"announced","level":1},{"type":"link_close","level":0},{"type":"text","content":" Supabase Wrappers — a framework for building foreign data wrappers with Postgres. Wrappers allow your Supabase project to act as a one-stop hub for your data.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[127,128],"level":0},{"type":"inline","content":"When we released Wrappers, we had support for just two providers — Stripe and Firebase. We're now up to 6! This round, we're happy to release support for S3, ClickHouse, BigQuery, and Logflare! Wrappers add a mind-bending level of extensibility to Supabase projects. You can pull data straight into your projects as though they were normal Supabase tables — you can even query them with our client libraries. It's a whole new world of possibilities.","level":1,"lines":[127,128],"children":[{"type":"text","content":"When we released Wrappers, we had support for just two providers — Stripe and Firebase. We're now up to 6! This round, we're happy to release support for S3, ClickHouse, BigQuery, and Logflare! Wrappers add a mind-bending level of extensibility to Supabase projects. You can pull data straight into your projects as though they were normal Supabase tables — you can even query them with our client libraries. It's a whole new world of possibilities.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[129,130],"level":0},{"type":"inline","content":"\u003cImg src=\"/images/blog/launch-week-8/day-3/wrappers.png\" alt=\"Wrappers\" /\u003e","level":1,"lines":[129,130],"children":[{"type":"text","content":"\u003cImg src=\"/images/blog/launch-week-8/day-3/wrappers.png\" alt=\"Wrappers\" /\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[131,132],"level":0},{"type":"inline","content":"[Wrapping Up](#wrapping-up)","level":1,"lines":[131,132],"children":[{"type":"text","content":"Wrapping Up","level":0}],"lvl":2,"i":6,"seen":0,"slug":"wrapping-up"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[133,134],"level":0},{"type":"inline","content":"We hope you get a lot of value out of these new features and enhancements. As we mentioned earlier, many of the features listed here came directly from [Feature Requests](https://github.com/orgs/supabase/discussions/categories/feature-requests) on GitHub. Thanks to everyone who has taken the time to submit these, and encourage submissions for anything else you'd like to see.","level":1,"lines":[133,134],"children":[{"type":"text","content":"We hope you get a lot of value out of these new features and enhancements. As we mentioned earlier, many of the features listed here came directly from ","level":0},{"type":"link_open","href":"https://github.com/orgs/supabase/discussions/categories/feature-requests","title":"","level":0},{"type":"text","content":"Feature Requests","level":1},{"type":"link_close","level":0},{"type":"text","content":" on GitHub. Thanks to everyone who has taken the time to submit these, and encourage submissions for anything else you'd like to see.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[135,136],"level":0},{"type":"inline","content":"[More Launch Week 8](#more-launch-week-8)","level":1,"lines":[135,136],"children":[{"type":"text","content":"More Launch Week 8","level":0}],"lvl":2,"i":7,"seen":0,"slug":"more-launch-week-8"},{"type":"heading_close","hLevel":2,"level":0},{"type":"bullet_list_open","lines":[137,143],"level":0},{"type":"list_item_open","lines":[137,138],"level":1},{"type":"paragraph_open","tight":true,"lines":[137,138],"level":2},{"type":"inline","content":"[Supabase Local Dev: migrations, branching, and observability](https://supabase.com/blog/supabase-local-dev)","level":3,"lines":[137,138],"children":[{"type":"link_open","href":"https://supabase.com/blog/supabase-local-dev","title":"","level":0},{"type":"text","content":"Supabase Local Dev: migrations, branching, and observability","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[138,139],"level":1},{"type":"paragraph_open","tight":true,"lines":[138,139],"level":2},{"type":"inline","content":"[Hugging Face is now supported in Supabase](https://supabase.com/blog/hugging-face-supabase)","level":3,"lines":[138,139],"children":[{"type":"link_open","href":"https://supabase.com/blog/hugging-face-supabase","title":"","level":0},{"type":"text","content":"Hugging Face is now supported in Supabase","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[139,140],"level":1},{"type":"paragraph_open","tight":true,"lines":[139,140],"level":2},{"type":"inline","content":"[Launch Week 8](https://supabase.com/launch-week)","level":3,"lines":[139,140],"children":[{"type":"link_open","href":"https://supabase.com/launch-week","title":"","level":0},{"type":"text","content":"Launch Week 8","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[140,141],"level":1},{"type":"paragraph_open","tight":true,"lines":[140,141],"level":2},{"type":"inline","content":"[Coding the stars - an interactive constellation with Three.js and React Three Fiber](https://supabase.com/blog/interactive-constellation-threejs-react-three-fiber)","level":3,"lines":[140,141],"children":[{"type":"link_open","href":"https://supabase.com/blog/interactive-constellation-threejs-react-three-fiber","title":"","level":0},{"type":"text","content":"Coding the stars - an interactive constellation with Three.js and React Three Fiber","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[141,142],"level":1},{"type":"paragraph_open","tight":true,"lines":[141,142],"level":2},{"type":"inline","content":"[Why we'll stay remote](https://supabase.com/blog/why-supabase-remote)","level":3,"lines":[141,142],"children":[{"type":"link_open","href":"https://supabase.com/blog/why-supabase-remote","title":"","level":0},{"type":"text","content":"Why we'll stay remote","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[142,143],"level":1},{"type":"paragraph_open","tight":true,"lines":[142,143],"level":2},{"type":"inline","content":"[Postgres Language Server](https://github.com/supabase/postgres_lsp)","level":3,"lines":[142,143],"children":[{"type":"link_open","href":"https://github.com/supabase/postgres_lsp","title":"","level":0},{"type":"text","content":"Postgres Language Server","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0}],"content":"- [Supabase AI, right in the SQL Editor](#supabase-ai-right-in-the-sql-editor)\n- [Schema Visualizer](#schema-visualizer)\n- [Role Management](#role-management)\n- [Shared SQL Snippets](#shared-sql-snippets)\n- [Database Migration UI](#database-migration-ui)\n- [Wrappers UI](#wrappers-ui)\n- [Wrapping Up](#wrapping-up)\n- [More Launch Week 8](#more-launch-week-8)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-studio-3-0"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>