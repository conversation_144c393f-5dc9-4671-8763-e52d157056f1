<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase Beta January 2022</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="New auth providers, SMS providers, and new videos." data-next-head=""/><meta property="og:title" content="Supabase Beta January 2022" data-next-head=""/><meta property="og:description" content="New auth providers, SMS providers, and new videos." data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-beta-january-2022" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2022-02-22" data-next-head=""/><meta property="article:author" content="https://github.com/kiwicopple" data-next-head=""/><meta property="article:tag" content="release-notes" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/2022-january/thumb.jpg" data-next-head=""/><meta property="og:image:alt" content="Supabase Beta January 2022 thumbnail" data-next-head=""/><meta property="og:video" content="https://www.youtube.com/v/codAs9-NeHM" data-next-head=""/><meta property="og:video:type" content="application/x-shockwave-flash" data-next-head=""/><meta property="og:video:width" content="640" data-next-head=""/><meta property="og:video:height" content="385" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Supabase Beta January 2022</h1><div class="text-light flex space-x-3 text-sm"><p>22 Feb 2022</p><p>•</p><p>6 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/kiwicopple"><div class="flex items-center gap-3"><div class="w-10"><img alt="Paul Copplestone avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fkiwicopple.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fkiwicopple.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fkiwicopple.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Paul Copplestone</span><span class="text-foreground-lighter mb-0 text-xs">CEO and Co-Founder</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Supabase Beta January 2022" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-january%2Fthumb.jpg&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-january%2Fthumb.jpg&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-january%2Fthumb.jpg&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-january%2Fthumb.jpg&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-january%2Fthumb.jpg&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-january%2Fthumb.jpg&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-january%2Fthumb.jpg&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-january%2Fthumb.jpg&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-january%2Fthumb.jpg&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-january%2Fthumb.jpg&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-january%2Fthumb.jpg&amp;w=3840&amp;q=100"/></div><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p>This month&#x27;s beta update is more stacked than the Superbowl (*Supa-bowl) halftime show. Here’s all of the highlights from January...</p>
<h2 id="new-oauth-providers" class="group scroll-mt-24">New OAuth providers<a href="#new-oauth-providers" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>We’re continually amazed by how quickly new auth providers are being added into Supabase, and last month, 2 more have been added.</p>
<h3 id="notion" class="group scroll-mt-24">Notion<a href="#notion" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Added by <a href="https://github.com/zernonia">zernonia</a>.
You may already know him as the maintainer of <a href="https://www.madewithsupabase.com/">madewithsupabase.com</a>.</p>
<h3 id="linkedin" class="group scroll-mt-24">LinkedIn<a href="#linkedin" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Added by <a href="https://github.com/riderx">riderx</a>.
You may already be familiar with Martin from his podcast <a href="https://podcasts.apple.com/us/podcast/indie-makers/id1488437972">Indie Makers</a>.</p>
<h2 id="new-sms-providers" class="group scroll-mt-24">New SMS providers<a href="#new-sms-providers" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>Along with the new 0Auth providers above, last month saw the addition of two more SMS phone providers to allow you to authenticate users via an SMS OTP (One-Time Password) token.</p>
<h3 id="vonage" class="group scroll-mt-24">Vonage<a href="#vonage" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p><strong><a href="https://www.vonage.com/">Vonage</a></strong> is a US-based cloud communications provider.
Added by <a href="https://github.com/devkiran">devkiran</a> (from <a href="https://twitter.com/BoxyHQ">BoxyHQ</a>).</p>
<h3 id="textlocal" class="group scroll-mt-24">Textlocal<a href="#textlocal" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We are now fulfilling a popular request with this Indian-compliant SMS provider, <a href="https://www.textlocal.com/">Textlocal</a>.
Also added by <a href="https://github.com/devkiran">devkiran</a>.</p>
<h3 id="other-sms-providers" class="group scroll-mt-24">Other SMS providers<a href="#other-sms-providers" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Just a reminder, we also support <a href="https://supabase.com/docs/guides/auth/phone-login/twilio">Twilio</a> and <a href="https://supabase.com/docs/guides/auth/phone-login/messagebird">MessageBird</a>.</p>
<p>On a final note, we are hiring for an <a href="https://about.supabase.com/careers/auth-engineers">Auth Engineer</a>.</p>
<h2 id="query-logs-with-sql" class="group scroll-mt-24">Query logs with SQL<a href="#query-logs-with-sql" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Supabase logs are more powerful with the <a href="https://github.com/supabase/supabase/pull/4734">newly added SQL querying</a>.</p>
<p>We added <a href="https://github.com/supabase/supabase/pull/4904">timestamp filtering</a>, and you’ll notice our usage charts have <a href="https://github.com/supabase/supabase/pull/4732">more time spans available</a>.</p>
<p>We’re enabling developers to quickly diagnose issues with their projects with powerful logging and observability tools and we have a lot more to come.</p>
<h2 id="graphql-v010" class="group scroll-mt-24">GraphQL v0.1.0<a href="#graphql-v010" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>Last month we released <a href="https://github.com/supabase/pg_graphql"><code class="short-inline-codeblock">pg_graphql</code></a> v0.1.0, which includes <a href="https://supabase.github.io/pg_graphql/configuration/#comment-directives">Comment Directives</a>.</p>
<p>We haven’t released GraphQL onto the platform yet because we it&#x27;s still under heavy development. You can expect availability in the next few months.</p>
<p>Example</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>create table account(</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    id serial primary key</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>);</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>comment on table public.account is</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>e&#x27;@graphql({ &quot;name&quot;: &quot;AccountHolder&quot; })&#x27;;</span></div></div><br/></code></div></div>
<p>Result</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>// Renames &quot;Account&quot; to &quot;AccountHolder&quot;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>type AccountHolder {</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  id: Int!</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<h2 id="new-examples" class="group scroll-mt-24">New examples<a href="#new-examples" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<h3 id="remix-auth" class="group scroll-mt-24">Remix Auth<a href="#remix-auth" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>It’s all anyone seems to be <a href="https://twitter.com/jkup/status/1456360115205033989">talking about</a>. We genuinely love what <a href="https://remix.run/">Remix</a> are doing, so it’s only right that we show off how Remix and Supabase work well together.</p>
<p>Check out the new <a href="https://github.com/supabase/examples/tree/main/supabase-js-v1/auth/remix-auth">Remix Auth example</a>, and let us know what you think.</p>
<h3 id="expo-todo-list" class="group scroll-mt-24">Expo Todo List<a href="#expo-todo-list" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Our React Native example has been correctly updated to be an Expo example.
<a href="https://github.com/supabase/examples/tree/main/supabase-js-v1/todo-list/expo-todo-list">Check it out here</a>.</p>
<h2 id="video-api-requests-with-database-webhooks" class="group scroll-mt-24">Video: API requests with Database Webhooks<a href="#video-api-requests-with-database-webhooks" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>There&#x27;s no stopping <a href="https://jonmeyers.io/videos">Jon Meyers</a>! He’s back with an in-depth video on how to easily <a href="https://www.youtube.com/watch?v=codAs9-NeHM&amp;feature=emb_title">automate API requests</a> using our very own Database Webhooks.</p>
<p>You&#x27;ll learn how to listen to <em>any</em> database change, then send those changes <a href="https://supabase.com/blog/supabase-functions-updates#hook-payload">in a payload</a> via HTTP request.</p>
<div class="video-container"><iframe class="video-with-border w-full" src="https://www.youtube-nocookie.com/embed/codAs9-NeHM" frameBorder="1" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen=""></iframe></div>
<h2 id="set-your-own-support-ticket-priority" class="group scroll-mt-24">Set your own support ticket priority<a href="#set-your-own-support-ticket-priority" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>As with any platform, there can be the occasional glitch.</p>
<p>The Supabase Dashboard includes a dedicated support form that goes straight to our support inbox. This support form includes your project information and,
since we all want to see your issues solved, we thought it would make sense that <em>you</em> could set the priority of your support tickets.</p>
<p>This change has drastically improved response times for urgent support tickets.
The form includes extra “urgent” levels for the PRO and Pay As You Go projects.</p>
<p>And, as part of our continued commitment to Support, we are hiring <a href="https://about.supabase.com/careers/support-and-qa">Support Engineers</a>.</p>
<h2 id="community" class="group scroll-mt-24">Community<a href="#community" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>As always, the community has been amazing during the month of February.</p>
<h3 id="supabase--snaplet" class="group scroll-mt-24">Supabase + Snaplet<a href="#supabase--snaplet" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Are you maintaining multiple environments? Snaplet helps you copy a production database and clone it into different environments.</p>
<p>Check out the <a href="https://docs.snaplet.dev/tutorials/supabase-clone-environments">Supabase clone environments</a> tutorial on the Snaplet docs.</p>
<div class="video-container"><iframe class="video-with-border w-full" src="https://www.youtube-nocookie.com/embed/oPtMMhdhEP4" frameBorder="1" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen=""></iframe></div>
<h3 id="supabase--retool" class="group scroll-mt-24">Supabase + Retool<a href="#supabase--retool" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Retool has put together a brilliant 10 minute admin panel setup using Supabase with Retool.</p>
<div class="video-container"><iframe class="video-with-border w-full" src="https://www.youtube-nocookie.com/embed/AgB2-CSrnoI" frameBorder="1" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen=""></iframe></div>
<h3 id="learn-with-jason" class="group scroll-mt-24">Learn with Jason<a href="#learn-with-jason" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Jason Lengstorf caught up with our very own <a href="https://jonmeyers.io/">Jon Meyers</a> on his awesome show, <a href="https://www.youtube.com/watch?v=8vqY1KT4TLU">Learn with Jason</a>, to talk about building an app <a href="https://supabase.com/docs/guides/with-nextjs">with Supabase and Next.js</a>.</p>
<div class="video-container"><iframe class="video-with-border w-full" src="https://www.youtube-nocookie.com/embed/8vqY1KT4TLU" frameBorder="1" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen=""></iframe></div>
<h3 id="new-article-highlights" class="group scroll-mt-24">New article highlights<a href="#new-article-highlights" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<ul>
<li>Arctype published a new guide showing how to connect to your Supabase database. <a href="https://arctype.com/postgres/connect/supabase-postgres">Link</a></li>
<li>Marmalabs built a Supabase adapter for react-admin, the frontend framework for building admin applications on top of REST/GraphQL services. <a href="https://github.com/marmelab/ra-supabase">Link</a></li>
<li>HotGlue created a guide showing how easy it is to integrate Salesforce and Supabase. <a href="https://www.notion.so/Supabase-and-hotglue-article-9e7f5583d27c419490ee7d536d6d269d">Link</a></li>
</ul>
<h3 id="postgrest" class="group scroll-mt-24">PostgREST<a href="#postgrest" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>One of Supabase’s key tools that allow a lot of our functionality has had some updates.</p>
<ul>
<li><code class="short-inline-codeblock">pg_listen</code> was removed in favor of PostgREST’s built-in schema reloading.</li>
<li>PostgREST database connection pool size gets scaled with compute size for better performance for certain workload shapes.</li>
</ul>
<h2 id="coming-next-launch-week-4" class="group scroll-mt-24">Coming Next: Launch Week 4<a href="#coming-next-launch-week-4" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Preparation for Launch Week 4 is underway!</p>
<p>Of course, we can’t tell you what will happen (perhaps because we don’t know ourselves yet), but you can always speculate in the community <a href="https://discord.supabase.com/">Discord server</a>, or even <a href="https://twitter.com/supabase">tweet us your predictions</a>.</p>
<h2 id="get-started" class="group scroll-mt-24">Get started<a href="#get-started" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ul>
<li>Start using Supabase today: <strong><a href="https://supabase.com/dashboard/">supabase.com/dashboard</a></strong></li>
<li>Make sure to <strong><a href="https://github.com/supabase/supabase">star us on GitHub</a></strong></li>
<li>Follow us <strong><a href="https://twitter.com/supabase">on Twitter</a></strong></li>
<li>Subscribe to our <strong><a href="https://www.youtube.com/c/supabase">YouTube channel</a></strong></li>
<li>Become a <strong><a href="https://github.com/sponsors/supabase">sponsor</a></strong></li>
</ul></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-january-2022&amp;text=Supabase%20Beta%20January%202022"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-january-2022&amp;text=Supabase%20Beta%20January%202022"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-january-2022&amp;t=Supabase%20Beta%20January%202022"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="postgres-audit.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Postgres Auditing in 150 lines of SQL</h4><p class="small">8 March 2022</p></div></div></div></div></a></div><div><a href="https://supabase.com/blog/product-hunt-golden-kitty-awards-2021"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Golden Kitty Awards Ceremony Watch Party with Supabase</h4><p class="small">20 January 2022</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/release-notes"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">release-notes</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#new-oauth-providers">New OAuth providers</a>
<ul>
<li><a href="#notion">Notion</a></li>
<li><a href="#linkedin">LinkedIn</a></li>
</ul>
</li>
<li><a href="#new-sms-providers">New SMS providers</a>
<ul>
<li><a href="#vonage">Vonage</a></li>
<li><a href="#textlocal">Textlocal</a></li>
<li><a href="#other-sms-providers">Other SMS providers</a></li>
</ul>
</li>
<li><a href="#query-logs-with-sql">Query logs with SQL</a></li>
<li><a href="#graphql-v010">GraphQL v0.1.0</a></li>
<li><a href="#new-examples">New examples</a>
<ul>
<li><a href="#remix-auth">Remix Auth</a></li>
<li><a href="#expo-todo-list">Expo Todo List</a></li>
</ul>
</li>
<li><a href="#video-api-requests-with-database-webhooks">Video: API requests with Database Webhooks</a></li>
<li><a href="#set-your-own-support-ticket-priority">Set your own support ticket priority</a></li>
<li><a href="#community">Community</a>
<ul>
<li><a href="#supabase--snaplet">Supabase + Snaplet</a></li>
<li><a href="#supabase--retool">Supabase + Retool</a></li>
<li><a href="#learn-with-jason">Learn with Jason</a></li>
<li><a href="#new-article-highlights">New article highlights</a></li>
<li><a href="#postgrest">PostgREST</a></li>
</ul>
</li>
<li><a href="#coming-next-launch-week-4">Coming Next: Launch Week 4</a></li>
<li><a href="#get-started">Get started</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-january-2022&amp;text=Supabase%20Beta%20January%202022"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-january-2022&amp;text=Supabase%20Beta%20January%202022"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-january-2022&amp;t=Supabase%20Beta%20January%202022"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"postgres-audit","title":"Postgres Auditing in 150 lines of SQL","description":"PostgreSQL has a robust set of features which we can leverage to create a generic auditing solution in 150 lines of SQL.","author":"oli_rice","author_url":"https://github.com/olirice","author_image_url":"https://github.com/olirice.png","image":"supa-audit/postgres_auditing_in_150_lines_of_SQL.png","thumb":"supa-audit/postgres_auditing_in_150_lines_of_SQL.png","categories":["postgres"],"tags":["postgres","planetpg"],"date":"2022-03-08","toc_depth":3,"formattedDate":"8 March 2022","readingTime":"13 minute read","url":"/blog/postgres-audit","path":"/blog/postgres-audit"},"nextPost":{"slug":"product-hunt-golden-kitty-awards-2021","title":"Golden Kitty Awards Ceremony Watch Party with Supabase","description":"Hang out with us while watching the Product Hunt Golden Kitty Awards Ceremony","author":"thor_schaeff","author_url":"https://github.com/thorwebdev","author_image_url":"https://github.com/thorwebdev.png","image":"product-hunt-golden-kitty-awards-2021.png","thumb":"product-hunt-golden-kitty-awards-2021-thumb.png","categories":["developers"],"tags":["community"],"date":"2022-01-20","toc_depth":2,"formattedDate":"20 January 2022","readingTime":"2 minute read","url":"/blog/product-hunt-golden-kitty-awards-2021","path":"/blog/product-hunt-golden-kitty-awards-2021"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-beta-january-2022","source":"\nThis month's beta update is more stacked than the Superbowl (\\*Supa-bowl) halftime show. Here’s all of the highlights from January...\n\n## New OAuth providers\n\n![New 0Auth Providers](/images/blog/2022-january/new-auth-providers-supabase_monthly-email-jan-2022.png)\n\nWe’re continually amazed by how quickly new auth providers are being added into Supabase, and last month, 2 more have been added.\n\n### Notion\n\nAdded by [zernonia](https://github.com/zernonia).\nYou may already know him as the maintainer of [madewithsupabase.com](https://www.madewithsupabase.com/).\n\n### LinkedIn\n\nAdded by [riderx](https://github.com/riderx).\nYou may already be familiar with Martin from his podcast [Indie Makers](https://podcasts.apple.com/us/podcast/indie-makers/id1488437972).\n\n## New SMS providers\n\n![New SMS Providers](/images/blog/2022-january/new-sms-providers-supabase_monthly-email-jan-2022.png)\n\nAlong with the new 0Auth providers above, last month saw the addition of two more SMS phone providers to allow you to authenticate users via an SMS OTP (One-Time Password) token.\n\n### Vonage\n\n**[Vonage](https://www.vonage.com/)** is a US-based cloud communications provider.\nAdded by [devkiran](https://github.com/devkiran) (from [BoxyHQ](https://twitter.com/BoxyHQ)).\n\n### Textlocal\n\nWe are now fulfilling a popular request with this Indian-compliant SMS provider, [Textlocal](https://www.textlocal.com/).\nAlso added by [devkiran](https://github.com/devkiran).\n\n### Other SMS providers\n\nJust a reminder, we also support [Twilio](https://supabase.com/docs/guides/auth/phone-login/twilio) and [MessageBird](https://supabase.com/docs/guides/auth/phone-login/messagebird).\n\nOn a final note, we are hiring for an [Auth Engineer](https://about.supabase.com/careers/auth-engineers).\n\n## Query logs with SQL\n\nSupabase logs are more powerful with the [newly added SQL querying](https://github.com/supabase/supabase/pull/4734).\n\nWe added [timestamp filtering](https://github.com/supabase/supabase/pull/4904), and you’ll notice our usage charts have [more time spans available](https://github.com/supabase/supabase/pull/4732).\n\nWe’re enabling developers to quickly diagnose issues with their projects with powerful logging and observability tools and we have a lot more to come.\n\n## GraphQL v0.1.0\n\n![Graph QL v0.1.0](/images/blog/2022-january/pg_graphql_0.1.0_monthly-email-dec-2021.png)\n\nLast month we released [`pg_graphql`](https://github.com/supabase/pg_graphql) v0.1.0, which includes [Comment Directives](https://supabase.github.io/pg_graphql/configuration/#comment-directives).\n\nWe haven’t released GraphQL onto the platform yet because we it's still under heavy development. You can expect availability in the next few months.\n\nExample\n\n```sql\ncreate table account(\n    id serial primary key\n);\n\ncomment on table public.account is\ne'@graphql({ \"name\": \"AccountHolder\" })';\n```\n\nResult\n\n```jsx\n// Renames \"Account\" to \"AccountHolder\"\ntype AccountHolder {\n  id: Int!\n}\n```\n\n## New examples\n\n### Remix Auth\n\nIt’s all anyone seems to be [talking about](https://twitter.com/jkup/status/1456360115205033989). We genuinely love what [Remix](https://remix.run/) are doing, so it’s only right that we show off how Remix and Supabase work well together.\n\nCheck out the new [Remix Auth example](https://github.com/supabase/examples/tree/main/supabase-js-v1/auth/remix-auth), and let us know what you think.\n\n### Expo Todo List\n\nOur React Native example has been correctly updated to be an Expo example.\n[Check it out here](https://github.com/supabase/examples/tree/main/supabase-js-v1/todo-list/expo-todo-list).\n\n## Video: API requests with Database Webhooks\n\nThere's no stopping [Jon Meyers](https://jonmeyers.io/videos)! He’s back with an in-depth video on how to easily [automate API requests](https://www.youtube.com/watch?v=codAs9-NeHM\u0026feature=emb_title) using our very own Database Webhooks.\n\nYou'll learn how to listen to _any_ database change, then send those changes [in a payload](https://supabase.com/blog/supabase-functions-updates#hook-payload) via HTTP request.\n\n\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"video-with-border w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/codAs9-NeHM\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen\n  \u003e\u003c/iframe\u003e\n\u003c/div\u003e\n\n## Set your own support ticket priority\n\nAs with any platform, there can be the occasional glitch.\n\nThe Supabase Dashboard includes a dedicated support form that goes straight to our support inbox. This support form includes your project information and,\nsince we all want to see your issues solved, we thought it would make sense that _you_ could set the priority of your support tickets.\n\nThis change has drastically improved response times for urgent support tickets.\nThe form includes extra “urgent” levels for the PRO and Pay As You Go projects.\n\nAnd, as part of our continued commitment to Support, we are hiring [Support Engineers](https://about.supabase.com/careers/support-and-qa).\n\n## Community\n\nAs always, the community has been amazing during the month of February.\n\n### Supabase + Snaplet\n\nAre you maintaining multiple environments? Snaplet helps you copy a production database and clone it into different environments.\n\nCheck out the [Supabase clone environments](https://docs.snaplet.dev/tutorials/supabase-clone-environments) tutorial on the Snaplet docs.\n\n\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"video-with-border w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/oPtMMhdhEP4\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen\n  \u003e\u003c/iframe\u003e\n\u003c/div\u003e\n\n### Supabase + Retool\n\nRetool has put together a brilliant 10 minute admin panel setup using Supabase with Retool.\n\n\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"video-with-border w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/AgB2-CSrnoI\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen\n  \u003e\u003c/iframe\u003e\n\u003c/div\u003e\n\n### Learn with Jason\n\nJason Lengstorf caught up with our very own [Jon Meyers](https://jonmeyers.io/) on his awesome show, [Learn with Jason](https://www.youtube.com/watch?v=8vqY1KT4TLU), to talk about building an app [with Supabase and Next.js](https://supabase.com/docs/guides/with-nextjs).\n\n\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"video-with-border w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/8vqY1KT4TLU\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen\n  \u003e\u003c/iframe\u003e\n\u003c/div\u003e\n\n### New article highlights\n\n- Arctype published a new guide showing how to connect to your Supabase database. [Link](https://arctype.com/postgres/connect/supabase-postgres)\n- Marmalabs built a Supabase adapter for react-admin, the frontend framework for building admin applications on top of REST/GraphQL services. [Link](https://github.com/marmelab/ra-supabase)\n- HotGlue created a guide showing how easy it is to integrate Salesforce and Supabase. [Link](https://www.notion.so/Supabase-and-hotglue-article-9e7f5583d27c419490ee7d536d6d269d)\n\n### PostgREST\n\nOne of Supabase’s key tools that allow a lot of our functionality has had some updates.\n\n- `pg_listen` was removed in favor of PostgREST’s built-in schema reloading.\n- PostgREST database connection pool size gets scaled with compute size for better performance for certain workload shapes.\n\n## Coming Next: Launch Week 4\n\nPreparation for Launch Week 4 is underway!\n\nOf course, we can’t tell you what will happen (perhaps because we don’t know ourselves yet), but you can always speculate in the community [Discord server](https://discord.supabase.com/), or even [tweet us your predictions](https://twitter.com/supabase).\n\n## Get started\n\n- Start using Supabase today: **[supabase.com/dashboard](https://supabase.com/dashboard/)**\n- Make sure to **[star us on GitHub](https://github.com/supabase/supabase)**\n- Follow us **[on Twitter](https://twitter.com/supabase)**\n- Subscribe to our **[YouTube channel](https://www.youtube.com/c/supabase)**\n- Become a **[sponsor](https://github.com/sponsors/supabase)**\n","title":"Supabase Beta January 2022","description":"New auth providers, SMS providers, and new videos.","author":"paul_copplestone","author_url":"https://github.com/kiwicopple","author_image_url":"https://github.com/kiwicopple.png","image":"2022-january/thumb.jpg","thumb":"2022-january/thumb.jpg","categories":["product"],"tags":["release-notes"],"date":"2022-02-22","toc_depth":3,"video":"https://www.youtube.com/v/codAs9-NeHM","content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    h2: \"h2\",\n    img: \"img\",\n    h3: \"h3\",\n    a: \"a\",\n    strong: \"strong\",\n    code: \"code\",\n    em: \"em\",\n    ul: \"ul\",\n    li: \"li\"\n  }, _provideComponents(), props.components), {CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This month's beta update is more stacked than the Superbowl (*Supa-bowl) halftime show. Here’s all of the highlights from January...\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"new-oauth-providers\",\n      children: \"New OAuth providers\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2022-january/new-auth-providers-supabase_monthly-email-jan-2022.png\",\n        alt: \"New 0Auth Providers\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We’re continually amazed by how quickly new auth providers are being added into Supabase, and last month, 2 more have been added.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"notion\",\n      children: \"Notion\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Added by \", _jsx(_components.a, {\n        href: \"https://github.com/zernonia\",\n        children: \"zernonia\"\n      }), \".\\nYou may already know him as the maintainer of \", _jsx(_components.a, {\n        href: \"https://www.madewithsupabase.com/\",\n        children: \"madewithsupabase.com\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"linkedin\",\n      children: \"LinkedIn\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Added by \", _jsx(_components.a, {\n        href: \"https://github.com/riderx\",\n        children: \"riderx\"\n      }), \".\\nYou may already be familiar with Martin from his podcast \", _jsx(_components.a, {\n        href: \"https://podcasts.apple.com/us/podcast/indie-makers/id1488437972\",\n        children: \"Indie Makers\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"new-sms-providers\",\n      children: \"New SMS providers\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2022-january/new-sms-providers-supabase_monthly-email-jan-2022.png\",\n        alt: \"New SMS Providers\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Along with the new 0Auth providers above, last month saw the addition of two more SMS phone providers to allow you to authenticate users via an SMS OTP (One-Time Password) token.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"vonage\",\n      children: \"Vonage\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.strong, {\n        children: _jsx(_components.a, {\n          href: \"https://www.vonage.com/\",\n          children: \"Vonage\"\n        })\n      }), \" is a US-based cloud communications provider.\\nAdded by \", _jsx(_components.a, {\n        href: \"https://github.com/devkiran\",\n        children: \"devkiran\"\n      }), \" (from \", _jsx(_components.a, {\n        href: \"https://twitter.com/BoxyHQ\",\n        children: \"BoxyHQ\"\n      }), \").\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"textlocal\",\n      children: \"Textlocal\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We are now fulfilling a popular request with this Indian-compliant SMS provider, \", _jsx(_components.a, {\n        href: \"https://www.textlocal.com/\",\n        children: \"Textlocal\"\n      }), \".\\nAlso added by \", _jsx(_components.a, {\n        href: \"https://github.com/devkiran\",\n        children: \"devkiran\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"other-sms-providers\",\n      children: \"Other SMS providers\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Just a reminder, we also support \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/auth/phone-login/twilio\",\n        children: \"Twilio\"\n      }), \" and \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/auth/phone-login/messagebird\",\n        children: \"MessageBird\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"On a final note, we are hiring for an \", _jsx(_components.a, {\n        href: \"https://about.supabase.com/careers/auth-engineers\",\n        children: \"Auth Engineer\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"query-logs-with-sql\",\n      children: \"Query logs with SQL\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Supabase logs are more powerful with the \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/supabase/pull/4734\",\n        children: \"newly added SQL querying\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We added \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/supabase/pull/4904\",\n        children: \"timestamp filtering\"\n      }), \", and you’ll notice our usage charts have \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/supabase/pull/4732\",\n        children: \"more time spans available\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We’re enabling developers to quickly diagnose issues with their projects with powerful logging and observability tools and we have a lot more to come.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"graphql-v010\",\n      children: \"GraphQL v0.1.0\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2022-january/pg_graphql_0.1.0_monthly-email-dec-2021.png\",\n        alt: \"Graph QL v0.1.0\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Last month we released \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/pg_graphql\",\n        children: _jsx(_components.code, {\n          children: \"pg_graphql\"\n        })\n      }), \" v0.1.0, which includes \", _jsx(_components.a, {\n        href: \"https://supabase.github.io/pg_graphql/configuration/#comment-directives\",\n        children: \"Comment Directives\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We haven’t released GraphQL onto the platform yet because we it's still under heavy development. You can expect availability in the next few months.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Example\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"create table \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"account\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    id \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"serial primary key\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \");\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"comment on table public.account is\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'@graphql({ \\\"name\\\": \\\"AccountHolder\\\" })'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Result\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// Renames \\\"Account\\\" to \\\"AccountHolder\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"type \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"AccountHolder\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  id: Int!\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"jsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"new-examples\",\n      children: \"New examples\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"remix-auth\",\n      children: \"Remix Auth\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"It’s all anyone seems to be \", _jsx(_components.a, {\n        href: \"https://twitter.com/jkup/status/1456360115205033989\",\n        children: \"talking about\"\n      }), \". We genuinely love what \", _jsx(_components.a, {\n        href: \"https://remix.run/\",\n        children: \"Remix\"\n      }), \" are doing, so it’s only right that we show off how Remix and Supabase work well together.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Check out the new \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/examples/tree/main/supabase-js-v1/auth/remix-auth\",\n        children: \"Remix Auth example\"\n      }), \", and let us know what you think.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"expo-todo-list\",\n      children: \"Expo Todo List\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Our React Native example has been correctly updated to be an Expo example.\\n\", _jsx(_components.a, {\n        href: \"https://github.com/supabase/examples/tree/main/supabase-js-v1/todo-list/expo-todo-list\",\n        children: \"Check it out here\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"video-api-requests-with-database-webhooks\",\n      children: \"Video: API requests with Database Webhooks\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"There's no stopping \", _jsx(_components.a, {\n        href: \"https://jonmeyers.io/videos\",\n        children: \"Jon Meyers\"\n      }), \"! He’s back with an in-depth video on how to easily \", _jsx(_components.a, {\n        href: \"https://www.youtube.com/watch?v=codAs9-NeHM\u0026feature=emb_title\",\n        children: \"automate API requests\"\n      }), \" using our very own Database Webhooks.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You'll learn how to listen to \", _jsx(_components.em, {\n        children: \"any\"\n      }), \" database change, then send those changes \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/supabase-functions-updates#hook-payload\",\n        children: \"in a payload\"\n      }), \" via HTTP request.\"]\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"video-container\",\n      children: _jsx(\"iframe\", {\n        className: \"video-with-border w-full\",\n        src: \"https://www.youtube-nocookie.com/embed/codAs9-NeHM\",\n        frameBorder: \"1\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n        allowFullScreen: true\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"set-your-own-support-ticket-priority\",\n      children: \"Set your own support ticket priority\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"As with any platform, there can be the occasional glitch.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The Supabase Dashboard includes a dedicated support form that goes straight to our support inbox. This support form includes your project information and,\\nsince we all want to see your issues solved, we thought it would make sense that \", _jsx(_components.em, {\n        children: \"you\"\n      }), \" could set the priority of your support tickets.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This change has drastically improved response times for urgent support tickets.\\nThe form includes extra “urgent” levels for the PRO and Pay As You Go projects.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"And, as part of our continued commitment to Support, we are hiring \", _jsx(_components.a, {\n        href: \"https://about.supabase.com/careers/support-and-qa\",\n        children: \"Support Engineers\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"community\",\n      children: \"Community\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"As always, the community has been amazing during the month of February.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"supabase--snaplet\",\n      children: \"Supabase + Snaplet\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Are you maintaining multiple environments? Snaplet helps you copy a production database and clone it into different environments.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Check out the \", _jsx(_components.a, {\n        href: \"https://docs.snaplet.dev/tutorials/supabase-clone-environments\",\n        children: \"Supabase clone environments\"\n      }), \" tutorial on the Snaplet docs.\"]\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"video-container\",\n      children: _jsx(\"iframe\", {\n        className: \"video-with-border w-full\",\n        src: \"https://www.youtube-nocookie.com/embed/oPtMMhdhEP4\",\n        frameBorder: \"1\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n        allowFullScreen: true\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"supabase--retool\",\n      children: \"Supabase + Retool\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Retool has put together a brilliant 10 minute admin panel setup using Supabase with Retool.\"\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"video-container\",\n      children: _jsx(\"iframe\", {\n        className: \"video-with-border w-full\",\n        src: \"https://www.youtube-nocookie.com/embed/AgB2-CSrnoI\",\n        frameBorder: \"1\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n        allowFullScreen: true\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"learn-with-jason\",\n      children: \"Learn with Jason\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Jason Lengstorf caught up with our very own \", _jsx(_components.a, {\n        href: \"https://jonmeyers.io/\",\n        children: \"Jon Meyers\"\n      }), \" on his awesome show, \", _jsx(_components.a, {\n        href: \"https://www.youtube.com/watch?v=8vqY1KT4TLU\",\n        children: \"Learn with Jason\"\n      }), \", to talk about building an app \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/with-nextjs\",\n        children: \"with Supabase and Next.js\"\n      }), \".\"]\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"video-container\",\n      children: _jsx(\"iframe\", {\n        className: \"video-with-border w-full\",\n        src: \"https://www.youtube-nocookie.com/embed/8vqY1KT4TLU\",\n        frameBorder: \"1\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n        allowFullScreen: true\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"new-article-highlights\",\n      children: \"New article highlights\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Arctype published a new guide showing how to connect to your Supabase database. \", _jsx(_components.a, {\n          href: \"https://arctype.com/postgres/connect/supabase-postgres\",\n          children: \"Link\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Marmalabs built a Supabase adapter for react-admin, the frontend framework for building admin applications on top of REST/GraphQL services. \", _jsx(_components.a, {\n          href: \"https://github.com/marmelab/ra-supabase\",\n          children: \"Link\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"HotGlue created a guide showing how easy it is to integrate Salesforce and Supabase. \", _jsx(_components.a, {\n          href: \"https://www.notion.so/Supabase-and-hotglue-article-9e7f5583d27c419490ee7d536d6d269d\",\n          children: \"Link\"\n        })]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"postgrest\",\n      children: \"PostgREST\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"One of Supabase’s key tools that allow a lot of our functionality has had some updates.\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.code, {\n          children: \"pg_listen\"\n        }), \" was removed in favor of PostgREST’s built-in schema reloading.\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"PostgREST database connection pool size gets scaled with compute size for better performance for certain workload shapes.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"coming-next-launch-week-4\",\n      children: \"Coming Next: Launch Week 4\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Preparation for Launch Week 4 is underway!\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Of course, we can’t tell you what will happen (perhaps because we don’t know ourselves yet), but you can always speculate in the community \", _jsx(_components.a, {\n        href: \"https://discord.supabase.com/\",\n        children: \"Discord server\"\n      }), \", or even \", _jsx(_components.a, {\n        href: \"https://twitter.com/supabase\",\n        children: \"tweet us your predictions\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"get-started\",\n      children: \"Get started\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Start using Supabase today: \", _jsx(_components.strong, {\n          children: _jsx(_components.a, {\n            href: \"https://supabase.com/dashboard/\",\n            children: \"supabase.com/dashboard\"\n          })\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Make sure to \", _jsx(_components.strong, {\n          children: _jsx(_components.a, {\n            href: \"https://github.com/supabase/supabase\",\n            children: \"star us on GitHub\"\n          })\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Follow us \", _jsx(_components.strong, {\n          children: _jsx(_components.a, {\n            href: \"https://twitter.com/supabase\",\n            children: \"on Twitter\"\n          })\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Subscribe to our \", _jsx(_components.strong, {\n          children: _jsx(_components.a, {\n            href: \"https://www.youtube.com/c/supabase\",\n            children: \"YouTube channel\"\n          })\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Become a \", _jsx(_components.strong, {\n          children: _jsx(_components.a, {\n            href: \"https://github.com/sponsors/supabase\",\n            children: \"sponsor\"\n          })\n        })]\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"New OAuth providers","slug":"new-oauth-providers","lvl":2,"i":0,"seen":0},{"content":"Notion","slug":"notion","lvl":3,"i":1,"seen":0},{"content":"LinkedIn","slug":"linkedin","lvl":3,"i":2,"seen":0},{"content":"New SMS providers","slug":"new-sms-providers","lvl":2,"i":3,"seen":0},{"content":"Vonage","slug":"vonage","lvl":3,"i":4,"seen":0},{"content":"Textlocal","slug":"textlocal","lvl":3,"i":5,"seen":0},{"content":"Other SMS providers","slug":"other-sms-providers","lvl":3,"i":6,"seen":0},{"content":"Query logs with SQL","slug":"query-logs-with-sql","lvl":2,"i":7,"seen":0},{"content":"GraphQL v0.1.0","slug":"graphql-v010","lvl":2,"i":8,"seen":0},{"content":"New examples","slug":"new-examples","lvl":2,"i":9,"seen":0},{"content":"Remix Auth","slug":"remix-auth","lvl":3,"i":10,"seen":0},{"content":"Expo Todo List","slug":"expo-todo-list","lvl":3,"i":11,"seen":0},{"content":"Video: API requests with Database Webhooks","slug":"video-api-requests-with-database-webhooks","lvl":2,"i":12,"seen":0},{"content":"Set your own support ticket priority","slug":"set-your-own-support-ticket-priority","lvl":2,"i":13,"seen":0},{"content":"Community","slug":"community","lvl":2,"i":14,"seen":0},{"content":"Supabase + Snaplet","slug":"supabase--snaplet","lvl":3,"i":15,"seen":0},{"content":"Supabase + Retool","slug":"supabase--retool","lvl":3,"i":16,"seen":0},{"content":"Learn with Jason","slug":"learn-with-jason","lvl":3,"i":17,"seen":0},{"content":"New article highlights","slug":"new-article-highlights","lvl":3,"i":18,"seen":0},{"content":"PostgREST","slug":"postgrest","lvl":3,"i":19,"seen":0},{"content":"Coming Next: Launch Week 4","slug":"coming-next-launch-week-4","lvl":2,"i":20,"seen":0},{"content":"Get started","slug":"get-started","lvl":2,"i":21,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,2],"level":0},{"type":"inline","content":"This month's beta update is more stacked than the Superbowl (\\*Supa-bowl) halftime show. Here’s all of the highlights from January...","level":1,"lines":[1,2],"children":[{"type":"text","content":"This month's beta update is more stacked than the Superbowl (*Supa-bowl) halftime show. Here’s all of the highlights from January...","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[3,4],"level":0},{"type":"inline","content":"[New OAuth providers](#new-oauth-providers)","level":1,"lines":[3,4],"children":[{"type":"text","content":"New OAuth providers","level":0}],"lvl":2,"i":0,"seen":0,"slug":"new-oauth-providers"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[5,6],"level":0},{"type":"inline","content":"![New 0Auth Providers](/images/blog/2022-january/new-auth-providers-supabase_monthly-email-jan-2022.png)","level":1,"lines":[5,6],"children":[{"type":"image","src":"/images/blog/2022-january/new-auth-providers-supabase_monthly-email-jan-2022.png","title":"","alt":"New 0Auth Providers","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[7,8],"level":0},{"type":"inline","content":"We’re continually amazed by how quickly new auth providers are being added into Supabase, and last month, 2 more have been added.","level":1,"lines":[7,8],"children":[{"type":"text","content":"We’re continually amazed by how quickly new auth providers are being added into Supabase, and last month, 2 more have been added.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[9,10],"level":0},{"type":"inline","content":"[Notion](#notion)","level":1,"lines":[9,10],"children":[{"type":"text","content":"Notion","level":0}],"lvl":3,"i":1,"seen":0,"slug":"notion"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[11,13],"level":0},{"type":"inline","content":"Added by [zernonia](https://github.com/zernonia).\nYou may already know him as the maintainer of [madewithsupabase.com](https://www.madewithsupabase.com/).","level":1,"lines":[11,13],"children":[{"type":"text","content":"Added by ","level":0},{"type":"link_open","href":"https://github.com/zernonia","title":"","level":0},{"type":"text","content":"zernonia","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0},{"type":"softbreak","level":0},{"type":"text","content":"You may already know him as the maintainer of ","level":0},{"type":"link_open","href":"https://www.madewithsupabase.com/","title":"","level":0},{"type":"text","content":"madewithsupabase.com","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[14,15],"level":0},{"type":"inline","content":"[LinkedIn](#linkedin)","level":1,"lines":[14,15],"children":[{"type":"text","content":"LinkedIn","level":0}],"lvl":3,"i":2,"seen":0,"slug":"linkedin"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[16,18],"level":0},{"type":"inline","content":"Added by [riderx](https://github.com/riderx).\nYou may already be familiar with Martin from his podcast [Indie Makers](https://podcasts.apple.com/us/podcast/indie-makers/id1488437972).","level":1,"lines":[16,18],"children":[{"type":"text","content":"Added by ","level":0},{"type":"link_open","href":"https://github.com/riderx","title":"","level":0},{"type":"text","content":"riderx","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0},{"type":"softbreak","level":0},{"type":"text","content":"You may already be familiar with Martin from his podcast ","level":0},{"type":"link_open","href":"https://podcasts.apple.com/us/podcast/indie-makers/id1488437972","title":"","level":0},{"type":"text","content":"Indie Makers","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[19,20],"level":0},{"type":"inline","content":"[New SMS providers](#new-sms-providers)","level":1,"lines":[19,20],"children":[{"type":"text","content":"New SMS providers","level":0}],"lvl":2,"i":3,"seen":0,"slug":"new-sms-providers"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[21,22],"level":0},{"type":"inline","content":"![New SMS Providers](/images/blog/2022-january/new-sms-providers-supabase_monthly-email-jan-2022.png)","level":1,"lines":[21,22],"children":[{"type":"image","src":"/images/blog/2022-january/new-sms-providers-supabase_monthly-email-jan-2022.png","title":"","alt":"New SMS Providers","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[23,24],"level":0},{"type":"inline","content":"Along with the new 0Auth providers above, last month saw the addition of two more SMS phone providers to allow you to authenticate users via an SMS OTP (One-Time Password) token.","level":1,"lines":[23,24],"children":[{"type":"text","content":"Along with the new 0Auth providers above, last month saw the addition of two more SMS phone providers to allow you to authenticate users via an SMS OTP (One-Time Password) token.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[25,26],"level":0},{"type":"inline","content":"[Vonage](#vonage)","level":1,"lines":[25,26],"children":[{"type":"text","content":"Vonage","level":0}],"lvl":3,"i":4,"seen":0,"slug":"vonage"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[27,29],"level":0},{"type":"inline","content":"**[Vonage](https://www.vonage.com/)** is a US-based cloud communications provider.\nAdded by [devkiran](https://github.com/devkiran) (from [BoxyHQ](https://twitter.com/BoxyHQ)).","level":1,"lines":[27,29],"children":[{"type":"strong_open","level":0},{"type":"link_open","href":"https://www.vonage.com/","title":"","level":1},{"type":"text","content":"Vonage","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0},{"type":"text","content":" is a US-based cloud communications provider.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Added by ","level":0},{"type":"link_open","href":"https://github.com/devkiran","title":"","level":0},{"type":"text","content":"devkiran","level":1},{"type":"link_close","level":0},{"type":"text","content":" (from ","level":0},{"type":"link_open","href":"https://twitter.com/BoxyHQ","title":"","level":0},{"type":"text","content":"BoxyHQ","level":1},{"type":"link_close","level":0},{"type":"text","content":").","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[30,31],"level":0},{"type":"inline","content":"[Textlocal](#textlocal)","level":1,"lines":[30,31],"children":[{"type":"text","content":"Textlocal","level":0}],"lvl":3,"i":5,"seen":0,"slug":"textlocal"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[32,34],"level":0},{"type":"inline","content":"We are now fulfilling a popular request with this Indian-compliant SMS provider, [Textlocal](https://www.textlocal.com/).\nAlso added by [devkiran](https://github.com/devkiran).","level":1,"lines":[32,34],"children":[{"type":"text","content":"We are now fulfilling a popular request with this Indian-compliant SMS provider, ","level":0},{"type":"link_open","href":"https://www.textlocal.com/","title":"","level":0},{"type":"text","content":"Textlocal","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Also added by ","level":0},{"type":"link_open","href":"https://github.com/devkiran","title":"","level":0},{"type":"text","content":"devkiran","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[35,36],"level":0},{"type":"inline","content":"[Other SMS providers](#other-sms-providers)","level":1,"lines":[35,36],"children":[{"type":"text","content":"Other SMS providers","level":0}],"lvl":3,"i":6,"seen":0,"slug":"other-sms-providers"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[37,38],"level":0},{"type":"inline","content":"Just a reminder, we also support [Twilio](https://supabase.com/docs/guides/auth/phone-login/twilio) and [MessageBird](https://supabase.com/docs/guides/auth/phone-login/messagebird).","level":1,"lines":[37,38],"children":[{"type":"text","content":"Just a reminder, we also support ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/auth/phone-login/twilio","title":"","level":0},{"type":"text","content":"Twilio","level":1},{"type":"link_close","level":0},{"type":"text","content":" and ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/auth/phone-login/messagebird","title":"","level":0},{"type":"text","content":"MessageBird","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[39,40],"level":0},{"type":"inline","content":"On a final note, we are hiring for an [Auth Engineer](https://about.supabase.com/careers/auth-engineers).","level":1,"lines":[39,40],"children":[{"type":"text","content":"On a final note, we are hiring for an ","level":0},{"type":"link_open","href":"https://about.supabase.com/careers/auth-engineers","title":"","level":0},{"type":"text","content":"Auth Engineer","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[41,42],"level":0},{"type":"inline","content":"[Query logs with SQL](#query-logs-with-sql)","level":1,"lines":[41,42],"children":[{"type":"text","content":"Query logs with SQL","level":0}],"lvl":2,"i":7,"seen":0,"slug":"query-logs-with-sql"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[43,44],"level":0},{"type":"inline","content":"Supabase logs are more powerful with the [newly added SQL querying](https://github.com/supabase/supabase/pull/4734).","level":1,"lines":[43,44],"children":[{"type":"text","content":"Supabase logs are more powerful with the ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase/pull/4734","title":"","level":0},{"type":"text","content":"newly added SQL querying","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[45,46],"level":0},{"type":"inline","content":"We added [timestamp filtering](https://github.com/supabase/supabase/pull/4904), and you’ll notice our usage charts have [more time spans available](https://github.com/supabase/supabase/pull/4732).","level":1,"lines":[45,46],"children":[{"type":"text","content":"We added ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase/pull/4904","title":"","level":0},{"type":"text","content":"timestamp filtering","level":1},{"type":"link_close","level":0},{"type":"text","content":", and you’ll notice our usage charts have ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase/pull/4732","title":"","level":0},{"type":"text","content":"more time spans available","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[47,48],"level":0},{"type":"inline","content":"We’re enabling developers to quickly diagnose issues with their projects with powerful logging and observability tools and we have a lot more to come.","level":1,"lines":[47,48],"children":[{"type":"text","content":"We’re enabling developers to quickly diagnose issues with their projects with powerful logging and observability tools and we have a lot more to come.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[49,50],"level":0},{"type":"inline","content":"[GraphQL v0.1.0](#graphql-v010)","level":1,"lines":[49,50],"children":[{"type":"text","content":"GraphQL v0.1.0","level":0}],"lvl":2,"i":8,"seen":0,"slug":"graphql-v010"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[51,52],"level":0},{"type":"inline","content":"![Graph QL v0.1.0](/images/blog/2022-january/pg_graphql_0.1.0_monthly-email-dec-2021.png)","level":1,"lines":[51,52],"children":[{"type":"image","src":"/images/blog/2022-january/pg_graphql_0.1.0_monthly-email-dec-2021.png","title":"","alt":"Graph QL v0.1.0","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[53,54],"level":0},{"type":"inline","content":"Last month we released [`pg_graphql`](https://github.com/supabase/pg_graphql) v0.1.0, which includes [Comment Directives](https://supabase.github.io/pg_graphql/configuration/#comment-directives).","level":1,"lines":[53,54],"children":[{"type":"text","content":"Last month we released ","level":0},{"type":"link_open","href":"https://github.com/supabase/pg_graphql","title":"","level":0},{"type":"code","content":"pg_graphql","block":false,"level":1},{"type":"link_close","level":0},{"type":"text","content":" v0.1.0, which includes ","level":0},{"type":"link_open","href":"https://supabase.github.io/pg_graphql/configuration/#comment-directives","title":"","level":0},{"type":"text","content":"Comment Directives","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[55,56],"level":0},{"type":"inline","content":"We haven’t released GraphQL onto the platform yet because we it's still under heavy development. You can expect availability in the next few months.","level":1,"lines":[55,56],"children":[{"type":"text","content":"We haven’t released GraphQL onto the platform yet because we it's still under heavy development. You can expect availability in the next few months.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[57,58],"level":0},{"type":"inline","content":"Example","level":1,"lines":[57,58],"children":[{"type":"text","content":"Example","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql","content":"create table account(\n    id serial primary key\n);\n\ncomment on table public.account is\ne'@graphql({ \"name\": \"AccountHolder\" })';\n","lines":[59,67],"level":0},{"type":"paragraph_open","tight":false,"lines":[68,69],"level":0},{"type":"inline","content":"Result","level":1,"lines":[68,69],"children":[{"type":"text","content":"Result","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"jsx","content":"// Renames \"Account\" to \"AccountHolder\"\ntype AccountHolder {\n  id: Int!\n}\n","lines":[70,76],"level":0},{"type":"heading_open","hLevel":2,"lines":[77,78],"level":0},{"type":"inline","content":"[New examples](#new-examples)","level":1,"lines":[77,78],"children":[{"type":"text","content":"New examples","level":0}],"lvl":2,"i":9,"seen":0,"slug":"new-examples"},{"type":"heading_close","hLevel":2,"level":0},{"type":"heading_open","hLevel":3,"lines":[79,80],"level":0},{"type":"inline","content":"[Remix Auth](#remix-auth)","level":1,"lines":[79,80],"children":[{"type":"text","content":"Remix Auth","level":0}],"lvl":3,"i":10,"seen":0,"slug":"remix-auth"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[81,82],"level":0},{"type":"inline","content":"It’s all anyone seems to be [talking about](https://twitter.com/jkup/status/1456360115205033989). We genuinely love what [Remix](https://remix.run/) are doing, so it’s only right that we show off how Remix and Supabase work well together.","level":1,"lines":[81,82],"children":[{"type":"text","content":"It’s all anyone seems to be ","level":0},{"type":"link_open","href":"https://twitter.com/jkup/status/1456360115205033989","title":"","level":0},{"type":"text","content":"talking about","level":1},{"type":"link_close","level":0},{"type":"text","content":". We genuinely love what ","level":0},{"type":"link_open","href":"https://remix.run/","title":"","level":0},{"type":"text","content":"Remix","level":1},{"type":"link_close","level":0},{"type":"text","content":" are doing, so it’s only right that we show off how Remix and Supabase work well together.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[83,84],"level":0},{"type":"inline","content":"Check out the new [Remix Auth example](https://github.com/supabase/examples/tree/main/supabase-js-v1/auth/remix-auth), and let us know what you think.","level":1,"lines":[83,84],"children":[{"type":"text","content":"Check out the new ","level":0},{"type":"link_open","href":"https://github.com/supabase/examples/tree/main/supabase-js-v1/auth/remix-auth","title":"","level":0},{"type":"text","content":"Remix Auth example","level":1},{"type":"link_close","level":0},{"type":"text","content":", and let us know what you think.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[85,86],"level":0},{"type":"inline","content":"[Expo Todo List](#expo-todo-list)","level":1,"lines":[85,86],"children":[{"type":"text","content":"Expo Todo List","level":0}],"lvl":3,"i":11,"seen":0,"slug":"expo-todo-list"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[87,89],"level":0},{"type":"inline","content":"Our React Native example has been correctly updated to be an Expo example.\n[Check it out here](https://github.com/supabase/examples/tree/main/supabase-js-v1/todo-list/expo-todo-list).","level":1,"lines":[87,89],"children":[{"type":"text","content":"Our React Native example has been correctly updated to be an Expo example.","level":0},{"type":"softbreak","level":0},{"type":"link_open","href":"https://github.com/supabase/examples/tree/main/supabase-js-v1/todo-list/expo-todo-list","title":"","level":0},{"type":"text","content":"Check it out here","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[90,91],"level":0},{"type":"inline","content":"[Video: API requests with Database Webhooks](#video-api-requests-with-database-webhooks)","level":1,"lines":[90,91],"children":[{"type":"text","content":"Video: API requests with Database Webhooks","level":0}],"lvl":2,"i":12,"seen":0,"slug":"video-api-requests-with-database-webhooks"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[92,93],"level":0},{"type":"inline","content":"There's no stopping [Jon Meyers](https://jonmeyers.io/videos)! He’s back with an in-depth video on how to easily [automate API requests](https://www.youtube.com/watch?v=codAs9-NeHM\u0026feature=emb_title) using our very own Database Webhooks.","level":1,"lines":[92,93],"children":[{"type":"text","content":"There's no stopping ","level":0},{"type":"link_open","href":"https://jonmeyers.io/videos","title":"","level":0},{"type":"text","content":"Jon Meyers","level":1},{"type":"link_close","level":0},{"type":"text","content":"! He’s back with an in-depth video on how to easily ","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=codAs9-NeHM\u0026feature=emb_title","title":"","level":0},{"type":"text","content":"automate API requests","level":1},{"type":"link_close","level":0},{"type":"text","content":" using our very own Database Webhooks.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[94,95],"level":0},{"type":"inline","content":"You'll learn how to listen to _any_ database change, then send those changes [in a payload](https://supabase.com/blog/supabase-functions-updates#hook-payload) via HTTP request.","level":1,"lines":[94,95],"children":[{"type":"text","content":"You'll learn how to listen to ","level":0},{"type":"em_open","level":0},{"type":"text","content":"any","level":1},{"type":"em_close","level":0},{"type":"text","content":" database change, then send those changes ","level":0},{"type":"link_open","href":"https://supabase.com/blog/supabase-functions-updates#hook-payload","title":"","level":0},{"type":"text","content":"in a payload","level":1},{"type":"link_close","level":0},{"type":"text","content":" via HTTP request.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[96,103],"level":0},{"type":"inline","content":"\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"video-with-border w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/codAs9-NeHM\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen","level":1,"lines":[96,103],"children":[{"type":"text","content":"\u003cdiv className=\"video-container\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ciframe","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"video-with-border w-full\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://www.youtube-nocookie.com/embed/codAs9-NeHM\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"frameBorder=\"1\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allowFullScreen","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"blockquote_open","lines":[103,105],"level":0},{"type":"paragraph_open","tight":false,"lines":[103,105],"level":1},{"type":"inline","content":"\u003c/iframe\u003e\n\u003c/div\u003e","level":2,"lines":[103,105],"children":[{"type":"text","content":"\u003c/iframe\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"heading_open","hLevel":2,"lines":[106,107],"level":0},{"type":"inline","content":"[Set your own support ticket priority](#set-your-own-support-ticket-priority)","level":1,"lines":[106,107],"children":[{"type":"text","content":"Set your own support ticket priority","level":0}],"lvl":2,"i":13,"seen":0,"slug":"set-your-own-support-ticket-priority"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[108,109],"level":0},{"type":"inline","content":"As with any platform, there can be the occasional glitch.","level":1,"lines":[108,109],"children":[{"type":"text","content":"As with any platform, there can be the occasional glitch.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[110,112],"level":0},{"type":"inline","content":"The Supabase Dashboard includes a dedicated support form that goes straight to our support inbox. This support form includes your project information and,\nsince we all want to see your issues solved, we thought it would make sense that _you_ could set the priority of your support tickets.","level":1,"lines":[110,112],"children":[{"type":"text","content":"The Supabase Dashboard includes a dedicated support form that goes straight to our support inbox. This support form includes your project information and,","level":0},{"type":"softbreak","level":0},{"type":"text","content":"since we all want to see your issues solved, we thought it would make sense that ","level":0},{"type":"em_open","level":0},{"type":"text","content":"you","level":1},{"type":"em_close","level":0},{"type":"text","content":" could set the priority of your support tickets.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[113,115],"level":0},{"type":"inline","content":"This change has drastically improved response times for urgent support tickets.\nThe form includes extra “urgent” levels for the PRO and Pay As You Go projects.","level":1,"lines":[113,115],"children":[{"type":"text","content":"This change has drastically improved response times for urgent support tickets.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"The form includes extra “urgent” levels for the PRO and Pay As You Go projects.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[116,117],"level":0},{"type":"inline","content":"And, as part of our continued commitment to Support, we are hiring [Support Engineers](https://about.supabase.com/careers/support-and-qa).","level":1,"lines":[116,117],"children":[{"type":"text","content":"And, as part of our continued commitment to Support, we are hiring ","level":0},{"type":"link_open","href":"https://about.supabase.com/careers/support-and-qa","title":"","level":0},{"type":"text","content":"Support Engineers","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[118,119],"level":0},{"type":"inline","content":"[Community](#community)","level":1,"lines":[118,119],"children":[{"type":"text","content":"Community","level":0}],"lvl":2,"i":14,"seen":0,"slug":"community"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[120,121],"level":0},{"type":"inline","content":"As always, the community has been amazing during the month of February.","level":1,"lines":[120,121],"children":[{"type":"text","content":"As always, the community has been amazing during the month of February.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[122,123],"level":0},{"type":"inline","content":"[Supabase + Snaplet](#supabase--snaplet)","level":1,"lines":[122,123],"children":[{"type":"text","content":"Supabase + Snaplet","level":0}],"lvl":3,"i":15,"seen":0,"slug":"supabase--snaplet"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[124,125],"level":0},{"type":"inline","content":"Are you maintaining multiple environments? Snaplet helps you copy a production database and clone it into different environments.","level":1,"lines":[124,125],"children":[{"type":"text","content":"Are you maintaining multiple environments? Snaplet helps you copy a production database and clone it into different environments.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[126,127],"level":0},{"type":"inline","content":"Check out the [Supabase clone environments](https://docs.snaplet.dev/tutorials/supabase-clone-environments) tutorial on the Snaplet docs.","level":1,"lines":[126,127],"children":[{"type":"text","content":"Check out the ","level":0},{"type":"link_open","href":"https://docs.snaplet.dev/tutorials/supabase-clone-environments","title":"","level":0},{"type":"text","content":"Supabase clone environments","level":1},{"type":"link_close","level":0},{"type":"text","content":" tutorial on the Snaplet docs.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[128,135],"level":0},{"type":"inline","content":"\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"video-with-border w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/oPtMMhdhEP4\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen","level":1,"lines":[128,135],"children":[{"type":"text","content":"\u003cdiv className=\"video-container\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ciframe","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"video-with-border w-full\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://www.youtube-nocookie.com/embed/oPtMMhdhEP4\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"frameBorder=\"1\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allowFullScreen","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"blockquote_open","lines":[135,137],"level":0},{"type":"paragraph_open","tight":false,"lines":[135,137],"level":1},{"type":"inline","content":"\u003c/iframe\u003e\n\u003c/div\u003e","level":2,"lines":[135,137],"children":[{"type":"text","content":"\u003c/iframe\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"heading_open","hLevel":3,"lines":[138,139],"level":0},{"type":"inline","content":"[Supabase + Retool](#supabase--retool)","level":1,"lines":[138,139],"children":[{"type":"text","content":"Supabase + Retool","level":0}],"lvl":3,"i":16,"seen":0,"slug":"supabase--retool"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[140,141],"level":0},{"type":"inline","content":"Retool has put together a brilliant 10 minute admin panel setup using Supabase with Retool.","level":1,"lines":[140,141],"children":[{"type":"text","content":"Retool has put together a brilliant 10 minute admin panel setup using Supabase with Retool.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[142,149],"level":0},{"type":"inline","content":"\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"video-with-border w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/AgB2-CSrnoI\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen","level":1,"lines":[142,149],"children":[{"type":"text","content":"\u003cdiv className=\"video-container\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ciframe","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"video-with-border w-full\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://www.youtube-nocookie.com/embed/AgB2-CSrnoI\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"frameBorder=\"1\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allowFullScreen","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"blockquote_open","lines":[149,151],"level":0},{"type":"paragraph_open","tight":false,"lines":[149,151],"level":1},{"type":"inline","content":"\u003c/iframe\u003e\n\u003c/div\u003e","level":2,"lines":[149,151],"children":[{"type":"text","content":"\u003c/iframe\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"heading_open","hLevel":3,"lines":[152,153],"level":0},{"type":"inline","content":"[Learn with Jason](#learn-with-jason)","level":1,"lines":[152,153],"children":[{"type":"text","content":"Learn with Jason","level":0}],"lvl":3,"i":17,"seen":0,"slug":"learn-with-jason"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[154,155],"level":0},{"type":"inline","content":"Jason Lengstorf caught up with our very own [Jon Meyers](https://jonmeyers.io/) on his awesome show, [Learn with Jason](https://www.youtube.com/watch?v=8vqY1KT4TLU), to talk about building an app [with Supabase and Next.js](https://supabase.com/docs/guides/with-nextjs).","level":1,"lines":[154,155],"children":[{"type":"text","content":"Jason Lengstorf caught up with our very own ","level":0},{"type":"link_open","href":"https://jonmeyers.io/","title":"","level":0},{"type":"text","content":"Jon Meyers","level":1},{"type":"link_close","level":0},{"type":"text","content":" on his awesome show, ","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=8vqY1KT4TLU","title":"","level":0},{"type":"text","content":"Learn with Jason","level":1},{"type":"link_close","level":0},{"type":"text","content":", to talk about building an app ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/with-nextjs","title":"","level":0},{"type":"text","content":"with Supabase and Next.js","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[156,163],"level":0},{"type":"inline","content":"\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"video-with-border w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/8vqY1KT4TLU\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen","level":1,"lines":[156,163],"children":[{"type":"text","content":"\u003cdiv className=\"video-container\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ciframe","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"video-with-border w-full\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://www.youtube-nocookie.com/embed/8vqY1KT4TLU\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"frameBorder=\"1\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allowFullScreen","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"blockquote_open","lines":[163,165],"level":0},{"type":"paragraph_open","tight":false,"lines":[163,165],"level":1},{"type":"inline","content":"\u003c/iframe\u003e\n\u003c/div\u003e","level":2,"lines":[163,165],"children":[{"type":"text","content":"\u003c/iframe\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"heading_open","hLevel":3,"lines":[166,167],"level":0},{"type":"inline","content":"[New article highlights](#new-article-highlights)","level":1,"lines":[166,167],"children":[{"type":"text","content":"New article highlights","level":0}],"lvl":3,"i":18,"seen":0,"slug":"new-article-highlights"},{"type":"heading_close","hLevel":3,"level":0},{"type":"bullet_list_open","lines":[168,172],"level":0},{"type":"list_item_open","lines":[168,169],"level":1},{"type":"paragraph_open","tight":true,"lines":[168,169],"level":2},{"type":"inline","content":"Arctype published a new guide showing how to connect to your Supabase database. [Link](https://arctype.com/postgres/connect/supabase-postgres)","level":3,"lines":[168,169],"children":[{"type":"text","content":"Arctype published a new guide showing how to connect to your Supabase database. ","level":0},{"type":"link_open","href":"https://arctype.com/postgres/connect/supabase-postgres","title":"","level":0},{"type":"text","content":"Link","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[169,170],"level":1},{"type":"paragraph_open","tight":true,"lines":[169,170],"level":2},{"type":"inline","content":"Marmalabs built a Supabase adapter for react-admin, the frontend framework for building admin applications on top of REST/GraphQL services. [Link](https://github.com/marmelab/ra-supabase)","level":3,"lines":[169,170],"children":[{"type":"text","content":"Marmalabs built a Supabase adapter for react-admin, the frontend framework for building admin applications on top of REST/GraphQL services. ","level":0},{"type":"link_open","href":"https://github.com/marmelab/ra-supabase","title":"","level":0},{"type":"text","content":"Link","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[170,172],"level":1},{"type":"paragraph_open","tight":true,"lines":[170,171],"level":2},{"type":"inline","content":"HotGlue created a guide showing how easy it is to integrate Salesforce and Supabase. [Link](https://www.notion.so/Supabase-and-hotglue-article-9e7f5583d27c419490ee7d536d6d269d)","level":3,"lines":[170,171],"children":[{"type":"text","content":"HotGlue created a guide showing how easy it is to integrate Salesforce and Supabase. ","level":0},{"type":"link_open","href":"https://www.notion.so/Supabase-and-hotglue-article-9e7f5583d27c419490ee7d536d6d269d","title":"","level":0},{"type":"text","content":"Link","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":3,"lines":[172,173],"level":0},{"type":"inline","content":"[PostgREST](#postgrest)","level":1,"lines":[172,173],"children":[{"type":"text","content":"PostgREST","level":0}],"lvl":3,"i":19,"seen":0,"slug":"postgrest"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[174,175],"level":0},{"type":"inline","content":"One of Supabase’s key tools that allow a lot of our functionality has had some updates.","level":1,"lines":[174,175],"children":[{"type":"text","content":"One of Supabase’s key tools that allow a lot of our functionality has had some updates.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[176,179],"level":0},{"type":"list_item_open","lines":[176,177],"level":1},{"type":"paragraph_open","tight":true,"lines":[176,177],"level":2},{"type":"inline","content":"`pg_listen` was removed in favor of PostgREST’s built-in schema reloading.","level":3,"lines":[176,177],"children":[{"type":"code","content":"pg_listen","block":false,"level":0},{"type":"text","content":" was removed in favor of PostgREST’s built-in schema reloading.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[177,179],"level":1},{"type":"paragraph_open","tight":true,"lines":[177,178],"level":2},{"type":"inline","content":"PostgREST database connection pool size gets scaled with compute size for better performance for certain workload shapes.","level":3,"lines":[177,178],"children":[{"type":"text","content":"PostgREST database connection pool size gets scaled with compute size for better performance for certain workload shapes.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":2,"lines":[179,180],"level":0},{"type":"inline","content":"[Coming Next: Launch Week 4](#coming-next-launch-week-4)","level":1,"lines":[179,180],"children":[{"type":"text","content":"Coming Next: Launch Week 4","level":0}],"lvl":2,"i":20,"seen":0,"slug":"coming-next-launch-week-4"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[181,182],"level":0},{"type":"inline","content":"Preparation for Launch Week 4 is underway!","level":1,"lines":[181,182],"children":[{"type":"text","content":"Preparation for Launch Week 4 is underway!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[183,184],"level":0},{"type":"inline","content":"Of course, we can’t tell you what will happen (perhaps because we don’t know ourselves yet), but you can always speculate in the community [Discord server](https://discord.supabase.com/), or even [tweet us your predictions](https://twitter.com/supabase).","level":1,"lines":[183,184],"children":[{"type":"text","content":"Of course, we can’t tell you what will happen (perhaps because we don’t know ourselves yet), but you can always speculate in the community ","level":0},{"type":"link_open","href":"https://discord.supabase.com/","title":"","level":0},{"type":"text","content":"Discord server","level":1},{"type":"link_close","level":0},{"type":"text","content":", or even ","level":0},{"type":"link_open","href":"https://twitter.com/supabase","title":"","level":0},{"type":"text","content":"tweet us your predictions","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[185,186],"level":0},{"type":"inline","content":"[Get started](#get-started)","level":1,"lines":[185,186],"children":[{"type":"text","content":"Get started","level":0}],"lvl":2,"i":21,"seen":0,"slug":"get-started"},{"type":"heading_close","hLevel":2,"level":0},{"type":"bullet_list_open","lines":[187,192],"level":0},{"type":"list_item_open","lines":[187,188],"level":1},{"type":"paragraph_open","tight":true,"lines":[187,188],"level":2},{"type":"inline","content":"Start using Supabase today: **[supabase.com/dashboard](https://supabase.com/dashboard/)**","level":3,"lines":[187,188],"children":[{"type":"text","content":"Start using Supabase today: ","level":0},{"type":"strong_open","level":0},{"type":"link_open","href":"https://supabase.com/dashboard/","title":"","level":1},{"type":"text","content":"supabase.com/dashboard","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[188,189],"level":1},{"type":"paragraph_open","tight":true,"lines":[188,189],"level":2},{"type":"inline","content":"Make sure to **[star us on GitHub](https://github.com/supabase/supabase)**","level":3,"lines":[188,189],"children":[{"type":"text","content":"Make sure to ","level":0},{"type":"strong_open","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase","title":"","level":1},{"type":"text","content":"star us on GitHub","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[189,190],"level":1},{"type":"paragraph_open","tight":true,"lines":[189,190],"level":2},{"type":"inline","content":"Follow us **[on Twitter](https://twitter.com/supabase)**","level":3,"lines":[189,190],"children":[{"type":"text","content":"Follow us ","level":0},{"type":"strong_open","level":0},{"type":"link_open","href":"https://twitter.com/supabase","title":"","level":1},{"type":"text","content":"on Twitter","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[190,191],"level":1},{"type":"paragraph_open","tight":true,"lines":[190,191],"level":2},{"type":"inline","content":"Subscribe to our **[YouTube channel](https://www.youtube.com/c/supabase)**","level":3,"lines":[190,191],"children":[{"type":"text","content":"Subscribe to our ","level":0},{"type":"strong_open","level":0},{"type":"link_open","href":"https://www.youtube.com/c/supabase","title":"","level":1},{"type":"text","content":"YouTube channel","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[191,192],"level":1},{"type":"paragraph_open","tight":true,"lines":[191,192],"level":2},{"type":"inline","content":"Become a **[sponsor](https://github.com/sponsors/supabase)**","level":3,"lines":[191,192],"children":[{"type":"text","content":"Become a ","level":0},{"type":"strong_open","level":0},{"type":"link_open","href":"https://github.com/sponsors/supabase","title":"","level":1},{"type":"text","content":"sponsor","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0}],"content":"- [New OAuth providers](#new-oauth-providers)\n  * [Notion](#notion)\n  * [LinkedIn](#linkedin)\n- [New SMS providers](#new-sms-providers)\n  * [Vonage](#vonage)\n  * [Textlocal](#textlocal)\n  * [Other SMS providers](#other-sms-providers)\n- [Query logs with SQL](#query-logs-with-sql)\n- [GraphQL v0.1.0](#graphql-v010)\n- [New examples](#new-examples)\n  * [Remix Auth](#remix-auth)\n  * [Expo Todo List](#expo-todo-list)\n- [Video: API requests with Database Webhooks](#video-api-requests-with-database-webhooks)\n- [Set your own support ticket priority](#set-your-own-support-ticket-priority)\n- [Community](#community)\n  * [Supabase + Snaplet](#supabase--snaplet)\n  * [Supabase + Retool](#supabase--retool)\n  * [Learn with Jason](#learn-with-jason)\n  * [New article highlights](#new-article-highlights)\n  * [PostgREST](#postgrest)\n- [Coming Next: Launch Week 4](#coming-next-launch-week-4)\n- [Get started](#get-started)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-beta-january-2022"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>