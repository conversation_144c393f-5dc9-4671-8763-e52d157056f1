<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase Auth: SSO,  Mobile, and Server-side support</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Supacharging Supabase Auth with Sign in with Apple on iOS, Single-Sign-On support with SAML 2.0, and PKCE for server-side rendering and mobile auth." data-next-head=""/><meta property="og:title" content="Supabase Auth: SSO,  Mobile, and Server-side support" data-next-head=""/><meta property="og:description" content="Supacharging Supabase Auth with Sign in with Apple on iOS, Single-Sign-On support with SAML 2.0, and PKCE for server-side rendering and mobile auth." data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-auth-sso-pkce" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2023-04-13" data-next-head=""/><meta property="article:author" content="https://github.com/hf" data-next-head=""/><meta property="article:author" content="https://github.com/j0" data-next-head=""/><meta property="article:author" content="https://github.com/kangmingtay" data-next-head=""/><meta property="article:tag" content="launch-week" data-next-head=""/><meta property="article:tag" content="auth" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/launch-week-7/day-4-supabase-auth-sso-pkce/supabase-auth-sso-pkce-thumb.png" data-next-head=""/><meta property="og:image:alt" content="Supabase Auth: SSO,  Mobile, and Server-side support thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Supabase Auth: SSO,  Mobile, and Server-side support</h1><div class="text-light flex space-x-3 text-sm"><p>13 Apr 2023</p><p>•</p><p>10 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/hf"><div class="flex items-center gap-3"><div class="w-10"><img alt="Stojan Dimitrovski avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fhf.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fhf.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fhf.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Stojan Dimitrovski</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/j0"><div class="flex items-center gap-3"><div class="w-10"><img alt="Joel Lee avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fj0.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fj0.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fj0.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Joel Lee</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/kangmingtay"><div class="flex items-center gap-3"><div class="w-10"><img alt="Kang Ming Tay avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fkangmingtay.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fkangmingtay.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fkangmingtay.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Kang Ming Tay</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Supabase Auth: SSO,  Mobile, and Server-side support" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-4-supabase-auth-sso-pkce%2Fsupabase-auth-sso-pkce-og.jpg&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-4-supabase-auth-sso-pkce%2Fsupabase-auth-sso-pkce-og.jpg&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-4-supabase-auth-sso-pkce%2Fsupabase-auth-sso-pkce-og.jpg&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-4-supabase-auth-sso-pkce%2Fsupabase-auth-sso-pkce-og.jpg&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-4-supabase-auth-sso-pkce%2Fsupabase-auth-sso-pkce-og.jpg&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-4-supabase-auth-sso-pkce%2Fsupabase-auth-sso-pkce-og.jpg&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-4-supabase-auth-sso-pkce%2Fsupabase-auth-sso-pkce-og.jpg&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-4-supabase-auth-sso-pkce%2Fsupabase-auth-sso-pkce-og.jpg&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-4-supabase-auth-sso-pkce%2Fsupabase-auth-sso-pkce-og.jpg&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-4-supabase-auth-sso-pkce%2Fsupabase-auth-sso-pkce-og.jpg&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-4-supabase-auth-sso-pkce%2Fsupabase-auth-sso-pkce-og.jpg&amp;w=3840&amp;q=100"/></div><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p>Today we&#x27;re excited to announce a few new features for Supabase Auth:</p>
<ol>
<li><a href="#single-sign-on-support-using-saml-20">Easily add Single Sign-On support to your projects using SAML 2.0</a></li>
<li><a href="#server-side-and-mobile-auth">Better support for server-side rendering and mobile apps using PKCE</a></li>
<li><a href="#native-apple-login-on-ios">Native Apple login on iOS</a></li>
</ol>
<h2 id="single-sign-on-support-using-saml-20" class="group scroll-mt-24">Single Sign-On Support using SAML 2.0<a href="#single-sign-on-support-using-saml-20" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>With Single Sign-On (SSO), your users can login with their company&#x27;s identity provider (IDP), a critical feature when you&#x27;re building applications for Enterprises.</p>
<p>Every developer building a B2B application eventually needs the SSO authentication flow to onboard enterprise customers. SSO is a requirement for larger Enterprise customers because it&#x27;s a standard request in Enterprise Security Policies. Over the past few months, we&#x27;ve been <a href="https://supabase.com/docs/guides/platform/sso">dogfooding SSO for our own Enterprise customers</a>, and today we&#x27;re releasing it for you to do the same.</p>
<p>Building SSO into your application isn&#x27;t necessarily hard, but does come with some complexity. A lot of time can be spent understanding the nuances and details of the protocol - from dissecting the jargon to testing the implementation heavily. It took us months to build it for ourselves. With this release, you will have SSO set up and running in less than an hour so that you can focus on shipping the core features of your product. This feature is available for the <a href="../pricing.html">Pro Plan and above</a>, starting today. This will also be available on the self-hosted version.</p>
<div class="video-container"><iframe class="w-full" src="https://www.youtube-nocookie.com/embed/hAwJeR6mhB0" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share"></iframe></div>
<h3 id="getting-started-with-saml-20" class="group scroll-mt-24">Getting Started with SAML 2.0<a href="#getting-started-with-saml-20" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>To get started, enable the <a href="https://supabase.com/dashboard/project/phcnitosaawbzytgyznx/auth/providers">“SAML 2.0“</a> <a href="https://supabase.com/dashboard/project/_/auth/providers">authentication method in the dashboard</a>. We&#x27;ve added new commands to the <a href="https://supabase.com/docs/guides/cli">Supabase CLI</a> to help with the configuration process:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span>$ supabase sso --help</span></div></div><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span>Manage Single Sign-On (SSO) authentication for projects</span></div></div><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span>Usage:</span></div></div><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span>  supabase sso [command]</span></div></div><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span>Available Commands:</span></div></div><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span>  add         Add a new SSO identity provider</span></div></div><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span>  info        Returns the SAML SSO settings required for the identity provider</span></div></div><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span>  list        List all SSO identity providers for a project</span></div></div><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span>  remove      Remove an existing SSO identity provider</span></div></div><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span>  show        Show information about an SSO identity provider</span></div></div><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span>  update      Update information about an SSO identity provider</span></div></div><br/></code></div></div>
<p>Once you&#x27;ve added a new SSO identity provider to your project, it&#x27;s as simple as calling the <code class="short-inline-codeblock">signInWithSSO()</code> from the <code class="short-inline-codeblock">supabase-js</code> library:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>const { data } = await supabase.auth.signInWithSSO({ domain: &#x27;acme.corp&#x27; })</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>if (data.url) window.location.href = data.url</span></div></div><br/></code></div></div>
<h3 id="sso-with-row-level-security-and-multi-tenancy" class="group scroll-mt-24">SSO with Row Level Security and multi-tenancy<a href="#sso-with-row-level-security-and-multi-tenancy" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>As usual, we&#x27;ve engineered this feature around the excellent capabilities of PostgreSQL.</p>
<p>For example, you can use Row Level Security (RLS) to build multi-tenant applications, simply by using the provider&#x27;s unique identifier in the user&#x27;s JWT:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>create policy &quot;Only allow read-write access to tenants&quot; on tablename as restrictive to authenticated using (</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  tenant_id = (select auth.jwt() -&gt; &#x27;app_metadata&#x27; -&gt;&gt; &#x27;provider&#x27;)</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>);</span></div></div><br/></code></div></div>
<p>The journey to enterprise readiness isn&#x27;t an end goal, it is a continuous process that demands constant attention and maintenance. With Supabase Auth, your team can offload this engineering burden to us and prioritize the features that matter.</p>
<h2 id="server-side-and-mobile-auth" class="group scroll-mt-24">Server-Side and Mobile Auth<a href="#server-side-and-mobile-auth" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Many developers today are using Supabase to build mobile apps, and server-side rendering is becoming popular (again!). This release will add support for these use cases by introducing the <em>Proof Key for Code Exchange flow (PKCE)</em> authentication flow. This improves security for mobile apps and makes building server-first apps simple. Since this is a major update that touches many of the authentication routes, we will be rolling it out gradually over the next few weeks.</p>
<h3 id="a-brief-history-of-supabase-auth" class="group scroll-mt-24">A brief history of Supabase Auth<a href="#a-brief-history-of-supabase-auth" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>When we <a href="https://news.ycombinator.com/item?id=24072051">launched</a> Supabase Auth, our target was JAMstack developers. In these cases, the protocol used between the user&#x27;s application and Supabase Auth is known as the <a href="https://www.rfc-editor.org/rfc/rfc6749#section-4.2">Implicit Grant Flow</a>:</p>
<!-- -->
<p>As developers built more complex apps, they encountered two problems with this authentication flow:</p>
<ul>
<li><strong>Server-Side Email Verification Links</strong>
Data provided in a URL fragment is only accessible in a browser environment, not on the server. This is problematic for email verification links that redirect users to a server-side route.</li>
<li><strong>Challenges with Mobile App Authentication</strong>
The implicit grant flow raised security concerns for mobile use cases since <a href="https://www.rfc-editor.org/rfc/rfc7636#section-1">malicious apps could potentially obtain the user session</a>.</li>
</ul>
<p>Server-side auth unlocks a number of benefits. Developers can:</p>
<ul>
<li>Set cookies on the same domain as the application.</li>
<li>Enable server-side rendering for protected pages.</li>
<li>Perform downstream actions after user authentication, such as adding the user to a CRM or sending analytics.</li>
</ul>
<h3 id="introducing-pkce" class="group scroll-mt-24">Introducing PKCE<a href="#introducing-pkce" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>To solve these problems, we&#x27;re introducing support for the <em>Proof Key for Code Exchange</em> flow (PKCE, pronounced “pixy”).</p>
<p>The PKCE flow introduces a <em>code verifier</em> (a randomly generated secret) and a <em>code challenge</em> (the hash of the <em>code verifier</em>). The authorization code is returned as a query parameter so it&#x27;s accessible on the server. During the PKCE flow:</p>
<ol>
<li>The <em>code challenge</em> is sent to Supabase Auth, which returns an <em>authorization code.</em></li>
<li>The client sends the <em>authorization code</em> together with the <em>code verifier</em> to obtain the user&#x27;s session.</li>
<li>Supabase Auth checks if the <em>code verifier</em> matches the <em>code challenge</em> sent earlier by computing the hash. This renders a malicious attacker&#x27;s attempt to intercept the authorization code useless, since they need to know the value of the <em>code verifier</em> as well.</li>
</ol>
<!-- -->
<h3 id="migrating-to-pkce-on-the-client" class="group scroll-mt-24">Migrating to PKCE on the client<a href="#migrating-to-pkce-on-the-client" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Over the next few weeks, you&#x27;ll be able to use it with the Supabase libraries. We&#x27;ve already added PKCE to the <a href="https://supabase.com/docs/reference/javascript/installing">JavaScript</a> client library and our <a href="https://supabase.com/docs/guides/auth/auth-helpers">auth-helpers</a> library. If you&#x27;re using <code class="short-inline-codeblock">supabase-js</code> , you can switch to PKCE by initializing your client with the following option:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>import { createClient } from &#x27;@supabase/supabase-js&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  auth: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    flowType: &#x27;pkce&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  },</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>})</span></div></div><br/></code></div></div>
<p>For client-side auth, that&#x27;s all you need to do to switch over. <code class="short-inline-codeblock">supabase-js</code> will handle the generation and storage for the code verifier, as well as exchanging the authorization code for the user&#x27;s session.</p>
<h3 id="migrating-to-pkce-on-the-server" class="group scroll-mt-24">Migrating to PKCE on the server<a href="#migrating-to-pkce-on-the-server" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Server-side authentication is now a lot easier. Let&#x27;s look at an example using Next.js.</p>
<p>Install the <code class="short-inline-codeblock">next</code> version of auth-helpers (lets use the <code class="short-inline-codeblock">nextjs</code> version for this example)</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>npm install @supabase/auth-helpers-nextjs@next</span></div></div><br/></code></div></div>
<p>Then prepare an endpoint for the sign in process. The redirect URL is set to <code class="short-inline-codeblock">/api/auth/callback</code>, which will be implemented next.</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span>// api/auth/login</span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span>import { NextApiRequest, NextApiResponse } from &#x27;next&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span>import { createServerSupabaseClient } from &#x27;@supabase/auth-helpers-nextjs&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span>export default async function handler(req: NextApiRequest, res: NextApiResponse) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span>  // Create the Supabase Client</span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span>  const supabase = createServerSupabaseClient(</span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span>    { req, res },</span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span>    {</span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span>      supabaseUrl: process.env.SUPABASE_URL,</span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span>      supabaseKey: process.env.SUPABASE_ANON_KEY,</span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span>    }</span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span>  )</span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span>  // Start sign in with one-time password</span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span>  const { error } = await supabase.auth.signInWithOtp({</span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span>    email: &#x27;<EMAIL>&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span>    options: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span>      emailRedirectTo: &#x27;http://localhost:3000/api/auth/callback&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span>    },</span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span>  })</span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span>  if (error) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span>    res.json(JSON.stringify(error))</span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span>  res.redirect(&#x27;/&#x27;)</span></div></div><div><span class="ch-code-line-number">_<!-- -->28</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<p>Now we can set up the callback API endpoint:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>// api/auth/callback</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>import { NextApiRequest, NextApiResponse } from &#x27;next&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>import { createServerSupabaseClient } from &#x27;@supabase/auth-helpers-nextjs&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>export default async function handler(req: NextApiRequest, res: NextApiResponse) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>  // Create authenticated Supabase Client</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>  const supabase = createServerSupabaseClient(</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>    { req, res },</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>    {</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>      supabaseUrl: SUPABASE_URL,</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>      supabaseKey: SUPABASE_ANON_KEY,</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>    }</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>  )</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>  // check for code in url querystring</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>  const code = req.query.code</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>  if (typeof code === &#x27;string&#x27;) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>    // exchange the auth code for user session</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>    await supabase.auth.exchangeCodeForSession(code)</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>  // redirect the user to a server-side protected area in your app</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>  res.redirect(&#x27;/&#x27;)</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<h3 id="roll-out" class="group scroll-mt-24">Roll out<a href="#roll-out" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Since this is a major update that touches many of the authentication routes, we will roll it out gradually over the next few weeks. You will receive a notification in your dashboard when the feature is available for your project. Reach out to us if you want early access to this feature.</p>
<p><strong>Update</strong>: Server-Side Auth (PKCE) is now available on all projects. Please refer to our <a href="https://supabase.com/docs/guides/auth/server-side-rendering">Server Side Auth Guide</a> for further details on how to add PKCE to your project.</p>
<h2 id="native-apple-login-on-ios" class="group scroll-mt-24">Native Apple login on iOS<a href="#native-apple-login-on-ios" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>While PKCE support is great, that is not the only news for you mobile app developers out there.</p>
<p>Building apps for iOS requires <sup><a href="#user-content-fn-1" id="user-content-fnref-1" data-footnote-ref="true" aria-describedby="footnote-label">1</a></sup> support for native <em>Sign in with Apple</em>. We heard the community&#x27;s requests for native sign-in. We hope you join our excitement to officially announce support for native <em>Sign in with Apple</em>.</p>
<p>Your app&#x27;s iOS Bundle ID can now be configured in the Apple provider section of your project&#x27;s dashboard.</p>
<p></p>
<p>This is the only prerequisite for triggering a native <em>Sign in with Apple.</em> With <a href="https://pub.dev/packages/supabase_flutter#native-sign-in-with-apple-example">supabase-flutter</a>, this is as easy as:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>final AuthResponse response = await supabase.auth.signInWithApple();</span></div></div><br/></code></div></div>
<p>It&#x27;s that easy! No need to set up deep links, no need to pass any parameters.</p>
<p>We&#x27;re just starting with Apple login, and soon add support for Google login.</p>
<h2 id="wrapping-up" class="group scroll-mt-24">Wrapping Up<a href="#wrapping-up" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Supabase Auth aims to continue developing auth features that are secure by default yet simple to implement. We use Supabase Auth for our hosted platform and continuously dogfood the latest version on it. If you are interested to migrate to Supabase Auth, you can check out this blog post on how <a href="https://kevcodez.medium.com/migrating-125-000-users-from-auth0-to-supabase-81c0568de307">Parqet migrated 125,000 users from Auth0 to Supabase Auth</a>.</p>
<section data-footnotes="true" class="footnotes"><h2 id="footnote-label" class="sr-only">Footnotes<a href="#footnote-label" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ol>
<li id="user-content-fn-1">
<p><a href="https://developer.apple.com/app-store/review/guidelines/#sign-in-with-apple">App store review guidelines</a> <a href="#user-content-fnref-1" data-footnote-backref="true" class="data-footnote-backref" aria-label="Back to content">↩</a></p>
</li>
</ol>
</section></div></article><div class="flex flex-col gap-3 lg:gap-4 border-t border-muted py-4 lg:py-8 mt-4 lg:mt-8"><h3 class="text-foreground text-xl mb-4">More Launch Week 7</h3><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="https://supabase.com/blog/designing-with-ai-midjourney"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Designing with AI</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="https://github.com/supabase/supavisor"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Supavisor</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="https://supabase.com/blog/supabase-logs-self-hosted"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Open Source Logging</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="https://supabase.com/blog/edge-runtime-self-hosted-deno-functions"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Self-hosted Deno Edge Functions</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="storage-v3-resumable-uploads.html"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg max-w-[240px]">Storage v3: Resumable Uploads with support for 50GB files</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="supabase-auth-sso-pkce.html"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg max-w-[240px]">Supabase Auth: SSO, Mobile, and Server-side support</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="launch-week-7-community-highlights.html"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Community Highlight</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="supabase-studio-2.0.html"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Studio Updates</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="dbdev.html"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">dbdev</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="pg-tle.html"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Postgres TLE</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div></div><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-auth-sso-pkce&amp;text=Supabase%20Auth%3A%20SSO%2C%20%20Mobile%2C%20and%20Server-side%20support"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-auth-sso-pkce&amp;text=Supabase%20Auth%3A%20SSO%2C%20%20Mobile%2C%20and%20Server-side%20support"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-auth-sso-pkce&amp;t=Supabase%20Auth%3A%20SSO%2C%20%20Mobile%2C%20and%20Server-side%20support"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="supabase-studio-2.0.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Supabase Studio 2.0: help when you need it most</h4><p class="small">14 April 2023</p></div></div></div></div></a></div><div><a href="storage-v3-resumable-uploads.html"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Supabase Storage v3: Resumable Uploads with support for 50GB files</h4><p class="small">12 April 2023</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/launch-week"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">launch-week</div></a><a href="https://supabase.com/blog/tags/auth"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">auth</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#single-sign-on-support-using-saml-20">Single Sign-On Support using SAML 2.0</a>
<ul>
<li><a href="#getting-started-with-saml-20">Getting Started with SAML 2.0</a></li>
<li><a href="#sso-with-row-level-security-and-multi-tenancy">SSO with Row Level Security and multi-tenancy</a></li>
</ul>
</li>
<li><a href="#server-side-and-mobile-auth">Server-Side and Mobile Auth</a>
<ul>
<li><a href="#a-brief-history-of-supabase-auth">A brief history of Supabase Auth</a></li>
<li><a href="#introducing-pkce">Introducing PKCE</a></li>
<li><a href="#migrating-to-pkce-on-the-client">Migrating to PKCE on the client</a></li>
<li><a href="#migrating-to-pkce-on-the-server">Migrating to PKCE on the server</a></li>
<li><a href="#roll-out">Roll out</a></li>
</ul>
</li>
<li><a href="#native-apple-login-on-ios">Native Apple login on iOS</a></li>
<li><a href="#wrapping-up">Wrapping Up</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-auth-sso-pkce&amp;text=Supabase%20Auth%3A%20SSO%2C%20%20Mobile%2C%20and%20Server-side%20support"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-auth-sso-pkce&amp;text=Supabase%20Auth%3A%20SSO%2C%20%20Mobile%2C%20and%20Server-side%20support"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-auth-sso-pkce&amp;t=Supabase%20Auth%3A%20SSO%2C%20%20Mobile%2C%20and%20Server-side%20support"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"supabase-studio-2.0","title":"Supabase Studio 2.0: help when you need it most","description":"Supabase Studio now comes with ChatGPT, and GraphiQL built in, Cascade Deletes, and Foreign Key Selectors, and much more.","launchweek":"7","categories":["product"],"tags":["launch-week","studio"],"date":"2023-04-14","toc_depth":3,"author":"alaister,joshenlim,jonny,saltcod","image":"launch-week-7/day-5-supabase-studio-2.0/day-5-supabase-studio-2.0-og.jpg","thumb":"launch-week-7/day-5-supabase-studio-2.0/day-5-supabase-studio-2.0-thumb.jpg","formattedDate":"14 April 2023","readingTime":"8 minute read","url":"/blog/supabase-studio-2.0","path":"/blog/supabase-studio-2.0"},"nextPost":{"slug":"storage-v3-resumable-uploads","title":"Supabase Storage v3: Resumable Uploads with support for 50GB files","description":"Storage V3 with lots of new features including resumable uploads, more image transformationsm a Next.js image loader and more.","launchweek":"7","categories":["product"],"tags":["launch-week","storage"],"date":"2023-04-12","toc_depth":3,"author":"fabrizio,inian","image":"launch-week-7/day-3-storage-resumable-uploads/storage-v3-og.jpg","thumb":"launch-week-7/day-3-storage-resumable-uploads/storage-v3-thumb.jpg","formattedDate":"12 April 2023","readingTime":"9 minute read","url":"/blog/storage-v3-resumable-uploads","path":"/blog/storage-v3-resumable-uploads"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-auth-sso-pkce","source":"\nToday we're excited to announce a few new features for Supabase Auth:\n\n1. [Easily add Single Sign-On support to your projects using SAML 2.0](/blog/supabase-auth-sso-pkce#single-sign-on-support-using-saml-20)\n2. [Better support for server-side rendering and mobile apps using PKCE](/blog/supabase-auth-sso-pkce#server-side-and-mobile-auth)\n3. [Native Apple login on iOS](/blog/supabase-auth-sso-pkce#native-apple-login-on-ios)\n\n## Single Sign-On Support using SAML 2.0\n\nWith Single Sign-On (SSO), your users can login with their company's identity provider (IDP), a critical feature when you're building applications for Enterprises.\n\nEvery developer building a B2B application eventually needs the SSO authentication flow to onboard enterprise customers. SSO is a requirement for larger Enterprise customers because it's a standard request in Enterprise Security Policies. Over the past few months, we've been [dogfooding SSO for our own Enterprise customers](https://supabase.com/docs/guides/platform/sso), and today we're releasing it for you to do the same.\n\nBuilding SSO into your application isn't necessarily hard, but does come with some complexity. A lot of time can be spent understanding the nuances and details of the protocol - from dissecting the jargon to testing the implementation heavily. It took us months to build it for ourselves. With this release, you will have SSO set up and running in less than an hour so that you can focus on shipping the core features of your product. This feature is available for the [Pro Plan and above](https://supabase.com/pricing), starting today. This will also be available on the self-hosted version.\n\n\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/hAwJeR6mhB0\"\n    title=\"YouTube video player\"\n    frameborder=\"0\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"\n    allowfullscreen\n  \u003e\u003c/iframe\u003e\n\u003c/div\u003e\n\n### Getting Started with SAML 2.0\n\nTo get started, enable the [“SAML 2.0“](https://supabase.com/dashboard/project/phcnitosaawbzytgyznx/auth/providers) [authentication method in the dashboard](https://supabase.com/dashboard/project/_/auth/providers). We've added new commands to the [Supabase CLI](https://supabase.com/docs/guides/cli) to help with the configuration process:\n\n```bash\n$ supabase sso --help\nManage Single Sign-On (SSO) authentication for projects\n\nUsage:\n  supabase sso [command]\n\nAvailable Commands:\n  add         Add a new SSO identity provider\n  info        Returns the SAML SSO settings required for the identity provider\n  list        List all SSO identity providers for a project\n  remove      Remove an existing SSO identity provider\n  show        Show information about an SSO identity provider\n  update      Update information about an SSO identity provider\n```\n\nOnce you've added a new SSO identity provider to your project, it's as simple as calling the `signInWithSSO()` from the `supabase-js` library:\n\n```tsx\nconst { data } = await supabase.auth.signInWithSSO({ domain: 'acme.corp' })\n\nif (data.url) window.location.href = data.url\n```\n\n### SSO with Row Level Security and multi-tenancy\n\nAs usual, we've engineered this feature around the excellent capabilities of PostgreSQL.\n\nFor example, you can use Row Level Security (RLS) to build multi-tenant applications, simply by using the provider's unique identifier in the user's JWT:\n\n```sql\ncreate policy \"Only allow read-write access to tenants\" on tablename as restrictive to authenticated using (\n  tenant_id = (select auth.jwt() -\u003e 'app_metadata' -\u003e\u003e 'provider')\n);\n```\n\nThe journey to enterprise readiness isn't an end goal, it is a continuous process that demands constant attention and maintenance. With Supabase Auth, your team can offload this engineering burden to us and prioritize the features that matter.\n\n## Server-Side and Mobile Auth\n\nMany developers today are using Supabase to build mobile apps, and server-side rendering is becoming popular (again!). This release will add support for these use cases by introducing the _Proof Key for Code Exchange flow (PKCE)_ authentication flow. This improves security for mobile apps and makes building server-first apps simple. Since this is a major update that touches many of the authentication routes, we will be rolling it out gradually over the next few weeks.\n\n### A brief history of Supabase Auth\n\nWhen we [launched](https://news.ycombinator.com/item?id=24072051) Supabase Auth, our target was JAMstack developers. In these cases, the protocol used between the user's application and Supabase Auth is known as the [Implicit Grant Flow](https://www.rfc-editor.org/rfc/rfc6749#section-4.2):\n\n\u003cImg\n  alt=\"diagram reference\"\n  className=\"!m-0\"\n  src={{\n    light:\n      '/images/blog/launch-week-7/day-4-supabase-auth-sso-pkce/supabase-auth-implicit-grant-flow--light.svg',\n    dark: '/images/blog/launch-week-7/day-4-supabase-auth-sso-pkce/supabase-auth-implicit-grant-flow--dark.svg',\n  }}\n/\u003e\n\nAs developers built more complex apps, they encountered two problems with this authentication flow:\n\n- **Server-Side Email Verification Links**\n  Data provided in a URL fragment is only accessible in a browser environment, not on the server. This is problematic for email verification links that redirect users to a server-side route.\n- **Challenges with Mobile App Authentication**\n  The implicit grant flow raised security concerns for mobile use cases since [malicious apps could potentially obtain the user session](https://www.rfc-editor.org/rfc/rfc7636#section-1).\n\nServer-side auth unlocks a number of benefits. Developers can:\n\n- Set cookies on the same domain as the application.\n- Enable server-side rendering for protected pages.\n- Perform downstream actions after user authentication, such as adding the user to a CRM or sending analytics.\n\n### Introducing PKCE\n\nTo solve these problems, we're introducing support for the _Proof Key for Code Exchange_ flow (PKCE, pronounced “pixy”).\n\nThe PKCE flow introduces a _code verifier_ (a randomly generated secret) and a _code challenge_ (the hash of the _code verifier_). The authorization code is returned as a query parameter so it's accessible on the server. During the PKCE flow:\n\n1. The _code challenge_ is sent to Supabase Auth, which returns an _authorization code._\n2. The client sends the _authorization code_ together with the _code verifier_ to obtain the user's session.\n3. Supabase Auth checks if the _code verifier_ matches the _code challenge_ sent earlier by computing the hash. This renders a malicious attacker's attempt to intercept the authorization code useless, since they need to know the value of the _code verifier_ as well.\n\n\u003cImg\n  alt=\"diagram reference\"\n  className=\"!m-0\"\n  src={{\n    light:\n      '/images/blog/launch-week-7/day-4-supabase-auth-sso-pkce/supabase-auth-pkce-flow--light.svg',\n    dark: '/images/blog/launch-week-7/day-4-supabase-auth-sso-pkce/supabase-auth-pkce-flow--dark.svg',\n  }}\n/\u003e\n\n### Migrating to PKCE on the client\n\nOver the next few weeks, you'll be able to use it with the Supabase libraries. We've already added PKCE to the [JavaScript](https://supabase.com/docs/reference/javascript/installing) client library and our [auth-helpers](https://supabase.com/docs/guides/auth/auth-helpers) library. If you're using `supabase-js` , you can switch to PKCE by initializing your client with the following option:\n\n```tsx\nimport { createClient } from '@supabase/supabase-js'\n\nconst supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {\n  auth: {\n    flowType: 'pkce',\n  },\n})\n```\n\nFor client-side auth, that's all you need to do to switch over. `supabase-js` will handle the generation and storage for the code verifier, as well as exchanging the authorization code for the user's session.\n\n### Migrating to PKCE on the server\n\nServer-side authentication is now a lot easier. Let's look at an example using Next.js.\n\nInstall the `next` version of auth-helpers (lets use the `nextjs` version for this example)\n\n```bash\nnpm install @supabase/auth-helpers-nextjs@next\n```\n\nThen prepare an endpoint for the sign in process. The redirect URL is set to `/api/auth/callback`, which will be implemented next.\n\n```tsx\n// api/auth/login\nimport { NextApiRequest, NextApiResponse } from 'next'\nimport { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs'\n\nexport default async function handler(req: NextApiRequest, res: NextApiResponse) {\n  // Create the Supabase Client\n  const supabase = createServerSupabaseClient(\n    { req, res },\n    {\n      supabaseUrl: process.env.SUPABASE_URL,\n      supabaseKey: process.env.SUPABASE_ANON_KEY,\n    }\n  )\n\n  // Start sign in with one-time password\n  const { error } = await supabase.auth.signInWithOtp({\n    email: '<EMAIL>',\n    options: {\n      emailRedirectTo: 'http://localhost:3000/api/auth/callback',\n    },\n  })\n\n  if (error) {\n    res.json(JSON.stringify(error))\n  }\n\n  res.redirect('/')\n}\n```\n\nNow we can set up the callback API endpoint:\n\n```tsx\n// api/auth/callback\nimport { NextApiRequest, NextApiResponse } from 'next'\nimport { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs'\n\nexport default async function handler(req: NextApiRequest, res: NextApiResponse) {\n  // Create authenticated Supabase Client\n  const supabase = createServerSupabaseClient(\n    { req, res },\n    {\n      supabaseUrl: SUPABASE_URL,\n      supabaseKey: SUPABASE_ANON_KEY,\n    }\n  )\n  // check for code in url querystring\n  const code = req.query.code\n\n  if (typeof code === 'string') {\n    // exchange the auth code for user session\n    await supabase.auth.exchangeCodeForSession(code)\n  }\n\n  // redirect the user to a server-side protected area in your app\n  res.redirect('/')\n}\n```\n\n### Roll out\n\nSince this is a major update that touches many of the authentication routes, we will roll it out gradually over the next few weeks. You will receive a notification in your dashboard when the feature is available for your project. Reach out to us if you want early access to this feature.\n\n**Update**: Server-Side Auth (PKCE) is now available on all projects. Please refer to our [Server Side Auth Guide](https://supabase.com/docs/guides/auth/server-side-rendering) for further details on how to add PKCE to your project.\n\n## Native Apple login on iOS\n\nWhile PKCE support is great, that is not the only news for you mobile app developers out there.\n\nBuilding apps for iOS requires [^1] support for native _Sign in with Apple_. We heard the community's requests for native sign-in. We hope you join our excitement to officially announce support for native _Sign in with Apple_.\n\nYour app's iOS Bundle ID can now be configured in the Apple provider section of your project's dashboard.\n\n![native-apple-login-on-iOS.png](/images/blog/launch-week-7/day-4-supabase-auth-sso-pkce/native-apple-login-on-iOS.png)\n\nThis is the only prerequisite for triggering a native _Sign in with Apple._ With [supabase-flutter](https://pub.dev/packages/supabase_flutter#native-sign-in-with-apple-example), this is as easy as:\n\n```dart\nfinal AuthResponse response = await supabase.auth.signInWithApple();\n```\n\nIt's that easy! No need to set up deep links, no need to pass any parameters.\n\nWe're just starting with Apple login, and soon add support for Google login.\n\n## Wrapping Up\n\nSupabase Auth aims to continue developing auth features that are secure by default yet simple to implement. We use Supabase Auth for our hosted platform and continuously dogfood the latest version on it. If you are interested to migrate to Supabase Auth, you can check out this blog post on how [Parqet migrated 125,000 users from Auth0 to Supabase Auth](https://kevcodez.medium.com/migrating-125-000-users-from-auth0-to-supabase-81c0568de307).\n\n[^1]: [App store review guidelines](https://developer.apple.com/app-store/review/guidelines/#sign-in-with-apple)\n","title":"Supabase Auth: SSO,  Mobile, and Server-side support","description":"Supacharging Supabase Auth with Sign in with Apple on iOS, Single-Sign-On support with SAML 2.0, and PKCE for server-side rendering and mobile auth.","launchweek":"7","categories":["product"],"tags":["launch-week","auth"],"date":"2023-04-13","toc_depth":3,"author":"stojan,joel,kangmingtay","image":"launch-week-7/day-4-supabase-auth-sso-pkce/supabase-auth-sso-pkce-thumb.png","thumb":"launch-week-7/day-4-supabase-auth-sso-pkce/supabase-auth-sso-pkce-og.jpg","content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    ol: \"ol\",\n    li: \"li\",\n    a: \"a\",\n    h2: \"h2\",\n    h3: \"h3\",\n    code: \"code\",\n    em: \"em\",\n    ul: \"ul\",\n    strong: \"strong\",\n    sup: \"sup\",\n    img: \"img\",\n    section: \"section\"\n  }, _provideComponents(), props.components), {Img, CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  if (!Img) _missingMdxReference(\"Img\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Today we're excited to announce a few new features for Supabase Auth:\"\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"/blog/supabase-auth-sso-pkce#single-sign-on-support-using-saml-20\",\n          children: \"Easily add Single Sign-On support to your projects using SAML 2.0\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"/blog/supabase-auth-sso-pkce#server-side-and-mobile-auth\",\n          children: \"Better support for server-side rendering and mobile apps using PKCE\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"/blog/supabase-auth-sso-pkce#native-apple-login-on-ios\",\n          children: \"Native Apple login on iOS\"\n        })\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"single-sign-on-support-using-saml-20\",\n      children: \"Single Sign-On Support using SAML 2.0\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"With Single Sign-On (SSO), your users can login with their company's identity provider (IDP), a critical feature when you're building applications for Enterprises.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Every developer building a B2B application eventually needs the SSO authentication flow to onboard enterprise customers. SSO is a requirement for larger Enterprise customers because it's a standard request in Enterprise Security Policies. Over the past few months, we've been \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/platform/sso\",\n        children: \"dogfooding SSO for our own Enterprise customers\"\n      }), \", and today we're releasing it for you to do the same.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Building SSO into your application isn't necessarily hard, but does come with some complexity. A lot of time can be spent understanding the nuances and details of the protocol - from dissecting the jargon to testing the implementation heavily. It took us months to build it for ourselves. With this release, you will have SSO set up and running in less than an hour so that you can focus on shipping the core features of your product. This feature is available for the \", _jsx(_components.a, {\n        href: \"https://supabase.com/pricing\",\n        children: \"Pro Plan and above\"\n      }), \", starting today. This will also be available on the self-hosted version.\"]\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"video-container\",\n      children: _jsx(\"iframe\", {\n        className: \"w-full\",\n        src: \"https://www.youtube-nocookie.com/embed/hAwJeR6mhB0\",\n        title: \"YouTube video player\",\n        frameborder: \"0\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\",\n        allowfullscreen: true\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"getting-started-with-saml-20\",\n      children: \"Getting Started with SAML 2.0\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To get started, enable the \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard/project/phcnitosaawbzytgyznx/auth/providers\",\n        children: \"“SAML 2.0“\"\n      }), \" \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard/project/_/auth/providers\",\n        children: \"authentication method in the dashboard\"\n      }), \". We've added new commands to the \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/cli\",\n        children: \"Supabase CLI\"\n      }), \" to help with the configuration process:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"$ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"supabase sso \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"--help\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"Manage \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"Single Sign-On\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" (SSO) authentication \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"for\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" projects\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"Usage:\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  supabase \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"sso\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" [command]\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"Available \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"Commands:\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  add         \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"Add a new SSO identity provider\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  info        \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"Returns the SAML SSO settings required for the identity provider\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  list        \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"List all SSO identity providers for a project\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  remove      \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"Remove an existing SSO identity provider\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  show        \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"Show information about an SSO identity provider\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  update      \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"Update information about an SSO identity provider\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Once you've added a new SSO identity provider to your project, it's as simple as calling the \", _jsx(_components.code, {\n        children: \"signInWithSSO()\"\n      }), \" from the \", _jsx(_components.code, {\n        children: \"supabase-js\"\n      }), \" library:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"data\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase.auth.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"signInWithSSO\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({ domain: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'acme.corp'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (data.url) window.location.href \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" data.url\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"tsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"sso-with-row-level-security-and-multi-tenancy\",\n      children: \"SSO with Row Level Security and multi-tenancy\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"As usual, we've engineered this feature around the excellent capabilities of PostgreSQL.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"For example, you can use Row Level Security (RLS) to build multi-tenant applications, simply by using the provider's unique identifier in the user's JWT:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"create policy \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"Only allow read-write access to tenants\\\" \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"on\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" tablename \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"as\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" restrictive \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"to\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" authenticated \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"using\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  tenant_id \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"select \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"auth\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"jwt\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"() \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"-\u003e \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'app_metadata' \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"-\u003e\u003e \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'provider'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \");\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The journey to enterprise readiness isn't an end goal, it is a continuous process that demands constant attention and maintenance. With Supabase Auth, your team can offload this engineering burden to us and prioritize the features that matter.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"server-side-and-mobile-auth\",\n      children: \"Server-Side and Mobile Auth\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Many developers today are using Supabase to build mobile apps, and server-side rendering is becoming popular (again!). This release will add support for these use cases by introducing the \", _jsx(_components.em, {\n        children: \"Proof Key for Code Exchange flow (PKCE)\"\n      }), \" authentication flow. This improves security for mobile apps and makes building server-first apps simple. Since this is a major update that touches many of the authentication routes, we will be rolling it out gradually over the next few weeks.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"a-brief-history-of-supabase-auth\",\n      children: \"A brief history of Supabase Auth\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"When we \", _jsx(_components.a, {\n        href: \"https://news.ycombinator.com/item?id=24072051\",\n        children: \"launched\"\n      }), \" Supabase Auth, our target was JAMstack developers. In these cases, the protocol used between the user's application and Supabase Auth is known as the \", _jsx(_components.a, {\n        href: \"https://www.rfc-editor.org/rfc/rfc6749#section-4.2\",\n        children: \"Implicit Grant Flow\"\n      }), \":\"]\n    }), \"\\n\", _jsx(Img, {\n      alt: \"diagram reference\",\n      className: \"!m-0\",\n      src: {\n        light: '/images/blog/launch-week-7/day-4-supabase-auth-sso-pkce/supabase-auth-implicit-grant-flow--light.svg',\n        dark: '/images/blog/launch-week-7/day-4-supabase-auth-sso-pkce/supabase-auth-implicit-grant-flow--dark.svg'\n      }\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"As developers built more complex apps, they encountered two problems with this authentication flow:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Server-Side Email Verification Links\"\n        }), \"\\nData provided in a URL fragment is only accessible in a browser environment, not on the server. This is problematic for email verification links that redirect users to a server-side route.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Challenges with Mobile App Authentication\"\n        }), \"\\nThe implicit grant flow raised security concerns for mobile use cases since \", _jsx(_components.a, {\n          href: \"https://www.rfc-editor.org/rfc/rfc7636#section-1\",\n          children: \"malicious apps could potentially obtain the user session\"\n        }), \".\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Server-side auth unlocks a number of benefits. Developers can:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Set cookies on the same domain as the application.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Enable server-side rendering for protected pages.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Perform downstream actions after user authentication, such as adding the user to a CRM or sending analytics.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"introducing-pkce\",\n      children: \"Introducing PKCE\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To solve these problems, we're introducing support for the \", _jsx(_components.em, {\n        children: \"Proof Key for Code Exchange\"\n      }), \" flow (PKCE, pronounced “pixy”).\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The PKCE flow introduces a \", _jsx(_components.em, {\n        children: \"code verifier\"\n      }), \" (a randomly generated secret) and a \", _jsx(_components.em, {\n        children: \"code challenge\"\n      }), \" (the hash of the \", _jsx(_components.em, {\n        children: \"code verifier\"\n      }), \"). The authorization code is returned as a query parameter so it's accessible on the server. During the PKCE flow:\"]\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"The \", _jsx(_components.em, {\n          children: \"code challenge\"\n        }), \" is sent to Supabase Auth, which returns an \", _jsx(_components.em, {\n          children: \"authorization code.\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"The client sends the \", _jsx(_components.em, {\n          children: \"authorization code\"\n        }), \" together with the \", _jsx(_components.em, {\n          children: \"code verifier\"\n        }), \" to obtain the user's session.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Supabase Auth checks if the \", _jsx(_components.em, {\n          children: \"code verifier\"\n        }), \" matches the \", _jsx(_components.em, {\n          children: \"code challenge\"\n        }), \" sent earlier by computing the hash. This renders a malicious attacker's attempt to intercept the authorization code useless, since they need to know the value of the \", _jsx(_components.em, {\n          children: \"code verifier\"\n        }), \" as well.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(Img, {\n      alt: \"diagram reference\",\n      className: \"!m-0\",\n      src: {\n        light: '/images/blog/launch-week-7/day-4-supabase-auth-sso-pkce/supabase-auth-pkce-flow--light.svg',\n        dark: '/images/blog/launch-week-7/day-4-supabase-auth-sso-pkce/supabase-auth-pkce-flow--dark.svg'\n      }\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"migrating-to-pkce-on-the-client\",\n      children: \"Migrating to PKCE on the client\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Over the next few weeks, you'll be able to use it with the Supabase libraries. We've already added PKCE to the \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/reference/javascript/installing\",\n        children: \"JavaScript\"\n      }), \" client library and our \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/auth/auth-helpers\",\n        children: \"auth-helpers\"\n      }), \" library. If you're using \", _jsx(_components.code, {\n        children: \"supabase-js\"\n      }), \" , you can switch to PKCE by initializing your client with the following option:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { createClient } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'@supabase/supabase-js'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"supabase \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"createClient\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"SUPABASE_URL\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"SUPABASE_ANON_KEY\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  auth: {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    flowType: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'pkce'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"})\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"tsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"For client-side auth, that's all you need to do to switch over. \", _jsx(_components.code, {\n        children: \"supabase-js\"\n      }), \" will handle the generation and storage for the code verifier, as well as exchanging the authorization code for the user's session.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"migrating-to-pkce-on-the-server\",\n      children: \"Migrating to PKCE on the server\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Server-side authentication is now a lot easier. Let's look at an example using Next.js.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Install the \", _jsx(_components.code, {\n        children: \"next\"\n      }), \" version of auth-helpers (lets use the \", _jsx(_components.code, {\n        children: \"nextjs\"\n      }), \" version for this example)\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"npm \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"install @supabase/auth-helpers-nextjs@next\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Then prepare an endpoint for the sign in process. The redirect URL is set to \", _jsx(_components.code, {\n        children: \"/api/auth/callback\"\n      }), \", which will be implemented next.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// api/auth/login\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { NextApiRequest, NextApiResponse } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'next'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { createServerSupabaseClient } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'@supabase/auth-helpers-nextjs'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export default async function \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"handler\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(req\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" NextApiRequest, res\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" NextApiResponse) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  // Create the Supabase Client\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"supabase \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"createServerSupabaseClient\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    { req, res },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      supabaseUrl: process.env.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"SUPABASE_URL\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      supabaseKey: process.env.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"SUPABASE_ANON_KEY\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  )\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  // Start sign in with one-time password\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"error\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase.auth.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"signInWithOtp\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    email: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'<EMAIL>'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    options: {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      emailRedirectTo: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'http://localhost:3000/api/auth/callback'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (error) {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    res.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"json\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"JSON\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"stringify\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(error))\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  res.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"redirect\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'/'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"tsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Now we can set up the callback API endpoint:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// api/auth/callback\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { NextApiRequest, NextApiResponse } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'next'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { createServerSupabaseClient } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'@supabase/auth-helpers-nextjs'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export default async function \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"handler\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(req\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" NextApiRequest, res\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" NextApiResponse) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  // Create authenticated Supabase Client\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"supabase \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"createServerSupabaseClient\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    { req, res },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      supabaseUrl: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"SUPABASE_URL\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      supabaseKey: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"SUPABASE_ANON_KEY\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  )\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  // check for code in url querystring\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"code \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" req.query.code\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"typeof\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" code \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=== \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'string'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \") {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    // exchange the auth code for user session\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase.auth.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"exchangeCodeForSession\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(code)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  // redirect the user to a server-side protected area in your app\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  res.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"redirect\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'/'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"tsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"roll-out\",\n      children: \"Roll out\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Since this is a major update that touches many of the authentication routes, we will roll it out gradually over the next few weeks. You will receive a notification in your dashboard when the feature is available for your project. Reach out to us if you want early access to this feature.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.strong, {\n        children: \"Update\"\n      }), \": Server-Side Auth (PKCE) is now available on all projects. Please refer to our \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/auth/server-side-rendering\",\n        children: \"Server Side Auth Guide\"\n      }), \" for further details on how to add PKCE to your project.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"native-apple-login-on-ios\",\n      children: \"Native Apple login on iOS\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"While PKCE support is great, that is not the only news for you mobile app developers out there.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Building apps for iOS requires \", _jsx(_components.sup, {\n        children: _jsx(_components.a, {\n          href: \"#user-content-fn-1\",\n          id: \"user-content-fnref-1\",\n          \"data-footnote-ref\": true,\n          \"aria-describedby\": \"footnote-label\",\n          children: \"1\"\n        })\n      }), \" support for native \", _jsx(_components.em, {\n        children: \"Sign in with Apple\"\n      }), \". We heard the community's requests for native sign-in. We hope you join our excitement to officially announce support for native \", _jsx(_components.em, {\n        children: \"Sign in with Apple\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Your app's iOS Bundle ID can now be configured in the Apple provider section of your project's dashboard.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-7/day-4-supabase-auth-sso-pkce/native-apple-login-on-iOS.png\",\n        alt: \"native-apple-login-on-iOS.png\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"This is the only prerequisite for triggering a native \", _jsx(_components.em, {\n        children: \"Sign in with Apple.\"\n      }), \" With \", _jsx(_components.a, {\n        href: \"https://pub.dev/packages/supabase_flutter#native-sign-in-with-apple-example\",\n        children: \"supabase-flutter\"\n      }), \", this is as easy as:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"final \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"AuthResponse\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" response \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase.auth.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"signInWithApple\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"();\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"dart\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"It's that easy! No need to set up deep links, no need to pass any parameters.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We're just starting with Apple login, and soon add support for Google login.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"wrapping-up\",\n      children: \"Wrapping Up\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Supabase Auth aims to continue developing auth features that are secure by default yet simple to implement. We use Supabase Auth for our hosted platform and continuously dogfood the latest version on it. If you are interested to migrate to Supabase Auth, you can check out this blog post on how \", _jsx(_components.a, {\n        href: \"https://kevcodez.medium.com/migrating-125-000-users-from-auth0-to-supabase-81c0568de307\",\n        children: \"Parqet migrated 125,000 users from Auth0 to Supabase Auth\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.section, {\n      \"data-footnotes\": true,\n      className: \"footnotes\",\n      children: [_jsx(_components.h2, {\n        className: \"sr-only\",\n        id: \"footnote-label\",\n        children: \"Footnotes\"\n      }), \"\\n\", _jsxs(_components.ol, {\n        children: [\"\\n\", _jsxs(_components.li, {\n          id: \"user-content-fn-1\",\n          children: [\"\\n\", _jsxs(_components.p, {\n            children: [_jsx(_components.a, {\n              href: \"https://developer.apple.com/app-store/review/guidelines/#sign-in-with-apple\",\n              children: \"App store review guidelines\"\n            }), \" \", _jsx(_components.a, {\n              href: \"#user-content-fnref-1\",\n              \"data-footnote-backref\": true,\n              className: \"data-footnote-backref\",\n              \"aria-label\": \"Back to content\",\n              children: \"↩\"\n            })]\n          }), \"\\n\"]\n        }), \"\\n\"]\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"Single Sign-On Support using SAML 2.0","slug":"single-sign-on-support-using-saml-20","lvl":2,"i":0,"seen":0},{"content":"Getting Started with SAML 2.0","slug":"getting-started-with-saml-20","lvl":3,"i":1,"seen":0},{"content":"SSO with Row Level Security and multi-tenancy","slug":"sso-with-row-level-security-and-multi-tenancy","lvl":3,"i":2,"seen":0},{"content":"Server-Side and Mobile Auth","slug":"server-side-and-mobile-auth","lvl":2,"i":3,"seen":0},{"content":"A brief history of Supabase Auth","slug":"a-brief-history-of-supabase-auth","lvl":3,"i":4,"seen":0},{"content":"Introducing PKCE","slug":"introducing-pkce","lvl":3,"i":5,"seen":0},{"content":"Migrating to PKCE on the client","slug":"migrating-to-pkce-on-the-client","lvl":3,"i":6,"seen":0},{"content":"Migrating to PKCE on the server","slug":"migrating-to-pkce-on-the-server","lvl":3,"i":7,"seen":0},{"content":"Roll out","slug":"roll-out","lvl":3,"i":8,"seen":0},{"content":"Native Apple login on iOS","slug":"native-apple-login-on-ios","lvl":2,"i":9,"seen":0},{"content":"Wrapping Up","slug":"wrapping-up","lvl":2,"i":10,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,2],"level":0},{"type":"inline","content":"Today we're excited to announce a few new features for Supabase Auth:","level":1,"lines":[1,2],"children":[{"type":"text","content":"Today we're excited to announce a few new features for Supabase Auth:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"ordered_list_open","order":1,"lines":[3,7],"level":0},{"type":"list_item_open","lines":[3,4],"level":1},{"type":"paragraph_open","tight":true,"lines":[3,4],"level":2},{"type":"inline","content":"[Easily add Single Sign-On support to your projects using SAML 2.0](/blog/supabase-auth-sso-pkce#single-sign-on-support-using-saml-20)","level":3,"lines":[3,4],"children":[{"type":"link_open","href":"/blog/supabase-auth-sso-pkce#single-sign-on-support-using-saml-20","title":"","level":0},{"type":"text","content":"Easily add Single Sign-On support to your projects using SAML 2.0","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[4,5],"level":1},{"type":"paragraph_open","tight":true,"lines":[4,5],"level":2},{"type":"inline","content":"[Better support for server-side rendering and mobile apps using PKCE](/blog/supabase-auth-sso-pkce#server-side-and-mobile-auth)","level":3,"lines":[4,5],"children":[{"type":"link_open","href":"/blog/supabase-auth-sso-pkce#server-side-and-mobile-auth","title":"","level":0},{"type":"text","content":"Better support for server-side rendering and mobile apps using PKCE","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[5,7],"level":1},{"type":"paragraph_open","tight":true,"lines":[5,6],"level":2},{"type":"inline","content":"[Native Apple login on iOS](/blog/supabase-auth-sso-pkce#native-apple-login-on-ios)","level":3,"lines":[5,6],"children":[{"type":"link_open","href":"/blog/supabase-auth-sso-pkce#native-apple-login-on-ios","title":"","level":0},{"type":"text","content":"Native Apple login on iOS","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"ordered_list_close","level":0},{"type":"heading_open","hLevel":2,"lines":[7,8],"level":0},{"type":"inline","content":"[Single Sign-On Support using SAML 2.0](#single-sign-on-support-using-saml-20)","level":1,"lines":[7,8],"children":[{"type":"text","content":"Single Sign-On Support using SAML 2.0","level":0}],"lvl":2,"i":0,"seen":0,"slug":"single-sign-on-support-using-saml-20"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[9,10],"level":0},{"type":"inline","content":"With Single Sign-On (SSO), your users can login with their company's identity provider (IDP), a critical feature when you're building applications for Enterprises.","level":1,"lines":[9,10],"children":[{"type":"text","content":"With Single Sign-On (SSO), your users can login with their company's identity provider (IDP), a critical feature when you're building applications for Enterprises.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[11,12],"level":0},{"type":"inline","content":"Every developer building a B2B application eventually needs the SSO authentication flow to onboard enterprise customers. SSO is a requirement for larger Enterprise customers because it's a standard request in Enterprise Security Policies. Over the past few months, we've been [dogfooding SSO for our own Enterprise customers](https://supabase.com/docs/guides/platform/sso), and today we're releasing it for you to do the same.","level":1,"lines":[11,12],"children":[{"type":"text","content":"Every developer building a B2B application eventually needs the SSO authentication flow to onboard enterprise customers. SSO is a requirement for larger Enterprise customers because it's a standard request in Enterprise Security Policies. Over the past few months, we've been ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/platform/sso","title":"","level":0},{"type":"text","content":"dogfooding SSO for our own Enterprise customers","level":1},{"type":"link_close","level":0},{"type":"text","content":", and today we're releasing it for you to do the same.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[13,14],"level":0},{"type":"inline","content":"Building SSO into your application isn't necessarily hard, but does come with some complexity. A lot of time can be spent understanding the nuances and details of the protocol - from dissecting the jargon to testing the implementation heavily. It took us months to build it for ourselves. With this release, you will have SSO set up and running in less than an hour so that you can focus on shipping the core features of your product. This feature is available for the [Pro Plan and above](https://supabase.com/pricing), starting today. This will also be available on the self-hosted version.","level":1,"lines":[13,14],"children":[{"type":"text","content":"Building SSO into your application isn't necessarily hard, but does come with some complexity. A lot of time can be spent understanding the nuances and details of the protocol - from dissecting the jargon to testing the implementation heavily. It took us months to build it for ourselves. With this release, you will have SSO set up and running in less than an hour so that you can focus on shipping the core features of your product. This feature is available for the ","level":0},{"type":"link_open","href":"https://supabase.com/pricing","title":"","level":0},{"type":"text","content":"Pro Plan and above","level":1},{"type":"link_close","level":0},{"type":"text","content":", starting today. This will also be available on the self-hosted version.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[15,23],"level":0},{"type":"inline","content":"\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/hAwJeR6mhB0\"\n    title=\"YouTube video player\"\n    frameborder=\"0\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"\n    allowfullscreen","level":1,"lines":[15,23],"children":[{"type":"text","content":"\u003cdiv className=\"video-container\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ciframe","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"w-full\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://www.youtube-nocookie.com/embed/hAwJeR6mhB0\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"title=\"YouTube video player\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"frameborder=\"0\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allowfullscreen","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"blockquote_open","lines":[23,25],"level":0},{"type":"paragraph_open","tight":false,"lines":[23,25],"level":1},{"type":"inline","content":"\u003c/iframe\u003e\n\u003c/div\u003e","level":2,"lines":[23,25],"children":[{"type":"text","content":"\u003c/iframe\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"heading_open","hLevel":3,"lines":[26,27],"level":0},{"type":"inline","content":"[Getting Started with SAML 2.0](#getting-started-with-saml-20)","level":1,"lines":[26,27],"children":[{"type":"text","content":"Getting Started with SAML 2.0","level":0}],"lvl":3,"i":1,"seen":0,"slug":"getting-started-with-saml-20"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[28,29],"level":0},{"type":"inline","content":"To get started, enable the [“SAML 2.0“](https://supabase.com/dashboard/project/phcnitosaawbzytgyznx/auth/providers) [authentication method in the dashboard](https://supabase.com/dashboard/project/_/auth/providers). We've added new commands to the [Supabase CLI](https://supabase.com/docs/guides/cli) to help with the configuration process:","level":1,"lines":[28,29],"children":[{"type":"text","content":"To get started, enable the ","level":0},{"type":"link_open","href":"https://supabase.com/dashboard/project/phcnitosaawbzytgyznx/auth/providers","title":"","level":0},{"type":"text","content":"“SAML 2.0“","level":1},{"type":"link_close","level":0},{"type":"text","content":" ","level":0},{"type":"link_open","href":"https://supabase.com/dashboard/project/_/auth/providers","title":"","level":0},{"type":"text","content":"authentication method in the dashboard","level":1},{"type":"link_close","level":0},{"type":"text","content":". We've added new commands to the ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/cli","title":"","level":0},{"type":"text","content":"Supabase CLI","level":1},{"type":"link_close","level":0},{"type":"text","content":" to help with the configuration process:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"bash","content":"$ supabase sso --help\nManage Single Sign-On (SSO) authentication for projects\n\nUsage:\n  supabase sso [command]\n\nAvailable Commands:\n  add         Add a new SSO identity provider\n  info        Returns the SAML SSO settings required for the identity provider\n  list        List all SSO identity providers for a project\n  remove      Remove an existing SSO identity provider\n  show        Show information about an SSO identity provider\n  update      Update information about an SSO identity provider\n","lines":[30,45],"level":0},{"type":"paragraph_open","tight":false,"lines":[46,47],"level":0},{"type":"inline","content":"Once you've added a new SSO identity provider to your project, it's as simple as calling the `signInWithSSO()` from the `supabase-js` library:","level":1,"lines":[46,47],"children":[{"type":"text","content":"Once you've added a new SSO identity provider to your project, it's as simple as calling the ","level":0},{"type":"code","content":"signInWithSSO()","block":false,"level":0},{"type":"text","content":" from the ","level":0},{"type":"code","content":"supabase-js","block":false,"level":0},{"type":"text","content":" library:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"tsx","content":"const { data } = await supabase.auth.signInWithSSO({ domain: 'acme.corp' })\n\nif (data.url) window.location.href = data.url\n","lines":[48,53],"level":0},{"type":"heading_open","hLevel":3,"lines":[54,55],"level":0},{"type":"inline","content":"[SSO with Row Level Security and multi-tenancy](#sso-with-row-level-security-and-multi-tenancy)","level":1,"lines":[54,55],"children":[{"type":"text","content":"SSO with Row Level Security and multi-tenancy","level":0}],"lvl":3,"i":2,"seen":0,"slug":"sso-with-row-level-security-and-multi-tenancy"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[56,57],"level":0},{"type":"inline","content":"As usual, we've engineered this feature around the excellent capabilities of PostgreSQL.","level":1,"lines":[56,57],"children":[{"type":"text","content":"As usual, we've engineered this feature around the excellent capabilities of PostgreSQL.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[58,59],"level":0},{"type":"inline","content":"For example, you can use Row Level Security (RLS) to build multi-tenant applications, simply by using the provider's unique identifier in the user's JWT:","level":1,"lines":[58,59],"children":[{"type":"text","content":"For example, you can use Row Level Security (RLS) to build multi-tenant applications, simply by using the provider's unique identifier in the user's JWT:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql","content":"create policy \"Only allow read-write access to tenants\" on tablename as restrictive to authenticated using (\n  tenant_id = (select auth.jwt() -\u003e 'app_metadata' -\u003e\u003e 'provider')\n);\n","lines":[60,65],"level":0},{"type":"paragraph_open","tight":false,"lines":[66,67],"level":0},{"type":"inline","content":"The journey to enterprise readiness isn't an end goal, it is a continuous process that demands constant attention and maintenance. With Supabase Auth, your team can offload this engineering burden to us and prioritize the features that matter.","level":1,"lines":[66,67],"children":[{"type":"text","content":"The journey to enterprise readiness isn't an end goal, it is a continuous process that demands constant attention and maintenance. With Supabase Auth, your team can offload this engineering burden to us and prioritize the features that matter.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[68,69],"level":0},{"type":"inline","content":"[Server-Side and Mobile Auth](#server-side-and-mobile-auth)","level":1,"lines":[68,69],"children":[{"type":"text","content":"Server-Side and Mobile Auth","level":0}],"lvl":2,"i":3,"seen":0,"slug":"server-side-and-mobile-auth"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[70,71],"level":0},{"type":"inline","content":"Many developers today are using Supabase to build mobile apps, and server-side rendering is becoming popular (again!). This release will add support for these use cases by introducing the _Proof Key for Code Exchange flow (PKCE)_ authentication flow. This improves security for mobile apps and makes building server-first apps simple. Since this is a major update that touches many of the authentication routes, we will be rolling it out gradually over the next few weeks.","level":1,"lines":[70,71],"children":[{"type":"text","content":"Many developers today are using Supabase to build mobile apps, and server-side rendering is becoming popular (again!). This release will add support for these use cases by introducing the ","level":0},{"type":"em_open","level":0},{"type":"text","content":"Proof Key for Code Exchange flow (PKCE)","level":1},{"type":"em_close","level":0},{"type":"text","content":" authentication flow. This improves security for mobile apps and makes building server-first apps simple. Since this is a major update that touches many of the authentication routes, we will be rolling it out gradually over the next few weeks.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[72,73],"level":0},{"type":"inline","content":"[A brief history of Supabase Auth](#a-brief-history-of-supabase-auth)","level":1,"lines":[72,73],"children":[{"type":"text","content":"A brief history of Supabase Auth","level":0}],"lvl":3,"i":4,"seen":0,"slug":"a-brief-history-of-supabase-auth"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[74,75],"level":0},{"type":"inline","content":"When we [launched](https://news.ycombinator.com/item?id=24072051) Supabase Auth, our target was JAMstack developers. In these cases, the protocol used between the user's application and Supabase Auth is known as the [Implicit Grant Flow](https://www.rfc-editor.org/rfc/rfc6749#section-4.2):","level":1,"lines":[74,75],"children":[{"type":"text","content":"When we ","level":0},{"type":"link_open","href":"https://news.ycombinator.com/item?id=24072051","title":"","level":0},{"type":"text","content":"launched","level":1},{"type":"link_close","level":0},{"type":"text","content":" Supabase Auth, our target was JAMstack developers. In these cases, the protocol used between the user's application and Supabase Auth is known as the ","level":0},{"type":"link_open","href":"https://www.rfc-editor.org/rfc/rfc6749#section-4.2","title":"","level":0},{"type":"text","content":"Implicit Grant Flow","level":1},{"type":"link_close","level":0},{"type":"text","content":":","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[76,85],"level":0},{"type":"inline","content":"\u003cImg\n  alt=\"diagram reference\"\n  className=\"!m-0\"\n  src={{\n    light:\n      '/images/blog/launch-week-7/day-4-supabase-auth-sso-pkce/supabase-auth-implicit-grant-flow--light.svg',\n    dark: '/images/blog/launch-week-7/day-4-supabase-auth-sso-pkce/supabase-auth-implicit-grant-flow--dark.svg',\n  }}\n/\u003e","level":1,"lines":[76,85],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"diagram reference\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"!m-0\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src={{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"light:","level":0},{"type":"softbreak","level":0},{"type":"text","content":"'/images/blog/launch-week-7/day-4-supabase-auth-sso-pkce/supabase-auth-implicit-grant-flow--light.svg',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"dark: '/images/blog/launch-week-7/day-4-supabase-auth-sso-pkce/supabase-auth-implicit-grant-flow--dark.svg',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"}}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[86,87],"level":0},{"type":"inline","content":"As developers built more complex apps, they encountered two problems with this authentication flow:","level":1,"lines":[86,87],"children":[{"type":"text","content":"As developers built more complex apps, they encountered two problems with this authentication flow:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[88,93],"level":0},{"type":"list_item_open","lines":[88,90],"level":1},{"type":"paragraph_open","tight":true,"lines":[88,90],"level":2},{"type":"inline","content":"**Server-Side Email Verification Links**\nData provided in a URL fragment is only accessible in a browser environment, not on the server. This is problematic for email verification links that redirect users to a server-side route.","level":3,"lines":[88,90],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Server-Side Email Verification Links","level":1},{"type":"strong_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Data provided in a URL fragment is only accessible in a browser environment, not on the server. This is problematic for email verification links that redirect users to a server-side route.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[90,93],"level":1},{"type":"paragraph_open","tight":true,"lines":[90,92],"level":2},{"type":"inline","content":"**Challenges with Mobile App Authentication**\nThe implicit grant flow raised security concerns for mobile use cases since [malicious apps could potentially obtain the user session](https://www.rfc-editor.org/rfc/rfc7636#section-1).","level":3,"lines":[90,92],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Challenges with Mobile App Authentication","level":1},{"type":"strong_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"The implicit grant flow raised security concerns for mobile use cases since ","level":0},{"type":"link_open","href":"https://www.rfc-editor.org/rfc/rfc7636#section-1","title":"","level":0},{"type":"text","content":"malicious apps could potentially obtain the user session","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[93,94],"level":0},{"type":"inline","content":"Server-side auth unlocks a number of benefits. Developers can:","level":1,"lines":[93,94],"children":[{"type":"text","content":"Server-side auth unlocks a number of benefits. Developers can:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[95,99],"level":0},{"type":"list_item_open","lines":[95,96],"level":1},{"type":"paragraph_open","tight":true,"lines":[95,96],"level":2},{"type":"inline","content":"Set cookies on the same domain as the application.","level":3,"lines":[95,96],"children":[{"type":"text","content":"Set cookies on the same domain as the application.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[96,97],"level":1},{"type":"paragraph_open","tight":true,"lines":[96,97],"level":2},{"type":"inline","content":"Enable server-side rendering for protected pages.","level":3,"lines":[96,97],"children":[{"type":"text","content":"Enable server-side rendering for protected pages.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[97,99],"level":1},{"type":"paragraph_open","tight":true,"lines":[97,98],"level":2},{"type":"inline","content":"Perform downstream actions after user authentication, such as adding the user to a CRM or sending analytics.","level":3,"lines":[97,98],"children":[{"type":"text","content":"Perform downstream actions after user authentication, such as adding the user to a CRM or sending analytics.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":3,"lines":[99,100],"level":0},{"type":"inline","content":"[Introducing PKCE](#introducing-pkce)","level":1,"lines":[99,100],"children":[{"type":"text","content":"Introducing PKCE","level":0}],"lvl":3,"i":5,"seen":0,"slug":"introducing-pkce"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[101,102],"level":0},{"type":"inline","content":"To solve these problems, we're introducing support for the _Proof Key for Code Exchange_ flow (PKCE, pronounced “pixy”).","level":1,"lines":[101,102],"children":[{"type":"text","content":"To solve these problems, we're introducing support for the ","level":0},{"type":"em_open","level":0},{"type":"text","content":"Proof Key for Code Exchange","level":1},{"type":"em_close","level":0},{"type":"text","content":" flow (PKCE, pronounced “pixy”).","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[103,104],"level":0},{"type":"inline","content":"The PKCE flow introduces a _code verifier_ (a randomly generated secret) and a _code challenge_ (the hash of the _code verifier_). The authorization code is returned as a query parameter so it's accessible on the server. During the PKCE flow:","level":1,"lines":[103,104],"children":[{"type":"text","content":"The PKCE flow introduces a ","level":0},{"type":"em_open","level":0},{"type":"text","content":"code verifier","level":1},{"type":"em_close","level":0},{"type":"text","content":" (a randomly generated secret) and a ","level":0},{"type":"em_open","level":0},{"type":"text","content":"code challenge","level":1},{"type":"em_close","level":0},{"type":"text","content":" (the hash of the ","level":0},{"type":"em_open","level":0},{"type":"text","content":"code verifier","level":1},{"type":"em_close","level":0},{"type":"text","content":"). The authorization code is returned as a query parameter so it's accessible on the server. During the PKCE flow:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"ordered_list_open","order":1,"lines":[105,109],"level":0},{"type":"list_item_open","lines":[105,106],"level":1},{"type":"paragraph_open","tight":true,"lines":[105,106],"level":2},{"type":"inline","content":"The _code challenge_ is sent to Supabase Auth, which returns an _authorization code._","level":3,"lines":[105,106],"children":[{"type":"text","content":"The ","level":0},{"type":"em_open","level":0},{"type":"text","content":"code challenge","level":1},{"type":"em_close","level":0},{"type":"text","content":" is sent to Supabase Auth, which returns an ","level":0},{"type":"em_open","level":0},{"type":"text","content":"authorization code.","level":1},{"type":"em_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[106,107],"level":1},{"type":"paragraph_open","tight":true,"lines":[106,107],"level":2},{"type":"inline","content":"The client sends the _authorization code_ together with the _code verifier_ to obtain the user's session.","level":3,"lines":[106,107],"children":[{"type":"text","content":"The client sends the ","level":0},{"type":"em_open","level":0},{"type":"text","content":"authorization code","level":1},{"type":"em_close","level":0},{"type":"text","content":" together with the ","level":0},{"type":"em_open","level":0},{"type":"text","content":"code verifier","level":1},{"type":"em_close","level":0},{"type":"text","content":" to obtain the user's session.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[107,109],"level":1},{"type":"paragraph_open","tight":true,"lines":[107,108],"level":2},{"type":"inline","content":"Supabase Auth checks if the _code verifier_ matches the _code challenge_ sent earlier by computing the hash. This renders a malicious attacker's attempt to intercept the authorization code useless, since they need to know the value of the _code verifier_ as well.","level":3,"lines":[107,108],"children":[{"type":"text","content":"Supabase Auth checks if the ","level":0},{"type":"em_open","level":0},{"type":"text","content":"code verifier","level":1},{"type":"em_close","level":0},{"type":"text","content":" matches the ","level":0},{"type":"em_open","level":0},{"type":"text","content":"code challenge","level":1},{"type":"em_close","level":0},{"type":"text","content":" sent earlier by computing the hash. This renders a malicious attacker's attempt to intercept the authorization code useless, since they need to know the value of the ","level":0},{"type":"em_open","level":0},{"type":"text","content":"code verifier","level":1},{"type":"em_close","level":0},{"type":"text","content":" as well.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"ordered_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[109,118],"level":0},{"type":"inline","content":"\u003cImg\n  alt=\"diagram reference\"\n  className=\"!m-0\"\n  src={{\n    light:\n      '/images/blog/launch-week-7/day-4-supabase-auth-sso-pkce/supabase-auth-pkce-flow--light.svg',\n    dark: '/images/blog/launch-week-7/day-4-supabase-auth-sso-pkce/supabase-auth-pkce-flow--dark.svg',\n  }}\n/\u003e","level":1,"lines":[109,118],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"diagram reference\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"!m-0\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src={{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"light:","level":0},{"type":"softbreak","level":0},{"type":"text","content":"'/images/blog/launch-week-7/day-4-supabase-auth-sso-pkce/supabase-auth-pkce-flow--light.svg',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"dark: '/images/blog/launch-week-7/day-4-supabase-auth-sso-pkce/supabase-auth-pkce-flow--dark.svg',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"}}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[119,120],"level":0},{"type":"inline","content":"[Migrating to PKCE on the client](#migrating-to-pkce-on-the-client)","level":1,"lines":[119,120],"children":[{"type":"text","content":"Migrating to PKCE on the client","level":0}],"lvl":3,"i":6,"seen":0,"slug":"migrating-to-pkce-on-the-client"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[121,122],"level":0},{"type":"inline","content":"Over the next few weeks, you'll be able to use it with the Supabase libraries. We've already added PKCE to the [JavaScript](https://supabase.com/docs/reference/javascript/installing) client library and our [auth-helpers](https://supabase.com/docs/guides/auth/auth-helpers) library. If you're using `supabase-js` , you can switch to PKCE by initializing your client with the following option:","level":1,"lines":[121,122],"children":[{"type":"text","content":"Over the next few weeks, you'll be able to use it with the Supabase libraries. We've already added PKCE to the ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/javascript/installing","title":"","level":0},{"type":"text","content":"JavaScript","level":1},{"type":"link_close","level":0},{"type":"text","content":" client library and our ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/auth/auth-helpers","title":"","level":0},{"type":"text","content":"auth-helpers","level":1},{"type":"link_close","level":0},{"type":"text","content":" library. If you're using ","level":0},{"type":"code","content":"supabase-js","block":false,"level":0},{"type":"text","content":" , you can switch to PKCE by initializing your client with the following option:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"tsx","content":"import { createClient } from '@supabase/supabase-js'\n\nconst supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {\n  auth: {\n    flowType: 'pkce',\n  },\n})\n","lines":[123,132],"level":0},{"type":"paragraph_open","tight":false,"lines":[133,134],"level":0},{"type":"inline","content":"For client-side auth, that's all you need to do to switch over. `supabase-js` will handle the generation and storage for the code verifier, as well as exchanging the authorization code for the user's session.","level":1,"lines":[133,134],"children":[{"type":"text","content":"For client-side auth, that's all you need to do to switch over. ","level":0},{"type":"code","content":"supabase-js","block":false,"level":0},{"type":"text","content":" will handle the generation and storage for the code verifier, as well as exchanging the authorization code for the user's session.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[135,136],"level":0},{"type":"inline","content":"[Migrating to PKCE on the server](#migrating-to-pkce-on-the-server)","level":1,"lines":[135,136],"children":[{"type":"text","content":"Migrating to PKCE on the server","level":0}],"lvl":3,"i":7,"seen":0,"slug":"migrating-to-pkce-on-the-server"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[137,138],"level":0},{"type":"inline","content":"Server-side authentication is now a lot easier. Let's look at an example using Next.js.","level":1,"lines":[137,138],"children":[{"type":"text","content":"Server-side authentication is now a lot easier. Let's look at an example using Next.js.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[139,140],"level":0},{"type":"inline","content":"Install the `next` version of auth-helpers (lets use the `nextjs` version for this example)","level":1,"lines":[139,140],"children":[{"type":"text","content":"Install the ","level":0},{"type":"code","content":"next","block":false,"level":0},{"type":"text","content":" version of auth-helpers (lets use the ","level":0},{"type":"code","content":"nextjs","block":false,"level":0},{"type":"text","content":" version for this example)","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"bash","content":"npm install @supabase/auth-helpers-nextjs@next\n","lines":[141,144],"level":0},{"type":"paragraph_open","tight":false,"lines":[145,146],"level":0},{"type":"inline","content":"Then prepare an endpoint for the sign in process. The redirect URL is set to `/api/auth/callback`, which will be implemented next.","level":1,"lines":[145,146],"children":[{"type":"text","content":"Then prepare an endpoint for the sign in process. The redirect URL is set to ","level":0},{"type":"code","content":"/api/auth/callback","block":false,"level":0},{"type":"text","content":", which will be implemented next.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"tsx","content":"// api/auth/login\nimport { NextApiRequest, NextApiResponse } from 'next'\nimport { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs'\n\nexport default async function handler(req: NextApiRequest, res: NextApiResponse) {\n  // Create the Supabase Client\n  const supabase = createServerSupabaseClient(\n    { req, res },\n    {\n      supabaseUrl: process.env.SUPABASE_URL,\n      supabaseKey: process.env.SUPABASE_ANON_KEY,\n    }\n  )\n\n  // Start sign in with one-time password\n  const { error } = await supabase.auth.signInWithOtp({\n    email: '<EMAIL>',\n    options: {\n      emailRedirectTo: 'http://localhost:3000/api/auth/callback',\n    },\n  })\n\n  if (error) {\n    res.json(JSON.stringify(error))\n  }\n\n  res.redirect('/')\n}\n","lines":[147,177],"level":0},{"type":"paragraph_open","tight":false,"lines":[178,179],"level":0},{"type":"inline","content":"Now we can set up the callback API endpoint:","level":1,"lines":[178,179],"children":[{"type":"text","content":"Now we can set up the callback API endpoint:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"tsx","content":"// api/auth/callback\nimport { NextApiRequest, NextApiResponse } from 'next'\nimport { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs'\n\nexport default async function handler(req: NextApiRequest, res: NextApiResponse) {\n  // Create authenticated Supabase Client\n  const supabase = createServerSupabaseClient(\n    { req, res },\n    {\n      supabaseUrl: SUPABASE_URL,\n      supabaseKey: SUPABASE_ANON_KEY,\n    }\n  )\n  // check for code in url querystring\n  const code = req.query.code\n\n  if (typeof code === 'string') {\n    // exchange the auth code for user session\n    await supabase.auth.exchangeCodeForSession(code)\n  }\n\n  // redirect the user to a server-side protected area in your app\n  res.redirect('/')\n}\n","lines":[180,206],"level":0},{"type":"heading_open","hLevel":3,"lines":[207,208],"level":0},{"type":"inline","content":"[Roll out](#roll-out)","level":1,"lines":[207,208],"children":[{"type":"text","content":"Roll out","level":0}],"lvl":3,"i":8,"seen":0,"slug":"roll-out"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[209,210],"level":0},{"type":"inline","content":"Since this is a major update that touches many of the authentication routes, we will roll it out gradually over the next few weeks. You will receive a notification in your dashboard when the feature is available for your project. Reach out to us if you want early access to this feature.","level":1,"lines":[209,210],"children":[{"type":"text","content":"Since this is a major update that touches many of the authentication routes, we will roll it out gradually over the next few weeks. You will receive a notification in your dashboard when the feature is available for your project. Reach out to us if you want early access to this feature.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[211,212],"level":0},{"type":"inline","content":"**Update**: Server-Side Auth (PKCE) is now available on all projects. Please refer to our [Server Side Auth Guide](https://supabase.com/docs/guides/auth/server-side-rendering) for further details on how to add PKCE to your project.","level":1,"lines":[211,212],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Update","level":1},{"type":"strong_close","level":0},{"type":"text","content":": Server-Side Auth (PKCE) is now available on all projects. Please refer to our ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/auth/server-side-rendering","title":"","level":0},{"type":"text","content":"Server Side Auth Guide","level":1},{"type":"link_close","level":0},{"type":"text","content":" for further details on how to add PKCE to your project.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[213,214],"level":0},{"type":"inline","content":"[Native Apple login on iOS](#native-apple-login-on-ios)","level":1,"lines":[213,214],"children":[{"type":"text","content":"Native Apple login on iOS","level":0}],"lvl":2,"i":9,"seen":0,"slug":"native-apple-login-on-ios"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[215,216],"level":0},{"type":"inline","content":"While PKCE support is great, that is not the only news for you mobile app developers out there.","level":1,"lines":[215,216],"children":[{"type":"text","content":"While PKCE support is great, that is not the only news for you mobile app developers out there.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[217,218],"level":0},{"type":"inline","content":"Building apps for iOS requires [^1] support for native _Sign in with Apple_. We heard the community's requests for native sign-in. We hope you join our excitement to officially announce support for native _Sign in with Apple_.","level":1,"lines":[217,218],"children":[{"type":"text","content":"Building apps for iOS requires ","level":0},{"type":"footnote_ref","id":0,"subId":0,"level":0},{"type":"text","content":" support for native ","level":0},{"type":"em_open","level":0},{"type":"text","content":"Sign in with Apple","level":1},{"type":"em_close","level":0},{"type":"text","content":". We heard the community's requests for native sign-in. We hope you join our excitement to officially announce support for native ","level":0},{"type":"em_open","level":0},{"type":"text","content":"Sign in with Apple","level":1},{"type":"em_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[219,220],"level":0},{"type":"inline","content":"Your app's iOS Bundle ID can now be configured in the Apple provider section of your project's dashboard.","level":1,"lines":[219,220],"children":[{"type":"text","content":"Your app's iOS Bundle ID can now be configured in the Apple provider section of your project's dashboard.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[221,222],"level":0},{"type":"inline","content":"![native-apple-login-on-iOS.png](/images/blog/launch-week-7/day-4-supabase-auth-sso-pkce/native-apple-login-on-iOS.png)","level":1,"lines":[221,222],"children":[{"type":"image","src":"/images/blog/launch-week-7/day-4-supabase-auth-sso-pkce/native-apple-login-on-iOS.png","title":"","alt":"native-apple-login-on-iOS.png","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[223,224],"level":0},{"type":"inline","content":"This is the only prerequisite for triggering a native _Sign in with Apple._ With [supabase-flutter](https://pub.dev/packages/supabase_flutter#native-sign-in-with-apple-example), this is as easy as:","level":1,"lines":[223,224],"children":[{"type":"text","content":"This is the only prerequisite for triggering a native ","level":0},{"type":"em_open","level":0},{"type":"text","content":"Sign in with Apple.","level":1},{"type":"em_close","level":0},{"type":"text","content":" With ","level":0},{"type":"link_open","href":"https://pub.dev/packages/supabase_flutter#native-sign-in-with-apple-example","title":"","level":0},{"type":"text","content":"supabase-flutter","level":1},{"type":"link_close","level":0},{"type":"text","content":", this is as easy as:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"dart","content":"final AuthResponse response = await supabase.auth.signInWithApple();\n","lines":[225,228],"level":0},{"type":"paragraph_open","tight":false,"lines":[229,230],"level":0},{"type":"inline","content":"It's that easy! No need to set up deep links, no need to pass any parameters.","level":1,"lines":[229,230],"children":[{"type":"text","content":"It's that easy! No need to set up deep links, no need to pass any parameters.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[231,232],"level":0},{"type":"inline","content":"We're just starting with Apple login, and soon add support for Google login.","level":1,"lines":[231,232],"children":[{"type":"text","content":"We're just starting with Apple login, and soon add support for Google login.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[233,234],"level":0},{"type":"inline","content":"[Wrapping Up](#wrapping-up)","level":1,"lines":[233,234],"children":[{"type":"text","content":"Wrapping Up","level":0}],"lvl":2,"i":10,"seen":0,"slug":"wrapping-up"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[235,236],"level":0},{"type":"inline","content":"Supabase Auth aims to continue developing auth features that are secure by default yet simple to implement. We use Supabase Auth for our hosted platform and continuously dogfood the latest version on it. If you are interested to migrate to Supabase Auth, you can check out this blog post on how [Parqet migrated 125,000 users from Auth0 to Supabase Auth](https://kevcodez.medium.com/migrating-125-000-users-from-auth0-to-supabase-81c0568de307).","level":1,"lines":[235,236],"children":[{"type":"text","content":"Supabase Auth aims to continue developing auth features that are secure by default yet simple to implement. We use Supabase Auth for our hosted platform and continuously dogfood the latest version on it. If you are interested to migrate to Supabase Auth, you can check out this blog post on how ","level":0},{"type":"link_open","href":"https://kevcodez.medium.com/migrating-125-000-users-from-auth0-to-supabase-81c0568de307","title":"","level":0},{"type":"text","content":"Parqet migrated 125,000 users from Auth0 to Supabase Auth","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"footnote_block_open","level":0},{"type":"footnote_open","id":0,"level":1},{"type":"paragraph_open","tight":false,"lines":[237,238],"level":1},{"type":"inline","content":"[App store review guidelines](https://developer.apple.com/app-store/review/guidelines/#sign-in-with-apple)","level":2,"lines":[237,238],"children":[{"type":"link_open","href":"https://developer.apple.com/app-store/review/guidelines/#sign-in-with-apple","title":"","level":0},{"type":"text","content":"App store review guidelines","level":1},{"type":"link_close","level":0}]},{"type":"footnote_anchor","id":0,"subId":0,"level":2},{"type":"paragraph_close","tight":false,"level":1},{"type":"footnote_close","level":1},{"type":"footnote_block_close","level":0}],"content":"- [Single Sign-On Support using SAML 2.0](#single-sign-on-support-using-saml-20)\n  * [Getting Started with SAML 2.0](#getting-started-with-saml-20)\n  * [SSO with Row Level Security and multi-tenancy](#sso-with-row-level-security-and-multi-tenancy)\n- [Server-Side and Mobile Auth](#server-side-and-mobile-auth)\n  * [A brief history of Supabase Auth](#a-brief-history-of-supabase-auth)\n  * [Introducing PKCE](#introducing-pkce)\n  * [Migrating to PKCE on the client](#migrating-to-pkce-on-the-client)\n  * [Migrating to PKCE on the server](#migrating-to-pkce-on-the-server)\n  * [Roll out](#roll-out)\n- [Native Apple login on iOS](#native-apple-login-on-ios)\n- [Wrapping Up](#wrapping-up)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-auth-sso-pkce"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>