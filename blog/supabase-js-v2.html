<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">supabase-js v2</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="A look at supabase-js v2, which brings type support and focuses on quality-of-life improvements for developers." data-next-head=""/><meta property="og:title" content="supabase-js v2" data-next-head=""/><meta property="og:description" content="A look at supabase-js v2, which brings type support and focuses on quality-of-life improvements for developers." data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-js-v2" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2022-08-16" data-next-head=""/><meta property="article:author" content="https://twitter.com/everConfusedGuy" data-next-head=""/><meta property="article:author" content="https://github.com/alaister" data-next-head=""/><meta property="article:tag" content="launch-week" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/lw5-supabase-js/thumb.jpg" data-next-head=""/><meta property="og:image:alt" content="supabase-js v2 thumbnail" data-next-head=""/><meta property="og:video" content="https://www.youtube.com/v/iqZlPtl_b-I" data-next-head=""/><meta property="og:video:type" content="application/x-shockwave-flash" data-next-head=""/><meta property="og:video:width" content="640" data-next-head=""/><meta property="og:video:height" content="385" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">supabase-js v2</h1><div class="text-light flex space-x-3 text-sm"><p>16 Aug 2022</p><p>•</p><p>8 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://twitter.com/everConfusedGuy"><div class="flex items-center gap-3"><div class="w-10"><img alt="Inian Parameshwaran avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Favatars.githubusercontent.com%2Fu%2F2155545%3Fv%3D4&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Favatars.githubusercontent.com%2Fu%2F2155545%3Fv%3D4&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Favatars.githubusercontent.com%2Fu%2F2155545%3Fv%3D4&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Inian Parameshwaran</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/alaister"><div class="flex items-center gap-3"><div class="w-10"><img alt="Alaister Young avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Falaister.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Falaister.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Falaister.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Alaister Young</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="supabase-js v2" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-supabase-js%2Fthumb.jpg&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-supabase-js%2Fthumb.jpg&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-supabase-js%2Fthumb.jpg&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-supabase-js%2Fthumb.jpg&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-supabase-js%2Fthumb.jpg&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-supabase-js%2Fthumb.jpg&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-supabase-js%2Fthumb.jpg&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-supabase-js%2Fthumb.jpg&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-supabase-js%2Fthumb.jpg&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-supabase-js%2Fthumb.jpg&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-supabase-js%2Fthumb.jpg&amp;w=3840&amp;q=100"/></div><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<div class="bg-gray-300 rounded-lg p-6 italic"><p><br/> ⚠️ UPDATED 20/10: supabase-js v2 is fully released 🥳<br/></p><p><a href="https://supabase.com/docs/reference/javascript">Check the updated docs</a> and <a href="https://supabase.com/docs/reference/javascript/v1/upgrade-guide">migration guide</a>.</p></div>
<p>Today we&#x27;re publishing a release candidate for <a href="https://github.com/supabase/supabase-js">supabase-js v2</a>, which focuses on “quality-of-life” improvements for developers.</p>
<p>Try it out by running <code class="short-inline-codeblock">npm i @supabase/supabase-js@rc</code></p>
<p>Nearly 2 years ago we <a href="https://supabase.com/blog/improved-dx">released supabase-js v1</a>.
Since then it has been used in over <a href="https://github.com/supabase/supabase-js/network/dependents?package_id=UGFja2FnZS04MjM3OTUyMDU%3D">17K repositories</a> and has grown
to <a href="https://www.npmjs.com/package/@supabase/supabase-js">50K weekly downloads</a>. Supabase users give a lot of great feedback and we&#x27;ve
learned some of the largest pain-points as a result.</p>
<h2 id="major-updates" class="group scroll-mt-24">Major Updates<a href="#major-updates" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>This release focuses on solving these pain-points. At the same time we want to make the upgrade path for supabase-js as easy as possible,
so we&#x27;ve been strategic about the changes we&#x27;re making. We plan to follow this model going forward: incremental changes over huge rewrites.</p>
<h3 id="type-support" class="group scroll-mt-24">Type support<a href="#type-support" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>If you followed yesterday&#x27;s announcement, you will have noticed that we added
<a href="https://supabase.com/docs/reference/cli/usage#supabase-gen-types">type generators</a> to the CLI.</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>supabase start</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>supabase gen types typescript --local &gt; DatabaseDefinitions.ts</span></div></div><br/></code></div></div>
<p>These types can now be used to enrich your development experience:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>import type { Database } from &#x27;./DatabaseDefinitions&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>const supabase = createClient&lt;Database&gt;(SUPABASE_URL, ANON_KEY)</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>const { data } = await supabase.from(&#x27;messages&#x27;).select().match({ id: 1 })</span></div></div><br/></code></div></div>
<details><summary>ℹ️ Differences from v1</summary><p>v1 also supported types, but the types were generated from the API rather than the database, so it
lost a lot of detailed information. The library also required you to specify the definition in
every method call, rather than at the client level.</p><div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>supabase.from&lt;Database[&#x27;Message&#x27;]&gt;(&#x27;messages&#x27;).select(&#x27;*&#x27;)</span></div></div><br/></code></div></div></details>
<hr/>
<h3 id="new-auth-methods" class="group scroll-mt-24">New Auth methods<a href="#new-auth-methods" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We&#x27;re removing the <code class="short-inline-codeblock">signIn()</code> method in favor of more explicit method signatures:
<code class="short-inline-codeblock">signInWithPassword()</code>, and <code class="short-inline-codeblock">signInWithOtp()</code>.</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>// v2</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>const { data } = await supabase.auth.signInWithPassword({</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>  email: &#x27;hello@example&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>  password: &#x27;pass&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>})</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>// v1</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>const { data } = await supabase.auth.signIn({</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>  email: &#x27;hello@example&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>  password: &#x27;pass&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>})</span></div></div><br/></code></div></div>
<p>This helps with type hinting. Previously it was difficult for developers to know what they were missing.
A lot of developers didn&#x27;t even realize they could use passwordless magic links.</p>
<hr/>
<h3 id="data-methods-return-minimal-by-default" class="group scroll-mt-24">Data methods return minimal by default<a href="#data-methods-return-minimal-by-default" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>The <code class="short-inline-codeblock">insert()</code>, <code class="short-inline-codeblock">update()</code>, and <code class="short-inline-codeblock">upsert()</code> methods now require you to explicitly append <code class="short-inline-codeblock">select()</code> if you want the data to be returned.</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>// v2</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>const { data } = await supabase.from(&#x27;messages&#x27;).insert({ id: 1, message: &#x27;Hello world&#x27; }).select() // select is now explicitly required</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>// v1</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>const { data } = await supabase.from(&#x27;messages&#x27;).insert({ id: 1, message: &#x27;Hello world&#x27; }) // insert would also &quot;select()&quot;</span></div></div><br/></code></div></div>
<p>This was another common question in our GitHub Discussions. While the idea of automatically returning data is great,
developers often turn on Row Level Security (which is great), and then they forget to add a <code class="short-inline-codeblock">select</code> Policy.
It is a bit surprising that you need to add a <code class="short-inline-codeblock">select</code> policy to do an <code class="short-inline-codeblock">insert</code>, so we opted for the “principle of least surprise”.
If you don&#x27;t append <code class="short-inline-codeblock">select()</code>, the <code class="short-inline-codeblock">data</code> value will be an empty object: <code class="short-inline-codeblock">{}</code>.</p>
<details><summary>ℹ️ Differences from v1</summary><p>Previously you could pass a <code class="short-inline-codeblock">returning: &#x27;minimal&#x27;</code> option to the <code class="short-inline-codeblock">insert()</code>, <code class="short-inline-codeblock">update()</code>, and
<code class="short-inline-codeblock">upsert()</code> statements. We&#x27;ve now made this the default behaviour.</p></details>
<hr/>
<h3 id="auth-admin-methods" class="group scroll-mt-24">Auth Admin methods<a href="#auth-admin-methods" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We&#x27;ve move all server-side Auth methods from <code class="short-inline-codeblock">supabase.auth.api</code> to <code class="short-inline-codeblock">supabase.auth.admin</code>:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>// v2</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>const { data: user, error } = await supabase.auth.admin.listUsers()</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>// v1</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>const { data: user, error } = await supabase.auth.api.listUsers()</span></div></div><br/></code></div></div>
<p>All <code class="short-inline-codeblock">admin</code> methods expect a <code class="short-inline-codeblock">SERVICE_ROLE</code> key.
This change makes it clear that any methods under the <code class="short-inline-codeblock">admin</code> namespace should only be used on a trusted server-side environment.</p>
<hr/>
<h3 id="async-auth-overhaul" class="group scroll-mt-24">Async Auth overhaul<a href="#async-auth-overhaul" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We&#x27;ve rebuilt the Auth library, making it async for almost all methods.</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>// v2</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>const { data } = await supabase.auth.getSession()</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>// v1</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>const { data } = supabase.auth.session()</span></div></div><br/></code></div></div>
<p>This solves the “getting logged out” issue, which has been a recurring challenge in our GitHub Discussions.</p>
<details><summary>ℹ️ Improvements from v1</summary><p>The previous implementation had a race condition when refreshing the auth token across multiple tabs. The async re-write forces the library to wait
for a valid/invalid session before taking any action.</p></details>
<hr/>
<h3 id="scoped-constructor-config" class="group scroll-mt-24">Scoped constructor config<a href="#scoped-constructor-config" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We&#x27;re being much more explicit about the modular approach that <code class="short-inline-codeblock">supabase-js</code> uses:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>const supabase = createClient(apiURL, apiKey, {</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>  db: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>    schema: &#x27;public&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>  },</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>  auth: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>    autoRefreshToken: true,</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>    persistSession: true,</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>  },</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>  realtime: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>    channels,</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>    endpoint,</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>  },</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>  // common across all libraries</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>  global: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>    fetch: customFetch,</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>    headers: DEFAULT_HEADERS,</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>  },</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>})</span></div></div><br/></code></div></div>
<p>This is clearer for developers - if you&#x27;re only using some parts of Supabase, you only receive the hints for those parts.</p>
<details><summary>ℹ️ Improvements from v1</summary><p>We noticed a lot of confusion for the variable naming between each library.
For example, Auth has a config parameter called &quot;storageKey&quot;, which was was often confused with the <code class="short-inline-codeblock">storage-js</code> library bundled in the <code class="short-inline-codeblock">supabase-js</code> library.</p></details>
<hr/>
<h3 id="better-errors" class="group scroll-mt-24">Better Errors<a href="#better-errors" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We&#x27;ve created error types for all of the sub-libraries in <code class="short-inline-codeblock">supabase-js</code>. Here&#x27;s a example for Edge Functions:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>import { FunctionsHttpError, FunctionsRelayError, FunctionsFetchError } from &#x27;@supabase/supabase-js&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>const { data: user, error } = await supabase.functions.invoke(&#x27;hello&#x27;)</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>if (error instanceof FunctionsHttpError) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>  console.log(&#x27;Function returned an error&#x27;, error.message)</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>} else if (error instanceof FunctionsRelayError) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>  console.log(&#x27;Relay error:&#x27;, error.message)</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>} else if (error instanceof FunctionsFetchError) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>  console.log(&#x27;Fetch error:&#x27;, error.message)</span></div></div><div><span class="ch-code-line-number">_<!-- -->11</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<hr/>
<h3 id="improvements-for-edge-functions" class="group scroll-mt-24">Improvements for Edge Functions<a href="#improvements-for-edge-functions" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p><code class="short-inline-codeblock">supabase-js</code> now automatically detects the content type for the request/response bodies for all Edge Function invocations:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>// v2</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>const { data: user, error } = await supabase.functions.invoke(&#x27;hello&#x27;, {</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  body: { foo: &#x27;bar&#x27; },</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>})</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>// v1</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>const { data: user, error } = await supabase.functions.invoke(&#x27;hello&#x27;, {</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  headers: { &#x27;Content-Type&#x27;: &#x27;application/json&#x27; }</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  body: JSON.stringify({ foo: &#x27;bar&#x27; }),</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>})</span></div></div><br/></code></div></div>
<p>This improvement inspired by a Supabase community member. Thanks <a href="https://github.com/supabase/functions-js/pull/23">@vejja</a>!</p>
<hr/>
<h3 id="multiplayer-sneak-peek" class="group scroll-mt-24">Multiplayer Sneak Peek<a href="#multiplayer-sneak-peek" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>There is a new <code class="short-inline-codeblock">channel()</code> interface which are releasing in v2.
This is a &quot;preparatory&quot; release for our upcoming <a href="https://supabase.com/blog/supabase-realtime-with-multiplayer-features">multiplayer</a> features.</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>supabase</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  .channel(&#x27;any_string_you_want&#x27;)</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  .on(&#x27;presence&#x27;, { event: &#x27;track&#x27; }, (payload) =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    console.log(payload)</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  })</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  .subscribe()</span></div></div><br/></code></div></div>
<p>As part of this change, the old <code class="short-inline-codeblock">from().on().subscribe()</code> method for listening to database changes will be changing:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->23</span><div style="display:inline-block;margin-left:16px"><span>// v2</span></div></div><div><span class="ch-code-line-number">_<!-- -->23</span><div style="display:inline-block;margin-left:16px"><span>supabase</span></div></div><div><span class="ch-code-line-number">_<!-- -->23</span><div style="display:inline-block;margin-left:16px"><span>  .channel(&#x27;any_string_you_want&#x27;)</span></div></div><div><span class="ch-code-line-number">_<!-- -->23</span><div style="display:inline-block;margin-left:16px"><span>  .on(</span></div></div><div><span class="ch-code-line-number">_<!-- -->23</span><div style="display:inline-block;margin-left:16px"><span>    &#x27;postgres_changes&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->23</span><div style="display:inline-block;margin-left:16px"><span>    {</span></div></div><div><span class="ch-code-line-number">_<!-- -->23</span><div style="display:inline-block;margin-left:16px"><span>      event: &#x27;INSERT&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->23</span><div style="display:inline-block;margin-left:16px"><span>      schema: &#x27;public&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->23</span><div style="display:inline-block;margin-left:16px"><span>      table: &#x27;movies&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->23</span><div style="display:inline-block;margin-left:16px"><span>    },</span></div></div><div><span class="ch-code-line-number">_<!-- -->23</span><div style="display:inline-block;margin-left:16px"><span>    (payload) =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->23</span><div style="display:inline-block;margin-left:16px"><span>      console.log(payload)</span></div></div><div><span class="ch-code-line-number">_<!-- -->23</span><div style="display:inline-block;margin-left:16px"><span>    }</span></div></div><div><span class="ch-code-line-number">_<!-- -->23</span><div style="display:inline-block;margin-left:16px"><span>  )</span></div></div><div><span class="ch-code-line-number">_<!-- -->23</span><div style="display:inline-block;margin-left:16px"><span>  .subscribe()</span></div></div><div><span class="ch-code-line-number">_<!-- -->23</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->23</span><div style="display:inline-block;margin-left:16px"><span>// v1</span></div></div><div><span class="ch-code-line-number">_<!-- -->23</span><div style="display:inline-block;margin-left:16px"><span>supabase</span></div></div><div><span class="ch-code-line-number">_<!-- -->23</span><div style="display:inline-block;margin-left:16px"><span>  .from(&#x27;movies&#x27;)</span></div></div><div><span class="ch-code-line-number">_<!-- -->23</span><div style="display:inline-block;margin-left:16px"><span>  .on(&#x27;INSERT&#x27;, (payload) =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->23</span><div style="display:inline-block;margin-left:16px"><span>    console.log(payload)</span></div></div><div><span class="ch-code-line-number">_<!-- -->23</span><div style="display:inline-block;margin-left:16px"><span>  })</span></div></div><div><span class="ch-code-line-number">_<!-- -->23</span><div style="display:inline-block;margin-left:16px"><span>  .subscribe()</span></div></div><br/></code></div></div>
<p>You can listen to PostgreSQL database changes on any channel you want by subscribing to the <code class="short-inline-codeblock">&#x27;postgres_changes&#x27;</code> event.
For now, we will continue to support <code class="short-inline-codeblock">from().on().subscribe()</code>, but in the future we will deprecate this in favor of the <code class="short-inline-codeblock">channel().on().subscribe()</code> method.</p>
<hr/>
<h2 id="community" class="group scroll-mt-24">Community<a href="#community" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Version 2.0 is the result of the combined work of over 100 contributors to our libraries, and over 450 contributors to our docs and websites.
If you&#x27;re one of those contributors, thank you.</p>
<ul>
<li><a href="https://github.com/supabase/functions-js/graphs/contributors"><code class="short-inline-codeblock">functions-js</code></a> (4)</li>
<li><a href="https://github.com/supabase/gotrue-js/graphs/contributors"><code class="short-inline-codeblock">gotrue-js</code></a> (47)</li>
<li><a href="https://github.com/supabase/postgrest-js/graphs/contributors"><code class="short-inline-codeblock">postgrest-js</code></a> (30)</li>
<li><a href="https://github.com/supabase/realtime-js/graphs/contributors"><code class="short-inline-codeblock">realtime-js</code></a> (16)</li>
<li><a href="https://github.com/supabase/storage-js/graphs/contributors"><code class="short-inline-codeblock">storage-js</code></a> (17)</li>
<li><a href="https://github.com/supabase/supabase-js/graphs/contributors"><code class="short-inline-codeblock">supabase-js</code></a> (39)</li>
</ul>
<p>Special shout outs to: <a href="https://github.com/vejja">@vejja</a>, <a href="https://github.com/pixtron">@pixtron</a>, <a href="https://github.com/pixtron">@bnjmnt4n</a>, and <a href="https://github.com/karlseguin">@karlseguin</a>.</p>
<h2 id="migrating-to-v2" class="group scroll-mt-24">Migrating to v2<a href="#migrating-to-v2" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Update today by running:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>npm i @supabase/supabase-js@2</span></div></div><br/></code></div></div>
<p><a href="https://supabase.com/docs/reference/javascript/v1/upgrade-guide">Migration guide</a></p>
<p>We&#x27;ll continuing merging security fixes to v1, with maintenance patches for the next three months.</p>
<h2 id="announcement-video-and-discussion" class="group scroll-mt-24">Announcement video and discussion<a href="#announcement-video-and-discussion" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<div class="video-container"><iframe class="w-full" src="https://www.youtube-nocookie.com/embed/iqZlPtl_b-I" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"></iframe></div>
<h2 id="supabase-js-v2-resources" class="group scroll-mt-24">supabase-js v2 resources<a href="#supabase-js-v2-resources" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ul>
<li><a href="https://supabase.com/docs/reference/javascript">v2 Documentation</a></li>
<li><a href="https://supabase.com/docs/reference/javascript/v1/upgrade-guide">Migration guide</a></li>
<li><a href="https://supabase.com/docs/guides/with-nextjs">Next.js quickstart guide</a></li>
<li><a href="https://github.com/supabase/supabase/tree/master/examples">Examples</a></li>
</ul></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-js-v2&amp;text=supabase-js%20v2"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-js-v2&amp;text=supabase-js%20v2"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-js-v2&amp;t=supabase-js%20v2"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="supabase-soc2.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Supabase is SOC2 compliant</h4><p class="small">17 August 2022</p></div></div></div></div></a></div><div><a href="supabase-cli-v1-and-admin-api-beta.html"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Supabase CLI v1 and Management API Beta</h4><p class="small">15 August 2022</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/launch-week"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">launch-week</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#major-updates">Major Updates</a>
<ul>
<li><a href="#type-support">Type support</a></li>
<li><a href="#new-auth-methods">New Auth methods</a></li>
<li><a href="#data-methods-return-minimal-by-default">Data methods return minimal by default</a></li>
<li><a href="#auth-admin-methods">Auth Admin methods</a></li>
<li><a href="#async-auth-overhaul">Async Auth overhaul</a></li>
<li><a href="#scoped-constructor-config">Scoped constructor config</a></li>
<li><a href="#better-errors">Better Errors</a></li>
<li><a href="#improvements-for-edge-functions">Improvements for Edge Functions</a></li>
<li><a href="#multiplayer-sneak-peek">Multiplayer Sneak Peek</a></li>
</ul>
</li>
<li><a href="#community">Community</a></li>
<li><a href="#migrating-to-v2">Migrating to v2</a></li>
<li><a href="#announcement-video-and-discussion">Announcement video and discussion</a></li>
<li><a href="#supabase-js-v2-resources">supabase-js v2 resources</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-js-v2&amp;text=supabase-js%20v2"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-js-v2&amp;text=supabase-js%20v2"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-js-v2&amp;t=supabase-js%20v2"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"supabase-soc2","title":"Supabase is SOC2 compliant","description":"Supabase is now SOC2 compliant. Learn how we got here and what it means for our customers.","author":"inian,joel","image":"lw5-soc2/thumb.jpg","thumb":"lw5-soc2/thumb.jpg","categories":["company"],"tags":["launch-week"],"date":"2022-08-17","toc_depth":3,"video":"https://www.youtube.com/v/6bGQotxisoY","formattedDate":"17 August 2022","readingTime":"9 minute read","url":"/blog/supabase-soc2","path":"/blog/supabase-soc2"},"nextPost":{"slug":"supabase-cli-v1-and-admin-api-beta","title":"Supabase CLI v1 and Management API Beta","description":"We are moving Supabase CLI v1 out of beta, and releasing Management API beta.","author":"soedirgo,qiao","image":"lw5-cli/thumbnail.jpg","thumb":"lw5-cli/thumbnail.jpg","categories":["product"],"tags":["launch-week"],"date":"2022-08-15","toc_depth":3,"video":"https://www.youtube.com/v/OpPOaJI_Z28","formattedDate":"15 August 2022","readingTime":"5 minute read","url":"/blog/supabase-cli-v1-and-admin-api-beta","path":"/blog/supabase-cli-v1-and-admin-api-beta"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-js-v2","source":"\n\u003cdiv className=\"bg-gray-300 rounded-lg p-6 italic\"\u003e\n\u003cbr /\u003e ⚠️ UPDATED 20/10: supabase-js v2 is fully released 🥳\u003cbr /\u003e\n\n[Check the updated docs](https://supabase.com/docs/reference/javascript) and [migration guide](https://supabase.com/docs/reference/javascript/v1/upgrade-guide).\n\n\u003c/div\u003e\n\nToday we're publishing a release candidate for [supabase-js v2](https://github.com/supabase/supabase-js), which focuses on “quality-of-life” improvements for developers.\n\nTry it out by running `npm i @supabase/supabase-js@rc`\n\nNearly 2 years ago we [released supabase-js v1](https://supabase.com/blog/improved-dx).\nSince then it has been used in over [17K repositories](https://github.com/supabase/supabase-js/network/dependents?package_id=UGFja2FnZS04MjM3OTUyMDU%3D) and has grown\nto [50K weekly downloads](https://www.npmjs.com/package/@supabase/supabase-js). Supabase users give a lot of great feedback and we've\nlearned some of the largest pain-points as a result.\n\n## Major Updates\n\nThis release focuses on solving these pain-points. At the same time we want to make the upgrade path for supabase-js as easy as possible,\nso we've been strategic about the changes we're making. We plan to follow this model going forward: incremental changes over huge rewrites.\n\n### Type support\n\nIf you followed yesterday's announcement, you will have noticed that we added\n[type generators](https://supabase.com/docs/reference/cli/usage#supabase-gen-types) to the CLI.\n\n```bash\nsupabase start\nsupabase gen types typescript --local \u003e DatabaseDefinitions.ts\n```\n\nThese types can now be used to enrich your development experience:\n\n```tsx\nimport type { Database } from './DatabaseDefinitions'\n\nconst supabase = createClient\u003cDatabase\u003e(SUPABASE_URL, ANON_KEY)\n\nconst { data } = await supabase.from('messages').select().match({ id: 1 })\n```\n\n\u003cdetails\u003e\n\n\u003csummary\u003eℹ️ Differences from v1\u003c/summary\u003e\n\nv1 also supported types, but the types were generated from the API rather than the database, so it\nlost a lot of detailed information. The library also required you to specify the definition in\nevery method call, rather than at the client level.\n\n```\nsupabase.from\u003cDatabase['Message']\u003e('messages').select('*')\n```\n\n\u003c/details\u003e\n\n---\n\n### New Auth methods\n\nWe're removing the `signIn()` method in favor of more explicit method signatures:\n`signInWithPassword()`, and `signInWithOtp()`.\n\n```ts\n// v2\nconst { data } = await supabase.auth.signInWithPassword({\n  email: 'hello@example',\n  password: 'pass',\n})\n\n// v1\nconst { data } = await supabase.auth.signIn({\n  email: 'hello@example',\n  password: 'pass',\n})\n```\n\nThis helps with type hinting. Previously it was difficult for developers to know what they were missing.\nA lot of developers didn't even realize they could use passwordless magic links.\n\n---\n\n### Data methods return minimal by default\n\nThe `insert()`, `update()`, and `upsert()` methods now require you to explicitly append `select()` if you want the data to be returned.\n\n```ts\n// v2\nconst { data } = await supabase.from('messages').insert({ id: 1, message: 'Hello world' }).select() // select is now explicitly required\n\n// v1\nconst { data } = await supabase.from('messages').insert({ id: 1, message: 'Hello world' }) // insert would also \"select()\"\n```\n\nThis was another common question in our GitHub Discussions. While the idea of automatically returning data is great,\ndevelopers often turn on Row Level Security (which is great), and then they forget to add a `select` Policy.\nIt is a bit surprising that you need to add a `select` policy to do an `insert`, so we opted for the “principle of least surprise”.\nIf you don't append `select()`, the `data` value will be an empty object: `{}`.\n\n\u003cdetails\u003e\n\n\u003csummary\u003eℹ️ Differences from v1\u003c/summary\u003e\n\nPreviously you could pass a `returning: 'minimal'` option to the `insert()`, `update()`, and\n`upsert()` statements. We've now made this the default behaviour.\n\n\u003c/details\u003e\n\n---\n\n### Auth Admin methods\n\nWe've move all server-side Auth methods from `supabase.auth.api` to `supabase.auth.admin`:\n\n```ts\n// v2\nconst { data: user, error } = await supabase.auth.admin.listUsers()\n\n// v1\nconst { data: user, error } = await supabase.auth.api.listUsers()\n```\n\nAll `admin` methods expect a `SERVICE_ROLE` key.\nThis change makes it clear that any methods under the `admin` namespace should only be used on a trusted server-side environment.\n\n---\n\n### Async Auth overhaul\n\nWe've rebuilt the Auth library, making it async for almost all methods.\n\n```ts\n// v2\nconst { data } = await supabase.auth.getSession()\n\n// v1\nconst { data } = supabase.auth.session()\n```\n\nThis solves the “getting logged out” issue, which has been a recurring challenge in our GitHub Discussions.\n\n\u003cdetails\u003e\n\n\u003csummary\u003eℹ️ Improvements from v1\u003c/summary\u003e\n\nThe previous implementation had a race condition when refreshing the auth token across multiple tabs. The async re-write forces the library to wait\nfor a valid/invalid session before taking any action.\n\n\u003c/details\u003e\n\n---\n\n### Scoped constructor config\n\nWe're being much more explicit about the modular approach that `supabase-js` uses:\n\n```ts\nconst supabase = createClient(apiURL, apiKey, {\n  db: {\n    schema: 'public',\n  },\n  auth: {\n    autoRefreshToken: true,\n    persistSession: true,\n  },\n  realtime: {\n    channels,\n    endpoint,\n  },\n  // common across all libraries\n  global: {\n    fetch: customFetch,\n    headers: DEFAULT_HEADERS,\n  },\n})\n```\n\nThis is clearer for developers - if you're only using some parts of Supabase, you only receive the hints for those parts.\n\n\u003cdetails\u003e\n\n\u003csummary\u003eℹ️ Improvements from v1\u003c/summary\u003e\n\nWe noticed a lot of confusion for the variable naming between each library.\nFor example, Auth has a config parameter called \"storageKey\", which was was often confused with the `storage-js` library bundled in the `supabase-js` library.\n\n\u003c/details\u003e\n\n---\n\n### Better Errors\n\nWe've created error types for all of the sub-libraries in `supabase-js`. Here's a example for Edge Functions:\n\n```ts\nimport { FunctionsHttpError, FunctionsRelayError, FunctionsFetchError } from '@supabase/supabase-js'\n\nconst { data: user, error } = await supabase.functions.invoke('hello')\n\nif (error instanceof FunctionsHttpError) {\n  console.log('Function returned an error', error.message)\n} else if (error instanceof FunctionsRelayError) {\n  console.log('Relay error:', error.message)\n} else if (error instanceof FunctionsFetchError) {\n  console.log('Fetch error:', error.message)\n}\n```\n\n---\n\n### Improvements for Edge Functions\n\n`supabase-js` now automatically detects the content type for the request/response bodies for all Edge Function invocations:\n\n```ts\n// v2\nconst { data: user, error } = await supabase.functions.invoke('hello', {\n  body: { foo: 'bar' },\n})\n\n// v1\nconst { data: user, error } = await supabase.functions.invoke('hello', {\n  headers: { 'Content-Type': 'application/json' }\n  body: JSON.stringify({ foo: 'bar' }),\n})\n```\n\nThis improvement inspired by a Supabase community member. Thanks [@vejja](https://github.com/supabase/functions-js/pull/23)!\n\n---\n\n### Multiplayer Sneak Peek\n\nThere is a new `channel()` interface which are releasing in v2.\nThis is a \"preparatory\" release for our upcoming [multiplayer](https://supabase.com/blog/supabase-realtime-with-multiplayer-features) features.\n\n```ts\nsupabase\n  .channel('any_string_you_want')\n  .on('presence', { event: 'track' }, (payload) =\u003e {\n    console.log(payload)\n  })\n  .subscribe()\n```\n\nAs part of this change, the old `from().on().subscribe()` method for listening to database changes will be changing:\n\n```ts\n// v2\nsupabase\n  .channel('any_string_you_want')\n  .on(\n    'postgres_changes',\n    {\n      event: 'INSERT',\n      schema: 'public',\n      table: 'movies',\n    },\n    (payload) =\u003e {\n      console.log(payload)\n    }\n  )\n  .subscribe()\n\n// v1\nsupabase\n  .from('movies')\n  .on('INSERT', (payload) =\u003e {\n    console.log(payload)\n  })\n  .subscribe()\n```\n\nYou can listen to PostgreSQL database changes on any channel you want by subscribing to the `'postgres_changes'` event.\nFor now, we will continue to support `from().on().subscribe()`, but in the future we will deprecate this in favor of the `channel().on().subscribe()` method.\n\n---\n\n## Community\n\nVersion 2.0 is the result of the combined work of over 100 contributors to our libraries, and over 450 contributors to our docs and websites.\nIf you're one of those contributors, thank you.\n\n- [`functions-js`](https://github.com/supabase/functions-js/graphs/contributors) (4)\n- [`gotrue-js`](https://github.com/supabase/gotrue-js/graphs/contributors) (47)\n- [`postgrest-js`](https://github.com/supabase/postgrest-js/graphs/contributors) (30)\n- [`realtime-js`](https://github.com/supabase/realtime-js/graphs/contributors) (16)\n- [`storage-js`](https://github.com/supabase/storage-js/graphs/contributors) (17)\n- [`supabase-js`](https://github.com/supabase/supabase-js/graphs/contributors) (39)\n\nSpecial shout outs to: [@vejja](https://github.com/vejja), [@pixtron](https://github.com/pixtron), [@bnjmnt4n](https://github.com/pixtron), and [@karlseguin](https://github.com/karlseguin).\n\n## Migrating to v2\n\nUpdate today by running:\n\n```bash\nnpm i @supabase/supabase-js@2\n```\n\n[Migration guide](https://supabase.com/docs/reference/javascript/v1/upgrade-guide)\n\nWe'll continuing merging security fixes to v1, with maintenance patches for the next three months.\n\n## Announcement video and discussion\n\n\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/iqZlPtl_b-I\"\n    title=\"YouTube video player\"\n    frameborder=\"0\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowfullscreen\n  \u003e\u003c/iframe\u003e\n\u003c/div\u003e\n\n## supabase-js v2 resources\n\n- [v2 Documentation](https://supabase.com/docs/reference/javascript)\n- [Migration guide](https://supabase.com/docs/reference/javascript/v1/upgrade-guide)\n- [Next.js quickstart guide](/docs/guides/with-nextjs)\n- [Examples](https://github.com/supabase/supabase/tree/master/examples)\n","title":"supabase-js v2","description":"A look at supabase-js v2, which brings type support and focuses on quality-of-life improvements for developers.","author":"inian,alaister","image":"lw5-supabase-js/thumb.jpg","thumb":"lw5-supabase-js/thumb.jpg","categories":["product"],"tags":["launch-week"],"date":"2022-08-16","toc_depth":3,"video":"https://www.youtube.com/v/iqZlPtl_b-I","content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    a: \"a\",\n    code: \"code\",\n    h2: \"h2\",\n    h3: \"h3\",\n    hr: \"hr\",\n    ul: \"ul\",\n    li: \"li\"\n  }, _provideComponents(), props.components), {CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsxs(\"div\", {\n      className: \"bg-gray-300 rounded-lg p-6 italic\",\n      children: [_jsxs(_components.p, {\n        children: [_jsx(\"br\", {}), \" ⚠️ UPDATED 20/10: supabase-js v2 is fully released 🥳\", _jsx(\"br\", {})]\n      }), _jsxs(_components.p, {\n        children: [_jsx(_components.a, {\n          href: \"https://supabase.com/docs/reference/javascript\",\n          children: \"Check the updated docs\"\n        }), \" and \", _jsx(_components.a, {\n          href: \"https://supabase.com/docs/reference/javascript/v1/upgrade-guide\",\n          children: \"migration guide\"\n        }), \".\"]\n      })]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Today we're publishing a release candidate for \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/supabase-js\",\n        children: \"supabase-js v2\"\n      }), \", which focuses on “quality-of-life” improvements for developers.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Try it out by running \", _jsx(_components.code, {\n        children: \"npm i @supabase/supabase-js@rc\"\n      })]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Nearly 2 years ago we \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/improved-dx\",\n        children: \"released supabase-js v1\"\n      }), \".\\nSince then it has been used in over \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/supabase-js/network/dependents?package_id=UGFja2FnZS04MjM3OTUyMDU%3D\",\n        children: \"17K repositories\"\n      }), \" and has grown\\nto \", _jsx(_components.a, {\n        href: \"https://www.npmjs.com/package/@supabase/supabase-js\",\n        children: \"50K weekly downloads\"\n      }), \". Supabase users give a lot of great feedback and we've\\nlearned some of the largest pain-points as a result.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"major-updates\",\n      children: \"Major Updates\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This release focuses on solving these pain-points. At the same time we want to make the upgrade path for supabase-js as easy as possible,\\nso we've been strategic about the changes we're making. We plan to follow this model going forward: incremental changes over huge rewrites.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"type-support\",\n      children: \"Type support\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you followed yesterday's announcement, you will have noticed that we added\\n\", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/reference/cli/usage#supabase-gen-types\",\n        children: \"type generators\"\n      }), \" to the CLI.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"supabase \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"start\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"supabase \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"gen types typescript \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"--local \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"DatabaseDefinitions.ts\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"These types can now be used to enrich your development experience:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"import type\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { Database } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'./DatabaseDefinitions'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"supabase \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"createClient\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Database\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"SUPABASE_URL\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"ANON_KEY\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"data\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'messages'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \").\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"select\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"().\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"match\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({ id: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"1\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"tsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(\"details\", {\n      children: [_jsx(\"summary\", {\n        children: \"ℹ️ Differences from v1\"\n      }), _jsx(_components.p, {\n        children: \"v1 also supported types, but the types were generated from the API rather than the database, so it\\nlost a lot of detailed information. The library also required you to specify the definition in\\nevery method call, rather than at the client level.\"\n      }), _jsx(CH.Code, {\n        codeConfig: chCodeConfig,\n        northPanel: {\n          \"tabs\": [\"\"],\n          \"active\": \"\",\n          \"heightRatio\": 1\n        },\n        files: [{\n          \"name\": \"\",\n          \"focus\": \"\",\n          \"code\": {\n            \"lines\": [{\n              \"tokens\": [{\n                \"content\": \"supabase.from\u003cDatabase['Message']\u003e('messages').select('*')\",\n                \"props\": {}\n              }]\n            }],\n            \"lang\": \"text\"\n          },\n          \"annotations\": []\n        }]\n      })]\n    }), \"\\n\", _jsx(_components.hr, {}), \"\\n\", _jsx(_components.h3, {\n      id: \"new-auth-methods\",\n      children: \"New Auth methods\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We're removing the \", _jsx(_components.code, {\n        children: \"signIn()\"\n      }), \" method in favor of more explicit method signatures:\\n\", _jsx(_components.code, {\n        children: \"signInWithPassword()\"\n      }), \", and \", _jsx(_components.code, {\n        children: \"signInWithOtp()\"\n      }), \".\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// v2\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"data\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase.auth.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"signInWithPassword\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  email: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'hello@example'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  password: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'pass'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"})\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"// v1\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"data\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase.auth.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"signIn\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  email: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'hello@example'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  password: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'pass'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"})\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"ts\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This helps with type hinting. Previously it was difficult for developers to know what they were missing.\\nA lot of developers didn't even realize they could use passwordless magic links.\"\n    }), \"\\n\", _jsx(_components.hr, {}), \"\\n\", _jsx(_components.h3, {\n      id: \"data-methods-return-minimal-by-default\",\n      children: \"Data methods return minimal by default\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The \", _jsx(_components.code, {\n        children: \"insert()\"\n      }), \", \", _jsx(_components.code, {\n        children: \"update()\"\n      }), \", and \", _jsx(_components.code, {\n        children: \"upsert()\"\n      }), \" methods now require you to explicitly append \", _jsx(_components.code, {\n        children: \"select()\"\n      }), \" if you want the data to be returned.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// v2\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"data\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'messages'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \").\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"insert\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({ id: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"1\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", message: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'Hello world'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" }).\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"select\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"() \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"// select is now explicitly required\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"// v1\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"data\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'messages'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \").\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"insert\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({ id: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"1\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", message: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'Hello world'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" }) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"// insert would also \\\"select()\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"ts\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"This was another common question in our GitHub Discussions. While the idea of automatically returning data is great,\\ndevelopers often turn on Row Level Security (which is great), and then they forget to add a \", _jsx(_components.code, {\n        children: \"select\"\n      }), \" Policy.\\nIt is a bit surprising that you need to add a \", _jsx(_components.code, {\n        children: \"select\"\n      }), \" policy to do an \", _jsx(_components.code, {\n        children: \"insert\"\n      }), \", so we opted for the “principle of least surprise”.\\nIf you don't append \", _jsx(_components.code, {\n        children: \"select()\"\n      }), \", the \", _jsx(_components.code, {\n        children: \"data\"\n      }), \" value will be an empty object: \", _jsx(_components.code, {\n        children: \"{}\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(\"details\", {\n      children: [_jsx(\"summary\", {\n        children: \"ℹ️ Differences from v1\"\n      }), _jsxs(_components.p, {\n        children: [\"Previously you could pass a \", _jsx(_components.code, {\n          children: \"returning: 'minimal'\"\n        }), \" option to the \", _jsx(_components.code, {\n          children: \"insert()\"\n        }), \", \", _jsx(_components.code, {\n          children: \"update()\"\n        }), \", and\\n\", _jsx(_components.code, {\n          children: \"upsert()\"\n        }), \" statements. We've now made this the default behaviour.\"]\n      })]\n    }), \"\\n\", _jsx(_components.hr, {}), \"\\n\", _jsx(_components.h3, {\n      id: \"auth-admin-methods\",\n      children: \"Auth Admin methods\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We've move all server-side Auth methods from \", _jsx(_components.code, {\n        children: \"supabase.auth.api\"\n      }), \" to \", _jsx(_components.code, {\n        children: \"supabase.auth.admin\"\n      }), \":\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// v2\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"data\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"user\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"error\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase.auth.admin.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"listUsers\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"// v1\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"data\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"user\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"error\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase.auth.api.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"listUsers\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"ts\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"All \", _jsx(_components.code, {\n        children: \"admin\"\n      }), \" methods expect a \", _jsx(_components.code, {\n        children: \"SERVICE_ROLE\"\n      }), \" key.\\nThis change makes it clear that any methods under the \", _jsx(_components.code, {\n        children: \"admin\"\n      }), \" namespace should only be used on a trusted server-side environment.\"]\n    }), \"\\n\", _jsx(_components.hr, {}), \"\\n\", _jsx(_components.h3, {\n      id: \"async-auth-overhaul\",\n      children: \"Async Auth overhaul\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We've rebuilt the Auth library, making it async for almost all methods.\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// v2\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"data\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase.auth.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"getSession\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"// v1\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"data\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase.auth.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"session\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"ts\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This solves the “getting logged out” issue, which has been a recurring challenge in our GitHub Discussions.\"\n    }), \"\\n\", _jsxs(\"details\", {\n      children: [_jsx(\"summary\", {\n        children: \"ℹ️ Improvements from v1\"\n      }), _jsx(_components.p, {\n        children: \"The previous implementation had a race condition when refreshing the auth token across multiple tabs. The async re-write forces the library to wait\\nfor a valid/invalid session before taking any action.\"\n      })]\n    }), \"\\n\", _jsx(_components.hr, {}), \"\\n\", _jsx(_components.h3, {\n      id: \"scoped-constructor-config\",\n      children: \"Scoped constructor config\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We're being much more explicit about the modular approach that \", _jsx(_components.code, {\n        children: \"supabase-js\"\n      }), \" uses:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"supabase \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"createClient\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(apiURL, apiKey, {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  db: {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    schema: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'public'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  auth: {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    autoRefreshToken: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"true\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    persistSession: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"true\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  realtime: {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    channels,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    endpoint,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  // common across all libraries\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  global: {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    fetch: customFetch,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    headers: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"DEFAULT_HEADERS\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"})\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"ts\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This is clearer for developers - if you're only using some parts of Supabase, you only receive the hints for those parts.\"\n    }), \"\\n\", _jsxs(\"details\", {\n      children: [_jsx(\"summary\", {\n        children: \"ℹ️ Improvements from v1\"\n      }), _jsxs(_components.p, {\n        children: [\"We noticed a lot of confusion for the variable naming between each library.\\nFor example, Auth has a config parameter called \\\"storageKey\\\", which was was often confused with the \", _jsx(_components.code, {\n          children: \"storage-js\"\n        }), \" library bundled in the \", _jsx(_components.code, {\n          children: \"supabase-js\"\n        }), \" library.\"]\n      })]\n    }), \"\\n\", _jsx(_components.hr, {}), \"\\n\", _jsx(_components.h3, {\n      id: \"better-errors\",\n      children: \"Better Errors\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We've created error types for all of the sub-libraries in \", _jsx(_components.code, {\n        children: \"supabase-js\"\n      }), \". Here's a example for Edge Functions:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { FunctionsHttpError, FunctionsRelayError, FunctionsFetchError } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'@supabase/supabase-js'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"data\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"user\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"error\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase.functions.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"invoke\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'hello'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (error \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"instanceof \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"FunctionsHttpError\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \") {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  console.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"log\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'Function returned an error'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", error.message)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"} \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"else if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (error \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"instanceof \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"FunctionsRelayError\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \") {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  console.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"log\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'Relay error:'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", error.message)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"} \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"else if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (error \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"instanceof \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"FunctionsFetchError\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \") {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  console.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"log\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'Fetch error:'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", error.message)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"ts\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.hr, {}), \"\\n\", _jsx(_components.h3, {\n      id: \"improvements-for-edge-functions\",\n      children: \"Improvements for Edge Functions\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.code, {\n        children: \"supabase-js\"\n      }), \" now automatically detects the content type for the request/response bodies for all Edge Function invocations:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// v2\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"data\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"user\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"error\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase.functions.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"invoke\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'hello'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  body: { foo: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'bar'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"})\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"// v1\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"data\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"user\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"error\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase.functions.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"invoke\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'hello'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  headers: { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'Content-Type'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'application/json'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  body: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"JSON\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"stringify\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({ foo: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'bar'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" }),\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"})\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"ts\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"This improvement inspired by a Supabase community member. Thanks \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/functions-js/pull/23\",\n        children: \"@vejja\"\n      }), \"!\"]\n    }), \"\\n\", _jsx(_components.hr, {}), \"\\n\", _jsx(_components.h3, {\n      id: \"multiplayer-sneak-peek\",\n      children: \"Multiplayer Sneak Peek\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"There is a new \", _jsx(_components.code, {\n        children: \"channel()\"\n      }), \" interface which are releasing in v2.\\nThis is a \\\"preparatory\\\" release for our upcoming \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/supabase-realtime-with-multiplayer-features\",\n        children: \"multiplayer\"\n      }), \" features.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"supabase\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  .\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"channel\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'any_string_you_want'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  .\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"on\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'presence'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", { event: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'track'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" }, (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"payload\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    console.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"log\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(payload)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  .\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"subscribe\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"ts\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"As part of this change, the old \", _jsx(_components.code, {\n        children: \"from().on().subscribe()\"\n      }), \" method for listening to database changes will be changing:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// v2\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"supabase\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  .\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"channel\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'any_string_you_want'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  .\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"on\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    'postgres_changes'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      event: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'INSERT'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      schema: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'public'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      table: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'movies'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"payload\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      console.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"log\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(payload)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  )\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  .\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"subscribe\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"// v1\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"supabase\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  .\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'movies'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  .\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"on\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'INSERT'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"payload\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    console.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"log\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(payload)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  .\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"subscribe\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"ts\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You can listen to PostgreSQL database changes on any channel you want by subscribing to the \", _jsx(_components.code, {\n        children: \"'postgres_changes'\"\n      }), \" event.\\nFor now, we will continue to support \", _jsx(_components.code, {\n        children: \"from().on().subscribe()\"\n      }), \", but in the future we will deprecate this in favor of the \", _jsx(_components.code, {\n        children: \"channel().on().subscribe()\"\n      }), \" method.\"]\n    }), \"\\n\", _jsx(_components.hr, {}), \"\\n\", _jsx(_components.h2, {\n      id: \"community\",\n      children: \"Community\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Version 2.0 is the result of the combined work of over 100 contributors to our libraries, and over 450 contributors to our docs and websites.\\nIf you're one of those contributors, thank you.\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://github.com/supabase/functions-js/graphs/contributors\",\n          children: _jsx(_components.code, {\n            children: \"functions-js\"\n          })\n        }), \" (4)\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://github.com/supabase/gotrue-js/graphs/contributors\",\n          children: _jsx(_components.code, {\n            children: \"gotrue-js\"\n          })\n        }), \" (47)\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://github.com/supabase/postgrest-js/graphs/contributors\",\n          children: _jsx(_components.code, {\n            children: \"postgrest-js\"\n          })\n        }), \" (30)\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://github.com/supabase/realtime-js/graphs/contributors\",\n          children: _jsx(_components.code, {\n            children: \"realtime-js\"\n          })\n        }), \" (16)\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://github.com/supabase/storage-js/graphs/contributors\",\n          children: _jsx(_components.code, {\n            children: \"storage-js\"\n          })\n        }), \" (17)\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase-js/graphs/contributors\",\n          children: _jsx(_components.code, {\n            children: \"supabase-js\"\n          })\n        }), \" (39)\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Special shout outs to: \", _jsx(_components.a, {\n        href: \"https://github.com/vejja\",\n        children: \"@vejja\"\n      }), \", \", _jsx(_components.a, {\n        href: \"https://github.com/pixtron\",\n        children: \"@pixtron\"\n      }), \", \", _jsx(_components.a, {\n        href: \"https://github.com/pixtron\",\n        children: \"@bnjmnt4n\"\n      }), \", and \", _jsx(_components.a, {\n        href: \"https://github.com/karlseguin\",\n        children: \"@karlseguin\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"migrating-to-v2\",\n      children: \"Migrating to v2\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Update today by running:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"npm \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"i @supabase/supabase-js@\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"2\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://supabase.com/docs/reference/javascript/v1/upgrade-guide\",\n        children: \"Migration guide\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We'll continuing merging security fixes to v1, with maintenance patches for the next three months.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"announcement-video-and-discussion\",\n      children: \"Announcement video and discussion\"\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"video-container\",\n      children: _jsx(\"iframe\", {\n        className: \"w-full\",\n        src: \"https://www.youtube-nocookie.com/embed/iqZlPtl_b-I\",\n        title: \"YouTube video player\",\n        frameborder: \"0\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n        allowfullscreen: true\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"supabase-js-v2-resources\",\n      children: \"supabase-js v2 resources\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/docs/reference/javascript\",\n          children: \"v2 Documentation\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/docs/reference/javascript/v1/upgrade-guide\",\n          children: \"Migration guide\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"/docs/guides/with-nextjs\",\n          children: \"Next.js quickstart guide\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase/tree/master/examples\",\n          children: \"Examples\"\n        })\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"Major Updates","slug":"major-updates","lvl":2,"i":0,"seen":0},{"content":"Type support","slug":"type-support","lvl":3,"i":1,"seen":0},{"content":"New Auth methods","slug":"new-auth-methods","lvl":3,"i":2,"seen":0},{"content":"Data methods return minimal by default","slug":"data-methods-return-minimal-by-default","lvl":3,"i":3,"seen":0},{"content":"Auth Admin methods","slug":"auth-admin-methods","lvl":3,"i":4,"seen":0},{"content":"Async Auth overhaul","slug":"async-auth-overhaul","lvl":3,"i":5,"seen":0},{"content":"Scoped constructor config","slug":"scoped-constructor-config","lvl":3,"i":6,"seen":0},{"content":"Better Errors","slug":"better-errors","lvl":3,"i":7,"seen":0},{"content":"Improvements for Edge Functions","slug":"improvements-for-edge-functions","lvl":3,"i":8,"seen":0},{"content":"Multiplayer Sneak Peek","slug":"multiplayer-sneak-peek","lvl":3,"i":9,"seen":0},{"content":"Community","slug":"community","lvl":2,"i":10,"seen":0},{"content":"Migrating to v2","slug":"migrating-to-v2","lvl":2,"i":11,"seen":0},{"content":"Announcement video and discussion","slug":"announcement-video-and-discussion","lvl":2,"i":12,"seen":0},{"content":"supabase-js v2 resources","slug":"supabase-js-v2-resources","lvl":2,"i":13,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,3],"level":0},{"type":"inline","content":"\u003cdiv className=\"bg-gray-300 rounded-lg p-6 italic\"\u003e\n\u003cbr /\u003e ⚠️ UPDATED 20/10: supabase-js v2 is fully released 🥳\u003cbr /\u003e","level":1,"lines":[1,3],"children":[{"type":"text","content":"\u003cdiv className=\"bg-gray-300 rounded-lg p-6 italic\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cbr /\u003e ⚠️ UPDATED 20/10: supabase-js v2 is fully released 🥳\u003cbr /\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[4,5],"level":0},{"type":"inline","content":"[Check the updated docs](https://supabase.com/docs/reference/javascript) and [migration guide](https://supabase.com/docs/reference/javascript/v1/upgrade-guide).","level":1,"lines":[4,5],"children":[{"type":"link_open","href":"https://supabase.com/docs/reference/javascript","title":"","level":0},{"type":"text","content":"Check the updated docs","level":1},{"type":"link_close","level":0},{"type":"text","content":" and ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/javascript/v1/upgrade-guide","title":"","level":0},{"type":"text","content":"migration guide","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[6,7],"level":0},{"type":"inline","content":"\u003c/div\u003e","level":1,"lines":[6,7],"children":[{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[8,9],"level":0},{"type":"inline","content":"Today we're publishing a release candidate for [supabase-js v2](https://github.com/supabase/supabase-js), which focuses on “quality-of-life” improvements for developers.","level":1,"lines":[8,9],"children":[{"type":"text","content":"Today we're publishing a release candidate for ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase-js","title":"","level":0},{"type":"text","content":"supabase-js v2","level":1},{"type":"link_close","level":0},{"type":"text","content":", which focuses on “quality-of-life” improvements for developers.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[10,11],"level":0},{"type":"inline","content":"Try it out by running `npm i @supabase/supabase-js@rc`","level":1,"lines":[10,11],"children":[{"type":"text","content":"Try it out by running ","level":0},{"type":"code","content":"npm i @supabase/supabase-js@rc","block":false,"level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[12,16],"level":0},{"type":"inline","content":"Nearly 2 years ago we [released supabase-js v1](https://supabase.com/blog/improved-dx).\nSince then it has been used in over [17K repositories](https://github.com/supabase/supabase-js/network/dependents?package_id=UGFja2FnZS04MjM3OTUyMDU%3D) and has grown\nto [50K weekly downloads](https://www.npmjs.com/package/@supabase/supabase-js). Supabase users give a lot of great feedback and we've\nlearned some of the largest pain-points as a result.","level":1,"lines":[12,16],"children":[{"type":"text","content":"Nearly 2 years ago we ","level":0},{"type":"link_open","href":"https://supabase.com/blog/improved-dx","title":"","level":0},{"type":"text","content":"released supabase-js v1","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Since then it has been used in over ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase-js/network/dependents?package_id=UGFja2FnZS04MjM3OTUyMDU%3D","title":"","level":0},{"type":"text","content":"17K repositories","level":1},{"type":"link_close","level":0},{"type":"text","content":" and has grown","level":0},{"type":"softbreak","level":0},{"type":"text","content":"to ","level":0},{"type":"link_open","href":"https://www.npmjs.com/package/@supabase/supabase-js","title":"","level":0},{"type":"text","content":"50K weekly downloads","level":1},{"type":"link_close","level":0},{"type":"text","content":". Supabase users give a lot of great feedback and we've","level":0},{"type":"softbreak","level":0},{"type":"text","content":"learned some of the largest pain-points as a result.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[17,18],"level":0},{"type":"inline","content":"[Major Updates](#major-updates)","level":1,"lines":[17,18],"children":[{"type":"text","content":"Major Updates","level":0}],"lvl":2,"i":0,"seen":0,"slug":"major-updates"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[19,21],"level":0},{"type":"inline","content":"This release focuses on solving these pain-points. At the same time we want to make the upgrade path for supabase-js as easy as possible,\nso we've been strategic about the changes we're making. We plan to follow this model going forward: incremental changes over huge rewrites.","level":1,"lines":[19,21],"children":[{"type":"text","content":"This release focuses on solving these pain-points. At the same time we want to make the upgrade path for supabase-js as easy as possible,","level":0},{"type":"softbreak","level":0},{"type":"text","content":"so we've been strategic about the changes we're making. We plan to follow this model going forward: incremental changes over huge rewrites.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[22,23],"level":0},{"type":"inline","content":"[Type support](#type-support)","level":1,"lines":[22,23],"children":[{"type":"text","content":"Type support","level":0}],"lvl":3,"i":1,"seen":0,"slug":"type-support"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[24,26],"level":0},{"type":"inline","content":"If you followed yesterday's announcement, you will have noticed that we added\n[type generators](https://supabase.com/docs/reference/cli/usage#supabase-gen-types) to the CLI.","level":1,"lines":[24,26],"children":[{"type":"text","content":"If you followed yesterday's announcement, you will have noticed that we added","level":0},{"type":"softbreak","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/cli/usage#supabase-gen-types","title":"","level":0},{"type":"text","content":"type generators","level":1},{"type":"link_close","level":0},{"type":"text","content":" to the CLI.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"bash","content":"supabase start\nsupabase gen types typescript --local \u003e DatabaseDefinitions.ts\n","lines":[27,31],"level":0},{"type":"paragraph_open","tight":false,"lines":[32,33],"level":0},{"type":"inline","content":"These types can now be used to enrich your development experience:","level":1,"lines":[32,33],"children":[{"type":"text","content":"These types can now be used to enrich your development experience:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"tsx","content":"import type { Database } from './DatabaseDefinitions'\n\nconst supabase = createClient\u003cDatabase\u003e(SUPABASE_URL, ANON_KEY)\n\nconst { data } = await supabase.from('messages').select().match({ id: 1 })\n","lines":[34,41],"level":0},{"type":"paragraph_open","tight":false,"lines":[42,43],"level":0},{"type":"inline","content":"\u003cdetails\u003e","level":1,"lines":[42,43],"children":[{"type":"text","content":"\u003cdetails\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[44,45],"level":0},{"type":"inline","content":"\u003csummary\u003eℹ️ Differences from v1\u003c/summary\u003e","level":1,"lines":[44,45],"children":[{"type":"text","content":"\u003csummary\u003eℹ️ Differences from v1\u003c/summary\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[46,49],"level":0},{"type":"inline","content":"v1 also supported types, but the types were generated from the API rather than the database, so it\nlost a lot of detailed information. The library also required you to specify the definition in\nevery method call, rather than at the client level.","level":1,"lines":[46,49],"children":[{"type":"text","content":"v1 also supported types, but the types were generated from the API rather than the database, so it","level":0},{"type":"softbreak","level":0},{"type":"text","content":"lost a lot of detailed information. The library also required you to specify the definition in","level":0},{"type":"softbreak","level":0},{"type":"text","content":"every method call, rather than at the client level.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"","content":"supabase.from\u003cDatabase['Message']\u003e('messages').select('*')\n","lines":[50,53],"level":0},{"type":"paragraph_open","tight":false,"lines":[54,55],"level":0},{"type":"inline","content":"\u003c/details\u003e","level":1,"lines":[54,55],"children":[{"type":"text","content":"\u003c/details\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"hr","lines":[56,57],"level":0},{"type":"heading_open","hLevel":3,"lines":[58,59],"level":0},{"type":"inline","content":"[New Auth methods](#new-auth-methods)","level":1,"lines":[58,59],"children":[{"type":"text","content":"New Auth methods","level":0}],"lvl":3,"i":2,"seen":0,"slug":"new-auth-methods"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[60,62],"level":0},{"type":"inline","content":"We're removing the `signIn()` method in favor of more explicit method signatures:\n`signInWithPassword()`, and `signInWithOtp()`.","level":1,"lines":[60,62],"children":[{"type":"text","content":"We're removing the ","level":0},{"type":"code","content":"signIn()","block":false,"level":0},{"type":"text","content":" method in favor of more explicit method signatures:","level":0},{"type":"softbreak","level":0},{"type":"code","content":"signInWithPassword()","block":false,"level":0},{"type":"text","content":", and ","level":0},{"type":"code","content":"signInWithOtp()","block":false,"level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"ts","content":"// v2\nconst { data } = await supabase.auth.signInWithPassword({\n  email: 'hello@example',\n  password: 'pass',\n})\n\n// v1\nconst { data } = await supabase.auth.signIn({\n  email: 'hello@example',\n  password: 'pass',\n})\n","lines":[63,76],"level":0},{"type":"paragraph_open","tight":false,"lines":[77,79],"level":0},{"type":"inline","content":"This helps with type hinting. Previously it was difficult for developers to know what they were missing.\nA lot of developers didn't even realize they could use passwordless magic links.","level":1,"lines":[77,79],"children":[{"type":"text","content":"This helps with type hinting. Previously it was difficult for developers to know what they were missing.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"A lot of developers didn't even realize they could use passwordless magic links.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"hr","lines":[80,81],"level":0},{"type":"heading_open","hLevel":3,"lines":[82,83],"level":0},{"type":"inline","content":"[Data methods return minimal by default](#data-methods-return-minimal-by-default)","level":1,"lines":[82,83],"children":[{"type":"text","content":"Data methods return minimal by default","level":0}],"lvl":3,"i":3,"seen":0,"slug":"data-methods-return-minimal-by-default"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[84,85],"level":0},{"type":"inline","content":"The `insert()`, `update()`, and `upsert()` methods now require you to explicitly append `select()` if you want the data to be returned.","level":1,"lines":[84,85],"children":[{"type":"text","content":"The ","level":0},{"type":"code","content":"insert()","block":false,"level":0},{"type":"text","content":", ","level":0},{"type":"code","content":"update()","block":false,"level":0},{"type":"text","content":", and ","level":0},{"type":"code","content":"upsert()","block":false,"level":0},{"type":"text","content":" methods now require you to explicitly append ","level":0},{"type":"code","content":"select()","block":false,"level":0},{"type":"text","content":" if you want the data to be returned.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"ts","content":"// v2\nconst { data } = await supabase.from('messages').insert({ id: 1, message: 'Hello world' }).select() // select is now explicitly required\n\n// v1\nconst { data } = await supabase.from('messages').insert({ id: 1, message: 'Hello world' }) // insert would also \"select()\"\n","lines":[86,93],"level":0},{"type":"paragraph_open","tight":false,"lines":[94,98],"level":0},{"type":"inline","content":"This was another common question in our GitHub Discussions. While the idea of automatically returning data is great,\ndevelopers often turn on Row Level Security (which is great), and then they forget to add a `select` Policy.\nIt is a bit surprising that you need to add a `select` policy to do an `insert`, so we opted for the “principle of least surprise”.\nIf you don't append `select()`, the `data` value will be an empty object: `{}`.","level":1,"lines":[94,98],"children":[{"type":"text","content":"This was another common question in our GitHub Discussions. While the idea of automatically returning data is great,","level":0},{"type":"softbreak","level":0},{"type":"text","content":"developers often turn on Row Level Security (which is great), and then they forget to add a ","level":0},{"type":"code","content":"select","block":false,"level":0},{"type":"text","content":" Policy.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"It is a bit surprising that you need to add a ","level":0},{"type":"code","content":"select","block":false,"level":0},{"type":"text","content":" policy to do an ","level":0},{"type":"code","content":"insert","block":false,"level":0},{"type":"text","content":", so we opted for the “principle of least surprise”.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"If you don't append ","level":0},{"type":"code","content":"select()","block":false,"level":0},{"type":"text","content":", the ","level":0},{"type":"code","content":"data","block":false,"level":0},{"type":"text","content":" value will be an empty object: ","level":0},{"type":"code","content":"{}","block":false,"level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[99,100],"level":0},{"type":"inline","content":"\u003cdetails\u003e","level":1,"lines":[99,100],"children":[{"type":"text","content":"\u003cdetails\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[101,102],"level":0},{"type":"inline","content":"\u003csummary\u003eℹ️ Differences from v1\u003c/summary\u003e","level":1,"lines":[101,102],"children":[{"type":"text","content":"\u003csummary\u003eℹ️ Differences from v1\u003c/summary\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[103,105],"level":0},{"type":"inline","content":"Previously you could pass a `returning: 'minimal'` option to the `insert()`, `update()`, and\n`upsert()` statements. We've now made this the default behaviour.","level":1,"lines":[103,105],"children":[{"type":"text","content":"Previously you could pass a ","level":0},{"type":"code","content":"returning: 'minimal'","block":false,"level":0},{"type":"text","content":" option to the ","level":0},{"type":"code","content":"insert()","block":false,"level":0},{"type":"text","content":", ","level":0},{"type":"code","content":"update()","block":false,"level":0},{"type":"text","content":", and","level":0},{"type":"softbreak","level":0},{"type":"code","content":"upsert()","block":false,"level":0},{"type":"text","content":" statements. We've now made this the default behaviour.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[106,107],"level":0},{"type":"inline","content":"\u003c/details\u003e","level":1,"lines":[106,107],"children":[{"type":"text","content":"\u003c/details\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"hr","lines":[108,109],"level":0},{"type":"heading_open","hLevel":3,"lines":[110,111],"level":0},{"type":"inline","content":"[Auth Admin methods](#auth-admin-methods)","level":1,"lines":[110,111],"children":[{"type":"text","content":"Auth Admin methods","level":0}],"lvl":3,"i":4,"seen":0,"slug":"auth-admin-methods"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[112,113],"level":0},{"type":"inline","content":"We've move all server-side Auth methods from `supabase.auth.api` to `supabase.auth.admin`:","level":1,"lines":[112,113],"children":[{"type":"text","content":"We've move all server-side Auth methods from ","level":0},{"type":"code","content":"supabase.auth.api","block":false,"level":0},{"type":"text","content":" to ","level":0},{"type":"code","content":"supabase.auth.admin","block":false,"level":0},{"type":"text","content":":","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"ts","content":"// v2\nconst { data: user, error } = await supabase.auth.admin.listUsers()\n\n// v1\nconst { data: user, error } = await supabase.auth.api.listUsers()\n","lines":[114,121],"level":0},{"type":"paragraph_open","tight":false,"lines":[122,124],"level":0},{"type":"inline","content":"All `admin` methods expect a `SERVICE_ROLE` key.\nThis change makes it clear that any methods under the `admin` namespace should only be used on a trusted server-side environment.","level":1,"lines":[122,124],"children":[{"type":"text","content":"All ","level":0},{"type":"code","content":"admin","block":false,"level":0},{"type":"text","content":" methods expect a ","level":0},{"type":"code","content":"SERVICE_ROLE","block":false,"level":0},{"type":"text","content":" key.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"This change makes it clear that any methods under the ","level":0},{"type":"code","content":"admin","block":false,"level":0},{"type":"text","content":" namespace should only be used on a trusted server-side environment.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"hr","lines":[125,126],"level":0},{"type":"heading_open","hLevel":3,"lines":[127,128],"level":0},{"type":"inline","content":"[Async Auth overhaul](#async-auth-overhaul)","level":1,"lines":[127,128],"children":[{"type":"text","content":"Async Auth overhaul","level":0}],"lvl":3,"i":5,"seen":0,"slug":"async-auth-overhaul"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[129,130],"level":0},{"type":"inline","content":"We've rebuilt the Auth library, making it async for almost all methods.","level":1,"lines":[129,130],"children":[{"type":"text","content":"We've rebuilt the Auth library, making it async for almost all methods.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"ts","content":"// v2\nconst { data } = await supabase.auth.getSession()\n\n// v1\nconst { data } = supabase.auth.session()\n","lines":[131,138],"level":0},{"type":"paragraph_open","tight":false,"lines":[139,140],"level":0},{"type":"inline","content":"This solves the “getting logged out” issue, which has been a recurring challenge in our GitHub Discussions.","level":1,"lines":[139,140],"children":[{"type":"text","content":"This solves the “getting logged out” issue, which has been a recurring challenge in our GitHub Discussions.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[141,142],"level":0},{"type":"inline","content":"\u003cdetails\u003e","level":1,"lines":[141,142],"children":[{"type":"text","content":"\u003cdetails\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[143,144],"level":0},{"type":"inline","content":"\u003csummary\u003eℹ️ Improvements from v1\u003c/summary\u003e","level":1,"lines":[143,144],"children":[{"type":"text","content":"\u003csummary\u003eℹ️ Improvements from v1\u003c/summary\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[145,147],"level":0},{"type":"inline","content":"The previous implementation had a race condition when refreshing the auth token across multiple tabs. The async re-write forces the library to wait\nfor a valid/invalid session before taking any action.","level":1,"lines":[145,147],"children":[{"type":"text","content":"The previous implementation had a race condition when refreshing the auth token across multiple tabs. The async re-write forces the library to wait","level":0},{"type":"softbreak","level":0},{"type":"text","content":"for a valid/invalid session before taking any action.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[148,149],"level":0},{"type":"inline","content":"\u003c/details\u003e","level":1,"lines":[148,149],"children":[{"type":"text","content":"\u003c/details\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"hr","lines":[150,151],"level":0},{"type":"heading_open","hLevel":3,"lines":[152,153],"level":0},{"type":"inline","content":"[Scoped constructor config](#scoped-constructor-config)","level":1,"lines":[152,153],"children":[{"type":"text","content":"Scoped constructor config","level":0}],"lvl":3,"i":6,"seen":0,"slug":"scoped-constructor-config"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[154,155],"level":0},{"type":"inline","content":"We're being much more explicit about the modular approach that `supabase-js` uses:","level":1,"lines":[154,155],"children":[{"type":"text","content":"We're being much more explicit about the modular approach that ","level":0},{"type":"code","content":"supabase-js","block":false,"level":0},{"type":"text","content":" uses:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"ts","content":"const supabase = createClient(apiURL, apiKey, {\n  db: {\n    schema: 'public',\n  },\n  auth: {\n    autoRefreshToken: true,\n    persistSession: true,\n  },\n  realtime: {\n    channels,\n    endpoint,\n  },\n  // common across all libraries\n  global: {\n    fetch: customFetch,\n    headers: DEFAULT_HEADERS,\n  },\n})\n","lines":[156,176],"level":0},{"type":"paragraph_open","tight":false,"lines":[177,178],"level":0},{"type":"inline","content":"This is clearer for developers - if you're only using some parts of Supabase, you only receive the hints for those parts.","level":1,"lines":[177,178],"children":[{"type":"text","content":"This is clearer for developers - if you're only using some parts of Supabase, you only receive the hints for those parts.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[179,180],"level":0},{"type":"inline","content":"\u003cdetails\u003e","level":1,"lines":[179,180],"children":[{"type":"text","content":"\u003cdetails\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[181,182],"level":0},{"type":"inline","content":"\u003csummary\u003eℹ️ Improvements from v1\u003c/summary\u003e","level":1,"lines":[181,182],"children":[{"type":"text","content":"\u003csummary\u003eℹ️ Improvements from v1\u003c/summary\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[183,185],"level":0},{"type":"inline","content":"We noticed a lot of confusion for the variable naming between each library.\nFor example, Auth has a config parameter called \"storageKey\", which was was often confused with the `storage-js` library bundled in the `supabase-js` library.","level":1,"lines":[183,185],"children":[{"type":"text","content":"We noticed a lot of confusion for the variable naming between each library.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"For example, Auth has a config parameter called \"storageKey\", which was was often confused with the ","level":0},{"type":"code","content":"storage-js","block":false,"level":0},{"type":"text","content":" library bundled in the ","level":0},{"type":"code","content":"supabase-js","block":false,"level":0},{"type":"text","content":" library.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[186,187],"level":0},{"type":"inline","content":"\u003c/details\u003e","level":1,"lines":[186,187],"children":[{"type":"text","content":"\u003c/details\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"hr","lines":[188,189],"level":0},{"type":"heading_open","hLevel":3,"lines":[190,191],"level":0},{"type":"inline","content":"[Better Errors](#better-errors)","level":1,"lines":[190,191],"children":[{"type":"text","content":"Better Errors","level":0}],"lvl":3,"i":7,"seen":0,"slug":"better-errors"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[192,193],"level":0},{"type":"inline","content":"We've created error types for all of the sub-libraries in `supabase-js`. Here's a example for Edge Functions:","level":1,"lines":[192,193],"children":[{"type":"text","content":"We've created error types for all of the sub-libraries in ","level":0},{"type":"code","content":"supabase-js","block":false,"level":0},{"type":"text","content":". Here's a example for Edge Functions:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"ts","content":"import { FunctionsHttpError, FunctionsRelayError, FunctionsFetchError } from '@supabase/supabase-js'\n\nconst { data: user, error } = await supabase.functions.invoke('hello')\n\nif (error instanceof FunctionsHttpError) {\n  console.log('Function returned an error', error.message)\n} else if (error instanceof FunctionsRelayError) {\n  console.log('Relay error:', error.message)\n} else if (error instanceof FunctionsFetchError) {\n  console.log('Fetch error:', error.message)\n}\n","lines":[194,207],"level":0},{"type":"hr","lines":[208,209],"level":0},{"type":"heading_open","hLevel":3,"lines":[210,211],"level":0},{"type":"inline","content":"[Improvements for Edge Functions](#improvements-for-edge-functions)","level":1,"lines":[210,211],"children":[{"type":"text","content":"Improvements for Edge Functions","level":0}],"lvl":3,"i":8,"seen":0,"slug":"improvements-for-edge-functions"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[212,213],"level":0},{"type":"inline","content":"`supabase-js` now automatically detects the content type for the request/response bodies for all Edge Function invocations:","level":1,"lines":[212,213],"children":[{"type":"code","content":"supabase-js","block":false,"level":0},{"type":"text","content":" now automatically detects the content type for the request/response bodies for all Edge Function invocations:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"ts","content":"// v2\nconst { data: user, error } = await supabase.functions.invoke('hello', {\n  body: { foo: 'bar' },\n})\n\n// v1\nconst { data: user, error } = await supabase.functions.invoke('hello', {\n  headers: { 'Content-Type': 'application/json' }\n  body: JSON.stringify({ foo: 'bar' }),\n})\n","lines":[214,226],"level":0},{"type":"paragraph_open","tight":false,"lines":[227,228],"level":0},{"type":"inline","content":"This improvement inspired by a Supabase community member. Thanks [@vejja](https://github.com/supabase/functions-js/pull/23)!","level":1,"lines":[227,228],"children":[{"type":"text","content":"This improvement inspired by a Supabase community member. Thanks ","level":0},{"type":"link_open","href":"https://github.com/supabase/functions-js/pull/23","title":"","level":0},{"type":"text","content":"@vejja","level":1},{"type":"link_close","level":0},{"type":"text","content":"!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"hr","lines":[229,230],"level":0},{"type":"heading_open","hLevel":3,"lines":[231,232],"level":0},{"type":"inline","content":"[Multiplayer Sneak Peek](#multiplayer-sneak-peek)","level":1,"lines":[231,232],"children":[{"type":"text","content":"Multiplayer Sneak Peek","level":0}],"lvl":3,"i":9,"seen":0,"slug":"multiplayer-sneak-peek"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[233,235],"level":0},{"type":"inline","content":"There is a new `channel()` interface which are releasing in v2.\nThis is a \"preparatory\" release for our upcoming [multiplayer](https://supabase.com/blog/supabase-realtime-with-multiplayer-features) features.","level":1,"lines":[233,235],"children":[{"type":"text","content":"There is a new ","level":0},{"type":"code","content":"channel()","block":false,"level":0},{"type":"text","content":" interface which are releasing in v2.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"This is a \"preparatory\" release for our upcoming ","level":0},{"type":"link_open","href":"https://supabase.com/blog/supabase-realtime-with-multiplayer-features","title":"","level":0},{"type":"text","content":"multiplayer","level":1},{"type":"link_close","level":0},{"type":"text","content":" features.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"ts","content":"supabase\n  .channel('any_string_you_want')\n  .on('presence', { event: 'track' }, (payload) =\u003e {\n    console.log(payload)\n  })\n  .subscribe()\n","lines":[236,244],"level":0},{"type":"paragraph_open","tight":false,"lines":[245,246],"level":0},{"type":"inline","content":"As part of this change, the old `from().on().subscribe()` method for listening to database changes will be changing:","level":1,"lines":[245,246],"children":[{"type":"text","content":"As part of this change, the old ","level":0},{"type":"code","content":"from().on().subscribe()","block":false,"level":0},{"type":"text","content":" method for listening to database changes will be changing:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"ts","content":"// v2\nsupabase\n  .channel('any_string_you_want')\n  .on(\n    'postgres_changes',\n    {\n      event: 'INSERT',\n      schema: 'public',\n      table: 'movies',\n    },\n    (payload) =\u003e {\n      console.log(payload)\n    }\n  )\n  .subscribe()\n\n// v1\nsupabase\n  .from('movies')\n  .on('INSERT', (payload) =\u003e {\n    console.log(payload)\n  })\n  .subscribe()\n","lines":[247,272],"level":0},{"type":"paragraph_open","tight":false,"lines":[273,275],"level":0},{"type":"inline","content":"You can listen to PostgreSQL database changes on any channel you want by subscribing to the `'postgres_changes'` event.\nFor now, we will continue to support `from().on().subscribe()`, but in the future we will deprecate this in favor of the `channel().on().subscribe()` method.","level":1,"lines":[273,275],"children":[{"type":"text","content":"You can listen to PostgreSQL database changes on any channel you want by subscribing to the ","level":0},{"type":"code","content":"'postgres_changes'","block":false,"level":0},{"type":"text","content":" event.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"For now, we will continue to support ","level":0},{"type":"code","content":"from().on().subscribe()","block":false,"level":0},{"type":"text","content":", but in the future we will deprecate this in favor of the ","level":0},{"type":"code","content":"channel().on().subscribe()","block":false,"level":0},{"type":"text","content":" method.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"hr","lines":[276,277],"level":0},{"type":"heading_open","hLevel":2,"lines":[278,279],"level":0},{"type":"inline","content":"[Community](#community)","level":1,"lines":[278,279],"children":[{"type":"text","content":"Community","level":0}],"lvl":2,"i":10,"seen":0,"slug":"community"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[280,282],"level":0},{"type":"inline","content":"Version 2.0 is the result of the combined work of over 100 contributors to our libraries, and over 450 contributors to our docs and websites.\nIf you're one of those contributors, thank you.","level":1,"lines":[280,282],"children":[{"type":"text","content":"Version 2.0 is the result of the combined work of over 100 contributors to our libraries, and over 450 contributors to our docs and websites.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"If you're one of those contributors, thank you.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[283,290],"level":0},{"type":"list_item_open","lines":[283,284],"level":1},{"type":"paragraph_open","tight":true,"lines":[283,284],"level":2},{"type":"inline","content":"[`functions-js`](https://github.com/supabase/functions-js/graphs/contributors) (4)","level":3,"lines":[283,284],"children":[{"type":"link_open","href":"https://github.com/supabase/functions-js/graphs/contributors","title":"","level":0},{"type":"code","content":"functions-js","block":false,"level":1},{"type":"link_close","level":0},{"type":"text","content":" (4)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[284,285],"level":1},{"type":"paragraph_open","tight":true,"lines":[284,285],"level":2},{"type":"inline","content":"[`gotrue-js`](https://github.com/supabase/gotrue-js/graphs/contributors) (47)","level":3,"lines":[284,285],"children":[{"type":"link_open","href":"https://github.com/supabase/gotrue-js/graphs/contributors","title":"","level":0},{"type":"code","content":"gotrue-js","block":false,"level":1},{"type":"link_close","level":0},{"type":"text","content":" (47)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[285,286],"level":1},{"type":"paragraph_open","tight":true,"lines":[285,286],"level":2},{"type":"inline","content":"[`postgrest-js`](https://github.com/supabase/postgrest-js/graphs/contributors) (30)","level":3,"lines":[285,286],"children":[{"type":"link_open","href":"https://github.com/supabase/postgrest-js/graphs/contributors","title":"","level":0},{"type":"code","content":"postgrest-js","block":false,"level":1},{"type":"link_close","level":0},{"type":"text","content":" (30)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[286,287],"level":1},{"type":"paragraph_open","tight":true,"lines":[286,287],"level":2},{"type":"inline","content":"[`realtime-js`](https://github.com/supabase/realtime-js/graphs/contributors) (16)","level":3,"lines":[286,287],"children":[{"type":"link_open","href":"https://github.com/supabase/realtime-js/graphs/contributors","title":"","level":0},{"type":"code","content":"realtime-js","block":false,"level":1},{"type":"link_close","level":0},{"type":"text","content":" (16)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[287,288],"level":1},{"type":"paragraph_open","tight":true,"lines":[287,288],"level":2},{"type":"inline","content":"[`storage-js`](https://github.com/supabase/storage-js/graphs/contributors) (17)","level":3,"lines":[287,288],"children":[{"type":"link_open","href":"https://github.com/supabase/storage-js/graphs/contributors","title":"","level":0},{"type":"code","content":"storage-js","block":false,"level":1},{"type":"link_close","level":0},{"type":"text","content":" (17)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[288,290],"level":1},{"type":"paragraph_open","tight":true,"lines":[288,289],"level":2},{"type":"inline","content":"[`supabase-js`](https://github.com/supabase/supabase-js/graphs/contributors) (39)","level":3,"lines":[288,289],"children":[{"type":"link_open","href":"https://github.com/supabase/supabase-js/graphs/contributors","title":"","level":0},{"type":"code","content":"supabase-js","block":false,"level":1},{"type":"link_close","level":0},{"type":"text","content":" (39)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[290,291],"level":0},{"type":"inline","content":"Special shout outs to: [@vejja](https://github.com/vejja), [@pixtron](https://github.com/pixtron), [@bnjmnt4n](https://github.com/pixtron), and [@karlseguin](https://github.com/karlseguin).","level":1,"lines":[290,291],"children":[{"type":"text","content":"Special shout outs to: ","level":0},{"type":"link_open","href":"https://github.com/vejja","title":"","level":0},{"type":"text","content":"@vejja","level":1},{"type":"link_close","level":0},{"type":"text","content":", ","level":0},{"type":"link_open","href":"https://github.com/pixtron","title":"","level":0},{"type":"text","content":"@pixtron","level":1},{"type":"link_close","level":0},{"type":"text","content":", ","level":0},{"type":"link_open","href":"https://github.com/pixtron","title":"","level":0},{"type":"text","content":"@bnjmnt4n","level":1},{"type":"link_close","level":0},{"type":"text","content":", and ","level":0},{"type":"link_open","href":"https://github.com/karlseguin","title":"","level":0},{"type":"text","content":"@karlseguin","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[292,293],"level":0},{"type":"inline","content":"[Migrating to v2](#migrating-to-v2)","level":1,"lines":[292,293],"children":[{"type":"text","content":"Migrating to v2","level":0}],"lvl":2,"i":11,"seen":0,"slug":"migrating-to-v2"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[294,295],"level":0},{"type":"inline","content":"Update today by running:","level":1,"lines":[294,295],"children":[{"type":"text","content":"Update today by running:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"bash","content":"npm i @supabase/supabase-js@2\n","lines":[296,299],"level":0},{"type":"paragraph_open","tight":false,"lines":[300,301],"level":0},{"type":"inline","content":"[Migration guide](https://supabase.com/docs/reference/javascript/v1/upgrade-guide)","level":1,"lines":[300,301],"children":[{"type":"link_open","href":"https://supabase.com/docs/reference/javascript/v1/upgrade-guide","title":"","level":0},{"type":"text","content":"Migration guide","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[302,303],"level":0},{"type":"inline","content":"We'll continuing merging security fixes to v1, with maintenance patches for the next three months.","level":1,"lines":[302,303],"children":[{"type":"text","content":"We'll continuing merging security fixes to v1, with maintenance patches for the next three months.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[304,305],"level":0},{"type":"inline","content":"[Announcement video and discussion](#announcement-video-and-discussion)","level":1,"lines":[304,305],"children":[{"type":"text","content":"Announcement video and discussion","level":0}],"lvl":2,"i":12,"seen":0,"slug":"announcement-video-and-discussion"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[306,314],"level":0},{"type":"inline","content":"\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/iqZlPtl_b-I\"\n    title=\"YouTube video player\"\n    frameborder=\"0\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowfullscreen","level":1,"lines":[306,314],"children":[{"type":"text","content":"\u003cdiv className=\"video-container\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ciframe","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"w-full\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://www.youtube-nocookie.com/embed/iqZlPtl_b-I\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"title=\"YouTube video player\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"frameborder=\"0\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allowfullscreen","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"blockquote_open","lines":[314,316],"level":0},{"type":"paragraph_open","tight":false,"lines":[314,316],"level":1},{"type":"inline","content":"\u003c/iframe\u003e\n\u003c/div\u003e","level":2,"lines":[314,316],"children":[{"type":"text","content":"\u003c/iframe\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"heading_open","hLevel":2,"lines":[317,318],"level":0},{"type":"inline","content":"[supabase-js v2 resources](#supabase-js-v2-resources)","level":1,"lines":[317,318],"children":[{"type":"text","content":"supabase-js v2 resources","level":0}],"lvl":2,"i":13,"seen":0,"slug":"supabase-js-v2-resources"},{"type":"heading_close","hLevel":2,"level":0},{"type":"bullet_list_open","lines":[319,323],"level":0},{"type":"list_item_open","lines":[319,320],"level":1},{"type":"paragraph_open","tight":true,"lines":[319,320],"level":2},{"type":"inline","content":"[v2 Documentation](https://supabase.com/docs/reference/javascript)","level":3,"lines":[319,320],"children":[{"type":"link_open","href":"https://supabase.com/docs/reference/javascript","title":"","level":0},{"type":"text","content":"v2 Documentation","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[320,321],"level":1},{"type":"paragraph_open","tight":true,"lines":[320,321],"level":2},{"type":"inline","content":"[Migration guide](https://supabase.com/docs/reference/javascript/v1/upgrade-guide)","level":3,"lines":[320,321],"children":[{"type":"link_open","href":"https://supabase.com/docs/reference/javascript/v1/upgrade-guide","title":"","level":0},{"type":"text","content":"Migration guide","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[321,322],"level":1},{"type":"paragraph_open","tight":true,"lines":[321,322],"level":2},{"type":"inline","content":"[Next.js quickstart guide](/docs/guides/with-nextjs)","level":3,"lines":[321,322],"children":[{"type":"link_open","href":"/docs/guides/with-nextjs","title":"","level":0},{"type":"text","content":"Next.js quickstart guide","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[322,323],"level":1},{"type":"paragraph_open","tight":true,"lines":[322,323],"level":2},{"type":"inline","content":"[Examples](https://github.com/supabase/supabase/tree/master/examples)","level":3,"lines":[322,323],"children":[{"type":"link_open","href":"https://github.com/supabase/supabase/tree/master/examples","title":"","level":0},{"type":"text","content":"Examples","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0}],"content":"- [Major Updates](#major-updates)\n  * [Type support](#type-support)\n  * [New Auth methods](#new-auth-methods)\n  * [Data methods return minimal by default](#data-methods-return-minimal-by-default)\n  * [Auth Admin methods](#auth-admin-methods)\n  * [Async Auth overhaul](#async-auth-overhaul)\n  * [Scoped constructor config](#scoped-constructor-config)\n  * [Better Errors](#better-errors)\n  * [Improvements for Edge Functions](#improvements-for-edge-functions)\n  * [Multiplayer Sneak Peek](#multiplayer-sneak-peek)\n- [Community](#community)\n- [Migrating to v2](#migrating-to-v2)\n- [Announcement video and discussion](#announcement-video-and-discussion)\n- [supabase-js v2 resources](#supabase-js-v2-resources)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-js-v2"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>