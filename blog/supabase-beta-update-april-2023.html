<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase Beta April 2023</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="A review of Launch Week 7 and more exciting updates from last month." data-next-head=""/><meta property="og:title" content="Supabase Beta April 2023" data-next-head=""/><meta property="og:description" content="A review of Launch Week 7 and more exciting updates from last month." data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-beta-update-april-2023" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2023-05-09" data-next-head=""/><meta property="article:author" content="https://github.com/awalias" data-next-head=""/><meta property="article:tag" content="release-notes" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/2023-05-09-beta-update-april/monthly-update-april-2023.jpg" data-next-head=""/><meta property="og:image:alt" content="Supabase Beta April 2023 thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Supabase Beta April 2023</h1><div class="text-light flex space-x-3 text-sm"><p>09 May 2023</p><p>•</p><p>4 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/awalias"><div class="flex items-center gap-3"><div class="w-10"><img alt="Ant Wilson avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fawalias.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fawalias.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fawalias.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Ant Wilson</span><span class="text-foreground-lighter mb-0 text-xs">CTO and Co-Founder</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Supabase Beta April 2023" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2023-05-09-beta-update-april%2Fmonthly-update-april-2023.jpg&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2023-05-09-beta-update-april%2Fmonthly-update-april-2023.jpg&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2023-05-09-beta-update-april%2Fmonthly-update-april-2023.jpg&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2023-05-09-beta-update-april%2Fmonthly-update-april-2023.jpg&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2023-05-09-beta-update-april%2Fmonthly-update-april-2023.jpg&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2023-05-09-beta-update-april%2Fmonthly-update-april-2023.jpg&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2023-05-09-beta-update-april%2Fmonthly-update-april-2023.jpg&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2023-05-09-beta-update-april%2Fmonthly-update-april-2023.jpg&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2023-05-09-beta-update-april%2Fmonthly-update-april-2023.jpg&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2023-05-09-beta-update-april%2Fmonthly-update-april-2023.jpg&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2023-05-09-beta-update-april%2Fmonthly-update-april-2023.jpg&amp;w=3840&amp;q=100"/></div><p>Brace yourself, this is one of the most feature-packed Beta Updates we&#x27;ve published so far.</p>
<p><a href="../launch-week.html">Launch Week 7</a> was a massive success with great feedback from the community. We also gave away lots of mechanical keyboards, including the Hackathon winner which we announce here!</p>
<h2 id="day-1---supabase-logs-open-source-logging-server" class="group scroll-mt-24">Day 1 - Supabase Logs: open source logging server<a href="#day-1---supabase-logs-open-source-logging-server" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>Logflare is the hub of analytics streams for Supabase. We are open sourcing it so that you can self-host your own Logging infrastructure.</p>
<p><a href="https://supabase.com/blog/supabase-logs-self-hosted">Blog Post</a><br/>
<a href="https://www.youtube.com/watch?v=Ai2BjHV36Ng">Video overview</a></p>
<h2 id="day-2---supabase-edge-runtime-self-hosted-deno-functions" class="group scroll-mt-24">Day 2 - Supabase Edge Runtime: Self-hosted Deno Functions<a href="#day-2---supabase-edge-runtime-self-hosted-deno-functions" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>You can now self-host Edge Functions and run them in local development using our new Edge Runtime. We published a guide showing how to self-host Edge Functions with Fly and what more is coming<!-- -->️</p>
<p><a href="https://supabase.com/blog/edge-runtime-self-hosted-deno-functions">Blog post</a><br/>
<a href="https://www.youtube.com/watch?v=cPGxPl1lx4Y">Video overview</a></p>
<h2 id="day-3---storage-v3-resumable-uploads-with-support-for-50gb-files" class="group scroll-mt-24">Day 3 - Storage v3: Resumable Uploads with support for 50GB files<a href="#day-3---storage-v3-resumable-uploads-with-support-for-50gb-files" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>Supabase Storage received many of the most requested features from our users: Resumable Uploads, Quality Filters, Next.js support, and WebP support.</p>
<p><a href="https://supabase.com/blog/storage-v3-resumable-uploads">Blog post</a><br/>
<a href="https://www.youtube.com/watch?v=pT2PcZFq_M0">Video overview</a></p>
<h2 id="day-4---supabase-auth-sso-mobile-and-server-side-support" class="group scroll-mt-24">Day 4 - Supabase Auth: SSO, Mobile, and Server-side support<a href="#day-4---supabase-auth-sso-mobile-and-server-side-support" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>On day 4, we introduced SSO with SAML 2.0, PKCE, and Sign in with Apple for iOS. It felt like acronym day, but it was actually Auth day!</p>
<p><a href="https://supabase.com/blog/supabase-auth-sso-pkce">Blog post</a><br/>
<a href="https://www.youtube.com/watch?v=hAwJeR6mhB0">Video overview</a></p>
<h2 id="day-5---supabase-studio-20-with-new-ai-features" class="group scroll-mt-24">Day 5 - Supabase Studio 2.0 with new AI features<a href="#day-5---supabase-studio-20-with-new-ai-features" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>Supabase Studio got a major upgrade that goes from redesigns to improved developer experience, and new tools. We have the features people have been asking for and new capabilities that will change the way you work.</p>
<p><a href="https://supabase.com/blog/supabase-studio-2.0">Blog Post</a><br/>
<a href="https://www.youtube.com/watch?v=0rcNqHt5KWU">Video overview</a></p>
<h2 id="community-highlights" class="group scroll-mt-24">Community highlights<a href="#community-highlights" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>Our community defines us. We&#x27;re honored to work with, sponsor, and support incredible people and tools 💜. Our CEO wrote a highlight of the last 3 months.</p>
<p><a href="https://supabase.com/blog/launch-week-7-community-highlights">Blog post</a></p>
<h2 id="introducing-dbdev-postgresql-package-manager" class="group scroll-mt-24">Introducing dbdev: PostgreSQL Package Manager<a href="#introducing-dbdev-postgresql-package-manager" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p><a href="https://database.dev/">database.dev</a> fills the same role for PostgreSQL as <code class="short-inline-codeblock">npm</code> for JavaScript or <code class="short-inline-codeblock">pip</code> for Python, it enables publishing libraries and applications for repeatable deployment. Our goal is to create an open ecosystem for packaging and discovering SQL.</p>
<p><a href="dbdev.html">Blog post</a></p>
<h2 id="more-product-announcements" class="group scroll-mt-24">More product announcements<a href="#more-product-announcements" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ul>
<li>Trusted Language Extensions for Postgres. <a href="https://supabase.com/blog/pg-tle">[Blog post]</a></li>
<li>What&#x27;s New in pg_graphql v1.2. <a href="whats-new-in-pg-graphql-v1-2.html">[Blog post]</a></li>
<li>GitHub Discussions are now a new knowledge source for search &amp; AI (Troubleshooting category only for now). <a href="../docs.html">[Check it out]</a></li>
<li>New API report with routing information for each chart, making it easier to debug API calls. <a href="https://github.com/supabase/supabase/pull/14063">[PR]</a></li>
<li>Storage permission changes: the developer role is now allowed to update the storage settings (previously was only owner and admin). <a href="https://github.com/supabase/supabase/pull/13883">[PR]</a></li>
</ul>
<h2 id="launch-week-7-hackathon-winners" class="group scroll-mt-24">Launch Week 7 Hackathon winners<a href="#launch-week-7-hackathon-winners" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>The community is loving <code class="short-inline-codeblock">pgvector</code> to build AI apps so we decided to make it part of the traditional Launch Week Hackathon. The quality of the apps was out of this world, it wasn&#x27;t easy, but in the end, we selected <a href="https://github.com/n4ze3m/page-assist">Page Assist</a> - by <a href="https://twitter.com/n4ze3m">@n4ze3m </a>as the winner of the Best Overall Project.</p>
<p><a href="launch-week-7-hackathon-winners.html">Full list of Winners</a></p>
<p><a href="https://www.madewithsupabase.com/launch-week-7">See all the submissions</a></p>
<h2 id="mendableai-switches-from-pinecone-to-supabase-for-postgresql-vector-embeddings" class="group scroll-mt-24">Mendable.ai switches from Pinecone to Supabase for PostgreSQL vector embeddings.<a href="#mendableai-switches-from-pinecone-to-supabase-for-postgresql-vector-embeddings" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>With Supabase&#x27;s pg_vector, Mendable.ai could build a more cost-effective solution that is just as performant - if not more performant - than other vector databases.</p>
<p><a href="https://supabase.com/customers/mendableai">Read the full story</a></p>
<h2 id="from-the-community" class="group scroll-mt-24">From the community<a href="#from-the-community" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<ul>
<li>FlutterFlow now supports Supabase Authentication. <a href="https://www.youtube.com/watch?v=tL-sLPfWzVE">Video guide</a></li>
<li>Supabase + ClickHouse: Combining the Best of the OLTP and OLAP Worlds. <a href="https://www.youtube.com/watch?v=LDWEsw41Zko">Webinar</a></li>
<li>Our friend Guillaume put together the most incredible course about Supabase, with the in and outs of the platform. <a href="https://www.youtube.com/watch?v=8DTOTT7q0XA">Full course</a></li>
<li>Supabase + LangChain starter template for building full stack AI apps. <a href="https://github.com/langchain-ai/langchain-template-supabase">Template</a></li>
<li>Creating a Books Tracker App with .NET MAUI and Supabase. <a href="https://hackernoon.com/creating-a-books-tracker-app-with-net-maui-and-supabase">Article</a>
<ul>
<li>Vanta Case Study - Supabase turns trust into a revenue-generating opportunity with Vanta. <a href="https://www.vanta.com/customers/supabase">Case Study</a></li>
</ul>
</li>
</ul>
<h2 id="meme-zone" class="group scroll-mt-24">Meme Zone<a href="#meme-zone" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>As always, one of our favorite memes from last month. <a href="https://twitter.com/supabase">Follow us on Twitter</a> for more.</p>
<p></p></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-update-april-2023&amp;text=Supabase%20Beta%20April%202023"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-update-april-2023&amp;text=Supabase%20Beta%20April%202023"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-update-april-2023&amp;t=Supabase%20Beta%20April%202023"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="flutter-hackathon.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Flutter Hackathon</h4><p class="small">12 May 2023</p></div></div></div></div></a></div><div><a href="flutter-multi-factor-authentication.html"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Securing your Flutter apps with Multi-Factor Authentication</h4><p class="small">4 May 2023</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/release-notes"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">release-notes</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#day-1---supabase-logs-open-source-logging-server">Day 1 - Supabase Logs: open source logging server</a></li>
<li><a href="#day-2---supabase-edge-runtime-self-hosted-deno-functions">Day 2 - Supabase Edge Runtime: Self-hosted Deno Functions</a></li>
<li><a href="#day-3---storage-v3-resumable-uploads-with-support-for-50gb-files">Day 3 - Storage v3: Resumable Uploads with support for 50GB files</a></li>
<li><a href="#day-4---supabase-auth-sso-mobile-and-server-side-support">Day 4 - Supabase Auth: SSO, Mobile, and Server-side support</a></li>
<li><a href="#day-5---supabase-studio-20-with-new-ai-features">Day 5 - Supabase Studio 2.0 with new AI features</a></li>
<li><a href="#community-highlights">Community highlights</a></li>
<li><a href="#introducing-dbdev-postgresql-package-manager">Introducing dbdev: PostgreSQL Package Manager</a></li>
<li><a href="#more-product-announcements">More product announcements</a></li>
<li><a href="#launch-week-7-hackathon-winners">Launch Week 7 Hackathon winners</a></li>
<li><a href="#mendableai-switches-from-pinecone-to-supabase-for-postgresql-vector-embeddings">Mendable.ai switches from Pinecone to Supabase for PostgreSQL vector embeddings.</a></li>
<li><a href="#from-the-community">From the community</a></li>
<li><a href="#meme-zone">Meme Zone</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-update-april-2023&amp;text=Supabase%20Beta%20April%202023"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-update-april-2023&amp;text=Supabase%20Beta%20April%202023"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-update-april-2023&amp;t=Supabase%20Beta%20April%202023"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"flutter-hackathon","title":"Flutter Hackathon","description":"Build Flutter apps and win limited edition swag.","author":"tyler_shukert","image":"flutter-hackathon/thumbnail.jpg","thumb":"flutter-hackathon/thumbnail.jpg","categories":["developers"],"tags":["flutter","hackathon"],"date":"2023-05-12","toc_depth":3,"formattedDate":"12 May 2023","readingTime":"4 minute read","url":"/blog/flutter-hackathon","path":"/blog/flutter-hackathon"},"nextPost":{"slug":"flutter-multi-factor-authentication","title":"Securing your Flutter apps with Multi-Factor Authentication","description":"Build a Flutter app where the user is required to authenticate using Multi-Factor Authentication.","author":"tyler_shukert","image":"flutter-mfa/flutter-mfa-thumb.jpg","thumb":"flutter-mfa/flutter-mfa-thumb.jpg","categories":["developers"],"tags":["flutter","auth"],"date":"2023-05-04","toc_depth":2,"formattedDate":"4 May 2023","readingTime":"50 minute read","url":"/blog/flutter-multi-factor-authentication","path":"/blog/flutter-multi-factor-authentication"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-beta-update-april-2023","source":"\nBrace yourself, this is one of the most feature-packed Beta Updates we've published so far.\n\n[Launch Week 7](https://supabase.com/launch-week) was a massive success with great feedback from the community. We also gave away lots of mechanical keyboards, including the Hackathon winner which we announce here!\n\n## Day 1 - Supabase Logs: open source logging server\n\n![Day 1 - Supabase Logs: open source logging server](/images/blog/2023-05-09-beta-update-april/newsletter-OG-day-1.jpg)\n\nLogflare is the hub of analytics streams for Supabase. We are open sourcing it so that you can self-host your own Logging infrastructure.\n\n[Blog Post](https://supabase.com/blog/supabase-logs-self-hosted)\\\n[Video overview](https://www.youtube.com/watch?v=Ai2BjHV36Ng)\n\n## Day 2 - Supabase Edge Runtime: Self-hosted Deno Functions\n\n![Day 2 - Supabase Edge Runtime: Self-hosted Deno Functions](/images/blog/2023-05-09-beta-update-april/newsletter-OG-day2.jpg)\n\nYou can now self-host Edge Functions and run them in local development using our new Edge Runtime. We published a guide showing how to self-host Edge Functions with Fly and what more is coming![⚡](https://fonts.gstatic.com/s/e/notoemoji/15.0/26a1/32.png)️\n\n[Blog post](https://supabase.com/blog/edge-runtime-self-hosted-deno-functions)\\\n[Video overview](https://www.youtube.com/watch?v=cPGxPl1lx4Y)\n\n## Day 3 - Storage v3: Resumable Uploads with support for 50GB files\n\n![Day 3 - Storage v3: Resumable Uploads with support for 50GB files](/images/blog/2023-05-09-beta-update-april/newsletter-OG-day3.jpg)\n\nSupabase Storage received many of the most requested features from our users: Resumable Uploads, Quality Filters, Next.js support, and WebP support.\n\n[Blog post](https://supabase.com/blog/storage-v3-resumable-uploads)\\\n[Video overview](https://www.youtube.com/watch?v=pT2PcZFq_M0)\n\n## Day 4 - Supabase Auth: SSO, Mobile, and Server-side support\n\n![Day 4 - Supabase Auth: SSO, Mobile, and Server-side support](/images/blog/2023-05-09-beta-update-april/newsletter-OG-day4.jpg)\n\nOn day 4, we introduced SSO with SAML 2.0, PKCE, and Sign in with Apple for iOS. It felt like acronym day, but it was actually Auth day!\n\n[Blog post](https://supabase.com/blog/supabase-auth-sso-pkce)\\\n[Video overview](https://www.youtube.com/watch?v=hAwJeR6mhB0)\n\n## Day 5 - Supabase Studio 2.0 with new AI features\n\n![Day 5 - Supabase Studio 2.0 with new AI features](/images/blog/2023-05-09-beta-update-april/newsletter-OG-day5-studio.jpg)\n\nSupabase Studio got a major upgrade that goes from redesigns to improved developer experience, and new tools. We have the features people have been asking for and new capabilities that will change the way you work.\n\n[Blog Post](https://supabase.com/blog/supabase-studio-2.0)\\\n[Video overview](https://www.youtube.com/watch?v=0rcNqHt5KWU)\n\n## Community highlights\n\n![Community highlights](/images/blog/2023-05-09-beta-update-april/newsletter-OG-day5-community.jpg)\n\nOur community defines us. We're honored to work with, sponsor, and support incredible people and tools 💜. Our CEO wrote a highlight of the last 3 months.\n\n[Blog post](https://supabase.com/blog/launch-week-7-community-highlights)\n\n## Introducing dbdev: PostgreSQL Package Manager\n\n![dbdev: PostgreSQL Package Manager](/images/blog/2023-05-09-beta-update-april/newsletter-OG-day5-dbdev.jpg)\n\n[database.dev](https://database.dev/) fills the same role for PostgreSQL as `npm` for JavaScript or `pip` for Python, it enables publishing libraries and applications for repeatable deployment. Our goal is to create an open ecosystem for packaging and discovering SQL.\n\n[Blog post](https://supabase.com/blog/dbdev)\n\n## More product announcements\n\n- Trusted Language Extensions for Postgres. [[Blog post]](https://supabase.com/blog/pg-tle)\n- What's New in pg_graphql v1.2. [[Blog post]](https://supabase.com/blog/whats-new-in-pg-graphql-v1-2)\n- GitHub Discussions are now a new knowledge source for search \u0026 AI (Troubleshooting category only for now). [[Check it out]](https://supabase.com/docs)\n- New API report with routing information for each chart, making it easier to debug API calls. [[PR]](https://github.com/supabase/supabase/pull/14063)\n- Storage permission changes: the developer role is now allowed to update the storage settings (previously was only owner and admin). [[PR]](https://github.com/supabase/supabase/pull/13883)\n\n## Launch Week 7 Hackathon winners\n\n![Launch Week 7 Hackathon winners](/images/blog/2023-05-09-beta-update-april/newsletter-hackathon.jpg)\n\nThe community is loving `pgvector` to build AI apps so we decided to make it part of the traditional Launch Week Hackathon. The quality of the apps was out of this world, it wasn't easy, but in the end, we selected [Page Assist](https://github.com/n4ze3m/page-assist) - by [@n4ze3m ](https://twitter.com/n4ze3m)as the winner of the Best Overall Project.\n\n[Full list of Winners](https://supabase.com/blog/launch-week-7-hackathon-winners)\n\n[See all the submissions](https://www.madewithsupabase.com/launch-week-7)\n\n## Mendable.ai switches from Pinecone to Supabase for PostgreSQL vector embeddings.\n\n![Mendable logo](/images/blog/2023-05-09-beta-update-april/customer-stories-mendable.png)\n\nWith Supabase's pg_vector, Mendable.ai could build a more cost-effective solution that is just as performant - if not more performant - than other vector databases.\n\n[Read the full story](https://supabase.com/customers/mendableai)\n\n## From the community\n\n![Community](/images/blog/2022-june/community.jpg)\n\n- FlutterFlow now supports Supabase Authentication. [Video guide](https://www.youtube.com/watch?v=tL-sLPfWzVE)\n- Supabase + ClickHouse: Combining the Best of the OLTP and OLAP Worlds. [Webinar](https://www.youtube.com/watch?v=LDWEsw41Zko)\n- Our friend Guillaume put together the most incredible course about Supabase, with the in and outs of the platform. [Full course](https://www.youtube.com/watch?v=8DTOTT7q0XA)\n- Supabase + LangChain starter template for building full stack AI apps. [Template](https://github.com/langchain-ai/langchain-template-supabase)\n- Creating a Books Tracker App with .NET MAUI and Supabase. [Article](https://hackernoon.com/creating-a-books-tracker-app-with-net-maui-and-supabase)\n  - Vanta Case Study - Supabase turns trust into a revenue-generating opportunity with Vanta. [Case Study](https://www.vanta.com/customers/supabase)\n\n## Meme Zone\n\nAs always, one of our favorite memes from last month. [Follow us on Twitter](https://twitter.com/supabase) for more.\n\n![Beta Update Meme](images/blog/2023-05-09-beta-update-april/beta-update-april-2023-meme.png)\n","title":"Supabase Beta April 2023","description":"A review of Launch Week 7 and more exciting updates from last month.","author":"ant_wilson","image":"2023-05-09-beta-update-april/monthly-update-april-2023.jpg","thumb":"2023-05-09-beta-update-april/monthly-update-april-2023.jpg","categories":["product"],"tags":["release-notes"],"date":"2023-05-09","toc_depth":3,"content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    a: \"a\",\n    h2: \"h2\",\n    img: \"img\",\n    br: \"br\",\n    code: \"code\",\n    ul: \"ul\",\n    li: \"li\"\n  }, _provideComponents(), props.components);\n  return _jsxs(_Fragment, {\n    children: [_jsx(_components.p, {\n      children: \"Brace yourself, this is one of the most feature-packed Beta Updates we've published so far.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://supabase.com/launch-week\",\n        children: \"Launch Week 7\"\n      }), \" was a massive success with great feedback from the community. We also gave away lots of mechanical keyboards, including the Hackathon winner which we announce here!\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"day-1---supabase-logs-open-source-logging-server\",\n      children: \"Day 1 - Supabase Logs: open source logging server\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2023-05-09-beta-update-april/newsletter-OG-day-1.jpg\",\n        alt: \"Day 1 - Supabase Logs: open source logging server\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Logflare is the hub of analytics streams for Supabase. We are open sourcing it so that you can self-host your own Logging infrastructure.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://supabase.com/blog/supabase-logs-self-hosted\",\n        children: \"Blog Post\"\n      }), _jsx(_components.br, {}), \"\\n\", _jsx(_components.a, {\n        href: \"https://www.youtube.com/watch?v=Ai2BjHV36Ng\",\n        children: \"Video overview\"\n      })]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"day-2---supabase-edge-runtime-self-hosted-deno-functions\",\n      children: \"Day 2 - Supabase Edge Runtime: Self-hosted Deno Functions\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2023-05-09-beta-update-april/newsletter-OG-day2.jpg\",\n        alt: \"Day 2 - Supabase Edge Runtime: Self-hosted Deno Functions\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You can now self-host Edge Functions and run them in local development using our new Edge Runtime. We published a guide showing how to self-host Edge Functions with Fly and what more is coming\", _jsx(_components.img, {\n        src: \"https://fonts.gstatic.com/s/e/notoemoji/15.0/26a1/32.png\",\n        alt: \"⚡\"\n      }), \"️\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://supabase.com/blog/edge-runtime-self-hosted-deno-functions\",\n        children: \"Blog post\"\n      }), _jsx(_components.br, {}), \"\\n\", _jsx(_components.a, {\n        href: \"https://www.youtube.com/watch?v=cPGxPl1lx4Y\",\n        children: \"Video overview\"\n      })]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"day-3---storage-v3-resumable-uploads-with-support-for-50gb-files\",\n      children: \"Day 3 - Storage v3: Resumable Uploads with support for 50GB files\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2023-05-09-beta-update-april/newsletter-OG-day3.jpg\",\n        alt: \"Day 3 - Storage v3: Resumable Uploads with support for 50GB files\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Supabase Storage received many of the most requested features from our users: Resumable Uploads, Quality Filters, Next.js support, and WebP support.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://supabase.com/blog/storage-v3-resumable-uploads\",\n        children: \"Blog post\"\n      }), _jsx(_components.br, {}), \"\\n\", _jsx(_components.a, {\n        href: \"https://www.youtube.com/watch?v=pT2PcZFq_M0\",\n        children: \"Video overview\"\n      })]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"day-4---supabase-auth-sso-mobile-and-server-side-support\",\n      children: \"Day 4 - Supabase Auth: SSO, Mobile, and Server-side support\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2023-05-09-beta-update-april/newsletter-OG-day4.jpg\",\n        alt: \"Day 4 - Supabase Auth: SSO, Mobile, and Server-side support\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"On day 4, we introduced SSO with SAML 2.0, PKCE, and Sign in with Apple for iOS. It felt like acronym day, but it was actually Auth day!\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://supabase.com/blog/supabase-auth-sso-pkce\",\n        children: \"Blog post\"\n      }), _jsx(_components.br, {}), \"\\n\", _jsx(_components.a, {\n        href: \"https://www.youtube.com/watch?v=hAwJeR6mhB0\",\n        children: \"Video overview\"\n      })]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"day-5---supabase-studio-20-with-new-ai-features\",\n      children: \"Day 5 - Supabase Studio 2.0 with new AI features\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2023-05-09-beta-update-april/newsletter-OG-day5-studio.jpg\",\n        alt: \"Day 5 - Supabase Studio 2.0 with new AI features\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Supabase Studio got a major upgrade that goes from redesigns to improved developer experience, and new tools. We have the features people have been asking for and new capabilities that will change the way you work.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://supabase.com/blog/supabase-studio-2.0\",\n        children: \"Blog Post\"\n      }), _jsx(_components.br, {}), \"\\n\", _jsx(_components.a, {\n        href: \"https://www.youtube.com/watch?v=0rcNqHt5KWU\",\n        children: \"Video overview\"\n      })]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"community-highlights\",\n      children: \"Community highlights\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2023-05-09-beta-update-april/newsletter-OG-day5-community.jpg\",\n        alt: \"Community highlights\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Our community defines us. We're honored to work with, sponsor, and support incredible people and tools 💜. Our CEO wrote a highlight of the last 3 months.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://supabase.com/blog/launch-week-7-community-highlights\",\n        children: \"Blog post\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"introducing-dbdev-postgresql-package-manager\",\n      children: \"Introducing dbdev: PostgreSQL Package Manager\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2023-05-09-beta-update-april/newsletter-OG-day5-dbdev.jpg\",\n        alt: \"dbdev: PostgreSQL Package Manager\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://database.dev/\",\n        children: \"database.dev\"\n      }), \" fills the same role for PostgreSQL as \", _jsx(_components.code, {\n        children: \"npm\"\n      }), \" for JavaScript or \", _jsx(_components.code, {\n        children: \"pip\"\n      }), \" for Python, it enables publishing libraries and applications for repeatable deployment. Our goal is to create an open ecosystem for packaging and discovering SQL.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://supabase.com/blog/dbdev\",\n        children: \"Blog post\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"more-product-announcements\",\n      children: \"More product announcements\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Trusted Language Extensions for Postgres. \", _jsx(_components.a, {\n          href: \"https://supabase.com/blog/pg-tle\",\n          children: \"[Blog post]\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"What's New in pg_graphql v1.2. \", _jsx(_components.a, {\n          href: \"https://supabase.com/blog/whats-new-in-pg-graphql-v1-2\",\n          children: \"[Blog post]\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"GitHub Discussions are now a new knowledge source for search \u0026 AI (Troubleshooting category only for now). \", _jsx(_components.a, {\n          href: \"https://supabase.com/docs\",\n          children: \"[Check it out]\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"New API report with routing information for each chart, making it easier to debug API calls. \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase/pull/14063\",\n          children: \"[PR]\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Storage permission changes: the developer role is now allowed to update the storage settings (previously was only owner and admin). \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase/pull/13883\",\n          children: \"[PR]\"\n        })]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"launch-week-7-hackathon-winners\",\n      children: \"Launch Week 7 Hackathon winners\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2023-05-09-beta-update-april/newsletter-hackathon.jpg\",\n        alt: \"Launch Week 7 Hackathon winners\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The community is loving \", _jsx(_components.code, {\n        children: \"pgvector\"\n      }), \" to build AI apps so we decided to make it part of the traditional Launch Week Hackathon. The quality of the apps was out of this world, it wasn't easy, but in the end, we selected \", _jsx(_components.a, {\n        href: \"https://github.com/n4ze3m/page-assist\",\n        children: \"Page Assist\"\n      }), \" - by \", _jsx(_components.a, {\n        href: \"https://twitter.com/n4ze3m\",\n        children: \"@n4ze3m \"\n      }), \"as the winner of the Best Overall Project.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://supabase.com/blog/launch-week-7-hackathon-winners\",\n        children: \"Full list of Winners\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://www.madewithsupabase.com/launch-week-7\",\n        children: \"See all the submissions\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"mendableai-switches-from-pinecone-to-supabase-for-postgresql-vector-embeddings\",\n      children: \"Mendable.ai switches from Pinecone to Supabase for PostgreSQL vector embeddings.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2023-05-09-beta-update-april/customer-stories-mendable.png\",\n        alt: \"Mendable logo\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"With Supabase's pg_vector, Mendable.ai could build a more cost-effective solution that is just as performant - if not more performant - than other vector databases.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://supabase.com/customers/mendableai\",\n        children: \"Read the full story\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"from-the-community\",\n      children: \"From the community\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2022-june/community.jpg\",\n        alt: \"Community\"\n      })\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"FlutterFlow now supports Supabase Authentication. \", _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=tL-sLPfWzVE\",\n          children: \"Video guide\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Supabase + ClickHouse: Combining the Best of the OLTP and OLAP Worlds. \", _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=LDWEsw41Zko\",\n          children: \"Webinar\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Our friend Guillaume put together the most incredible course about Supabase, with the in and outs of the platform. \", _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=8DTOTT7q0XA\",\n          children: \"Full course\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Supabase + LangChain starter template for building full stack AI apps. \", _jsx(_components.a, {\n          href: \"https://github.com/langchain-ai/langchain-template-supabase\",\n          children: \"Template\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Creating a Books Tracker App with .NET MAUI and Supabase. \", _jsx(_components.a, {\n          href: \"https://hackernoon.com/creating-a-books-tracker-app-with-net-maui-and-supabase\",\n          children: \"Article\"\n        }), \"\\n\", _jsxs(_components.ul, {\n          children: [\"\\n\", _jsxs(_components.li, {\n            children: [\"Vanta Case Study - Supabase turns trust into a revenue-generating opportunity with Vanta. \", _jsx(_components.a, {\n              href: \"https://www.vanta.com/customers/supabase\",\n              children: \"Case Study\"\n            })]\n          }), \"\\n\"]\n        }), \"\\n\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"meme-zone\",\n      children: \"Meme Zone\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"As always, one of our favorite memes from last month. \", _jsx(_components.a, {\n        href: \"https://twitter.com/supabase\",\n        children: \"Follow us on Twitter\"\n      }), \" for more.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"images/blog/2023-05-09-beta-update-april/beta-update-april-2023-meme.png\",\n        alt: \"Beta Update Meme\"\n      })\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"Day 1 - Supabase Logs: open source logging server","slug":"day-1---supabase-logs-open-source-logging-server","lvl":2,"i":0,"seen":0},{"content":"Day 2 - Supabase Edge Runtime: Self-hosted Deno Functions","slug":"day-2---supabase-edge-runtime-self-hosted-deno-functions","lvl":2,"i":1,"seen":0},{"content":"Day 3 - Storage v3: Resumable Uploads with support for 50GB files","slug":"day-3---storage-v3-resumable-uploads-with-support-for-50gb-files","lvl":2,"i":2,"seen":0},{"content":"Day 4 - Supabase Auth: SSO, Mobile, and Server-side support","slug":"day-4---supabase-auth-sso-mobile-and-server-side-support","lvl":2,"i":3,"seen":0},{"content":"Day 5 - Supabase Studio 2.0 with new AI features","slug":"day-5---supabase-studio-20-with-new-ai-features","lvl":2,"i":4,"seen":0},{"content":"Community highlights","slug":"community-highlights","lvl":2,"i":5,"seen":0},{"content":"Introducing dbdev: PostgreSQL Package Manager","slug":"introducing-dbdev-postgresql-package-manager","lvl":2,"i":6,"seen":0},{"content":"More product announcements","slug":"more-product-announcements","lvl":2,"i":7,"seen":0},{"content":"Launch Week 7 Hackathon winners","slug":"launch-week-7-hackathon-winners","lvl":2,"i":8,"seen":0},{"content":"Mendable.ai switches from Pinecone to Supabase for PostgreSQL vector embeddings.","slug":"mendableai-switches-from-pinecone-to-supabase-for-postgresql-vector-embeddings","lvl":2,"i":9,"seen":0},{"content":"From the community","slug":"from-the-community","lvl":2,"i":10,"seen":0},{"content":"Meme Zone","slug":"meme-zone","lvl":2,"i":11,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,2],"level":0},{"type":"inline","content":"Brace yourself, this is one of the most feature-packed Beta Updates we've published so far.","level":1,"lines":[1,2],"children":[{"type":"text","content":"Brace yourself, this is one of the most feature-packed Beta Updates we've published so far.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[3,4],"level":0},{"type":"inline","content":"[Launch Week 7](https://supabase.com/launch-week) was a massive success with great feedback from the community. We also gave away lots of mechanical keyboards, including the Hackathon winner which we announce here!","level":1,"lines":[3,4],"children":[{"type":"link_open","href":"https://supabase.com/launch-week","title":"","level":0},{"type":"text","content":"Launch Week 7","level":1},{"type":"link_close","level":0},{"type":"text","content":" was a massive success with great feedback from the community. We also gave away lots of mechanical keyboards, including the Hackathon winner which we announce here!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[5,6],"level":0},{"type":"inline","content":"[Day 1 - Supabase Logs: open source logging server](#day-1---supabase-logs-open-source-logging-server)","level":1,"lines":[5,6],"children":[{"type":"text","content":"Day 1 - Supabase Logs: open source logging server","level":0}],"lvl":2,"i":0,"seen":0,"slug":"day-1---supabase-logs-open-source-logging-server"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[7,8],"level":0},{"type":"inline","content":"![Day 1 - Supabase Logs: open source logging server](/images/blog/2023-05-09-beta-update-april/newsletter-OG-day-1.jpg)","level":1,"lines":[7,8],"children":[{"type":"image","src":"/images/blog/2023-05-09-beta-update-april/newsletter-OG-day-1.jpg","title":"","alt":"Day 1 - Supabase Logs: open source logging server","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[9,10],"level":0},{"type":"inline","content":"Logflare is the hub of analytics streams for Supabase. We are open sourcing it so that you can self-host your own Logging infrastructure.","level":1,"lines":[9,10],"children":[{"type":"text","content":"Logflare is the hub of analytics streams for Supabase. We are open sourcing it so that you can self-host your own Logging infrastructure.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[11,13],"level":0},{"type":"inline","content":"[Blog Post](https://supabase.com/blog/supabase-logs-self-hosted)\\\n[Video overview](https://www.youtube.com/watch?v=Ai2BjHV36Ng)","level":1,"lines":[11,13],"children":[{"type":"link_open","href":"https://supabase.com/blog/supabase-logs-self-hosted","title":"","level":0},{"type":"text","content":"Blog Post","level":1},{"type":"link_close","level":0},{"type":"hardbreak","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=Ai2BjHV36Ng","title":"","level":0},{"type":"text","content":"Video overview","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[14,15],"level":0},{"type":"inline","content":"[Day 2 - Supabase Edge Runtime: Self-hosted Deno Functions](#day-2---supabase-edge-runtime-self-hosted-deno-functions)","level":1,"lines":[14,15],"children":[{"type":"text","content":"Day 2 - Supabase Edge Runtime: Self-hosted Deno Functions","level":0}],"lvl":2,"i":1,"seen":0,"slug":"day-2---supabase-edge-runtime-self-hosted-deno-functions"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[16,17],"level":0},{"type":"inline","content":"![Day 2 - Supabase Edge Runtime: Self-hosted Deno Functions](/images/blog/2023-05-09-beta-update-april/newsletter-OG-day2.jpg)","level":1,"lines":[16,17],"children":[{"type":"image","src":"/images/blog/2023-05-09-beta-update-april/newsletter-OG-day2.jpg","title":"","alt":"Day 2 - Supabase Edge Runtime: Self-hosted Deno Functions","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[18,19],"level":0},{"type":"inline","content":"You can now self-host Edge Functions and run them in local development using our new Edge Runtime. We published a guide showing how to self-host Edge Functions with Fly and what more is coming![⚡](https://fonts.gstatic.com/s/e/notoemoji/15.0/26a1/32.png)️","level":1,"lines":[18,19],"children":[{"type":"text","content":"You can now self-host Edge Functions and run them in local development using our new Edge Runtime. We published a guide showing how to self-host Edge Functions with Fly and what more is coming","level":0},{"type":"image","src":"https://fonts.gstatic.com/s/e/notoemoji/15.0/26a1/32.png","title":"","alt":"⚡","level":0},{"type":"text","content":"️","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[20,22],"level":0},{"type":"inline","content":"[Blog post](https://supabase.com/blog/edge-runtime-self-hosted-deno-functions)\\\n[Video overview](https://www.youtube.com/watch?v=cPGxPl1lx4Y)","level":1,"lines":[20,22],"children":[{"type":"link_open","href":"https://supabase.com/blog/edge-runtime-self-hosted-deno-functions","title":"","level":0},{"type":"text","content":"Blog post","level":1},{"type":"link_close","level":0},{"type":"hardbreak","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=cPGxPl1lx4Y","title":"","level":0},{"type":"text","content":"Video overview","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[23,24],"level":0},{"type":"inline","content":"[Day 3 - Storage v3: Resumable Uploads with support for 50GB files](#day-3---storage-v3-resumable-uploads-with-support-for-50gb-files)","level":1,"lines":[23,24],"children":[{"type":"text","content":"Day 3 - Storage v3: Resumable Uploads with support for 50GB files","level":0}],"lvl":2,"i":2,"seen":0,"slug":"day-3---storage-v3-resumable-uploads-with-support-for-50gb-files"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[25,26],"level":0},{"type":"inline","content":"![Day 3 - Storage v3: Resumable Uploads with support for 50GB files](/images/blog/2023-05-09-beta-update-april/newsletter-OG-day3.jpg)","level":1,"lines":[25,26],"children":[{"type":"image","src":"/images/blog/2023-05-09-beta-update-april/newsletter-OG-day3.jpg","title":"","alt":"Day 3 - Storage v3: Resumable Uploads with support for 50GB files","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[27,28],"level":0},{"type":"inline","content":"Supabase Storage received many of the most requested features from our users: Resumable Uploads, Quality Filters, Next.js support, and WebP support.","level":1,"lines":[27,28],"children":[{"type":"text","content":"Supabase Storage received many of the most requested features from our users: Resumable Uploads, Quality Filters, Next.js support, and WebP support.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[29,31],"level":0},{"type":"inline","content":"[Blog post](https://supabase.com/blog/storage-v3-resumable-uploads)\\\n[Video overview](https://www.youtube.com/watch?v=pT2PcZFq_M0)","level":1,"lines":[29,31],"children":[{"type":"link_open","href":"https://supabase.com/blog/storage-v3-resumable-uploads","title":"","level":0},{"type":"text","content":"Blog post","level":1},{"type":"link_close","level":0},{"type":"hardbreak","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=pT2PcZFq_M0","title":"","level":0},{"type":"text","content":"Video overview","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[32,33],"level":0},{"type":"inline","content":"[Day 4 - Supabase Auth: SSO, Mobile, and Server-side support](#day-4---supabase-auth-sso-mobile-and-server-side-support)","level":1,"lines":[32,33],"children":[{"type":"text","content":"Day 4 - Supabase Auth: SSO, Mobile, and Server-side support","level":0}],"lvl":2,"i":3,"seen":0,"slug":"day-4---supabase-auth-sso-mobile-and-server-side-support"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[34,35],"level":0},{"type":"inline","content":"![Day 4 - Supabase Auth: SSO, Mobile, and Server-side support](/images/blog/2023-05-09-beta-update-april/newsletter-OG-day4.jpg)","level":1,"lines":[34,35],"children":[{"type":"image","src":"/images/blog/2023-05-09-beta-update-april/newsletter-OG-day4.jpg","title":"","alt":"Day 4 - Supabase Auth: SSO, Mobile, and Server-side support","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[36,37],"level":0},{"type":"inline","content":"On day 4, we introduced SSO with SAML 2.0, PKCE, and Sign in with Apple for iOS. It felt like acronym day, but it was actually Auth day!","level":1,"lines":[36,37],"children":[{"type":"text","content":"On day 4, we introduced SSO with SAML 2.0, PKCE, and Sign in with Apple for iOS. It felt like acronym day, but it was actually Auth day!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[38,40],"level":0},{"type":"inline","content":"[Blog post](https://supabase.com/blog/supabase-auth-sso-pkce)\\\n[Video overview](https://www.youtube.com/watch?v=hAwJeR6mhB0)","level":1,"lines":[38,40],"children":[{"type":"link_open","href":"https://supabase.com/blog/supabase-auth-sso-pkce","title":"","level":0},{"type":"text","content":"Blog post","level":1},{"type":"link_close","level":0},{"type":"hardbreak","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=hAwJeR6mhB0","title":"","level":0},{"type":"text","content":"Video overview","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[41,42],"level":0},{"type":"inline","content":"[Day 5 - Supabase Studio 2.0 with new AI features](#day-5---supabase-studio-20-with-new-ai-features)","level":1,"lines":[41,42],"children":[{"type":"text","content":"Day 5 - Supabase Studio 2.0 with new AI features","level":0}],"lvl":2,"i":4,"seen":0,"slug":"day-5---supabase-studio-20-with-new-ai-features"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[43,44],"level":0},{"type":"inline","content":"![Day 5 - Supabase Studio 2.0 with new AI features](/images/blog/2023-05-09-beta-update-april/newsletter-OG-day5-studio.jpg)","level":1,"lines":[43,44],"children":[{"type":"image","src":"/images/blog/2023-05-09-beta-update-april/newsletter-OG-day5-studio.jpg","title":"","alt":"Day 5 - Supabase Studio 2.0 with new AI features","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[45,46],"level":0},{"type":"inline","content":"Supabase Studio got a major upgrade that goes from redesigns to improved developer experience, and new tools. We have the features people have been asking for and new capabilities that will change the way you work.","level":1,"lines":[45,46],"children":[{"type":"text","content":"Supabase Studio got a major upgrade that goes from redesigns to improved developer experience, and new tools. We have the features people have been asking for and new capabilities that will change the way you work.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[47,49],"level":0},{"type":"inline","content":"[Blog Post](https://supabase.com/blog/supabase-studio-2.0)\\\n[Video overview](https://www.youtube.com/watch?v=0rcNqHt5KWU)","level":1,"lines":[47,49],"children":[{"type":"link_open","href":"https://supabase.com/blog/supabase-studio-2.0","title":"","level":0},{"type":"text","content":"Blog Post","level":1},{"type":"link_close","level":0},{"type":"hardbreak","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=0rcNqHt5KWU","title":"","level":0},{"type":"text","content":"Video overview","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[50,51],"level":0},{"type":"inline","content":"[Community highlights](#community-highlights)","level":1,"lines":[50,51],"children":[{"type":"text","content":"Community highlights","level":0}],"lvl":2,"i":5,"seen":0,"slug":"community-highlights"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[52,53],"level":0},{"type":"inline","content":"![Community highlights](/images/blog/2023-05-09-beta-update-april/newsletter-OG-day5-community.jpg)","level":1,"lines":[52,53],"children":[{"type":"image","src":"/images/blog/2023-05-09-beta-update-april/newsletter-OG-day5-community.jpg","title":"","alt":"Community highlights","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[54,55],"level":0},{"type":"inline","content":"Our community defines us. We're honored to work with, sponsor, and support incredible people and tools 💜. Our CEO wrote a highlight of the last 3 months.","level":1,"lines":[54,55],"children":[{"type":"text","content":"Our community defines us. We're honored to work with, sponsor, and support incredible people and tools 💜. Our CEO wrote a highlight of the last 3 months.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[56,57],"level":0},{"type":"inline","content":"[Blog post](https://supabase.com/blog/launch-week-7-community-highlights)","level":1,"lines":[56,57],"children":[{"type":"link_open","href":"https://supabase.com/blog/launch-week-7-community-highlights","title":"","level":0},{"type":"text","content":"Blog post","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[58,59],"level":0},{"type":"inline","content":"[Introducing dbdev: PostgreSQL Package Manager](#introducing-dbdev-postgresql-package-manager)","level":1,"lines":[58,59],"children":[{"type":"text","content":"Introducing dbdev: PostgreSQL Package Manager","level":0}],"lvl":2,"i":6,"seen":0,"slug":"introducing-dbdev-postgresql-package-manager"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[60,61],"level":0},{"type":"inline","content":"![dbdev: PostgreSQL Package Manager](/images/blog/2023-05-09-beta-update-april/newsletter-OG-day5-dbdev.jpg)","level":1,"lines":[60,61],"children":[{"type":"image","src":"/images/blog/2023-05-09-beta-update-april/newsletter-OG-day5-dbdev.jpg","title":"","alt":"dbdev: PostgreSQL Package Manager","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[62,63],"level":0},{"type":"inline","content":"[database.dev](https://database.dev/) fills the same role for PostgreSQL as `npm` for JavaScript or `pip` for Python, it enables publishing libraries and applications for repeatable deployment. Our goal is to create an open ecosystem for packaging and discovering SQL.","level":1,"lines":[62,63],"children":[{"type":"link_open","href":"https://database.dev/","title":"","level":0},{"type":"text","content":"database.dev","level":1},{"type":"link_close","level":0},{"type":"text","content":" fills the same role for PostgreSQL as ","level":0},{"type":"code","content":"npm","block":false,"level":0},{"type":"text","content":" for JavaScript or ","level":0},{"type":"code","content":"pip","block":false,"level":0},{"type":"text","content":" for Python, it enables publishing libraries and applications for repeatable deployment. Our goal is to create an open ecosystem for packaging and discovering SQL.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[64,65],"level":0},{"type":"inline","content":"[Blog post](https://supabase.com/blog/dbdev)","level":1,"lines":[64,65],"children":[{"type":"link_open","href":"https://supabase.com/blog/dbdev","title":"","level":0},{"type":"text","content":"Blog post","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[66,67],"level":0},{"type":"inline","content":"[More product announcements](#more-product-announcements)","level":1,"lines":[66,67],"children":[{"type":"text","content":"More product announcements","level":0}],"lvl":2,"i":7,"seen":0,"slug":"more-product-announcements"},{"type":"heading_close","hLevel":2,"level":0},{"type":"bullet_list_open","lines":[68,74],"level":0},{"type":"list_item_open","lines":[68,69],"level":1},{"type":"paragraph_open","tight":true,"lines":[68,69],"level":2},{"type":"inline","content":"Trusted Language Extensions for Postgres. [[Blog post]](https://supabase.com/blog/pg-tle)","level":3,"lines":[68,69],"children":[{"type":"text","content":"Trusted Language Extensions for Postgres. ","level":0},{"type":"link_open","href":"https://supabase.com/blog/pg-tle","title":"","level":0},{"type":"text","content":"[Blog post]","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[69,70],"level":1},{"type":"paragraph_open","tight":true,"lines":[69,70],"level":2},{"type":"inline","content":"What's New in pg_graphql v1.2. [[Blog post]](https://supabase.com/blog/whats-new-in-pg-graphql-v1-2)","level":3,"lines":[69,70],"children":[{"type":"text","content":"What's New in pg_graphql v1.2. ","level":0},{"type":"link_open","href":"https://supabase.com/blog/whats-new-in-pg-graphql-v1-2","title":"","level":0},{"type":"text","content":"[Blog post]","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[70,71],"level":1},{"type":"paragraph_open","tight":true,"lines":[70,71],"level":2},{"type":"inline","content":"GitHub Discussions are now a new knowledge source for search \u0026 AI (Troubleshooting category only for now). [[Check it out]](https://supabase.com/docs)","level":3,"lines":[70,71],"children":[{"type":"text","content":"GitHub Discussions are now a new knowledge source for search \u0026 AI (Troubleshooting category only for now). ","level":0},{"type":"link_open","href":"https://supabase.com/docs","title":"","level":0},{"type":"text","content":"[Check it out]","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[71,72],"level":1},{"type":"paragraph_open","tight":true,"lines":[71,72],"level":2},{"type":"inline","content":"New API report with routing information for each chart, making it easier to debug API calls. [[PR]](https://github.com/supabase/supabase/pull/14063)","level":3,"lines":[71,72],"children":[{"type":"text","content":"New API report with routing information for each chart, making it easier to debug API calls. ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase/pull/14063","title":"","level":0},{"type":"text","content":"[PR]","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[72,74],"level":1},{"type":"paragraph_open","tight":true,"lines":[72,73],"level":2},{"type":"inline","content":"Storage permission changes: the developer role is now allowed to update the storage settings (previously was only owner and admin). [[PR]](https://github.com/supabase/supabase/pull/13883)","level":3,"lines":[72,73],"children":[{"type":"text","content":"Storage permission changes: the developer role is now allowed to update the storage settings (previously was only owner and admin). ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase/pull/13883","title":"","level":0},{"type":"text","content":"[PR]","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":2,"lines":[74,75],"level":0},{"type":"inline","content":"[Launch Week 7 Hackathon winners](#launch-week-7-hackathon-winners)","level":1,"lines":[74,75],"children":[{"type":"text","content":"Launch Week 7 Hackathon winners","level":0}],"lvl":2,"i":8,"seen":0,"slug":"launch-week-7-hackathon-winners"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[76,77],"level":0},{"type":"inline","content":"![Launch Week 7 Hackathon winners](/images/blog/2023-05-09-beta-update-april/newsletter-hackathon.jpg)","level":1,"lines":[76,77],"children":[{"type":"image","src":"/images/blog/2023-05-09-beta-update-april/newsletter-hackathon.jpg","title":"","alt":"Launch Week 7 Hackathon winners","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[78,79],"level":0},{"type":"inline","content":"The community is loving `pgvector` to build AI apps so we decided to make it part of the traditional Launch Week Hackathon. The quality of the apps was out of this world, it wasn't easy, but in the end, we selected [Page Assist](https://github.com/n4ze3m/page-assist) - by [@n4ze3m ](https://twitter.com/n4ze3m)as the winner of the Best Overall Project.","level":1,"lines":[78,79],"children":[{"type":"text","content":"The community is loving ","level":0},{"type":"code","content":"pgvector","block":false,"level":0},{"type":"text","content":" to build AI apps so we decided to make it part of the traditional Launch Week Hackathon. The quality of the apps was out of this world, it wasn't easy, but in the end, we selected ","level":0},{"type":"link_open","href":"https://github.com/n4ze3m/page-assist","title":"","level":0},{"type":"text","content":"Page Assist","level":1},{"type":"link_close","level":0},{"type":"text","content":" - by ","level":0},{"type":"link_open","href":"https://twitter.com/n4ze3m","title":"","level":0},{"type":"text","content":"@n4ze3m ","level":1},{"type":"link_close","level":0},{"type":"text","content":"as the winner of the Best Overall Project.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[80,81],"level":0},{"type":"inline","content":"[Full list of Winners](https://supabase.com/blog/launch-week-7-hackathon-winners)","level":1,"lines":[80,81],"children":[{"type":"link_open","href":"https://supabase.com/blog/launch-week-7-hackathon-winners","title":"","level":0},{"type":"text","content":"Full list of Winners","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[82,83],"level":0},{"type":"inline","content":"[See all the submissions](https://www.madewithsupabase.com/launch-week-7)","level":1,"lines":[82,83],"children":[{"type":"link_open","href":"https://www.madewithsupabase.com/launch-week-7","title":"","level":0},{"type":"text","content":"See all the submissions","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[84,85],"level":0},{"type":"inline","content":"[Mendable.ai switches from Pinecone to Supabase for PostgreSQL vector embeddings.](#mendableai-switches-from-pinecone-to-supabase-for-postgresql-vector-embeddings)","level":1,"lines":[84,85],"children":[{"type":"text","content":"Mendable.ai switches from Pinecone to Supabase for PostgreSQL vector embeddings.","level":0}],"lvl":2,"i":9,"seen":0,"slug":"mendableai-switches-from-pinecone-to-supabase-for-postgresql-vector-embeddings"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[86,87],"level":0},{"type":"inline","content":"![Mendable logo](/images/blog/2023-05-09-beta-update-april/customer-stories-mendable.png)","level":1,"lines":[86,87],"children":[{"type":"image","src":"/images/blog/2023-05-09-beta-update-april/customer-stories-mendable.png","title":"","alt":"Mendable logo","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[88,89],"level":0},{"type":"inline","content":"With Supabase's pg_vector, Mendable.ai could build a more cost-effective solution that is just as performant - if not more performant - than other vector databases.","level":1,"lines":[88,89],"children":[{"type":"text","content":"With Supabase's pg_vector, Mendable.ai could build a more cost-effective solution that is just as performant - if not more performant - than other vector databases.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[90,91],"level":0},{"type":"inline","content":"[Read the full story](https://supabase.com/customers/mendableai)","level":1,"lines":[90,91],"children":[{"type":"link_open","href":"https://supabase.com/customers/mendableai","title":"","level":0},{"type":"text","content":"Read the full story","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[92,93],"level":0},{"type":"inline","content":"[From the community](#from-the-community)","level":1,"lines":[92,93],"children":[{"type":"text","content":"From the community","level":0}],"lvl":2,"i":10,"seen":0,"slug":"from-the-community"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[94,95],"level":0},{"type":"inline","content":"![Community](/images/blog/2022-june/community.jpg)","level":1,"lines":[94,95],"children":[{"type":"image","src":"/images/blog/2022-june/community.jpg","title":"","alt":"Community","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[96,103],"level":0},{"type":"list_item_open","lines":[96,97],"level":1},{"type":"paragraph_open","tight":true,"lines":[96,97],"level":2},{"type":"inline","content":"FlutterFlow now supports Supabase Authentication. [Video guide](https://www.youtube.com/watch?v=tL-sLPfWzVE)","level":3,"lines":[96,97],"children":[{"type":"text","content":"FlutterFlow now supports Supabase Authentication. ","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=tL-sLPfWzVE","title":"","level":0},{"type":"text","content":"Video guide","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[97,98],"level":1},{"type":"paragraph_open","tight":true,"lines":[97,98],"level":2},{"type":"inline","content":"Supabase + ClickHouse: Combining the Best of the OLTP and OLAP Worlds. [Webinar](https://www.youtube.com/watch?v=LDWEsw41Zko)","level":3,"lines":[97,98],"children":[{"type":"text","content":"Supabase + ClickHouse: Combining the Best of the OLTP and OLAP Worlds. ","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=LDWEsw41Zko","title":"","level":0},{"type":"text","content":"Webinar","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[98,99],"level":1},{"type":"paragraph_open","tight":true,"lines":[98,99],"level":2},{"type":"inline","content":"Our friend Guillaume put together the most incredible course about Supabase, with the in and outs of the platform. [Full course](https://www.youtube.com/watch?v=8DTOTT7q0XA)","level":3,"lines":[98,99],"children":[{"type":"text","content":"Our friend Guillaume put together the most incredible course about Supabase, with the in and outs of the platform. ","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=8DTOTT7q0XA","title":"","level":0},{"type":"text","content":"Full course","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[99,100],"level":1},{"type":"paragraph_open","tight":true,"lines":[99,100],"level":2},{"type":"inline","content":"Supabase + LangChain starter template for building full stack AI apps. [Template](https://github.com/langchain-ai/langchain-template-supabase)","level":3,"lines":[99,100],"children":[{"type":"text","content":"Supabase + LangChain starter template for building full stack AI apps. ","level":0},{"type":"link_open","href":"https://github.com/langchain-ai/langchain-template-supabase","title":"","level":0},{"type":"text","content":"Template","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[100,103],"level":1},{"type":"paragraph_open","tight":true,"lines":[100,101],"level":2},{"type":"inline","content":"Creating a Books Tracker App with .NET MAUI and Supabase. [Article](https://hackernoon.com/creating-a-books-tracker-app-with-net-maui-and-supabase)","level":3,"lines":[100,101],"children":[{"type":"text","content":"Creating a Books Tracker App with .NET MAUI and Supabase. ","level":0},{"type":"link_open","href":"https://hackernoon.com/creating-a-books-tracker-app-with-net-maui-and-supabase","title":"","level":0},{"type":"text","content":"Article","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"bullet_list_open","lines":[101,103],"level":2},{"type":"list_item_open","lines":[101,103],"level":3},{"type":"paragraph_open","tight":true,"lines":[101,102],"level":4},{"type":"inline","content":"Vanta Case Study - Supabase turns trust into a revenue-generating opportunity with Vanta. [Case Study](https://www.vanta.com/customers/supabase)","level":5,"lines":[101,102],"children":[{"type":"text","content":"Vanta Case Study - Supabase turns trust into a revenue-generating opportunity with Vanta. ","level":0},{"type":"link_open","href":"https://www.vanta.com/customers/supabase","title":"","level":0},{"type":"text","content":"Case Study","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":4},{"type":"list_item_close","level":3},{"type":"bullet_list_close","level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":2,"lines":[103,104],"level":0},{"type":"inline","content":"[Meme Zone](#meme-zone)","level":1,"lines":[103,104],"children":[{"type":"text","content":"Meme Zone","level":0}],"lvl":2,"i":11,"seen":0,"slug":"meme-zone"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[105,106],"level":0},{"type":"inline","content":"As always, one of our favorite memes from last month. [Follow us on Twitter](https://twitter.com/supabase) for more.","level":1,"lines":[105,106],"children":[{"type":"text","content":"As always, one of our favorite memes from last month. ","level":0},{"type":"link_open","href":"https://twitter.com/supabase","title":"","level":0},{"type":"text","content":"Follow us on Twitter","level":1},{"type":"link_close","level":0},{"type":"text","content":" for more.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[107,108],"level":0},{"type":"inline","content":"![Beta Update Meme](images/blog/2023-05-09-beta-update-april/beta-update-april-2023-meme.png)","level":1,"lines":[107,108],"children":[{"type":"image","src":"images/blog/2023-05-09-beta-update-april/beta-update-april-2023-meme.png","title":"","alt":"Beta Update Meme","level":0}]},{"type":"paragraph_close","tight":false,"level":0}],"content":"- [Day 1 - Supabase Logs: open source logging server](#day-1---supabase-logs-open-source-logging-server)\n- [Day 2 - Supabase Edge Runtime: Self-hosted Deno Functions](#day-2---supabase-edge-runtime-self-hosted-deno-functions)\n- [Day 3 - Storage v3: Resumable Uploads with support for 50GB files](#day-3---storage-v3-resumable-uploads-with-support-for-50gb-files)\n- [Day 4 - Supabase Auth: SSO, Mobile, and Server-side support](#day-4---supabase-auth-sso-mobile-and-server-side-support)\n- [Day 5 - Supabase Studio 2.0 with new AI features](#day-5---supabase-studio-20-with-new-ai-features)\n- [Community highlights](#community-highlights)\n- [Introducing dbdev: PostgreSQL Package Manager](#introducing-dbdev-postgresql-package-manager)\n- [More product announcements](#more-product-announcements)\n- [Launch Week 7 Hackathon winners](#launch-week-7-hackathon-winners)\n- [Mendable.ai switches from Pinecone to Supabase for PostgreSQL vector embeddings.](#mendableai-switches-from-pinecone-to-supabase-for-postgresql-vector-embeddings)\n- [From the community](#from-the-community)\n- [Meme Zone](#meme-zone)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-beta-update-april-2023"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>