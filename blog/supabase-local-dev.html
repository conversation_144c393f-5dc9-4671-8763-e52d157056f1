<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase Local Dev: migrations, branching, and observability</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="New features to streamline the interaction between CLI, code editors, and remote databases." data-next-head=""/><meta property="og:title" content="Supabase Local Dev: migrations, branching, and observability" data-next-head=""/><meta property="og:description" content="New features to streamline the interaction between CLI, code editors, and remote databases." data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-local-dev" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2023-08-08" data-next-head=""/><meta property="article:author" content="https://github.com/sweatybridge" data-next-head=""/><meta property="article:author" content="https://github.com/soedirgo" data-next-head=""/><meta property="article:author" content="https://github.com/mildtomato" data-next-head=""/><meta property="article:tag" content="launch-week" data-next-head=""/><meta property="article:tag" content="announcements" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/launch-week-8/day-2/OG-day2.jpg" data-next-head=""/><meta property="og:image:alt" content="Supabase Local Dev: migrations, branching, and observability thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Supabase Local Dev: migrations, branching, and observability</h1><div class="text-light flex space-x-3 text-sm"><p>08 Aug 2023</p><p>•</p><p>16 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/sweatybridge"><div class="flex items-center gap-3"><div class="w-10"><img alt="Qiao Han avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fsweatybridge.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fsweatybridge.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fsweatybridge.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Qiao Han</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/soedirgo"><div class="flex items-center gap-3"><div class="w-10"><img alt="Bobbie Soedirgo avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fsoedirgo.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fsoedirgo.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fsoedirgo.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Bobbie Soedirgo</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/mildtomato"><div class="flex items-center gap-3"><div class="w-10"><img alt="Jonny Summers-Muir avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fmildtomato.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fmildtomato.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fmildtomato.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Jonny Summers-Muir</span><span class="text-foreground-lighter mb-0 text-xs">Product Design</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Supabase Local Dev: migrations, branching, and observability" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-2%2Fthumb-day2.jpg&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-2%2Fthumb-day2.jpg&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-2%2Fthumb-day2.jpg&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-2%2Fthumb-day2.jpg&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-2%2Fthumb-day2.jpg&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-2%2Fthumb-day2.jpg&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-2%2Fthumb-day2.jpg&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-2%2Fthumb-day2.jpg&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-2%2Fthumb-day2.jpg&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-2%2Fthumb-day2.jpg&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-2%2Fthumb-day2.jpg&amp;w=3840&amp;q=100"/></div><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p>One of our goals at Supabase is to make Postgres development delightful. To do this, we need to simplify the experience between our CLI, your code editor, and the remote Postgres database powering your applications.</p>
<p>We received feedback recently about our local development experience, encouraging us to improve. This iteration introduces many new features to address that feedback. Let’s jump into a few of the features we’re launching today.</p>
<div class="video-container"><iframe class="w-full" src="https://www.youtube-nocookie.com/embed/N0Wb85m3YMI" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share"></iframe></div>
<h2 id="postgres-language-server" class="group scroll-mt-24">Postgres Language Server<a href="#postgres-language-server" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>One of the unique features of Supabase is the ability to access your Postgres database directly from a browser or mobile app through our <a href="../docs/guides/api.html">Serverless APIs</a>. This means that developers are writing more <a href="https://www.postgresql.org/docs/current/plpgsql.html">PL/pgSQL</a>.</p>
<p>While code editors have great support for most programming languages, SQL support is underwhelming. We want to make Postgres as simple as Python. Our recently announced <a href="https://github.com/supabase/postgres_lsp">Postgres Language Server</a> takes us a step in that direction - eventually it will provide first-class support for Postgres in your favorite code editor including Linting, Syntax Highlighting, Migrations Parsing, SQL Auto-complete, and Intellisense.</p>
<p>The Postgres Language Server is not ready for Production just yet. The majority of work is still ahead, but we&#x27;ve verified that the technical approach works and we&#x27;re making it public now so that we can develop it in the open with input from the community. We’re already receiving amazing <a href="https://github.com/supabase/postgres_lsp/discussions">feedback</a> and <a href="https://news.ycombinator.com/item?id=37020610">support</a>.</p>
<p>Follow the progress of the <a href="https://github.com/supabase/postgres_lsp">Postgres Language Server on GitHub</a>.</p>
<h2 id="observability-tools-for-postgres" class="group scroll-mt-24">Observability tools for Postgres<a href="#observability-tools-for-postgres" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>We’ve added <a href="https://supabase.com/docs/guides/database/inspect">debugging utilities</a> to our CLI to identify production issues via the <code class="short-inline-codeblock">supabase inspect db</code> command. This interface includes 19 different commands to help you solve everything from slow queries to redundant indexes.</p>
<p>A lot of the credit for this belongs to <a href="https://github.com/heroku/heroku-pg-extras">Heroku’s pg-extras</a> feature, an amazingly useful set of functionality. We’ve adapted the work they started, added a few additional commands, and made it available for any Postgres database. Simply append the <code class="short-inline-codeblock">--db-url</code> param to use these commands with your own Postgres database.</p>
<p>This is just a starting point for the Supabase inspector. We’ll grow this feature to become an essential part of your Postgres toolkit.</p>
<div data-state="closed"><button type="button" aria-controls="radix-:Rfob9kla6:" aria-expanded="false" data-state="closed" class=" data-[state=open]:text hover:text-foreground-light flex items-center gap-3 [&amp;&gt;svg]:fill-current [&amp;&gt;svg]:rotate-90 [&amp;&gt;svg]:transition-transform [&amp;&gt;svg]:data-[state=&#x27;open&#x27;]:rotate-180 [&amp;&gt;svg]:data-[state=&#x27;open&#x27;]:text "><svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-triangle"><path d="M13.73 4a2 2 0 0 0-3.46 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path></svg><span>See full command</span></button><div data-state="closed" id="radix-:Rfob9kla6:" hidden=""></div></div>
<div data-state="closed"><button type="button" aria-controls="radix-:Rgob9kla6:" aria-expanded="false" data-state="closed" class=" data-[state=open]:text hover:text-foreground-light flex items-center gap-3 [&amp;&gt;svg]:fill-current [&amp;&gt;svg]:rotate-90 [&amp;&gt;svg]:transition-transform [&amp;&gt;svg]:data-[state=&#x27;open&#x27;]:rotate-180 [&amp;&gt;svg]:data-[state=&#x27;open&#x27;]:text "><svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-triangle"><path d="M13.73 4a2 2 0 0 0-3.46 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path></svg><span>Read the docs</span></button><div data-state="closed" id="radix-:Rgob9kla6:" hidden=""></div></div>
<h2 id="easier-backups" class="group scroll-mt-24">Easier backups<a href="#easier-backups" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>We’ve made it even easier to backup and migrate your database, using <code class="short-inline-codeblock">supabase db dump</code>. Under the hood, this simply uses <a href="https://www.postgresql.org/docs/current/app-pgdump.html">pg_dump</a> (it&#x27;s just Postgres, after all). However we also handle a few of the hairy issues that you might need to navigate on your own, like object permissions.</p>
<div data-state="closed"><button type="button" aria-controls="radix-:Rkob9kla6:" aria-expanded="false" data-state="closed" class=" data-[state=open]:text hover:text-foreground-light flex items-center gap-3 [&amp;&gt;svg]:fill-current [&amp;&gt;svg]:rotate-90 [&amp;&gt;svg]:transition-transform [&amp;&gt;svg]:data-[state=&#x27;open&#x27;]:rotate-180 [&amp;&gt;svg]:data-[state=&#x27;open&#x27;]:text "><svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-triangle"><path d="M13.73 4a2 2 0 0 0-3.46 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path></svg><span>See full command</span></button><div data-state="closed" id="radix-:Rkob9kla6:" hidden=""></div></div>
<h2 id="improved-database-migrations" class="group scroll-mt-24">Improved Database Migrations<a href="#improved-database-migrations" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>We’ve extended the CLI migration feature and added Dashboard support. Database migrations give you a way to update your database using version-controlled SQL files. We’ve built a lot of tooling around our migrations, including <a href="https://supabase.com/docs/reference/cli/supabase-migration-repair">reparation</a>, migration cleanup using the <a href="https://supabase.com/docs/reference/cli/supabase-migration-squash">squash</a> command, and <a href="https://supabase.com/docs/reference/cli/supabase-db-diff">diffing</a> (using <a href="https://github.com/djrobstep/migra">migra</a>) to generate a new migration or to detect schema drift.</p>
<p>With the new Postgres Language Server, we hope to make it as easy to write Postgres migrations as it is to develop applications in TypeScript, Go, Python, or Rust.</p>
<p>Finally, we’ve added a Migrations view <a href="https://app.supabase.com/project/_/database/migrations">in the dashboard</a> to track your migration history to improve the discoverability of migrations.</p>
<div data-state="closed"><button type="button" aria-controls="radix-:Rqob9kla6:" aria-expanded="false" data-state="closed" class=" data-[state=open]:text hover:text-foreground-light flex items-center gap-3 [&amp;&gt;svg]:fill-current [&amp;&gt;svg]:rotate-90 [&amp;&gt;svg]:transition-transform [&amp;&gt;svg]:data-[state=&#x27;open&#x27;]:rotate-180 [&amp;&gt;svg]:data-[state=&#x27;open&#x27;]:text "><svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-triangle"><path d="M13.73 4a2 2 0 0 0-3.46 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path></svg><span>See full command</span></button><div data-state="closed" id="radix-:Rqob9kla6:" hidden=""></div></div>
<h2 id="test-and-lint-your-database" class="group scroll-mt-24">Test and lint your database<a href="#test-and-lint-your-database" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>We’ve simplified the database testing experience, with <code class="short-inline-codeblock">supabase test</code>. Running <code class="short-inline-codeblock">supabase test new</code> stubs out a <a href="https://pgtap.org/">pgTAP test</a> for you to fill with testing logic. The CLI includes <a href="https://pgtap.org/pg_prove">pg_prove and the TAP harness</a>, so all you need to do is run <code class="short-inline-codeblock">supabase test db</code>.</p>
<p>To make life even easier, our friends at <a href="https://usebasejump.com/">Basejump</a> have created an entire suite of <a href="https://github.com/usebasejump/supabase-test-helpers">Supabase Test Helpers</a> which make it simple to create users, run tests as an <a href="https://usebasejump.com/blog/testing-on-supabase-with-pgtap#testing-authenticated">authenticated user</a>, and test your <a href="https://usebasejump.com/blog/testing-on-supabase-with-pgtap#rls-testing">RLS policies</a>.</p>
<p>Finally, while you wait for us to make progress on the Language Server, we’ve added <a href="https://supabase.com/docs/guides/cli/testing-and-linting#linting-your-database">support for linting</a> through the excellent <a href="https://github.com/okbob/plpgsql_check">plpgsql_check</a> extension.</p>
<div data-state="closed"><button type="button" aria-controls="radix-:R10ob9kla6:" aria-expanded="false" data-state="closed" class=" data-[state=open]:text hover:text-foreground-light flex items-center gap-3 [&amp;&gt;svg]:fill-current [&amp;&gt;svg]:rotate-90 [&amp;&gt;svg]:transition-transform [&amp;&gt;svg]:data-[state=&#x27;open&#x27;]:rotate-180 [&amp;&gt;svg]:data-[state=&#x27;open&#x27;]:text "><svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-triangle"><path d="M13.73 4a2 2 0 0 0-3.46 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path></svg><span>See full command</span></button><div data-state="closed" id="radix-:R10ob9kla6:" hidden=""></div></div>
<h2 id="database-seeding" class="group scroll-mt-24">Database seeding<a href="#database-seeding" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>Seeding is the process of populating a database with initial data, typically used to provide sample or default records for testing and development purposes. This gives you a reproducible development environment across your entire team.</p>
<p>We’ve added <a href="https://supabase.com/docs/guides/cli/seeding-your-database">support for seeding</a> to populate your local databases with data whenever you run <code class="short-inline-codeblock">supabase start</code> or <code class="short-inline-codeblock">supabase db reset</code>.</p>
<p>We’ve also worked with our friends at Snaplet to <a href="https://supabase.com/docs/guides/cli/seeding-your-database#generating-seed-data">generate seed data</a> directly from your database:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>npx snaplet generate --sql &gt; supabase/seed.sql</span></div></div><br/></code></div></div>
<h2 id="type-generators" class="group scroll-mt-24">Type generators<a href="#type-generators" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>Type generators introspect a PostgreSQL schema and automatically generate TypeScript definitions. This gives you <a href="https://www.youtube.com/watch?v=VSNgAIObBdw">end-to-end type safety</a> from the database to the browser.</p>
<p>In the past month, we&#x27;ve added relationship detection in supabase-js. Foreign keys are now included in the generated types so that supabase-js can detect whether a referenced table should be an array (one-to-many) or an object (many-to-one). We&#x27;ve also added Helper Types to improve the developer experience for common scenarios, like short-hand accessors:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>// Before</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>let movie: Database[&#x27;public&#x27;][&#x27;Tables&#x27;][&#x27;movies&#x27;][&#x27;Row&#x27;] = // ...</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>// After</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>let movie: Tables&lt;&#x27;movies&#x27;&gt; = // ...</span></div></div><br/></code></div></div>
<div data-state="closed"><button type="button" aria-controls="radix-:R1cob9kla6:" aria-expanded="false" data-state="closed" class=" data-[state=open]:text hover:text-foreground-light flex items-center gap-3 [&amp;&gt;svg]:fill-current [&amp;&gt;svg]:rotate-90 [&amp;&gt;svg]:transition-transform [&amp;&gt;svg]:data-[state=&#x27;open&#x27;]:rotate-180 [&amp;&gt;svg]:data-[state=&#x27;open&#x27;]:text "><svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-triangle"><path d="M13.73 4a2 2 0 0 0-3.46 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path></svg><span>See full command</span></button><div data-state="closed" id="radix-:R1cob9kla6:" hidden=""></div></div>
<h2 id="official-github-action" class="group scroll-mt-24">Official GitHub Action<a href="#official-github-action" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>We’ve developed an official <a href="https://github.com/marketplace/actions/supabase-cli-action">GitHub Action</a> which leverages the CLI. You can <a href="https://supabase.com/docs/guides/cli/github-action/generating-types">generate types on every PR</a>, or run <a href="https://supabase.com/docs/guides/cli/github-action/testing">your tests on every commit</a>.</p>
<h2 id="local-logging-and-debugging" class="group scroll-mt-24">Local Logging and Debugging<a href="#local-logging-and-debugging" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Logs are now accessible locally in the Dashboard. Last launch week we released an open source logging server, with support for BigQuery. In the past few months we’ve added Postgres support to this server. This means that all of your local logs are accessible with no additional config - simply run supabase start and then visit the local dashboard to start debugging.</p>
<p></p>
<h2 id="stable-releases" class="group scroll-mt-24">Stable releases<a href="#stable-releases" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We’ve moved the Supabase CLI to a fortnightly stable-release cycle. Every 2 weeks, we will update the <a href="https://www.npmjs.com/package/supabase?activeTab=versions"><code class="short-inline-codeblock">latest</code></a> tag on npm, the <a href="https://github.com/supabase/homebrew-tap/blob/main/supabase.rb"><code class="short-inline-codeblock">supabase/tap</code></a> for homebrew, and the <a href="https://github.com/supabase/homebrew-tap/blob/main/supabase.rb"><code class="short-inline-codeblock">supabase</code></a> scoop bucket. You can find the binary downloads in our GitHub <a href="https://github.com/supabase/cli/releases/latest">latest release</a>.</p>
<p>For the adventurous feature hunters, we’ve added a <code class="short-inline-codeblock">beta</code> release channel for the CLI, with new releases on every PR merged. You can follow <a href="https://github.com/supabase/cli#install-the-cli">this guide</a> to install Supabase CLI (beta).</p>
<h2 id="branching-and-preview-environments" class="group scroll-mt-24">Branching and Preview Environments<a href="#branching-and-preview-environments" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>And finally, probably our most anticipated feature - branching:</p>
<p></p>
<p>We’ve made major improvements to our local development with the features above - but we have bigger ambitions. For several months we’ve been developing Supabase branching and today we&#x27;re opening it up for alpha testers.</p>
<p>Supabase isn’t simply a database, it’s an entire backend - everything from your Postgres database to your <a href="https://supabase.com/blog/storage-v3-resumable-uploads">50GB videos</a>. Branching improves the experience of managing environments so that developers and teams spend less time on DevOps and more time building.</p>
<h3 id="supabase-branching-is-hard" class="group scroll-mt-24">Supabase branching is hard<a href="#supabase-branching-is-hard" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Every project is a Postgres database, wrapped in a suite of tools like <a href="../auth.html">Auth</a>, <a href="../storage.html">Storage</a>, <a href="../edge-functions.html">Edge Functions</a>, <a href="../realtime.html">Realtime</a> and <a href="https://supabase.com/vector">Vectors</a>, and encompassed by <a href="../docs/guides/api.html">API middleware</a> and <a href="https://supabase.com/blog/supabase-logs-self-hosted">logs</a>.</p>
<p>A good branching solution requires each tool to provide multi-tenancy support so that:</p>
<ol>
<li>Data can be isolated from production for security.</li>
<li>Compute can be isolated from each other to avoid noisy-neighbors.</li>
</ol>
<h3 id="how-does-branching-work" class="group scroll-mt-24">How does branching work?<a href="#how-does-branching-work" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We use Git to bridge the gap between your local development environment and your hosted database. For now, we’ve focused on GitHub.</p>
<p>Every time you open a new Pull Request on GitHub, a corresponding “Preview Environment” is created. Each preview branch is an isolated Firecracker instance that pauses automatically after a period of inactivity. Every time a change is pushed to GitHub, the migrations within the <code class="short-inline-codeblock">./supabase/migrations</code> folder are run against the Preview Branch so that your entire team is working from the same source of truth.</p>
<p>When you hit merge on your Pull Request we run the migrations on your Production database.</p>
<h3 id="what-about-data" class="group scroll-mt-24">What about data?<a href="#what-about-data" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We’re starting with seed data. Any SQL with <code class="short-inline-codeblock">./supabase/seed.sql</code> will populate your Preview Branch. This provides your entire team with an isolated and reproducible environment, safe from any data privacy concerns.</p>
<p>Importantly, we <em>aren’t</em> cloning production data until we find something appropriate for data security. We know that copy-on-write is an available option, and with the appropriate anonymization techniques it seems like a promising way to provide a “production-like” test environment.</p>
<p>We’ll also need to figure out what this means for large files in Supabase Storage. Do you need to anonymize your photos and videos? This is a work in progress and we’re open to feedback.</p>
<p>Either way, we want to support <em>both</em> seed data and anonymized production data, so that teams can choose their preference based on their risk profile. It makes sense to start with a seed.</p>
<h3 id="is-it-available-yet" class="group scroll-mt-24">Is it available yet?<a href="#is-it-available-yet" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<div role="alert" class="relative w-full text-sm rounded-lg p-4 [&amp;&gt;svg~*]:pl-10 [&amp;&gt;svg+div]:translate-y-[-3px] [&amp;&gt;svg]:absolute [&amp;&gt;svg]:left-4 [&amp;&gt;svg]:top-4 [&amp;&gt;svg]:w-[23px] [&amp;&gt;svg]:h-[23px] [&amp;&gt;svg]:p-1 [&amp;&gt;svg]:flex [&amp;&gt;svg]:rounded text-foreground [&amp;&gt;svg]:text-background mb-2 [&amp;&gt;svg]:bg-foreground-muted bg-surface-200/25 border border-default"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 21 20" class="w-6 h-6" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M0.625 9.8252C0.625 4.44043 4.99023 0.0751953 10.375 0.0751953C15.7598 0.0751953 20.125 4.44043 20.125 9.8252C20.125 15.21 15.7598 19.5752 10.375 19.5752C4.99023 19.5752 0.625 15.21 0.625 9.8252ZM9.3584 4.38135C9.45117 4.28857 9.55518 4.20996 9.66699 4.14648C9.88086 4.02539 10.1245 3.96045 10.375 3.96045C10.5845 3.96045 10.7896 4.00586 10.9766 4.09229C11.1294 4.1626 11.2705 4.26025 11.3916 4.38135C11.6611 4.65088 11.8125 5.0166 11.8125 5.39795C11.8125 5.5249 11.7959 5.6499 11.7637 5.77002C11.6987 6.01172 11.5718 6.23438 11.3916 6.41455C11.1221 6.68408 10.7563 6.83545 10.375 6.83545C9.99365 6.83545 9.62793 6.68408 9.3584 6.41455C9.08887 6.14502 8.9375 5.7793 8.9375 5.39795C8.9375 5.29492 8.94873 5.19287 8.97021 5.09375C9.02783 4.82568 9.16162 4.57812 9.3584 4.38135ZM10.375 15.6899C10.0933 15.6899 9.82275 15.5781 9.62354 15.3789C9.42432 15.1797 9.3125 14.9092 9.3125 14.6274V9.31494C9.3125 9.0332 9.42432 8.7627 9.62354 8.56348C9.82275 8.36426 10.0933 8.25244 10.375 8.25244C10.6567 8.25244 10.9272 8.36426 11.1265 8.56348C11.3257 8.7627 11.4375 9.0332 11.4375 9.31494V14.6274C11.4375 14.7944 11.3979 14.9575 11.3242 15.104C11.2739 15.2046 11.2075 15.2979 11.1265 15.3789C10.9272 15.5781 10.6567 15.6899 10.375 15.6899Z"></path></svg><h5 class="mb-1 text mt-0.5 flex gap-3 text-sm [&amp;_p]:mb-1.5 [&amp;_p]:mt-0">Branching update 12/13/2023</h5><div class="text-sm [&amp;_p]:leading-relaxed text-foreground-light font-normal [&amp;_p]:mb-1.5 [&amp;_p]:mt-0"><p>We are <a href="supabase-branching.html">rolling out access</a> and we&#x27;ll be onboarding organizations in batches over the next few weeks. You can still <a href="https://forms.supabase.com/branching-request">sign up for access</a>.</p></div></div>
<h2 id="get-started" class="group scroll-mt-24">Get started<a href="#get-started" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Jump into our updated <a href="https://supabase.com/docs/guides/cli">Local Development documentation</a> to get started with the CLI.</p>
<p>If you’re an existing user simply <a href="https://supabase.com/docs/guides/cli/getting-started#updating-the-supabase-cli">update your CLI</a> and check out the <a href="../docs/reference/cli/introduction.html">full command reference</a> for all the latest commands.</p>
<h2 id="more-launch-week-8" class="group scroll-mt-24">More Launch Week 8<a href="#more-launch-week-8" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ul>
<li><a href="https://supabase.com/blog/hugging-face-supabase">Hugging Face is now supported in Supabase</a></li>
<li><a href="../launch-week.html">Launch Week 8</a></li>
<li><a href="https://supabase.com/blog/interactive-constellation-threejs-react-three-fiber">Coding the stars - an interactive constellation with Three.js and React Three Fiber</a></li>
<li><a href="https://supabase.com/blog/why-supabase-remote">Why we&#x27;ll stay remote</a></li>
<li><a href="https://github.com/supabase/postgres_lsp">Postgres Language Server</a></li>
</ul></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-local-dev&amp;text=Supabase%20Local%20Dev%3A%20migrations%2C%20branching%2C%20and%20observability"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-local-dev&amp;text=Supabase%20Local%20Dev%3A%20migrations%2C%20branching%2C%20and%20observability"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-local-dev&amp;t=Supabase%20Local%20Dev%3A%20migrations%2C%20branching%2C%20and%20observability"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="supabase-studio-3-0.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Supabase Studio 3.0: AI SQL Editor, Schema Diagrams, and new Wrappers</h4><p class="small">9 August 2023</p></div></div></div></div></a></div><div><a href="https://supabase.com/blog/hugging-face-supabase"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Hugging Face is now supported in Supabase</h4><p class="small">7 August 2023</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/launch-week"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">launch-week</div></a><a href="https://supabase.com/blog/tags/announcements"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">announcements</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#postgres-language-server">Postgres Language Server</a></li>
<li><a href="#observability-tools-for-postgres">Observability tools for Postgres</a></li>
<li><a href="#easier-backups">Easier backups</a></li>
<li><a href="#improved-database-migrations">Improved Database Migrations</a></li>
<li><a href="#test-and-lint-your-database">Test and lint your database</a></li>
<li><a href="#database-seeding">Database seeding</a></li>
<li><a href="#type-generators">Type generators</a></li>
<li><a href="#official-github-action">Official GitHub Action</a></li>
<li><a href="#local-logging-and-debugging">Local Logging and Debugging</a></li>
<li><a href="#stable-releases">Stable releases</a></li>
<li><a href="#branching-and-preview-environments">Branching and Preview Environments</a>
<ul>
<li><a href="#supabase-branching-is-hard">Supabase branching is hard</a></li>
<li><a href="#how-does-branching-work">How does branching work?</a></li>
<li><a href="#what-about-data">What about data?</a></li>
<li><a href="#is-it-available-yet">Is it available yet?</a></li>
</ul>
</li>
<li><a href="#get-started">Get started</a></li>
<li><a href="#more-launch-week-8">More Launch Week 8</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-local-dev&amp;text=Supabase%20Local%20Dev%3A%20migrations%2C%20branching%2C%20and%20observability"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-local-dev&amp;text=Supabase%20Local%20Dev%3A%20migrations%2C%20branching%2C%20and%20observability"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-local-dev&amp;t=Supabase%20Local%20Dev%3A%20migrations%2C%20branching%2C%20and%20observability"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"supabase-studio-3-0","title":"Supabase Studio 3.0: AI SQL Editor, Schema Diagrams, and new Wrappers","description":"Supabase Studio now comes with an AI assisted SQL Editor, schema diagrams, and much more.","launchweek":"8","categories":["product"],"tags":["launch-week","studio","AI"],"date":"2023-08-09","toc_depth":3,"author":"alaister,gregnr,joshenlim","image":"launch-week-8/day-3/og-day3.jpg","thumb":"launch-week-8/day-3/thumb-day3.jpg","formattedDate":"9 August 2023","readingTime":"8 minute read","url":"/blog/supabase-studio-3-0","path":"/blog/supabase-studio-3-0"},"nextPost":{"slug":"hugging-face-supabase","title":"Hugging Face is now supported in Supabase","description":"We've added support Hugging Face support in our Python Vector Client and Edge Functions.","launchweek":"8","categories":["product"],"tags":["launch-week","announcements"],"date":"2023-08-07","toc_depth":3,"author":"gregnr,oli_rice","image":"launch-week-8/day-1/hugging-face-supabase-og.jpg","thumb":"launch-week-8/day-1/hugging-face-supabase-thumb.jpg","formattedDate":"7 August 2023","readingTime":"10 minute read","url":"/blog/hugging-face-supabase","path":"/blog/hugging-face-supabase"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-local-dev","source":"\nOne of our goals at Supabase is to make Postgres development delightful. To do this, we need to simplify the experience between our CLI, your code editor, and the remote Postgres database powering your applications.\n\nWe received feedback recently about our local development experience, encouraging us to improve. This iteration introduces many new features to address that feedback. Let’s jump into a few of the features we’re launching today.\n\n\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/N0Wb85m3YMI\"\n    title=\"YouTube video player\"\n    frameborder=\"0\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"\n    allowfullscreen\n  \u003e\u003c/iframe\u003e\n\u003c/div\u003e\n\n## Postgres Language Server\n\n![Postgres Language Server](/images/blog/launch-week-8/day-2/postgres-language-server.png)\n\nOne of the unique features of Supabase is the ability to access your Postgres database directly from a browser or mobile app through our [Serverless APIs](https://supabase.com/docs/guides/api). This means that developers are writing more [PL/pgSQL](https://www.postgresql.org/docs/current/plpgsql.html).\n\nWhile code editors have great support for most programming languages, SQL support is underwhelming. We want to make Postgres as simple as Python. Our recently announced [Postgres Language Server](https://github.com/supabase/postgres_lsp) takes us a step in that direction - eventually it will provide first-class support for Postgres in your favorite code editor including Linting, Syntax Highlighting, Migrations Parsing, SQL Auto-complete, and Intellisense.\n\nThe Postgres Language Server is not ready for Production just yet. The majority of work is still ahead, but we've verified that the technical approach works and we're making it public now so that we can develop it in the open with input from the community. We’re already receiving amazing [feedback](https://github.com/supabase/postgres_lsp/discussions) and [support](https://news.ycombinator.com/item?id=37020610).\n\nFollow the progress of the [Postgres Language Server on GitHub](https://github.com/supabase/postgres_lsp).\n\n## Observability tools for Postgres\n\n![Inspect your database](/images/blog/launch-week-8/day-2/inspect-your-database.png)\n\nWe’ve added [debugging utilities](https://supabase.com/docs/guides/database/inspect) to our CLI to identify production issues via the `supabase inspect db` command. This interface includes 19 different commands to help you solve everything from slow queries to redundant indexes.\n\nA lot of the credit for this belongs to [Heroku’s pg-extras](https://github.com/heroku/heroku-pg-extras) feature, an amazingly useful set of functionality. We’ve adapted the work they started, added a few additional commands, and made it available for any Postgres database. Simply append the `--db-url` param to use these commands with your own Postgres database.\n\nThis is just a starting point for the Supabase inspector. We’ll grow this feature to become an essential part of your Postgres toolkit.\n\n\u003cBlogCollapsible title=\"See full command\"\u003e\n\n    {/* prettier-ignore */}\n    ```markdown\n    supabase inspect db --help\n    Tools to inspect your Supabase database\n\n    Usage:\n      supabase inspect db [command]\n\n    Available Commands:\n      bloat                Estimates space allocated to a relation that is full of dead tuples\n      blocking             Show queries that are holding locks and the queries that are waiting for them to be released\n      cache-hit            Show cache hit rates for tables and indices\n      calls                Show queries from pg_stat_statements ordered by total times called\n      index-sizes          Show index sizes of individual indexes\n      index-usage          Show information about the efficiency of indexes\n      locks                Show queries which have taken out an exclusive lock on a relation\n      long-running-queries Show currently running queries running for longer than 5 minutes\n      outliers             Show queries from pg_stat_statements ordered by total execution time\n      replication-slots    Show information about replication slots on the database\n      role-connections     Show number of active connections for all database roles\n      seq-scans            Show number of sequential scans recorded against all tables\n      table-index-sizes    Show index sizes of individual tables\n      table-record-counts  Show estimated number of rows per table\n      table-sizes          Show table sizes of individual tables without their index sizes\n      total-index-size     Show total size of all indexes\n      total-table-sizes    Show total table sizes, including table index sizes\n      unused-indexes       Show indexes with low usage\n      vacuum-stats         Show statistics related to vacuum operations per table\n    ```\n\n\u003c/BlogCollapsible\u003e\n\n\u003cBlogCollapsible title=\"Read the docs\"\u003e\n\n{/* prettier-ignore */}\n  \u003cdiv\u003e\n    - [Show most frequently run queries](https://supabase.com/docs/reference/cli/supabase-inspect-db-calls)\n    - [Show long running queries](https://supabase.com/docs/reference/cli/supabase-inspect-db-long-running-queries)\n    - [Show queries ordered by total execution time](https://supabase.com/docs/reference/cli/supabase-inspect-db-outliers)\n    - [Show queries waiting and holding locks](https://supabase.com/docs/reference/cli/supabase-inspect-db-blocking)\n    - [Show queries taking exclusive locks](https://supabase.com/docs/reference/cli/supabase-inspect-db-locks)\n    - [Show total size of all indexes](https://supabase.com/docs/reference/cli/supabase-inspect-db-total-index-size)\n    - [Show sizes of individual indexes](https://supabase.com/docs/reference/cli/supabase-inspect-db-index-sizes)\n    - [Show information about index efficiency](https://supabase.com/docs/reference/cli/supabase-inspect-db-index-usage)\n    - [Show indexes with low usage](https://supabase.com/docs/reference/cli/supabase-inspect-db-unused-indexes)\n    - [Show total size of all tables](https://supabase.com/docs/reference/cli/supabase-inspect-db-total-table-sizes)\n    - [Show sizes of individual tables](https://supabase.com/docs/reference/cli/supabase-inspect-db-table-sizes)\n    - [Show index sizes of individual tables](https://supabase.com/docs/reference/cli/supabase-inspect-db-table-index-sizes)\n    - [Show cache hit rates for tables and indices](https://supabase.com/docs/reference/cli/supabase-inspect-db-cache-hit)\n    - [Show estimated number of rows per table](https://supabase.com/docs/reference/cli/supabase-inspect-db-table-record-counts)\n    - [Show number of sequential scans for all tables](https://supabase.com/docs/reference/cli/supabase-inspect-db-seq-scans)\n    - [Show information about replication slots](https://supabase.com/docs/reference/cli/supabase-inspect-db-replication-slots)\n    - [Show number of active connections](https://supabase.com/docs/reference/cli/supabase-inspect-db-role-connections)\n    - [Show estimated database bloat](https://supabase.com/docs/reference/cli/supabase-inspect-db-bloat)\n    - [Show statistics related to vacuum operations](https://supabase.com/docs/reference/cli/supabase-inspect-db-vacuum-stats)\n  \u003c/div\u003e\n\n\u003c/BlogCollapsible\u003e\n\n## Easier backups\n\n![How to back up your database with Supabase CLI](/images/blog/launch-week-8/day-2/backup-your-database.png)\n\nWe’ve made it even easier to backup and migrate your database, using `supabase db dump`. Under the hood, this simply uses [pg_dump](https://www.postgresql.org/docs/current/app-pgdump.html) (it's just Postgres, after all). However we also handle a few of the hairy issues that you might need to navigate on your own, like object permissions.\n\n\u003cBlogCollapsible title=\"See full command\"\u003e\n  \u003cdiv\u003e\n\n    {/* prettier-ignore */}\n    ```markdown\n    supabase db dump --help\n    Dumps data or schemas from the remote database\n\n    Usage:\n    supabase db dump [flags]\n\n    Flags:\n    --data-only Dumps only data records.\n    -f, --file string File path to save the dumped contents.\n    --keep-comments Keeps commented lines from pg_dump output.\n    --role-only Dumps only cluster roles.\n    --use-copy Uses copy statements in place of inserts.\n    ```\n\n  \u003c/div\u003e\n\u003c/BlogCollapsible\u003e\n\n## Improved Database Migrations\n\n![modify your database](/images/blog/launch-week-8/day-2/modify-your-database.png)\n\nWe’ve extended the CLI migration feature and added Dashboard support. Database migrations give you a way to update your database using version-controlled SQL files. We’ve built a lot of tooling around our migrations, including [reparation](https://supabase.com/docs/reference/cli/supabase-migration-repair), migration cleanup using the [squash](https://supabase.com/docs/reference/cli/supabase-migration-squash) command, and [diffing](https://supabase.com/docs/reference/cli/supabase-db-diff) (using [migra](https://github.com/djrobstep/migra)) to generate a new migration or to detect schema drift.\n\nWith the new Postgres Language Server, we hope to make it as easy to write Postgres migrations as it is to develop applications in TypeScript, Go, Python, or Rust.\n\nFinally, we’ve added a Migrations view [in the dashboard](https://app.supabase.com/project/_/database/migrations) to track your migration history to improve the discoverability of migrations.\n\n\u003cBlogCollapsible title=\"See full command\"\u003e\n  \u003cdiv\u003e\n\n    {/* prettier-ignore */}\n    ```markdown\n    supabase migration\n    Manage database migration scripts\n\n    Usage:\n      supabase migration [command]\n\n    Available Commands:\n      list        List local and remote migrations\n      new         Create an empty migration script\n      repair      Repair the migration history table\n      squash      Squash migrations to a single file\n      up          Apply pending migrations to local database\n    ```\n\n  \u003c/div\u003e\n\u003c/BlogCollapsible\u003e\n\n## Test and lint your database\n\n![Test your database](/images/blog/launch-week-8/day-2/test-your-database.png)\n\nWe’ve simplified the database testing experience, with `supabase test`. Running `supabase test new` stubs out a [pgTAP test](https://pgtap.org/) for you to fill with testing logic. The CLI includes [pg_prove and the TAP harness](https://pgtap.org/pg_prove), so all you need to do is run `supabase test db`.\n\nTo make life even easier, our friends at [Basejump](https://usebasejump.com/) have created an entire suite of [Supabase Test Helpers](https://github.com/usebasejump/supabase-test-helpers) which make it simple to create users, run tests as an [authenticated user](https://usebasejump.com/blog/testing-on-supabase-with-pgtap#testing-authenticated), and test your [RLS policies](https://usebasejump.com/blog/testing-on-supabase-with-pgtap#rls-testing).\n\nFinally, while you wait for us to make progress on the Language Server, we’ve added [support for linting](https://supabase.com/docs/guides/cli/testing-and-linting#linting-your-database) through the excellent [plpgsql_check](https://github.com/okbob/plpgsql_check) extension.\n\n\u003cBlogCollapsible title=\"See full command\"\u003e\n  \u003cdiv\u003e\n\n    {/* prettier-ignore */}\n    ```markdown\n    supabase test\n    Run tests on local Supabase containers\n\n    Usage:\n      supabase test [command]\n\n    Available Commands:\n      db          Tests local database with pgTAP\n      new         Create a new test file\n    ````\n\n    {/* prettier-ignore */}\n    ```markdown\n    supabase db lint\n    Checks local database for typing error\n\n    Usage:\n      supabase db lint [flags]\n\n    Flags:\n      -h, --help                        help for lint\n          --level [ warning | error ]   Error level to emit. (default warning)\n          --linked                      Lints the linked project for schema errors.\n      -s, --schema strings              List of schema to include. (default all)\n    ```\n\n  \u003c/div\u003e\n\u003c/BlogCollapsible\u003e\n\n## Database seeding\n\n![Support for seeding](/images/blog/launch-week-8/day-2/support-for-seeding.png)\n\nSeeding is the process of populating a database with initial data, typically used to provide sample or default records for testing and development purposes. This gives you a reproducible development environment across your entire team.\n\nWe’ve added [support for seeding](https://supabase.com/docs/guides/cli/seeding-your-database) to populate your local databases with data whenever you run `supabase start` or `supabase db reset`.\n\nWe’ve also worked with our friends at Snaplet to [generate seed data](https://supabase.com/docs/guides/cli/seeding-your-database#generating-seed-data) directly from your database:\n\n```markdown\nnpx snaplet generate --sql \u003e supabase/seed.sql\n```\n\n## Type generators\n\n![End to end Type Safety](/images/blog/launch-week-8/day-2/end-to-end-type-safety.png)\n\nType generators introspect a PostgreSQL schema and automatically generate TypeScript definitions. This gives you [end-to-end type safety](https://www.youtube.com/watch?v=VSNgAIObBdw) from the database to the browser.\n\nIn the past month, we've added relationship detection in supabase-js. Foreign keys are now included in the generated types so that supabase-js can detect whether a referenced table should be an array (one-to-many) or an object (many-to-one). We've also added Helper Types to improve the developer experience for common scenarios, like short-hand accessors:\n\n```tsx\n// Before\nlet movie: Database['public']['Tables']['movies']['Row'] = // ...\n\n// After\nlet movie: Tables\u003c'movies'\u003e = // ...\n```\n\n\u003cBlogCollapsible title=\"See full command\"\u003e\n  \u003cdiv\u003e\n\n    {/* prettier-ignore */}\n    ```markdown\n    supabase gen types\n    Generate types from Postgres schema\n\n    Usage:\n      supabase gen types [command]\n\n    Available Commands:\n      typescript  Generate types for TypeScript\n    ```\n\n  \u003c/div\u003e\n\u003c/BlogCollapsible\u003e\n\n## Official GitHub Action\n\n![Support for GitHub Actions](/images/blog/launch-week-8/day-2/support-for-github-actions.png)\n\nWe’ve developed an official [GitHub Action](https://github.com/marketplace/actions/supabase-cli-action) which leverages the CLI. You can [generate types on every PR](https://supabase.com/docs/guides/cli/github-action/generating-types), or run [your tests on every commit](https://supabase.com/docs/guides/cli/github-action/testing).\n\n## Local Logging and Debugging\n\nLogs are now accessible locally in the Dashboard. Last launch week we released an open source logging server, with support for BigQuery. In the past few months we’ve added Postgres support to this server. This means that all of your local logs are accessible with no additional config - simply run supabase start and then visit the local dashboard to start debugging.\n\n![Logs are now accessible locally in the dashboard](/images/blog/launch-week-8/day-2/local-logging-debugging.png)\n\n## Stable releases\n\nWe’ve moved the Supabase CLI to a fortnightly stable-release cycle. Every 2 weeks, we will update the [`latest`](https://www.npmjs.com/package/supabase?activeTab=versions) tag on npm, the [`supabase/tap`](https://github.com/supabase/homebrew-tap/blob/main/supabase.rb) for homebrew, and the [`supabase`](https://github.com/supabase/homebrew-tap/blob/main/supabase.rb) scoop bucket. You can find the binary downloads in our GitHub [latest release](https://github.com/supabase/cli/releases/latest).\n\nFor the adventurous feature hunters, we’ve added a `beta` release channel for the CLI, with new releases on every PR merged. You can follow [this guide](https://github.com/supabase/cli#install-the-cli) to install Supabase CLI (beta).\n\n## Branching and Preview Environments\n\nAnd finally, probably our most anticipated feature - branching:\n\n![Searching branches with Supabase](/images/blog/launch-week-8/day-2/branching-02.png)\n\nWe’ve made major improvements to our local development with the features above - but we have bigger ambitions. For several months we’ve been developing Supabase branching and today we're opening it up for alpha testers.\n\nSupabase isn’t simply a database, it’s an entire backend - everything from your Postgres database to your [50GB videos](https://supabase.com/blog/storage-v3-resumable-uploads). Branching improves the experience of managing environments so that developers and teams spend less time on DevOps and more time building.\n\n### Supabase branching is hard\n\nEvery project is a Postgres database, wrapped in a suite of tools like [Auth](https://supabase.com/auth), [Storage](https://supabase.com/storage), [Edge Functions](https://supabase.com/edge-functions), [Realtime](https://supabase.com/realtime) and [Vectors](https://supabase.com/vector), and encompassed by [API middleware](https://supabase.com/docs/guides/api) and [logs](https://supabase.com/blog/supabase-logs-self-hosted).\n\nA good branching solution requires each tool to provide multi-tenancy support so that:\n\n1. Data can be isolated from production for security.\n2. Compute can be isolated from each other to avoid noisy-neighbors.\n\n### How does branching work?\n\nWe use Git to bridge the gap between your local development environment and your hosted database. For now, we’ve focused on GitHub.\n\nEvery time you open a new Pull Request on GitHub, a corresponding “Preview Environment” is created. Each preview branch is an isolated Firecracker instance that pauses automatically after a period of inactivity. Every time a change is pushed to GitHub, the migrations within the `./supabase/migrations` folder are run against the Preview Branch so that your entire team is working from the same source of truth.\n\nWhen you hit merge on your Pull Request we run the migrations on your Production database.\n\n### What about data?\n\nWe’re starting with seed data. Any SQL with `./supabase/seed.sql` will populate your Preview Branch. This provides your entire team with an isolated and reproducible environment, safe from any data privacy concerns.\n\nImportantly, we _aren’t_ cloning production data until we find something appropriate for data security. We know that copy-on-write is an available option, and with the appropriate anonymization techniques it seems like a promising way to provide a “production-like” test environment.\n\nWe’ll also need to figure out what this means for large files in Supabase Storage. Do you need to anonymize your photos and videos? This is a work in progress and we’re open to feedback.\n\nEither way, we want to support _both_ seed data and anonymized production data, so that teams can choose their preference based on their risk profile. It makes sense to start with a seed.\n\n### Is it available yet?\n\n\u003cAdmonition type=\"note\" label=\"Branching update 12/13/2023\"\u003e\n\nWe are [rolling out access](https://supabase.com/blog/supabase-branching) and we'll be onboarding organizations in batches over the next few weeks. You can still [sign up for access](https://forms.supabase.com/branching-request).\n\n\u003c/Admonition\u003e\n\n## Get started\n\nJump into our updated [Local Development documentation](https://supabase.com/docs/guides/cli) to get started with the CLI.\n\nIf you’re an existing user simply [update your CLI](https://supabase.com/docs/guides/cli/getting-started#updating-the-supabase-cli) and check out the [full command reference](https://supabase.com/docs/reference/cli/introduction) for all the latest commands.\n\n## More Launch Week 8\n\n- [Hugging Face is now supported in Supabase](https://supabase.com/blog/hugging-face-supabase)\n- [Launch Week 8](https://supabase.com/launch-week)\n- [Coding the stars - an interactive constellation with Three.js and React Three Fiber](https://supabase.com/blog/interactive-constellation-threejs-react-three-fiber)\n- [Why we'll stay remote](https://supabase.com/blog/why-supabase-remote)\n- [Postgres Language Server](https://github.com/supabase/postgres_lsp)\n","title":"Supabase Local Dev: migrations, branching, and observability","description":"New features to streamline the interaction between CLI, code editors, and remote databases.","launchweek":"8","categories":["product"],"tags":["launch-week","announcements"],"date":"2023-08-08","toc_depth":3,"author":"qiao,soedirgo,jonny","image":"launch-week-8/day-2/OG-day2.jpg","thumb":"launch-week-8/day-2/thumb-day2.jpg","content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\n/*prettier-ignore*/\n/*prettier-ignore*/\n/*prettier-ignore*/\n/*prettier-ignore*/\n/*prettier-ignore*/\n/*prettier-ignore*/\n/*prettier-ignore*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    h2: \"h2\",\n    img: \"img\",\n    a: \"a\",\n    code: \"code\",\n    ul: \"ul\",\n    li: \"li\",\n    h3: \"h3\",\n    ol: \"ol\",\n    em: \"em\"\n  }, _provideComponents(), props.components), {BlogCollapsible, Admonition, CH} = _components;\n  if (!Admonition) _missingMdxReference(\"Admonition\", true);\n  if (!BlogCollapsible) _missingMdxReference(\"BlogCollapsible\", true);\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"One of our goals at Supabase is to make Postgres development delightful. To do this, we need to simplify the experience between our CLI, your code editor, and the remote Postgres database powering your applications.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We received feedback recently about our local development experience, encouraging us to improve. This iteration introduces many new features to address that feedback. Let’s jump into a few of the features we’re launching today.\"\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"video-container\",\n      children: _jsx(\"iframe\", {\n        className: \"w-full\",\n        src: \"https://www.youtube-nocookie.com/embed/N0Wb85m3YMI\",\n        title: \"YouTube video player\",\n        frameborder: \"0\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\",\n        allowfullscreen: true\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"postgres-language-server\",\n      children: \"Postgres Language Server\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-8/day-2/postgres-language-server.png\",\n        alt: \"Postgres Language Server\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"One of the unique features of Supabase is the ability to access your Postgres database directly from a browser or mobile app through our \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/api\",\n        children: \"Serverless APIs\"\n      }), \". This means that developers are writing more \", _jsx(_components.a, {\n        href: \"https://www.postgresql.org/docs/current/plpgsql.html\",\n        children: \"PL/pgSQL\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"While code editors have great support for most programming languages, SQL support is underwhelming. We want to make Postgres as simple as Python. Our recently announced \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/postgres_lsp\",\n        children: \"Postgres Language Server\"\n      }), \" takes us a step in that direction - eventually it will provide first-class support for Postgres in your favorite code editor including Linting, Syntax Highlighting, Migrations Parsing, SQL Auto-complete, and Intellisense.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The Postgres Language Server is not ready for Production just yet. The majority of work is still ahead, but we've verified that the technical approach works and we're making it public now so that we can develop it in the open with input from the community. We’re already receiving amazing \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/postgres_lsp/discussions\",\n        children: \"feedback\"\n      }), \" and \", _jsx(_components.a, {\n        href: \"https://news.ycombinator.com/item?id=37020610\",\n        children: \"support\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Follow the progress of the \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/postgres_lsp\",\n        children: \"Postgres Language Server on GitHub\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"observability-tools-for-postgres\",\n      children: \"Observability tools for Postgres\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-8/day-2/inspect-your-database.png\",\n        alt: \"Inspect your database\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We’ve added \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/database/inspect\",\n        children: \"debugging utilities\"\n      }), \" to our CLI to identify production issues via the \", _jsx(_components.code, {\n        children: \"supabase inspect db\"\n      }), \" command. This interface includes 19 different commands to help you solve everything from slow queries to redundant indexes.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"A lot of the credit for this belongs to \", _jsx(_components.a, {\n        href: \"https://github.com/heroku/heroku-pg-extras\",\n        children: \"Heroku’s pg-extras\"\n      }), \" feature, an amazingly useful set of functionality. We’ve adapted the work they started, added a few additional commands, and made it available for any Postgres database. Simply append the \", _jsx(_components.code, {\n        children: \"--db-url\"\n      }), \" param to use these commands with your own Postgres database.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This is just a starting point for the Supabase inspector. We’ll grow this feature to become an essential part of your Postgres toolkit.\"\n    }), \"\\n\", _jsx(BlogCollapsible, {\n      title: \"See full command\",\n      children: _jsx(CH.Code, {\n        codeConfig: chCodeConfig,\n        northPanel: {\n          \"tabs\": [\"\"],\n          \"active\": \"\",\n          \"heightRatio\": 1\n        },\n        files: [{\n          \"name\": \"\",\n          \"focus\": \"\",\n          \"code\": {\n            \"lines\": [{\n              \"tokens\": [{\n                \"content\": \"supabase inspect db --help\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"Tools to inspect your Supabase database\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"Usage:\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"  supabase inspect db [\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }, {\n                \"content\": \"command\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-8)\",\n                    \"textDecoration\": \"underline\"\n                  }\n                }\n              }, {\n                \"content\": \"]\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"Available Commands:\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"  bloat                Estimates space allocated to a relation that is full of dead tuples\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"  blocking             Show queries that are holding locks and the queries that are waiting for them to be released\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"  cache-hit            Show cache hit rates for tables and indices\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"  calls                Show queries from pg_stat_statements ordered by total times called\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"  index-sizes          Show index sizes of individual indexes\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"  index-usage          Show information about the efficiency of indexes\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"  locks                Show queries which have taken out an exclusive lock on a relation\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"  long-running-queries Show currently running queries running for longer than 5 minutes\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"  outliers             Show queries from pg_stat_statements ordered by total execution time\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"  replication-slots    Show information about replication slots on the database\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"  role-connections     Show number of active connections for all database roles\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"  seq-scans            Show number of sequential scans recorded against all tables\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"  table-index-sizes    Show index sizes of individual tables\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"  table-record-counts  Show estimated number of rows per table\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"  table-sizes          Show table sizes of individual tables without their index sizes\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"  total-index-size     Show total size of all indexes\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"  total-table-sizes    Show total table sizes, including table index sizes\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"  unused-indexes       Show indexes with low usage\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }, {\n              \"tokens\": [{\n                \"content\": \"  vacuum-stats         Show statistics related to vacuum operations per table\",\n                \"props\": {\n                  \"style\": {\n                    \"color\": \"var(--ch-4)\"\n                  }\n                }\n              }]\n            }],\n            \"lang\": \"markdown\"\n          },\n          \"annotations\": []\n        }]\n      })\n    }), \"\\n\", _jsx(BlogCollapsible, {\n      title: \"Read the docs\",\n      children: _jsx(\"div\", {\n        children: _jsxs(_components.ul, {\n          children: [\"\\n\", _jsx(_components.li, {\n            children: _jsx(_components.a, {\n              href: \"https://supabase.com/docs/reference/cli/supabase-inspect-db-calls\",\n              children: \"Show most frequently run queries\"\n            })\n          }), \"\\n\", _jsx(_components.li, {\n            children: _jsx(_components.a, {\n              href: \"https://supabase.com/docs/reference/cli/supabase-inspect-db-long-running-queries\",\n              children: \"Show long running queries\"\n            })\n          }), \"\\n\", _jsx(_components.li, {\n            children: _jsx(_components.a, {\n              href: \"https://supabase.com/docs/reference/cli/supabase-inspect-db-outliers\",\n              children: \"Show queries ordered by total execution time\"\n            })\n          }), \"\\n\", _jsx(_components.li, {\n            children: _jsx(_components.a, {\n              href: \"https://supabase.com/docs/reference/cli/supabase-inspect-db-blocking\",\n              children: \"Show queries waiting and holding locks\"\n            })\n          }), \"\\n\", _jsx(_components.li, {\n            children: _jsx(_components.a, {\n              href: \"https://supabase.com/docs/reference/cli/supabase-inspect-db-locks\",\n              children: \"Show queries taking exclusive locks\"\n            })\n          }), \"\\n\", _jsx(_components.li, {\n            children: _jsx(_components.a, {\n              href: \"https://supabase.com/docs/reference/cli/supabase-inspect-db-total-index-size\",\n              children: \"Show total size of all indexes\"\n            })\n          }), \"\\n\", _jsx(_components.li, {\n            children: _jsx(_components.a, {\n              href: \"https://supabase.com/docs/reference/cli/supabase-inspect-db-index-sizes\",\n              children: \"Show sizes of individual indexes\"\n            })\n          }), \"\\n\", _jsx(_components.li, {\n            children: _jsx(_components.a, {\n              href: \"https://supabase.com/docs/reference/cli/supabase-inspect-db-index-usage\",\n              children: \"Show information about index efficiency\"\n            })\n          }), \"\\n\", _jsx(_components.li, {\n            children: _jsx(_components.a, {\n              href: \"https://supabase.com/docs/reference/cli/supabase-inspect-db-unused-indexes\",\n              children: \"Show indexes with low usage\"\n            })\n          }), \"\\n\", _jsx(_components.li, {\n            children: _jsx(_components.a, {\n              href: \"https://supabase.com/docs/reference/cli/supabase-inspect-db-total-table-sizes\",\n              children: \"Show total size of all tables\"\n            })\n          }), \"\\n\", _jsx(_components.li, {\n            children: _jsx(_components.a, {\n              href: \"https://supabase.com/docs/reference/cli/supabase-inspect-db-table-sizes\",\n              children: \"Show sizes of individual tables\"\n            })\n          }), \"\\n\", _jsx(_components.li, {\n            children: _jsx(_components.a, {\n              href: \"https://supabase.com/docs/reference/cli/supabase-inspect-db-table-index-sizes\",\n              children: \"Show index sizes of individual tables\"\n            })\n          }), \"\\n\", _jsx(_components.li, {\n            children: _jsx(_components.a, {\n              href: \"https://supabase.com/docs/reference/cli/supabase-inspect-db-cache-hit\",\n              children: \"Show cache hit rates for tables and indices\"\n            })\n          }), \"\\n\", _jsx(_components.li, {\n            children: _jsx(_components.a, {\n              href: \"https://supabase.com/docs/reference/cli/supabase-inspect-db-table-record-counts\",\n              children: \"Show estimated number of rows per table\"\n            })\n          }), \"\\n\", _jsx(_components.li, {\n            children: _jsx(_components.a, {\n              href: \"https://supabase.com/docs/reference/cli/supabase-inspect-db-seq-scans\",\n              children: \"Show number of sequential scans for all tables\"\n            })\n          }), \"\\n\", _jsx(_components.li, {\n            children: _jsx(_components.a, {\n              href: \"https://supabase.com/docs/reference/cli/supabase-inspect-db-replication-slots\",\n              children: \"Show information about replication slots\"\n            })\n          }), \"\\n\", _jsx(_components.li, {\n            children: _jsx(_components.a, {\n              href: \"https://supabase.com/docs/reference/cli/supabase-inspect-db-role-connections\",\n              children: \"Show number of active connections\"\n            })\n          }), \"\\n\", _jsx(_components.li, {\n            children: _jsx(_components.a, {\n              href: \"https://supabase.com/docs/reference/cli/supabase-inspect-db-bloat\",\n              children: \"Show estimated database bloat\"\n            })\n          }), \"\\n\", _jsx(_components.li, {\n            children: _jsx(_components.a, {\n              href: \"https://supabase.com/docs/reference/cli/supabase-inspect-db-vacuum-stats\",\n              children: \"Show statistics related to vacuum operations\"\n            })\n          }), \"\\n\"]\n        })\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"easier-backups\",\n      children: \"Easier backups\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-8/day-2/backup-your-database.png\",\n        alt: \"How to back up your database with Supabase CLI\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We’ve made it even easier to backup and migrate your database, using \", _jsx(_components.code, {\n        children: \"supabase db dump\"\n      }), \". Under the hood, this simply uses \", _jsx(_components.a, {\n        href: \"https://www.postgresql.org/docs/current/app-pgdump.html\",\n        children: \"pg_dump\"\n      }), \" (it's just Postgres, after all). However we also handle a few of the hairy issues that you might need to navigate on your own, like object permissions.\"]\n    }), \"\\n\", _jsx(BlogCollapsible, {\n      title: \"See full command\",\n      children: _jsx(\"div\", {\n        children: _jsx(CH.Code, {\n          codeConfig: chCodeConfig,\n          northPanel: {\n            \"tabs\": [\"\"],\n            \"active\": \"\",\n            \"heightRatio\": 1\n          },\n          files: [{\n            \"name\": \"\",\n            \"focus\": \"\",\n            \"code\": {\n              \"lines\": [{\n                \"tokens\": [{\n                  \"content\": \"supabase db dump --help\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"Dumps data or schemas from the remote database\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"Usage:\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"supabase db dump [\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }, {\n                  \"content\": \"flags\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-8)\",\n                      \"textDecoration\": \"underline\"\n                    }\n                  }\n                }, {\n                  \"content\": \"]\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"Flags:\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"--data-only Dumps only data records.\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"-f, --file string File path to save the dumped contents.\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"--keep-comments Keeps commented lines from pg_dump output.\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"--role-only Dumps only cluster roles.\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"--use-copy Uses copy statements in place of inserts.\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }],\n              \"lang\": \"markdown\"\n            },\n            \"annotations\": []\n          }]\n        })\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"improved-database-migrations\",\n      children: \"Improved Database Migrations\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-8/day-2/modify-your-database.png\",\n        alt: \"modify your database\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We’ve extended the CLI migration feature and added Dashboard support. Database migrations give you a way to update your database using version-controlled SQL files. We’ve built a lot of tooling around our migrations, including \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/reference/cli/supabase-migration-repair\",\n        children: \"reparation\"\n      }), \", migration cleanup using the \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/reference/cli/supabase-migration-squash\",\n        children: \"squash\"\n      }), \" command, and \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/reference/cli/supabase-db-diff\",\n        children: \"diffing\"\n      }), \" (using \", _jsx(_components.a, {\n        href: \"https://github.com/djrobstep/migra\",\n        children: \"migra\"\n      }), \") to generate a new migration or to detect schema drift.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"With the new Postgres Language Server, we hope to make it as easy to write Postgres migrations as it is to develop applications in TypeScript, Go, Python, or Rust.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Finally, we’ve added a Migrations view \", _jsx(_components.a, {\n        href: \"https://app.supabase.com/project/_/database/migrations\",\n        children: \"in the dashboard\"\n      }), \" to track your migration history to improve the discoverability of migrations.\"]\n    }), \"\\n\", _jsx(BlogCollapsible, {\n      title: \"See full command\",\n      children: _jsx(\"div\", {\n        children: _jsx(CH.Code, {\n          codeConfig: chCodeConfig,\n          northPanel: {\n            \"tabs\": [\"\"],\n            \"active\": \"\",\n            \"heightRatio\": 1\n          },\n          files: [{\n            \"name\": \"\",\n            \"focus\": \"\",\n            \"code\": {\n              \"lines\": [{\n                \"tokens\": [{\n                  \"content\": \"supabase migration\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"Manage database migration scripts\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"Usage:\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"  supabase migration [\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }, {\n                  \"content\": \"command\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-8)\",\n                      \"textDecoration\": \"underline\"\n                    }\n                  }\n                }, {\n                  \"content\": \"]\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"Available Commands:\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"  list        List local and remote migrations\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"  new         Create an empty migration script\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"  repair      Repair the migration history table\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"  squash      Squash migrations to a single file\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"  up          Apply pending migrations to local database\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }],\n              \"lang\": \"markdown\"\n            },\n            \"annotations\": []\n          }]\n        })\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"test-and-lint-your-database\",\n      children: \"Test and lint your database\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-8/day-2/test-your-database.png\",\n        alt: \"Test your database\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We’ve simplified the database testing experience, with \", _jsx(_components.code, {\n        children: \"supabase test\"\n      }), \". Running \", _jsx(_components.code, {\n        children: \"supabase test new\"\n      }), \" stubs out a \", _jsx(_components.a, {\n        href: \"https://pgtap.org/\",\n        children: \"pgTAP test\"\n      }), \" for you to fill with testing logic. The CLI includes \", _jsx(_components.a, {\n        href: \"https://pgtap.org/pg_prove\",\n        children: \"pg_prove and the TAP harness\"\n      }), \", so all you need to do is run \", _jsx(_components.code, {\n        children: \"supabase test db\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To make life even easier, our friends at \", _jsx(_components.a, {\n        href: \"https://usebasejump.com/\",\n        children: \"Basejump\"\n      }), \" have created an entire suite of \", _jsx(_components.a, {\n        href: \"https://github.com/usebasejump/supabase-test-helpers\",\n        children: \"Supabase Test Helpers\"\n      }), \" which make it simple to create users, run tests as an \", _jsx(_components.a, {\n        href: \"https://usebasejump.com/blog/testing-on-supabase-with-pgtap#testing-authenticated\",\n        children: \"authenticated user\"\n      }), \", and test your \", _jsx(_components.a, {\n        href: \"https://usebasejump.com/blog/testing-on-supabase-with-pgtap#rls-testing\",\n        children: \"RLS policies\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Finally, while you wait for us to make progress on the Language Server, we’ve added \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/cli/testing-and-linting#linting-your-database\",\n        children: \"support for linting\"\n      }), \" through the excellent \", _jsx(_components.a, {\n        href: \"https://github.com/okbob/plpgsql_check\",\n        children: \"plpgsql_check\"\n      }), \" extension.\"]\n    }), \"\\n\", _jsx(BlogCollapsible, {\n      title: \"See full command\",\n      children: _jsxs(\"div\", {\n        children: [_jsx(CH.Code, {\n          codeConfig: chCodeConfig,\n          northPanel: {\n            \"tabs\": [\"\"],\n            \"active\": \"\",\n            \"heightRatio\": 1\n          },\n          files: [{\n            \"name\": \"\",\n            \"focus\": \"\",\n            \"code\": {\n              \"lines\": [{\n                \"tokens\": [{\n                  \"content\": \"supabase test\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"Run tests on local Supabase containers\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"Usage:\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"  supabase test [\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }, {\n                  \"content\": \"command\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-8)\",\n                      \"textDecoration\": \"underline\"\n                    }\n                  }\n                }, {\n                  \"content\": \"]\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"Available Commands:\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"  db          Tests local database with pgTAP\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"  new         Create a new test file\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }],\n              \"lang\": \"markdown\"\n            },\n            \"annotations\": []\n          }]\n        }), _jsx(CH.Code, {\n          codeConfig: chCodeConfig,\n          northPanel: {\n            \"tabs\": [\"\"],\n            \"active\": \"\",\n            \"heightRatio\": 1\n          },\n          files: [{\n            \"name\": \"\",\n            \"focus\": \"\",\n            \"code\": {\n              \"lines\": [{\n                \"tokens\": [{\n                  \"content\": \"supabase db lint\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"Checks local database for typing error\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"Usage:\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"  supabase db lint [\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }, {\n                  \"content\": \"flags\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-8)\",\n                      \"textDecoration\": \"underline\"\n                    }\n                  }\n                }, {\n                  \"content\": \"]\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"Flags:\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"  -h, --help                        help for lint\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"      --level [ warning | error ]   Error level to emit. (default warning)\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"      --linked                      Lints the linked project for schema errors.\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"  -s, --schema strings              List of schema to include. (default all)\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }],\n              \"lang\": \"markdown\"\n            },\n            \"annotations\": []\n          }]\n        })]\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"database-seeding\",\n      children: \"Database seeding\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-8/day-2/support-for-seeding.png\",\n        alt: \"Support for seeding\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Seeding is the process of populating a database with initial data, typically used to provide sample or default records for testing and development purposes. This gives you a reproducible development environment across your entire team.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We’ve added \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/cli/seeding-your-database\",\n        children: \"support for seeding\"\n      }), \" to populate your local databases with data whenever you run \", _jsx(_components.code, {\n        children: \"supabase start\"\n      }), \" or \", _jsx(_components.code, {\n        children: \"supabase db reset\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We’ve also worked with our friends at Snaplet to \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/cli/seeding-your-database#generating-seed-data\",\n        children: \"generate seed data\"\n      }), \" directly from your database:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"npx snaplet generate --sql \u003e supabase/seed.sql\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"markdown\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"type-generators\",\n      children: \"Type generators\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-8/day-2/end-to-end-type-safety.png\",\n        alt: \"End to end Type Safety\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Type generators introspect a PostgreSQL schema and automatically generate TypeScript definitions. This gives you \", _jsx(_components.a, {\n        href: \"https://www.youtube.com/watch?v=VSNgAIObBdw\",\n        children: \"end-to-end type safety\"\n      }), \" from the database to the browser.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In the past month, we've added relationship detection in supabase-js. Foreign keys are now included in the generated types so that supabase-js can detect whether a referenced table should be an array (one-to-many) or an object (many-to-one). We've also added Helper Types to improve the developer experience for common scenarios, like short-hand accessors:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// Before\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"let\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" movie\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"Database\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"[\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'public'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"][\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'Tables'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"][\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'movies'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"][\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'Row'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"] \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"// ...\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"// After\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"let\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" movie\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"Tables\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'movies'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"// ...\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"tsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(BlogCollapsible, {\n      title: \"See full command\",\n      children: _jsx(\"div\", {\n        children: _jsx(CH.Code, {\n          codeConfig: chCodeConfig,\n          northPanel: {\n            \"tabs\": [\"\"],\n            \"active\": \"\",\n            \"heightRatio\": 1\n          },\n          files: [{\n            \"name\": \"\",\n            \"focus\": \"\",\n            \"code\": {\n              \"lines\": [{\n                \"tokens\": [{\n                  \"content\": \"supabase gen types\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"Generate types from Postgres schema\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"Usage:\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"  supabase gen types [\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }, {\n                  \"content\": \"command\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-8)\",\n                      \"textDecoration\": \"underline\"\n                    }\n                  }\n                }, {\n                  \"content\": \"]\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"Available Commands:\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"  typescript  Generate types for TypeScript\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }],\n              \"lang\": \"markdown\"\n            },\n            \"annotations\": []\n          }]\n        })\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"official-github-action\",\n      children: \"Official GitHub Action\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-8/day-2/support-for-github-actions.png\",\n        alt: \"Support for GitHub Actions\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We’ve developed an official \", _jsx(_components.a, {\n        href: \"https://github.com/marketplace/actions/supabase-cli-action\",\n        children: \"GitHub Action\"\n      }), \" which leverages the CLI. You can \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/cli/github-action/generating-types\",\n        children: \"generate types on every PR\"\n      }), \", or run \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/cli/github-action/testing\",\n        children: \"your tests on every commit\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"local-logging-and-debugging\",\n      children: \"Local Logging and Debugging\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Logs are now accessible locally in the Dashboard. Last launch week we released an open source logging server, with support for BigQuery. In the past few months we’ve added Postgres support to this server. This means that all of your local logs are accessible with no additional config - simply run supabase start and then visit the local dashboard to start debugging.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-8/day-2/local-logging-debugging.png\",\n        alt: \"Logs are now accessible locally in the dashboard\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"stable-releases\",\n      children: \"Stable releases\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We’ve moved the Supabase CLI to a fortnightly stable-release cycle. Every 2 weeks, we will update the \", _jsx(_components.a, {\n        href: \"https://www.npmjs.com/package/supabase?activeTab=versions\",\n        children: _jsx(_components.code, {\n          children: \"latest\"\n        })\n      }), \" tag on npm, the \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/homebrew-tap/blob/main/supabase.rb\",\n        children: _jsx(_components.code, {\n          children: \"supabase/tap\"\n        })\n      }), \" for homebrew, and the \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/homebrew-tap/blob/main/supabase.rb\",\n        children: _jsx(_components.code, {\n          children: \"supabase\"\n        })\n      }), \" scoop bucket. You can find the binary downloads in our GitHub \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/cli/releases/latest\",\n        children: \"latest release\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"For the adventurous feature hunters, we’ve added a \", _jsx(_components.code, {\n        children: \"beta\"\n      }), \" release channel for the CLI, with new releases on every PR merged. You can follow \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/cli#install-the-cli\",\n        children: \"this guide\"\n      }), \" to install Supabase CLI (beta).\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"branching-and-preview-environments\",\n      children: \"Branching and Preview Environments\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"And finally, probably our most anticipated feature - branching:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-8/day-2/branching-02.png\",\n        alt: \"Searching branches with Supabase\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We’ve made major improvements to our local development with the features above - but we have bigger ambitions. For several months we’ve been developing Supabase branching and today we're opening it up for alpha testers.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Supabase isn’t simply a database, it’s an entire backend - everything from your Postgres database to your \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/storage-v3-resumable-uploads\",\n        children: \"50GB videos\"\n      }), \". Branching improves the experience of managing environments so that developers and teams spend less time on DevOps and more time building.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"supabase-branching-is-hard\",\n      children: \"Supabase branching is hard\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Every project is a Postgres database, wrapped in a suite of tools like \", _jsx(_components.a, {\n        href: \"https://supabase.com/auth\",\n        children: \"Auth\"\n      }), \", \", _jsx(_components.a, {\n        href: \"https://supabase.com/storage\",\n        children: \"Storage\"\n      }), \", \", _jsx(_components.a, {\n        href: \"https://supabase.com/edge-functions\",\n        children: \"Edge Functions\"\n      }), \", \", _jsx(_components.a, {\n        href: \"https://supabase.com/realtime\",\n        children: \"Realtime\"\n      }), \" and \", _jsx(_components.a, {\n        href: \"https://supabase.com/vector\",\n        children: \"Vectors\"\n      }), \", and encompassed by \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/api\",\n        children: \"API middleware\"\n      }), \" and \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/supabase-logs-self-hosted\",\n        children: \"logs\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"A good branching solution requires each tool to provide multi-tenancy support so that:\"\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Data can be isolated from production for security.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Compute can be isolated from each other to avoid noisy-neighbors.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"how-does-branching-work\",\n      children: \"How does branching work?\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We use Git to bridge the gap between your local development environment and your hosted database. For now, we’ve focused on GitHub.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Every time you open a new Pull Request on GitHub, a corresponding “Preview Environment” is created. Each preview branch is an isolated Firecracker instance that pauses automatically after a period of inactivity. Every time a change is pushed to GitHub, the migrations within the \", _jsx(_components.code, {\n        children: \"./supabase/migrations\"\n      }), \" folder are run against the Preview Branch so that your entire team is working from the same source of truth.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"When you hit merge on your Pull Request we run the migrations on your Production database.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"what-about-data\",\n      children: \"What about data?\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We’re starting with seed data. Any SQL with \", _jsx(_components.code, {\n        children: \"./supabase/seed.sql\"\n      }), \" will populate your Preview Branch. This provides your entire team with an isolated and reproducible environment, safe from any data privacy concerns.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Importantly, we \", _jsx(_components.em, {\n        children: \"aren’t\"\n      }), \" cloning production data until we find something appropriate for data security. We know that copy-on-write is an available option, and with the appropriate anonymization techniques it seems like a promising way to provide a “production-like” test environment.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We’ll also need to figure out what this means for large files in Supabase Storage. Do you need to anonymize your photos and videos? This is a work in progress and we’re open to feedback.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Either way, we want to support \", _jsx(_components.em, {\n        children: \"both\"\n      }), \" seed data and anonymized production data, so that teams can choose their preference based on their risk profile. It makes sense to start with a seed.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"is-it-available-yet\",\n      children: \"Is it available yet?\"\n    }), \"\\n\", _jsx(Admonition, {\n      type: \"note\",\n      label: \"Branching update 12/13/2023\",\n      children: _jsxs(_components.p, {\n        children: [\"We are \", _jsx(_components.a, {\n          href: \"https://supabase.com/blog/supabase-branching\",\n          children: \"rolling out access\"\n        }), \" and we'll be onboarding organizations in batches over the next few weeks. You can still \", _jsx(_components.a, {\n          href: \"https://forms.supabase.com/branching-request\",\n          children: \"sign up for access\"\n        }), \".\"]\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"get-started\",\n      children: \"Get started\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Jump into our updated \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/cli\",\n        children: \"Local Development documentation\"\n      }), \" to get started with the CLI.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you’re an existing user simply \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/cli/getting-started#updating-the-supabase-cli\",\n        children: \"update your CLI\"\n      }), \" and check out the \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/reference/cli/introduction\",\n        children: \"full command reference\"\n      }), \" for all the latest commands.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"more-launch-week-8\",\n      children: \"More Launch Week 8\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/blog/hugging-face-supabase\",\n          children: \"Hugging Face is now supported in Supabase\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/launch-week\",\n          children: \"Launch Week 8\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/blog/interactive-constellation-threejs-react-three-fiber\",\n          children: \"Coding the stars - an interactive constellation with Three.js and React Three Fiber\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/blog/why-supabase-remote\",\n          children: \"Why we'll stay remote\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase/postgres_lsp\",\n          children: \"Postgres Language Server\"\n        })\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"Postgres Language Server","slug":"postgres-language-server","lvl":2,"i":0,"seen":0},{"content":"Observability tools for Postgres","slug":"observability-tools-for-postgres","lvl":2,"i":1,"seen":0},{"content":"Easier backups","slug":"easier-backups","lvl":2,"i":2,"seen":0},{"content":"Improved Database Migrations","slug":"improved-database-migrations","lvl":2,"i":3,"seen":0},{"content":"Test and lint your database","slug":"test-and-lint-your-database","lvl":2,"i":4,"seen":0},{"content":"Database seeding","slug":"database-seeding","lvl":2,"i":5,"seen":0},{"content":"Type generators","slug":"type-generators","lvl":2,"i":6,"seen":0},{"content":"Official GitHub Action","slug":"official-github-action","lvl":2,"i":7,"seen":0},{"content":"Local Logging and Debugging","slug":"local-logging-and-debugging","lvl":2,"i":8,"seen":0},{"content":"Stable releases","slug":"stable-releases","lvl":2,"i":9,"seen":0},{"content":"Branching and Preview Environments","slug":"branching-and-preview-environments","lvl":2,"i":10,"seen":0},{"content":"Supabase branching is hard","slug":"supabase-branching-is-hard","lvl":3,"i":11,"seen":0},{"content":"How does branching work?","slug":"how-does-branching-work","lvl":3,"i":12,"seen":0},{"content":"What about data?","slug":"what-about-data","lvl":3,"i":13,"seen":0},{"content":"Is it available yet?","slug":"is-it-available-yet","lvl":3,"i":14,"seen":0},{"content":"Get started","slug":"get-started","lvl":2,"i":15,"seen":0},{"content":"More Launch Week 8","slug":"more-launch-week-8","lvl":2,"i":16,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,2],"level":0},{"type":"inline","content":"One of our goals at Supabase is to make Postgres development delightful. To do this, we need to simplify the experience between our CLI, your code editor, and the remote Postgres database powering your applications.","level":1,"lines":[1,2],"children":[{"type":"text","content":"One of our goals at Supabase is to make Postgres development delightful. To do this, we need to simplify the experience between our CLI, your code editor, and the remote Postgres database powering your applications.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[3,4],"level":0},{"type":"inline","content":"We received feedback recently about our local development experience, encouraging us to improve. This iteration introduces many new features to address that feedback. Let’s jump into a few of the features we’re launching today.","level":1,"lines":[3,4],"children":[{"type":"text","content":"We received feedback recently about our local development experience, encouraging us to improve. This iteration introduces many new features to address that feedback. Let’s jump into a few of the features we’re launching today.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[5,13],"level":0},{"type":"inline","content":"\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/N0Wb85m3YMI\"\n    title=\"YouTube video player\"\n    frameborder=\"0\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"\n    allowfullscreen","level":1,"lines":[5,13],"children":[{"type":"text","content":"\u003cdiv className=\"video-container\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ciframe","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"w-full\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://www.youtube-nocookie.com/embed/N0Wb85m3YMI\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"title=\"YouTube video player\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"frameborder=\"0\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allowfullscreen","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"blockquote_open","lines":[13,15],"level":0},{"type":"paragraph_open","tight":false,"lines":[13,15],"level":1},{"type":"inline","content":"\u003c/iframe\u003e\n\u003c/div\u003e","level":2,"lines":[13,15],"children":[{"type":"text","content":"\u003c/iframe\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"heading_open","hLevel":2,"lines":[16,17],"level":0},{"type":"inline","content":"[Postgres Language Server](#postgres-language-server)","level":1,"lines":[16,17],"children":[{"type":"text","content":"Postgres Language Server","level":0}],"lvl":2,"i":0,"seen":0,"slug":"postgres-language-server"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[18,19],"level":0},{"type":"inline","content":"![Postgres Language Server](/images/blog/launch-week-8/day-2/postgres-language-server.png)","level":1,"lines":[18,19],"children":[{"type":"image","src":"/images/blog/launch-week-8/day-2/postgres-language-server.png","title":"","alt":"Postgres Language Server","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[20,21],"level":0},{"type":"inline","content":"One of the unique features of Supabase is the ability to access your Postgres database directly from a browser or mobile app through our [Serverless APIs](https://supabase.com/docs/guides/api). This means that developers are writing more [PL/pgSQL](https://www.postgresql.org/docs/current/plpgsql.html).","level":1,"lines":[20,21],"children":[{"type":"text","content":"One of the unique features of Supabase is the ability to access your Postgres database directly from a browser or mobile app through our ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/api","title":"","level":0},{"type":"text","content":"Serverless APIs","level":1},{"type":"link_close","level":0},{"type":"text","content":". This means that developers are writing more ","level":0},{"type":"link_open","href":"https://www.postgresql.org/docs/current/plpgsql.html","title":"","level":0},{"type":"text","content":"PL/pgSQL","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[22,23],"level":0},{"type":"inline","content":"While code editors have great support for most programming languages, SQL support is underwhelming. We want to make Postgres as simple as Python. Our recently announced [Postgres Language Server](https://github.com/supabase/postgres_lsp) takes us a step in that direction - eventually it will provide first-class support for Postgres in your favorite code editor including Linting, Syntax Highlighting, Migrations Parsing, SQL Auto-complete, and Intellisense.","level":1,"lines":[22,23],"children":[{"type":"text","content":"While code editors have great support for most programming languages, SQL support is underwhelming. We want to make Postgres as simple as Python. Our recently announced ","level":0},{"type":"link_open","href":"https://github.com/supabase/postgres_lsp","title":"","level":0},{"type":"text","content":"Postgres Language Server","level":1},{"type":"link_close","level":0},{"type":"text","content":" takes us a step in that direction - eventually it will provide first-class support for Postgres in your favorite code editor including Linting, Syntax Highlighting, Migrations Parsing, SQL Auto-complete, and Intellisense.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[24,25],"level":0},{"type":"inline","content":"The Postgres Language Server is not ready for Production just yet. The majority of work is still ahead, but we've verified that the technical approach works and we're making it public now so that we can develop it in the open with input from the community. We’re already receiving amazing [feedback](https://github.com/supabase/postgres_lsp/discussions) and [support](https://news.ycombinator.com/item?id=37020610).","level":1,"lines":[24,25],"children":[{"type":"text","content":"The Postgres Language Server is not ready for Production just yet. The majority of work is still ahead, but we've verified that the technical approach works and we're making it public now so that we can develop it in the open with input from the community. We’re already receiving amazing ","level":0},{"type":"link_open","href":"https://github.com/supabase/postgres_lsp/discussions","title":"","level":0},{"type":"text","content":"feedback","level":1},{"type":"link_close","level":0},{"type":"text","content":" and ","level":0},{"type":"link_open","href":"https://news.ycombinator.com/item?id=37020610","title":"","level":0},{"type":"text","content":"support","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[26,27],"level":0},{"type":"inline","content":"Follow the progress of the [Postgres Language Server on GitHub](https://github.com/supabase/postgres_lsp).","level":1,"lines":[26,27],"children":[{"type":"text","content":"Follow the progress of the ","level":0},{"type":"link_open","href":"https://github.com/supabase/postgres_lsp","title":"","level":0},{"type":"text","content":"Postgres Language Server on GitHub","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[28,29],"level":0},{"type":"inline","content":"[Observability tools for Postgres](#observability-tools-for-postgres)","level":1,"lines":[28,29],"children":[{"type":"text","content":"Observability tools for Postgres","level":0}],"lvl":2,"i":1,"seen":0,"slug":"observability-tools-for-postgres"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[30,31],"level":0},{"type":"inline","content":"![Inspect your database](/images/blog/launch-week-8/day-2/inspect-your-database.png)","level":1,"lines":[30,31],"children":[{"type":"image","src":"/images/blog/launch-week-8/day-2/inspect-your-database.png","title":"","alt":"Inspect your database","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[32,33],"level":0},{"type":"inline","content":"We’ve added [debugging utilities](https://supabase.com/docs/guides/database/inspect) to our CLI to identify production issues via the `supabase inspect db` command. This interface includes 19 different commands to help you solve everything from slow queries to redundant indexes.","level":1,"lines":[32,33],"children":[{"type":"text","content":"We’ve added ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/database/inspect","title":"","level":0},{"type":"text","content":"debugging utilities","level":1},{"type":"link_close","level":0},{"type":"text","content":" to our CLI to identify production issues via the ","level":0},{"type":"code","content":"supabase inspect db","block":false,"level":0},{"type":"text","content":" command. This interface includes 19 different commands to help you solve everything from slow queries to redundant indexes.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[34,35],"level":0},{"type":"inline","content":"A lot of the credit for this belongs to [Heroku’s pg-extras](https://github.com/heroku/heroku-pg-extras) feature, an amazingly useful set of functionality. We’ve adapted the work they started, added a few additional commands, and made it available for any Postgres database. Simply append the `--db-url` param to use these commands with your own Postgres database.","level":1,"lines":[34,35],"children":[{"type":"text","content":"A lot of the credit for this belongs to ","level":0},{"type":"link_open","href":"https://github.com/heroku/heroku-pg-extras","title":"","level":0},{"type":"text","content":"Heroku’s pg-extras","level":1},{"type":"link_close","level":0},{"type":"text","content":" feature, an amazingly useful set of functionality. We’ve adapted the work they started, added a few additional commands, and made it available for any Postgres database. Simply append the ","level":0},{"type":"code","content":"--db-url","block":false,"level":0},{"type":"text","content":" param to use these commands with your own Postgres database.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[36,37],"level":0},{"type":"inline","content":"This is just a starting point for the Supabase inspector. We’ll grow this feature to become an essential part of your Postgres toolkit.","level":1,"lines":[36,37],"children":[{"type":"text","content":"This is just a starting point for the Supabase inspector. We’ll grow this feature to become an essential part of your Postgres toolkit.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[38,39],"level":0},{"type":"inline","content":"\u003cBlogCollapsible title=\"See full command\"\u003e","level":1,"lines":[38,39],"children":[{"type":"text","content":"\u003cBlogCollapsible title=\"See full command\"\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"code","content":"{/* prettier-ignore */}\n```markdown\nsupabase inspect db --help\nTools to inspect your Supabase database\n\nUsage:\n  supabase inspect db [command]\n\nAvailable Commands:\n  bloat                Estimates space allocated to a relation that is full of dead tuples\n  blocking             Show queries that are holding locks and the queries that are waiting for them to be released\n  cache-hit            Show cache hit rates for tables and indices\n  calls                Show queries from pg_stat_statements ordered by total times called\n  index-sizes          Show index sizes of individual indexes\n  index-usage          Show information about the efficiency of indexes\n  locks                Show queries which have taken out an exclusive lock on a relation\n  long-running-queries Show currently running queries running for longer than 5 minutes\n  outliers             Show queries from pg_stat_statements ordered by total execution time\n  replication-slots    Show information about replication slots on the database\n  role-connections     Show number of active connections for all database roles\n  seq-scans            Show number of sequential scans recorded against all tables\n  table-index-sizes    Show index sizes of individual tables\n  table-record-counts  Show estimated number of rows per table\n  table-sizes          Show table sizes of individual tables without their index sizes\n  total-index-size     Show total size of all indexes\n  total-table-sizes    Show total table sizes, including table index sizes\n  unused-indexes       Show indexes with low usage\n  vacuum-stats         Show statistics related to vacuum operations per table\n```\n","block":true,"lines":[40,70],"level":0},{"type":"paragraph_open","tight":false,"lines":[70,71],"level":0},{"type":"inline","content":"\u003c/BlogCollapsible\u003e","level":1,"lines":[70,71],"children":[{"type":"text","content":"\u003c/BlogCollapsible\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[72,73],"level":0},{"type":"inline","content":"\u003cBlogCollapsible title=\"Read the docs\"\u003e","level":1,"lines":[72,73],"children":[{"type":"text","content":"\u003cBlogCollapsible title=\"Read the docs\"\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[74,96],"level":0},{"type":"inline","content":"{/* prettier-ignore */}\n  \u003cdiv\u003e\n    - [Show most frequently run queries](https://supabase.com/docs/reference/cli/supabase-inspect-db-calls)\n    - [Show long running queries](https://supabase.com/docs/reference/cli/supabase-inspect-db-long-running-queries)\n    - [Show queries ordered by total execution time](https://supabase.com/docs/reference/cli/supabase-inspect-db-outliers)\n    - [Show queries waiting and holding locks](https://supabase.com/docs/reference/cli/supabase-inspect-db-blocking)\n    - [Show queries taking exclusive locks](https://supabase.com/docs/reference/cli/supabase-inspect-db-locks)\n    - [Show total size of all indexes](https://supabase.com/docs/reference/cli/supabase-inspect-db-total-index-size)\n    - [Show sizes of individual indexes](https://supabase.com/docs/reference/cli/supabase-inspect-db-index-sizes)\n    - [Show information about index efficiency](https://supabase.com/docs/reference/cli/supabase-inspect-db-index-usage)\n    - [Show indexes with low usage](https://supabase.com/docs/reference/cli/supabase-inspect-db-unused-indexes)\n    - [Show total size of all tables](https://supabase.com/docs/reference/cli/supabase-inspect-db-total-table-sizes)\n    - [Show sizes of individual tables](https://supabase.com/docs/reference/cli/supabase-inspect-db-table-sizes)\n    - [Show index sizes of individual tables](https://supabase.com/docs/reference/cli/supabase-inspect-db-table-index-sizes)\n    - [Show cache hit rates for tables and indices](https://supabase.com/docs/reference/cli/supabase-inspect-db-cache-hit)\n    - [Show estimated number of rows per table](https://supabase.com/docs/reference/cli/supabase-inspect-db-table-record-counts)\n    - [Show number of sequential scans for all tables](https://supabase.com/docs/reference/cli/supabase-inspect-db-seq-scans)\n    - [Show information about replication slots](https://supabase.com/docs/reference/cli/supabase-inspect-db-replication-slots)\n    - [Show number of active connections](https://supabase.com/docs/reference/cli/supabase-inspect-db-role-connections)\n    - [Show estimated database bloat](https://supabase.com/docs/reference/cli/supabase-inspect-db-bloat)\n    - [Show statistics related to vacuum operations](https://supabase.com/docs/reference/cli/supabase-inspect-db-vacuum-stats)\n  \u003c/div\u003e","level":1,"lines":[74,96],"children":[{"type":"text","content":"{/* prettier-ignore */}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cdiv\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"- ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/cli/supabase-inspect-db-calls","title":"","level":0},{"type":"text","content":"Show most frequently run queries","level":1},{"type":"link_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"- ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/cli/supabase-inspect-db-long-running-queries","title":"","level":0},{"type":"text","content":"Show long running queries","level":1},{"type":"link_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"- ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/cli/supabase-inspect-db-outliers","title":"","level":0},{"type":"text","content":"Show queries ordered by total execution time","level":1},{"type":"link_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"- ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/cli/supabase-inspect-db-blocking","title":"","level":0},{"type":"text","content":"Show queries waiting and holding locks","level":1},{"type":"link_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"- ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/cli/supabase-inspect-db-locks","title":"","level":0},{"type":"text","content":"Show queries taking exclusive locks","level":1},{"type":"link_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"- ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/cli/supabase-inspect-db-total-index-size","title":"","level":0},{"type":"text","content":"Show total size of all indexes","level":1},{"type":"link_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"- ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/cli/supabase-inspect-db-index-sizes","title":"","level":0},{"type":"text","content":"Show sizes of individual indexes","level":1},{"type":"link_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"- ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/cli/supabase-inspect-db-index-usage","title":"","level":0},{"type":"text","content":"Show information about index efficiency","level":1},{"type":"link_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"- ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/cli/supabase-inspect-db-unused-indexes","title":"","level":0},{"type":"text","content":"Show indexes with low usage","level":1},{"type":"link_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"- ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/cli/supabase-inspect-db-total-table-sizes","title":"","level":0},{"type":"text","content":"Show total size of all tables","level":1},{"type":"link_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"- ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/cli/supabase-inspect-db-table-sizes","title":"","level":0},{"type":"text","content":"Show sizes of individual tables","level":1},{"type":"link_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"- ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/cli/supabase-inspect-db-table-index-sizes","title":"","level":0},{"type":"text","content":"Show index sizes of individual tables","level":1},{"type":"link_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"- ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/cli/supabase-inspect-db-cache-hit","title":"","level":0},{"type":"text","content":"Show cache hit rates for tables and indices","level":1},{"type":"link_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"- ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/cli/supabase-inspect-db-table-record-counts","title":"","level":0},{"type":"text","content":"Show estimated number of rows per table","level":1},{"type":"link_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"- ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/cli/supabase-inspect-db-seq-scans","title":"","level":0},{"type":"text","content":"Show number of sequential scans for all tables","level":1},{"type":"link_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"- ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/cli/supabase-inspect-db-replication-slots","title":"","level":0},{"type":"text","content":"Show information about replication slots","level":1},{"type":"link_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"- ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/cli/supabase-inspect-db-role-connections","title":"","level":0},{"type":"text","content":"Show number of active connections","level":1},{"type":"link_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"- ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/cli/supabase-inspect-db-bloat","title":"","level":0},{"type":"text","content":"Show estimated database bloat","level":1},{"type":"link_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"- ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/cli/supabase-inspect-db-vacuum-stats","title":"","level":0},{"type":"text","content":"Show statistics related to vacuum operations","level":1},{"type":"link_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[97,98],"level":0},{"type":"inline","content":"\u003c/BlogCollapsible\u003e","level":1,"lines":[97,98],"children":[{"type":"text","content":"\u003c/BlogCollapsible\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[99,100],"level":0},{"type":"inline","content":"[Easier backups](#easier-backups)","level":1,"lines":[99,100],"children":[{"type":"text","content":"Easier backups","level":0}],"lvl":2,"i":2,"seen":0,"slug":"easier-backups"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[101,102],"level":0},{"type":"inline","content":"![How to back up your database with Supabase CLI](/images/blog/launch-week-8/day-2/backup-your-database.png)","level":1,"lines":[101,102],"children":[{"type":"image","src":"/images/blog/launch-week-8/day-2/backup-your-database.png","title":"","alt":"How to back up your database with Supabase CLI","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[103,104],"level":0},{"type":"inline","content":"We’ve made it even easier to backup and migrate your database, using `supabase db dump`. Under the hood, this simply uses [pg_dump](https://www.postgresql.org/docs/current/app-pgdump.html) (it's just Postgres, after all). However we also handle a few of the hairy issues that you might need to navigate on your own, like object permissions.","level":1,"lines":[103,104],"children":[{"type":"text","content":"We’ve made it even easier to backup and migrate your database, using ","level":0},{"type":"code","content":"supabase db dump","block":false,"level":0},{"type":"text","content":". Under the hood, this simply uses ","level":0},{"type":"link_open","href":"https://www.postgresql.org/docs/current/app-pgdump.html","title":"","level":0},{"type":"text","content":"pg_dump","level":1},{"type":"link_close","level":0},{"type":"text","content":" (it's just Postgres, after all). However we also handle a few of the hairy issues that you might need to navigate on your own, like object permissions.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[105,107],"level":0},{"type":"inline","content":"\u003cBlogCollapsible title=\"See full command\"\u003e\n  \u003cdiv\u003e","level":1,"lines":[105,107],"children":[{"type":"text","content":"\u003cBlogCollapsible title=\"See full command\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cdiv\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"code","content":"{/* prettier-ignore */}\n```markdown\nsupabase db dump --help\nDumps data or schemas from the remote database\n\nUsage:\nsupabase db dump [flags]\n\nFlags:\n--data-only Dumps only data records.\n-f, --file string File path to save the dumped contents.\n--keep-comments Keeps commented lines from pg_dump output.\n--role-only Dumps only cluster roles.\n--use-copy Uses copy statements in place of inserts.\n```\n","block":true,"lines":[108,124],"level":0},{"type":"paragraph_open","tight":false,"lines":[124,126],"level":0},{"type":"inline","content":"\u003c/div\u003e\n\u003c/BlogCollapsible\u003e","level":1,"lines":[124,126],"children":[{"type":"text","content":"\u003c/div\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/BlogCollapsible\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[127,128],"level":0},{"type":"inline","content":"[Improved Database Migrations](#improved-database-migrations)","level":1,"lines":[127,128],"children":[{"type":"text","content":"Improved Database Migrations","level":0}],"lvl":2,"i":3,"seen":0,"slug":"improved-database-migrations"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[129,130],"level":0},{"type":"inline","content":"![modify your database](/images/blog/launch-week-8/day-2/modify-your-database.png)","level":1,"lines":[129,130],"children":[{"type":"image","src":"/images/blog/launch-week-8/day-2/modify-your-database.png","title":"","alt":"modify your database","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[131,132],"level":0},{"type":"inline","content":"We’ve extended the CLI migration feature and added Dashboard support. Database migrations give you a way to update your database using version-controlled SQL files. We’ve built a lot of tooling around our migrations, including [reparation](https://supabase.com/docs/reference/cli/supabase-migration-repair), migration cleanup using the [squash](https://supabase.com/docs/reference/cli/supabase-migration-squash) command, and [diffing](https://supabase.com/docs/reference/cli/supabase-db-diff) (using [migra](https://github.com/djrobstep/migra)) to generate a new migration or to detect schema drift.","level":1,"lines":[131,132],"children":[{"type":"text","content":"We’ve extended the CLI migration feature and added Dashboard support. Database migrations give you a way to update your database using version-controlled SQL files. We’ve built a lot of tooling around our migrations, including ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/cli/supabase-migration-repair","title":"","level":0},{"type":"text","content":"reparation","level":1},{"type":"link_close","level":0},{"type":"text","content":", migration cleanup using the ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/cli/supabase-migration-squash","title":"","level":0},{"type":"text","content":"squash","level":1},{"type":"link_close","level":0},{"type":"text","content":" command, and ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/cli/supabase-db-diff","title":"","level":0},{"type":"text","content":"diffing","level":1},{"type":"link_close","level":0},{"type":"text","content":" (using ","level":0},{"type":"link_open","href":"https://github.com/djrobstep/migra","title":"","level":0},{"type":"text","content":"migra","level":1},{"type":"link_close","level":0},{"type":"text","content":") to generate a new migration or to detect schema drift.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[133,134],"level":0},{"type":"inline","content":"With the new Postgres Language Server, we hope to make it as easy to write Postgres migrations as it is to develop applications in TypeScript, Go, Python, or Rust.","level":1,"lines":[133,134],"children":[{"type":"text","content":"With the new Postgres Language Server, we hope to make it as easy to write Postgres migrations as it is to develop applications in TypeScript, Go, Python, or Rust.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[135,136],"level":0},{"type":"inline","content":"Finally, we’ve added a Migrations view [in the dashboard](https://app.supabase.com/project/_/database/migrations) to track your migration history to improve the discoverability of migrations.","level":1,"lines":[135,136],"children":[{"type":"text","content":"Finally, we’ve added a Migrations view ","level":0},{"type":"link_open","href":"https://app.supabase.com/project/_/database/migrations","title":"","level":0},{"type":"text","content":"in the dashboard","level":1},{"type":"link_close","level":0},{"type":"text","content":" to track your migration history to improve the discoverability of migrations.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[137,139],"level":0},{"type":"inline","content":"\u003cBlogCollapsible title=\"See full command\"\u003e\n  \u003cdiv\u003e","level":1,"lines":[137,139],"children":[{"type":"text","content":"\u003cBlogCollapsible title=\"See full command\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cdiv\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"code","content":"{/* prettier-ignore */}\n```markdown\nsupabase migration\nManage database migration scripts\n\nUsage:\n  supabase migration [command]\n\nAvailable Commands:\n  list        List local and remote migrations\n  new         Create an empty migration script\n  repair      Repair the migration history table\n  squash      Squash migrations to a single file\n  up          Apply pending migrations to local database\n```\n","block":true,"lines":[140,156],"level":0},{"type":"paragraph_open","tight":false,"lines":[156,158],"level":0},{"type":"inline","content":"\u003c/div\u003e\n\u003c/BlogCollapsible\u003e","level":1,"lines":[156,158],"children":[{"type":"text","content":"\u003c/div\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/BlogCollapsible\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[159,160],"level":0},{"type":"inline","content":"[Test and lint your database](#test-and-lint-your-database)","level":1,"lines":[159,160],"children":[{"type":"text","content":"Test and lint your database","level":0}],"lvl":2,"i":4,"seen":0,"slug":"test-and-lint-your-database"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[161,162],"level":0},{"type":"inline","content":"![Test your database](/images/blog/launch-week-8/day-2/test-your-database.png)","level":1,"lines":[161,162],"children":[{"type":"image","src":"/images/blog/launch-week-8/day-2/test-your-database.png","title":"","alt":"Test your database","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[163,164],"level":0},{"type":"inline","content":"We’ve simplified the database testing experience, with `supabase test`. Running `supabase test new` stubs out a [pgTAP test](https://pgtap.org/) for you to fill with testing logic. The CLI includes [pg_prove and the TAP harness](https://pgtap.org/pg_prove), so all you need to do is run `supabase test db`.","level":1,"lines":[163,164],"children":[{"type":"text","content":"We’ve simplified the database testing experience, with ","level":0},{"type":"code","content":"supabase test","block":false,"level":0},{"type":"text","content":". Running ","level":0},{"type":"code","content":"supabase test new","block":false,"level":0},{"type":"text","content":" stubs out a ","level":0},{"type":"link_open","href":"https://pgtap.org/","title":"","level":0},{"type":"text","content":"pgTAP test","level":1},{"type":"link_close","level":0},{"type":"text","content":" for you to fill with testing logic. The CLI includes ","level":0},{"type":"link_open","href":"https://pgtap.org/pg_prove","title":"","level":0},{"type":"text","content":"pg_prove and the TAP harness","level":1},{"type":"link_close","level":0},{"type":"text","content":", so all you need to do is run ","level":0},{"type":"code","content":"supabase test db","block":false,"level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[165,166],"level":0},{"type":"inline","content":"To make life even easier, our friends at [Basejump](https://usebasejump.com/) have created an entire suite of [Supabase Test Helpers](https://github.com/usebasejump/supabase-test-helpers) which make it simple to create users, run tests as an [authenticated user](https://usebasejump.com/blog/testing-on-supabase-with-pgtap#testing-authenticated), and test your [RLS policies](https://usebasejump.com/blog/testing-on-supabase-with-pgtap#rls-testing).","level":1,"lines":[165,166],"children":[{"type":"text","content":"To make life even easier, our friends at ","level":0},{"type":"link_open","href":"https://usebasejump.com/","title":"","level":0},{"type":"text","content":"Basejump","level":1},{"type":"link_close","level":0},{"type":"text","content":" have created an entire suite of ","level":0},{"type":"link_open","href":"https://github.com/usebasejump/supabase-test-helpers","title":"","level":0},{"type":"text","content":"Supabase Test Helpers","level":1},{"type":"link_close","level":0},{"type":"text","content":" which make it simple to create users, run tests as an ","level":0},{"type":"link_open","href":"https://usebasejump.com/blog/testing-on-supabase-with-pgtap#testing-authenticated","title":"","level":0},{"type":"text","content":"authenticated user","level":1},{"type":"link_close","level":0},{"type":"text","content":", and test your ","level":0},{"type":"link_open","href":"https://usebasejump.com/blog/testing-on-supabase-with-pgtap#rls-testing","title":"","level":0},{"type":"text","content":"RLS policies","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[167,168],"level":0},{"type":"inline","content":"Finally, while you wait for us to make progress on the Language Server, we’ve added [support for linting](https://supabase.com/docs/guides/cli/testing-and-linting#linting-your-database) through the excellent [plpgsql_check](https://github.com/okbob/plpgsql_check) extension.","level":1,"lines":[167,168],"children":[{"type":"text","content":"Finally, while you wait for us to make progress on the Language Server, we’ve added ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/cli/testing-and-linting#linting-your-database","title":"","level":0},{"type":"text","content":"support for linting","level":1},{"type":"link_close","level":0},{"type":"text","content":" through the excellent ","level":0},{"type":"link_open","href":"https://github.com/okbob/plpgsql_check","title":"","level":0},{"type":"text","content":"plpgsql_check","level":1},{"type":"link_close","level":0},{"type":"text","content":" extension.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[169,171],"level":0},{"type":"inline","content":"\u003cBlogCollapsible title=\"See full command\"\u003e\n  \u003cdiv\u003e","level":1,"lines":[169,171],"children":[{"type":"text","content":"\u003cBlogCollapsible title=\"See full command\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cdiv\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"code","content":"{/* prettier-ignore */}\n```markdown\nsupabase test\nRun tests on local Supabase containers\n\nUsage:\n  supabase test [command]\n\nAvailable Commands:\n  db          Tests local database with pgTAP\n  new         Create a new test file\n````\n\n{/* prettier-ignore */}\n```markdown\nsupabase db lint\nChecks local database for typing error\n\nUsage:\n  supabase db lint [flags]\n\nFlags:\n  -h, --help                        help for lint\n      --level [ warning | error ]   Error level to emit. (default warning)\n      --linked                      Lints the linked project for schema errors.\n  -s, --schema strings              List of schema to include. (default all)\n```\n","block":true,"lines":[172,200],"level":0},{"type":"paragraph_open","tight":false,"lines":[200,202],"level":0},{"type":"inline","content":"\u003c/div\u003e\n\u003c/BlogCollapsible\u003e","level":1,"lines":[200,202],"children":[{"type":"text","content":"\u003c/div\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/BlogCollapsible\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[203,204],"level":0},{"type":"inline","content":"[Database seeding](#database-seeding)","level":1,"lines":[203,204],"children":[{"type":"text","content":"Database seeding","level":0}],"lvl":2,"i":5,"seen":0,"slug":"database-seeding"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[205,206],"level":0},{"type":"inline","content":"![Support for seeding](/images/blog/launch-week-8/day-2/support-for-seeding.png)","level":1,"lines":[205,206],"children":[{"type":"image","src":"/images/blog/launch-week-8/day-2/support-for-seeding.png","title":"","alt":"Support for seeding","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[207,208],"level":0},{"type":"inline","content":"Seeding is the process of populating a database with initial data, typically used to provide sample or default records for testing and development purposes. This gives you a reproducible development environment across your entire team.","level":1,"lines":[207,208],"children":[{"type":"text","content":"Seeding is the process of populating a database with initial data, typically used to provide sample or default records for testing and development purposes. This gives you a reproducible development environment across your entire team.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[209,210],"level":0},{"type":"inline","content":"We’ve added [support for seeding](https://supabase.com/docs/guides/cli/seeding-your-database) to populate your local databases with data whenever you run `supabase start` or `supabase db reset`.","level":1,"lines":[209,210],"children":[{"type":"text","content":"We’ve added ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/cli/seeding-your-database","title":"","level":0},{"type":"text","content":"support for seeding","level":1},{"type":"link_close","level":0},{"type":"text","content":" to populate your local databases with data whenever you run ","level":0},{"type":"code","content":"supabase start","block":false,"level":0},{"type":"text","content":" or ","level":0},{"type":"code","content":"supabase db reset","block":false,"level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[211,212],"level":0},{"type":"inline","content":"We’ve also worked with our friends at Snaplet to [generate seed data](https://supabase.com/docs/guides/cli/seeding-your-database#generating-seed-data) directly from your database:","level":1,"lines":[211,212],"children":[{"type":"text","content":"We’ve also worked with our friends at Snaplet to ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/cli/seeding-your-database#generating-seed-data","title":"","level":0},{"type":"text","content":"generate seed data","level":1},{"type":"link_close","level":0},{"type":"text","content":" directly from your database:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"markdown","content":"npx snaplet generate --sql \u003e supabase/seed.sql\n","lines":[213,216],"level":0},{"type":"heading_open","hLevel":2,"lines":[217,218],"level":0},{"type":"inline","content":"[Type generators](#type-generators)","level":1,"lines":[217,218],"children":[{"type":"text","content":"Type generators","level":0}],"lvl":2,"i":6,"seen":0,"slug":"type-generators"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[219,220],"level":0},{"type":"inline","content":"![End to end Type Safety](/images/blog/launch-week-8/day-2/end-to-end-type-safety.png)","level":1,"lines":[219,220],"children":[{"type":"image","src":"/images/blog/launch-week-8/day-2/end-to-end-type-safety.png","title":"","alt":"End to end Type Safety","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[221,222],"level":0},{"type":"inline","content":"Type generators introspect a PostgreSQL schema and automatically generate TypeScript definitions. This gives you [end-to-end type safety](https://www.youtube.com/watch?v=VSNgAIObBdw) from the database to the browser.","level":1,"lines":[221,222],"children":[{"type":"text","content":"Type generators introspect a PostgreSQL schema and automatically generate TypeScript definitions. This gives you ","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=VSNgAIObBdw","title":"","level":0},{"type":"text","content":"end-to-end type safety","level":1},{"type":"link_close","level":0},{"type":"text","content":" from the database to the browser.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[223,224],"level":0},{"type":"inline","content":"In the past month, we've added relationship detection in supabase-js. Foreign keys are now included in the generated types so that supabase-js can detect whether a referenced table should be an array (one-to-many) or an object (many-to-one). We've also added Helper Types to improve the developer experience for common scenarios, like short-hand accessors:","level":1,"lines":[223,224],"children":[{"type":"text","content":"In the past month, we've added relationship detection in supabase-js. Foreign keys are now included in the generated types so that supabase-js can detect whether a referenced table should be an array (one-to-many) or an object (many-to-one). We've also added Helper Types to improve the developer experience for common scenarios, like short-hand accessors:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"tsx","content":"// Before\nlet movie: Database['public']['Tables']['movies']['Row'] = // ...\n\n// After\nlet movie: Tables\u003c'movies'\u003e = // ...\n","lines":[225,232],"level":0},{"type":"paragraph_open","tight":false,"lines":[233,235],"level":0},{"type":"inline","content":"\u003cBlogCollapsible title=\"See full command\"\u003e\n  \u003cdiv\u003e","level":1,"lines":[233,235],"children":[{"type":"text","content":"\u003cBlogCollapsible title=\"See full command\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cdiv\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"code","content":"{/* prettier-ignore */}\n```markdown\nsupabase gen types\nGenerate types from Postgres schema\n\nUsage:\n  supabase gen types [command]\n\nAvailable Commands:\n  typescript  Generate types for TypeScript\n```\n","block":true,"lines":[236,248],"level":0},{"type":"paragraph_open","tight":false,"lines":[248,250],"level":0},{"type":"inline","content":"\u003c/div\u003e\n\u003c/BlogCollapsible\u003e","level":1,"lines":[248,250],"children":[{"type":"text","content":"\u003c/div\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/BlogCollapsible\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[251,252],"level":0},{"type":"inline","content":"[Official GitHub Action](#official-github-action)","level":1,"lines":[251,252],"children":[{"type":"text","content":"Official GitHub Action","level":0}],"lvl":2,"i":7,"seen":0,"slug":"official-github-action"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[253,254],"level":0},{"type":"inline","content":"![Support for GitHub Actions](/images/blog/launch-week-8/day-2/support-for-github-actions.png)","level":1,"lines":[253,254],"children":[{"type":"image","src":"/images/blog/launch-week-8/day-2/support-for-github-actions.png","title":"","alt":"Support for GitHub Actions","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[255,256],"level":0},{"type":"inline","content":"We’ve developed an official [GitHub Action](https://github.com/marketplace/actions/supabase-cli-action) which leverages the CLI. You can [generate types on every PR](https://supabase.com/docs/guides/cli/github-action/generating-types), or run [your tests on every commit](https://supabase.com/docs/guides/cli/github-action/testing).","level":1,"lines":[255,256],"children":[{"type":"text","content":"We’ve developed an official ","level":0},{"type":"link_open","href":"https://github.com/marketplace/actions/supabase-cli-action","title":"","level":0},{"type":"text","content":"GitHub Action","level":1},{"type":"link_close","level":0},{"type":"text","content":" which leverages the CLI. You can ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/cli/github-action/generating-types","title":"","level":0},{"type":"text","content":"generate types on every PR","level":1},{"type":"link_close","level":0},{"type":"text","content":", or run ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/cli/github-action/testing","title":"","level":0},{"type":"text","content":"your tests on every commit","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[257,258],"level":0},{"type":"inline","content":"[Local Logging and Debugging](#local-logging-and-debugging)","level":1,"lines":[257,258],"children":[{"type":"text","content":"Local Logging and Debugging","level":0}],"lvl":2,"i":8,"seen":0,"slug":"local-logging-and-debugging"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[259,260],"level":0},{"type":"inline","content":"Logs are now accessible locally in the Dashboard. Last launch week we released an open source logging server, with support for BigQuery. In the past few months we’ve added Postgres support to this server. This means that all of your local logs are accessible with no additional config - simply run supabase start and then visit the local dashboard to start debugging.","level":1,"lines":[259,260],"children":[{"type":"text","content":"Logs are now accessible locally in the Dashboard. Last launch week we released an open source logging server, with support for BigQuery. In the past few months we’ve added Postgres support to this server. This means that all of your local logs are accessible with no additional config - simply run supabase start and then visit the local dashboard to start debugging.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[261,262],"level":0},{"type":"inline","content":"![Logs are now accessible locally in the dashboard](/images/blog/launch-week-8/day-2/local-logging-debugging.png)","level":1,"lines":[261,262],"children":[{"type":"image","src":"/images/blog/launch-week-8/day-2/local-logging-debugging.png","title":"","alt":"Logs are now accessible locally in the dashboard","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[263,264],"level":0},{"type":"inline","content":"[Stable releases](#stable-releases)","level":1,"lines":[263,264],"children":[{"type":"text","content":"Stable releases","level":0}],"lvl":2,"i":9,"seen":0,"slug":"stable-releases"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[265,266],"level":0},{"type":"inline","content":"We’ve moved the Supabase CLI to a fortnightly stable-release cycle. Every 2 weeks, we will update the [`latest`](https://www.npmjs.com/package/supabase?activeTab=versions) tag on npm, the [`supabase/tap`](https://github.com/supabase/homebrew-tap/blob/main/supabase.rb) for homebrew, and the [`supabase`](https://github.com/supabase/homebrew-tap/blob/main/supabase.rb) scoop bucket. You can find the binary downloads in our GitHub [latest release](https://github.com/supabase/cli/releases/latest).","level":1,"lines":[265,266],"children":[{"type":"text","content":"We’ve moved the Supabase CLI to a fortnightly stable-release cycle. Every 2 weeks, we will update the ","level":0},{"type":"link_open","href":"https://www.npmjs.com/package/supabase?activeTab=versions","title":"","level":0},{"type":"code","content":"latest","block":false,"level":1},{"type":"link_close","level":0},{"type":"text","content":" tag on npm, the ","level":0},{"type":"link_open","href":"https://github.com/supabase/homebrew-tap/blob/main/supabase.rb","title":"","level":0},{"type":"code","content":"supabase/tap","block":false,"level":1},{"type":"link_close","level":0},{"type":"text","content":" for homebrew, and the ","level":0},{"type":"link_open","href":"https://github.com/supabase/homebrew-tap/blob/main/supabase.rb","title":"","level":0},{"type":"code","content":"supabase","block":false,"level":1},{"type":"link_close","level":0},{"type":"text","content":" scoop bucket. You can find the binary downloads in our GitHub ","level":0},{"type":"link_open","href":"https://github.com/supabase/cli/releases/latest","title":"","level":0},{"type":"text","content":"latest release","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[267,268],"level":0},{"type":"inline","content":"For the adventurous feature hunters, we’ve added a `beta` release channel for the CLI, with new releases on every PR merged. You can follow [this guide](https://github.com/supabase/cli#install-the-cli) to install Supabase CLI (beta).","level":1,"lines":[267,268],"children":[{"type":"text","content":"For the adventurous feature hunters, we’ve added a ","level":0},{"type":"code","content":"beta","block":false,"level":0},{"type":"text","content":" release channel for the CLI, with new releases on every PR merged. You can follow ","level":0},{"type":"link_open","href":"https://github.com/supabase/cli#install-the-cli","title":"","level":0},{"type":"text","content":"this guide","level":1},{"type":"link_close","level":0},{"type":"text","content":" to install Supabase CLI (beta).","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[269,270],"level":0},{"type":"inline","content":"[Branching and Preview Environments](#branching-and-preview-environments)","level":1,"lines":[269,270],"children":[{"type":"text","content":"Branching and Preview Environments","level":0}],"lvl":2,"i":10,"seen":0,"slug":"branching-and-preview-environments"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[271,272],"level":0},{"type":"inline","content":"And finally, probably our most anticipated feature - branching:","level":1,"lines":[271,272],"children":[{"type":"text","content":"And finally, probably our most anticipated feature - branching:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[273,274],"level":0},{"type":"inline","content":"![Searching branches with Supabase](/images/blog/launch-week-8/day-2/branching-02.png)","level":1,"lines":[273,274],"children":[{"type":"image","src":"/images/blog/launch-week-8/day-2/branching-02.png","title":"","alt":"Searching branches with Supabase","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[275,276],"level":0},{"type":"inline","content":"We’ve made major improvements to our local development with the features above - but we have bigger ambitions. For several months we’ve been developing Supabase branching and today we're opening it up for alpha testers.","level":1,"lines":[275,276],"children":[{"type":"text","content":"We’ve made major improvements to our local development with the features above - but we have bigger ambitions. For several months we’ve been developing Supabase branching and today we're opening it up for alpha testers.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[277,278],"level":0},{"type":"inline","content":"Supabase isn’t simply a database, it’s an entire backend - everything from your Postgres database to your [50GB videos](https://supabase.com/blog/storage-v3-resumable-uploads). Branching improves the experience of managing environments so that developers and teams spend less time on DevOps and more time building.","level":1,"lines":[277,278],"children":[{"type":"text","content":"Supabase isn’t simply a database, it’s an entire backend - everything from your Postgres database to your ","level":0},{"type":"link_open","href":"https://supabase.com/blog/storage-v3-resumable-uploads","title":"","level":0},{"type":"text","content":"50GB videos","level":1},{"type":"link_close","level":0},{"type":"text","content":". Branching improves the experience of managing environments so that developers and teams spend less time on DevOps and more time building.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[279,280],"level":0},{"type":"inline","content":"[Supabase branching is hard](#supabase-branching-is-hard)","level":1,"lines":[279,280],"children":[{"type":"text","content":"Supabase branching is hard","level":0}],"lvl":3,"i":11,"seen":0,"slug":"supabase-branching-is-hard"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[281,282],"level":0},{"type":"inline","content":"Every project is a Postgres database, wrapped in a suite of tools like [Auth](https://supabase.com/auth), [Storage](https://supabase.com/storage), [Edge Functions](https://supabase.com/edge-functions), [Realtime](https://supabase.com/realtime) and [Vectors](https://supabase.com/vector), and encompassed by [API middleware](https://supabase.com/docs/guides/api) and [logs](https://supabase.com/blog/supabase-logs-self-hosted).","level":1,"lines":[281,282],"children":[{"type":"text","content":"Every project is a Postgres database, wrapped in a suite of tools like ","level":0},{"type":"link_open","href":"https://supabase.com/auth","title":"","level":0},{"type":"text","content":"Auth","level":1},{"type":"link_close","level":0},{"type":"text","content":", ","level":0},{"type":"link_open","href":"https://supabase.com/storage","title":"","level":0},{"type":"text","content":"Storage","level":1},{"type":"link_close","level":0},{"type":"text","content":", ","level":0},{"type":"link_open","href":"https://supabase.com/edge-functions","title":"","level":0},{"type":"text","content":"Edge Functions","level":1},{"type":"link_close","level":0},{"type":"text","content":", ","level":0},{"type":"link_open","href":"https://supabase.com/realtime","title":"","level":0},{"type":"text","content":"Realtime","level":1},{"type":"link_close","level":0},{"type":"text","content":" and ","level":0},{"type":"link_open","href":"https://supabase.com/vector","title":"","level":0},{"type":"text","content":"Vectors","level":1},{"type":"link_close","level":0},{"type":"text","content":", and encompassed by ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/api","title":"","level":0},{"type":"text","content":"API middleware","level":1},{"type":"link_close","level":0},{"type":"text","content":" and ","level":0},{"type":"link_open","href":"https://supabase.com/blog/supabase-logs-self-hosted","title":"","level":0},{"type":"text","content":"logs","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[283,284],"level":0},{"type":"inline","content":"A good branching solution requires each tool to provide multi-tenancy support so that:","level":1,"lines":[283,284],"children":[{"type":"text","content":"A good branching solution requires each tool to provide multi-tenancy support so that:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"ordered_list_open","order":1,"lines":[285,288],"level":0},{"type":"list_item_open","lines":[285,286],"level":1},{"type":"paragraph_open","tight":true,"lines":[285,286],"level":2},{"type":"inline","content":"Data can be isolated from production for security.","level":3,"lines":[285,286],"children":[{"type":"text","content":"Data can be isolated from production for security.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[286,288],"level":1},{"type":"paragraph_open","tight":true,"lines":[286,287],"level":2},{"type":"inline","content":"Compute can be isolated from each other to avoid noisy-neighbors.","level":3,"lines":[286,287],"children":[{"type":"text","content":"Compute can be isolated from each other to avoid noisy-neighbors.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"ordered_list_close","level":0},{"type":"heading_open","hLevel":3,"lines":[288,289],"level":0},{"type":"inline","content":"[How does branching work?](#how-does-branching-work)","level":1,"lines":[288,289],"children":[{"type":"text","content":"How does branching work?","level":0}],"lvl":3,"i":12,"seen":0,"slug":"how-does-branching-work"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[290,291],"level":0},{"type":"inline","content":"We use Git to bridge the gap between your local development environment and your hosted database. For now, we’ve focused on GitHub.","level":1,"lines":[290,291],"children":[{"type":"text","content":"We use Git to bridge the gap between your local development environment and your hosted database. For now, we’ve focused on GitHub.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[292,293],"level":0},{"type":"inline","content":"Every time you open a new Pull Request on GitHub, a corresponding “Preview Environment” is created. Each preview branch is an isolated Firecracker instance that pauses automatically after a period of inactivity. Every time a change is pushed to GitHub, the migrations within the `./supabase/migrations` folder are run against the Preview Branch so that your entire team is working from the same source of truth.","level":1,"lines":[292,293],"children":[{"type":"text","content":"Every time you open a new Pull Request on GitHub, a corresponding “Preview Environment” is created. Each preview branch is an isolated Firecracker instance that pauses automatically after a period of inactivity. Every time a change is pushed to GitHub, the migrations within the ","level":0},{"type":"code","content":"./supabase/migrations","block":false,"level":0},{"type":"text","content":" folder are run against the Preview Branch so that your entire team is working from the same source of truth.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[294,295],"level":0},{"type":"inline","content":"When you hit merge on your Pull Request we run the migrations on your Production database.","level":1,"lines":[294,295],"children":[{"type":"text","content":"When you hit merge on your Pull Request we run the migrations on your Production database.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[296,297],"level":0},{"type":"inline","content":"[What about data?](#what-about-data)","level":1,"lines":[296,297],"children":[{"type":"text","content":"What about data?","level":0}],"lvl":3,"i":13,"seen":0,"slug":"what-about-data"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[298,299],"level":0},{"type":"inline","content":"We’re starting with seed data. Any SQL with `./supabase/seed.sql` will populate your Preview Branch. This provides your entire team with an isolated and reproducible environment, safe from any data privacy concerns.","level":1,"lines":[298,299],"children":[{"type":"text","content":"We’re starting with seed data. Any SQL with ","level":0},{"type":"code","content":"./supabase/seed.sql","block":false,"level":0},{"type":"text","content":" will populate your Preview Branch. This provides your entire team with an isolated and reproducible environment, safe from any data privacy concerns.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[300,301],"level":0},{"type":"inline","content":"Importantly, we _aren’t_ cloning production data until we find something appropriate for data security. We know that copy-on-write is an available option, and with the appropriate anonymization techniques it seems like a promising way to provide a “production-like” test environment.","level":1,"lines":[300,301],"children":[{"type":"text","content":"Importantly, we ","level":0},{"type":"em_open","level":0},{"type":"text","content":"aren’t","level":1},{"type":"em_close","level":0},{"type":"text","content":" cloning production data until we find something appropriate for data security. We know that copy-on-write is an available option, and with the appropriate anonymization techniques it seems like a promising way to provide a “production-like” test environment.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[302,303],"level":0},{"type":"inline","content":"We’ll also need to figure out what this means for large files in Supabase Storage. Do you need to anonymize your photos and videos? This is a work in progress and we’re open to feedback.","level":1,"lines":[302,303],"children":[{"type":"text","content":"We’ll also need to figure out what this means for large files in Supabase Storage. Do you need to anonymize your photos and videos? This is a work in progress and we’re open to feedback.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[304,305],"level":0},{"type":"inline","content":"Either way, we want to support _both_ seed data and anonymized production data, so that teams can choose their preference based on their risk profile. It makes sense to start with a seed.","level":1,"lines":[304,305],"children":[{"type":"text","content":"Either way, we want to support ","level":0},{"type":"em_open","level":0},{"type":"text","content":"both","level":1},{"type":"em_close","level":0},{"type":"text","content":" seed data and anonymized production data, so that teams can choose their preference based on their risk profile. It makes sense to start with a seed.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[306,307],"level":0},{"type":"inline","content":"[Is it available yet?](#is-it-available-yet)","level":1,"lines":[306,307],"children":[{"type":"text","content":"Is it available yet?","level":0}],"lvl":3,"i":14,"seen":0,"slug":"is-it-available-yet"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[308,309],"level":0},{"type":"inline","content":"\u003cAdmonition type=\"note\" label=\"Branching update 12/13/2023\"\u003e","level":1,"lines":[308,309],"children":[{"type":"text","content":"\u003cAdmonition type=\"note\" label=\"Branching update 12/13/2023\"\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[310,311],"level":0},{"type":"inline","content":"We are [rolling out access](https://supabase.com/blog/supabase-branching) and we'll be onboarding organizations in batches over the next few weeks. You can still [sign up for access](https://forms.supabase.com/branching-request).","level":1,"lines":[310,311],"children":[{"type":"text","content":"We are ","level":0},{"type":"link_open","href":"https://supabase.com/blog/supabase-branching","title":"","level":0},{"type":"text","content":"rolling out access","level":1},{"type":"link_close","level":0},{"type":"text","content":" and we'll be onboarding organizations in batches over the next few weeks. You can still ","level":0},{"type":"link_open","href":"https://forms.supabase.com/branching-request","title":"","level":0},{"type":"text","content":"sign up for access","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[312,313],"level":0},{"type":"inline","content":"\u003c/Admonition\u003e","level":1,"lines":[312,313],"children":[{"type":"text","content":"\u003c/Admonition\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[314,315],"level":0},{"type":"inline","content":"[Get started](#get-started)","level":1,"lines":[314,315],"children":[{"type":"text","content":"Get started","level":0}],"lvl":2,"i":15,"seen":0,"slug":"get-started"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[316,317],"level":0},{"type":"inline","content":"Jump into our updated [Local Development documentation](https://supabase.com/docs/guides/cli) to get started with the CLI.","level":1,"lines":[316,317],"children":[{"type":"text","content":"Jump into our updated ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/cli","title":"","level":0},{"type":"text","content":"Local Development documentation","level":1},{"type":"link_close","level":0},{"type":"text","content":" to get started with the CLI.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[318,319],"level":0},{"type":"inline","content":"If you’re an existing user simply [update your CLI](https://supabase.com/docs/guides/cli/getting-started#updating-the-supabase-cli) and check out the [full command reference](https://supabase.com/docs/reference/cli/introduction) for all the latest commands.","level":1,"lines":[318,319],"children":[{"type":"text","content":"If you’re an existing user simply ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/cli/getting-started#updating-the-supabase-cli","title":"","level":0},{"type":"text","content":"update your CLI","level":1},{"type":"link_close","level":0},{"type":"text","content":" and check out the ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/cli/introduction","title":"","level":0},{"type":"text","content":"full command reference","level":1},{"type":"link_close","level":0},{"type":"text","content":" for all the latest commands.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[320,321],"level":0},{"type":"inline","content":"[More Launch Week 8](#more-launch-week-8)","level":1,"lines":[320,321],"children":[{"type":"text","content":"More Launch Week 8","level":0}],"lvl":2,"i":16,"seen":0,"slug":"more-launch-week-8"},{"type":"heading_close","hLevel":2,"level":0},{"type":"bullet_list_open","lines":[322,327],"level":0},{"type":"list_item_open","lines":[322,323],"level":1},{"type":"paragraph_open","tight":true,"lines":[322,323],"level":2},{"type":"inline","content":"[Hugging Face is now supported in Supabase](https://supabase.com/blog/hugging-face-supabase)","level":3,"lines":[322,323],"children":[{"type":"link_open","href":"https://supabase.com/blog/hugging-face-supabase","title":"","level":0},{"type":"text","content":"Hugging Face is now supported in Supabase","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[323,324],"level":1},{"type":"paragraph_open","tight":true,"lines":[323,324],"level":2},{"type":"inline","content":"[Launch Week 8](https://supabase.com/launch-week)","level":3,"lines":[323,324],"children":[{"type":"link_open","href":"https://supabase.com/launch-week","title":"","level":0},{"type":"text","content":"Launch Week 8","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[324,325],"level":1},{"type":"paragraph_open","tight":true,"lines":[324,325],"level":2},{"type":"inline","content":"[Coding the stars - an interactive constellation with Three.js and React Three Fiber](https://supabase.com/blog/interactive-constellation-threejs-react-three-fiber)","level":3,"lines":[324,325],"children":[{"type":"link_open","href":"https://supabase.com/blog/interactive-constellation-threejs-react-three-fiber","title":"","level":0},{"type":"text","content":"Coding the stars - an interactive constellation with Three.js and React Three Fiber","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[325,326],"level":1},{"type":"paragraph_open","tight":true,"lines":[325,326],"level":2},{"type":"inline","content":"[Why we'll stay remote](https://supabase.com/blog/why-supabase-remote)","level":3,"lines":[325,326],"children":[{"type":"link_open","href":"https://supabase.com/blog/why-supabase-remote","title":"","level":0},{"type":"text","content":"Why we'll stay remote","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[326,327],"level":1},{"type":"paragraph_open","tight":true,"lines":[326,327],"level":2},{"type":"inline","content":"[Postgres Language Server](https://github.com/supabase/postgres_lsp)","level":3,"lines":[326,327],"children":[{"type":"link_open","href":"https://github.com/supabase/postgres_lsp","title":"","level":0},{"type":"text","content":"Postgres Language Server","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0}],"content":"- [Postgres Language Server](#postgres-language-server)\n- [Observability tools for Postgres](#observability-tools-for-postgres)\n- [Easier backups](#easier-backups)\n- [Improved Database Migrations](#improved-database-migrations)\n- [Test and lint your database](#test-and-lint-your-database)\n- [Database seeding](#database-seeding)\n- [Type generators](#type-generators)\n- [Official GitHub Action](#official-github-action)\n- [Local Logging and Debugging](#local-logging-and-debugging)\n- [Stable releases](#stable-releases)\n- [Branching and Preview Environments](#branching-and-preview-environments)\n  * [Supabase branching is hard](#supabase-branching-is-hard)\n  * [How does branching work?](#how-does-branching-work)\n  * [What about data?](#what-about-data)\n  * [Is it available yet?](#is-it-available-yet)\n- [Get started](#get-started)\n- [More Launch Week 8](#more-launch-week-8)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-local-dev"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>