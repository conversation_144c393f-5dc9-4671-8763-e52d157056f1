<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase Beta April 2021</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Supabase &quot;gardening&quot; - stability, security, and community support." data-next-head=""/><meta property="og:title" content="Supabase Beta April 2021" data-next-head=""/><meta property="og:description" content="Supabase &quot;gardening&quot; - stability, security, and community support." data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-beta-april-2021" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2021-05-05" data-next-head=""/><meta property="article:author" content="https://github.com/kiwicopple" data-next-head=""/><meta property="article:tag" content="release-notes" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/april-2021/release-apr-2021.jpg" data-next-head=""/><meta property="og:image:alt" content="Supabase Beta April 2021 thumbnail" data-next-head=""/><meta property="og:video" content="https://www.youtube.com/v/uWJmUTCFdak" data-next-head=""/><meta property="og:video:type" content="application/x-shockwave-flash" data-next-head=""/><meta property="og:video:width" content="640" data-next-head=""/><meta property="og:video:height" content="385" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Supabase Beta April 2021</h1><div class="text-light flex space-x-3 text-sm"><p>05 May 2021</p><p>•</p><p>5 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/kiwicopple"><div class="flex items-center gap-3"><div class="w-10"><img alt="Paul Copplestone avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fkiwicopple.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fkiwicopple.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fkiwicopple.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Paul Copplestone</span><span class="text-foreground-lighter mb-0 text-xs">CEO and Co-Founder</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Supabase Beta April 2021" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fapril-2021%2Frelease-apr-2021.jpg&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fapril-2021%2Frelease-apr-2021.jpg&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fapril-2021%2Frelease-apr-2021.jpg&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fapril-2021%2Frelease-apr-2021.jpg&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fapril-2021%2Frelease-apr-2021.jpg&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fapril-2021%2Frelease-apr-2021.jpg&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fapril-2021%2Frelease-apr-2021.jpg&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fapril-2021%2Frelease-apr-2021.jpg&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fapril-2021%2Frelease-apr-2021.jpg&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fapril-2021%2Frelease-apr-2021.jpg&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fapril-2021%2Frelease-apr-2021.jpg&amp;w=3840&amp;q=100"/></div><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p>This month was a &quot;gardening&quot; month for Supabase. The team focused on stability, security, and community support.
Check out what we were working on below, as well as some incredible Community contributions.</p>
<h3 id="quick-demo" class="group scroll-mt-24">Quick demo<a href="#quick-demo" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Watch a full demo:</p>
<div class="video-container"><iframe class="video-with-border w-full" src="https://www.youtube-nocookie.com/embed/uWJmUTCFdak" frameBorder="1" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen=""></iframe></div>
<h2 id="light-mode" class="group scroll-mt-24">Light Mode<a href="#light-mode" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We&#x27;re a developer tool, which means that Dark Mode is <a href="https://twitter.com/supabase/status/1388131942919376904">extremely popular</a>.</p>
<p></p>
<p>While Dark mode is great, for some people it&#x27;s not an option. Dark Mode is difficult to use for developers with astigmatisms,
or even just working in brightly-lit environments.</p>
<p>So today we&#x27;re shipping Light Mode. Access it in the settings of your <a href="../dashboard/org.html">Dashboard</a>.</p>
<video width="99%" autoplay="" muted="" playsinline="" controls=""><source src="https://supabase.com/images/blog/april-2021/light-mode.mp4" type="video/mp4"/></video>
<h2 id="translations" class="group scroll-mt-24">Translations<a href="#translations" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>With the help of the community, we <a href="https://github.com/supabase/supabase/issues/1341">started internationalizing</a> our main repository:</p>
<ul>
<li><a href="https://github.com/supabase/supabase/blob/master/i18n/README.ar.md">Arabic | العربية</a></li>
<li><a href="https://github.com/supabase/supabase/blob/master/i18n/README.zh-cn.md">Chinese / 中文</a></li>
<li><a href="https://github.com/supabase/supabase/blob/master/i18n/README.nl.md">Dutch / Nederlands</a></li>
<li><a href="https://github.com/supabase/supabase">English</a></li>
<li><a href="https://github.com/supabase/supabase/blob/master/i18n/README.fr.md">French / Français</a></li>
<li><a href="https://github.com/supabase/supabase/blob/master/i18n/README.de.md">German / Deutsch</a></li>
<li><a href="https://github.com/supabase/supabase/blob/master/i18n/README.hi.md">Hindi / हिंदी</a></li>
<li><a href="https://github.com/supabase/supabase/blob/master/i18n/README.ne.md">Nepali / नेपाली</a></li>
<li><a href="https://github.com/supabase/supabase/blob/master/i18n/README.it.md">Italiano / Italian</a></li>
<li><a href="https://github.com/supabase/supabase/blob/master/i18n/README.jp.md">Japanese / 日本語</a></li>
<li><a href="https://github.com/supabase/supabase/blob/master/i18n/README.nb-no.md">Norwegian (Bokmål) / Norsk (Bokmål)</a></li>
<li><a href="https://github.com/supabase/supabase/blob/master/i18n/README.pl.md">Polish / Polski</a></li>
<li><a href="https://github.com/supabase/supabase/blob/master/i18n/README.pt.md">Portuguese / Portuguese</a></li>
<li><a href="https://github.com/supabase/supabase/blob/master/i18n/README.pt-br.md">Portuguese (Brazilian) / Português Brasileiro</a></li>
<li><a href="https://github.com/supabase/supabase/blob/master/i18n/README.ru.md">Russian / Pусский</a></li>
<li><a href="https://github.com/supabase/supabase/blob/master/i18n/README.es.md">Spanish / Español</a></li>
<li><a href="https://github.com/supabase/supabase/blob/master/i18n/README.zh-tw.md">Traditional Chinese / 正體中文</a></li>
<li><a href="https://github.com/supabase/supabase/blob/master/i18n/README.tr.md">Turkish / Türkçe</a></li>
<li><a href="https://github.com/supabase/supabase/blob/master/i18n/README.uk.md">Ukrainian / Українська</a></li>
</ul>
<p></p>
<h2 id="openapi-spec-for-storage" class="group scroll-mt-24">OpenAPI spec for Storage<a href="#openapi-spec-for-storage" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We released <a href="https://supabase.github.io/storage">Storage Api docs</a> built using OpenAPI (swagger).</p>
<p></p>
<h2 id="stripe-sync-engine-experimental" class="group scroll-mt-24">Stripe Sync Engine (Experimental)<a href="#stripe-sync-engine-experimental" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We <a href="https://github.com/supabase/stripe-sync-engine">open-sourced a server</a> which keeps any Postgres database in sync with Stripe.
This is experimental only. We&#x27;re evaluating other tools such as <a href="https://www.singer.io/tap/stripe/postgresql/">Singer</a>,
which provide a more general solution (but are less &quot;realtime&quot;), and we&#x27;re opening it up here to gather feedback.</p>
<p></p>
<h2 id="community-spotlight-threaded-comments" class="group scroll-mt-24">Community spotlight: Threaded comments<a href="#community-spotlight-threaded-comments" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>One of the most powerful Postgres features is &quot;recursive CTEs&quot; which can be used for nested items (comments, pages, friend-graphs). <a href="https://twitter.com/lawrencecchen">@lawrencecchen</a> has built a full <a href="https://github.com/lawrencecchen/threaded-comments">Threaded Comments demo</a> which you can <a href="https://github.com/lawrencecchen/threaded-comments#instant-deploy">Deploy with a single click</a>. Want to add comments to your blog with <a href="https://supabase.com/docs/reference/javascript/textsearch">Full Text Search</a>? Just use Postgres.</p>
<video width="99%" autoplay="" muted="" playsinline="" controls=""><source src="https://supabase.com/images/blog/april-2021/threaded-small.mp4" type="video/mp4"/></video>
<h2 id="community-spotlight-supascript" class="group scroll-mt-24">Community spotlight: SupaScript<a href="#community-spotlight-supascript" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>It looks like <a href="https://twitter.com/burggraf2">@burggraf2</a> got tired of waiting for us to ship Functions, and decided to
build a whole JS ecosystem within his Supabase database. If you want to write PG functions in JS, import remote libraries
from the web, and console log to your browser, check out this <a href="https://github.com/burggraf/SupaScript">SupaScript repo</a>.</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>// After installing:</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>// https://github.com/burggraf/SupaScript#installation</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>/**</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span> * Get all users who logged in this week.</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span> * Use in the database:  select * from users_this_week();</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span> * Use in the browser:   supabase.rpc(&#x27;users_this_week&#x27;);</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span> */</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>create or replace function users_this_week()</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>returns json as $$</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>  const moment = require(&#x27;https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.js&#x27;, false);</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>  const lastWeek = moment().subtract(7, &#x27;days&#x27;);</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>  const query = &#x27;select * from auth.users where created_at &gt; $1&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>  const users = sql(query, lastWeek);</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>  return users;</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>$$ language plv8;</span></div></div><br/></code></div></div>
<h2 id="community-spotlight-fireship" class="group scroll-mt-24">Community spotlight: Fireship<a href="#community-spotlight-fireship" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Fireship reviewed Supabase last week, and despite being a (self-proclaimed) Firebase fan-boy, the review was very impartial.</p>
<div class="video-container"><iframe class="video-with-border w-full" src="https://www.youtube-nocookie.com/embed/WiwfiVdfRIc" frameBorder="1" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen=""></iframe></div>
<h2 id="community" class="group scroll-mt-24">Community<a href="#community" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ul>
<li>Watch <a href="https://twitter.com/everConfusedGuy">@everConfusedGuy</a>&#x27;s talk at DevX conf - Building Supabase Storage: <a href="https://www.youtube.com/watch?v=YsUYOsq_o7g">YouTube video</a></li>
<li>DuckDuckAbdaal by <a href="https://twitter.com/Nutlope">@Nutlope</a> - a personalized search engine: <a href="https://twitter.com/Nutlope/status/1389082406477463557">Twitter</a></li>
<li>Supabase in 6 minutes by <a href="https://www.youtube.com/channel/UCNERPJ-vs61KEsD_dRNWFIw">Georges Duverger</a>: <a href="https://www.youtube.com/watch?v=c8DNV9yl0mg">YouTube video</a></li>
<li>Build a SaaS Platform with Stripe by <a href="https://twitter.com/_dijonmusters">@__dijonmusters:</a> <a href="https://dev.to/dijonmusters/series/12346">DEV blog</a></li>
<li>Supabase &amp; Sveltekit by Svelte Mastery: <a href="https://www.youtube.com/watch?v=j4AV2Liojk0">YouTube video</a></li>
<li>Firebase vs. Supabase - Draftbit Office Hours: <a href="https://www.youtube.com/watch?v=9Yg6i_zCuiM">YouTube Livestream</a></li>
</ul>
<p><strong>Supabase GitHub Star Growth</strong></p>
<ul>
<li>Our <a href="https://github.com/supabase/supabase">main GitHub repo</a> grew 60% (by stars) and we saw a flood of new contributors.</li>
<li>Our <a href="https://github.com/supabase/ui">UI Library</a> grew 175%</li>
<li>Our <a href="https://github.com/supabase/realtime">Realtime Server</a> grew 39% after landing on the <a href="https://news.ycombinator.com/item?id=26968449">front page of Hacker News</a>.</li>
</ul>
<p></p>
<small><p>Source: <a href="https://repository.surf/supabase">repository.surf/supabase</a></p></small>
<p>If you want to keep up to date, make sure you <a href="https://www.youtube.com/c/supabase">subscribe to our YouTube channel</a> or <a href="https://twitter.com/supabase">follow us on Twitter</a>.</p>
<h2 id="coming-next" class="group scroll-mt-24">Coming Next<a href="#coming-next" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>You might have noticed our Dashboard slowly changing (improving), as we migrate the components out to our <a href="https://github.com/supabase/ui">open source UI Library</a>. This progression is an important step towards offering a UI for <a href="https://supabase.com/docs/guides/local-development">Local Development</a> and <a href="../docs/guides/self-hosting.html">Self Hosting</a>.</p>
<p>We&#x27;re also working on our <a href="https://supabase.com/blog/supabase-workflows">Workflows engine</a>. This is quite a large task, but we&#x27;re making progress and aiming to ship sometime in July.</p>
<h2 id="one-more-thing" class="group scroll-mt-24">One more thing<a href="#one-more-thing" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p><a href="https://supabase.com/docs/careers">We started hiring</a>.</p>
<p></p>
<h3 id="get-started" class="group scroll-mt-24">Get started<a href="#get-started" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<ul>
<li>Start using Supabase today: <a href="https://supabase.com/dashboard/">supabase.com/dashboard</a></li>
<li>Make sure to <a href="https://github.com/supabase/supabase">star us on GitHub</a></li>
<li>Follow us <a href="https://twitter.com/supabase">on Twitter</a></li>
<li>Subscribe to our <a href="https://www.youtube.com/c/supabase">YouTube channel</a></li>
<li>Become a <a href="https://github.com/sponsors/supabase">sponsor</a></li>
</ul></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-april-2021&amp;text=Supabase%20Beta%20April%202021"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-april-2021&amp;text=Supabase%20Beta%20April%202021"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-april-2021&amp;t=Supabase%20Beta%20April%202021"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="supabase-beta-june-2021.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Supabase Beta June 2021</h4><p class="small">2 June 2021</p></div></div></div></div></a></div><div><a href="supabase-beta-march-2021.html"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Supabase Beta March 2021</h4><p class="small">6 April 2021</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/release-notes"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">release-notes</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#light-mode">Light Mode</a></li>
<li><a href="#translations">Translations</a></li>
<li><a href="#openapi-spec-for-storage">OpenAPI spec for Storage</a></li>
<li><a href="#stripe-sync-engine-experimental">Stripe Sync Engine (Experimental)</a></li>
<li><a href="#community-spotlight-threaded-comments">Community spotlight: Threaded comments</a></li>
<li><a href="#community-spotlight-supascript">Community spotlight: SupaScript</a></li>
<li><a href="#community-spotlight-fireship">Community spotlight: Fireship</a></li>
<li><a href="#community">Community</a></li>
<li><a href="#coming-next">Coming Next</a></li>
<li><a href="#one-more-thing">One more thing</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-april-2021&amp;text=Supabase%20Beta%20April%202021"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-april-2021&amp;text=Supabase%20Beta%20April%202021"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-april-2021&amp;t=Supabase%20Beta%20April%202021"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"supabase-beta-june-2021","title":"Supabase Beta June 2021","description":"Discord Logins, Vercel Integration, Full text search, and OAuth guides.","author":"paul_copplestone","author_url":"https://github.com/kiwicopple","author_image_url":"https://github.com/kiwicopple.png","image":"2021-june/release-june-2021.jpg","thumb":"2021-june/release-june-2021-cover.jpg","categories":["product"],"tags":["release-notes"],"date":"2021-06-02","toc_depth":3,"video":"https://www.youtube.com/v/m3yRPNyYolk","formattedDate":"2 June 2021","readingTime":"4 minute read","url":"/blog/supabase-beta-june-2021","path":"/blog/supabase-beta-june-2021"},"nextPost":{"slug":"supabase-beta-march-2021","title":"Supabase Beta March 2021","description":"Launch week, Storage, Supabase CLI, Connection Pooling, Supabase UI, and Pricing.","author":"paul_copplestone","author_url":"https://github.com/kiwicopple","author_image_url":"https://github.com/kiwicopple.png","image":"march-2021/release-mar-2021.jpg","thumb":"march-2021/release-mar-2021.jpg","categories":["product"],"tags":["release-notes"],"date":"2021-04-06","video":"https://www.youtube.com/v/TtLxxaYE1rA","formattedDate":"6 April 2021","readingTime":"4 minute read","url":"/blog/supabase-beta-march-2021","path":"/blog/supabase-beta-march-2021"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-beta-april-2021","source":"\nThis month was a \"gardening\" month for Supabase. The team focused on stability, security, and community support.\nCheck out what we were working on below, as well as some incredible Community contributions.\n\n### Quick demo\n\nWatch a full demo:\n\n\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"video-with-border w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/uWJmUTCFdak\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen\n  \u003e\u003c/iframe\u003e\n\u003c/div\u003e\n\n## Light Mode\n\nWe're a developer tool, which means that Dark Mode is [extremely popular](https://twitter.com/supabase/status/1388131942919376904).\n\n![This poll on twitter shows that 78.5% of our developer base use Dark Mode for the IDE](/images/blog/april-2021/twitter-darkmode.png)\n\nWhile Dark mode is great, for some people it's not an option. Dark Mode is difficult to use for developers with astigmatisms,\nor even just working in brightly-lit environments.\n\nSo today we're shipping Light Mode. Access it in the settings of your [Dashboard](https://supabase.com/dashboard).\n\n\u003cvideo width=\"99%\" autoPlay muted playsInline controls={true}\u003e\n  \u003csource src=\"/images/blog/april-2021/light-mode.mp4\" type=\"video/mp4\" /\u003e\n\u003c/video\u003e\n\n## Translations\n\nWith the help of the community, we [started internationalizing](https://github.com/supabase/supabase/issues/1341) our main repository:\n\n- [Arabic | العربية](https://github.com/supabase/supabase/blob/master/i18n/README.ar.md)\n- [Chinese / 中文](https://github.com/supabase/supabase/blob/master/i18n/README.zh-cn.md)\n- [Dutch / Nederlands](https://github.com/supabase/supabase/blob/master/i18n/README.nl.md)\n- [English](https://github.com/supabase/supabase)\n- [French / Français](https://github.com/supabase/supabase/blob/master/i18n/README.fr.md)\n- [German / Deutsch](https://github.com/supabase/supabase/blob/master/i18n/README.de.md)\n- [Hindi / हिंदी](https://github.com/supabase/supabase/blob/master/i18n/README.hi.md)\n- [Nepali / नेपाली](https://github.com/supabase/supabase/blob/master/i18n/README.ne.md)\n- [Italiano / Italian](https://github.com/supabase/supabase/blob/master/i18n/README.it.md)\n- [Japanese / 日本語](https://github.com/supabase/supabase/blob/master/i18n/README.jp.md)\n- [Norwegian (Bokmål) / Norsk (Bokmål)](https://github.com/supabase/supabase/blob/master/i18n/README.nb-no.md)\n- [Polish / Polski](https://github.com/supabase/supabase/blob/master/i18n/README.pl.md)\n- [Portuguese / Portuguese](https://github.com/supabase/supabase/blob/master/i18n/README.pt.md)\n- [Portuguese (Brazilian) / Português Brasileiro](https://github.com/supabase/supabase/blob/master/i18n/README.pt-br.md)\n- [Russian / Pусский](https://github.com/supabase/supabase/blob/master/i18n/README.ru.md)\n- [Spanish / Español](https://github.com/supabase/supabase/blob/master/i18n/README.es.md)\n- [Traditional Chinese / 正體中文](https://github.com/supabase/supabase/blob/master/i18n/README.zh-tw.md)\n- [Turkish / Türkçe](https://github.com/supabase/supabase/blob/master/i18n/README.tr.md)\n- [Ukrainian / Українська](https://github.com/supabase/supabase/blob/master/i18n/README.uk.md)\n\n![Map of the world](/images/blog/april-2021/map.png)\n\n## OpenAPI spec for Storage\n\nWe released [Storage Api docs](https://supabase.github.io/storage) built using OpenAPI (swagger).\n\n![Storage API documentation](/images/blog/april-2021/storage-openapi.png)\n\n## Stripe Sync Engine (Experimental)\n\nWe [open-sourced a server](https://github.com/supabase/stripe-sync-engine) which keeps any Postgres database in sync with Stripe.\nThis is experimental only. We're evaluating other tools such as [Singer](https://www.singer.io/tap/stripe/postgresql/),\nwhich provide a more general solution (but are less \"realtime\"), and we're opening it up here to gather feedback.\n\n![Stripe sync engine](/images/blog/april-2021/stripe-sync-engine.jpg)\n\n## Community spotlight: Threaded comments\n\nOne of the most powerful Postgres features is \"recursive CTEs\" which can be used for nested items (comments, pages, friend-graphs). [@lawrencecchen](https://twitter.com/lawrencecchen) has built a full [Threaded Comments demo](https://github.com/lawrencecchen/threaded-comments) which you can [Deploy with a single click](https://github.com/lawrencecchen/threaded-comments#instant-deploy). Want to add comments to your blog with [Full Text Search](/docs/reference/javascript/textsearch)? Just use Postgres.\n\n\u003cvideo width=\"99%\" autoPlay muted playsInline controls={true}\u003e\n  \u003csource src=\"/images/blog/april-2021/threaded-small.mp4\" type=\"video/mp4\" /\u003e\n\u003c/video\u003e\n\n## Community spotlight: SupaScript\n\nIt looks like [@burggraf2](https://twitter.com/burggraf2) got tired of waiting for us to ship Functions, and decided to\nbuild a whole JS ecosystem within his Supabase database. If you want to write PG functions in JS, import remote libraries\nfrom the web, and console log to your browser, check out this [SupaScript repo](https://github.com/burggraf/SupaScript).\n\n```js\n// After installing:\n// https://github.com/burggraf/SupaScript#installation\n\n/**\n * Get all users who logged in this week.\n * Use in the database:  select * from users_this_week();\n * Use in the browser:   supabase.rpc('users_this_week');\n */\ncreate or replace function users_this_week()\nreturns json as $$\n  const moment = require('https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.js', false);\n\n  const lastWeek = moment().subtract(7, 'days');\n  const query = 'select * from auth.users where created_at \u003e $1'\n  const users = sql(query, lastWeek);\n\n  return users;\n$$ language plv8;\n```\n\n## Community spotlight: Fireship\n\nFireship reviewed Supabase last week, and despite being a (self-proclaimed) Firebase fan-boy, the review was very impartial.\n\n\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"video-with-border w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/WiwfiVdfRIc\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen\n  \u003e\u003c/iframe\u003e\n\u003c/div\u003e\n\n## Community\n\n- Watch [@everConfusedGuy](https://twitter.com/everConfusedGuy)'s talk at DevX conf - Building Supabase Storage: [YouTube video](https://www.youtube.com/watch?v=YsUYOsq_o7g)\n- DuckDuckAbdaal by [@Nutlope](https://twitter.com/Nutlope) - a personalized search engine: [Twitter](https://twitter.com/Nutlope/status/1389082406477463557)\n- Supabase in 6 minutes by [Georges Duverger](https://www.youtube.com/channel/UCNERPJ-vs61KEsD_dRNWFIw): [YouTube video](https://www.youtube.com/watch?v=c8DNV9yl0mg)\n- Build a SaaS Platform with Stripe by [@\\_\\_dijonmusters:](https://twitter.com/_dijonmusters) [DEV blog](https://dev.to/dijonmusters/series/12346)\n- Supabase \u0026 Sveltekit by Svelte Mastery: [YouTube video](https://www.youtube.com/watch?v=j4AV2Liojk0)\n- Firebase vs. Supabase - Draftbit Office Hours: [YouTube Livestream](https://www.youtube.com/watch?v=9Yg6i_zCuiM)\n\n**Supabase GitHub Star Growth**\n\n- Our [main GitHub repo](https://github.com/supabase/supabase) grew 60% (by stars) and we saw a flood of new contributors.\n- Our [UI Library](https://github.com/supabase/ui) grew 175%\n- Our [Realtime Server](https://github.com/supabase/realtime) grew 39% after landing on the [front page of Hacker News](https://news.ycombinator.com/item?id=26968449).\n\n![Stars from github](/images/blog/april-2021/stars-all.png)\n\n\u003csmall\u003e\n  Source: \u003ca href=\"https://repository.surf/supabase\"\u003erepository.surf/supabase\u003c/a\u003e\n\u003c/small\u003e\n\nIf you want to keep up to date, make sure you [subscribe to our YouTube channel](https://www.youtube.com/c/supabase) or [follow us on Twitter](https://twitter.com/supabase).\n\n## Coming Next\n\nYou might have noticed our Dashboard slowly changing (improving), as we migrate the components out to our [open source UI Library](https://github.com/supabase/ui). This progression is an important step towards offering a UI for [Local Development](/docs/guides/local-development) and [Self Hosting](/docs/guides/self-hosting).\n\nWe're also working on our [Workflows engine](/blog/supabase-workflows). This is quite a large task, but we're making progress and aiming to ship sometime in July.\n\n## One more thing\n\n[We started hiring](/docs/careers).\n\n![screenshot of our hiring page](/images/blog/april-2021/hiring.png)\n\n### Get started\n\n- Start using Supabase today: [supabase.com/dashboard](https://supabase.com/dashboard/)\n- Make sure to [star us on GitHub](https://github.com/supabase/supabase)\n- Follow us [on Twitter](https://twitter.com/supabase)\n- Subscribe to our [YouTube channel](https://www.youtube.com/c/supabase)\n- Become a [sponsor](https://github.com/sponsors/supabase)\n","title":"Supabase Beta April 2021","description":"Supabase \"gardening\" - stability, security, and community support.","author":"paul_copplestone","author_url":"https://github.com/kiwicopple","author_image_url":"https://github.com/kiwicopple.png","image":"april-2021/release-apr-2021.jpg","thumb":"april-2021/release-apr-2021.jpg","categories":["product"],"tags":["release-notes"],"date":"2021-05-05","video":"https://www.youtube.com/v/uWJmUTCFdak","content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    h3: \"h3\",\n    h2: \"h2\",\n    a: \"a\",\n    img: \"img\",\n    ul: \"ul\",\n    li: \"li\",\n    strong: \"strong\"\n  }, _provideComponents(), props.components), {CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This month was a \\\"gardening\\\" month for Supabase. The team focused on stability, security, and community support.\\nCheck out what we were working on below, as well as some incredible Community contributions.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"quick-demo\",\n      children: \"Quick demo\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Watch a full demo:\"\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"video-container\",\n      children: _jsx(\"iframe\", {\n        className: \"video-with-border w-full\",\n        src: \"https://www.youtube-nocookie.com/embed/uWJmUTCFdak\",\n        frameBorder: \"1\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n        allowFullScreen: true\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"light-mode\",\n      children: \"Light Mode\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We're a developer tool, which means that Dark Mode is \", _jsx(_components.a, {\n        href: \"https://twitter.com/supabase/status/1388131942919376904\",\n        children: \"extremely popular\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/april-2021/twitter-darkmode.png\",\n        alt: \"This poll on twitter shows that 78.5% of our developer base use Dark Mode for the IDE\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"While Dark mode is great, for some people it's not an option. Dark Mode is difficult to use for developers with astigmatisms,\\nor even just working in brightly-lit environments.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"So today we're shipping Light Mode. Access it in the settings of your \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard\",\n        children: \"Dashboard\"\n      }), \".\"]\n    }), \"\\n\", _jsx(\"video\", {\n      width: \"99%\",\n      autoPlay: true,\n      muted: true,\n      playsInline: true,\n      controls: true,\n      children: _jsx(\"source\", {\n        src: \"/images/blog/april-2021/light-mode.mp4\",\n        type: \"video/mp4\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"translations\",\n      children: \"Translations\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"With the help of the community, we \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/supabase/issues/1341\",\n        children: \"started internationalizing\"\n      }), \" our main repository:\"]\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase/blob/master/i18n/README.ar.md\",\n          children: \"Arabic | العربية\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase/blob/master/i18n/README.zh-cn.md\",\n          children: \"Chinese / 中文\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase/blob/master/i18n/README.nl.md\",\n          children: \"Dutch / Nederlands\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase\",\n          children: \"English\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase/blob/master/i18n/README.fr.md\",\n          children: \"French / Français\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase/blob/master/i18n/README.de.md\",\n          children: \"German / Deutsch\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase/blob/master/i18n/README.hi.md\",\n          children: \"Hindi / हिंदी\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase/blob/master/i18n/README.ne.md\",\n          children: \"Nepali / नेपाली\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase/blob/master/i18n/README.it.md\",\n          children: \"Italiano / Italian\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase/blob/master/i18n/README.jp.md\",\n          children: \"Japanese / 日本語\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase/blob/master/i18n/README.nb-no.md\",\n          children: \"Norwegian (Bokmål) / Norsk (Bokmål)\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase/blob/master/i18n/README.pl.md\",\n          children: \"Polish / Polski\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase/blob/master/i18n/README.pt.md\",\n          children: \"Portuguese / Portuguese\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase/blob/master/i18n/README.pt-br.md\",\n          children: \"Portuguese (Brazilian) / Português Brasileiro\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase/blob/master/i18n/README.ru.md\",\n          children: \"Russian / Pусский\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase/blob/master/i18n/README.es.md\",\n          children: \"Spanish / Español\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase/blob/master/i18n/README.zh-tw.md\",\n          children: \"Traditional Chinese / 正體中文\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase/blob/master/i18n/README.tr.md\",\n          children: \"Turkish / Türkçe\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase/blob/master/i18n/README.uk.md\",\n          children: \"Ukrainian / Українська\"\n        })\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/april-2021/map.png\",\n        alt: \"Map of the world\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"openapi-spec-for-storage\",\n      children: \"OpenAPI spec for Storage\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We released \", _jsx(_components.a, {\n        href: \"https://supabase.github.io/storage\",\n        children: \"Storage Api docs\"\n      }), \" built using OpenAPI (swagger).\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/april-2021/storage-openapi.png\",\n        alt: \"Storage API documentation\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"stripe-sync-engine-experimental\",\n      children: \"Stripe Sync Engine (Experimental)\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/stripe-sync-engine\",\n        children: \"open-sourced a server\"\n      }), \" which keeps any Postgres database in sync with Stripe.\\nThis is experimental only. We're evaluating other tools such as \", _jsx(_components.a, {\n        href: \"https://www.singer.io/tap/stripe/postgresql/\",\n        children: \"Singer\"\n      }), \",\\nwhich provide a more general solution (but are less \\\"realtime\\\"), and we're opening it up here to gather feedback.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/april-2021/stripe-sync-engine.jpg\",\n        alt: \"Stripe sync engine\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"community-spotlight-threaded-comments\",\n      children: \"Community spotlight: Threaded comments\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"One of the most powerful Postgres features is \\\"recursive CTEs\\\" which can be used for nested items (comments, pages, friend-graphs). \", _jsx(_components.a, {\n        href: \"https://twitter.com/lawrencecchen\",\n        children: \"@lawrencecchen\"\n      }), \" has built a full \", _jsx(_components.a, {\n        href: \"https://github.com/lawrencecchen/threaded-comments\",\n        children: \"Threaded Comments demo\"\n      }), \" which you can \", _jsx(_components.a, {\n        href: \"https://github.com/lawrencecchen/threaded-comments#instant-deploy\",\n        children: \"Deploy with a single click\"\n      }), \". Want to add comments to your blog with \", _jsx(_components.a, {\n        href: \"/docs/reference/javascript/textsearch\",\n        children: \"Full Text Search\"\n      }), \"? Just use Postgres.\"]\n    }), \"\\n\", _jsx(\"video\", {\n      width: \"99%\",\n      autoPlay: true,\n      muted: true,\n      playsInline: true,\n      controls: true,\n      children: _jsx(\"source\", {\n        src: \"/images/blog/april-2021/threaded-small.mp4\",\n        type: \"video/mp4\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"community-spotlight-supascript\",\n      children: \"Community spotlight: SupaScript\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"It looks like \", _jsx(_components.a, {\n        href: \"https://twitter.com/burggraf2\",\n        children: \"@burggraf2\"\n      }), \" got tired of waiting for us to ship Functions, and decided to\\nbuild a whole JS ecosystem within his Supabase database. If you want to write PG functions in JS, import remote libraries\\nfrom the web, and console log to your browser, check out this \", _jsx(_components.a, {\n        href: \"https://github.com/burggraf/SupaScript\",\n        children: \"SupaScript repo\"\n      }), \".\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// After installing:\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"// https://github.com/burggraf/SupaScript#installation\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"/**\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \" * Get all users who logged in this week.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \" * Use in the database:  select * from users_this_week();\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \" * Use in the browser:   supabase.rpc('users_this_week');\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \" */\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"create or replace \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"function \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"users_this_week\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"returns json as $$\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"moment \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"require\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.js'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"false\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \");\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"lastWeek \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"moment\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"().\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"subtract\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"7\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'days'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \");\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"query \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'select * from auth.users where created_at \u003e $1'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"users \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"sql\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(query, lastWeek);\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" users;\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"$$ language plv8;\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"js\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"community-spotlight-fireship\",\n      children: \"Community spotlight: Fireship\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Fireship reviewed Supabase last week, and despite being a (self-proclaimed) Firebase fan-boy, the review was very impartial.\"\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"video-container\",\n      children: _jsx(\"iframe\", {\n        className: \"video-with-border w-full\",\n        src: \"https://www.youtube-nocookie.com/embed/WiwfiVdfRIc\",\n        frameBorder: \"1\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n        allowFullScreen: true\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"community\",\n      children: \"Community\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Watch \", _jsx(_components.a, {\n          href: \"https://twitter.com/everConfusedGuy\",\n          children: \"@everConfusedGuy\"\n        }), \"'s talk at DevX conf - Building Supabase Storage: \", _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=YsUYOsq_o7g\",\n          children: \"YouTube video\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"DuckDuckAbdaal by \", _jsx(_components.a, {\n          href: \"https://twitter.com/Nutlope\",\n          children: \"@Nutlope\"\n        }), \" - a personalized search engine: \", _jsx(_components.a, {\n          href: \"https://twitter.com/Nutlope/status/1389082406477463557\",\n          children: \"Twitter\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Supabase in 6 minutes by \", _jsx(_components.a, {\n          href: \"https://www.youtube.com/channel/UCNERPJ-vs61KEsD_dRNWFIw\",\n          children: \"Georges Duverger\"\n        }), \": \", _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=c8DNV9yl0mg\",\n          children: \"YouTube video\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Build a SaaS Platform with Stripe by \", _jsx(_components.a, {\n          href: \"https://twitter.com/_dijonmusters\",\n          children: \"@__dijonmusters:\"\n        }), \" \", _jsx(_components.a, {\n          href: \"https://dev.to/dijonmusters/series/12346\",\n          children: \"DEV blog\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Supabase \u0026 Sveltekit by Svelte Mastery: \", _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=j4AV2Liojk0\",\n          children: \"YouTube video\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Firebase vs. Supabase - Draftbit Office Hours: \", _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=9Yg6i_zCuiM\",\n          children: \"YouTube Livestream\"\n        })]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.strong, {\n        children: \"Supabase GitHub Star Growth\"\n      })\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Our \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase\",\n          children: \"main GitHub repo\"\n        }), \" grew 60% (by stars) and we saw a flood of new contributors.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Our \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/ui\",\n          children: \"UI Library\"\n        }), \" grew 175%\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Our \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/realtime\",\n          children: \"Realtime Server\"\n        }), \" grew 39% after landing on the \", _jsx(_components.a, {\n          href: \"https://news.ycombinator.com/item?id=26968449\",\n          children: \"front page of Hacker News\"\n        }), \".\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/april-2021/stars-all.png\",\n        alt: \"Stars from github\"\n      })\n    }), \"\\n\", _jsx(\"small\", {\n      children: _jsxs(_components.p, {\n        children: [\"Source: \", _jsx(\"a\", {\n          href: \"https://repository.surf/supabase\",\n          children: \"repository.surf/supabase\"\n        })]\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you want to keep up to date, make sure you \", _jsx(_components.a, {\n        href: \"https://www.youtube.com/c/supabase\",\n        children: \"subscribe to our YouTube channel\"\n      }), \" or \", _jsx(_components.a, {\n        href: \"https://twitter.com/supabase\",\n        children: \"follow us on Twitter\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"coming-next\",\n      children: \"Coming Next\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You might have noticed our Dashboard slowly changing (improving), as we migrate the components out to our \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/ui\",\n        children: \"open source UI Library\"\n      }), \". This progression is an important step towards offering a UI for \", _jsx(_components.a, {\n        href: \"/docs/guides/local-development\",\n        children: \"Local Development\"\n      }), \" and \", _jsx(_components.a, {\n        href: \"/docs/guides/self-hosting\",\n        children: \"Self Hosting\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We're also working on our \", _jsx(_components.a, {\n        href: \"/blog/supabase-workflows\",\n        children: \"Workflows engine\"\n      }), \". This is quite a large task, but we're making progress and aiming to ship sometime in July.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"one-more-thing\",\n      children: \"One more thing\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"/docs/careers\",\n        children: \"We started hiring\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/april-2021/hiring.png\",\n        alt: \"screenshot of our hiring page\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"get-started\",\n      children: \"Get started\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Start using Supabase today: \", _jsx(_components.a, {\n          href: \"https://supabase.com/dashboard/\",\n          children: \"supabase.com/dashboard\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Make sure to \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase\",\n          children: \"star us on GitHub\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Follow us \", _jsx(_components.a, {\n          href: \"https://twitter.com/supabase\",\n          children: \"on Twitter\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Subscribe to our \", _jsx(_components.a, {\n          href: \"https://www.youtube.com/c/supabase\",\n          children: \"YouTube channel\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Become a \", _jsx(_components.a, {\n          href: \"https://github.com/sponsors/supabase\",\n          children: \"sponsor\"\n        })]\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"Quick demo","slug":"quick-demo","lvl":3,"i":0,"seen":0},{"content":"Light Mode","slug":"light-mode","lvl":2,"i":1,"seen":0},{"content":"Translations","slug":"translations","lvl":2,"i":2,"seen":0},{"content":"OpenAPI spec for Storage","slug":"openapi-spec-for-storage","lvl":2,"i":3,"seen":0},{"content":"Stripe Sync Engine (Experimental)","slug":"stripe-sync-engine-experimental","lvl":2,"i":4,"seen":0},{"content":"Community spotlight: Threaded comments","slug":"community-spotlight-threaded-comments","lvl":2,"i":5,"seen":0},{"content":"Community spotlight: SupaScript","slug":"community-spotlight-supascript","lvl":2,"i":6,"seen":0},{"content":"Community spotlight: Fireship","slug":"community-spotlight-fireship","lvl":2,"i":7,"seen":0},{"content":"Community","slug":"community","lvl":2,"i":8,"seen":0},{"content":"Coming Next","slug":"coming-next","lvl":2,"i":9,"seen":0},{"content":"One more thing","slug":"one-more-thing","lvl":2,"i":10,"seen":0},{"content":"Get started","slug":"get-started","lvl":3,"i":11,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,3],"level":0},{"type":"inline","content":"This month was a \"gardening\" month for Supabase. The team focused on stability, security, and community support.\nCheck out what we were working on below, as well as some incredible Community contributions.","level":1,"lines":[1,3],"children":[{"type":"text","content":"This month was a \"gardening\" month for Supabase. The team focused on stability, security, and community support.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Check out what we were working on below, as well as some incredible Community contributions.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[4,5],"level":0},{"type":"inline","content":"[Quick demo](#quick-demo)","level":1,"lines":[4,5],"children":[{"type":"text","content":"Quick demo","level":0}],"lvl":3,"i":0,"seen":0,"slug":"quick-demo"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[6,7],"level":0},{"type":"inline","content":"Watch a full demo:","level":1,"lines":[6,7],"children":[{"type":"text","content":"Watch a full demo:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[8,15],"level":0},{"type":"inline","content":"\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"video-with-border w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/uWJmUTCFdak\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen","level":1,"lines":[8,15],"children":[{"type":"text","content":"\u003cdiv className=\"video-container\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ciframe","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"video-with-border w-full\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://www.youtube-nocookie.com/embed/uWJmUTCFdak\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"frameBorder=\"1\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allowFullScreen","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"blockquote_open","lines":[15,17],"level":0},{"type":"paragraph_open","tight":false,"lines":[15,17],"level":1},{"type":"inline","content":"\u003c/iframe\u003e\n\u003c/div\u003e","level":2,"lines":[15,17],"children":[{"type":"text","content":"\u003c/iframe\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"heading_open","hLevel":2,"lines":[18,19],"level":0},{"type":"inline","content":"[Light Mode](#light-mode)","level":1,"lines":[18,19],"children":[{"type":"text","content":"Light Mode","level":0}],"lvl":2,"i":1,"seen":0,"slug":"light-mode"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[20,21],"level":0},{"type":"inline","content":"We're a developer tool, which means that Dark Mode is [extremely popular](https://twitter.com/supabase/status/1388131942919376904).","level":1,"lines":[20,21],"children":[{"type":"text","content":"We're a developer tool, which means that Dark Mode is ","level":0},{"type":"link_open","href":"https://twitter.com/supabase/status/1388131942919376904","title":"","level":0},{"type":"text","content":"extremely popular","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[22,23],"level":0},{"type":"inline","content":"![This poll on twitter shows that 78.5% of our developer base use Dark Mode for the IDE](/images/blog/april-2021/twitter-darkmode.png)","level":1,"lines":[22,23],"children":[{"type":"image","src":"/images/blog/april-2021/twitter-darkmode.png","title":"","alt":"This poll on twitter shows that 78.5% of our developer base use Dark Mode for the IDE","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[24,26],"level":0},{"type":"inline","content":"While Dark mode is great, for some people it's not an option. Dark Mode is difficult to use for developers with astigmatisms,\nor even just working in brightly-lit environments.","level":1,"lines":[24,26],"children":[{"type":"text","content":"While Dark mode is great, for some people it's not an option. Dark Mode is difficult to use for developers with astigmatisms,","level":0},{"type":"softbreak","level":0},{"type":"text","content":"or even just working in brightly-lit environments.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[27,28],"level":0},{"type":"inline","content":"So today we're shipping Light Mode. Access it in the settings of your [Dashboard](https://supabase.com/dashboard).","level":1,"lines":[27,28],"children":[{"type":"text","content":"So today we're shipping Light Mode. Access it in the settings of your ","level":0},{"type":"link_open","href":"https://supabase.com/dashboard","title":"","level":0},{"type":"text","content":"Dashboard","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[29,32],"level":0},{"type":"inline","content":"\u003cvideo width=\"99%\" autoPlay muted playsInline controls={true}\u003e\n  \u003csource src=\"/images/blog/april-2021/light-mode.mp4\" type=\"video/mp4\" /\u003e\n\u003c/video\u003e","level":1,"lines":[29,32],"children":[{"type":"text","content":"\u003cvideo width=\"99%\" autoPlay muted playsInline controls={true}\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003csource src=\"/images/blog/april-2021/light-mode.mp4\" type=\"video/mp4\" /\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/video\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[33,34],"level":0},{"type":"inline","content":"[Translations](#translations)","level":1,"lines":[33,34],"children":[{"type":"text","content":"Translations","level":0}],"lvl":2,"i":2,"seen":0,"slug":"translations"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[35,36],"level":0},{"type":"inline","content":"With the help of the community, we [started internationalizing](https://github.com/supabase/supabase/issues/1341) our main repository:","level":1,"lines":[35,36],"children":[{"type":"text","content":"With the help of the community, we ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase/issues/1341","title":"","level":0},{"type":"text","content":"started internationalizing","level":1},{"type":"link_close","level":0},{"type":"text","content":" our main repository:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[37,57],"level":0},{"type":"list_item_open","lines":[37,38],"level":1},{"type":"paragraph_open","tight":true,"lines":[37,38],"level":2},{"type":"inline","content":"[Arabic | العربية](https://github.com/supabase/supabase/blob/master/i18n/README.ar.md)","level":3,"lines":[37,38],"children":[{"type":"link_open","href":"https://github.com/supabase/supabase/blob/master/i18n/README.ar.md","title":"","level":0},{"type":"text","content":"Arabic | العربية","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[38,39],"level":1},{"type":"paragraph_open","tight":true,"lines":[38,39],"level":2},{"type":"inline","content":"[Chinese / 中文](https://github.com/supabase/supabase/blob/master/i18n/README.zh-cn.md)","level":3,"lines":[38,39],"children":[{"type":"link_open","href":"https://github.com/supabase/supabase/blob/master/i18n/README.zh-cn.md","title":"","level":0},{"type":"text","content":"Chinese / 中文","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[39,40],"level":1},{"type":"paragraph_open","tight":true,"lines":[39,40],"level":2},{"type":"inline","content":"[Dutch / Nederlands](https://github.com/supabase/supabase/blob/master/i18n/README.nl.md)","level":3,"lines":[39,40],"children":[{"type":"link_open","href":"https://github.com/supabase/supabase/blob/master/i18n/README.nl.md","title":"","level":0},{"type":"text","content":"Dutch / Nederlands","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[40,41],"level":1},{"type":"paragraph_open","tight":true,"lines":[40,41],"level":2},{"type":"inline","content":"[English](https://github.com/supabase/supabase)","level":3,"lines":[40,41],"children":[{"type":"link_open","href":"https://github.com/supabase/supabase","title":"","level":0},{"type":"text","content":"English","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[41,42],"level":1},{"type":"paragraph_open","tight":true,"lines":[41,42],"level":2},{"type":"inline","content":"[French / Français](https://github.com/supabase/supabase/blob/master/i18n/README.fr.md)","level":3,"lines":[41,42],"children":[{"type":"link_open","href":"https://github.com/supabase/supabase/blob/master/i18n/README.fr.md","title":"","level":0},{"type":"text","content":"French / Français","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[42,43],"level":1},{"type":"paragraph_open","tight":true,"lines":[42,43],"level":2},{"type":"inline","content":"[German / Deutsch](https://github.com/supabase/supabase/blob/master/i18n/README.de.md)","level":3,"lines":[42,43],"children":[{"type":"link_open","href":"https://github.com/supabase/supabase/blob/master/i18n/README.de.md","title":"","level":0},{"type":"text","content":"German / Deutsch","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[43,44],"level":1},{"type":"paragraph_open","tight":true,"lines":[43,44],"level":2},{"type":"inline","content":"[Hindi / हिंदी](https://github.com/supabase/supabase/blob/master/i18n/README.hi.md)","level":3,"lines":[43,44],"children":[{"type":"link_open","href":"https://github.com/supabase/supabase/blob/master/i18n/README.hi.md","title":"","level":0},{"type":"text","content":"Hindi / हिंदी","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[44,45],"level":1},{"type":"paragraph_open","tight":true,"lines":[44,45],"level":2},{"type":"inline","content":"[Nepali / नेपाली](https://github.com/supabase/supabase/blob/master/i18n/README.ne.md)","level":3,"lines":[44,45],"children":[{"type":"link_open","href":"https://github.com/supabase/supabase/blob/master/i18n/README.ne.md","title":"","level":0},{"type":"text","content":"Nepali / नेपाली","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[45,46],"level":1},{"type":"paragraph_open","tight":true,"lines":[45,46],"level":2},{"type":"inline","content":"[Italiano / Italian](https://github.com/supabase/supabase/blob/master/i18n/README.it.md)","level":3,"lines":[45,46],"children":[{"type":"link_open","href":"https://github.com/supabase/supabase/blob/master/i18n/README.it.md","title":"","level":0},{"type":"text","content":"Italiano / Italian","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[46,47],"level":1},{"type":"paragraph_open","tight":true,"lines":[46,47],"level":2},{"type":"inline","content":"[Japanese / 日本語](https://github.com/supabase/supabase/blob/master/i18n/README.jp.md)","level":3,"lines":[46,47],"children":[{"type":"link_open","href":"https://github.com/supabase/supabase/blob/master/i18n/README.jp.md","title":"","level":0},{"type":"text","content":"Japanese / 日本語","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[47,48],"level":1},{"type":"paragraph_open","tight":true,"lines":[47,48],"level":2},{"type":"inline","content":"[Norwegian (Bokmål) / Norsk (Bokmål)](https://github.com/supabase/supabase/blob/master/i18n/README.nb-no.md)","level":3,"lines":[47,48],"children":[{"type":"link_open","href":"https://github.com/supabase/supabase/blob/master/i18n/README.nb-no.md","title":"","level":0},{"type":"text","content":"Norwegian (Bokmål) / Norsk (Bokmål)","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[48,49],"level":1},{"type":"paragraph_open","tight":true,"lines":[48,49],"level":2},{"type":"inline","content":"[Polish / Polski](https://github.com/supabase/supabase/blob/master/i18n/README.pl.md)","level":3,"lines":[48,49],"children":[{"type":"link_open","href":"https://github.com/supabase/supabase/blob/master/i18n/README.pl.md","title":"","level":0},{"type":"text","content":"Polish / Polski","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[49,50],"level":1},{"type":"paragraph_open","tight":true,"lines":[49,50],"level":2},{"type":"inline","content":"[Portuguese / Portuguese](https://github.com/supabase/supabase/blob/master/i18n/README.pt.md)","level":3,"lines":[49,50],"children":[{"type":"link_open","href":"https://github.com/supabase/supabase/blob/master/i18n/README.pt.md","title":"","level":0},{"type":"text","content":"Portuguese / Portuguese","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[50,51],"level":1},{"type":"paragraph_open","tight":true,"lines":[50,51],"level":2},{"type":"inline","content":"[Portuguese (Brazilian) / Português Brasileiro](https://github.com/supabase/supabase/blob/master/i18n/README.pt-br.md)","level":3,"lines":[50,51],"children":[{"type":"link_open","href":"https://github.com/supabase/supabase/blob/master/i18n/README.pt-br.md","title":"","level":0},{"type":"text","content":"Portuguese (Brazilian) / Português Brasileiro","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[51,52],"level":1},{"type":"paragraph_open","tight":true,"lines":[51,52],"level":2},{"type":"inline","content":"[Russian / Pусский](https://github.com/supabase/supabase/blob/master/i18n/README.ru.md)","level":3,"lines":[51,52],"children":[{"type":"link_open","href":"https://github.com/supabase/supabase/blob/master/i18n/README.ru.md","title":"","level":0},{"type":"text","content":"Russian / Pусский","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[52,53],"level":1},{"type":"paragraph_open","tight":true,"lines":[52,53],"level":2},{"type":"inline","content":"[Spanish / Español](https://github.com/supabase/supabase/blob/master/i18n/README.es.md)","level":3,"lines":[52,53],"children":[{"type":"link_open","href":"https://github.com/supabase/supabase/blob/master/i18n/README.es.md","title":"","level":0},{"type":"text","content":"Spanish / Español","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[53,54],"level":1},{"type":"paragraph_open","tight":true,"lines":[53,54],"level":2},{"type":"inline","content":"[Traditional Chinese / 正體中文](https://github.com/supabase/supabase/blob/master/i18n/README.zh-tw.md)","level":3,"lines":[53,54],"children":[{"type":"link_open","href":"https://github.com/supabase/supabase/blob/master/i18n/README.zh-tw.md","title":"","level":0},{"type":"text","content":"Traditional Chinese / 正體中文","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[54,55],"level":1},{"type":"paragraph_open","tight":true,"lines":[54,55],"level":2},{"type":"inline","content":"[Turkish / Türkçe](https://github.com/supabase/supabase/blob/master/i18n/README.tr.md)","level":3,"lines":[54,55],"children":[{"type":"link_open","href":"https://github.com/supabase/supabase/blob/master/i18n/README.tr.md","title":"","level":0},{"type":"text","content":"Turkish / Türkçe","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[55,57],"level":1},{"type":"paragraph_open","tight":true,"lines":[55,56],"level":2},{"type":"inline","content":"[Ukrainian / Українська](https://github.com/supabase/supabase/blob/master/i18n/README.uk.md)","level":3,"lines":[55,56],"children":[{"type":"link_open","href":"https://github.com/supabase/supabase/blob/master/i18n/README.uk.md","title":"","level":0},{"type":"text","content":"Ukrainian / Українська","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[57,58],"level":0},{"type":"inline","content":"![Map of the world](/images/blog/april-2021/map.png)","level":1,"lines":[57,58],"children":[{"type":"image","src":"/images/blog/april-2021/map.png","title":"","alt":"Map of the world","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[59,60],"level":0},{"type":"inline","content":"[OpenAPI spec for Storage](#openapi-spec-for-storage)","level":1,"lines":[59,60],"children":[{"type":"text","content":"OpenAPI spec for Storage","level":0}],"lvl":2,"i":3,"seen":0,"slug":"openapi-spec-for-storage"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[61,62],"level":0},{"type":"inline","content":"We released [Storage Api docs](https://supabase.github.io/storage) built using OpenAPI (swagger).","level":1,"lines":[61,62],"children":[{"type":"text","content":"We released ","level":0},{"type":"link_open","href":"https://supabase.github.io/storage","title":"","level":0},{"type":"text","content":"Storage Api docs","level":1},{"type":"link_close","level":0},{"type":"text","content":" built using OpenAPI (swagger).","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[63,64],"level":0},{"type":"inline","content":"![Storage API documentation](/images/blog/april-2021/storage-openapi.png)","level":1,"lines":[63,64],"children":[{"type":"image","src":"/images/blog/april-2021/storage-openapi.png","title":"","alt":"Storage API documentation","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[65,66],"level":0},{"type":"inline","content":"[Stripe Sync Engine (Experimental)](#stripe-sync-engine-experimental)","level":1,"lines":[65,66],"children":[{"type":"text","content":"Stripe Sync Engine (Experimental)","level":0}],"lvl":2,"i":4,"seen":0,"slug":"stripe-sync-engine-experimental"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[67,70],"level":0},{"type":"inline","content":"We [open-sourced a server](https://github.com/supabase/stripe-sync-engine) which keeps any Postgres database in sync with Stripe.\nThis is experimental only. We're evaluating other tools such as [Singer](https://www.singer.io/tap/stripe/postgresql/),\nwhich provide a more general solution (but are less \"realtime\"), and we're opening it up here to gather feedback.","level":1,"lines":[67,70],"children":[{"type":"text","content":"We ","level":0},{"type":"link_open","href":"https://github.com/supabase/stripe-sync-engine","title":"","level":0},{"type":"text","content":"open-sourced a server","level":1},{"type":"link_close","level":0},{"type":"text","content":" which keeps any Postgres database in sync with Stripe.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"This is experimental only. We're evaluating other tools such as ","level":0},{"type":"link_open","href":"https://www.singer.io/tap/stripe/postgresql/","title":"","level":0},{"type":"text","content":"Singer","level":1},{"type":"link_close","level":0},{"type":"text","content":",","level":0},{"type":"softbreak","level":0},{"type":"text","content":"which provide a more general solution (but are less \"realtime\"), and we're opening it up here to gather feedback.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[71,72],"level":0},{"type":"inline","content":"![Stripe sync engine](/images/blog/april-2021/stripe-sync-engine.jpg)","level":1,"lines":[71,72],"children":[{"type":"image","src":"/images/blog/april-2021/stripe-sync-engine.jpg","title":"","alt":"Stripe sync engine","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[73,74],"level":0},{"type":"inline","content":"[Community spotlight: Threaded comments](#community-spotlight-threaded-comments)","level":1,"lines":[73,74],"children":[{"type":"text","content":"Community spotlight: Threaded comments","level":0}],"lvl":2,"i":5,"seen":0,"slug":"community-spotlight-threaded-comments"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[75,76],"level":0},{"type":"inline","content":"One of the most powerful Postgres features is \"recursive CTEs\" which can be used for nested items (comments, pages, friend-graphs). [@lawrencecchen](https://twitter.com/lawrencecchen) has built a full [Threaded Comments demo](https://github.com/lawrencecchen/threaded-comments) which you can [Deploy with a single click](https://github.com/lawrencecchen/threaded-comments#instant-deploy). Want to add comments to your blog with [Full Text Search](/docs/reference/javascript/textsearch)? Just use Postgres.","level":1,"lines":[75,76],"children":[{"type":"text","content":"One of the most powerful Postgres features is \"recursive CTEs\" which can be used for nested items (comments, pages, friend-graphs). ","level":0},{"type":"link_open","href":"https://twitter.com/lawrencecchen","title":"","level":0},{"type":"text","content":"@lawrencecchen","level":1},{"type":"link_close","level":0},{"type":"text","content":" has built a full ","level":0},{"type":"link_open","href":"https://github.com/lawrencecchen/threaded-comments","title":"","level":0},{"type":"text","content":"Threaded Comments demo","level":1},{"type":"link_close","level":0},{"type":"text","content":" which you can ","level":0},{"type":"link_open","href":"https://github.com/lawrencecchen/threaded-comments#instant-deploy","title":"","level":0},{"type":"text","content":"Deploy with a single click","level":1},{"type":"link_close","level":0},{"type":"text","content":". Want to add comments to your blog with ","level":0},{"type":"link_open","href":"/docs/reference/javascript/textsearch","title":"","level":0},{"type":"text","content":"Full Text Search","level":1},{"type":"link_close","level":0},{"type":"text","content":"? Just use Postgres.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[77,80],"level":0},{"type":"inline","content":"\u003cvideo width=\"99%\" autoPlay muted playsInline controls={true}\u003e\n  \u003csource src=\"/images/blog/april-2021/threaded-small.mp4\" type=\"video/mp4\" /\u003e\n\u003c/video\u003e","level":1,"lines":[77,80],"children":[{"type":"text","content":"\u003cvideo width=\"99%\" autoPlay muted playsInline controls={true}\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003csource src=\"/images/blog/april-2021/threaded-small.mp4\" type=\"video/mp4\" /\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/video\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[81,82],"level":0},{"type":"inline","content":"[Community spotlight: SupaScript](#community-spotlight-supascript)","level":1,"lines":[81,82],"children":[{"type":"text","content":"Community spotlight: SupaScript","level":0}],"lvl":2,"i":6,"seen":0,"slug":"community-spotlight-supascript"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[83,86],"level":0},{"type":"inline","content":"It looks like [@burggraf2](https://twitter.com/burggraf2) got tired of waiting for us to ship Functions, and decided to\nbuild a whole JS ecosystem within his Supabase database. If you want to write PG functions in JS, import remote libraries\nfrom the web, and console log to your browser, check out this [SupaScript repo](https://github.com/burggraf/SupaScript).","level":1,"lines":[83,86],"children":[{"type":"text","content":"It looks like ","level":0},{"type":"link_open","href":"https://twitter.com/burggraf2","title":"","level":0},{"type":"text","content":"@burggraf2","level":1},{"type":"link_close","level":0},{"type":"text","content":" got tired of waiting for us to ship Functions, and decided to","level":0},{"type":"softbreak","level":0},{"type":"text","content":"build a whole JS ecosystem within his Supabase database. If you want to write PG functions in JS, import remote libraries","level":0},{"type":"softbreak","level":0},{"type":"text","content":"from the web, and console log to your browser, check out this ","level":0},{"type":"link_open","href":"https://github.com/burggraf/SupaScript","title":"","level":0},{"type":"text","content":"SupaScript repo","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"js","content":"// After installing:\n// https://github.com/burggraf/SupaScript#installation\n\n/**\n * Get all users who logged in this week.\n * Use in the database:  select * from users_this_week();\n * Use in the browser:   supabase.rpc('users_this_week');\n */\ncreate or replace function users_this_week()\nreturns json as $$\n  const moment = require('https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.js', false);\n\n  const lastWeek = moment().subtract(7, 'days');\n  const query = 'select * from auth.users where created_at \u003e $1'\n  const users = sql(query, lastWeek);\n\n  return users;\n$$ language plv8;\n","lines":[87,107],"level":0},{"type":"heading_open","hLevel":2,"lines":[108,109],"level":0},{"type":"inline","content":"[Community spotlight: Fireship](#community-spotlight-fireship)","level":1,"lines":[108,109],"children":[{"type":"text","content":"Community spotlight: Fireship","level":0}],"lvl":2,"i":7,"seen":0,"slug":"community-spotlight-fireship"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[110,111],"level":0},{"type":"inline","content":"Fireship reviewed Supabase last week, and despite being a (self-proclaimed) Firebase fan-boy, the review was very impartial.","level":1,"lines":[110,111],"children":[{"type":"text","content":"Fireship reviewed Supabase last week, and despite being a (self-proclaimed) Firebase fan-boy, the review was very impartial.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[112,119],"level":0},{"type":"inline","content":"\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"video-with-border w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/WiwfiVdfRIc\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen","level":1,"lines":[112,119],"children":[{"type":"text","content":"\u003cdiv className=\"video-container\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ciframe","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"video-with-border w-full\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://www.youtube-nocookie.com/embed/WiwfiVdfRIc\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"frameBorder=\"1\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allowFullScreen","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"blockquote_open","lines":[119,121],"level":0},{"type":"paragraph_open","tight":false,"lines":[119,121],"level":1},{"type":"inline","content":"\u003c/iframe\u003e\n\u003c/div\u003e","level":2,"lines":[119,121],"children":[{"type":"text","content":"\u003c/iframe\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"heading_open","hLevel":2,"lines":[122,123],"level":0},{"type":"inline","content":"[Community](#community)","level":1,"lines":[122,123],"children":[{"type":"text","content":"Community","level":0}],"lvl":2,"i":8,"seen":0,"slug":"community"},{"type":"heading_close","hLevel":2,"level":0},{"type":"bullet_list_open","lines":[124,131],"level":0},{"type":"list_item_open","lines":[124,125],"level":1},{"type":"paragraph_open","tight":true,"lines":[124,125],"level":2},{"type":"inline","content":"Watch [@everConfusedGuy](https://twitter.com/everConfusedGuy)'s talk at DevX conf - Building Supabase Storage: [YouTube video](https://www.youtube.com/watch?v=YsUYOsq_o7g)","level":3,"lines":[124,125],"children":[{"type":"text","content":"Watch ","level":0},{"type":"link_open","href":"https://twitter.com/everConfusedGuy","title":"","level":0},{"type":"text","content":"@everConfusedGuy","level":1},{"type":"link_close","level":0},{"type":"text","content":"'s talk at DevX conf - Building Supabase Storage: ","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=YsUYOsq_o7g","title":"","level":0},{"type":"text","content":"YouTube video","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[125,126],"level":1},{"type":"paragraph_open","tight":true,"lines":[125,126],"level":2},{"type":"inline","content":"DuckDuckAbdaal by [@Nutlope](https://twitter.com/Nutlope) - a personalized search engine: [Twitter](https://twitter.com/Nutlope/status/1389082406477463557)","level":3,"lines":[125,126],"children":[{"type":"text","content":"DuckDuckAbdaal by ","level":0},{"type":"link_open","href":"https://twitter.com/Nutlope","title":"","level":0},{"type":"text","content":"@Nutlope","level":1},{"type":"link_close","level":0},{"type":"text","content":" - a personalized search engine: ","level":0},{"type":"link_open","href":"https://twitter.com/Nutlope/status/1389082406477463557","title":"","level":0},{"type":"text","content":"Twitter","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[126,127],"level":1},{"type":"paragraph_open","tight":true,"lines":[126,127],"level":2},{"type":"inline","content":"Supabase in 6 minutes by [Georges Duverger](https://www.youtube.com/channel/UCNERPJ-vs61KEsD_dRNWFIw): [YouTube video](https://www.youtube.com/watch?v=c8DNV9yl0mg)","level":3,"lines":[126,127],"children":[{"type":"text","content":"Supabase in 6 minutes by ","level":0},{"type":"link_open","href":"https://www.youtube.com/channel/UCNERPJ-vs61KEsD_dRNWFIw","title":"","level":0},{"type":"text","content":"Georges Duverger","level":1},{"type":"link_close","level":0},{"type":"text","content":": ","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=c8DNV9yl0mg","title":"","level":0},{"type":"text","content":"YouTube video","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[127,128],"level":1},{"type":"paragraph_open","tight":true,"lines":[127,128],"level":2},{"type":"inline","content":"Build a SaaS Platform with Stripe by [@\\_\\_dijonmusters:](https://twitter.com/_dijonmusters) [DEV blog](https://dev.to/dijonmusters/series/12346)","level":3,"lines":[127,128],"children":[{"type":"text","content":"Build a SaaS Platform with Stripe by ","level":0},{"type":"link_open","href":"https://twitter.com/_dijonmusters","title":"","level":0},{"type":"text","content":"@__dijonmusters:","level":1},{"type":"link_close","level":0},{"type":"text","content":" ","level":0},{"type":"link_open","href":"https://dev.to/dijonmusters/series/12346","title":"","level":0},{"type":"text","content":"DEV blog","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[128,129],"level":1},{"type":"paragraph_open","tight":true,"lines":[128,129],"level":2},{"type":"inline","content":"Supabase \u0026 Sveltekit by Svelte Mastery: [YouTube video](https://www.youtube.com/watch?v=j4AV2Liojk0)","level":3,"lines":[128,129],"children":[{"type":"text","content":"Supabase \u0026 Sveltekit by Svelte Mastery: ","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=j4AV2Liojk0","title":"","level":0},{"type":"text","content":"YouTube video","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[129,131],"level":1},{"type":"paragraph_open","tight":true,"lines":[129,130],"level":2},{"type":"inline","content":"Firebase vs. Supabase - Draftbit Office Hours: [YouTube Livestream](https://www.youtube.com/watch?v=9Yg6i_zCuiM)","level":3,"lines":[129,130],"children":[{"type":"text","content":"Firebase vs. Supabase - Draftbit Office Hours: ","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=9Yg6i_zCuiM","title":"","level":0},{"type":"text","content":"YouTube Livestream","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[131,132],"level":0},{"type":"inline","content":"**Supabase GitHub Star Growth**","level":1,"lines":[131,132],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Supabase GitHub Star Growth","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[133,137],"level":0},{"type":"list_item_open","lines":[133,134],"level":1},{"type":"paragraph_open","tight":true,"lines":[133,134],"level":2},{"type":"inline","content":"Our [main GitHub repo](https://github.com/supabase/supabase) grew 60% (by stars) and we saw a flood of new contributors.","level":3,"lines":[133,134],"children":[{"type":"text","content":"Our ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase","title":"","level":0},{"type":"text","content":"main GitHub repo","level":1},{"type":"link_close","level":0},{"type":"text","content":" grew 60% (by stars) and we saw a flood of new contributors.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[134,135],"level":1},{"type":"paragraph_open","tight":true,"lines":[134,135],"level":2},{"type":"inline","content":"Our [UI Library](https://github.com/supabase/ui) grew 175%","level":3,"lines":[134,135],"children":[{"type":"text","content":"Our ","level":0},{"type":"link_open","href":"https://github.com/supabase/ui","title":"","level":0},{"type":"text","content":"UI Library","level":1},{"type":"link_close","level":0},{"type":"text","content":" grew 175%","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[135,137],"level":1},{"type":"paragraph_open","tight":true,"lines":[135,136],"level":2},{"type":"inline","content":"Our [Realtime Server](https://github.com/supabase/realtime) grew 39% after landing on the [front page of Hacker News](https://news.ycombinator.com/item?id=26968449).","level":3,"lines":[135,136],"children":[{"type":"text","content":"Our ","level":0},{"type":"link_open","href":"https://github.com/supabase/realtime","title":"","level":0},{"type":"text","content":"Realtime Server","level":1},{"type":"link_close","level":0},{"type":"text","content":" grew 39% after landing on the ","level":0},{"type":"link_open","href":"https://news.ycombinator.com/item?id=26968449","title":"","level":0},{"type":"text","content":"front page of Hacker News","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[137,138],"level":0},{"type":"inline","content":"![Stars from github](/images/blog/april-2021/stars-all.png)","level":1,"lines":[137,138],"children":[{"type":"image","src":"/images/blog/april-2021/stars-all.png","title":"","alt":"Stars from github","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[139,142],"level":0},{"type":"inline","content":"\u003csmall\u003e\n  Source: \u003ca href=\"https://repository.surf/supabase\"\u003erepository.surf/supabase\u003c/a\u003e\n\u003c/small\u003e","level":1,"lines":[139,142],"children":[{"type":"text","content":"\u003csmall\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Source: \u003ca href=\"https://repository.surf/supabase\"\u003erepository.surf/supabase\u003c/a\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/small\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[143,144],"level":0},{"type":"inline","content":"If you want to keep up to date, make sure you [subscribe to our YouTube channel](https://www.youtube.com/c/supabase) or [follow us on Twitter](https://twitter.com/supabase).","level":1,"lines":[143,144],"children":[{"type":"text","content":"If you want to keep up to date, make sure you ","level":0},{"type":"link_open","href":"https://www.youtube.com/c/supabase","title":"","level":0},{"type":"text","content":"subscribe to our YouTube channel","level":1},{"type":"link_close","level":0},{"type":"text","content":" or ","level":0},{"type":"link_open","href":"https://twitter.com/supabase","title":"","level":0},{"type":"text","content":"follow us on Twitter","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[145,146],"level":0},{"type":"inline","content":"[Coming Next](#coming-next)","level":1,"lines":[145,146],"children":[{"type":"text","content":"Coming Next","level":0}],"lvl":2,"i":9,"seen":0,"slug":"coming-next"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[147,148],"level":0},{"type":"inline","content":"You might have noticed our Dashboard slowly changing (improving), as we migrate the components out to our [open source UI Library](https://github.com/supabase/ui). This progression is an important step towards offering a UI for [Local Development](/docs/guides/local-development) and [Self Hosting](/docs/guides/self-hosting).","level":1,"lines":[147,148],"children":[{"type":"text","content":"You might have noticed our Dashboard slowly changing (improving), as we migrate the components out to our ","level":0},{"type":"link_open","href":"https://github.com/supabase/ui","title":"","level":0},{"type":"text","content":"open source UI Library","level":1},{"type":"link_close","level":0},{"type":"text","content":". This progression is an important step towards offering a UI for ","level":0},{"type":"link_open","href":"/docs/guides/local-development","title":"","level":0},{"type":"text","content":"Local Development","level":1},{"type":"link_close","level":0},{"type":"text","content":" and ","level":0},{"type":"link_open","href":"/docs/guides/self-hosting","title":"","level":0},{"type":"text","content":"Self Hosting","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[149,150],"level":0},{"type":"inline","content":"We're also working on our [Workflows engine](/blog/supabase-workflows). This is quite a large task, but we're making progress and aiming to ship sometime in July.","level":1,"lines":[149,150],"children":[{"type":"text","content":"We're also working on our ","level":0},{"type":"link_open","href":"/blog/supabase-workflows","title":"","level":0},{"type":"text","content":"Workflows engine","level":1},{"type":"link_close","level":0},{"type":"text","content":". This is quite a large task, but we're making progress and aiming to ship sometime in July.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[151,152],"level":0},{"type":"inline","content":"[One more thing](#one-more-thing)","level":1,"lines":[151,152],"children":[{"type":"text","content":"One more thing","level":0}],"lvl":2,"i":10,"seen":0,"slug":"one-more-thing"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[153,154],"level":0},{"type":"inline","content":"[We started hiring](/docs/careers).","level":1,"lines":[153,154],"children":[{"type":"link_open","href":"/docs/careers","title":"","level":0},{"type":"text","content":"We started hiring","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[155,156],"level":0},{"type":"inline","content":"![screenshot of our hiring page](/images/blog/april-2021/hiring.png)","level":1,"lines":[155,156],"children":[{"type":"image","src":"/images/blog/april-2021/hiring.png","title":"","alt":"screenshot of our hiring page","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[157,158],"level":0},{"type":"inline","content":"[Get started](#get-started)","level":1,"lines":[157,158],"children":[{"type":"text","content":"Get started","level":0}],"lvl":3,"i":11,"seen":0,"slug":"get-started"},{"type":"heading_close","hLevel":3,"level":0},{"type":"bullet_list_open","lines":[159,164],"level":0},{"type":"list_item_open","lines":[159,160],"level":1},{"type":"paragraph_open","tight":true,"lines":[159,160],"level":2},{"type":"inline","content":"Start using Supabase today: [supabase.com/dashboard](https://supabase.com/dashboard/)","level":3,"lines":[159,160],"children":[{"type":"text","content":"Start using Supabase today: ","level":0},{"type":"link_open","href":"https://supabase.com/dashboard/","title":"","level":0},{"type":"text","content":"supabase.com/dashboard","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[160,161],"level":1},{"type":"paragraph_open","tight":true,"lines":[160,161],"level":2},{"type":"inline","content":"Make sure to [star us on GitHub](https://github.com/supabase/supabase)","level":3,"lines":[160,161],"children":[{"type":"text","content":"Make sure to ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase","title":"","level":0},{"type":"text","content":"star us on GitHub","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[161,162],"level":1},{"type":"paragraph_open","tight":true,"lines":[161,162],"level":2},{"type":"inline","content":"Follow us [on Twitter](https://twitter.com/supabase)","level":3,"lines":[161,162],"children":[{"type":"text","content":"Follow us ","level":0},{"type":"link_open","href":"https://twitter.com/supabase","title":"","level":0},{"type":"text","content":"on Twitter","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[162,163],"level":1},{"type":"paragraph_open","tight":true,"lines":[162,163],"level":2},{"type":"inline","content":"Subscribe to our [YouTube channel](https://www.youtube.com/c/supabase)","level":3,"lines":[162,163],"children":[{"type":"text","content":"Subscribe to our ","level":0},{"type":"link_open","href":"https://www.youtube.com/c/supabase","title":"","level":0},{"type":"text","content":"YouTube channel","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[163,164],"level":1},{"type":"paragraph_open","tight":true,"lines":[163,164],"level":2},{"type":"inline","content":"Become a [sponsor](https://github.com/sponsors/supabase)","level":3,"lines":[163,164],"children":[{"type":"text","content":"Become a ","level":0},{"type":"link_open","href":"https://github.com/sponsors/supabase","title":"","level":0},{"type":"text","content":"sponsor","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0}],"content":"- [Light Mode](#light-mode)\n- [Translations](#translations)\n- [OpenAPI spec for Storage](#openapi-spec-for-storage)\n- [Stripe Sync Engine (Experimental)](#stripe-sync-engine-experimental)\n- [Community spotlight: Threaded comments](#community-spotlight-threaded-comments)\n- [Community spotlight: SupaScript](#community-spotlight-supascript)\n- [Community spotlight: Fireship](#community-spotlight-fireship)\n- [Community](#community)\n- [Coming Next](#coming-next)\n- [One more thing](#one-more-thing)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-beta-april-2021"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>