<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase Studio 2.0: help when you need it most</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Supabase Studio now comes with ChatGPT, and GraphiQL built in, Cascade Deletes, and Foreign Key Selectors, and much more." data-next-head=""/><meta property="og:title" content="Supabase Studio 2.0: help when you need it most" data-next-head=""/><meta property="og:description" content="Supabase Studio now comes with ChatGPT, and GraphiQL built in, Cascade Deletes, and Foreign Key Selectors, and much more." data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-studio-2.0" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2023-04-14" data-next-head=""/><meta property="article:author" content="https://github.com/alaister" data-next-head=""/><meta property="article:author" content="https://github.com/joshenlim" data-next-head=""/><meta property="article:author" content="https://github.com/mildtomato" data-next-head=""/><meta property="article:author" content="https://github.com/saltcod" data-next-head=""/><meta property="article:tag" content="launch-week" data-next-head=""/><meta property="article:tag" content="studio" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/launch-week-7/day-5-supabase-studio-2.0/day-5-supabase-studio-2.0-og.jpg" data-next-head=""/><meta property="og:image:alt" content="Supabase Studio 2.0: help when you need it most thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Supabase Studio 2.0: help when you need it most</h1><div class="text-light flex space-x-3 text-sm"><p>14 Apr 2023</p><p>•</p><p>8 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/alaister"><div class="flex items-center gap-3"><div class="w-10"><img alt="Alaister Young avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Falaister.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Falaister.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Falaister.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Alaister Young</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/joshenlim"><div class="flex items-center gap-3"><div class="w-10"><img alt="Joshen Lim avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fjoshenlim.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fjoshenlim.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fjoshenlim.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Joshen Lim</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/mildtomato"><div class="flex items-center gap-3"><div class="w-10"><img alt="Jonny Summers-Muir avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fmildtomato.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fmildtomato.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fmildtomato.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Jonny Summers-Muir</span><span class="text-foreground-lighter mb-0 text-xs">Product Design</span></div></div></a></div><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/saltcod"><div class="flex items-center gap-3"><div class="w-10"><img alt="Terry Sutton avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fsaltcod.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fsaltcod.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fsaltcod.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Terry Sutton</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Supabase Studio 2.0: help when you need it most" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-5-supabase-studio-2.0%2Fday-5-supabase-studio-2.0-thumb.jpg&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-5-supabase-studio-2.0%2Fday-5-supabase-studio-2.0-thumb.jpg&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-5-supabase-studio-2.0%2Fday-5-supabase-studio-2.0-thumb.jpg&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-5-supabase-studio-2.0%2Fday-5-supabase-studio-2.0-thumb.jpg&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-5-supabase-studio-2.0%2Fday-5-supabase-studio-2.0-thumb.jpg&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-5-supabase-studio-2.0%2Fday-5-supabase-studio-2.0-thumb.jpg&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-5-supabase-studio-2.0%2Fday-5-supabase-studio-2.0-thumb.jpg&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-5-supabase-studio-2.0%2Fday-5-supabase-studio-2.0-thumb.jpg&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-5-supabase-studio-2.0%2Fday-5-supabase-studio-2.0-thumb.jpg&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-5-supabase-studio-2.0%2Fday-5-supabase-studio-2.0-thumb.jpg&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-5-supabase-studio-2.0%2Fday-5-supabase-studio-2.0-thumb.jpg&amp;w=3840&amp;q=100"/></div><p>Today we&#x27;re announcing Supabase Studio 2.0, packed with a ton of new stuff. We have features people have been asking for forever, and new capabilities that will change the way you work.</p>
<div class="video-container"><iframe class="w-full" src="https://www.youtube-nocookie.com/embed/0rcNqHt5KWU" title="YouTube video player" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share"></iframe></div>
<p>Here&#x27;s the birds-eye-view:</p>
<ul><li><p><a href="#supabase-ai--assisted-database-development"><strong>Supabase AI</strong></a>: ChatGPT, right in the Studio</p></li><li><a href="#graphiql"><strong>GraphiQL</strong></a>: Query your database with GraphQL</li><li><a href="#cascade-deletes"><strong>Cascade deletes</strong></a>: Our #1 most requested feature from the community</li><li><a href="#query-performance"><strong>Query Performance</strong></a>: Investigate slow running queries</li><li><a href="#foreign-key-selector"><strong>Foreign key Selector</strong></a>: Look up a row in reference table</li><li><a href="#postgres-roles"><strong>Postgres Roles</strong></a>: Manage your Postgres roles and privileges</li><li><a href="#database-webhooks"><strong>Database Webhooks</strong></a>: Database triggers for Edge Functions</li><li><p><a href="#tableview-definitions"><strong>Table/View definitions</strong></a>: View SQL definitions for tables and views</p></li><li><a href="#api-autodocs"><strong>API Autodocs</strong></a>: View auto-generated docs right from the Table Editor</li><li><p><a href="#support-for-many-tables"><strong>Support for many tables</strong></a>: Table Editor now supports 1000s of
tables</p></li><li><a href="#json-editing"><strong>JSON editing</strong></a>: Improved JSON editing</li><li><a href="#nullable-columns"><strong>Nullable columns</strong></a>: Allow text/bool cells to be null or empty</li></ul>
<h2 id="supabase-ai--assisted-database-development" class="group scroll-mt-24">Supabase AI — assisted database development<a href="#supabase-ai--assisted-database-development" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<video width="99%" autoplay="" loop="" muted="" playsinline="" controls=""><source src="https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/cmdk.mp4" type="video/mp4"/></video>
<p>One of our guiding principles at Supabase is to make SQL more accessible to developers. We don&#x27;t want to just abstract it away with a custom implementation. By embracing SQL, developers learn important, transferable skills while they build on top of Supabase.</p>
<p>In the past few months, AI advancements have made this easy! Writing a complex SQL query is now as simple as asking ChatGPT, and we&#x27;re leaning into this approach with our Studio. AI gives developers superpowers, and by providing relevant context, Supabase gives AI superpowers.</p>
<p>AI has already changed a lot about the way we work as developers. We can use ChatGPT to write code for us as fast as we can prompt it. We wanted to bring this power into the Studio with integrations that will help you work even faster than before.</p>
<p>Today you&#x27;ll be able to do many common SQL operations with the help of AI. You can create tables, views and indexes, write functions and triggers, and more, right from the ⌘K menu.</p>
<p>Soon, we&#x27;ll also let you opt-in to sending your table schemas to OpenAI to help fine tune queries to your project. All this should result in a dramatic boost in development speed. Maybe you&#x27;ll only need half a weekend to scale to millions now!</p>
<p>As with every ⌘K menu, quick navigation is at the heart of things. You&#x27;ll now be able to jump to any page in the Studio in a couple of keystrokes. You can also search the Docs directly from the menu.</p>
<p>We&#x27;ll be actively working on our ⌘K menu in the coming months to make it faster and even more useful for you. Next, we&#x27;re going to focus on better support for writing RLS policies. Stay tuned, and make sure to explore how we&#x27;re integrating AI <a href="https://supabase.com/blog/tags/AI">behind the scenes</a>!</p>
<p>Huge props for the amazing work done on the <a href="https://cmdk.paco.me/">⌘K package</a> we&#x27;ve built on top of here.</p>
<hr/>
<p>Along with the new AI features, we also doubled-down on some of the critical missing pieces for a Postgres UI. Before we started to work on any of these, we combed through user feedback and tallied up the most submitted Feature Requests on GitHub.</p>
<div class="bg-gray-300 rounded-lg px-6 py-2 italic"><p>📢 Many of the features and enhancements below came from user requests — <a href="https://github.com/orgs/supabase/discussions/categories/feature-requests">please keep them coming</a>!</p></div>
<h2 id="graphiql" class="group scroll-mt-24">GraphiQL<a href="#graphiql" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>While GraphQL has been available in Supabase for just over a year, we haven&#x27;t provided a visual tool for using it. You can now use the very popular GraphiQL directly from the Studio. GraphiQL is a browser tool for writing, validating, and testing GraphQL testing. Being able to use a fully integrated GraphQL IDE is a huge DX win.</p>
<h2 id="cascade-deletes" class="group scroll-mt-24">Cascade deletes<a href="#cascade-deletes" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<video width="99%" autoplay="" loop="" muted="" playsinline="" controls=""><source src="https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/cascade-delete.mp4" type="video/mp4"/></video>
<p>Cascade deletes are a core feature of Postgres, but have not been available in the Studio UI until now. This has been the #1 feature request from the community for a long time. You can now choose what you want to happen when deleting a referenced row in another table, right from Table Editor side panel.</p>
<h2 id="query-performance" class="group scroll-mt-24">Query Performance<a href="#query-performance" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>We&#x27;ve just released a new Query Performance tool to help you identify slow queries. Using this tool, along with our new <a href="https://supabase.com/docs/guides/platform/performance#examining-query-performance">guide</a> should help you speed things up. These tools can help you uncover where you might have inefficient queries or schemas, or where you might need indexes or even additional compute resources.</p>
<h2 id="foreign-key-selector" class="group scroll-mt-24">Foreign key selector<a href="#foreign-key-selector" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<video width="99%" autoplay="" loop="" muted="" playsinline="" controls=""><source src="https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/fk-lookup.mp4" type="video/mp4"/></video>
<p>You can now select a referencing row from another table, rather than having to pass a value manually.
You can also quickly jump to the row being referenced in another table with the <em>View referencing</em> record
button from the grid itself.</p>
<h2 id="postgres-roles" class="group scroll-mt-24">Postgres Roles<a href="#postgres-roles" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>The Roles page got a huge revision, making it easy for you to manage access control through users, groups, and permissions. It&#x27;s now easy to see how many connections you&#x27;re using and where they&#x27;re coming from.</p>
<h2 id="database-webhooks" class="group scroll-mt-24">Database Webhooks<a href="#database-webhooks" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>We&#x27;ve wanted to improve the webhooks interface for a long time, and we&#x27;ve finally gotten to it. We now have full support for editing your webhooks, and you can now select an edge function to call instead of having to pass in a url. We&#x27;re happy to roll this very common request out to the community.</p>
<h2 id="tableview-definitions" class="group scroll-mt-24">Table/View definitions<a href="#tableview-definitions" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>You can now see the SQL definitions for your tables and views, directly from the Table Editor. This is a great way to see how Supabase is translating your UI changes into SQL. This can be useful for creating migrations, getting help, or just for learning more about how Supabase and SQL works.</p>
<h2 id="api-autodocs" class="group scroll-mt-24">API Autodocs<a href="#api-autodocs" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>You can now view the auto-generated API docs right from the Table Editor. Grab the SQL and <code class="short-inline-codeblock">supabase-js</code> code for all of the CRUD operations, straight from the table. You can also quickly generate and download a Typescript types file for your table, right from the editor.</p>
<h3 id="quality-of-life-improvements" class="group scroll-mt-24">Quality of life improvements<a href="#quality-of-life-improvements" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<h4 id="support-for-many-tables" class="group scroll-mt-24">Support for many tables<a href="#support-for-many-tables" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h4>
<p>Previously, the Table Editor would get slow and unresponsive when you had many tables. We&#x27;ve made a number of improvements to make it much faster and more responsive. Feel free to make all the tables you need!</p>
<h4 id="json-editing" class="group scroll-mt-24">JSON editing<a href="#json-editing" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h4>
<p>You&#x27;ve always been able to use the JSON data type in your tables, but editing the data wasn&#x27;t easy. We&#x27;ve improved the inline Table Editor, and also now allow you open json cells from the side panel for a more spacious editing experience. Next Launch Week we&#x27;re hoping to decide if it&#x27;s pronounced “Jason” or “Jay-sawn”. Stay tuned.</p>
<h4 id="nullable-columns" class="group scroll-mt-24">Nullable columns<a href="#nullable-columns" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h4>
<p>Speaking of extremely common feature requests, we&#x27;ve gotten this one a lot in the past few months. You used to have to handle this manually, but now you can now allow text/boolean cells to be null or empty. Supabase, where productivity is more than just an empty (or null) promise.</p>
<h2 id="wrapping-up" class="group scroll-mt-24">Wrapping Up<a href="#wrapping-up" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We hope you get a lot of value out of these new features and enhancements. As we mentioned earlier, many of the features listed here came directly from <a href="https://github.com/orgs/supabase/discussions/categories/feature-requests">Feature Requests</a> on GitHub. Thanks to everyone who have taken the time to submit these, and encourage submissions for anything else you&#x27;d like to see.</p></div></article><div class="flex flex-col gap-3 lg:gap-4 border-t border-muted py-4 lg:py-8 mt-4 lg:mt-8"><h3 class="text-foreground text-xl mb-4">More Launch Week 7</h3><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="https://supabase.com/blog/designing-with-ai-midjourney"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Designing with AI</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="https://github.com/supabase/supavisor"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Supavisor</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="https://supabase.com/blog/supabase-logs-self-hosted"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Open Source Logging</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="https://supabase.com/blog/edge-runtime-self-hosted-deno-functions"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Self-hosted Deno Edge Functions</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="https://supabase.com/blog/storage-v3-resumable-uploads"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg max-w-[240px]">Storage v3: Resumable Uploads with support for 50GB files</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="https://supabase.com/blog/supabase-auth-sso-pkce"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg max-w-[240px]">Supabase Auth: SSO, Mobile, and Server-side support</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="launch-week-7-community-highlights.html"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Community Highlight</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="supabase-studio-2.0.html"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Studio Updates</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="dbdev.html"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">dbdev</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="pg-tle.html"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Postgres TLE</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div></div><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-studio-2.0&amp;text=Supabase%20Studio%202.0%3A%20help%20when%20you%20need%20it%20most"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-studio-2.0&amp;text=Supabase%20Studio%202.0%3A%20help%20when%20you%20need%20it%20most"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-studio-2.0&amp;t=Supabase%20Studio%202.0%3A%20help%20when%20you%20need%20it%20most"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="pg-tle.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Trusted Language Extensions for Postgres</h4><p class="small">14 April 2023</p></div></div></div></div></a></div><div><a href="https://supabase.com/blog/supabase-auth-sso-pkce"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Supabase Auth: SSO,  Mobile, and Server-side support</h4><p class="small">13 April 2023</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/launch-week"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">launch-week</div></a><a href="https://supabase.com/blog/tags/studio"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">studio</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#supabase-ai--assisted-database-development">Supabase AI — assisted database development</a></li>
<li><a href="#graphiql">GraphiQL</a></li>
<li><a href="#cascade-deletes">Cascade deletes</a></li>
<li><a href="#query-performance">Query Performance</a></li>
<li><a href="#foreign-key-selector">Foreign key selector</a></li>
<li><a href="#postgres-roles">Postgres Roles</a></li>
<li><a href="#database-webhooks">Database Webhooks</a></li>
<li><a href="#tableview-definitions">Table/View definitions</a></li>
<li><a href="#api-autodocs">API Autodocs</a>
<ul>
<li><a href="#quality-of-life-improvements">Quality of life improvements</a></li>
</ul>
</li>
<li><a href="#wrapping-up">Wrapping Up</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-studio-2.0&amp;text=Supabase%20Studio%202.0%3A%20help%20when%20you%20need%20it%20most"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-studio-2.0&amp;text=Supabase%20Studio%202.0%3A%20help%20when%20you%20need%20it%20most"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-studio-2.0&amp;t=Supabase%20Studio%202.0%3A%20help%20when%20you%20need%20it%20most"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"pg-tle","title":"Trusted Language Extensions for Postgres","description":"We're collaborating with AWS to bring Trusted Language Extensions to Postgres.","launchweek":"7","categories":["postgres"],"tags":["launch-week","postgres"],"date":"2023-04-14","toc_depth":3,"author":"michel,daltjoh-aws","image":"launch-week-7/day-5-supabase-pg-tle/day-5-postgres-tle-thumb.jpg","thumb":"launch-week-7/day-5-supabase-pg-tle/day-5-postgres-tle-thumb.jpg","formattedDate":"14 April 2023","readingTime":"6 minute read","url":"/blog/pg-tle","path":"/blog/pg-tle"},"nextPost":{"slug":"supabase-auth-sso-pkce","title":"Supabase Auth: SSO,  Mobile, and Server-side support","description":"Supacharging Supabase Auth with Sign in with Apple on iOS, Single-Sign-On support with SAML 2.0, and PKCE for server-side rendering and mobile auth.","launchweek":"7","categories":["product"],"tags":["launch-week","auth"],"date":"2023-04-13","toc_depth":3,"author":"stojan,joel,kangmingtay","image":"launch-week-7/day-4-supabase-auth-sso-pkce/supabase-auth-sso-pkce-thumb.png","thumb":"launch-week-7/day-4-supabase-auth-sso-pkce/supabase-auth-sso-pkce-og.jpg","formattedDate":"13 April 2023","readingTime":"10 minute read","url":"/blog/supabase-auth-sso-pkce","path":"/blog/supabase-auth-sso-pkce"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-studio-2.0","source":"\nToday we're announcing Supabase Studio 2.0, packed with a ton of new stuff. We have features people have been asking for forever, and new capabilities that will change the way you work.\n\n\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/0rcNqHt5KWU\"\n    title=\"YouTube video player\"\n    frameBorder=\"0\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"\n    allowfullscreen\n  \u003e\u003c/iframe\u003e\n\u003c/div\u003e\n\nHere's the birds-eye-view:\n\n\u003cul\u003e\n  \u003cli\u003e\n    [**Supabase AI**](#supabase-ai--assisted-database-development): ChatGPT, right in the Studio\n  \u003c/li\u003e\n  \u003cli\u003e[**GraphiQL**](#graphiql): Query your database with GraphQL\u003c/li\u003e\n  \u003cli\u003e[**Cascade deletes**](#cascade-deletes): Our #1 most requested feature from the community\u003c/li\u003e\n  \u003cli\u003e[**Query Performance**](#query-performance): Investigate slow running queries\u003c/li\u003e\n  \u003cli\u003e[**Foreign key Selector**](#foreign-key-selector): Look up a row in reference table\u003c/li\u003e\n  \u003cli\u003e[**Postgres Roles**](#postgres-roles): Manage your Postgres roles and privileges\u003c/li\u003e\n  \u003cli\u003e[**Database Webhooks**](#database-webhooks): Database triggers for Edge Functions\u003c/li\u003e\n  \u003cli\u003e\n    [**Table/View definitions**](#tableview-definitions): View SQL definitions for tables and views\n  \u003c/li\u003e\n  \u003cli\u003e[**API Autodocs**](#api-autodocs): View auto-generated docs right from the Table Editor\u003c/li\u003e\n  \u003cli\u003e\n    [**Support for many tables**](#support-for-many-tables): Table Editor now supports 1000s of\n    tables\n  \u003c/li\u003e\n  \u003cli\u003e[**JSON editing**](#json-editing): Improved JSON editing\u003c/li\u003e\n  \u003cli\u003e[**Nullable columns**](#nullable-columns): Allow text/bool cells to be null or empty\u003c/li\u003e\n\u003c/ul\u003e\n\n## Supabase AI — assisted database development\n\n\u003cvideo width=\"99%\" autoPlay loop muted playsInline controls={true}\u003e\n  \u003csource\n    src=\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/cmdk.mp4\"\n    type=\"video/mp4\"\n  /\u003e\n\u003c/video\u003e\n\nOne of our guiding principles at Supabase is to make SQL more accessible to developers. We don't want to just abstract it away with a custom implementation. By embracing SQL, developers learn important, transferable skills while they build on top of Supabase.\n\nIn the past few months, AI advancements have made this easy! Writing a complex SQL query is now as simple as asking ChatGPT, and we're leaning into this approach with our Studio. AI gives developers superpowers, and by providing relevant context, Supabase gives AI superpowers.\n\nAI has already changed a lot about the way we work as developers. We can use ChatGPT to write code for us as fast as we can prompt it. We wanted to bring this power into the Studio with integrations that will help you work even faster than before.\n\nToday you'll be able to do many common SQL operations with the help of AI. You can create tables, views and indexes, write functions and triggers, and more, right from the ⌘K menu.\n\nSoon, we'll also let you opt-in to sending your table schemas to OpenAI to help fine tune queries to your project. All this should result in a dramatic boost in development speed. Maybe you'll only need half a weekend to scale to millions now!\n\nAs with every ⌘K menu, quick navigation is at the heart of things. You'll now be able to jump to any page in the Studio in a couple of keystrokes. You can also search the Docs directly from the menu.\n\nWe'll be actively working on our ⌘K menu in the coming months to make it faster and even more useful for you. Next, we're going to focus on better support for writing RLS policies. Stay tuned, and make sure to explore how we're integrating AI [behind the scenes](https://supabase.com/blog/tags/AI)!\n\nHuge props for the amazing work done on the [⌘K package](https://cmdk.paco.me/) we've built on top of here.\n\n---\n\nAlong with the new AI features, we also doubled-down on some of the critical missing pieces for a Postgres UI. Before we started to work on any of these, we combed through user feedback and tallied up the most submitted Feature Requests on GitHub.\n\n\u003cdiv className=\"bg-gray-300 rounded-lg px-6 py-2 italic\"\u003e\n\n📢 Many of the features and enhancements below came from user requests — [please keep them coming](https://github.com/orgs/supabase/discussions/categories/feature-requests)!\n\n\u003c/div\u003e\n\n## GraphiQL\n\n![Logs UI](/images/blog/lw7-studio/graphiql.png)\n\nWhile GraphQL has been available in Supabase for just over a year, we haven't provided a visual tool for using it. You can now use the very popular GraphiQL directly from the Studio. GraphiQL is a browser tool for writing, validating, and testing GraphQL testing. Being able to use a fully integrated GraphQL IDE is a huge DX win.\n\n## Cascade deletes\n\n\u003cvideo width=\"99%\" autoPlay loop muted playsInline controls={true}\u003e\n  \u003csource\n    src=\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/cascade-delete.mp4\"\n    type=\"video/mp4\"\n  /\u003e\n\u003c/video\u003e\n\nCascade deletes are a core feature of Postgres, but have not been available in the Studio UI until now. This has been the #1 feature request from the community for a long time. You can now choose what you want to happen when deleting a referenced row in another table, right from Table Editor side panel.\n\n## Query Performance\n\n![Logs UI](/images/blog/lw7-studio/query-performance.png)\n\nWe've just released a new Query Performance tool to help you identify slow queries. Using this tool, along with our new [guide](https://supabase.com/docs/guides/platform/performance#examining-query-performance) should help you speed things up. These tools can help you uncover where you might have inefficient queries or schemas, or where you might need indexes or even additional compute resources.\n\n## Foreign key selector\n\n\u003cvideo width=\"99%\" autoPlay loop muted playsInline controls={true}\u003e\n  \u003csource\n    src=\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/fk-lookup.mp4\"\n    type=\"video/mp4\"\n  /\u003e\n\u003c/video\u003e\n\nYou can now select a referencing row from another table, rather than having to pass a value manually.\nYou can also quickly jump to the row being referenced in another table with the _View referencing_ record\nbutton from the grid itself.\n\n## Postgres Roles\n\n![Logs UI](/images/blog/lw7-studio/roles.png)\n\nThe Roles page got a huge revision, making it easy for you to manage access control through users, groups, and permissions. It's now easy to see how many connections you're using and where they're coming from.\n\n## Database Webhooks\n\n![Logs UI](/images/blog/lw7-studio/webhooks.png)\n\nWe've wanted to improve the webhooks interface for a long time, and we've finally gotten to it. We now have full support for editing your webhooks, and you can now select an edge function to call instead of having to pass in a url. We're happy to roll this very common request out to the community.\n\n## Table/View definitions\n\n![Logs UI](/images/blog/lw7-studio/definitions.png)\n\nYou can now see the SQL definitions for your tables and views, directly from the Table Editor. This is a great way to see how Supabase is translating your UI changes into SQL. This can be useful for creating migrations, getting help, or just for learning more about how Supabase and SQL works.\n\n## API Autodocs\n\n![Logs UI](/images/blog/lw7-studio/api-autodocs.png)\n\nYou can now view the auto-generated API docs right from the Table Editor. Grab the SQL and `supabase-js` code for all of the CRUD operations, straight from the table. You can also quickly generate and download a Typescript types file for your table, right from the editor.\n\n### Quality of life improvements\n\n#### Support for many tables\n\nPreviously, the Table Editor would get slow and unresponsive when you had many tables. We've made a number of improvements to make it much faster and more responsive. Feel free to make all the tables you need!\n\n#### JSON editing\n\nYou've always been able to use the JSON data type in your tables, but editing the data wasn't easy. We've improved the inline Table Editor, and also now allow you open json cells from the side panel for a more spacious editing experience. Next Launch Week we're hoping to decide if it's pronounced “Jason” or “Jay-sawn”. Stay tuned.\n\n#### Nullable columns\n\nSpeaking of extremely common feature requests, we've gotten this one a lot in the past few months. You used to have to handle this manually, but now you can now allow text/boolean cells to be null or empty. Supabase, where productivity is more than just an empty (or null) promise.\n\n## Wrapping Up\n\nWe hope you get a lot of value out of these new features and enhancements. As we mentioned earlier, many of the features listed here came directly from [Feature Requests](https://github.com/orgs/supabase/discussions/categories/feature-requests) on GitHub. Thanks to everyone who have taken the time to submit these, and encourage submissions for anything else you'd like to see.\n","title":"Supabase Studio 2.0: help when you need it most","description":"Supabase Studio now comes with ChatGPT, and GraphiQL built in, Cascade Deletes, and Foreign Key Selectors, and much more.","launchweek":"7","categories":["product"],"tags":["launch-week","studio"],"date":"2023-04-14","toc_depth":3,"author":"alaister,joshenlim,jonny,saltcod","image":"launch-week-7/day-5-supabase-studio-2.0/day-5-supabase-studio-2.0-og.jpg","thumb":"launch-week-7/day-5-supabase-studio-2.0/day-5-supabase-studio-2.0-thumb.jpg","content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    a: \"a\",\n    strong: \"strong\",\n    h2: \"h2\",\n    hr: \"hr\",\n    img: \"img\",\n    em: \"em\",\n    code: \"code\",\n    h3: \"h3\",\n    h4: \"h4\"\n  }, _provideComponents(), props.components);\n  return _jsxs(_Fragment, {\n    children: [_jsx(_components.p, {\n      children: \"Today we're announcing Supabase Studio 2.0, packed with a ton of new stuff. We have features people have been asking for forever, and new capabilities that will change the way you work.\"\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"video-container\",\n      children: _jsx(\"iframe\", {\n        className: \"w-full\",\n        src: \"https://www.youtube-nocookie.com/embed/0rcNqHt5KWU\",\n        title: \"YouTube video player\",\n        frameBorder: \"0\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\",\n        allowfullscreen: true\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Here's the birds-eye-view:\"\n    }), \"\\n\", _jsxs(\"ul\", {\n      children: [_jsx(\"li\", {\n        children: _jsxs(_components.p, {\n          children: [_jsx(_components.a, {\n            href: \"#supabase-ai--assisted-database-development\",\n            children: _jsx(_components.strong, {\n              children: \"Supabase AI\"\n            })\n          }), \": ChatGPT, right in the Studio\"]\n        })\n      }), _jsxs(\"li\", {\n        children: [_jsx(_components.a, {\n          href: \"#graphiql\",\n          children: _jsx(_components.strong, {\n            children: \"GraphiQL\"\n          })\n        }), \": Query your database with GraphQL\"]\n      }), _jsxs(\"li\", {\n        children: [_jsx(_components.a, {\n          href: \"#cascade-deletes\",\n          children: _jsx(_components.strong, {\n            children: \"Cascade deletes\"\n          })\n        }), \": Our #1 most requested feature from the community\"]\n      }), _jsxs(\"li\", {\n        children: [_jsx(_components.a, {\n          href: \"#query-performance\",\n          children: _jsx(_components.strong, {\n            children: \"Query Performance\"\n          })\n        }), \": Investigate slow running queries\"]\n      }), _jsxs(\"li\", {\n        children: [_jsx(_components.a, {\n          href: \"#foreign-key-selector\",\n          children: _jsx(_components.strong, {\n            children: \"Foreign key Selector\"\n          })\n        }), \": Look up a row in reference table\"]\n      }), _jsxs(\"li\", {\n        children: [_jsx(_components.a, {\n          href: \"#postgres-roles\",\n          children: _jsx(_components.strong, {\n            children: \"Postgres Roles\"\n          })\n        }), \": Manage your Postgres roles and privileges\"]\n      }), _jsxs(\"li\", {\n        children: [_jsx(_components.a, {\n          href: \"#database-webhooks\",\n          children: _jsx(_components.strong, {\n            children: \"Database Webhooks\"\n          })\n        }), \": Database triggers for Edge Functions\"]\n      }), _jsx(\"li\", {\n        children: _jsxs(_components.p, {\n          children: [_jsx(_components.a, {\n            href: \"#tableview-definitions\",\n            children: _jsx(_components.strong, {\n              children: \"Table/View definitions\"\n            })\n          }), \": View SQL definitions for tables and views\"]\n        })\n      }), _jsxs(\"li\", {\n        children: [_jsx(_components.a, {\n          href: \"#api-autodocs\",\n          children: _jsx(_components.strong, {\n            children: \"API Autodocs\"\n          })\n        }), \": View auto-generated docs right from the Table Editor\"]\n      }), _jsx(\"li\", {\n        children: _jsxs(_components.p, {\n          children: [_jsx(_components.a, {\n            href: \"#support-for-many-tables\",\n            children: _jsx(_components.strong, {\n              children: \"Support for many tables\"\n            })\n          }), \": Table Editor now supports 1000s of\\ntables\"]\n        })\n      }), _jsxs(\"li\", {\n        children: [_jsx(_components.a, {\n          href: \"#json-editing\",\n          children: _jsx(_components.strong, {\n            children: \"JSON editing\"\n          })\n        }), \": Improved JSON editing\"]\n      }), _jsxs(\"li\", {\n        children: [_jsx(_components.a, {\n          href: \"#nullable-columns\",\n          children: _jsx(_components.strong, {\n            children: \"Nullable columns\"\n          })\n        }), \": Allow text/bool cells to be null or empty\"]\n      })]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"supabase-ai--assisted-database-development\",\n      children: \"Supabase AI — assisted database development\"\n    }), \"\\n\", _jsx(\"video\", {\n      width: \"99%\",\n      autoPlay: true,\n      loop: true,\n      muted: true,\n      playsInline: true,\n      controls: true,\n      children: _jsx(\"source\", {\n        src: \"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/cmdk.mp4\",\n        type: \"video/mp4\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"One of our guiding principles at Supabase is to make SQL more accessible to developers. We don't want to just abstract it away with a custom implementation. By embracing SQL, developers learn important, transferable skills while they build on top of Supabase.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In the past few months, AI advancements have made this easy! Writing a complex SQL query is now as simple as asking ChatGPT, and we're leaning into this approach with our Studio. AI gives developers superpowers, and by providing relevant context, Supabase gives AI superpowers.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"AI has already changed a lot about the way we work as developers. We can use ChatGPT to write code for us as fast as we can prompt it. We wanted to bring this power into the Studio with integrations that will help you work even faster than before.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Today you'll be able to do many common SQL operations with the help of AI. You can create tables, views and indexes, write functions and triggers, and more, right from the ⌘K menu.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Soon, we'll also let you opt-in to sending your table schemas to OpenAI to help fine tune queries to your project. All this should result in a dramatic boost in development speed. Maybe you'll only need half a weekend to scale to millions now!\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"As with every ⌘K menu, quick navigation is at the heart of things. You'll now be able to jump to any page in the Studio in a couple of keystrokes. You can also search the Docs directly from the menu.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We'll be actively working on our ⌘K menu in the coming months to make it faster and even more useful for you. Next, we're going to focus on better support for writing RLS policies. Stay tuned, and make sure to explore how we're integrating AI \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/tags/AI\",\n        children: \"behind the scenes\"\n      }), \"!\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Huge props for the amazing work done on the \", _jsx(_components.a, {\n        href: \"https://cmdk.paco.me/\",\n        children: \"⌘K package\"\n      }), \" we've built on top of here.\"]\n    }), \"\\n\", _jsx(_components.hr, {}), \"\\n\", _jsx(_components.p, {\n      children: \"Along with the new AI features, we also doubled-down on some of the critical missing pieces for a Postgres UI. Before we started to work on any of these, we combed through user feedback and tallied up the most submitted Feature Requests on GitHub.\"\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"bg-gray-300 rounded-lg px-6 py-2 italic\",\n      children: _jsxs(_components.p, {\n        children: [\"📢 Many of the features and enhancements below came from user requests — \", _jsx(_components.a, {\n          href: \"https://github.com/orgs/supabase/discussions/categories/feature-requests\",\n          children: \"please keep them coming\"\n        }), \"!\"]\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"graphiql\",\n      children: \"GraphiQL\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/lw7-studio/graphiql.png\",\n        alt: \"Logs UI\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"While GraphQL has been available in Supabase for just over a year, we haven't provided a visual tool for using it. You can now use the very popular GraphiQL directly from the Studio. GraphiQL is a browser tool for writing, validating, and testing GraphQL testing. Being able to use a fully integrated GraphQL IDE is a huge DX win.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"cascade-deletes\",\n      children: \"Cascade deletes\"\n    }), \"\\n\", _jsx(\"video\", {\n      width: \"99%\",\n      autoPlay: true,\n      loop: true,\n      muted: true,\n      playsInline: true,\n      controls: true,\n      children: _jsx(\"source\", {\n        src: \"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/cascade-delete.mp4\",\n        type: \"video/mp4\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Cascade deletes are a core feature of Postgres, but have not been available in the Studio UI until now. This has been the #1 feature request from the community for a long time. You can now choose what you want to happen when deleting a referenced row in another table, right from Table Editor side panel.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"query-performance\",\n      children: \"Query Performance\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/lw7-studio/query-performance.png\",\n        alt: \"Logs UI\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We've just released a new Query Performance tool to help you identify slow queries. Using this tool, along with our new \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/platform/performance#examining-query-performance\",\n        children: \"guide\"\n      }), \" should help you speed things up. These tools can help you uncover where you might have inefficient queries or schemas, or where you might need indexes or even additional compute resources.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"foreign-key-selector\",\n      children: \"Foreign key selector\"\n    }), \"\\n\", _jsx(\"video\", {\n      width: \"99%\",\n      autoPlay: true,\n      loop: true,\n      muted: true,\n      playsInline: true,\n      controls: true,\n      children: _jsx(\"source\", {\n        src: \"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/fk-lookup.mp4\",\n        type: \"video/mp4\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You can now select a referencing row from another table, rather than having to pass a value manually.\\nYou can also quickly jump to the row being referenced in another table with the \", _jsx(_components.em, {\n        children: \"View referencing\"\n      }), \" record\\nbutton from the grid itself.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"postgres-roles\",\n      children: \"Postgres Roles\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/lw7-studio/roles.png\",\n        alt: \"Logs UI\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The Roles page got a huge revision, making it easy for you to manage access control through users, groups, and permissions. It's now easy to see how many connections you're using and where they're coming from.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"database-webhooks\",\n      children: \"Database Webhooks\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/lw7-studio/webhooks.png\",\n        alt: \"Logs UI\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We've wanted to improve the webhooks interface for a long time, and we've finally gotten to it. We now have full support for editing your webhooks, and you can now select an edge function to call instead of having to pass in a url. We're happy to roll this very common request out to the community.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"tableview-definitions\",\n      children: \"Table/View definitions\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/lw7-studio/definitions.png\",\n        alt: \"Logs UI\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You can now see the SQL definitions for your tables and views, directly from the Table Editor. This is a great way to see how Supabase is translating your UI changes into SQL. This can be useful for creating migrations, getting help, or just for learning more about how Supabase and SQL works.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"api-autodocs\",\n      children: \"API Autodocs\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/lw7-studio/api-autodocs.png\",\n        alt: \"Logs UI\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You can now view the auto-generated API docs right from the Table Editor. Grab the SQL and \", _jsx(_components.code, {\n        children: \"supabase-js\"\n      }), \" code for all of the CRUD operations, straight from the table. You can also quickly generate and download a Typescript types file for your table, right from the editor.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"quality-of-life-improvements\",\n      children: \"Quality of life improvements\"\n    }), \"\\n\", _jsx(_components.h4, {\n      id: \"support-for-many-tables\",\n      children: \"Support for many tables\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Previously, the Table Editor would get slow and unresponsive when you had many tables. We've made a number of improvements to make it much faster and more responsive. Feel free to make all the tables you need!\"\n    }), \"\\n\", _jsx(_components.h4, {\n      id: \"json-editing\",\n      children: \"JSON editing\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You've always been able to use the JSON data type in your tables, but editing the data wasn't easy. We've improved the inline Table Editor, and also now allow you open json cells from the side panel for a more spacious editing experience. Next Launch Week we're hoping to decide if it's pronounced “Jason” or “Jay-sawn”. Stay tuned.\"\n    }), \"\\n\", _jsx(_components.h4, {\n      id: \"nullable-columns\",\n      children: \"Nullable columns\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Speaking of extremely common feature requests, we've gotten this one a lot in the past few months. You used to have to handle this manually, but now you can now allow text/boolean cells to be null or empty. Supabase, where productivity is more than just an empty (or null) promise.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"wrapping-up\",\n      children: \"Wrapping Up\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We hope you get a lot of value out of these new features and enhancements. As we mentioned earlier, many of the features listed here came directly from \", _jsx(_components.a, {\n        href: \"https://github.com/orgs/supabase/discussions/categories/feature-requests\",\n        children: \"Feature Requests\"\n      }), \" on GitHub. Thanks to everyone who have taken the time to submit these, and encourage submissions for anything else you'd like to see.\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"Supabase AI — assisted database development","slug":"supabase-ai--assisted-database-development","lvl":2,"i":0,"seen":0},{"content":"GraphiQL","slug":"graphiql","lvl":2,"i":1,"seen":0},{"content":"Cascade deletes","slug":"cascade-deletes","lvl":2,"i":2,"seen":0},{"content":"Query Performance","slug":"query-performance","lvl":2,"i":3,"seen":0},{"content":"Foreign key selector","slug":"foreign-key-selector","lvl":2,"i":4,"seen":0},{"content":"Postgres Roles","slug":"postgres-roles","lvl":2,"i":5,"seen":0},{"content":"Database Webhooks","slug":"database-webhooks","lvl":2,"i":6,"seen":0},{"content":"Table/View definitions","slug":"tableview-definitions","lvl":2,"i":7,"seen":0},{"content":"API Autodocs","slug":"api-autodocs","lvl":2,"i":8,"seen":0},{"content":"Quality of life improvements","slug":"quality-of-life-improvements","lvl":3,"i":9,"seen":0},{"content":"Support for many tables","slug":"support-for-many-tables","lvl":4,"i":10,"seen":0},{"content":"JSON editing","slug":"json-editing","lvl":4,"i":11,"seen":0},{"content":"Nullable columns","slug":"nullable-columns","lvl":4,"i":12,"seen":0},{"content":"Wrapping Up","slug":"wrapping-up","lvl":2,"i":13,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,2],"level":0},{"type":"inline","content":"Today we're announcing Supabase Studio 2.0, packed with a ton of new stuff. We have features people have been asking for forever, and new capabilities that will change the way you work.","level":1,"lines":[1,2],"children":[{"type":"text","content":"Today we're announcing Supabase Studio 2.0, packed with a ton of new stuff. We have features people have been asking for forever, and new capabilities that will change the way you work.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[3,11],"level":0},{"type":"inline","content":"\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/0rcNqHt5KWU\"\n    title=\"YouTube video player\"\n    frameBorder=\"0\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"\n    allowfullscreen","level":1,"lines":[3,11],"children":[{"type":"text","content":"\u003cdiv className=\"video-container\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ciframe","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"w-full\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://www.youtube-nocookie.com/embed/0rcNqHt5KWU\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"title=\"YouTube video player\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"frameBorder=\"0\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allowfullscreen","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"blockquote_open","lines":[11,13],"level":0},{"type":"paragraph_open","tight":false,"lines":[11,13],"level":1},{"type":"inline","content":"\u003c/iframe\u003e\n\u003c/div\u003e","level":2,"lines":[11,13],"children":[{"type":"text","content":"\u003c/iframe\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"paragraph_open","tight":false,"lines":[14,15],"level":0},{"type":"inline","content":"Here's the birds-eye-view:","level":1,"lines":[14,15],"children":[{"type":"text","content":"Here's the birds-eye-view:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[16,37],"level":0},{"type":"inline","content":"\u003cul\u003e\n  \u003cli\u003e\n    [**Supabase AI**](#supabase-ai--assisted-database-development): ChatGPT, right in the Studio\n  \u003c/li\u003e\n  \u003cli\u003e[**GraphiQL**](#graphiql): Query your database with GraphQL\u003c/li\u003e\n  \u003cli\u003e[**Cascade deletes**](#cascade-deletes): Our #1 most requested feature from the community\u003c/li\u003e\n  \u003cli\u003e[**Query Performance**](#query-performance): Investigate slow running queries\u003c/li\u003e\n  \u003cli\u003e[**Foreign key Selector**](#foreign-key-selector): Look up a row in reference table\u003c/li\u003e\n  \u003cli\u003e[**Postgres Roles**](#postgres-roles): Manage your Postgres roles and privileges\u003c/li\u003e\n  \u003cli\u003e[**Database Webhooks**](#database-webhooks): Database triggers for Edge Functions\u003c/li\u003e\n  \u003cli\u003e\n    [**Table/View definitions**](#tableview-definitions): View SQL definitions for tables and views\n  \u003c/li\u003e\n  \u003cli\u003e[**API Autodocs**](#api-autodocs): View auto-generated docs right from the Table Editor\u003c/li\u003e\n  \u003cli\u003e\n    [**Support for many tables**](#support-for-many-tables): Table Editor now supports 1000s of\n    tables\n  \u003c/li\u003e\n  \u003cli\u003e[**JSON editing**](#json-editing): Improved JSON editing\u003c/li\u003e\n  \u003cli\u003e[**Nullable columns**](#nullable-columns): Allow text/bool cells to be null or empty\u003c/li\u003e\n\u003c/ul\u003e","level":1,"lines":[16,37],"children":[{"type":"text","content":"\u003cul\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cli\u003e","level":0},{"type":"softbreak","level":0},{"type":"link_open","href":"#supabase-ai--assisted-database-development","title":"","level":0},{"type":"strong_open","level":1},{"type":"text","content":"Supabase AI","level":2},{"type":"strong_close","level":1},{"type":"link_close","level":0},{"type":"text","content":": ChatGPT, right in the Studio","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/li\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cli\u003e","level":0},{"type":"link_open","href":"#graphiql","title":"","level":0},{"type":"strong_open","level":1},{"type":"text","content":"GraphiQL","level":2},{"type":"strong_close","level":1},{"type":"link_close","level":0},{"type":"text","content":": Query your database with GraphQL\u003c/li\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cli\u003e","level":0},{"type":"link_open","href":"#cascade-deletes","title":"","level":0},{"type":"strong_open","level":1},{"type":"text","content":"Cascade deletes","level":2},{"type":"strong_close","level":1},{"type":"link_close","level":0},{"type":"text","content":": Our #1 most requested feature from the community\u003c/li\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cli\u003e","level":0},{"type":"link_open","href":"#query-performance","title":"","level":0},{"type":"strong_open","level":1},{"type":"text","content":"Query Performance","level":2},{"type":"strong_close","level":1},{"type":"link_close","level":0},{"type":"text","content":": Investigate slow running queries\u003c/li\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cli\u003e","level":0},{"type":"link_open","href":"#foreign-key-selector","title":"","level":0},{"type":"strong_open","level":1},{"type":"text","content":"Foreign key Selector","level":2},{"type":"strong_close","level":1},{"type":"link_close","level":0},{"type":"text","content":": Look up a row in reference table\u003c/li\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cli\u003e","level":0},{"type":"link_open","href":"#postgres-roles","title":"","level":0},{"type":"strong_open","level":1},{"type":"text","content":"Postgres Roles","level":2},{"type":"strong_close","level":1},{"type":"link_close","level":0},{"type":"text","content":": Manage your Postgres roles and privileges\u003c/li\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cli\u003e","level":0},{"type":"link_open","href":"#database-webhooks","title":"","level":0},{"type":"strong_open","level":1},{"type":"text","content":"Database Webhooks","level":2},{"type":"strong_close","level":1},{"type":"link_close","level":0},{"type":"text","content":": Database triggers for Edge Functions\u003c/li\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cli\u003e","level":0},{"type":"softbreak","level":0},{"type":"link_open","href":"#tableview-definitions","title":"","level":0},{"type":"strong_open","level":1},{"type":"text","content":"Table/View definitions","level":2},{"type":"strong_close","level":1},{"type":"link_close","level":0},{"type":"text","content":": View SQL definitions for tables and views","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/li\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cli\u003e","level":0},{"type":"link_open","href":"#api-autodocs","title":"","level":0},{"type":"strong_open","level":1},{"type":"text","content":"API Autodocs","level":2},{"type":"strong_close","level":1},{"type":"link_close","level":0},{"type":"text","content":": View auto-generated docs right from the Table Editor\u003c/li\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cli\u003e","level":0},{"type":"softbreak","level":0},{"type":"link_open","href":"#support-for-many-tables","title":"","level":0},{"type":"strong_open","level":1},{"type":"text","content":"Support for many tables","level":2},{"type":"strong_close","level":1},{"type":"link_close","level":0},{"type":"text","content":": Table Editor now supports 1000s of","level":0},{"type":"softbreak","level":0},{"type":"text","content":"tables","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/li\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cli\u003e","level":0},{"type":"link_open","href":"#json-editing","title":"","level":0},{"type":"strong_open","level":1},{"type":"text","content":"JSON editing","level":2},{"type":"strong_close","level":1},{"type":"link_close","level":0},{"type":"text","content":": Improved JSON editing\u003c/li\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cli\u003e","level":0},{"type":"link_open","href":"#nullable-columns","title":"","level":0},{"type":"strong_open","level":1},{"type":"text","content":"Nullable columns","level":2},{"type":"strong_close","level":1},{"type":"link_close","level":0},{"type":"text","content":": Allow text/bool cells to be null or empty\u003c/li\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/ul\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[38,39],"level":0},{"type":"inline","content":"[Supabase AI — assisted database development](#supabase-ai--assisted-database-development)","level":1,"lines":[38,39],"children":[{"type":"text","content":"Supabase AI — assisted database development","level":0}],"lvl":2,"i":0,"seen":0,"slug":"supabase-ai--assisted-database-development"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[40,46],"level":0},{"type":"inline","content":"\u003cvideo width=\"99%\" autoPlay loop muted playsInline controls={true}\u003e\n  \u003csource\n    src=\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/cmdk.mp4\"\n    type=\"video/mp4\"\n  /\u003e\n\u003c/video\u003e","level":1,"lines":[40,46],"children":[{"type":"text","content":"\u003cvideo width=\"99%\" autoPlay loop muted playsInline controls={true}\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003csource","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/cmdk.mp4\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"type=\"video/mp4\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/video\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[47,48],"level":0},{"type":"inline","content":"One of our guiding principles at Supabase is to make SQL more accessible to developers. We don't want to just abstract it away with a custom implementation. By embracing SQL, developers learn important, transferable skills while they build on top of Supabase.","level":1,"lines":[47,48],"children":[{"type":"text","content":"One of our guiding principles at Supabase is to make SQL more accessible to developers. We don't want to just abstract it away with a custom implementation. By embracing SQL, developers learn important, transferable skills while they build on top of Supabase.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[49,50],"level":0},{"type":"inline","content":"In the past few months, AI advancements have made this easy! Writing a complex SQL query is now as simple as asking ChatGPT, and we're leaning into this approach with our Studio. AI gives developers superpowers, and by providing relevant context, Supabase gives AI superpowers.","level":1,"lines":[49,50],"children":[{"type":"text","content":"In the past few months, AI advancements have made this easy! Writing a complex SQL query is now as simple as asking ChatGPT, and we're leaning into this approach with our Studio. AI gives developers superpowers, and by providing relevant context, Supabase gives AI superpowers.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[51,52],"level":0},{"type":"inline","content":"AI has already changed a lot about the way we work as developers. We can use ChatGPT to write code for us as fast as we can prompt it. We wanted to bring this power into the Studio with integrations that will help you work even faster than before.","level":1,"lines":[51,52],"children":[{"type":"text","content":"AI has already changed a lot about the way we work as developers. We can use ChatGPT to write code for us as fast as we can prompt it. We wanted to bring this power into the Studio with integrations that will help you work even faster than before.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[53,54],"level":0},{"type":"inline","content":"Today you'll be able to do many common SQL operations with the help of AI. You can create tables, views and indexes, write functions and triggers, and more, right from the ⌘K menu.","level":1,"lines":[53,54],"children":[{"type":"text","content":"Today you'll be able to do many common SQL operations with the help of AI. You can create tables, views and indexes, write functions and triggers, and more, right from the ⌘K menu.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[55,56],"level":0},{"type":"inline","content":"Soon, we'll also let you opt-in to sending your table schemas to OpenAI to help fine tune queries to your project. All this should result in a dramatic boost in development speed. Maybe you'll only need half a weekend to scale to millions now!","level":1,"lines":[55,56],"children":[{"type":"text","content":"Soon, we'll also let you opt-in to sending your table schemas to OpenAI to help fine tune queries to your project. All this should result in a dramatic boost in development speed. Maybe you'll only need half a weekend to scale to millions now!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[57,58],"level":0},{"type":"inline","content":"As with every ⌘K menu, quick navigation is at the heart of things. You'll now be able to jump to any page in the Studio in a couple of keystrokes. You can also search the Docs directly from the menu.","level":1,"lines":[57,58],"children":[{"type":"text","content":"As with every ⌘K menu, quick navigation is at the heart of things. You'll now be able to jump to any page in the Studio in a couple of keystrokes. You can also search the Docs directly from the menu.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[59,60],"level":0},{"type":"inline","content":"We'll be actively working on our ⌘K menu in the coming months to make it faster and even more useful for you. Next, we're going to focus on better support for writing RLS policies. Stay tuned, and make sure to explore how we're integrating AI [behind the scenes](https://supabase.com/blog/tags/AI)!","level":1,"lines":[59,60],"children":[{"type":"text","content":"We'll be actively working on our ⌘K menu in the coming months to make it faster and even more useful for you. Next, we're going to focus on better support for writing RLS policies. Stay tuned, and make sure to explore how we're integrating AI ","level":0},{"type":"link_open","href":"https://supabase.com/blog/tags/AI","title":"","level":0},{"type":"text","content":"behind the scenes","level":1},{"type":"link_close","level":0},{"type":"text","content":"!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[61,62],"level":0},{"type":"inline","content":"Huge props for the amazing work done on the [⌘K package](https://cmdk.paco.me/) we've built on top of here.","level":1,"lines":[61,62],"children":[{"type":"text","content":"Huge props for the amazing work done on the ","level":0},{"type":"link_open","href":"https://cmdk.paco.me/","title":"","level":0},{"type":"text","content":"⌘K package","level":1},{"type":"link_close","level":0},{"type":"text","content":" we've built on top of here.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"hr","lines":[63,64],"level":0},{"type":"paragraph_open","tight":false,"lines":[65,66],"level":0},{"type":"inline","content":"Along with the new AI features, we also doubled-down on some of the critical missing pieces for a Postgres UI. Before we started to work on any of these, we combed through user feedback and tallied up the most submitted Feature Requests on GitHub.","level":1,"lines":[65,66],"children":[{"type":"text","content":"Along with the new AI features, we also doubled-down on some of the critical missing pieces for a Postgres UI. Before we started to work on any of these, we combed through user feedback and tallied up the most submitted Feature Requests on GitHub.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[67,68],"level":0},{"type":"inline","content":"\u003cdiv className=\"bg-gray-300 rounded-lg px-6 py-2 italic\"\u003e","level":1,"lines":[67,68],"children":[{"type":"text","content":"\u003cdiv className=\"bg-gray-300 rounded-lg px-6 py-2 italic\"\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[69,70],"level":0},{"type":"inline","content":"📢 Many of the features and enhancements below came from user requests — [please keep them coming](https://github.com/orgs/supabase/discussions/categories/feature-requests)!","level":1,"lines":[69,70],"children":[{"type":"text","content":"📢 Many of the features and enhancements below came from user requests — ","level":0},{"type":"link_open","href":"https://github.com/orgs/supabase/discussions/categories/feature-requests","title":"","level":0},{"type":"text","content":"please keep them coming","level":1},{"type":"link_close","level":0},{"type":"text","content":"!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[71,72],"level":0},{"type":"inline","content":"\u003c/div\u003e","level":1,"lines":[71,72],"children":[{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[73,74],"level":0},{"type":"inline","content":"[GraphiQL](#graphiql)","level":1,"lines":[73,74],"children":[{"type":"text","content":"GraphiQL","level":0}],"lvl":2,"i":1,"seen":0,"slug":"graphiql"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[75,76],"level":0},{"type":"inline","content":"![Logs UI](/images/blog/lw7-studio/graphiql.png)","level":1,"lines":[75,76],"children":[{"type":"image","src":"/images/blog/lw7-studio/graphiql.png","title":"","alt":"Logs UI","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[77,78],"level":0},{"type":"inline","content":"While GraphQL has been available in Supabase for just over a year, we haven't provided a visual tool for using it. You can now use the very popular GraphiQL directly from the Studio. GraphiQL is a browser tool for writing, validating, and testing GraphQL testing. Being able to use a fully integrated GraphQL IDE is a huge DX win.","level":1,"lines":[77,78],"children":[{"type":"text","content":"While GraphQL has been available in Supabase for just over a year, we haven't provided a visual tool for using it. You can now use the very popular GraphiQL directly from the Studio. GraphiQL is a browser tool for writing, validating, and testing GraphQL testing. Being able to use a fully integrated GraphQL IDE is a huge DX win.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[79,80],"level":0},{"type":"inline","content":"[Cascade deletes](#cascade-deletes)","level":1,"lines":[79,80],"children":[{"type":"text","content":"Cascade deletes","level":0}],"lvl":2,"i":2,"seen":0,"slug":"cascade-deletes"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[81,87],"level":0},{"type":"inline","content":"\u003cvideo width=\"99%\" autoPlay loop muted playsInline controls={true}\u003e\n  \u003csource\n    src=\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/cascade-delete.mp4\"\n    type=\"video/mp4\"\n  /\u003e\n\u003c/video\u003e","level":1,"lines":[81,87],"children":[{"type":"text","content":"\u003cvideo width=\"99%\" autoPlay loop muted playsInline controls={true}\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003csource","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/cascade-delete.mp4\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"type=\"video/mp4\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/video\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[88,89],"level":0},{"type":"inline","content":"Cascade deletes are a core feature of Postgres, but have not been available in the Studio UI until now. This has been the #1 feature request from the community for a long time. You can now choose what you want to happen when deleting a referenced row in another table, right from Table Editor side panel.","level":1,"lines":[88,89],"children":[{"type":"text","content":"Cascade deletes are a core feature of Postgres, but have not been available in the Studio UI until now. This has been the #1 feature request from the community for a long time. You can now choose what you want to happen when deleting a referenced row in another table, right from Table Editor side panel.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[90,91],"level":0},{"type":"inline","content":"[Query Performance](#query-performance)","level":1,"lines":[90,91],"children":[{"type":"text","content":"Query Performance","level":0}],"lvl":2,"i":3,"seen":0,"slug":"query-performance"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[92,93],"level":0},{"type":"inline","content":"![Logs UI](/images/blog/lw7-studio/query-performance.png)","level":1,"lines":[92,93],"children":[{"type":"image","src":"/images/blog/lw7-studio/query-performance.png","title":"","alt":"Logs UI","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[94,95],"level":0},{"type":"inline","content":"We've just released a new Query Performance tool to help you identify slow queries. Using this tool, along with our new [guide](https://supabase.com/docs/guides/platform/performance#examining-query-performance) should help you speed things up. These tools can help you uncover where you might have inefficient queries or schemas, or where you might need indexes or even additional compute resources.","level":1,"lines":[94,95],"children":[{"type":"text","content":"We've just released a new Query Performance tool to help you identify slow queries. Using this tool, along with our new ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/platform/performance#examining-query-performance","title":"","level":0},{"type":"text","content":"guide","level":1},{"type":"link_close","level":0},{"type":"text","content":" should help you speed things up. These tools can help you uncover where you might have inefficient queries or schemas, or where you might need indexes or even additional compute resources.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[96,97],"level":0},{"type":"inline","content":"[Foreign key selector](#foreign-key-selector)","level":1,"lines":[96,97],"children":[{"type":"text","content":"Foreign key selector","level":0}],"lvl":2,"i":4,"seen":0,"slug":"foreign-key-selector"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[98,104],"level":0},{"type":"inline","content":"\u003cvideo width=\"99%\" autoPlay loop muted playsInline controls={true}\u003e\n  \u003csource\n    src=\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/fk-lookup.mp4\"\n    type=\"video/mp4\"\n  /\u003e\n\u003c/video\u003e","level":1,"lines":[98,104],"children":[{"type":"text","content":"\u003cvideo width=\"99%\" autoPlay loop muted playsInline controls={true}\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003csource","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/fk-lookup.mp4\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"type=\"video/mp4\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/video\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[105,108],"level":0},{"type":"inline","content":"You can now select a referencing row from another table, rather than having to pass a value manually.\nYou can also quickly jump to the row being referenced in another table with the _View referencing_ record\nbutton from the grid itself.","level":1,"lines":[105,108],"children":[{"type":"text","content":"You can now select a referencing row from another table, rather than having to pass a value manually.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"You can also quickly jump to the row being referenced in another table with the ","level":0},{"type":"em_open","level":0},{"type":"text","content":"View referencing","level":1},{"type":"em_close","level":0},{"type":"text","content":" record","level":0},{"type":"softbreak","level":0},{"type":"text","content":"button from the grid itself.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[109,110],"level":0},{"type":"inline","content":"[Postgres Roles](#postgres-roles)","level":1,"lines":[109,110],"children":[{"type":"text","content":"Postgres Roles","level":0}],"lvl":2,"i":5,"seen":0,"slug":"postgres-roles"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[111,112],"level":0},{"type":"inline","content":"![Logs UI](/images/blog/lw7-studio/roles.png)","level":1,"lines":[111,112],"children":[{"type":"image","src":"/images/blog/lw7-studio/roles.png","title":"","alt":"Logs UI","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[113,114],"level":0},{"type":"inline","content":"The Roles page got a huge revision, making it easy for you to manage access control through users, groups, and permissions. It's now easy to see how many connections you're using and where they're coming from.","level":1,"lines":[113,114],"children":[{"type":"text","content":"The Roles page got a huge revision, making it easy for you to manage access control through users, groups, and permissions. It's now easy to see how many connections you're using and where they're coming from.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[115,116],"level":0},{"type":"inline","content":"[Database Webhooks](#database-webhooks)","level":1,"lines":[115,116],"children":[{"type":"text","content":"Database Webhooks","level":0}],"lvl":2,"i":6,"seen":0,"slug":"database-webhooks"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[117,118],"level":0},{"type":"inline","content":"![Logs UI](/images/blog/lw7-studio/webhooks.png)","level":1,"lines":[117,118],"children":[{"type":"image","src":"/images/blog/lw7-studio/webhooks.png","title":"","alt":"Logs UI","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[119,120],"level":0},{"type":"inline","content":"We've wanted to improve the webhooks interface for a long time, and we've finally gotten to it. We now have full support for editing your webhooks, and you can now select an edge function to call instead of having to pass in a url. We're happy to roll this very common request out to the community.","level":1,"lines":[119,120],"children":[{"type":"text","content":"We've wanted to improve the webhooks interface for a long time, and we've finally gotten to it. We now have full support for editing your webhooks, and you can now select an edge function to call instead of having to pass in a url. We're happy to roll this very common request out to the community.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[121,122],"level":0},{"type":"inline","content":"[Table/View definitions](#tableview-definitions)","level":1,"lines":[121,122],"children":[{"type":"text","content":"Table/View definitions","level":0}],"lvl":2,"i":7,"seen":0,"slug":"tableview-definitions"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[123,124],"level":0},{"type":"inline","content":"![Logs UI](/images/blog/lw7-studio/definitions.png)","level":1,"lines":[123,124],"children":[{"type":"image","src":"/images/blog/lw7-studio/definitions.png","title":"","alt":"Logs UI","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[125,126],"level":0},{"type":"inline","content":"You can now see the SQL definitions for your tables and views, directly from the Table Editor. This is a great way to see how Supabase is translating your UI changes into SQL. This can be useful for creating migrations, getting help, or just for learning more about how Supabase and SQL works.","level":1,"lines":[125,126],"children":[{"type":"text","content":"You can now see the SQL definitions for your tables and views, directly from the Table Editor. This is a great way to see how Supabase is translating your UI changes into SQL. This can be useful for creating migrations, getting help, or just for learning more about how Supabase and SQL works.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[127,128],"level":0},{"type":"inline","content":"[API Autodocs](#api-autodocs)","level":1,"lines":[127,128],"children":[{"type":"text","content":"API Autodocs","level":0}],"lvl":2,"i":8,"seen":0,"slug":"api-autodocs"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[129,130],"level":0},{"type":"inline","content":"![Logs UI](/images/blog/lw7-studio/api-autodocs.png)","level":1,"lines":[129,130],"children":[{"type":"image","src":"/images/blog/lw7-studio/api-autodocs.png","title":"","alt":"Logs UI","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[131,132],"level":0},{"type":"inline","content":"You can now view the auto-generated API docs right from the Table Editor. Grab the SQL and `supabase-js` code for all of the CRUD operations, straight from the table. You can also quickly generate and download a Typescript types file for your table, right from the editor.","level":1,"lines":[131,132],"children":[{"type":"text","content":"You can now view the auto-generated API docs right from the Table Editor. Grab the SQL and ","level":0},{"type":"code","content":"supabase-js","block":false,"level":0},{"type":"text","content":" code for all of the CRUD operations, straight from the table. You can also quickly generate and download a Typescript types file for your table, right from the editor.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[133,134],"level":0},{"type":"inline","content":"[Quality of life improvements](#quality-of-life-improvements)","level":1,"lines":[133,134],"children":[{"type":"text","content":"Quality of life improvements","level":0}],"lvl":3,"i":9,"seen":0,"slug":"quality-of-life-improvements"},{"type":"heading_close","hLevel":3,"level":0},{"type":"heading_open","hLevel":4,"lines":[135,136],"level":0},{"type":"inline","content":"[Support for many tables](#support-for-many-tables)","level":1,"lines":[135,136],"children":[{"type":"text","content":"Support for many tables","level":0}],"lvl":4,"i":10,"seen":0,"slug":"support-for-many-tables"},{"type":"heading_close","hLevel":4,"level":0},{"type":"paragraph_open","tight":false,"lines":[137,138],"level":0},{"type":"inline","content":"Previously, the Table Editor would get slow and unresponsive when you had many tables. We've made a number of improvements to make it much faster and more responsive. Feel free to make all the tables you need!","level":1,"lines":[137,138],"children":[{"type":"text","content":"Previously, the Table Editor would get slow and unresponsive when you had many tables. We've made a number of improvements to make it much faster and more responsive. Feel free to make all the tables you need!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":4,"lines":[139,140],"level":0},{"type":"inline","content":"[JSON editing](#json-editing)","level":1,"lines":[139,140],"children":[{"type":"text","content":"JSON editing","level":0}],"lvl":4,"i":11,"seen":0,"slug":"json-editing"},{"type":"heading_close","hLevel":4,"level":0},{"type":"paragraph_open","tight":false,"lines":[141,142],"level":0},{"type":"inline","content":"You've always been able to use the JSON data type in your tables, but editing the data wasn't easy. We've improved the inline Table Editor, and also now allow you open json cells from the side panel for a more spacious editing experience. Next Launch Week we're hoping to decide if it's pronounced “Jason” or “Jay-sawn”. Stay tuned.","level":1,"lines":[141,142],"children":[{"type":"text","content":"You've always been able to use the JSON data type in your tables, but editing the data wasn't easy. We've improved the inline Table Editor, and also now allow you open json cells from the side panel for a more spacious editing experience. Next Launch Week we're hoping to decide if it's pronounced “Jason” or “Jay-sawn”. Stay tuned.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":4,"lines":[143,144],"level":0},{"type":"inline","content":"[Nullable columns](#nullable-columns)","level":1,"lines":[143,144],"children":[{"type":"text","content":"Nullable columns","level":0}],"lvl":4,"i":12,"seen":0,"slug":"nullable-columns"},{"type":"heading_close","hLevel":4,"level":0},{"type":"paragraph_open","tight":false,"lines":[145,146],"level":0},{"type":"inline","content":"Speaking of extremely common feature requests, we've gotten this one a lot in the past few months. You used to have to handle this manually, but now you can now allow text/boolean cells to be null or empty. Supabase, where productivity is more than just an empty (or null) promise.","level":1,"lines":[145,146],"children":[{"type":"text","content":"Speaking of extremely common feature requests, we've gotten this one a lot in the past few months. You used to have to handle this manually, but now you can now allow text/boolean cells to be null or empty. Supabase, where productivity is more than just an empty (or null) promise.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[147,148],"level":0},{"type":"inline","content":"[Wrapping Up](#wrapping-up)","level":1,"lines":[147,148],"children":[{"type":"text","content":"Wrapping Up","level":0}],"lvl":2,"i":13,"seen":0,"slug":"wrapping-up"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[149,150],"level":0},{"type":"inline","content":"We hope you get a lot of value out of these new features and enhancements. As we mentioned earlier, many of the features listed here came directly from [Feature Requests](https://github.com/orgs/supabase/discussions/categories/feature-requests) on GitHub. Thanks to everyone who have taken the time to submit these, and encourage submissions for anything else you'd like to see.","level":1,"lines":[149,150],"children":[{"type":"text","content":"We hope you get a lot of value out of these new features and enhancements. As we mentioned earlier, many of the features listed here came directly from ","level":0},{"type":"link_open","href":"https://github.com/orgs/supabase/discussions/categories/feature-requests","title":"","level":0},{"type":"text","content":"Feature Requests","level":1},{"type":"link_close","level":0},{"type":"text","content":" on GitHub. Thanks to everyone who have taken the time to submit these, and encourage submissions for anything else you'd like to see.","level":0}]},{"type":"paragraph_close","tight":false,"level":0}],"content":"- [Supabase AI — assisted database development](#supabase-ai--assisted-database-development)\n- [GraphiQL](#graphiql)\n- [Cascade deletes](#cascade-deletes)\n- [Query Performance](#query-performance)\n- [Foreign key selector](#foreign-key-selector)\n- [Postgres Roles](#postgres-roles)\n- [Database Webhooks](#database-webhooks)\n- [Table/View definitions](#tableview-definitions)\n- [API Autodocs](#api-autodocs)\n  * [Quality of life improvements](#quality-of-life-improvements)\n- [Wrapping Up](#wrapping-up)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-studio-2.0"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>