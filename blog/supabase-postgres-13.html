<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase is now on Postgres 13.3</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="From today, new Supabase projects will be on a version of Supabase Postgres that runs on Postgres 13.3." data-next-head=""/><meta property="og:title" content="Supabase is now on Postgres 13.3" data-next-head=""/><meta property="og:description" content="From today, new Supabase projects will be on a version of Supabase Postgres that runs on Postgres 13.3." data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-postgres-13" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2021-07-26" data-next-head=""/><meta property="article:author" content="https://github.com/dragarcia" data-next-head=""/><meta property="article:tag" content="launch-week" data-next-head=""/><meta property="article:tag" content="database" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/pg13/postgres-13-og.jpg" data-next-head=""/><meta property="og:image:alt" content="Supabase is now on Postgres 13.3 thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Supabase is now on Postgres 13.3</h1><div class="text-light flex space-x-3 text-sm"><p>26 Jul 2021</p><p>•</p><p>5 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/dragarcia"><div class="flex items-center gap-3"><div class="w-10"><img alt="Angelico de los Reyes avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fdragarcia.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fdragarcia.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fdragarcia.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Angelico de los Reyes</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Supabase is now on Postgres 13.3" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fpg13%2Fpostgres-13-thumb.jpg&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fpg13%2Fpostgres-13-thumb.jpg&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fpg13%2Fpostgres-13-thumb.jpg&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fpg13%2Fpostgres-13-thumb.jpg&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fpg13%2Fpostgres-13-thumb.jpg&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fpg13%2Fpostgres-13-thumb.jpg&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fpg13%2Fpostgres-13-thumb.jpg&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fpg13%2Fpostgres-13-thumb.jpg&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fpg13%2Fpostgres-13-thumb.jpg&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fpg13%2Fpostgres-13-thumb.jpg&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fpg13%2Fpostgres-13-thumb.jpg&amp;w=3840&amp;q=100"/></div><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p>From today, new Supabase projects will be on a version of <a href="https://github.com/supabase/postgres">Supabase Postgres</a> that runs
on <a href="https://www.postgresql.org/about/news/postgresql-13-released-2077/">Postgres 13.3</a>. This won&#x27;t be the only big change however in this version.
Here are a few other things that have changed under the hood.</p>
<h2 id="postgresql-version-133" class="group scroll-mt-24">PostgreSQL version 13.3<a href="#postgresql-version-133" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We&#x27;ve jumped from PostgreSQL 12.0 to PostgreSQL <a href="https://www.postgresql.org/docs/13/release-13-3.html">version 13.3</a>, introducing
significant performance improvements and some great new functionality. Some changes include:</p>
<ul>
<li>native <a href="https://www.postgresql.org/docs/13/functions-uuid.html">UUID generation</a> (!) using <code class="short-inline-codeblock">gen_random_uuid</code></li>
<li>a new JSONB <a href="https://www.postgresql.org/docs/13/functions-json.html">datetime</a> function</li>
<li>vacuuming indexes in <a href="https://www.postgresql.org/docs/13/sql-vacuum.html">parallel</a></li>
<li><a href="https://www.postgresql.org/docs/13/using-explain.html#USING-EXPLAIN-BASICS">incremental sorting</a></li>
<li>smaller <a href="https://www.postgresql.org/docs/13/btree-implementation.html#BTREE-DEDUPLICATION">btree indexes</a></li>
<li>improvements to <a href="https://www.postgresql.org/docs/13/planner-stats.html#PLANNER-STATS-EXTENDED">extended statistics</a></li>
</ul>
<h2 id="supabase-versioning" class="group scroll-mt-24">Supabase Versioning<a href="#supabase-versioning" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Our <a href="https://github.com/supabase/postgres">Postgres repo</a> has jumped from <code class="short-inline-codeblock">0.15.0</code> to <code class="short-inline-codeblock">13.3.0</code>. From now on, both major and minor versions of
Supabase Postgres will follow PostgreSQL. This makes it much easier to ascertain what version of PostgreSQL is installed. In the situation wherein
there are no updates to PostgreSQL in between releases, the patch version will be bumped up.</p>
<h2 id="large-system-extensions-lse-enabled-for-arm-instances" class="group scroll-mt-24">Large System Extensions (LSE) enabled for ARM instances<a href="#large-system-extensions-lse-enabled-for-arm-instances" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<small><p><em>Disclaimer for self-hosting: This is not available for x86 instances. All instances on the
Supabase platform have this enabled by default.</em></p></small>
<p>The recent wave of Graviton 2 instances from AWS introduces support for the Large System Extensions (LSE). This looks to greatly enhance
application performance through atomics, and
<a href="https://github.com/aws/aws-graviton-getting-started#building-for-graviton-and-graviton2">improves locking and synchronization performance across large systems</a>.</p>
<h3 id="preliminary-benchmarks" class="group scroll-mt-24">Preliminary Benchmarks<a href="#preliminary-benchmarks" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Below is a comparison between the ARM versions of Supabase Postgres <code class="short-inline-codeblock">0.15.0</code> and <code class="short-inline-codeblock">13.3.0</code>. Both are using <code class="short-inline-codeblock">m6gd.8xlarge</code> instances
and follow the PostgreSQL configuration <a href="https://www.percona.com/blog/2021/01/22/postgresql-on-arm-based-aws-ec2-instances-is-it-any-good/">here</a>.
The following configuration of <code class="short-inline-codeblock">pgbench</code> was used.</p>
<div class="ch-codegroup not-prose "><div class="ch-editor-frame"><div class="ch-frame-title-bar"><div class="ch-frame-buttons"><div class="ch-frame-button ch-frame-button-left"></div><div class="ch-frame-button-space"></div><div class="ch-frame-button ch-frame-button-middle"></div><div class="ch-frame-button-space"></div><div class="ch-frame-button ch-frame-button-right"></div></div><div title="hideCopy" data-ch-tab="north" data-active="true" class="ch-editor-tab"><div><span style="opacity:0.5"></span>hideCopy</div></div><div style="flex:1;min-width:0.8em"></div><button type="button" title="Copy code" class="ch-editor-button"><svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.6px" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg></button></div><div data-ch-panel="north" style="flex-grow:1;overflow:hidden"><div style="height:100%" class="ch-code-wrapper " data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>pgbench -i -s 150</span></div></div><br/></code></div></div></div></div>
<p>Running the benchmark with 2, 4, 8, 16, 64, and 128 clients:</p>
<div class="ch-codegroup not-prose "><div class="ch-editor-frame"><div class="ch-frame-title-bar"><div class="ch-frame-buttons"><div class="ch-frame-button ch-frame-button-left"></div><div class="ch-frame-button-space"></div><div class="ch-frame-button ch-frame-button-middle"></div><div class="ch-frame-button-space"></div><div class="ch-frame-button ch-frame-button-right"></div></div><div title="hideCopy" data-ch-tab="north" data-active="true" class="ch-editor-tab"><div><span style="opacity:0.5"></span>hideCopy</div></div><div style="flex:1;min-width:0.8em"></div><button type="button" title="Copy code" class="ch-editor-button"><svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.6px" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg></button></div><div data-ch-panel="north" style="flex-grow:1;overflow:hidden"><div style="height:100%" class="ch-code-wrapper " data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>pgbench -P 5 -c {num_clients} -j {num_clients} -T 300 -M prepared postgres</span></div></div><br/></code></div></div></div></div>
<p></p>
<h2 id="ubuntu-2004" class="group scroll-mt-24">Ubuntu 20.04<a href="#ubuntu-2004" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We have taken the opportunity to upgrade new projects from Ubuntu 18.04 to Ubuntu 20.04. A switch to Ubuntu 20.04 guarantees that the underlying
OS of Supabase Postgres is supported by the Canonical team up until the year 2025 (2023 for Ubuntu 18.04).</p>
<h2 id="built-from-source" class="group scroll-mt-24">Built from source<a href="#built-from-source" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Driven by the need to enable LSE, the underlying PostgreSQL in this version was built from the ground up instead of downloaded binaries.
Supabase Postgres can now be easily upgraded whenever a new major or minor version of PostgreSQL is released. When PostgreSQL 14 comes out,
expect Supabase Postgres 14 to quickly follow.</p>
<h2 id="new-extensions" class="group scroll-mt-24">New Extensions<a href="#new-extensions" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<h3 id="pgrouting" class="group scroll-mt-24"><code class="short-inline-codeblock">pgRouting</code><a href="#pgrouting" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>An extension of PostGIS, <a href="https://pgrouting.org/"><code class="short-inline-codeblock">pgRouting</code></a> provides geospatial routing functionality.</p>
<p>More information can be found <a href="https://docs.pgrouting.org/latest/en/index.html">here</a>.</p>
<h3 id="wal2json" class="group scroll-mt-24"><code class="short-inline-codeblock">wal2json</code><a href="#wal2json" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Converts WAL output into clean and organized JSON objects.</p>
<p>More information can be found <a href="https://github.com/eulerto/wal2json">here</a>.</p>
<h2 id="enhanced-security" class="group scroll-mt-24">Enhanced security<a href="#enhanced-security" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>To help combat brute force attacks, <code class="short-inline-codeblock">fail2ban</code> has now been configured to protect direct connections to Postgres. This applies to both
ports <code class="short-inline-codeblock">5432</code> (PostgreSQL) and <code class="short-inline-codeblock">6543</code> (PgBouncer).</p>
<p>IPs get banned for 10 minutes after three failed attempts, and we&#x27;ll continue to fine-tune and improve the protections applied to the
database servers based on evolving traffic patterns.</p>
<h2 id="postgresql-bundles" class="group scroll-mt-24">PostgreSQL bundles<a href="#postgresql-bundles" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p><code class="short-inline-codeblock">[Coming Soon]</code></p>
<p>Last but not the least, we&#x27;re diversifying the images of Supabase Postgres available in the AWS and Digital Ocean Marketplaces.</p>
<p>Instead of a single option, we&#x27;ll soon offer four configurations of PostgreSQL. Each bundle offers functionality for
common use-cases. For example, if you&#x27;re using Postgres with Serverless functions, you might want to run the PgBouncer bundle. If you want an
HTTP API with your Postgres offering you might want to run the PostgREST bundle.</p>
<table><thead><tr><th><code class="short-inline-codeblock">Name</code></th><th><code class="short-inline-codeblock">PostgreSQL</code></th><th><code class="short-inline-codeblock">PgBouncer</code></th><th><code class="short-inline-codeblock">PostgREST</code></th></tr></thead><tbody><tr><td>Supabase Postgres</td><td>✅</td><td></td><td></td></tr><tr><td>Supabase Postgres: PgBouncer Bundle</td><td>✅</td><td>✅</td><td></td></tr><tr><td>Supabase Postgres: PostgREST Bundle</td><td>✅</td><td></td><td>✅</td></tr><tr><td>Supabase Postgres: Complete Bundle</td><td>✅</td><td>✅</td><td>✅</td></tr></tbody></table>
<p>Each offering will be available for both the <code class="short-inline-codeblock">x86</code> and <code class="short-inline-codeblock">arm</code> architectures.</p>
<h2 id="try-postgresql-13" class="group scroll-mt-24">Try PostgreSQL 13<a href="#try-postgresql-13" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Try the new features of PostgreSQL 13.3 today by creating a <a href="https://supabase.com/dashboard/">new project</a> on Supabase.</p>
<h2 id="more-postgres-resources" class="group scroll-mt-24">More Postgres resources<a href="#more-postgres-resources" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ul>
<li><a href="realtime-row-level-security-in-postgresql.html">Realtime Postgres RLS on Supabase</a></li>
<li><a href="seen-by-in-postgresql.html">Implementing &quot;seen by&quot; functionality with Postgres</a></li>
<li><a href="partial-postgresql-data-dumps-with-rls.html">Partial data dumps using Postgres Row Level Security</a></li>
<li><a href="https://supabase.com/blog/postgresql-views">Postgres Views</a></li>
<li><a href="postgres-audit.html">Postgres Auditing in 150 lines of SQL</a></li>
<li><a href="https://supabase.com/blog/cracking-postgres-interview">Cracking PostgreSQL Interview Questions</a></li>
<li><a href="https://supabase.com/blog/postgresql-templates">What are PostgreSQL Templates?</a></li>
</ul></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-postgres-13&amp;text=Supabase%20is%20now%20on%20Postgres%2013.3"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-postgres-13&amp;text=Supabase%20is%20now%20on%20Postgres%2013.3"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-postgres-13&amp;t=Supabase%20is%20now%20on%20Postgres%2013.3"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="supabase-community-day.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Supabase Community Day</h4><p class="small">26 July 2021</p></div></div></div></div></a></div><div><a href="supabase-launch-week-sql.html"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Supabase Launch Week II: The SQL</h4><p class="small">22 July 2021</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/launch-week"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">launch-week</div></a><a href="https://supabase.com/blog/tags/database"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">database</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#postgresql-version-133">PostgreSQL version 13.3</a></li>
<li><a href="#supabase-versioning">Supabase Versioning</a></li>
<li><a href="#large-system-extensions-lse-enabled-for-arm-instances">Large System Extensions (LSE) enabled for ARM instances</a></li>
<li><a href="#ubuntu-2004">Ubuntu 20.04</a></li>
<li><a href="#built-from-source">Built from source</a></li>
<li><a href="#new-extensions">New Extensions</a></li>
<li><a href="#enhanced-security">Enhanced security</a></li>
<li><a href="#postgresql-bundles">PostgreSQL bundles</a></li>
<li><a href="#try-postgresql-13">Try PostgreSQL 13</a></li>
<li><a href="#more-postgres-resources">More Postgres resources</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-postgres-13&amp;text=Supabase%20is%20now%20on%20Postgres%2013.3"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-postgres-13&amp;text=Supabase%20is%20now%20on%20Postgres%2013.3"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-postgres-13&amp;t=Supabase%20is%20now%20on%20Postgres%2013.3"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"supabase-community-day","title":"Supabase Community Day","description":"Community Day","author":"steve_chavez","author_url":"https://github.com/steve-chavez","author_image_url":"https://github.com/steve-chavez.png","thumb":"launch-week-sql-day-1-community-day/launch-week-sql-day-1-community-day-thumb.jpg","image":"launch-week-sql-day-1-community-day/launch-week-sql-day-1-community-day-og.jpg","categories":["developers"],"tags":["launch-week"],"date":"2021-07-26","toc_depth":2,"formattedDate":"26 July 2021","readingTime":"5 minute read","url":"/blog/supabase-community-day","path":"/blog/supabase-community-day"},"nextPost":{"slug":"supabase-launch-week-sql","title":"Supabase Launch Week II: The SQL","description":"Five days of Supabase. Again.","author":"paul_copplestone","author_url":"https://github.com/kiwicopple","author_image_url":"https://github.com/kiwicopple.png","image":"launch-week-sql/launchweek-2-the-sql-og.jpg","thumb":"launch-week-sql/supabase-launch-the-sql.png","categories":["product"],"tags":["launch-week"],"date":"2021-07-22","toc_depth":2,"formattedDate":"22 July 2021","readingTime":"3 minute read","url":"/blog/supabase-launch-week-sql","path":"/blog/supabase-launch-week-sql"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-postgres-13","source":"\nFrom today, new Supabase projects will be on a version of [Supabase Postgres](https://github.com/supabase/postgres) that runs\non [Postgres 13.3](https://www.postgresql.org/about/news/postgresql-13-released-2077/). This won't be the only big change however in this version.\nHere are a few other things that have changed under the hood.\n\n## PostgreSQL version 13.3\n\nWe've jumped from PostgreSQL 12.0 to PostgreSQL [version 13.3](https://www.postgresql.org/docs/13/release-13-3.html), introducing\nsignificant performance improvements and some great new functionality. Some changes include:\n\n- native [UUID generation](https://www.postgresql.org/docs/13/functions-uuid.html) (!) using `gen_random_uuid`\n- a new JSONB [datetime](https://www.postgresql.org/docs/13/functions-json.html) function\n- vacuuming indexes in [parallel](https://www.postgresql.org/docs/13/sql-vacuum.html)\n- [incremental sorting](https://www.postgresql.org/docs/13/using-explain.html#USING-EXPLAIN-BASICS)\n- smaller [btree indexes](https://www.postgresql.org/docs/13/btree-implementation.html#BTREE-DEDUPLICATION)\n- improvements to [extended statistics](https://www.postgresql.org/docs/13/planner-stats.html#PLANNER-STATS-EXTENDED)\n\n## Supabase Versioning\n\nOur [Postgres repo](https://github.com/supabase/postgres) has jumped from `0.15.0` to `13.3.0`. From now on, both major and minor versions of\nSupabase Postgres will follow PostgreSQL. This makes it much easier to ascertain what version of PostgreSQL is installed. In the situation wherein\nthere are no updates to PostgreSQL in between releases, the patch version will be bumped up.\n\n## Large System Extensions (LSE) enabled for ARM instances\n\n\u003csmall\u003e\n  *Disclaimer for self-hosting: This is not available for x86 instances. All instances on the\n  Supabase platform have this enabled by default.*\n\u003c/small\u003e\n\nThe recent wave of Graviton 2 instances from AWS introduces support for the Large System Extensions (LSE). This looks to greatly enhance\napplication performance through atomics, and\n[improves locking and synchronization performance across large systems](https://github.com/aws/aws-graviton-getting-started#building-for-graviton-and-graviton2).\n\n### Preliminary Benchmarks\n\nBelow is a comparison between the ARM versions of Supabase Postgres `0.15.0` and `13.3.0`. Both are using `m6gd.8xlarge` instances\nand follow the PostgreSQL configuration [here](https://www.percona.com/blog/2021/01/22/postgresql-on-arm-based-aws-ec2-instances-is-it-any-good/).\nThe following configuration of `pgbench` was used.\n\n```bash hideCopy\npgbench -i -s 150\n```\n\nRunning the benchmark with 2, 4, 8, 16, 64, and 128 clients:\n\n```bash hideCopy\npgbench -P 5 -c {num_clients} -j {num_clients} -T 300 -M prepared postgres\n```\n\n![PostgreSQL 13.3 performance](/images/blog/pg13/postgres-13-performance.png)\n\n## Ubuntu 20.04\n\nWe have taken the opportunity to upgrade new projects from Ubuntu 18.04 to Ubuntu 20.04. A switch to Ubuntu 20.04 guarantees that the underlying\nOS of Supabase Postgres is supported by the Canonical team up until the year 2025 (2023 for Ubuntu 18.04).\n\n## Built from source\n\nDriven by the need to enable LSE, the underlying PostgreSQL in this version was built from the ground up instead of downloaded binaries.\nSupabase Postgres can now be easily upgraded whenever a new major or minor version of PostgreSQL is released. When PostgreSQL 14 comes out,\nexpect Supabase Postgres 14 to quickly follow.\n\n## New Extensions\n\n### `pgRouting`\n\nAn extension of PostGIS, [`pgRouting`](https://pgrouting.org/) provides geospatial routing functionality.\n\nMore information can be found [here](https://docs.pgrouting.org/latest/en/index.html).\n\n### `wal2json`\n\nConverts WAL output into clean and organized JSON objects.\n\nMore information can be found [here](https://github.com/eulerto/wal2json).\n\n## Enhanced security\n\nTo help combat brute force attacks, `fail2ban` has now been configured to protect direct connections to Postgres. This applies to both\nports `5432` (PostgreSQL) and `6543` (PgBouncer).\n\nIPs get banned for 10 minutes after three failed attempts, and we'll continue to fine-tune and improve the protections applied to the\ndatabase servers based on evolving traffic patterns.\n\n## PostgreSQL bundles\n\n`[Coming Soon]`\n\nLast but not the least, we're diversifying the images of Supabase Postgres available in the AWS and Digital Ocean Marketplaces.\n\nInstead of a single option, we'll soon offer four configurations of PostgreSQL. Each bundle offers functionality for\ncommon use-cases. For example, if you're using Postgres with Serverless functions, you might want to run the PgBouncer bundle. If you want an\nHTTP API with your Postgres offering you might want to run the PostgREST bundle.\n\n| `Name`                              | `PostgreSQL` | `PgBouncer` | `PostgREST` |\n| ----------------------------------- | ------------ | ----------- | ----------- |\n| Supabase Postgres                   | ✅           |             |             |\n| Supabase Postgres: PgBouncer Bundle | ✅           | ✅          |             |\n| Supabase Postgres: PostgREST Bundle | ✅           |             | ✅          |\n| Supabase Postgres: Complete Bundle  | ✅           | ✅          | ✅          |\n\nEach offering will be available for both the `x86` and `arm` architectures.\n\n## Try PostgreSQL 13\n\nTry the new features of PostgreSQL 13.3 today by creating a [new project](https://supabase.com/dashboard/) on Supabase.\n\n## More Postgres resources\n\n- [Realtime Postgres RLS on Supabase](https://supabase.com//blog/realtime-row-level-security-in-postgresql)\n- [Implementing \"seen by\" functionality with Postgres](https://supabase.com/blog/seen-by-in-postgresql)\n- [Partial data dumps using Postgres Row Level Security](https://supabase.com/blog/partial-postgresql-data-dumps-with-rls)\n- [Postgres Views](https://supabase.com/blog/postgresql-views)\n- [Postgres Auditing in 150 lines of SQL](https://supabase.com/blog/postgres-audit)\n- [Cracking PostgreSQL Interview Questions](https://supabase.com/blog/cracking-postgres-interview)\n- [What are PostgreSQL Templates?](https://supabase.com/blog/postgresql-templates)\n","title":"Supabase is now on Postgres 13.3","description":"From today, new Supabase projects will be on a version of Supabase Postgres that runs on Postgres 13.3.","author":"angelico_de_los_reyes","author_url":"https://github.com/dragarcia","author_image_url":"https://github.com/dragarcia.png","image":"pg13/postgres-13-og.jpg","thumb":"pg13/postgres-13-thumb.jpg","categories":["postgres"],"tags":["launch-week","database"],"date":"2021-07-26","toc_depth":2,"content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    a: \"a\",\n    h2: \"h2\",\n    ul: \"ul\",\n    li: \"li\",\n    code: \"code\",\n    em: \"em\",\n    h3: \"h3\",\n    img: \"img\",\n    table: \"table\",\n    thead: \"thead\",\n    tr: \"tr\",\n    th: \"th\",\n    tbody: \"tbody\",\n    td: \"td\"\n  }, _provideComponents(), props.components), {CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"From today, new Supabase projects will be on a version of \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/postgres\",\n        children: \"Supabase Postgres\"\n      }), \" that runs\\non \", _jsx(_components.a, {\n        href: \"https://www.postgresql.org/about/news/postgresql-13-released-2077/\",\n        children: \"Postgres 13.3\"\n      }), \". This won't be the only big change however in this version.\\nHere are a few other things that have changed under the hood.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"postgresql-version-133\",\n      children: \"PostgreSQL version 13.3\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We've jumped from PostgreSQL 12.0 to PostgreSQL \", _jsx(_components.a, {\n        href: \"https://www.postgresql.org/docs/13/release-13-3.html\",\n        children: \"version 13.3\"\n      }), \", introducing\\nsignificant performance improvements and some great new functionality. Some changes include:\"]\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"native \", _jsx(_components.a, {\n          href: \"https://www.postgresql.org/docs/13/functions-uuid.html\",\n          children: \"UUID generation\"\n        }), \" (!) using \", _jsx(_components.code, {\n          children: \"gen_random_uuid\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"a new JSONB \", _jsx(_components.a, {\n          href: \"https://www.postgresql.org/docs/13/functions-json.html\",\n          children: \"datetime\"\n        }), \" function\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"vacuuming indexes in \", _jsx(_components.a, {\n          href: \"https://www.postgresql.org/docs/13/sql-vacuum.html\",\n          children: \"parallel\"\n        })]\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://www.postgresql.org/docs/13/using-explain.html#USING-EXPLAIN-BASICS\",\n          children: \"incremental sorting\"\n        })\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"smaller \", _jsx(_components.a, {\n          href: \"https://www.postgresql.org/docs/13/btree-implementation.html#BTREE-DEDUPLICATION\",\n          children: \"btree indexes\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"improvements to \", _jsx(_components.a, {\n          href: \"https://www.postgresql.org/docs/13/planner-stats.html#PLANNER-STATS-EXTENDED\",\n          children: \"extended statistics\"\n        })]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"supabase-versioning\",\n      children: \"Supabase Versioning\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Our \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/postgres\",\n        children: \"Postgres repo\"\n      }), \" has jumped from \", _jsx(_components.code, {\n        children: \"0.15.0\"\n      }), \" to \", _jsx(_components.code, {\n        children: \"13.3.0\"\n      }), \". From now on, both major and minor versions of\\nSupabase Postgres will follow PostgreSQL. This makes it much easier to ascertain what version of PostgreSQL is installed. In the situation wherein\\nthere are no updates to PostgreSQL in between releases, the patch version will be bumped up.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"large-system-extensions-lse-enabled-for-arm-instances\",\n      children: \"Large System Extensions (LSE) enabled for ARM instances\"\n    }), \"\\n\", _jsx(\"small\", {\n      children: _jsx(_components.p, {\n        children: _jsx(_components.em, {\n          children: \"Disclaimer for self-hosting: This is not available for x86 instances. All instances on the\\nSupabase platform have this enabled by default.\"\n        })\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The recent wave of Graviton 2 instances from AWS introduces support for the Large System Extensions (LSE). This looks to greatly enhance\\napplication performance through atomics, and\\n\", _jsx(_components.a, {\n        href: \"https://github.com/aws/aws-graviton-getting-started#building-for-graviton-and-graviton2\",\n        children: \"improves locking and synchronization performance across large systems\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"preliminary-benchmarks\",\n      children: \"Preliminary Benchmarks\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Below is a comparison between the ARM versions of Supabase Postgres \", _jsx(_components.code, {\n        children: \"0.15.0\"\n      }), \" and \", _jsx(_components.code, {\n        children: \"13.3.0\"\n      }), \". Both are using \", _jsx(_components.code, {\n        children: \"m6gd.8xlarge\"\n      }), \" instances\\nand follow the PostgreSQL configuration \", _jsx(_components.a, {\n        href: \"https://www.percona.com/blog/2021/01/22/postgresql-on-arm-based-aws-ec2-instances-is-it-any-good/\",\n        children: \"here\"\n      }), \".\\nThe following configuration of \", _jsx(_components.code, {\n        children: \"pgbench\"\n      }), \" was used.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"hideCopy\"],\n        \"active\": \"hideCopy\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"hideCopy\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"pgbench \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"-i -s 150\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Running the benchmark with 2, 4, 8, 16, 64, and 128 clients:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"hideCopy\"],\n        \"active\": \"hideCopy\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"hideCopy\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"pgbench \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"-P 5 -c \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"{num_clients} \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"-j \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"{num_clients} \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"-T 300 -M \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"prepared postgres\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/pg13/postgres-13-performance.png\",\n        alt: \"PostgreSQL 13.3 performance\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"ubuntu-2004\",\n      children: \"Ubuntu 20.04\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We have taken the opportunity to upgrade new projects from Ubuntu 18.04 to Ubuntu 20.04. A switch to Ubuntu 20.04 guarantees that the underlying\\nOS of Supabase Postgres is supported by the Canonical team up until the year 2025 (2023 for Ubuntu 18.04).\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"built-from-source\",\n      children: \"Built from source\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Driven by the need to enable LSE, the underlying PostgreSQL in this version was built from the ground up instead of downloaded binaries.\\nSupabase Postgres can now be easily upgraded whenever a new major or minor version of PostgreSQL is released. When PostgreSQL 14 comes out,\\nexpect Supabase Postgres 14 to quickly follow.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"new-extensions\",\n      children: \"New Extensions\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"pgrouting\",\n      children: _jsx(_components.code, {\n        children: \"pgRouting\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"An extension of PostGIS, \", _jsx(_components.a, {\n        href: \"https://pgrouting.org/\",\n        children: _jsx(_components.code, {\n          children: \"pgRouting\"\n        })\n      }), \" provides geospatial routing functionality.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"More information can be found \", _jsx(_components.a, {\n        href: \"https://docs.pgrouting.org/latest/en/index.html\",\n        children: \"here\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"wal2json\",\n      children: _jsx(_components.code, {\n        children: \"wal2json\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Converts WAL output into clean and organized JSON objects.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"More information can be found \", _jsx(_components.a, {\n        href: \"https://github.com/eulerto/wal2json\",\n        children: \"here\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"enhanced-security\",\n      children: \"Enhanced security\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To help combat brute force attacks, \", _jsx(_components.code, {\n        children: \"fail2ban\"\n      }), \" has now been configured to protect direct connections to Postgres. This applies to both\\nports \", _jsx(_components.code, {\n        children: \"5432\"\n      }), \" (PostgreSQL) and \", _jsx(_components.code, {\n        children: \"6543\"\n      }), \" (PgBouncer).\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"IPs get banned for 10 minutes after three failed attempts, and we'll continue to fine-tune and improve the protections applied to the\\ndatabase servers based on evolving traffic patterns.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"postgresql-bundles\",\n      children: \"PostgreSQL bundles\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.code, {\n        children: \"[Coming Soon]\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Last but not the least, we're diversifying the images of Supabase Postgres available in the AWS and Digital Ocean Marketplaces.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Instead of a single option, we'll soon offer four configurations of PostgreSQL. Each bundle offers functionality for\\ncommon use-cases. For example, if you're using Postgres with Serverless functions, you might want to run the PgBouncer bundle. If you want an\\nHTTP API with your Postgres offering you might want to run the PostgREST bundle.\"\n    }), \"\\n\", _jsxs(_components.table, {\n      children: [_jsx(_components.thead, {\n        children: _jsxs(_components.tr, {\n          children: [_jsx(_components.th, {\n            children: _jsx(_components.code, {\n              children: \"Name\"\n            })\n          }), _jsx(_components.th, {\n            children: _jsx(_components.code, {\n              children: \"PostgreSQL\"\n            })\n          }), _jsx(_components.th, {\n            children: _jsx(_components.code, {\n              children: \"PgBouncer\"\n            })\n          }), _jsx(_components.th, {\n            children: _jsx(_components.code, {\n              children: \"PostgREST\"\n            })\n          })]\n        })\n      }), _jsxs(_components.tbody, {\n        children: [_jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"Supabase Postgres\"\n          }), _jsx(_components.td, {\n            children: \"✅\"\n          }), _jsx(_components.td, {}), _jsx(_components.td, {})]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"Supabase Postgres: PgBouncer Bundle\"\n          }), _jsx(_components.td, {\n            children: \"✅\"\n          }), _jsx(_components.td, {\n            children: \"✅\"\n          }), _jsx(_components.td, {})]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"Supabase Postgres: PostgREST Bundle\"\n          }), _jsx(_components.td, {\n            children: \"✅\"\n          }), _jsx(_components.td, {}), _jsx(_components.td, {\n            children: \"✅\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"Supabase Postgres: Complete Bundle\"\n          }), _jsx(_components.td, {\n            children: \"✅\"\n          }), _jsx(_components.td, {\n            children: \"✅\"\n          }), _jsx(_components.td, {\n            children: \"✅\"\n          })]\n        })]\n      })]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Each offering will be available for both the \", _jsx(_components.code, {\n        children: \"x86\"\n      }), \" and \", _jsx(_components.code, {\n        children: \"arm\"\n      }), \" architectures.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"try-postgresql-13\",\n      children: \"Try PostgreSQL 13\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Try the new features of PostgreSQL 13.3 today by creating a \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard/\",\n        children: \"new project\"\n      }), \" on Supabase.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"more-postgres-resources\",\n      children: \"More Postgres resources\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com//blog/realtime-row-level-security-in-postgresql\",\n          children: \"Realtime Postgres RLS on Supabase\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/blog/seen-by-in-postgresql\",\n          children: \"Implementing \\\"seen by\\\" functionality with Postgres\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/blog/partial-postgresql-data-dumps-with-rls\",\n          children: \"Partial data dumps using Postgres Row Level Security\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/blog/postgresql-views\",\n          children: \"Postgres Views\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/blog/postgres-audit\",\n          children: \"Postgres Auditing in 150 lines of SQL\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/blog/cracking-postgres-interview\",\n          children: \"Cracking PostgreSQL Interview Questions\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/blog/postgresql-templates\",\n          children: \"What are PostgreSQL Templates?\"\n        })\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"PostgreSQL version 13.3","slug":"postgresql-version-133","lvl":2,"i":0,"seen":0},{"content":"Supabase Versioning","slug":"supabase-versioning","lvl":2,"i":1,"seen":0},{"content":"Large System Extensions (LSE) enabled for ARM instances","slug":"large-system-extensions-lse-enabled-for-arm-instances","lvl":2,"i":2,"seen":0},{"content":"Preliminary Benchmarks","slug":"preliminary-benchmarks","lvl":3,"i":3,"seen":0},{"content":"Ubuntu 20.04","slug":"ubuntu-2004","lvl":2,"i":4,"seen":0},{"content":"Built from source","slug":"built-from-source","lvl":2,"i":5,"seen":0},{"content":"New Extensions","slug":"new-extensions","lvl":2,"i":6,"seen":0},{"content":"`pgRouting`","slug":"pgrouting","lvl":3,"i":7,"seen":0},{"content":"`wal2json`","slug":"wal2json","lvl":3,"i":8,"seen":0},{"content":"Enhanced security","slug":"enhanced-security","lvl":2,"i":9,"seen":0},{"content":"PostgreSQL bundles","slug":"postgresql-bundles","lvl":2,"i":10,"seen":0},{"content":"Try PostgreSQL 13","slug":"try-postgresql-13","lvl":2,"i":11,"seen":0},{"content":"More Postgres resources","slug":"more-postgres-resources","lvl":2,"i":12,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,4],"level":0},{"type":"inline","content":"From today, new Supabase projects will be on a version of [Supabase Postgres](https://github.com/supabase/postgres) that runs\non [Postgres 13.3](https://www.postgresql.org/about/news/postgresql-13-released-2077/). This won't be the only big change however in this version.\nHere are a few other things that have changed under the hood.","level":1,"lines":[1,4],"children":[{"type":"text","content":"From today, new Supabase projects will be on a version of ","level":0},{"type":"link_open","href":"https://github.com/supabase/postgres","title":"","level":0},{"type":"text","content":"Supabase Postgres","level":1},{"type":"link_close","level":0},{"type":"text","content":" that runs","level":0},{"type":"softbreak","level":0},{"type":"text","content":"on ","level":0},{"type":"link_open","href":"https://www.postgresql.org/about/news/postgresql-13-released-2077/","title":"","level":0},{"type":"text","content":"Postgres 13.3","level":1},{"type":"link_close","level":0},{"type":"text","content":". This won't be the only big change however in this version.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Here are a few other things that have changed under the hood.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[5,6],"level":0},{"type":"inline","content":"[PostgreSQL version 13.3](#postgresql-version-133)","level":1,"lines":[5,6],"children":[{"type":"text","content":"PostgreSQL version 13.3","level":0}],"lvl":2,"i":0,"seen":0,"slug":"postgresql-version-133"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[7,9],"level":0},{"type":"inline","content":"We've jumped from PostgreSQL 12.0 to PostgreSQL [version 13.3](https://www.postgresql.org/docs/13/release-13-3.html), introducing\nsignificant performance improvements and some great new functionality. Some changes include:","level":1,"lines":[7,9],"children":[{"type":"text","content":"We've jumped from PostgreSQL 12.0 to PostgreSQL ","level":0},{"type":"link_open","href":"https://www.postgresql.org/docs/13/release-13-3.html","title":"","level":0},{"type":"text","content":"version 13.3","level":1},{"type":"link_close","level":0},{"type":"text","content":", introducing","level":0},{"type":"softbreak","level":0},{"type":"text","content":"significant performance improvements and some great new functionality. Some changes include:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[10,17],"level":0},{"type":"list_item_open","lines":[10,11],"level":1},{"type":"paragraph_open","tight":true,"lines":[10,11],"level":2},{"type":"inline","content":"native [UUID generation](https://www.postgresql.org/docs/13/functions-uuid.html) (!) using `gen_random_uuid`","level":3,"lines":[10,11],"children":[{"type":"text","content":"native ","level":0},{"type":"link_open","href":"https://www.postgresql.org/docs/13/functions-uuid.html","title":"","level":0},{"type":"text","content":"UUID generation","level":1},{"type":"link_close","level":0},{"type":"text","content":" (!) using ","level":0},{"type":"code","content":"gen_random_uuid","block":false,"level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[11,12],"level":1},{"type":"paragraph_open","tight":true,"lines":[11,12],"level":2},{"type":"inline","content":"a new JSONB [datetime](https://www.postgresql.org/docs/13/functions-json.html) function","level":3,"lines":[11,12],"children":[{"type":"text","content":"a new JSONB ","level":0},{"type":"link_open","href":"https://www.postgresql.org/docs/13/functions-json.html","title":"","level":0},{"type":"text","content":"datetime","level":1},{"type":"link_close","level":0},{"type":"text","content":" function","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[12,13],"level":1},{"type":"paragraph_open","tight":true,"lines":[12,13],"level":2},{"type":"inline","content":"vacuuming indexes in [parallel](https://www.postgresql.org/docs/13/sql-vacuum.html)","level":3,"lines":[12,13],"children":[{"type":"text","content":"vacuuming indexes in ","level":0},{"type":"link_open","href":"https://www.postgresql.org/docs/13/sql-vacuum.html","title":"","level":0},{"type":"text","content":"parallel","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[13,14],"level":1},{"type":"paragraph_open","tight":true,"lines":[13,14],"level":2},{"type":"inline","content":"[incremental sorting](https://www.postgresql.org/docs/13/using-explain.html#USING-EXPLAIN-BASICS)","level":3,"lines":[13,14],"children":[{"type":"link_open","href":"https://www.postgresql.org/docs/13/using-explain.html#USING-EXPLAIN-BASICS","title":"","level":0},{"type":"text","content":"incremental sorting","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[14,15],"level":1},{"type":"paragraph_open","tight":true,"lines":[14,15],"level":2},{"type":"inline","content":"smaller [btree indexes](https://www.postgresql.org/docs/13/btree-implementation.html#BTREE-DEDUPLICATION)","level":3,"lines":[14,15],"children":[{"type":"text","content":"smaller ","level":0},{"type":"link_open","href":"https://www.postgresql.org/docs/13/btree-implementation.html#BTREE-DEDUPLICATION","title":"","level":0},{"type":"text","content":"btree indexes","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[15,17],"level":1},{"type":"paragraph_open","tight":true,"lines":[15,16],"level":2},{"type":"inline","content":"improvements to [extended statistics](https://www.postgresql.org/docs/13/planner-stats.html#PLANNER-STATS-EXTENDED)","level":3,"lines":[15,16],"children":[{"type":"text","content":"improvements to ","level":0},{"type":"link_open","href":"https://www.postgresql.org/docs/13/planner-stats.html#PLANNER-STATS-EXTENDED","title":"","level":0},{"type":"text","content":"extended statistics","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":2,"lines":[17,18],"level":0},{"type":"inline","content":"[Supabase Versioning](#supabase-versioning)","level":1,"lines":[17,18],"children":[{"type":"text","content":"Supabase Versioning","level":0}],"lvl":2,"i":1,"seen":0,"slug":"supabase-versioning"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[19,22],"level":0},{"type":"inline","content":"Our [Postgres repo](https://github.com/supabase/postgres) has jumped from `0.15.0` to `13.3.0`. From now on, both major and minor versions of\nSupabase Postgres will follow PostgreSQL. This makes it much easier to ascertain what version of PostgreSQL is installed. In the situation wherein\nthere are no updates to PostgreSQL in between releases, the patch version will be bumped up.","level":1,"lines":[19,22],"children":[{"type":"text","content":"Our ","level":0},{"type":"link_open","href":"https://github.com/supabase/postgres","title":"","level":0},{"type":"text","content":"Postgres repo","level":1},{"type":"link_close","level":0},{"type":"text","content":" has jumped from ","level":0},{"type":"code","content":"0.15.0","block":false,"level":0},{"type":"text","content":" to ","level":0},{"type":"code","content":"13.3.0","block":false,"level":0},{"type":"text","content":". From now on, both major and minor versions of","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Supabase Postgres will follow PostgreSQL. This makes it much easier to ascertain what version of PostgreSQL is installed. In the situation wherein","level":0},{"type":"softbreak","level":0},{"type":"text","content":"there are no updates to PostgreSQL in between releases, the patch version will be bumped up.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[23,24],"level":0},{"type":"inline","content":"[Large System Extensions (LSE) enabled for ARM instances](#large-system-extensions-lse-enabled-for-arm-instances)","level":1,"lines":[23,24],"children":[{"type":"text","content":"Large System Extensions (LSE) enabled for ARM instances","level":0}],"lvl":2,"i":2,"seen":0,"slug":"large-system-extensions-lse-enabled-for-arm-instances"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[25,29],"level":0},{"type":"inline","content":"\u003csmall\u003e\n  *Disclaimer for self-hosting: This is not available for x86 instances. All instances on the\n  Supabase platform have this enabled by default.*\n\u003c/small\u003e","level":1,"lines":[25,29],"children":[{"type":"text","content":"\u003csmall\u003e","level":0},{"type":"softbreak","level":0},{"type":"em_open","level":0},{"type":"text","content":"Disclaimer for self-hosting: This is not available for x86 instances. All instances on the","level":1},{"type":"softbreak","level":1},{"type":"text","content":"Supabase platform have this enabled by default.","level":1},{"type":"em_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/small\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[30,33],"level":0},{"type":"inline","content":"The recent wave of Graviton 2 instances from AWS introduces support for the Large System Extensions (LSE). This looks to greatly enhance\napplication performance through atomics, and\n[improves locking and synchronization performance across large systems](https://github.com/aws/aws-graviton-getting-started#building-for-graviton-and-graviton2).","level":1,"lines":[30,33],"children":[{"type":"text","content":"The recent wave of Graviton 2 instances from AWS introduces support for the Large System Extensions (LSE). This looks to greatly enhance","level":0},{"type":"softbreak","level":0},{"type":"text","content":"application performance through atomics, and","level":0},{"type":"softbreak","level":0},{"type":"link_open","href":"https://github.com/aws/aws-graviton-getting-started#building-for-graviton-and-graviton2","title":"","level":0},{"type":"text","content":"improves locking and synchronization performance across large systems","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[34,35],"level":0},{"type":"inline","content":"[Preliminary Benchmarks](#preliminary-benchmarks)","level":1,"lines":[34,35],"children":[{"type":"text","content":"Preliminary Benchmarks","level":0}],"lvl":3,"i":3,"seen":0,"slug":"preliminary-benchmarks"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[36,39],"level":0},{"type":"inline","content":"Below is a comparison between the ARM versions of Supabase Postgres `0.15.0` and `13.3.0`. Both are using `m6gd.8xlarge` instances\nand follow the PostgreSQL configuration [here](https://www.percona.com/blog/2021/01/22/postgresql-on-arm-based-aws-ec2-instances-is-it-any-good/).\nThe following configuration of `pgbench` was used.","level":1,"lines":[36,39],"children":[{"type":"text","content":"Below is a comparison between the ARM versions of Supabase Postgres ","level":0},{"type":"code","content":"0.15.0","block":false,"level":0},{"type":"text","content":" and ","level":0},{"type":"code","content":"13.3.0","block":false,"level":0},{"type":"text","content":". Both are using ","level":0},{"type":"code","content":"m6gd.8xlarge","block":false,"level":0},{"type":"text","content":" instances","level":0},{"type":"softbreak","level":0},{"type":"text","content":"and follow the PostgreSQL configuration ","level":0},{"type":"link_open","href":"https://www.percona.com/blog/2021/01/22/postgresql-on-arm-based-aws-ec2-instances-is-it-any-good/","title":"","level":0},{"type":"text","content":"here","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0},{"type":"softbreak","level":0},{"type":"text","content":"The following configuration of ","level":0},{"type":"code","content":"pgbench","block":false,"level":0},{"type":"text","content":" was used.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"bash hideCopy","content":"pgbench -i -s 150\n","lines":[40,43],"level":0},{"type":"paragraph_open","tight":false,"lines":[44,45],"level":0},{"type":"inline","content":"Running the benchmark with 2, 4, 8, 16, 64, and 128 clients:","level":1,"lines":[44,45],"children":[{"type":"text","content":"Running the benchmark with 2, 4, 8, 16, 64, and 128 clients:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"bash hideCopy","content":"pgbench -P 5 -c {num_clients} -j {num_clients} -T 300 -M prepared postgres\n","lines":[46,49],"level":0},{"type":"paragraph_open","tight":false,"lines":[50,51],"level":0},{"type":"inline","content":"![PostgreSQL 13.3 performance](/images/blog/pg13/postgres-13-performance.png)","level":1,"lines":[50,51],"children":[{"type":"image","src":"/images/blog/pg13/postgres-13-performance.png","title":"","alt":"PostgreSQL 13.3 performance","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[52,53],"level":0},{"type":"inline","content":"[Ubuntu 20.04](#ubuntu-2004)","level":1,"lines":[52,53],"children":[{"type":"text","content":"Ubuntu 20.04","level":0}],"lvl":2,"i":4,"seen":0,"slug":"ubuntu-2004"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[54,56],"level":0},{"type":"inline","content":"We have taken the opportunity to upgrade new projects from Ubuntu 18.04 to Ubuntu 20.04. A switch to Ubuntu 20.04 guarantees that the underlying\nOS of Supabase Postgres is supported by the Canonical team up until the year 2025 (2023 for Ubuntu 18.04).","level":1,"lines":[54,56],"children":[{"type":"text","content":"We have taken the opportunity to upgrade new projects from Ubuntu 18.04 to Ubuntu 20.04. A switch to Ubuntu 20.04 guarantees that the underlying","level":0},{"type":"softbreak","level":0},{"type":"text","content":"OS of Supabase Postgres is supported by the Canonical team up until the year 2025 (2023 for Ubuntu 18.04).","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[57,58],"level":0},{"type":"inline","content":"[Built from source](#built-from-source)","level":1,"lines":[57,58],"children":[{"type":"text","content":"Built from source","level":0}],"lvl":2,"i":5,"seen":0,"slug":"built-from-source"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[59,62],"level":0},{"type":"inline","content":"Driven by the need to enable LSE, the underlying PostgreSQL in this version was built from the ground up instead of downloaded binaries.\nSupabase Postgres can now be easily upgraded whenever a new major or minor version of PostgreSQL is released. When PostgreSQL 14 comes out,\nexpect Supabase Postgres 14 to quickly follow.","level":1,"lines":[59,62],"children":[{"type":"text","content":"Driven by the need to enable LSE, the underlying PostgreSQL in this version was built from the ground up instead of downloaded binaries.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Supabase Postgres can now be easily upgraded whenever a new major or minor version of PostgreSQL is released. When PostgreSQL 14 comes out,","level":0},{"type":"softbreak","level":0},{"type":"text","content":"expect Supabase Postgres 14 to quickly follow.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[63,64],"level":0},{"type":"inline","content":"[New Extensions](#new-extensions)","level":1,"lines":[63,64],"children":[{"type":"text","content":"New Extensions","level":0}],"lvl":2,"i":6,"seen":0,"slug":"new-extensions"},{"type":"heading_close","hLevel":2,"level":0},{"type":"heading_open","hLevel":3,"lines":[65,66],"level":0},{"type":"inline","content":"[`pgRouting`](#pgrouting)","level":1,"lines":[65,66],"children":[{"type":"code","content":"pgRouting","block":false,"level":0}],"lvl":3,"i":7,"seen":0,"slug":"pgrouting"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[67,68],"level":0},{"type":"inline","content":"An extension of PostGIS, [`pgRouting`](https://pgrouting.org/) provides geospatial routing functionality.","level":1,"lines":[67,68],"children":[{"type":"text","content":"An extension of PostGIS, ","level":0},{"type":"link_open","href":"https://pgrouting.org/","title":"","level":0},{"type":"code","content":"pgRouting","block":false,"level":1},{"type":"link_close","level":0},{"type":"text","content":" provides geospatial routing functionality.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[69,70],"level":0},{"type":"inline","content":"More information can be found [here](https://docs.pgrouting.org/latest/en/index.html).","level":1,"lines":[69,70],"children":[{"type":"text","content":"More information can be found ","level":0},{"type":"link_open","href":"https://docs.pgrouting.org/latest/en/index.html","title":"","level":0},{"type":"text","content":"here","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[71,72],"level":0},{"type":"inline","content":"[`wal2json`](#wal2json)","level":1,"lines":[71,72],"children":[{"type":"code","content":"wal2json","block":false,"level":0}],"lvl":3,"i":8,"seen":0,"slug":"wal2json"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[73,74],"level":0},{"type":"inline","content":"Converts WAL output into clean and organized JSON objects.","level":1,"lines":[73,74],"children":[{"type":"text","content":"Converts WAL output into clean and organized JSON objects.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[75,76],"level":0},{"type":"inline","content":"More information can be found [here](https://github.com/eulerto/wal2json).","level":1,"lines":[75,76],"children":[{"type":"text","content":"More information can be found ","level":0},{"type":"link_open","href":"https://github.com/eulerto/wal2json","title":"","level":0},{"type":"text","content":"here","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[77,78],"level":0},{"type":"inline","content":"[Enhanced security](#enhanced-security)","level":1,"lines":[77,78],"children":[{"type":"text","content":"Enhanced security","level":0}],"lvl":2,"i":9,"seen":0,"slug":"enhanced-security"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[79,81],"level":0},{"type":"inline","content":"To help combat brute force attacks, `fail2ban` has now been configured to protect direct connections to Postgres. This applies to both\nports `5432` (PostgreSQL) and `6543` (PgBouncer).","level":1,"lines":[79,81],"children":[{"type":"text","content":"To help combat brute force attacks, ","level":0},{"type":"code","content":"fail2ban","block":false,"level":0},{"type":"text","content":" has now been configured to protect direct connections to Postgres. This applies to both","level":0},{"type":"softbreak","level":0},{"type":"text","content":"ports ","level":0},{"type":"code","content":"5432","block":false,"level":0},{"type":"text","content":" (PostgreSQL) and ","level":0},{"type":"code","content":"6543","block":false,"level":0},{"type":"text","content":" (PgBouncer).","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[82,84],"level":0},{"type":"inline","content":"IPs get banned for 10 minutes after three failed attempts, and we'll continue to fine-tune and improve the protections applied to the\ndatabase servers based on evolving traffic patterns.","level":1,"lines":[82,84],"children":[{"type":"text","content":"IPs get banned for 10 minutes after three failed attempts, and we'll continue to fine-tune and improve the protections applied to the","level":0},{"type":"softbreak","level":0},{"type":"text","content":"database servers based on evolving traffic patterns.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[85,86],"level":0},{"type":"inline","content":"[PostgreSQL bundles](#postgresql-bundles)","level":1,"lines":[85,86],"children":[{"type":"text","content":"PostgreSQL bundles","level":0}],"lvl":2,"i":10,"seen":0,"slug":"postgresql-bundles"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[87,88],"level":0},{"type":"inline","content":"`[Coming Soon]`","level":1,"lines":[87,88],"children":[{"type":"code","content":"[Coming Soon]","block":false,"level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[89,90],"level":0},{"type":"inline","content":"Last but not the least, we're diversifying the images of Supabase Postgres available in the AWS and Digital Ocean Marketplaces.","level":1,"lines":[89,90],"children":[{"type":"text","content":"Last but not the least, we're diversifying the images of Supabase Postgres available in the AWS and Digital Ocean Marketplaces.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[91,94],"level":0},{"type":"inline","content":"Instead of a single option, we'll soon offer four configurations of PostgreSQL. Each bundle offers functionality for\ncommon use-cases. For example, if you're using Postgres with Serverless functions, you might want to run the PgBouncer bundle. If you want an\nHTTP API with your Postgres offering you might want to run the PostgREST bundle.","level":1,"lines":[91,94],"children":[{"type":"text","content":"Instead of a single option, we'll soon offer four configurations of PostgreSQL. Each bundle offers functionality for","level":0},{"type":"softbreak","level":0},{"type":"text","content":"common use-cases. For example, if you're using Postgres with Serverless functions, you might want to run the PgBouncer bundle. If you want an","level":0},{"type":"softbreak","level":0},{"type":"text","content":"HTTP API with your Postgres offering you might want to run the PostgREST bundle.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"table_open","lines":[95,101],"level":0},{"type":"thead_open","lines":[95,96],"level":1},{"type":"tr_open","lines":[95,96],"level":2},{"type":"th_open","align":"","lines":[95,96],"level":3},{"type":"inline","content":"`Name`","lines":[95,96],"level":4,"children":[{"type":"code","content":"Name","block":false,"level":0}]},{"type":"th_close","level":3},{"type":"th_open","align":"","lines":[95,96],"level":3},{"type":"inline","content":"`PostgreSQL`","lines":[95,96],"level":4,"children":[{"type":"code","content":"PostgreSQL","block":false,"level":0}]},{"type":"th_close","level":3},{"type":"th_open","align":"","lines":[95,96],"level":3},{"type":"inline","content":"`PgBouncer`","lines":[95,96],"level":4,"children":[{"type":"code","content":"PgBouncer","block":false,"level":0}]},{"type":"th_close","level":3},{"type":"th_open","align":"","lines":[95,96],"level":3},{"type":"inline","content":"`PostgREST`","lines":[95,96],"level":4,"children":[{"type":"code","content":"PostgREST","block":false,"level":0}]},{"type":"th_close","level":3},{"type":"tr_close","level":2},{"type":"thead_close","level":1},{"type":"tbody_open","lines":[97,101],"level":1},{"type":"tr_open","level":2},{"type":"td_open","align":"","level":3},{"type":"inline","content":"Supabase Postgres","level":4,"children":[{"type":"text","content":"Supabase Postgres","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"✅","level":4,"children":[{"type":"text","content":"✅","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"","level":4,"children":[]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"","level":4,"children":[]},{"type":"td_close","level":3},{"type":"tr_close","level":2},{"type":"tr_open","level":2},{"type":"td_open","align":"","level":3},{"type":"inline","content":"Supabase Postgres: PgBouncer Bundle","level":4,"children":[{"type":"text","content":"Supabase Postgres: PgBouncer Bundle","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"✅","level":4,"children":[{"type":"text","content":"✅","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"✅","level":4,"children":[{"type":"text","content":"✅","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"","level":4,"children":[]},{"type":"td_close","level":3},{"type":"tr_close","level":2},{"type":"tr_open","level":2},{"type":"td_open","align":"","level":3},{"type":"inline","content":"Supabase Postgres: PostgREST Bundle","level":4,"children":[{"type":"text","content":"Supabase Postgres: PostgREST Bundle","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"✅","level":4,"children":[{"type":"text","content":"✅","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"","level":4,"children":[]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"✅","level":4,"children":[{"type":"text","content":"✅","level":0}]},{"type":"td_close","level":3},{"type":"tr_close","level":2},{"type":"tr_open","level":2},{"type":"td_open","align":"","level":3},{"type":"inline","content":"Supabase Postgres: Complete Bundle","level":4,"children":[{"type":"text","content":"Supabase Postgres: Complete Bundle","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"✅","level":4,"children":[{"type":"text","content":"✅","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"✅","level":4,"children":[{"type":"text","content":"✅","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"✅","level":4,"children":[{"type":"text","content":"✅","level":0}]},{"type":"td_close","level":3},{"type":"tr_close","level":2},{"type":"tbody_close","level":1},{"type":"table_close","level":0},{"type":"paragraph_open","tight":false,"lines":[102,103],"level":0},{"type":"inline","content":"Each offering will be available for both the `x86` and `arm` architectures.","level":1,"lines":[102,103],"children":[{"type":"text","content":"Each offering will be available for both the ","level":0},{"type":"code","content":"x86","block":false,"level":0},{"type":"text","content":" and ","level":0},{"type":"code","content":"arm","block":false,"level":0},{"type":"text","content":" architectures.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[104,105],"level":0},{"type":"inline","content":"[Try PostgreSQL 13](#try-postgresql-13)","level":1,"lines":[104,105],"children":[{"type":"text","content":"Try PostgreSQL 13","level":0}],"lvl":2,"i":11,"seen":0,"slug":"try-postgresql-13"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[106,107],"level":0},{"type":"inline","content":"Try the new features of PostgreSQL 13.3 today by creating a [new project](https://supabase.com/dashboard/) on Supabase.","level":1,"lines":[106,107],"children":[{"type":"text","content":"Try the new features of PostgreSQL 13.3 today by creating a ","level":0},{"type":"link_open","href":"https://supabase.com/dashboard/","title":"","level":0},{"type":"text","content":"new project","level":1},{"type":"link_close","level":0},{"type":"text","content":" on Supabase.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[108,109],"level":0},{"type":"inline","content":"[More Postgres resources](#more-postgres-resources)","level":1,"lines":[108,109],"children":[{"type":"text","content":"More Postgres resources","level":0}],"lvl":2,"i":12,"seen":0,"slug":"more-postgres-resources"},{"type":"heading_close","hLevel":2,"level":0},{"type":"bullet_list_open","lines":[110,117],"level":0},{"type":"list_item_open","lines":[110,111],"level":1},{"type":"paragraph_open","tight":true,"lines":[110,111],"level":2},{"type":"inline","content":"[Realtime Postgres RLS on Supabase](https://supabase.com//blog/realtime-row-level-security-in-postgresql)","level":3,"lines":[110,111],"children":[{"type":"link_open","href":"https://supabase.com//blog/realtime-row-level-security-in-postgresql","title":"","level":0},{"type":"text","content":"Realtime Postgres RLS on Supabase","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[111,112],"level":1},{"type":"paragraph_open","tight":true,"lines":[111,112],"level":2},{"type":"inline","content":"[Implementing \"seen by\" functionality with Postgres](https://supabase.com/blog/seen-by-in-postgresql)","level":3,"lines":[111,112],"children":[{"type":"link_open","href":"https://supabase.com/blog/seen-by-in-postgresql","title":"","level":0},{"type":"text","content":"Implementing \"seen by\" functionality with Postgres","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[112,113],"level":1},{"type":"paragraph_open","tight":true,"lines":[112,113],"level":2},{"type":"inline","content":"[Partial data dumps using Postgres Row Level Security](https://supabase.com/blog/partial-postgresql-data-dumps-with-rls)","level":3,"lines":[112,113],"children":[{"type":"link_open","href":"https://supabase.com/blog/partial-postgresql-data-dumps-with-rls","title":"","level":0},{"type":"text","content":"Partial data dumps using Postgres Row Level Security","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[113,114],"level":1},{"type":"paragraph_open","tight":true,"lines":[113,114],"level":2},{"type":"inline","content":"[Postgres Views](https://supabase.com/blog/postgresql-views)","level":3,"lines":[113,114],"children":[{"type":"link_open","href":"https://supabase.com/blog/postgresql-views","title":"","level":0},{"type":"text","content":"Postgres Views","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[114,115],"level":1},{"type":"paragraph_open","tight":true,"lines":[114,115],"level":2},{"type":"inline","content":"[Postgres Auditing in 150 lines of SQL](https://supabase.com/blog/postgres-audit)","level":3,"lines":[114,115],"children":[{"type":"link_open","href":"https://supabase.com/blog/postgres-audit","title":"","level":0},{"type":"text","content":"Postgres Auditing in 150 lines of SQL","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[115,116],"level":1},{"type":"paragraph_open","tight":true,"lines":[115,116],"level":2},{"type":"inline","content":"[Cracking PostgreSQL Interview Questions](https://supabase.com/blog/cracking-postgres-interview)","level":3,"lines":[115,116],"children":[{"type":"link_open","href":"https://supabase.com/blog/cracking-postgres-interview","title":"","level":0},{"type":"text","content":"Cracking PostgreSQL Interview Questions","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[116,117],"level":1},{"type":"paragraph_open","tight":true,"lines":[116,117],"level":2},{"type":"inline","content":"[What are PostgreSQL Templates?](https://supabase.com/blog/postgresql-templates)","level":3,"lines":[116,117],"children":[{"type":"link_open","href":"https://supabase.com/blog/postgresql-templates","title":"","level":0},{"type":"text","content":"What are PostgreSQL Templates?","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0}],"content":"- [PostgreSQL version 13.3](#postgresql-version-133)\n- [Supabase Versioning](#supabase-versioning)\n- [Large System Extensions (LSE) enabled for ARM instances](#large-system-extensions-lse-enabled-for-arm-instances)\n- [Ubuntu 20.04](#ubuntu-2004)\n- [Built from source](#built-from-source)\n- [New Extensions](#new-extensions)\n- [Enhanced security](#enhanced-security)\n- [PostgreSQL bundles](#postgresql-bundles)\n- [Try PostgreSQL 13](#try-postgresql-13)\n- [More Postgres resources](#more-postgres-resources)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-postgres-13"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>