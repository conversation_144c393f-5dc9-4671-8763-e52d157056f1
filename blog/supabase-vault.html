<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase Vault</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Today we&#x27;re announcing Vault, a Postgres extension for managing secrets and encryption inside your database." data-next-head=""/><meta property="og:title" content="Supabase Vault" data-next-head=""/><meta property="og:description" content="Today we&#x27;re announcing Vault, a Postgres extension for managing secrets and encryption inside your database." data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-vault" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2022-08-19" data-next-head=""/><meta property="article:author" content="https://github.com/michelp" data-next-head=""/><meta property="article:tag" content="launch-week" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/lw5-vault/supabase-vault.jpg" data-next-head=""/><meta property="og:image:alt" content="Supabase Vault thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Supabase Vault</h1><div class="text-light flex space-x-3 text-sm"><p>19 Aug 2022</p><p>•</p><p>8 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/michelp"><div class="flex items-center gap-3"><div class="w-10"><img alt="Michel Pelletier avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fmichelp.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fmichelp.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fmichelp.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Michel Pelletier</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Supabase Vault" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-vault%2Fsupabase-vault.jpg&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-vault%2Fsupabase-vault.jpg&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-vault%2Fsupabase-vault.jpg&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-vault%2Fsupabase-vault.jpg&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-vault%2Fsupabase-vault.jpg&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-vault%2Fsupabase-vault.jpg&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-vault%2Fsupabase-vault.jpg&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-vault%2Fsupabase-vault.jpg&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-vault%2Fsupabase-vault.jpg&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-vault%2Fsupabase-vault.jpg&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-vault%2Fsupabase-vault.jpg&amp;w=3840&amp;q=100"/></div><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p>Today we&#x27;re announcing <a href="https://supabase.com/docs/guides/database/vault">Supabase Vault</a>, a Postgres extension for managing secrets and encryption inside your database.
Vault is a thin usability-layer on top of <a href="https://github.com/michelp/pgsodium">pgsodium</a>.</p>
<div class="bg-gray-300 rounded-lg px-6 py-2 bold"><p>❇️ UPDATE JUNE 2023 ❇️</p><p>Vault is now available on every Supabase project. <a href="https://supabase.com/dashboard/project/_/settings/vault/secrets">Check it out</a></p></div>
<p><a href="https://supabase.com/blog/transparent-column-encryption-with-postgres">Transparent Column Encryption with Postgres</a> is a blog post that describes the technology behind Vault - <a href="https://doc.libsodium.org/">libsodium</a> and
<a href="https://doc.libsodium.org/">pgsodium</a>. Now, we will go through a quick example of storing a secret, like a service access token, into the Vault.</p>
<h2 id="background" class="group scroll-mt-24">Background<a href="#background" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Until now, the industry-standard for PostgreSQL encryption is a built-in extension called <a href="https://www.postgresql.org/docs/current/pgcrypto.html"><code class="short-inline-codeblock">pgcrypto</code></a>.
Like most cloud providers, Supabase offers <code class="short-inline-codeblock">pgcrypto</code> for developers to use in their applications. <code class="short-inline-codeblock">pgcrypto</code> has been around for a long time,
and while it supports some basic encryption and decryption abilities, it lacks features like public key signing, key derivation APIs, streaming encryption,
and other modern features required by security-first applications.</p>
<h3 id="problems-with-raw-keys" class="group scroll-mt-24">Problems with raw keys<a href="#problems-with-raw-keys" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Databases often store sensitive information, and they need tools that guarantee this data is stored and backed-up in an encrypted form.
A fundamental issue with <code class="short-inline-codeblock">pgcrypto</code> is that it lacks the ability to derive keys from outside of SQL. Instead you must have the raw encryption key for the algorithm you wish to use:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->12</span><div style="display:inline-block;margin-left:16px"><span>create extension pgcrypto;</span></div></div><div><span class="ch-code-line-number">_<!-- -->12</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->12</span><div style="display:inline-block;margin-left:16px"><span>create table users (</span></div></div><div><span class="ch-code-line-number">_<!-- -->12</span><div style="display:inline-block;margin-left:16px"><span>  id serial primary key,</span></div></div><div><span class="ch-code-line-number">_<!-- -->12</span><div style="display:inline-block;margin-left:16px"><span>  email varchar not null unique</span></div></div><div><span class="ch-code-line-number">_<!-- -->12</span><div style="display:inline-block;margin-left:16px"><span>);</span></div></div><div><span class="ch-code-line-number">_<!-- -->12</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->12</span><div style="display:inline-block;margin-left:16px"><span>insert into users</span></div></div><div><span class="ch-code-line-number">_<!-- -->12</span><div style="display:inline-block;margin-left:16px"><span>  (email)</span></div></div><div><span class="ch-code-line-number">_<!-- -->12</span><div style="display:inline-block;margin-left:16px"><span>values</span></div></div><div><span class="ch-code-line-number">_<!-- -->12</span><div style="display:inline-block;margin-left:16px"><span>  (pgp_sym_encrypt(&#x27;<EMAIL>&#x27;, &#x27;s3kr3t_k3y&#x27;)),</span></div></div><div><span class="ch-code-line-number">_<!-- -->12</span><div style="display:inline-block;margin-left:16px"><span>  (pgp_sym_encrypt(&#x27;<EMAIL>&#x27;, &#x27;s3kr3t_key&#x27;));</span></div></div><br/></code></div></div>
<p>pgcrypto works with <em>raw</em> keys. In order to encrypt the data with pgcrypto you must pass the key directly to the encryption function.
Leaking those raw keys is all too easy - logs, files, clients, tables, replication streams - you name it.
Wouldn&#x27;t it be great if you could encrypt data, but instead of specifying the raw key you reference it indirectly, like with a key ID?</p>
<h2 id="supabase-vault" class="group scroll-mt-24">Supabase Vault<a href="#supabase-vault" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Supabase Vault allows you to store secrets without leaking any sensitive information.</p>
<p>The Vault is a good place to put things like API keys, access tokens, and other secrets from external services that you need to access within your database.
The core of the Supabase Vault is a table with some metadata and an encrypted text column where you can put your secrets and any metadata related to them.</p>
<p>We take the pain out of key management by pre-generating a unique, per-database key that is used by default - a “root” key - which is stored outside of the SQL language,
accessibly only internally in the Postgres server by the libsodium library. This root key is managed by the <a href="https://github.com/michelp/pgsodium">pgsodium</a> Postgres extension
when the server boots using <a href="https://github.com/michelp/pgsodium#server-key-management">Server Key Management.</a></p>
<p>pgsodium provides an Encryption and Key Derivation API based on the <a href="https://libsodium.gitbook.io/doc/">libsodium library</a> and can get it&#x27;s root key from a
variety of sources depending on how you configure it. Supabase generates and preserves your project&#x27;s root key behind the scenes, so you don&#x27;t have to worry about it.
If you install pgsodium locally the default is to generate a random root key from the <code class="short-inline-codeblock">/dev/urandom</code> device which is then saved in a file in your Postgres data directory.</p>
<p>Installing the <code class="short-inline-codeblock">vault</code> extension is the same as any other Postgres extension:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>create extension supabase_vault with schema vault;</span></div></div><br/></code></div></div>
<p>Once enabled, you can insert secrets into the <code class="short-inline-codeblock">vault.secrets</code> table:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>insert into vault.secrets</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  (secret, associated)</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>values</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  (&#x27;s3kr3t_k3y&#x27;, &#x27;This is the secret API service key.&#x27;);</span></div></div><br/></code></div></div>
<p>Now when you look in the <code class="short-inline-codeblock">vault.secrets</code> table, the secret is encrypted:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>select * from vault.secrets;</span></div></div><br/></code></div></div>
<div class="ch-codegroup not-prose "><div class="ch-editor-frame"><div class="ch-frame-title-bar"><div class="ch-frame-buttons"><div class="ch-frame-button ch-frame-button-left"></div><div class="ch-frame-button-space"></div><div class="ch-frame-button ch-frame-button-middle"></div><div class="ch-frame-button-space"></div><div class="ch-frame-button ch-frame-button-right"></div></div><div title="hideCopy" data-ch-tab="north" data-active="true" class="ch-editor-tab"><div><span style="opacity:0.5"></span>hideCopy</div></div><div style="flex:1;min-width:0.8em"></div><button type="button" title="Copy code" class="ch-editor-button"><svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.6px" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg></button></div><div data-ch-panel="north" style="flex-grow:1;overflow:hidden"><div style="height:100%" class="ch-code-wrapper " data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>-[ RECORD 1 ]--------------------------------------------------------</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>id         | f6a2fe0a-3471-4eea-a581-75c4d2be396b</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>secret     | /eT9bb96POTJ7L2gYrluTZ3r3pG5IMwPSQo6pQP0xdZTarpRrpWPXTWQ</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>key_id     | caabfc28-2ab3-48f5-8978-1b3d4b659911</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>associated | This is the secret API service key.</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>nonce      | \x77c7381c523630ba72f1f137626a9f9a</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>created_at | 2022-08-18 19:33:15.312651+00</span></div></div><br/></code></div></div></div></div>
<p>Notice how the row has a <code class="short-inline-codeblock">key_id</code> column. This is the <em>ID</em> of the internally derived key that is used to encrypt the secret, not the key itself.
The actual raw key is not available to you in SQL, it is managed entirely outside of the SQL language in the Postgres server.</p>
<p>At Supabase, we manage this key for your project automatically and generate a unique default Key ID for you in the <code class="short-inline-codeblock">secrets</code> table.
For self-hosting, pgsodium <a href="https://github.com/michelp/pgsodium#server-key-management">supports a variety of ways</a> to place the root key into Postgres.</p>
<p>To see the decrypted data, there is a special view created called <code class="short-inline-codeblock">vault.decrypted_secrets</code>:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>select * from vault.decrypted_secrets;</span></div></div><br/></code></div></div>
<div class="ch-codegroup not-prose "><div class="ch-editor-frame"><div class="ch-frame-title-bar"><div class="ch-frame-buttons"><div class="ch-frame-button ch-frame-button-left"></div><div class="ch-frame-button-space"></div><div class="ch-frame-button ch-frame-button-middle"></div><div class="ch-frame-button-space"></div><div class="ch-frame-button ch-frame-button-right"></div></div><div title="hideCopy" data-ch-tab="north" data-active="true" class="ch-editor-tab"><div><span style="opacity:0.5"></span>hideCopy</div></div><div style="flex:1;min-width:0.8em"></div><button type="button" title="Copy code" class="ch-editor-button"><svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.6px" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg></button></div><div data-ch-panel="north" style="flex-grow:1;overflow:hidden"><div style="height:100%" class="ch-code-wrapper " data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>-[ RECORD 1 ]----+---------------------------------------------------------</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>id               | f6a2fe0a-3471-4eea-a581-75c4d2be396b</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>secret           | /eT9bb96POTJ7L2gYrluTZ3r3pG5IMwPSQo6pQP0xdZTarpRrpWPXTWQ</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>decrypted_secret | s3kr3t_k3y</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>key_id           | caabfc28-2ab3-48f5-8978-1b3d4b659911</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>associated       | This is the secret API service key.</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>nonce            | \x77c7381c523630ba72f1f137626a9f9a</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>created_at       | 2022-08-18 19:33:15.312651+00</span></div></div><br/></code></div></div></div></div>
<p>Now you can see a new <code class="short-inline-codeblock">decrypted_secret</code> column that contains the decrypted secret we originally inserted into the table.
This <code class="short-inline-codeblock">vault.decrypted_secrets</code> view automatically decrypts rows in the <code class="short-inline-codeblock">vault.secrets</code> table “on-the-fly” as you query them, but the secret is stored on disk in <em>encrypted</em> form.
If you take a backup, or pause your project, that data remains encrypted. We will keep your hidden root key safe in our backend systems for when you need to restore or un-pause your projects.</p>
<p>If you wish to use your own Key ID for different secrets, instead of the default Key ID we&#x27;ve generated, you can create one using the <code class="short-inline-codeblock">pgsodium.create_key()</code> function:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>select * from pgsodium.create_key(&#x27;This is a comment for the new key&#x27;);</span></div></div><br/></code></div></div>
<div class="ch-codegroup not-prose "><div class="ch-editor-frame"><div class="ch-frame-title-bar"><div class="ch-frame-buttons"><div class="ch-frame-button ch-frame-button-left"></div><div class="ch-frame-button-space"></div><div class="ch-frame-button ch-frame-button-middle"></div><div class="ch-frame-button-space"></div><div class="ch-frame-button ch-frame-button-right"></div></div><div title="hideCopy" data-ch-tab="north" data-active="true" class="ch-editor-tab"><div><span style="opacity:0.5"></span>hideCopy</div></div><div style="flex:1;min-width:0.8em"></div><button type="button" title="Copy code" class="ch-editor-button"><svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.6px" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg></button></div><div data-ch-panel="north" style="flex-grow:1;overflow:hidden"><div style="height:100%" class="ch-code-wrapper " data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>-[ RECORD 1 ]-------------------------------------</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>id          | f9f176eb-7069-4743-9403-582c04354ffc</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>status      | valid</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>created     | 2022-08-18 22:31:50.331792</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>expires     |</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>key_type    | aead-det</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>key_id      | 2</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>key_context | \x7067736f6469756d</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>comment     | This is the comment for the new key</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>user_data   |</span></div></div><br/></code></div></div></div></div>
<p>Now you can encrypt table secrets with this new key by inserting its ID explicitly:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>insert into vault.secrets</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  (secret, associated, key_id)</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>values</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  (</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    &#x27;apikey_XaYrurzcquqhEdBjzfTzfwAZqpd&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    &#x27;This is some different associated data.&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    &#x27;f9f176eb-7069-4743-9403-582c04354ffc&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  )</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>returning *;</span></div></div><br/></code></div></div>
<div class="ch-codegroup not-prose "><div class="ch-editor-frame"><div class="ch-frame-title-bar"><div class="ch-frame-buttons"><div class="ch-frame-button ch-frame-button-left"></div><div class="ch-frame-button-space"></div><div class="ch-frame-button ch-frame-button-middle"></div><div class="ch-frame-button-space"></div><div class="ch-frame-button ch-frame-button-right"></div></div><div title="hideCopy" data-ch-tab="north" data-active="true" class="ch-editor-tab"><div><span style="opacity:0.5"></span>hideCopy</div></div><div style="flex:1;min-width:0.8em"></div><button type="button" title="Copy code" class="ch-editor-button"><svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.6px" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg></button></div><div data-ch-panel="north" style="flex-grow:1;overflow:hidden"><div style="height:100%" class="ch-code-wrapper " data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>-[ RECORD 1 ]------------------------------------------------------------</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>id         | 9c58a0f3-aa40-4789-b683-6db48b241f9e</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>secret     | YWxuTnWdF55MuRrZ7xneBvaz2uH59U1dJV/7CCZjSn5B5jELOoy/csq8x/s=</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>key_id     | f9f176eb-7069-4743-9403-582c04354ffc</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>associated | This is some different associated data.</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>nonce      | \xd39808b07c9ae52c8f02c33a7f87595c</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>created_at | 2022-08-18 22:34:07.219941+00</span></div></div><br/></code></div></div></div></div>
<p>The type of encryption used by the Vault is called <a href="https://en.wikipedia.org/wiki/Authenticated_encryption">Authenticated Encryption with Associated Data</a>.
The data you insert into the <code class="short-inline-codeblock">associated</code> column, which is up to you, is combined with the encrypted text when libsodium creates the authentication signature for the secret.
This means that when you read the secret, you know that the associated data is also authentic. The associated data could be an account ID or some
information that ties your system to the secret. And as always, you can refer to rows in the secrets table by their primary key UUID.</p>
<p>If you only want to store secrets that you know are encrypted on disk and in backups, then all you need to know is shown above. Just insert secrets into the table,
optionally creating new keys, and select them from the view when you want to use them.</p>
<h2 id="going-beyond-the-vault" class="group scroll-mt-24">Going Beyond the Vault<a href="#going-beyond-the-vault" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>The Vault is good for a reasonable amount of secure data, like API keys, access tokens, or environment variables.
But if you have a lot more sensitive information, like personally Identifiable Information (PII),
you may want to break them out into side-tables using pgsodium&#x27;s
<a href="https://supabase.com/blog/transparent-column-encryption-with-postgres">Transparent Column Encryption</a> which we will describe soon in a follow-up blog post. Stay tuned!</p></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-vault&amp;text=Supabase%20Vault"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-vault&amp;text=Supabase%20Vault"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-vault&amp;t=Supabase%20Vault"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="postgrest-v10.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">PostgREST v10: EXPLAIN and Improved Relationship Detection</h4><p class="small">19 August 2022</p></div></div></div></div></a></div><div><a href="https://supabase.com/blog/supabase-realtime-multiplayer-general-availability"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Realtime: Multiplayer Edition</h4><p class="small">18 August 2022</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/launch-week"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">launch-week</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#background">Background</a></li>
<li><a href="#supabase-vault">Supabase Vault</a></li>
<li><a href="#going-beyond-the-vault">Going Beyond the Vault</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-vault&amp;text=Supabase%20Vault"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-vault&amp;text=Supabase%20Vault"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-vault&amp;t=Supabase%20Vault"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"postgrest-v10","title":"PostgREST v10: EXPLAIN and Improved Relationship Detection","description":"Today, PostgREST 10 was released. Let's take a look at some of the new features that go hand in hand with supabase-js v2.","author":"steve_chavez","image":"lw5/postgrest-v10.png","thumb":"lw5/postgrest-v10.png","categories":["postgres"],"tags":["postgres","launch-week"],"date":"2022-08-19","toc_depth":3,"formattedDate":"19 August 2022","readingTime":"8 minute read","url":"/blog/postgrest-v10","path":"/blog/postgrest-v10"},"nextPost":{"slug":"supabase-realtime-multiplayer-general-availability","title":"Realtime: Multiplayer Edition","description":"Announcing the general availability of Realtime's Broadcast and Presence.","author":"wenbo,stas","image":"lw5-realtime/thumb.jpg","thumb":"lw5-realtime/thumb.jpg","categories":["product"],"tags":["launch-week"],"date":"2022-08-18","toc_depth":3,"video":"https://www.youtube.com/v/CGZr5tybW18","formattedDate":"18 August 2022","readingTime":"10 minute read","url":"/blog/supabase-realtime-multiplayer-general-availability","path":"/blog/supabase-realtime-multiplayer-general-availability"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-vault","source":"\nToday we're announcing [Supabase Vault](https://supabase.com/docs/guides/database/vault), a Postgres extension for managing secrets and encryption inside your database.\nVault is a thin usability-layer on top of [pgsodium](https://github.com/michelp/pgsodium).\n\n\u003cdiv className=\"bg-gray-300 rounded-lg px-6 py-2 bold\"\u003e\n\n❇️ UPDATE JUNE 2023 ❇️\n\nVault is now available on every Supabase project. [Check it out](https://supabase.com/dashboard/project/_/settings/vault/secrets)\n\n\u003c/div\u003e\n\n[Transparent Column Encryption with Postgres](https://supabase.com/blog/transparent-column-encryption-with-postgres) is a blog post that describes the technology behind Vault - [libsodium](https://doc.libsodium.org/) and\n[pgsodium](https://doc.libsodium.org/). Now, we will go through a quick example of storing a secret, like a service access token, into the Vault.\n\n## Background\n\nUntil now, the industry-standard for PostgreSQL encryption is a built-in extension called [`pgcrypto`](https://www.postgresql.org/docs/current/pgcrypto.html).\nLike most cloud providers, Supabase offers `pgcrypto` for developers to use in their applications. `pgcrypto` has been around for a long time,\nand while it supports some basic encryption and decryption abilities, it lacks features like public key signing, key derivation APIs, streaming encryption,\nand other modern features required by security-first applications.\n\n### Problems with raw keys\n\nDatabases often store sensitive information, and they need tools that guarantee this data is stored and backed-up in an encrypted form.\nA fundamental issue with `pgcrypto` is that it lacks the ability to derive keys from outside of SQL. Instead you must have the raw encryption key for the algorithm you wish to use:\n\n```sql\ncreate extension pgcrypto;\n\ncreate table users (\n  id serial primary key,\n  email varchar not null unique\n);\n\ninsert into users\n  (email)\nvalues\n  (pgp_sym_encrypt('<EMAIL>', 's3kr3t_k3y')),\n  (pgp_sym_encrypt('<EMAIL>', 's3kr3t_key'));\n```\n\npgcrypto works with _raw_ keys. In order to encrypt the data with pgcrypto you must pass the key directly to the encryption function.\nLeaking those raw keys is all too easy - logs, files, clients, tables, replication streams - you name it.\nWouldn't it be great if you could encrypt data, but instead of specifying the raw key you reference it indirectly, like with a key ID?\n\n## Supabase Vault\n\nSupabase Vault allows you to store secrets without leaking any sensitive information.\n\nThe Vault is a good place to put things like API keys, access tokens, and other secrets from external services that you need to access within your database.\nThe core of the Supabase Vault is a table with some metadata and an encrypted text column where you can put your secrets and any metadata related to them.\n\nWe take the pain out of key management by pre-generating a unique, per-database key that is used by default - a “root” key - which is stored outside of the SQL language,\naccessibly only internally in the Postgres server by the libsodium library. This root key is managed by the [pgsodium](https://github.com/michelp/pgsodium) Postgres extension\nwhen the server boots using [Server Key Management.](https://github.com/michelp/pgsodium#server-key-management)\n\npgsodium provides an Encryption and Key Derivation API based on the [libsodium library](https://libsodium.gitbook.io/doc/) and can get it's root key from a\nvariety of sources depending on how you configure it. Supabase generates and preserves your project's root key behind the scenes, so you don't have to worry about it.\nIf you install pgsodium locally the default is to generate a random root key from the `/dev/urandom` device which is then saved in a file in your Postgres data directory.\n\nInstalling the `vault` extension is the same as any other Postgres extension:\n\n```sql\ncreate extension supabase_vault with schema vault;\n```\n\nOnce enabled, you can insert secrets into the `vault.secrets` table:\n\n```sql\ninsert into vault.secrets\n  (secret, associated)\nvalues\n  ('s3kr3t_k3y', 'This is the secret API service key.');\n```\n\nNow when you look in the `vault.secrets` table, the secret is encrypted:\n\n```sql\nselect * from vault.secrets;\n```\n\n```text hideCopy\n-[ RECORD 1 ]--------------------------------------------------------\nid         | f6a2fe0a-3471-4eea-a581-75c4d2be396b\nsecret     | /eT9bb96POTJ7L2gYrluTZ3r3pG5IMwPSQo6pQP0xdZTarpRrpWPXTWQ\nkey_id     | caabfc28-2ab3-48f5-8978-1b3d4b659911\nassociated | This is the secret API service key.\nnonce      | \\x77c7381c523630ba72f1f137626a9f9a\ncreated_at | 2022-08-18 19:33:15.312651+00\n```\n\nNotice how the row has a `key_id` column. This is the _ID_ of the internally derived key that is used to encrypt the secret, not the key itself.\nThe actual raw key is not available to you in SQL, it is managed entirely outside of the SQL language in the Postgres server.\n\nAt Supabase, we manage this key for your project automatically and generate a unique default Key ID for you in the `secrets` table.\nFor self-hosting, pgsodium [supports a variety of ways](https://github.com/michelp/pgsodium#server-key-management) to place the root key into Postgres.\n\nTo see the decrypted data, there is a special view created called `vault.decrypted_secrets`:\n\n```sql\nselect * from vault.decrypted_secrets;\n```\n\n```text hideCopy\n-[ RECORD 1 ]----+---------------------------------------------------------\nid               | f6a2fe0a-3471-4eea-a581-75c4d2be396b\nsecret           | /eT9bb96POTJ7L2gYrluTZ3r3pG5IMwPSQo6pQP0xdZTarpRrpWPXTWQ\ndecrypted_secret | s3kr3t_k3y\nkey_id           | caabfc28-2ab3-48f5-8978-1b3d4b659911\nassociated       | This is the secret API service key.\nnonce            | \\x77c7381c523630ba72f1f137626a9f9a\ncreated_at       | 2022-08-18 19:33:15.312651+00\n```\n\nNow you can see a new `decrypted_secret` column that contains the decrypted secret we originally inserted into the table.\nThis `vault.decrypted_secrets` view automatically decrypts rows in the `vault.secrets` table “on-the-fly” as you query them, but the secret is stored on disk in _encrypted_ form.\nIf you take a backup, or pause your project, that data remains encrypted. We will keep your hidden root key safe in our backend systems for when you need to restore or un-pause your projects.\n\nIf you wish to use your own Key ID for different secrets, instead of the default Key ID we've generated, you can create one using the `pgsodium.create_key()` function:\n\n```sql\nselect * from pgsodium.create_key('This is a comment for the new key');\n```\n\n```text hideCopy\n-[ RECORD 1 ]-------------------------------------\nid          | f9f176eb-7069-4743-9403-582c04354ffc\nstatus      | valid\ncreated     | 2022-08-18 22:31:50.331792\nexpires     |\nkey_type    | aead-det\nkey_id      | 2\nkey_context | \\x7067736f6469756d\ncomment     | This is the comment for the new key\nuser_data   |\n```\n\nNow you can encrypt table secrets with this new key by inserting its ID explicitly:\n\n```sql\ninsert into vault.secrets\n  (secret, associated, key_id)\nvalues\n  (\n    'apikey_XaYrurzcquqhEdBjzfTzfwAZqpd',\n    'This is some different associated data.',\n    'f9f176eb-7069-4743-9403-582c04354ffc'\n  )\nreturning *;\n```\n\n```text hideCopy\n-[ RECORD 1 ]------------------------------------------------------------\nid         | 9c58a0f3-aa40-4789-b683-6db48b241f9e\nsecret     | YWxuTnWdF55MuRrZ7xneBvaz2uH59U1dJV/7CCZjSn5B5jELOoy/csq8x/s=\nkey_id     | f9f176eb-7069-4743-9403-582c04354ffc\nassociated | This is some different associated data.\nnonce      | \\xd39808b07c9ae52c8f02c33a7f87595c\ncreated_at | 2022-08-18 22:34:07.219941+00\n```\n\nThe type of encryption used by the Vault is called [Authenticated Encryption with Associated Data](https://en.wikipedia.org/wiki/Authenticated_encryption).\nThe data you insert into the `associated` column, which is up to you, is combined with the encrypted text when libsodium creates the authentication signature for the secret.\nThis means that when you read the secret, you know that the associated data is also authentic. The associated data could be an account ID or some\ninformation that ties your system to the secret. And as always, you can refer to rows in the secrets table by their primary key UUID.\n\nIf you only want to store secrets that you know are encrypted on disk and in backups, then all you need to know is shown above. Just insert secrets into the table,\noptionally creating new keys, and select them from the view when you want to use them.\n\n## Going Beyond the Vault\n\nThe Vault is good for a reasonable amount of secure data, like API keys, access tokens, or environment variables.\nBut if you have a lot more sensitive information, like personally Identifiable Information (PII),\nyou may want to break them out into side-tables using pgsodium's\n[Transparent Column Encryption](https://supabase.com/blog/transparent-column-encryption-with-postgres) which we will describe soon in a follow-up blog post. Stay tuned!\n","title":"Supabase Vault","description":"Today we're announcing Vault, a Postgres extension for managing secrets and encryption inside your database.","author":"michel","image":"lw5-vault/supabase-vault.jpg","thumb":"lw5-vault/supabase-vault.jpg","categories":["product"],"tags":["launch-week"],"date":"2022-08-19","toc_depth":2,"content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    a: \"a\",\n    h2: \"h2\",\n    code: \"code\",\n    h3: \"h3\",\n    em: \"em\"\n  }, _provideComponents(), props.components), {CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Today we're announcing \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/database/vault\",\n        children: \"Supabase Vault\"\n      }), \", a Postgres extension for managing secrets and encryption inside your database.\\nVault is a thin usability-layer on top of \", _jsx(_components.a, {\n        href: \"https://github.com/michelp/pgsodium\",\n        children: \"pgsodium\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(\"div\", {\n      className: \"bg-gray-300 rounded-lg px-6 py-2 bold\",\n      children: [_jsx(_components.p, {\n        children: \"❇️ UPDATE JUNE 2023 ❇️\"\n      }), _jsxs(_components.p, {\n        children: [\"Vault is now available on every Supabase project. \", _jsx(_components.a, {\n          href: \"https://supabase.com/dashboard/project/_/settings/vault/secrets\",\n          children: \"Check it out\"\n        })]\n      })]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://supabase.com/blog/transparent-column-encryption-with-postgres\",\n        children: \"Transparent Column Encryption with Postgres\"\n      }), \" is a blog post that describes the technology behind Vault - \", _jsx(_components.a, {\n        href: \"https://doc.libsodium.org/\",\n        children: \"libsodium\"\n      }), \" and\\n\", _jsx(_components.a, {\n        href: \"https://doc.libsodium.org/\",\n        children: \"pgsodium\"\n      }), \". Now, we will go through a quick example of storing a secret, like a service access token, into the Vault.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"background\",\n      children: \"Background\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Until now, the industry-standard for PostgreSQL encryption is a built-in extension called \", _jsx(_components.a, {\n        href: \"https://www.postgresql.org/docs/current/pgcrypto.html\",\n        children: _jsx(_components.code, {\n          children: \"pgcrypto\"\n        })\n      }), \".\\nLike most cloud providers, Supabase offers \", _jsx(_components.code, {\n        children: \"pgcrypto\"\n      }), \" for developers to use in their applications. \", _jsx(_components.code, {\n        children: \"pgcrypto\"\n      }), \" has been around for a long time,\\nand while it supports some basic encryption and decryption abilities, it lacks features like public key signing, key derivation APIs, streaming encryption,\\nand other modern features required by security-first applications.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"problems-with-raw-keys\",\n      children: \"Problems with raw keys\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Databases often store sensitive information, and they need tools that guarantee this data is stored and backed-up in an encrypted form.\\nA fundamental issue with \", _jsx(_components.code, {\n        children: \"pgcrypto\"\n      }), \" is that it lacks the ability to derive keys from outside of SQL. Instead you must have the raw encryption key for the algorithm you wish to use:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"create\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" extension pgcrypto;\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"create table \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"users\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  id \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"serial primary key\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  email \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"varchar not null unique\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \");\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"insert into\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" users\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  (email)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"values\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  (pgp_sym_encrypt(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'<EMAIL>'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'s3kr3t_k3y'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")),\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  (pgp_sym_encrypt(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'<EMAIL>'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'s3kr3t_key'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"));\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"pgcrypto works with \", _jsx(_components.em, {\n        children: \"raw\"\n      }), \" keys. In order to encrypt the data with pgcrypto you must pass the key directly to the encryption function.\\nLeaking those raw keys is all too easy - logs, files, clients, tables, replication streams - you name it.\\nWouldn't it be great if you could encrypt data, but instead of specifying the raw key you reference it indirectly, like with a key ID?\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"supabase-vault\",\n      children: \"Supabase Vault\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Supabase Vault allows you to store secrets without leaking any sensitive information.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The Vault is a good place to put things like API keys, access tokens, and other secrets from external services that you need to access within your database.\\nThe core of the Supabase Vault is a table with some metadata and an encrypted text column where you can put your secrets and any metadata related to them.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We take the pain out of key management by pre-generating a unique, per-database key that is used by default - a “root” key - which is stored outside of the SQL language,\\naccessibly only internally in the Postgres server by the libsodium library. This root key is managed by the \", _jsx(_components.a, {\n        href: \"https://github.com/michelp/pgsodium\",\n        children: \"pgsodium\"\n      }), \" Postgres extension\\nwhen the server boots using \", _jsx(_components.a, {\n        href: \"https://github.com/michelp/pgsodium#server-key-management\",\n        children: \"Server Key Management.\"\n      })]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"pgsodium provides an Encryption and Key Derivation API based on the \", _jsx(_components.a, {\n        href: \"https://libsodium.gitbook.io/doc/\",\n        children: \"libsodium library\"\n      }), \" and can get it's root key from a\\nvariety of sources depending on how you configure it. Supabase generates and preserves your project's root key behind the scenes, so you don't have to worry about it.\\nIf you install pgsodium locally the default is to generate a random root key from the \", _jsx(_components.code, {\n        children: \"/dev/urandom\"\n      }), \" device which is then saved in a file in your Postgres data directory.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Installing the \", _jsx(_components.code, {\n        children: \"vault\"\n      }), \" extension is the same as any other Postgres extension:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"create\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" extension supabase_vault \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"with schema\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" vault;\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Once enabled, you can insert secrets into the \", _jsx(_components.code, {\n        children: \"vault.secrets\"\n      }), \" table:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"insert into \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"vault\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"secrets\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"secret\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \", associated)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"values\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'s3kr3t_k3y'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'This is the secret API service key.'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \");\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Now when you look in the \", _jsx(_components.code, {\n        children: \"vault.secrets\"\n      }), \" table, the secret is encrypted:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"select * from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"vault\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"secrets\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"hideCopy\"],\n        \"active\": \"hideCopy\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"hideCopy\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"-[ RECORD 1 ]--------------------------------------------------------\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"id         | f6a2fe0a-3471-4eea-a581-75c4d2be396b\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"secret     | /eT9bb96POTJ7L2gYrluTZ3r3pG5IMwPSQo6pQP0xdZTarpRrpWPXTWQ\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"key_id     | caabfc28-2ab3-48f5-8978-1b3d4b659911\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"associated | This is the secret API service key.\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"nonce      | \\\\x77c7381c523630ba72f1f137626a9f9a\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"created_at | 2022-08-18 19:33:15.312651+00\",\n              \"props\": {}\n            }]\n          }],\n          \"lang\": \"text\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Notice how the row has a \", _jsx(_components.code, {\n        children: \"key_id\"\n      }), \" column. This is the \", _jsx(_components.em, {\n        children: \"ID\"\n      }), \" of the internally derived key that is used to encrypt the secret, not the key itself.\\nThe actual raw key is not available to you in SQL, it is managed entirely outside of the SQL language in the Postgres server.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"At Supabase, we manage this key for your project automatically and generate a unique default Key ID for you in the \", _jsx(_components.code, {\n        children: \"secrets\"\n      }), \" table.\\nFor self-hosting, pgsodium \", _jsx(_components.a, {\n        href: \"https://github.com/michelp/pgsodium#server-key-management\",\n        children: \"supports a variety of ways\"\n      }), \" to place the root key into Postgres.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To see the decrypted data, there is a special view created called \", _jsx(_components.code, {\n        children: \"vault.decrypted_secrets\"\n      }), \":\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"select * from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"vault\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"decrypted_secrets\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"hideCopy\"],\n        \"active\": \"hideCopy\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"hideCopy\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"-[ RECORD 1 ]----+---------------------------------------------------------\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"id               | f6a2fe0a-3471-4eea-a581-75c4d2be396b\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"secret           | /eT9bb96POTJ7L2gYrluTZ3r3pG5IMwPSQo6pQP0xdZTarpRrpWPXTWQ\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"decrypted_secret | s3kr3t_k3y\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"key_id           | caabfc28-2ab3-48f5-8978-1b3d4b659911\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"associated       | This is the secret API service key.\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"nonce            | \\\\x77c7381c523630ba72f1f137626a9f9a\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"created_at       | 2022-08-18 19:33:15.312651+00\",\n              \"props\": {}\n            }]\n          }],\n          \"lang\": \"text\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Now you can see a new \", _jsx(_components.code, {\n        children: \"decrypted_secret\"\n      }), \" column that contains the decrypted secret we originally inserted into the table.\\nThis \", _jsx(_components.code, {\n        children: \"vault.decrypted_secrets\"\n      }), \" view automatically decrypts rows in the \", _jsx(_components.code, {\n        children: \"vault.secrets\"\n      }), \" table “on-the-fly” as you query them, but the secret is stored on disk in \", _jsx(_components.em, {\n        children: \"encrypted\"\n      }), \" form.\\nIf you take a backup, or pause your project, that data remains encrypted. We will keep your hidden root key safe in our backend systems for when you need to restore or un-pause your projects.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you wish to use your own Key ID for different secrets, instead of the default Key ID we've generated, you can create one using the \", _jsx(_components.code, {\n        children: \"pgsodium.create_key()\"\n      }), \" function:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"select * from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"pgsodium\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"create_key\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'This is a comment for the new key'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \");\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"hideCopy\"],\n        \"active\": \"hideCopy\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"hideCopy\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"-[ RECORD 1 ]-------------------------------------\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"id          | f9f176eb-7069-4743-9403-582c04354ffc\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"status      | valid\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"created     | 2022-08-18 22:31:50.331792\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"expires     |\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"key_type    | aead-det\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"key_id      | 2\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"key_context | \\\\x7067736f6469756d\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"comment     | This is the comment for the new key\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"user_data   |\",\n              \"props\": {}\n            }]\n          }],\n          \"lang\": \"text\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Now you can encrypt table secrets with this new key by inserting its ID explicitly:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"insert into \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"vault\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"secrets\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"secret\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \", associated, key_id)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"values\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    'apikey_XaYrurzcquqhEdBjzfTzfwAZqpd'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    'This is some different associated data.'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    'f9f176eb-7069-4743-9403-582c04354ffc'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  )\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"returning \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"*\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"hideCopy\"],\n        \"active\": \"hideCopy\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"hideCopy\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"-[ RECORD 1 ]------------------------------------------------------------\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"id         | 9c58a0f3-aa40-4789-b683-6db48b241f9e\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"secret     | YWxuTnWdF55MuRrZ7xneBvaz2uH59U1dJV/7CCZjSn5B5jELOoy/csq8x/s=\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"key_id     | f9f176eb-7069-4743-9403-582c04354ffc\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"associated | This is some different associated data.\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"nonce      | \\\\xd39808b07c9ae52c8f02c33a7f87595c\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"created_at | 2022-08-18 22:34:07.219941+00\",\n              \"props\": {}\n            }]\n          }],\n          \"lang\": \"text\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The type of encryption used by the Vault is called \", _jsx(_components.a, {\n        href: \"https://en.wikipedia.org/wiki/Authenticated_encryption\",\n        children: \"Authenticated Encryption with Associated Data\"\n      }), \".\\nThe data you insert into the \", _jsx(_components.code, {\n        children: \"associated\"\n      }), \" column, which is up to you, is combined with the encrypted text when libsodium creates the authentication signature for the secret.\\nThis means that when you read the secret, you know that the associated data is also authentic. The associated data could be an account ID or some\\ninformation that ties your system to the secret. And as always, you can refer to rows in the secrets table by their primary key UUID.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"If you only want to store secrets that you know are encrypted on disk and in backups, then all you need to know is shown above. Just insert secrets into the table,\\noptionally creating new keys, and select them from the view when you want to use them.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"going-beyond-the-vault\",\n      children: \"Going Beyond the Vault\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The Vault is good for a reasonable amount of secure data, like API keys, access tokens, or environment variables.\\nBut if you have a lot more sensitive information, like personally Identifiable Information (PII),\\nyou may want to break them out into side-tables using pgsodium's\\n\", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/transparent-column-encryption-with-postgres\",\n        children: \"Transparent Column Encryption\"\n      }), \" which we will describe soon in a follow-up blog post. Stay tuned!\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"Background","slug":"background","lvl":2,"i":0,"seen":0},{"content":"Problems with raw keys","slug":"problems-with-raw-keys","lvl":3,"i":1,"seen":0},{"content":"Supabase Vault","slug":"supabase-vault","lvl":2,"i":2,"seen":0},{"content":"Going Beyond the Vault","slug":"going-beyond-the-vault","lvl":2,"i":3,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,3],"level":0},{"type":"inline","content":"Today we're announcing [Supabase Vault](https://supabase.com/docs/guides/database/vault), a Postgres extension for managing secrets and encryption inside your database.\nVault is a thin usability-layer on top of [pgsodium](https://github.com/michelp/pgsodium).","level":1,"lines":[1,3],"children":[{"type":"text","content":"Today we're announcing ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/database/vault","title":"","level":0},{"type":"text","content":"Supabase Vault","level":1},{"type":"link_close","level":0},{"type":"text","content":", a Postgres extension for managing secrets and encryption inside your database.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Vault is a thin usability-layer on top of ","level":0},{"type":"link_open","href":"https://github.com/michelp/pgsodium","title":"","level":0},{"type":"text","content":"pgsodium","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[4,5],"level":0},{"type":"inline","content":"\u003cdiv className=\"bg-gray-300 rounded-lg px-6 py-2 bold\"\u003e","level":1,"lines":[4,5],"children":[{"type":"text","content":"\u003cdiv className=\"bg-gray-300 rounded-lg px-6 py-2 bold\"\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[6,7],"level":0},{"type":"inline","content":"❇️ UPDATE JUNE 2023 ❇️","level":1,"lines":[6,7],"children":[{"type":"text","content":"❇️ UPDATE JUNE 2023 ❇️","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[8,9],"level":0},{"type":"inline","content":"Vault is now available on every Supabase project. [Check it out](https://supabase.com/dashboard/project/_/settings/vault/secrets)","level":1,"lines":[8,9],"children":[{"type":"text","content":"Vault is now available on every Supabase project. ","level":0},{"type":"link_open","href":"https://supabase.com/dashboard/project/_/settings/vault/secrets","title":"","level":0},{"type":"text","content":"Check it out","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[10,11],"level":0},{"type":"inline","content":"\u003c/div\u003e","level":1,"lines":[10,11],"children":[{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[12,14],"level":0},{"type":"inline","content":"[Transparent Column Encryption with Postgres](https://supabase.com/blog/transparent-column-encryption-with-postgres) is a blog post that describes the technology behind Vault - [libsodium](https://doc.libsodium.org/) and\n[pgsodium](https://doc.libsodium.org/). Now, we will go through a quick example of storing a secret, like a service access token, into the Vault.","level":1,"lines":[12,14],"children":[{"type":"link_open","href":"https://supabase.com/blog/transparent-column-encryption-with-postgres","title":"","level":0},{"type":"text","content":"Transparent Column Encryption with Postgres","level":1},{"type":"link_close","level":0},{"type":"text","content":" is a blog post that describes the technology behind Vault - ","level":0},{"type":"link_open","href":"https://doc.libsodium.org/","title":"","level":0},{"type":"text","content":"libsodium","level":1},{"type":"link_close","level":0},{"type":"text","content":" and","level":0},{"type":"softbreak","level":0},{"type":"link_open","href":"https://doc.libsodium.org/","title":"","level":0},{"type":"text","content":"pgsodium","level":1},{"type":"link_close","level":0},{"type":"text","content":". Now, we will go through a quick example of storing a secret, like a service access token, into the Vault.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[15,16],"level":0},{"type":"inline","content":"[Background](#background)","level":1,"lines":[15,16],"children":[{"type":"text","content":"Background","level":0}],"lvl":2,"i":0,"seen":0,"slug":"background"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[17,21],"level":0},{"type":"inline","content":"Until now, the industry-standard for PostgreSQL encryption is a built-in extension called [`pgcrypto`](https://www.postgresql.org/docs/current/pgcrypto.html).\nLike most cloud providers, Supabase offers `pgcrypto` for developers to use in their applications. `pgcrypto` has been around for a long time,\nand while it supports some basic encryption and decryption abilities, it lacks features like public key signing, key derivation APIs, streaming encryption,\nand other modern features required by security-first applications.","level":1,"lines":[17,21],"children":[{"type":"text","content":"Until now, the industry-standard for PostgreSQL encryption is a built-in extension called ","level":0},{"type":"link_open","href":"https://www.postgresql.org/docs/current/pgcrypto.html","title":"","level":0},{"type":"code","content":"pgcrypto","block":false,"level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Like most cloud providers, Supabase offers ","level":0},{"type":"code","content":"pgcrypto","block":false,"level":0},{"type":"text","content":" for developers to use in their applications. ","level":0},{"type":"code","content":"pgcrypto","block":false,"level":0},{"type":"text","content":" has been around for a long time,","level":0},{"type":"softbreak","level":0},{"type":"text","content":"and while it supports some basic encryption and decryption abilities, it lacks features like public key signing, key derivation APIs, streaming encryption,","level":0},{"type":"softbreak","level":0},{"type":"text","content":"and other modern features required by security-first applications.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[22,23],"level":0},{"type":"inline","content":"[Problems with raw keys](#problems-with-raw-keys)","level":1,"lines":[22,23],"children":[{"type":"text","content":"Problems with raw keys","level":0}],"lvl":3,"i":1,"seen":0,"slug":"problems-with-raw-keys"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[24,26],"level":0},{"type":"inline","content":"Databases often store sensitive information, and they need tools that guarantee this data is stored and backed-up in an encrypted form.\nA fundamental issue with `pgcrypto` is that it lacks the ability to derive keys from outside of SQL. Instead you must have the raw encryption key for the algorithm you wish to use:","level":1,"lines":[24,26],"children":[{"type":"text","content":"Databases often store sensitive information, and they need tools that guarantee this data is stored and backed-up in an encrypted form.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"A fundamental issue with ","level":0},{"type":"code","content":"pgcrypto","block":false,"level":0},{"type":"text","content":" is that it lacks the ability to derive keys from outside of SQL. Instead you must have the raw encryption key for the algorithm you wish to use:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql","content":"create extension pgcrypto;\n\ncreate table users (\n  id serial primary key,\n  email varchar not null unique\n);\n\ninsert into users\n  (email)\nvalues\n  (pgp_sym_encrypt('<EMAIL>', 's3kr3t_k3y')),\n  (pgp_sym_encrypt('<EMAIL>', 's3kr3t_key'));\n","lines":[27,41],"level":0},{"type":"paragraph_open","tight":false,"lines":[42,45],"level":0},{"type":"inline","content":"pgcrypto works with _raw_ keys. In order to encrypt the data with pgcrypto you must pass the key directly to the encryption function.\nLeaking those raw keys is all too easy - logs, files, clients, tables, replication streams - you name it.\nWouldn't it be great if you could encrypt data, but instead of specifying the raw key you reference it indirectly, like with a key ID?","level":1,"lines":[42,45],"children":[{"type":"text","content":"pgcrypto works with ","level":0},{"type":"em_open","level":0},{"type":"text","content":"raw","level":1},{"type":"em_close","level":0},{"type":"text","content":" keys. In order to encrypt the data with pgcrypto you must pass the key directly to the encryption function.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Leaking those raw keys is all too easy - logs, files, clients, tables, replication streams - you name it.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Wouldn't it be great if you could encrypt data, but instead of specifying the raw key you reference it indirectly, like with a key ID?","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[46,47],"level":0},{"type":"inline","content":"[Supabase Vault](#supabase-vault)","level":1,"lines":[46,47],"children":[{"type":"text","content":"Supabase Vault","level":0}],"lvl":2,"i":2,"seen":0,"slug":"supabase-vault"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[48,49],"level":0},{"type":"inline","content":"Supabase Vault allows you to store secrets without leaking any sensitive information.","level":1,"lines":[48,49],"children":[{"type":"text","content":"Supabase Vault allows you to store secrets without leaking any sensitive information.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[50,52],"level":0},{"type":"inline","content":"The Vault is a good place to put things like API keys, access tokens, and other secrets from external services that you need to access within your database.\nThe core of the Supabase Vault is a table with some metadata and an encrypted text column where you can put your secrets and any metadata related to them.","level":1,"lines":[50,52],"children":[{"type":"text","content":"The Vault is a good place to put things like API keys, access tokens, and other secrets from external services that you need to access within your database.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"The core of the Supabase Vault is a table with some metadata and an encrypted text column where you can put your secrets and any metadata related to them.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[53,56],"level":0},{"type":"inline","content":"We take the pain out of key management by pre-generating a unique, per-database key that is used by default - a “root” key - which is stored outside of the SQL language,\naccessibly only internally in the Postgres server by the libsodium library. This root key is managed by the [pgsodium](https://github.com/michelp/pgsodium) Postgres extension\nwhen the server boots using [Server Key Management.](https://github.com/michelp/pgsodium#server-key-management)","level":1,"lines":[53,56],"children":[{"type":"text","content":"We take the pain out of key management by pre-generating a unique, per-database key that is used by default - a “root” key - which is stored outside of the SQL language,","level":0},{"type":"softbreak","level":0},{"type":"text","content":"accessibly only internally in the Postgres server by the libsodium library. This root key is managed by the ","level":0},{"type":"link_open","href":"https://github.com/michelp/pgsodium","title":"","level":0},{"type":"text","content":"pgsodium","level":1},{"type":"link_close","level":0},{"type":"text","content":" Postgres extension","level":0},{"type":"softbreak","level":0},{"type":"text","content":"when the server boots using ","level":0},{"type":"link_open","href":"https://github.com/michelp/pgsodium#server-key-management","title":"","level":0},{"type":"text","content":"Server Key Management.","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[57,60],"level":0},{"type":"inline","content":"pgsodium provides an Encryption and Key Derivation API based on the [libsodium library](https://libsodium.gitbook.io/doc/) and can get it's root key from a\nvariety of sources depending on how you configure it. Supabase generates and preserves your project's root key behind the scenes, so you don't have to worry about it.\nIf you install pgsodium locally the default is to generate a random root key from the `/dev/urandom` device which is then saved in a file in your Postgres data directory.","level":1,"lines":[57,60],"children":[{"type":"text","content":"pgsodium provides an Encryption and Key Derivation API based on the ","level":0},{"type":"link_open","href":"https://libsodium.gitbook.io/doc/","title":"","level":0},{"type":"text","content":"libsodium library","level":1},{"type":"link_close","level":0},{"type":"text","content":" and can get it's root key from a","level":0},{"type":"softbreak","level":0},{"type":"text","content":"variety of sources depending on how you configure it. Supabase generates and preserves your project's root key behind the scenes, so you don't have to worry about it.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"If you install pgsodium locally the default is to generate a random root key from the ","level":0},{"type":"code","content":"/dev/urandom","block":false,"level":0},{"type":"text","content":" device which is then saved in a file in your Postgres data directory.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[61,62],"level":0},{"type":"inline","content":"Installing the `vault` extension is the same as any other Postgres extension:","level":1,"lines":[61,62],"children":[{"type":"text","content":"Installing the ","level":0},{"type":"code","content":"vault","block":false,"level":0},{"type":"text","content":" extension is the same as any other Postgres extension:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql","content":"create extension supabase_vault with schema vault;\n","lines":[63,66],"level":0},{"type":"paragraph_open","tight":false,"lines":[67,68],"level":0},{"type":"inline","content":"Once enabled, you can insert secrets into the `vault.secrets` table:","level":1,"lines":[67,68],"children":[{"type":"text","content":"Once enabled, you can insert secrets into the ","level":0},{"type":"code","content":"vault.secrets","block":false,"level":0},{"type":"text","content":" table:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql","content":"insert into vault.secrets\n  (secret, associated)\nvalues\n  ('s3kr3t_k3y', 'This is the secret API service key.');\n","lines":[69,75],"level":0},{"type":"paragraph_open","tight":false,"lines":[76,77],"level":0},{"type":"inline","content":"Now when you look in the `vault.secrets` table, the secret is encrypted:","level":1,"lines":[76,77],"children":[{"type":"text","content":"Now when you look in the ","level":0},{"type":"code","content":"vault.secrets","block":false,"level":0},{"type":"text","content":" table, the secret is encrypted:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql","content":"select * from vault.secrets;\n","lines":[78,81],"level":0},{"type":"fence","params":"text hideCopy","content":"-[ RECORD 1 ]--------------------------------------------------------\nid         | f6a2fe0a-3471-4eea-a581-75c4d2be396b\nsecret     | /eT9bb96POTJ7L2gYrluTZ3r3pG5IMwPSQo6pQP0xdZTarpRrpWPXTWQ\nkey_id     | caabfc28-2ab3-48f5-8978-1b3d4b659911\nassociated | This is the secret API service key.\nnonce      | \\x77c7381c523630ba72f1f137626a9f9a\ncreated_at | 2022-08-18 19:33:15.312651+00\n","lines":[82,91],"level":0},{"type":"paragraph_open","tight":false,"lines":[92,94],"level":0},{"type":"inline","content":"Notice how the row has a `key_id` column. This is the _ID_ of the internally derived key that is used to encrypt the secret, not the key itself.\nThe actual raw key is not available to you in SQL, it is managed entirely outside of the SQL language in the Postgres server.","level":1,"lines":[92,94],"children":[{"type":"text","content":"Notice how the row has a ","level":0},{"type":"code","content":"key_id","block":false,"level":0},{"type":"text","content":" column. This is the ","level":0},{"type":"em_open","level":0},{"type":"text","content":"ID","level":1},{"type":"em_close","level":0},{"type":"text","content":" of the internally derived key that is used to encrypt the secret, not the key itself.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"The actual raw key is not available to you in SQL, it is managed entirely outside of the SQL language in the Postgres server.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[95,97],"level":0},{"type":"inline","content":"At Supabase, we manage this key for your project automatically and generate a unique default Key ID for you in the `secrets` table.\nFor self-hosting, pgsodium [supports a variety of ways](https://github.com/michelp/pgsodium#server-key-management) to place the root key into Postgres.","level":1,"lines":[95,97],"children":[{"type":"text","content":"At Supabase, we manage this key for your project automatically and generate a unique default Key ID for you in the ","level":0},{"type":"code","content":"secrets","block":false,"level":0},{"type":"text","content":" table.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"For self-hosting, pgsodium ","level":0},{"type":"link_open","href":"https://github.com/michelp/pgsodium#server-key-management","title":"","level":0},{"type":"text","content":"supports a variety of ways","level":1},{"type":"link_close","level":0},{"type":"text","content":" to place the root key into Postgres.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[98,99],"level":0},{"type":"inline","content":"To see the decrypted data, there is a special view created called `vault.decrypted_secrets`:","level":1,"lines":[98,99],"children":[{"type":"text","content":"To see the decrypted data, there is a special view created called ","level":0},{"type":"code","content":"vault.decrypted_secrets","block":false,"level":0},{"type":"text","content":":","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql","content":"select * from vault.decrypted_secrets;\n","lines":[100,103],"level":0},{"type":"fence","params":"text hideCopy","content":"-[ RECORD 1 ]----+---------------------------------------------------------\nid               | f6a2fe0a-3471-4eea-a581-75c4d2be396b\nsecret           | /eT9bb96POTJ7L2gYrluTZ3r3pG5IMwPSQo6pQP0xdZTarpRrpWPXTWQ\ndecrypted_secret | s3kr3t_k3y\nkey_id           | caabfc28-2ab3-48f5-8978-1b3d4b659911\nassociated       | This is the secret API service key.\nnonce            | \\x77c7381c523630ba72f1f137626a9f9a\ncreated_at       | 2022-08-18 19:33:15.312651+00\n","lines":[104,114],"level":0},{"type":"paragraph_open","tight":false,"lines":[115,118],"level":0},{"type":"inline","content":"Now you can see a new `decrypted_secret` column that contains the decrypted secret we originally inserted into the table.\nThis `vault.decrypted_secrets` view automatically decrypts rows in the `vault.secrets` table “on-the-fly” as you query them, but the secret is stored on disk in _encrypted_ form.\nIf you take a backup, or pause your project, that data remains encrypted. We will keep your hidden root key safe in our backend systems for when you need to restore or un-pause your projects.","level":1,"lines":[115,118],"children":[{"type":"text","content":"Now you can see a new ","level":0},{"type":"code","content":"decrypted_secret","block":false,"level":0},{"type":"text","content":" column that contains the decrypted secret we originally inserted into the table.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"This ","level":0},{"type":"code","content":"vault.decrypted_secrets","block":false,"level":0},{"type":"text","content":" view automatically decrypts rows in the ","level":0},{"type":"code","content":"vault.secrets","block":false,"level":0},{"type":"text","content":" table “on-the-fly” as you query them, but the secret is stored on disk in ","level":0},{"type":"em_open","level":0},{"type":"text","content":"encrypted","level":1},{"type":"em_close","level":0},{"type":"text","content":" form.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"If you take a backup, or pause your project, that data remains encrypted. We will keep your hidden root key safe in our backend systems for when you need to restore or un-pause your projects.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[119,120],"level":0},{"type":"inline","content":"If you wish to use your own Key ID for different secrets, instead of the default Key ID we've generated, you can create one using the `pgsodium.create_key()` function:","level":1,"lines":[119,120],"children":[{"type":"text","content":"If you wish to use your own Key ID for different secrets, instead of the default Key ID we've generated, you can create one using the ","level":0},{"type":"code","content":"pgsodium.create_key()","block":false,"level":0},{"type":"text","content":" function:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql","content":"select * from pgsodium.create_key('This is a comment for the new key');\n","lines":[121,124],"level":0},{"type":"fence","params":"text hideCopy","content":"-[ RECORD 1 ]-------------------------------------\nid          | f9f176eb-7069-4743-9403-582c04354ffc\nstatus      | valid\ncreated     | 2022-08-18 22:31:50.331792\nexpires     |\nkey_type    | aead-det\nkey_id      | 2\nkey_context | \\x7067736f6469756d\ncomment     | This is the comment for the new key\nuser_data   |\n","lines":[125,137],"level":0},{"type":"paragraph_open","tight":false,"lines":[138,139],"level":0},{"type":"inline","content":"Now you can encrypt table secrets with this new key by inserting its ID explicitly:","level":1,"lines":[138,139],"children":[{"type":"text","content":"Now you can encrypt table secrets with this new key by inserting its ID explicitly:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql","content":"insert into vault.secrets\n  (secret, associated, key_id)\nvalues\n  (\n    'apikey_XaYrurzcquqhEdBjzfTzfwAZqpd',\n    'This is some different associated data.',\n    'f9f176eb-7069-4743-9403-582c04354ffc'\n  )\nreturning *;\n","lines":[140,151],"level":0},{"type":"fence","params":"text hideCopy","content":"-[ RECORD 1 ]------------------------------------------------------------\nid         | 9c58a0f3-aa40-4789-b683-6db48b241f9e\nsecret     | YWxuTnWdF55MuRrZ7xneBvaz2uH59U1dJV/7CCZjSn5B5jELOoy/csq8x/s=\nkey_id     | f9f176eb-7069-4743-9403-582c04354ffc\nassociated | This is some different associated data.\nnonce      | \\xd39808b07c9ae52c8f02c33a7f87595c\ncreated_at | 2022-08-18 22:34:07.219941+00\n","lines":[152,161],"level":0},{"type":"paragraph_open","tight":false,"lines":[162,166],"level":0},{"type":"inline","content":"The type of encryption used by the Vault is called [Authenticated Encryption with Associated Data](https://en.wikipedia.org/wiki/Authenticated_encryption).\nThe data you insert into the `associated` column, which is up to you, is combined with the encrypted text when libsodium creates the authentication signature for the secret.\nThis means that when you read the secret, you know that the associated data is also authentic. The associated data could be an account ID or some\ninformation that ties your system to the secret. And as always, you can refer to rows in the secrets table by their primary key UUID.","level":1,"lines":[162,166],"children":[{"type":"text","content":"The type of encryption used by the Vault is called ","level":0},{"type":"link_open","href":"https://en.wikipedia.org/wiki/Authenticated_encryption","title":"","level":0},{"type":"text","content":"Authenticated Encryption with Associated Data","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0},{"type":"softbreak","level":0},{"type":"text","content":"The data you insert into the ","level":0},{"type":"code","content":"associated","block":false,"level":0},{"type":"text","content":" column, which is up to you, is combined with the encrypted text when libsodium creates the authentication signature for the secret.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"This means that when you read the secret, you know that the associated data is also authentic. The associated data could be an account ID or some","level":0},{"type":"softbreak","level":0},{"type":"text","content":"information that ties your system to the secret. And as always, you can refer to rows in the secrets table by their primary key UUID.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[167,169],"level":0},{"type":"inline","content":"If you only want to store secrets that you know are encrypted on disk and in backups, then all you need to know is shown above. Just insert secrets into the table,\noptionally creating new keys, and select them from the view when you want to use them.","level":1,"lines":[167,169],"children":[{"type":"text","content":"If you only want to store secrets that you know are encrypted on disk and in backups, then all you need to know is shown above. Just insert secrets into the table,","level":0},{"type":"softbreak","level":0},{"type":"text","content":"optionally creating new keys, and select them from the view when you want to use them.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[170,171],"level":0},{"type":"inline","content":"[Going Beyond the Vault](#going-beyond-the-vault)","level":1,"lines":[170,171],"children":[{"type":"text","content":"Going Beyond the Vault","level":0}],"lvl":2,"i":3,"seen":0,"slug":"going-beyond-the-vault"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[172,176],"level":0},{"type":"inline","content":"The Vault is good for a reasonable amount of secure data, like API keys, access tokens, or environment variables.\nBut if you have a lot more sensitive information, like personally Identifiable Information (PII),\nyou may want to break them out into side-tables using pgsodium's\n[Transparent Column Encryption](https://supabase.com/blog/transparent-column-encryption-with-postgres) which we will describe soon in a follow-up blog post. Stay tuned!","level":1,"lines":[172,176],"children":[{"type":"text","content":"The Vault is good for a reasonable amount of secure data, like API keys, access tokens, or environment variables.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"But if you have a lot more sensitive information, like personally Identifiable Information (PII),","level":0},{"type":"softbreak","level":0},{"type":"text","content":"you may want to break them out into side-tables using pgsodium's","level":0},{"type":"softbreak","level":0},{"type":"link_open","href":"https://supabase.com/blog/transparent-column-encryption-with-postgres","title":"","level":0},{"type":"text","content":"Transparent Column Encryption","level":1},{"type":"link_close","level":0},{"type":"text","content":" which we will describe soon in a follow-up blog post. Stay tuned!","level":0}]},{"type":"paragraph_close","tight":false,"level":0}],"content":"- [Background](#background)\n- [Supabase Vault](#supabase-vault)\n- [Going Beyond the Vault](#going-beyond-the-vault)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-vault"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>