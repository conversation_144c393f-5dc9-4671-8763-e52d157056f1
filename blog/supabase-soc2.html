<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase is SOC2 compliant</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Supabase is now SOC2 compliant. Learn how we got here and what it means for our customers." data-next-head=""/><meta property="og:title" content="Supabase is SOC2 compliant" data-next-head=""/><meta property="og:description" content="Supabase is now SOC2 compliant. Learn how we got here and what it means for our customers." data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-soc2" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2022-08-17" data-next-head=""/><meta property="article:author" content="https://twitter.com/everConfusedGuy" data-next-head=""/><meta property="article:author" content="https://github.com/j0" data-next-head=""/><meta property="article:tag" content="launch-week" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/lw5-soc2/thumb.jpg" data-next-head=""/><meta property="og:image:alt" content="Supabase is SOC2 compliant thumbnail" data-next-head=""/><meta property="og:video" content="https://www.youtube.com/v/6bGQotxisoY" data-next-head=""/><meta property="og:video:type" content="application/x-shockwave-flash" data-next-head=""/><meta property="og:video:width" content="640" data-next-head=""/><meta property="og:video:height" content="385" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Supabase is SOC2 compliant</h1><div class="text-light flex space-x-3 text-sm"><p>17 Aug 2022</p><p>•</p><p>9 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://twitter.com/everConfusedGuy"><div class="flex items-center gap-3"><div class="w-10"><img alt="Inian Parameshwaran avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Favatars.githubusercontent.com%2Fu%2F2155545%3Fv%3D4&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Favatars.githubusercontent.com%2Fu%2F2155545%3Fv%3D4&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Favatars.githubusercontent.com%2Fu%2F2155545%3Fv%3D4&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Inian Parameshwaran</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/j0"><div class="flex items-center gap-3"><div class="w-10"><img alt="Joel Lee avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fj0.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fj0.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fj0.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Joel Lee</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Supabase is SOC2 compliant" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-soc2%2Fthumb.jpg&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-soc2%2Fthumb.jpg&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-soc2%2Fthumb.jpg&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-soc2%2Fthumb.jpg&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-soc2%2Fthumb.jpg&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-soc2%2Fthumb.jpg&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-soc2%2Fthumb.jpg&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-soc2%2Fthumb.jpg&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-soc2%2Fthumb.jpg&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-soc2%2Fthumb.jpg&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flw5-soc2%2Fthumb.jpg&amp;w=3840&amp;q=100"/></div><p>Update (2023-05-22): Supabase is now SOC2 Type 2 compliant. Read more about security at Supabase in our <a href="../security.html">Security Center</a>.</p>
<hr/>
<p>Supabase is now SOC2 Type 1 compliant. Let’s dig into what that means, and explain the process we went through to get there. If you’re building a SaaS product, this blog post should provide a rough guide to getting your SOC2 certification.</p>
<h2 id="what-is-soc2" class="group scroll-mt-24">What is SOC2?<a href="#what-is-soc2" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>SOC2 is a compliance standard developed by the American Institute of CPAs. Yup CPAs - a bunch of accountants came up with the generally accepted compliance security standard for SaaS companies. SOC2 evaluates companies on 5 Trust Services Criteria: security, availability, processing integrity, confidentiality, and privacy. There are two types of compliance, both requiring an external audit:</p>
<ul>
<li>Type 1: checks if a company is compliant at a point in time.</li>
<li>Type 2: checks if a company stays compliant during an observation window (typically 6 months to a year).</li>
</ul>
<h2 id="why-did-we-get-soc2-certified" class="group scroll-mt-24">Why did we get SOC2 certified?<a href="#why-did-we-get-soc2-certified" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>The main reason to go through a SOC2 audit is a simple one - customers expect it.</p>
<p>The Supabase team was spending a considerable amount of time filling out security questionnaires and fielding calls with potential customers - walking them through our security practices and how we protect customer data.</p>
<p>We are a database company — customers trust us with their data. We started with a SOC2 audit since one of the main things covered is our handling of sensitive customer data.</p>
<p>It is also much easier to become-and remain-SOC2 compliant when you institute the appropriate practices and standards as early as possible.</p>
<h2 id="choosing-a-compliance-monitoring-tool" class="group scroll-mt-24">Choosing a compliance monitoring tool<a href="#choosing-a-compliance-monitoring-tool" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>There are a few tools that plug into your cloud providers, task management system, identity, and HR providers and make it easy to monitor, collect and submit evidence to auditors. We evaluated <a href="https://www.vanta.com/">Vanta</a>, <a href="https://drata.com/">Drata</a>, <a href="https://secureframe.com/">Secureframe</a>, and <a href="https://tugboatlogic.com/">Tugboat Logic</a>. Our stack is pretty standard (AWS, GCP, GSuite, and Github). All the tools we evaluated were integrated with these systems.</p>
<p>Some of them had overly broad permissions when integrating with our vendors (read/write access to Github for example) which made them a no-go. Some came with a lightweight agent that we could use for Mobile Device Management (MDM). Other than that, the products are largely the same.</p>
<p>We chose Vanta, with Drata coming in a close second. Vanta supported more compliance standards (at that time) and is a fellow YC company (which sealed the deal). The gap between these tools is much closer now since all of them support the compliance standards we have our eyes on.</p>
<h2 id="choosing-an-auditor" class="group scroll-mt-24">Choosing an auditor<a href="#choosing-an-auditor" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>After setting up Vanta (nb: pestering devs to install a security agent on their laptop) and working through the checklist of tasks required for SOC2, we started looking around for auditors. Vanta was helpful here - they made introductions to a few auditors and we chose one who has previously worked with a lot of SaaS companies. Our auditors were also familiar with how Vanta worked which made the audit easier for us.</p>
<p>The engagement started with a call where we reviewed the systems which are in scope for the audit. We then set a date at which the audit takes place. The Type 1 audit verifies that we are adhering to our stated policies at this point in time.</p>
<h2 id="off-to-the-races" class="group scroll-mt-24">Off to the races<a href="#off-to-the-races" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Once the date for the audit was set, it was a mad scramble to get everything in order.</p>
<p>Vanta has a lightweight, read-only agent built on top of <a href="https://github.com/osquery/osquery">osquery</a> to verify if all team members have important security features enabled: hard disk encryption, screen locks, etc. It supports Mac, Windows, and Linux, although we had <a href="https://github.com/system76/firmware-open/issues/268">some issues</a> with the agent running correctly in Linux. Getting hard disk encryption right is still difficult in some Linux distros. We decided to standardize on Mac and Windows to sidestep these issues. A more powerful MDM would enable us to remote wipe computers in case it is stolen, but we decided that the existing protections we had in place were sufficient to defend against this scenario.</p>
<p>We came up with a few important policies. For example, Access Control Policy, Data Management Policy, Asset Management Policy, etc. Vanta has templates which we used as a starting point and adapted to our requirements. Since our auditors are familiar with the Vanta templates, there wasn’t much back and forth regarding the policy language.</p>
<p>For employee background checks, we use <a href="https://certn.co/">Certn</a>. They integrate with Vanta, making it easy to check the status within the Vanta portal itself. Background checks aren’t <em>required</em> for SOC2, but they are a factor in deciding whom we hire especially in certain roles like SRE and Finance where there is little room for error.</p>
<p>We submitted a ton of evidence to prove that we can stay on top of all activities in our cloud. We use <a href="https://vector.dev/">Vector</a> to ship logs to <a href="http://logflare.com/">Logflare</a>, where they are retained at least for a year. Our <a href="https://supabase.com/blog/supabase-reports-and-metrics">monitoring stack</a> consists of regional Victoria Metrics clusters and metrics are then aggregated into a centralized cluster. We also read from Cloudtrail logs and get notified in Slack when certain events like the root AWS account is logged into, a bucket is made public, an IAM policy changes, etc.</p>
<p>Enabling GitHub’s branch protection protects your repos from merging code without receiving a review first. This can be a challenge sometimes - at Supabase we have a few one-person teams. We had to relax our policy for some repos (POCs, internal projects) while keeping it strict for repos that handle critical systems.</p>
<p>Other tasks were already handled - encrypting data at rest and in transit, enforcing MFA where possible, standardizing on a password manager, and not using long-lived SSH keys (we use <a href="https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/Connect-using-EC2-Instance-Connect.html">AWS Instance Connect</a>), etc.</p>
<p>We also evaluated all our vendors who had access to confidential data and now it was our turn to send them questionnaires or ask them for their SOC2 reports 😈.</p>
<h2 id="making-the-soc2-process-work-for-you" class="group scroll-mt-24">Making the SOC2 process work for you<a href="#making-the-soc2-process-work-for-you" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We went through a lot of effort to make sure we weren’t adding processes just because it’s a requirement for SOC2. Everything had to improve our security or risk threshold in a meaningful way. This usually involves back and forth with the auditor to show how you satisfy a particular control even though you may not be doing it in exactly the way they want you to.</p>
<p>We used the SOC2 process to codify the security practices we were already doing informally at Supabase.</p>
<p>There are a few more debatable “check-the-box” activities - like the security awareness training which I couldn’t find a way around as much as I tried. Our team has a pretty good security posture and we don’t think watching a video on why reusing passwords is a bad idea helps much.</p>
<h2 id="a-few-surprises" class="group scroll-mt-24">A few surprises<a href="#a-few-surprises" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We were surprised by the amount of manual evidence that we had to collect (umm..take screenshots) even with a tool like Vanta. There are things an automated tool like Vanta can never fully automate. For example, we had to collect evidence to show that have proper on-boarding and off-boarding checklists or demonstrate how we resolve security issues reported by our penetration tests within a certain time frame.</p>
<p>Since the Type 1 audit is just about providing evidence at a single point in time, there isn’t <em>that</em> much utility in using a tool like Vanta. However, for Type 2 certification, where we need to show evidence that we have been compliant over a period of time, a monitoring tool like Vanta becomes more useful. Additionally, if you’re planning to get multiple certifications, investing in a compliance monitoring tool is valuable since there is considerable overlap between different frameworks, and you’ll only need to do the work of collecting your evidence once.</p>
<p>We spent some time trying to get a perfect score in Vanta before reaching out to the auditor. That turned out to be a mistake; Vanta is fairly broad in its checks, but the ones you need to pass depend on the audit’s scope, and the auditor. For example, our auditor didn’t care about seeing our exact board meeting minutes, but Vanta wanted us to document it!</p>
<p>Another revelation was that a lot of controls other companies had talked about in their SOC2 compliance journeys ended up not being a blocker for us. I am not going to list them here since it is dependent on the systems that are in scope, your larger architecture, and your auditor. Again, the takeaway here is to reach out to your auditor as early as possible to get a sense of what is relevant to your audit.</p>
<p>Auditors aren’t adversarial like pentesters or bug bounty hunters. For example, if you point them to a PR and say that PR closes a security issue reported, they <em>trust</em> that it did. They aren’t going to find a way to bypass the fix you implemented and use that to raise an exception in your audit. This is why getting SOC2 certified is just a small part of staying secure - what makes your auditor happy will likely be pretty different from what makes a penetration tester happy.</p>
<h2 id="security-center" class="group scroll-mt-24">Security Center<a href="#security-center" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Today we are launching our <a href="../security.html">security center</a>, which shows the steps we take to protect your data. You can also find our audit reports at our security portal <a href="https://security.supabase.com">here</a>, or reach out to <a href="mailto:<EMAIL>"><EMAIL></a></p>
<h2 id="whats-next" class="group scroll-mt-24">What’s next<a href="#whats-next" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Getting the SOC2 certification is just the start, and we will be working on getting certified for HIPAA next. If you are interested in discussing these, we would love to chat. We will also be rolling out a series of security improvements to all projects hosted on the Supabase Cloud offering over the coming months. Stay tuned!</p>
<h2 id="announcement-video-and-discussion" class="group scroll-mt-24">Announcement video and discussion<a href="#announcement-video-and-discussion" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<div class="video-container"><iframe class="w-full" src="https://www.youtube-nocookie.com/embed/6bGQotxisoY" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"></iframe></div>
<h2 id="more-launch-week-5" class="group scroll-mt-24">More Launch Week 5<a href="#more-launch-week-5" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ul>
<li><a href="../launch-week.html">Launch Week Page</a></li>
<li><a href="https://supabase.com/blog/launch-week-5-hackathon">Launch Week 5 Hackathon</a></li>
<li><a href="https://supabase.com/blog/supabase-cli-v1-and-admin-api-beta">Day 1 - Supabase CLI v1 and Management API Beta</a></li>
<li><a href="https://www.youtube.com/watch?v=OpPOaJI_Z28&amp;feature=emb_title">Youtube video - Supabase CLI v1 and Management API Beta</a></li>
<li><a href="supabase-js-v2.html">Day 2 - supabase-js v2 Release Candidate</a></li>
<li><a href="https://www.youtube.com/watch?v=iqZlPtl_b-I">Youtube Video - supabase-js v2 Release Candidate</a></li>
</ul></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-soc2&amp;text=Supabase%20is%20SOC2%20compliant"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-soc2&amp;text=Supabase%20is%20SOC2%20compliant"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-soc2&amp;t=Supabase%20is%20SOC2%20compliant"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="supabase-realtime-multiplayer-general-availability.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Realtime: Multiplayer Edition</h4><p class="small">18 August 2022</p></div></div></div></div></a></div><div><a href="supabase-js-v2.html"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">supabase-js v2</h4><p class="small">16 August 2022</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/launch-week"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">launch-week</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#what-is-soc2">What is SOC2?</a></li>
<li><a href="#why-did-we-get-soc2-certified">Why did we get SOC2 certified?</a></li>
<li><a href="#choosing-a-compliance-monitoring-tool">Choosing a compliance monitoring tool</a></li>
<li><a href="#choosing-an-auditor">Choosing an auditor</a></li>
<li><a href="#off-to-the-races">Off to the races</a></li>
<li><a href="#making-the-soc2-process-work-for-you">Making the SOC2 process work for you</a></li>
<li><a href="#a-few-surprises">A few surprises</a></li>
<li><a href="#security-center">Security Center</a></li>
<li><a href="#whats-next">What’s next</a></li>
<li><a href="#announcement-video-and-discussion">Announcement video and discussion</a></li>
<li><a href="#more-launch-week-5">More Launch Week 5</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-soc2&amp;text=Supabase%20is%20SOC2%20compliant"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-soc2&amp;text=Supabase%20is%20SOC2%20compliant"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-soc2&amp;t=Supabase%20is%20SOC2%20compliant"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"supabase-realtime-multiplayer-general-availability","title":"Realtime: Multiplayer Edition","description":"Announcing the general availability of Realtime's Broadcast and Presence.","author":"wenbo,stas","image":"lw5-realtime/thumb.jpg","thumb":"lw5-realtime/thumb.jpg","categories":["product"],"tags":["launch-week"],"date":"2022-08-18","toc_depth":3,"video":"https://www.youtube.com/v/CGZr5tybW18","formattedDate":"18 August 2022","readingTime":"10 minute read","url":"/blog/supabase-realtime-multiplayer-general-availability","path":"/blog/supabase-realtime-multiplayer-general-availability"},"nextPost":{"slug":"supabase-js-v2","title":"supabase-js v2","description":"A look at supabase-js v2, which brings type support and focuses on quality-of-life improvements for developers.","author":"inian,alaister","image":"lw5-supabase-js/thumb.jpg","thumb":"lw5-supabase-js/thumb.jpg","categories":["product"],"tags":["launch-week"],"date":"2022-08-16","toc_depth":3,"video":"https://www.youtube.com/v/iqZlPtl_b-I","formattedDate":"16 August 2022","readingTime":"8 minute read","url":"/blog/supabase-js-v2","path":"/blog/supabase-js-v2"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-soc2","source":"\nUpdate (2023-05-22): Supabase is now SOC2 Type 2 compliant. Read more about security at Supabase in our [Security Center](https://supabase.com/security).\n\n---\n\nSupabase is now SOC2 Type 1 compliant. Let’s dig into what that means, and explain the process we went through to get there. If you’re building a SaaS product, this blog post should provide a rough guide to getting your SOC2 certification.\n\n## What is SOC2?\n\nSOC2 is a compliance standard developed by the American Institute of CPAs. Yup CPAs - a bunch of accountants came up with the generally accepted compliance security standard for SaaS companies. SOC2 evaluates companies on 5 Trust Services Criteria: security, availability, processing integrity, confidentiality, and privacy. There are two types of compliance, both requiring an external audit:\n\n- Type 1: checks if a company is compliant at a point in time.\n- Type 2: checks if a company stays compliant during an observation window (typically 6 months to a year).\n\n## Why did we get SOC2 certified?\n\nThe main reason to go through a SOC2 audit is a simple one - customers expect it.\n\nThe Supabase team was spending a considerable amount of time filling out security questionnaires and fielding calls with potential customers - walking them through our security practices and how we protect customer data.\n\nWe are a database company — customers trust us with their data. We started with a SOC2 audit since one of the main things covered is our handling of sensitive customer data.\n\nIt is also much easier to become-and remain-SOC2 compliant when you institute the appropriate practices and standards as early as possible.\n\n## Choosing a compliance monitoring tool\n\nThere are a few tools that plug into your cloud providers, task management system, identity, and HR providers and make it easy to monitor, collect and submit evidence to auditors. We evaluated [Vanta](https://www.vanta.com/), [Drata](https://drata.com/), [Secureframe](https://secureframe.com/), and [Tugboat Logic](https://tugboatlogic.com/). Our stack is pretty standard (AWS, GCP, GSuite, and Github). All the tools we evaluated were integrated with these systems.\n\nSome of them had overly broad permissions when integrating with our vendors (read/write access to Github for example) which made them a no-go. Some came with a lightweight agent that we could use for Mobile Device Management (MDM). Other than that, the products are largely the same.\n\nWe chose Vanta, with Drata coming in a close second. Vanta supported more compliance standards (at that time) and is a fellow YC company (which sealed the deal). The gap between these tools is much closer now since all of them support the compliance standards we have our eyes on.\n\n## Choosing an auditor\n\nAfter setting up Vanta (nb: pestering devs to install a security agent on their laptop) and working through the checklist of tasks required for SOC2, we started looking around for auditors. Vanta was helpful here - they made introductions to a few auditors and we chose one who has previously worked with a lot of SaaS companies. Our auditors were also familiar with how Vanta worked which made the audit easier for us.\n\nThe engagement started with a call where we reviewed the systems which are in scope for the audit. We then set a date at which the audit takes place. The Type 1 audit verifies that we are adhering to our stated policies at this point in time.\n\n## Off to the races\n\nOnce the date for the audit was set, it was a mad scramble to get everything in order.\n\nVanta has a lightweight, read-only agent built on top of [osquery](https://github.com/osquery/osquery) to verify if all team members have important security features enabled: hard disk encryption, screen locks, etc. It supports Mac, Windows, and Linux, although we had [some issues](https://github.com/system76/firmware-open/issues/268) with the agent running correctly in Linux. Getting hard disk encryption right is still difficult in some Linux distros. We decided to standardize on Mac and Windows to sidestep these issues. A more powerful MDM would enable us to remote wipe computers in case it is stolen, but we decided that the existing protections we had in place were sufficient to defend against this scenario.\n\nWe came up with a few important policies. For example, Access Control Policy, Data Management Policy, Asset Management Policy, etc. Vanta has templates which we used as a starting point and adapted to our requirements. Since our auditors are familiar with the Vanta templates, there wasn’t much back and forth regarding the policy language.\n\nFor employee background checks, we use [Certn](https://certn.co/). They integrate with Vanta, making it easy to check the status within the Vanta portal itself. Background checks aren’t _required_ for SOC2, but they are a factor in deciding whom we hire especially in certain roles like SRE and Finance where there is little room for error.\n\nWe submitted a ton of evidence to prove that we can stay on top of all activities in our cloud. We use [Vector](https://vector.dev/) to ship logs to [Logflare](http://logflare.com/), where they are retained at least for a year. Our [monitoring stack](https://supabase.com/blog/supabase-reports-and-metrics) consists of regional Victoria Metrics clusters and metrics are then aggregated into a centralized cluster. We also read from Cloudtrail logs and get notified in Slack when certain events like the root AWS account is logged into, a bucket is made public, an IAM policy changes, etc.\n\nEnabling GitHub’s branch protection protects your repos from merging code without receiving a review first. This can be a challenge sometimes - at Supabase we have a few one-person teams. We had to relax our policy for some repos (POCs, internal projects) while keeping it strict for repos that handle critical systems.\n\nOther tasks were already handled - encrypting data at rest and in transit, enforcing MFA where possible, standardizing on a password manager, and not using long-lived SSH keys (we use [AWS Instance Connect](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/Connect-using-EC2-Instance-Connect.html)), etc.\n\nWe also evaluated all our vendors who had access to confidential data and now it was our turn to send them questionnaires or ask them for their SOC2 reports 😈.\n\n## Making the SOC2 process work for you\n\nWe went through a lot of effort to make sure we weren’t adding processes just because it’s a requirement for SOC2. Everything had to improve our security or risk threshold in a meaningful way. This usually involves back and forth with the auditor to show how you satisfy a particular control even though you may not be doing it in exactly the way they want you to.\n\nWe used the SOC2 process to codify the security practices we were already doing informally at Supabase.\n\nThere are a few more debatable “check-the-box” activities - like the security awareness training which I couldn’t find a way around as much as I tried. Our team has a pretty good security posture and we don’t think watching a video on why reusing passwords is a bad idea helps much.\n\n## A few surprises\n\nWe were surprised by the amount of manual evidence that we had to collect (umm..take screenshots) even with a tool like Vanta. There are things an automated tool like Vanta can never fully automate. For example, we had to collect evidence to show that have proper on-boarding and off-boarding checklists or demonstrate how we resolve security issues reported by our penetration tests within a certain time frame.\n\nSince the Type 1 audit is just about providing evidence at a single point in time, there isn’t _that_ much utility in using a tool like Vanta. However, for Type 2 certification, where we need to show evidence that we have been compliant over a period of time, a monitoring tool like Vanta becomes more useful. Additionally, if you’re planning to get multiple certifications, investing in a compliance monitoring tool is valuable since there is considerable overlap between different frameworks, and you’ll only need to do the work of collecting your evidence once.\n\nWe spent some time trying to get a perfect score in Vanta before reaching out to the auditor. That turned out to be a mistake; Vanta is fairly broad in its checks, but the ones you need to pass depend on the audit’s scope, and the auditor. For example, our auditor didn’t care about seeing our exact board meeting minutes, but Vanta wanted us to document it!\n\nAnother revelation was that a lot of controls other companies had talked about in their SOC2 compliance journeys ended up not being a blocker for us. I am not going to list them here since it is dependent on the systems that are in scope, your larger architecture, and your auditor. Again, the takeaway here is to reach out to your auditor as early as possible to get a sense of what is relevant to your audit.\n\nAuditors aren’t adversarial like pentesters or bug bounty hunters. For example, if you point them to a PR and say that PR closes a security issue reported, they _trust_ that it did. They aren’t going to find a way to bypass the fix you implemented and use that to raise an exception in your audit. This is why getting SOC2 certified is just a small part of staying secure - what makes your auditor happy will likely be pretty different from what makes a penetration tester happy.\n\n## Security Center\n\nToday we are launching our [security center](https://supabase.com/security), which shows the steps we take to protect your data. You can also find our audit reports at our security portal [here](https://security.supabase.com), or reach <NAME_EMAIL>\n\n## What’s next\n\nGetting the SOC2 certification is just the start, and we will be working on getting certified for HIPAA next. If you are interested in discussing these, we would love to chat. We will also be rolling out a series of security improvements to all projects hosted on the Supabase Cloud offering over the coming months. Stay tuned!\n\n## Announcement video and discussion\n\n\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/6bGQotxisoY\"\n    title=\"YouTube video player\"\n    frameborder=\"0\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowfullscreen\n  \u003e\u003c/iframe\u003e\n\u003c/div\u003e\n\n## More Launch Week 5\n\n- [Launch Week Page](https://supabase.com/launch-week)\n- [Launch Week 5 Hackathon](https://supabase.com/blog/launch-week-5-hackathon)\n- [Day 1 - Supabase CLI v1 and Management API Beta](https://supabase.com/blog/supabase-cli-v1-and-admin-api-beta)\n- [Youtube video - Supabase CLI v1 and Management API Beta](https://www.youtube.com/watch?v=OpPOaJI_Z28\u0026feature=emb_title)\n- [Day 2 - supabase-js v2 Release Candidate](https://supabase.com/blog/supabase-js-v2)\n- [Youtube Video - supabase-js v2 Release Candidate](https://www.youtube.com/watch?v=iqZlPtl_b-I)\n","title":"Supabase is SOC2 compliant","description":"Supabase is now SOC2 compliant. Learn how we got here and what it means for our customers.","author":"inian,joel","image":"lw5-soc2/thumb.jpg","thumb":"lw5-soc2/thumb.jpg","categories":["company"],"tags":["launch-week"],"date":"2022-08-17","toc_depth":3,"video":"https://www.youtube.com/v/6bGQotxisoY","content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    a: \"a\",\n    hr: \"hr\",\n    h2: \"h2\",\n    ul: \"ul\",\n    li: \"li\",\n    em: \"em\"\n  }, _provideComponents(), props.components);\n  return _jsxs(_Fragment, {\n    children: [_jsxs(_components.p, {\n      children: [\"Update (2023-05-22): Supabase is now SOC2 Type 2 compliant. Read more about security at Supabase in our \", _jsx(_components.a, {\n        href: \"https://supabase.com/security\",\n        children: \"Security Center\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.hr, {}), \"\\n\", _jsx(_components.p, {\n      children: \"Supabase is now SOC2 Type 1 compliant. Let’s dig into what that means, and explain the process we went through to get there. If you’re building a SaaS product, this blog post should provide a rough guide to getting your SOC2 certification.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"what-is-soc2\",\n      children: \"What is SOC2?\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"SOC2 is a compliance standard developed by the American Institute of CPAs. Yup CPAs - a bunch of accountants came up with the generally accepted compliance security standard for SaaS companies. SOC2 evaluates companies on 5 Trust Services Criteria: security, availability, processing integrity, confidentiality, and privacy. There are two types of compliance, both requiring an external audit:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Type 1: checks if a company is compliant at a point in time.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Type 2: checks if a company stays compliant during an observation window (typically 6 months to a year).\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"why-did-we-get-soc2-certified\",\n      children: \"Why did we get SOC2 certified?\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The main reason to go through a SOC2 audit is a simple one - customers expect it.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The Supabase team was spending a considerable amount of time filling out security questionnaires and fielding calls with potential customers - walking them through our security practices and how we protect customer data.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We are a database company — customers trust us with their data. We started with a SOC2 audit since one of the main things covered is our handling of sensitive customer data.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"It is also much easier to become-and remain-SOC2 compliant when you institute the appropriate practices and standards as early as possible.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"choosing-a-compliance-monitoring-tool\",\n      children: \"Choosing a compliance monitoring tool\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"There are a few tools that plug into your cloud providers, task management system, identity, and HR providers and make it easy to monitor, collect and submit evidence to auditors. We evaluated \", _jsx(_components.a, {\n        href: \"https://www.vanta.com/\",\n        children: \"Vanta\"\n      }), \", \", _jsx(_components.a, {\n        href: \"https://drata.com/\",\n        children: \"Drata\"\n      }), \", \", _jsx(_components.a, {\n        href: \"https://secureframe.com/\",\n        children: \"Secureframe\"\n      }), \", and \", _jsx(_components.a, {\n        href: \"https://tugboatlogic.com/\",\n        children: \"Tugboat Logic\"\n      }), \". Our stack is pretty standard (AWS, GCP, GSuite, and Github). All the tools we evaluated were integrated with these systems.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Some of them had overly broad permissions when integrating with our vendors (read/write access to Github for example) which made them a no-go. Some came with a lightweight agent that we could use for Mobile Device Management (MDM). Other than that, the products are largely the same.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We chose Vanta, with Drata coming in a close second. Vanta supported more compliance standards (at that time) and is a fellow YC company (which sealed the deal). The gap between these tools is much closer now since all of them support the compliance standards we have our eyes on.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"choosing-an-auditor\",\n      children: \"Choosing an auditor\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"After setting up Vanta (nb: pestering devs to install a security agent on their laptop) and working through the checklist of tasks required for SOC2, we started looking around for auditors. Vanta was helpful here - they made introductions to a few auditors and we chose one who has previously worked with a lot of SaaS companies. Our auditors were also familiar with how Vanta worked which made the audit easier for us.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The engagement started with a call where we reviewed the systems which are in scope for the audit. We then set a date at which the audit takes place. The Type 1 audit verifies that we are adhering to our stated policies at this point in time.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"off-to-the-races\",\n      children: \"Off to the races\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Once the date for the audit was set, it was a mad scramble to get everything in order.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Vanta has a lightweight, read-only agent built on top of \", _jsx(_components.a, {\n        href: \"https://github.com/osquery/osquery\",\n        children: \"osquery\"\n      }), \" to verify if all team members have important security features enabled: hard disk encryption, screen locks, etc. It supports Mac, Windows, and Linux, although we had \", _jsx(_components.a, {\n        href: \"https://github.com/system76/firmware-open/issues/268\",\n        children: \"some issues\"\n      }), \" with the agent running correctly in Linux. Getting hard disk encryption right is still difficult in some Linux distros. We decided to standardize on Mac and Windows to sidestep these issues. A more powerful MDM would enable us to remote wipe computers in case it is stolen, but we decided that the existing protections we had in place were sufficient to defend against this scenario.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We came up with a few important policies. For example, Access Control Policy, Data Management Policy, Asset Management Policy, etc. Vanta has templates which we used as a starting point and adapted to our requirements. Since our auditors are familiar with the Vanta templates, there wasn’t much back and forth regarding the policy language.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"For employee background checks, we use \", _jsx(_components.a, {\n        href: \"https://certn.co/\",\n        children: \"Certn\"\n      }), \". They integrate with Vanta, making it easy to check the status within the Vanta portal itself. Background checks aren’t \", _jsx(_components.em, {\n        children: \"required\"\n      }), \" for SOC2, but they are a factor in deciding whom we hire especially in certain roles like SRE and Finance where there is little room for error.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We submitted a ton of evidence to prove that we can stay on top of all activities in our cloud. We use \", _jsx(_components.a, {\n        href: \"https://vector.dev/\",\n        children: \"Vector\"\n      }), \" to ship logs to \", _jsx(_components.a, {\n        href: \"http://logflare.com/\",\n        children: \"Logflare\"\n      }), \", where they are retained at least for a year. Our \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/supabase-reports-and-metrics\",\n        children: \"monitoring stack\"\n      }), \" consists of regional Victoria Metrics clusters and metrics are then aggregated into a centralized cluster. We also read from Cloudtrail logs and get notified in Slack when certain events like the root AWS account is logged into, a bucket is made public, an IAM policy changes, etc.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Enabling GitHub’s branch protection protects your repos from merging code without receiving a review first. This can be a challenge sometimes - at Supabase we have a few one-person teams. We had to relax our policy for some repos (POCs, internal projects) while keeping it strict for repos that handle critical systems.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Other tasks were already handled - encrypting data at rest and in transit, enforcing MFA where possible, standardizing on a password manager, and not using long-lived SSH keys (we use \", _jsx(_components.a, {\n        href: \"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/Connect-using-EC2-Instance-Connect.html\",\n        children: \"AWS Instance Connect\"\n      }), \"), etc.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We also evaluated all our vendors who had access to confidential data and now it was our turn to send them questionnaires or ask them for their SOC2 reports 😈.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"making-the-soc2-process-work-for-you\",\n      children: \"Making the SOC2 process work for you\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We went through a lot of effort to make sure we weren’t adding processes just because it’s a requirement for SOC2. Everything had to improve our security or risk threshold in a meaningful way. This usually involves back and forth with the auditor to show how you satisfy a particular control even though you may not be doing it in exactly the way they want you to.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We used the SOC2 process to codify the security practices we were already doing informally at Supabase.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"There are a few more debatable “check-the-box” activities - like the security awareness training which I couldn’t find a way around as much as I tried. Our team has a pretty good security posture and we don’t think watching a video on why reusing passwords is a bad idea helps much.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"a-few-surprises\",\n      children: \"A few surprises\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We were surprised by the amount of manual evidence that we had to collect (umm..take screenshots) even with a tool like Vanta. There are things an automated tool like Vanta can never fully automate. For example, we had to collect evidence to show that have proper on-boarding and off-boarding checklists or demonstrate how we resolve security issues reported by our penetration tests within a certain time frame.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Since the Type 1 audit is just about providing evidence at a single point in time, there isn’t \", _jsx(_components.em, {\n        children: \"that\"\n      }), \" much utility in using a tool like Vanta. However, for Type 2 certification, where we need to show evidence that we have been compliant over a period of time, a monitoring tool like Vanta becomes more useful. Additionally, if you’re planning to get multiple certifications, investing in a compliance monitoring tool is valuable since there is considerable overlap between different frameworks, and you’ll only need to do the work of collecting your evidence once.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We spent some time trying to get a perfect score in Vanta before reaching out to the auditor. That turned out to be a mistake; Vanta is fairly broad in its checks, but the ones you need to pass depend on the audit’s scope, and the auditor. For example, our auditor didn’t care about seeing our exact board meeting minutes, but Vanta wanted us to document it!\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Another revelation was that a lot of controls other companies had talked about in their SOC2 compliance journeys ended up not being a blocker for us. I am not going to list them here since it is dependent on the systems that are in scope, your larger architecture, and your auditor. Again, the takeaway here is to reach out to your auditor as early as possible to get a sense of what is relevant to your audit.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Auditors aren’t adversarial like pentesters or bug bounty hunters. For example, if you point them to a PR and say that PR closes a security issue reported, they \", _jsx(_components.em, {\n        children: \"trust\"\n      }), \" that it did. They aren’t going to find a way to bypass the fix you implemented and use that to raise an exception in your audit. This is why getting SOC2 certified is just a small part of staying secure - what makes your auditor happy will likely be pretty different from what makes a penetration tester happy.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"security-center\",\n      children: \"Security Center\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Today we are launching our \", _jsx(_components.a, {\n        href: \"https://supabase.com/security\",\n        children: \"security center\"\n      }), \", which shows the steps we take to protect your data. You can also find our audit reports at our security portal \", _jsx(_components.a, {\n        href: \"https://security.supabase.com\",\n        children: \"here\"\n      }), \", or reach out to \", _jsx(_components.a, {\n        href: \"mailto:<EMAIL>\",\n        children: \"<EMAIL>\"\n      })]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"whats-next\",\n      children: \"What’s next\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Getting the SOC2 certification is just the start, and we will be working on getting certified for HIPAA next. If you are interested in discussing these, we would love to chat. We will also be rolling out a series of security improvements to all projects hosted on the Supabase Cloud offering over the coming months. Stay tuned!\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"announcement-video-and-discussion\",\n      children: \"Announcement video and discussion\"\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"video-container\",\n      children: _jsx(\"iframe\", {\n        className: \"w-full\",\n        src: \"https://www.youtube-nocookie.com/embed/6bGQotxisoY\",\n        title: \"YouTube video player\",\n        frameborder: \"0\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n        allowfullscreen: true\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"more-launch-week-5\",\n      children: \"More Launch Week 5\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/launch-week\",\n          children: \"Launch Week Page\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/blog/launch-week-5-hackathon\",\n          children: \"Launch Week 5 Hackathon\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/blog/supabase-cli-v1-and-admin-api-beta\",\n          children: \"Day 1 - Supabase CLI v1 and Management API Beta\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=OpPOaJI_Z28\u0026feature=emb_title\",\n          children: \"Youtube video - Supabase CLI v1 and Management API Beta\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/blog/supabase-js-v2\",\n          children: \"Day 2 - supabase-js v2 Release Candidate\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=iqZlPtl_b-I\",\n          children: \"Youtube Video - supabase-js v2 Release Candidate\"\n        })\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"What is SOC2?","slug":"what-is-soc2","lvl":2,"i":0,"seen":0},{"content":"Why did we get SOC2 certified?","slug":"why-did-we-get-soc2-certified","lvl":2,"i":1,"seen":0},{"content":"Choosing a compliance monitoring tool","slug":"choosing-a-compliance-monitoring-tool","lvl":2,"i":2,"seen":0},{"content":"Choosing an auditor","slug":"choosing-an-auditor","lvl":2,"i":3,"seen":0},{"content":"Off to the races","slug":"off-to-the-races","lvl":2,"i":4,"seen":0},{"content":"Making the SOC2 process work for you","slug":"making-the-soc2-process-work-for-you","lvl":2,"i":5,"seen":0},{"content":"A few surprises","slug":"a-few-surprises","lvl":2,"i":6,"seen":0},{"content":"Security Center","slug":"security-center","lvl":2,"i":7,"seen":0},{"content":"What’s next","slug":"whats-next","lvl":2,"i":8,"seen":0},{"content":"Announcement video and discussion","slug":"announcement-video-and-discussion","lvl":2,"i":9,"seen":0},{"content":"More Launch Week 5","slug":"more-launch-week-5","lvl":2,"i":10,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,2],"level":0},{"type":"inline","content":"Update (2023-05-22): Supabase is now SOC2 Type 2 compliant. Read more about security at Supabase in our [Security Center](https://supabase.com/security).","level":1,"lines":[1,2],"children":[{"type":"text","content":"Update (2023-05-22): Supabase is now SOC2 Type 2 compliant. Read more about security at Supabase in our ","level":0},{"type":"link_open","href":"https://supabase.com/security","title":"","level":0},{"type":"text","content":"Security Center","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"hr","lines":[3,4],"level":0},{"type":"paragraph_open","tight":false,"lines":[5,6],"level":0},{"type":"inline","content":"Supabase is now SOC2 Type 1 compliant. Let’s dig into what that means, and explain the process we went through to get there. If you’re building a SaaS product, this blog post should provide a rough guide to getting your SOC2 certification.","level":1,"lines":[5,6],"children":[{"type":"text","content":"Supabase is now SOC2 Type 1 compliant. Let’s dig into what that means, and explain the process we went through to get there. If you’re building a SaaS product, this blog post should provide a rough guide to getting your SOC2 certification.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[7,8],"level":0},{"type":"inline","content":"[What is SOC2?](#what-is-soc2)","level":1,"lines":[7,8],"children":[{"type":"text","content":"What is SOC2?","level":0}],"lvl":2,"i":0,"seen":0,"slug":"what-is-soc2"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[9,10],"level":0},{"type":"inline","content":"SOC2 is a compliance standard developed by the American Institute of CPAs. Yup CPAs - a bunch of accountants came up with the generally accepted compliance security standard for SaaS companies. SOC2 evaluates companies on 5 Trust Services Criteria: security, availability, processing integrity, confidentiality, and privacy. There are two types of compliance, both requiring an external audit:","level":1,"lines":[9,10],"children":[{"type":"text","content":"SOC2 is a compliance standard developed by the American Institute of CPAs. Yup CPAs - a bunch of accountants came up with the generally accepted compliance security standard for SaaS companies. SOC2 evaluates companies on 5 Trust Services Criteria: security, availability, processing integrity, confidentiality, and privacy. There are two types of compliance, both requiring an external audit:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[11,14],"level":0},{"type":"list_item_open","lines":[11,12],"level":1},{"type":"paragraph_open","tight":true,"lines":[11,12],"level":2},{"type":"inline","content":"Type 1: checks if a company is compliant at a point in time.","level":3,"lines":[11,12],"children":[{"type":"text","content":"Type 1: checks if a company is compliant at a point in time.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[12,14],"level":1},{"type":"paragraph_open","tight":true,"lines":[12,13],"level":2},{"type":"inline","content":"Type 2: checks if a company stays compliant during an observation window (typically 6 months to a year).","level":3,"lines":[12,13],"children":[{"type":"text","content":"Type 2: checks if a company stays compliant during an observation window (typically 6 months to a year).","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":2,"lines":[14,15],"level":0},{"type":"inline","content":"[Why did we get SOC2 certified?](#why-did-we-get-soc2-certified)","level":1,"lines":[14,15],"children":[{"type":"text","content":"Why did we get SOC2 certified?","level":0}],"lvl":2,"i":1,"seen":0,"slug":"why-did-we-get-soc2-certified"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[16,17],"level":0},{"type":"inline","content":"The main reason to go through a SOC2 audit is a simple one - customers expect it.","level":1,"lines":[16,17],"children":[{"type":"text","content":"The main reason to go through a SOC2 audit is a simple one - customers expect it.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[18,19],"level":0},{"type":"inline","content":"The Supabase team was spending a considerable amount of time filling out security questionnaires and fielding calls with potential customers - walking them through our security practices and how we protect customer data.","level":1,"lines":[18,19],"children":[{"type":"text","content":"The Supabase team was spending a considerable amount of time filling out security questionnaires and fielding calls with potential customers - walking them through our security practices and how we protect customer data.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[20,21],"level":0},{"type":"inline","content":"We are a database company — customers trust us with their data. We started with a SOC2 audit since one of the main things covered is our handling of sensitive customer data.","level":1,"lines":[20,21],"children":[{"type":"text","content":"We are a database company — customers trust us with their data. We started with a SOC2 audit since one of the main things covered is our handling of sensitive customer data.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[22,23],"level":0},{"type":"inline","content":"It is also much easier to become-and remain-SOC2 compliant when you institute the appropriate practices and standards as early as possible.","level":1,"lines":[22,23],"children":[{"type":"text","content":"It is also much easier to become-and remain-SOC2 compliant when you institute the appropriate practices and standards as early as possible.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[24,25],"level":0},{"type":"inline","content":"[Choosing a compliance monitoring tool](#choosing-a-compliance-monitoring-tool)","level":1,"lines":[24,25],"children":[{"type":"text","content":"Choosing a compliance monitoring tool","level":0}],"lvl":2,"i":2,"seen":0,"slug":"choosing-a-compliance-monitoring-tool"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[26,27],"level":0},{"type":"inline","content":"There are a few tools that plug into your cloud providers, task management system, identity, and HR providers and make it easy to monitor, collect and submit evidence to auditors. We evaluated [Vanta](https://www.vanta.com/), [Drata](https://drata.com/), [Secureframe](https://secureframe.com/), and [Tugboat Logic](https://tugboatlogic.com/). Our stack is pretty standard (AWS, GCP, GSuite, and Github). All the tools we evaluated were integrated with these systems.","level":1,"lines":[26,27],"children":[{"type":"text","content":"There are a few tools that plug into your cloud providers, task management system, identity, and HR providers and make it easy to monitor, collect and submit evidence to auditors. We evaluated ","level":0},{"type":"link_open","href":"https://www.vanta.com/","title":"","level":0},{"type":"text","content":"Vanta","level":1},{"type":"link_close","level":0},{"type":"text","content":", ","level":0},{"type":"link_open","href":"https://drata.com/","title":"","level":0},{"type":"text","content":"Drata","level":1},{"type":"link_close","level":0},{"type":"text","content":", ","level":0},{"type":"link_open","href":"https://secureframe.com/","title":"","level":0},{"type":"text","content":"Secureframe","level":1},{"type":"link_close","level":0},{"type":"text","content":", and ","level":0},{"type":"link_open","href":"https://tugboatlogic.com/","title":"","level":0},{"type":"text","content":"Tugboat Logic","level":1},{"type":"link_close","level":0},{"type":"text","content":". Our stack is pretty standard (AWS, GCP, GSuite, and Github). All the tools we evaluated were integrated with these systems.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[28,29],"level":0},{"type":"inline","content":"Some of them had overly broad permissions when integrating with our vendors (read/write access to Github for example) which made them a no-go. Some came with a lightweight agent that we could use for Mobile Device Management (MDM). Other than that, the products are largely the same.","level":1,"lines":[28,29],"children":[{"type":"text","content":"Some of them had overly broad permissions when integrating with our vendors (read/write access to Github for example) which made them a no-go. Some came with a lightweight agent that we could use for Mobile Device Management (MDM). Other than that, the products are largely the same.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[30,31],"level":0},{"type":"inline","content":"We chose Vanta, with Drata coming in a close second. Vanta supported more compliance standards (at that time) and is a fellow YC company (which sealed the deal). The gap between these tools is much closer now since all of them support the compliance standards we have our eyes on.","level":1,"lines":[30,31],"children":[{"type":"text","content":"We chose Vanta, with Drata coming in a close second. Vanta supported more compliance standards (at that time) and is a fellow YC company (which sealed the deal). The gap between these tools is much closer now since all of them support the compliance standards we have our eyes on.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[32,33],"level":0},{"type":"inline","content":"[Choosing an auditor](#choosing-an-auditor)","level":1,"lines":[32,33],"children":[{"type":"text","content":"Choosing an auditor","level":0}],"lvl":2,"i":3,"seen":0,"slug":"choosing-an-auditor"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[34,35],"level":0},{"type":"inline","content":"After setting up Vanta (nb: pestering devs to install a security agent on their laptop) and working through the checklist of tasks required for SOC2, we started looking around for auditors. Vanta was helpful here - they made introductions to a few auditors and we chose one who has previously worked with a lot of SaaS companies. Our auditors were also familiar with how Vanta worked which made the audit easier for us.","level":1,"lines":[34,35],"children":[{"type":"text","content":"After setting up Vanta (nb: pestering devs to install a security agent on their laptop) and working through the checklist of tasks required for SOC2, we started looking around for auditors. Vanta was helpful here - they made introductions to a few auditors and we chose one who has previously worked with a lot of SaaS companies. Our auditors were also familiar with how Vanta worked which made the audit easier for us.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[36,37],"level":0},{"type":"inline","content":"The engagement started with a call where we reviewed the systems which are in scope for the audit. We then set a date at which the audit takes place. The Type 1 audit verifies that we are adhering to our stated policies at this point in time.","level":1,"lines":[36,37],"children":[{"type":"text","content":"The engagement started with a call where we reviewed the systems which are in scope for the audit. We then set a date at which the audit takes place. The Type 1 audit verifies that we are adhering to our stated policies at this point in time.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[38,39],"level":0},{"type":"inline","content":"[Off to the races](#off-to-the-races)","level":1,"lines":[38,39],"children":[{"type":"text","content":"Off to the races","level":0}],"lvl":2,"i":4,"seen":0,"slug":"off-to-the-races"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[40,41],"level":0},{"type":"inline","content":"Once the date for the audit was set, it was a mad scramble to get everything in order.","level":1,"lines":[40,41],"children":[{"type":"text","content":"Once the date for the audit was set, it was a mad scramble to get everything in order.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[42,43],"level":0},{"type":"inline","content":"Vanta has a lightweight, read-only agent built on top of [osquery](https://github.com/osquery/osquery) to verify if all team members have important security features enabled: hard disk encryption, screen locks, etc. It supports Mac, Windows, and Linux, although we had [some issues](https://github.com/system76/firmware-open/issues/268) with the agent running correctly in Linux. Getting hard disk encryption right is still difficult in some Linux distros. We decided to standardize on Mac and Windows to sidestep these issues. A more powerful MDM would enable us to remote wipe computers in case it is stolen, but we decided that the existing protections we had in place were sufficient to defend against this scenario.","level":1,"lines":[42,43],"children":[{"type":"text","content":"Vanta has a lightweight, read-only agent built on top of ","level":0},{"type":"link_open","href":"https://github.com/osquery/osquery","title":"","level":0},{"type":"text","content":"osquery","level":1},{"type":"link_close","level":0},{"type":"text","content":" to verify if all team members have important security features enabled: hard disk encryption, screen locks, etc. It supports Mac, Windows, and Linux, although we had ","level":0},{"type":"link_open","href":"https://github.com/system76/firmware-open/issues/268","title":"","level":0},{"type":"text","content":"some issues","level":1},{"type":"link_close","level":0},{"type":"text","content":" with the agent running correctly in Linux. Getting hard disk encryption right is still difficult in some Linux distros. We decided to standardize on Mac and Windows to sidestep these issues. A more powerful MDM would enable us to remote wipe computers in case it is stolen, but we decided that the existing protections we had in place were sufficient to defend against this scenario.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[44,45],"level":0},{"type":"inline","content":"We came up with a few important policies. For example, Access Control Policy, Data Management Policy, Asset Management Policy, etc. Vanta has templates which we used as a starting point and adapted to our requirements. Since our auditors are familiar with the Vanta templates, there wasn’t much back and forth regarding the policy language.","level":1,"lines":[44,45],"children":[{"type":"text","content":"We came up with a few important policies. For example, Access Control Policy, Data Management Policy, Asset Management Policy, etc. Vanta has templates which we used as a starting point and adapted to our requirements. Since our auditors are familiar with the Vanta templates, there wasn’t much back and forth regarding the policy language.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[46,47],"level":0},{"type":"inline","content":"For employee background checks, we use [Certn](https://certn.co/). They integrate with Vanta, making it easy to check the status within the Vanta portal itself. Background checks aren’t _required_ for SOC2, but they are a factor in deciding whom we hire especially in certain roles like SRE and Finance where there is little room for error.","level":1,"lines":[46,47],"children":[{"type":"text","content":"For employee background checks, we use ","level":0},{"type":"link_open","href":"https://certn.co/","title":"","level":0},{"type":"text","content":"Certn","level":1},{"type":"link_close","level":0},{"type":"text","content":". They integrate with Vanta, making it easy to check the status within the Vanta portal itself. Background checks aren’t ","level":0},{"type":"em_open","level":0},{"type":"text","content":"required","level":1},{"type":"em_close","level":0},{"type":"text","content":" for SOC2, but they are a factor in deciding whom we hire especially in certain roles like SRE and Finance where there is little room for error.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[48,49],"level":0},{"type":"inline","content":"We submitted a ton of evidence to prove that we can stay on top of all activities in our cloud. We use [Vector](https://vector.dev/) to ship logs to [Logflare](http://logflare.com/), where they are retained at least for a year. Our [monitoring stack](https://supabase.com/blog/supabase-reports-and-metrics) consists of regional Victoria Metrics clusters and metrics are then aggregated into a centralized cluster. We also read from Cloudtrail logs and get notified in Slack when certain events like the root AWS account is logged into, a bucket is made public, an IAM policy changes, etc.","level":1,"lines":[48,49],"children":[{"type":"text","content":"We submitted a ton of evidence to prove that we can stay on top of all activities in our cloud. We use ","level":0},{"type":"link_open","href":"https://vector.dev/","title":"","level":0},{"type":"text","content":"Vector","level":1},{"type":"link_close","level":0},{"type":"text","content":" to ship logs to ","level":0},{"type":"link_open","href":"http://logflare.com/","title":"","level":0},{"type":"text","content":"Logflare","level":1},{"type":"link_close","level":0},{"type":"text","content":", where they are retained at least for a year. Our ","level":0},{"type":"link_open","href":"https://supabase.com/blog/supabase-reports-and-metrics","title":"","level":0},{"type":"text","content":"monitoring stack","level":1},{"type":"link_close","level":0},{"type":"text","content":" consists of regional Victoria Metrics clusters and metrics are then aggregated into a centralized cluster. We also read from Cloudtrail logs and get notified in Slack when certain events like the root AWS account is logged into, a bucket is made public, an IAM policy changes, etc.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[50,51],"level":0},{"type":"inline","content":"Enabling GitHub’s branch protection protects your repos from merging code without receiving a review first. This can be a challenge sometimes - at Supabase we have a few one-person teams. We had to relax our policy for some repos (POCs, internal projects) while keeping it strict for repos that handle critical systems.","level":1,"lines":[50,51],"children":[{"type":"text","content":"Enabling GitHub’s branch protection protects your repos from merging code without receiving a review first. This can be a challenge sometimes - at Supabase we have a few one-person teams. We had to relax our policy for some repos (POCs, internal projects) while keeping it strict for repos that handle critical systems.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[52,53],"level":0},{"type":"inline","content":"Other tasks were already handled - encrypting data at rest and in transit, enforcing MFA where possible, standardizing on a password manager, and not using long-lived SSH keys (we use [AWS Instance Connect](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/Connect-using-EC2-Instance-Connect.html)), etc.","level":1,"lines":[52,53],"children":[{"type":"text","content":"Other tasks were already handled - encrypting data at rest and in transit, enforcing MFA where possible, standardizing on a password manager, and not using long-lived SSH keys (we use ","level":0},{"type":"link_open","href":"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/Connect-using-EC2-Instance-Connect.html","title":"","level":0},{"type":"text","content":"AWS Instance Connect","level":1},{"type":"link_close","level":0},{"type":"text","content":"), etc.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[54,55],"level":0},{"type":"inline","content":"We also evaluated all our vendors who had access to confidential data and now it was our turn to send them questionnaires or ask them for their SOC2 reports 😈.","level":1,"lines":[54,55],"children":[{"type":"text","content":"We also evaluated all our vendors who had access to confidential data and now it was our turn to send them questionnaires or ask them for their SOC2 reports 😈.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[56,57],"level":0},{"type":"inline","content":"[Making the SOC2 process work for you](#making-the-soc2-process-work-for-you)","level":1,"lines":[56,57],"children":[{"type":"text","content":"Making the SOC2 process work for you","level":0}],"lvl":2,"i":5,"seen":0,"slug":"making-the-soc2-process-work-for-you"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[58,59],"level":0},{"type":"inline","content":"We went through a lot of effort to make sure we weren’t adding processes just because it’s a requirement for SOC2. Everything had to improve our security or risk threshold in a meaningful way. This usually involves back and forth with the auditor to show how you satisfy a particular control even though you may not be doing it in exactly the way they want you to.","level":1,"lines":[58,59],"children":[{"type":"text","content":"We went through a lot of effort to make sure we weren’t adding processes just because it’s a requirement for SOC2. Everything had to improve our security or risk threshold in a meaningful way. This usually involves back and forth with the auditor to show how you satisfy a particular control even though you may not be doing it in exactly the way they want you to.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[60,61],"level":0},{"type":"inline","content":"We used the SOC2 process to codify the security practices we were already doing informally at Supabase.","level":1,"lines":[60,61],"children":[{"type":"text","content":"We used the SOC2 process to codify the security practices we were already doing informally at Supabase.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[62,63],"level":0},{"type":"inline","content":"There are a few more debatable “check-the-box” activities - like the security awareness training which I couldn’t find a way around as much as I tried. Our team has a pretty good security posture and we don’t think watching a video on why reusing passwords is a bad idea helps much.","level":1,"lines":[62,63],"children":[{"type":"text","content":"There are a few more debatable “check-the-box” activities - like the security awareness training which I couldn’t find a way around as much as I tried. Our team has a pretty good security posture and we don’t think watching a video on why reusing passwords is a bad idea helps much.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[64,65],"level":0},{"type":"inline","content":"[A few surprises](#a-few-surprises)","level":1,"lines":[64,65],"children":[{"type":"text","content":"A few surprises","level":0}],"lvl":2,"i":6,"seen":0,"slug":"a-few-surprises"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[66,67],"level":0},{"type":"inline","content":"We were surprised by the amount of manual evidence that we had to collect (umm..take screenshots) even with a tool like Vanta. There are things an automated tool like Vanta can never fully automate. For example, we had to collect evidence to show that have proper on-boarding and off-boarding checklists or demonstrate how we resolve security issues reported by our penetration tests within a certain time frame.","level":1,"lines":[66,67],"children":[{"type":"text","content":"We were surprised by the amount of manual evidence that we had to collect (umm..take screenshots) even with a tool like Vanta. There are things an automated tool like Vanta can never fully automate. For example, we had to collect evidence to show that have proper on-boarding and off-boarding checklists or demonstrate how we resolve security issues reported by our penetration tests within a certain time frame.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[68,69],"level":0},{"type":"inline","content":"Since the Type 1 audit is just about providing evidence at a single point in time, there isn’t _that_ much utility in using a tool like Vanta. However, for Type 2 certification, where we need to show evidence that we have been compliant over a period of time, a monitoring tool like Vanta becomes more useful. Additionally, if you’re planning to get multiple certifications, investing in a compliance monitoring tool is valuable since there is considerable overlap between different frameworks, and you’ll only need to do the work of collecting your evidence once.","level":1,"lines":[68,69],"children":[{"type":"text","content":"Since the Type 1 audit is just about providing evidence at a single point in time, there isn’t ","level":0},{"type":"em_open","level":0},{"type":"text","content":"that","level":1},{"type":"em_close","level":0},{"type":"text","content":" much utility in using a tool like Vanta. However, for Type 2 certification, where we need to show evidence that we have been compliant over a period of time, a monitoring tool like Vanta becomes more useful. Additionally, if you’re planning to get multiple certifications, investing in a compliance monitoring tool is valuable since there is considerable overlap between different frameworks, and you’ll only need to do the work of collecting your evidence once.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[70,71],"level":0},{"type":"inline","content":"We spent some time trying to get a perfect score in Vanta before reaching out to the auditor. That turned out to be a mistake; Vanta is fairly broad in its checks, but the ones you need to pass depend on the audit’s scope, and the auditor. For example, our auditor didn’t care about seeing our exact board meeting minutes, but Vanta wanted us to document it!","level":1,"lines":[70,71],"children":[{"type":"text","content":"We spent some time trying to get a perfect score in Vanta before reaching out to the auditor. That turned out to be a mistake; Vanta is fairly broad in its checks, but the ones you need to pass depend on the audit’s scope, and the auditor. For example, our auditor didn’t care about seeing our exact board meeting minutes, but Vanta wanted us to document it!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[72,73],"level":0},{"type":"inline","content":"Another revelation was that a lot of controls other companies had talked about in their SOC2 compliance journeys ended up not being a blocker for us. I am not going to list them here since it is dependent on the systems that are in scope, your larger architecture, and your auditor. Again, the takeaway here is to reach out to your auditor as early as possible to get a sense of what is relevant to your audit.","level":1,"lines":[72,73],"children":[{"type":"text","content":"Another revelation was that a lot of controls other companies had talked about in their SOC2 compliance journeys ended up not being a blocker for us. I am not going to list them here since it is dependent on the systems that are in scope, your larger architecture, and your auditor. Again, the takeaway here is to reach out to your auditor as early as possible to get a sense of what is relevant to your audit.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[74,75],"level":0},{"type":"inline","content":"Auditors aren’t adversarial like pentesters or bug bounty hunters. For example, if you point them to a PR and say that PR closes a security issue reported, they _trust_ that it did. They aren’t going to find a way to bypass the fix you implemented and use that to raise an exception in your audit. This is why getting SOC2 certified is just a small part of staying secure - what makes your auditor happy will likely be pretty different from what makes a penetration tester happy.","level":1,"lines":[74,75],"children":[{"type":"text","content":"Auditors aren’t adversarial like pentesters or bug bounty hunters. For example, if you point them to a PR and say that PR closes a security issue reported, they ","level":0},{"type":"em_open","level":0},{"type":"text","content":"trust","level":1},{"type":"em_close","level":0},{"type":"text","content":" that it did. They aren’t going to find a way to bypass the fix you implemented and use that to raise an exception in your audit. This is why getting SOC2 certified is just a small part of staying secure - what makes your auditor happy will likely be pretty different from what makes a penetration tester happy.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[76,77],"level":0},{"type":"inline","content":"[Security Center](#security-center)","level":1,"lines":[76,77],"children":[{"type":"text","content":"Security Center","level":0}],"lvl":2,"i":7,"seen":0,"slug":"security-center"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[78,79],"level":0},{"type":"inline","content":"Today we are launching our [security center](https://supabase.com/security), which shows the steps we take to protect your data. You can also find our audit reports at our security portal [here](https://security.supabase.com), or reach <NAME_EMAIL>","level":1,"lines":[78,79],"children":[{"type":"text","content":"Today we are launching our ","level":0},{"type":"link_open","href":"https://supabase.com/security","title":"","level":0},{"type":"text","content":"security center","level":1},{"type":"link_close","level":0},{"type":"text","content":", which shows the steps we take to protect your data. You can also find our audit reports at our security portal ","level":0},{"type":"link_open","href":"https://security.supabase.com","title":"","level":0},{"type":"text","content":"here","level":1},{"type":"link_close","level":0},{"type":"text","content":", or reach <NAME_EMAIL>","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[80,81],"level":0},{"type":"inline","content":"[What’s next](#whats-next)","level":1,"lines":[80,81],"children":[{"type":"text","content":"What’s next","level":0}],"lvl":2,"i":8,"seen":0,"slug":"whats-next"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[82,83],"level":0},{"type":"inline","content":"Getting the SOC2 certification is just the start, and we will be working on getting certified for HIPAA next. If you are interested in discussing these, we would love to chat. We will also be rolling out a series of security improvements to all projects hosted on the Supabase Cloud offering over the coming months. Stay tuned!","level":1,"lines":[82,83],"children":[{"type":"text","content":"Getting the SOC2 certification is just the start, and we will be working on getting certified for HIPAA next. If you are interested in discussing these, we would love to chat. We will also be rolling out a series of security improvements to all projects hosted on the Supabase Cloud offering over the coming months. Stay tuned!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[84,85],"level":0},{"type":"inline","content":"[Announcement video and discussion](#announcement-video-and-discussion)","level":1,"lines":[84,85],"children":[{"type":"text","content":"Announcement video and discussion","level":0}],"lvl":2,"i":9,"seen":0,"slug":"announcement-video-and-discussion"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[86,94],"level":0},{"type":"inline","content":"\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/6bGQotxisoY\"\n    title=\"YouTube video player\"\n    frameborder=\"0\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowfullscreen","level":1,"lines":[86,94],"children":[{"type":"text","content":"\u003cdiv className=\"video-container\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ciframe","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"w-full\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://www.youtube-nocookie.com/embed/6bGQotxisoY\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"title=\"YouTube video player\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"frameborder=\"0\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allowfullscreen","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"blockquote_open","lines":[94,96],"level":0},{"type":"paragraph_open","tight":false,"lines":[94,96],"level":1},{"type":"inline","content":"\u003c/iframe\u003e\n\u003c/div\u003e","level":2,"lines":[94,96],"children":[{"type":"text","content":"\u003c/iframe\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"heading_open","hLevel":2,"lines":[97,98],"level":0},{"type":"inline","content":"[More Launch Week 5](#more-launch-week-5)","level":1,"lines":[97,98],"children":[{"type":"text","content":"More Launch Week 5","level":0}],"lvl":2,"i":10,"seen":0,"slug":"more-launch-week-5"},{"type":"heading_close","hLevel":2,"level":0},{"type":"bullet_list_open","lines":[99,105],"level":0},{"type":"list_item_open","lines":[99,100],"level":1},{"type":"paragraph_open","tight":true,"lines":[99,100],"level":2},{"type":"inline","content":"[Launch Week Page](https://supabase.com/launch-week)","level":3,"lines":[99,100],"children":[{"type":"link_open","href":"https://supabase.com/launch-week","title":"","level":0},{"type":"text","content":"Launch Week Page","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[100,101],"level":1},{"type":"paragraph_open","tight":true,"lines":[100,101],"level":2},{"type":"inline","content":"[Launch Week 5 Hackathon](https://supabase.com/blog/launch-week-5-hackathon)","level":3,"lines":[100,101],"children":[{"type":"link_open","href":"https://supabase.com/blog/launch-week-5-hackathon","title":"","level":0},{"type":"text","content":"Launch Week 5 Hackathon","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[101,102],"level":1},{"type":"paragraph_open","tight":true,"lines":[101,102],"level":2},{"type":"inline","content":"[Day 1 - Supabase CLI v1 and Management API Beta](https://supabase.com/blog/supabase-cli-v1-and-admin-api-beta)","level":3,"lines":[101,102],"children":[{"type":"link_open","href":"https://supabase.com/blog/supabase-cli-v1-and-admin-api-beta","title":"","level":0},{"type":"text","content":"Day 1 - Supabase CLI v1 and Management API Beta","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[102,103],"level":1},{"type":"paragraph_open","tight":true,"lines":[102,103],"level":2},{"type":"inline","content":"[Youtube video - Supabase CLI v1 and Management API Beta](https://www.youtube.com/watch?v=OpPOaJI_Z28\u0026feature=emb_title)","level":3,"lines":[102,103],"children":[{"type":"link_open","href":"https://www.youtube.com/watch?v=OpPOaJI_Z28\u0026feature=emb_title","title":"","level":0},{"type":"text","content":"Youtube video - Supabase CLI v1 and Management API Beta","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[103,104],"level":1},{"type":"paragraph_open","tight":true,"lines":[103,104],"level":2},{"type":"inline","content":"[Day 2 - supabase-js v2 Release Candidate](https://supabase.com/blog/supabase-js-v2)","level":3,"lines":[103,104],"children":[{"type":"link_open","href":"https://supabase.com/blog/supabase-js-v2","title":"","level":0},{"type":"text","content":"Day 2 - supabase-js v2 Release Candidate","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[104,105],"level":1},{"type":"paragraph_open","tight":true,"lines":[104,105],"level":2},{"type":"inline","content":"[Youtube Video - supabase-js v2 Release Candidate](https://www.youtube.com/watch?v=iqZlPtl_b-I)","level":3,"lines":[104,105],"children":[{"type":"link_open","href":"https://www.youtube.com/watch?v=iqZlPtl_b-I","title":"","level":0},{"type":"text","content":"Youtube Video - supabase-js v2 Release Candidate","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0}],"content":"- [What is SOC2?](#what-is-soc2)\n- [Why did we get SOC2 certified?](#why-did-we-get-soc2-certified)\n- [Choosing a compliance monitoring tool](#choosing-a-compliance-monitoring-tool)\n- [Choosing an auditor](#choosing-an-auditor)\n- [Off to the races](#off-to-the-races)\n- [Making the SOC2 process work for you](#making-the-soc2-process-work-for-you)\n- [A few surprises](#a-few-surprises)\n- [Security Center](#security-center)\n- [What’s next](#whats-next)\n- [Announcement video and discussion](#announcement-video-and-discussion)\n- [More Launch Week 5](#more-launch-week-5)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-soc2"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>