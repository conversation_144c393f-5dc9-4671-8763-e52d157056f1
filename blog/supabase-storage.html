<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Storage is now available in Supabase</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Launching Supabase Storage and how you can use it in your apps" data-next-head=""/><meta property="og:title" content="Storage is now available in Supabase" data-next-head=""/><meta property="og:description" content="Launching Supabase Storage and how you can use it in your apps" data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-storage" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="03-30-2021" data-next-head=""/><meta property="article:author" content="https://twitter.com/everConfusedGuy" data-next-head=""/><meta property="article:tag" content="supabase" data-next-head=""/><meta property="article:tag" content="storage" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/storage/ph-1.png" data-next-head=""/><meta property="og:image:alt" content="Storage is now available in Supabase thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Storage is now available in Supabase</h1><div class="text-light flex space-x-3 text-sm"><p>30 Mar 2021</p><p>•</p><p>9 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://twitter.com/everConfusedGuy"><div class="flex items-center gap-3"><div class="w-10"><img alt="Inian Parameshwaran avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Favatars.githubusercontent.com%2Fu%2F2155545%3Fv%3D4&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Favatars.githubusercontent.com%2Fu%2F2155545%3Fv%3D4&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Favatars.githubusercontent.com%2Fu%2F2155545%3Fv%3D4&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Inian Parameshwaran</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Storage is now available in Supabase" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fstorage%2Fph-1.png&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fstorage%2Fph-1.png&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fstorage%2Fph-1.png&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fstorage%2Fph-1.png&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fstorage%2Fph-1.png&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fstorage%2Fph-1.png&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fstorage%2Fph-1.png&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fstorage%2Fph-1.png&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fstorage%2Fph-1.png&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fstorage%2Fph-1.png&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fstorage%2Fph-1.png&amp;w=3840&amp;q=100"/></div><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<div class="bg-gray-300 rounded-lg px-6 py-2 italic"><p>🆕 Storage has undergone significant enhancements with two major releases since its initial launch. Here is what is new:</p><ul>
<li><a href="storage-image-resizing-smart-cdn.html">Supabase Storage v2: Image resizing and Smart CDN</a></li>
<li><a href="storage-v3-resumable-uploads.html">Supabase Storage v3: Resumable Uploads with support for 50GB files</a></li>
</ul></div>
<p>Today, we are launching one of most requested features - <a href="../storage.html">Storage!</a> When you sign up for Supabase, you get a Postgres database with realtime subscriptions, user management, auto-generated APIs and now a scalable object store. These services integrate very well with each other and you don&#x27;t need to sign up for yet another service just for your storage requirements.</p>
<p>As with anything that we build in Supabase, Storage is fast, secure and scalable. You can use it to store terabytes of Machine Learning training data, your e-commerce product gallery or just your growing collection of JPEGs featuring cats playing synths in space.</p>
<p>This post outlines our design decisions while building Storage, and we&#x27;ll show you how to use it in your own applications.</p>
<h2 id="architecture" class="group scroll-mt-24">Architecture<a href="#architecture" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>There are three key components to Supabase Storage:</p>
<ul>
<li>Backend (where the objects are actually stored).</li>
<li>Middleware (access and permissions). This consists of an API Server and Postgres.</li>
<li>Frontend (a nice UI).</li>
</ul>
<p>The Storage API server sits behind Kong, an API Gateway. It talks to different storage backends like S3, Backblaze, etc to retrieve and store objects.</p>
<p>Object metadata and security rules are stored in your Postgres database.</p>
<p>We have built a powerful file explorer right into the dashboard, and using our Client libraries you can integrate Storage into your applications.</p>
<p></p>
<h2 id="frontend" class="group scroll-mt-24">Frontend<a href="#frontend" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We set out to build the most usable front-end for storing content. Using Lists to display file hierarchies is a poor experience for exploring files - the most common use-case of a shared File system.</p>
<p>As avid Mac users, we&#x27;ve defaulted to using the column-based explorer in Finder as it allow us to quickly drill into nested folders and provides a clear birds eye view of file hierarchies at the same time.</p>
<p>We put in a lot of effort to make sure that the dashboard is fast and easy to navigate with our miller-based column view, allowing you to get to deeply nested folders quickly. If you&#x27;re a fan of List Views though, don&#x27;t worry! We have that option available too. The choice is yours.</p>
<p></p>
<p>Our File Browser also has a rich preview for a wide set of file types including images, audio, and videos.</p>
<p></p>
<p>And if you already know the location of a file but want to save a few clicks, you can paste the path into the location bar and navigate to it directly.</p>
<p></p>
<h2 id="designing-the-storage-middleware" class="group scroll-mt-24">Designing the storage middleware<a href="#designing-the-storage-middleware" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We focused on performance, security and interoperability with the rest of the Supabase ecosystem.</p>
<h3 id="integration-with-the-supabase-ecosystem" class="group scroll-mt-24">Integration with the Supabase ecosystem<a href="#integration-with-the-supabase-ecosystem" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Supabase is a <a href="../docs.html#how-it-works">collection of open source tools</a> which integrate very well with each other. We evaluated open source object storage servers like <a href="https://ceph.io/">Ceph</a>, <a href="https://www.openstack.org/software/releases/ocata/components/swift">Swift</a>, <a href="https://min.io/">Minio</a> and <a href="https://github.com/scality/Zenko">Zenko</a> but none of these tools were a good fit for our existing ecosystem.</p>
<p><strong>Postgres Compatibility</strong></p>
<p>Each of these open source tools are amazing, but they all had a major drawback - we couldn&#x27;t use Postgres as the server&#x27;s datastore. If you haven&#x27;t noticed yet, our team likes Postgres a lot 😉.</p>
<p>For example, Minio <a href="https://docs.min.io/docs/minio-multi-user-quickstart-guide.html">uses etcd</a> (when used in multi-user gateway mode) and Zenko <a href="https://zenko.readthedocs.io/en/latest/operation/Architecture/index.html">requires mongodb and Kafka.</a> Every project on Supabase is a dedicated Postgres database, so leveraging it for object and user metadata is more efficient, reducing the number of dependencies for anyone using the Supabase ecosystem of tools.</p>
<p><strong>Smaller footprint</strong></p>
<p>We are using managed services like <a href="https://aws.amazon.com/s3/">S3</a>, <a href="https://wasabi.com/">Wasabi</a> and <a href="https://www.backblaze.com/">Backblaze</a> for our storage backend. We won&#x27;t be managing our own hard disks and storage capacity, and so we don&#x27;t require a lot of features offered by existing tools. For example, a large part of Minio&#x27;s codebase is to offer erasure encoding and bitrot protection, automatically making use of new disks attached to the cluster.</p>
<p><strong>Integration with Supabase Auth</strong></p>
<p>Existing tools do not play well with our authentication system. For example, Minio is bundled with its own <a href="https://docs.min.io/docs/minio-admin-complete-guide.html#user">auth system</a> and there is no easy way to map Minio users to <a href="../docs/guides/auth.html">Supabase users</a>.</p>
<p>In the end, we opted to build our own <a href="https://github.com/supabase/storage-api">Storage API server</a>.</p>
<h2 id="security" class="group scroll-mt-24">Security<a href="#security" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<h3 id="authentication" class="group scroll-mt-24">Authentication<a href="#authentication" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Our storage service sits behind <a href="https://github.com/kong/kong">Kong</a> along with other services like <a href="https://github.com/PostgREST/postgrest">PostgREST</a> and <a href="https://github.com/supabase/realtime">Realtime</a>. This allows us to reuse our existing Authentication system - any requests with a valid API key are authenticated and passed to the storage API.</p>
<h3 id="authorization" class="group scroll-mt-24">Authorization<a href="#authorization" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>For Authorization, we wanted to avoid creating a new DSL like Firebase. Instead, we created one where you can write policies in the One True Language - SQL!</p>
<p></p>
<p>To do this, we leverage Postgres&#x27; Row Level Security. We create a table for <code class="short-inline-codeblock">buckets</code> and <code class="short-inline-codeblock">objects</code> inside each Supabase project. These tables are namespaced in a separate schema called <code class="short-inline-codeblock">storage</code>.</p>
<p>The idea is simple - if a user is able to <code class="short-inline-codeblock">select</code> from the <code class="short-inline-codeblock">objects</code> table, they can retrieve the object too. Using Postgres&#x27; Row Level Security, you can define fine-grained Policies to determine access levels for different users.</p>
<p>When a user makes a request for a file, the API detects the user in the <code class="short-inline-codeblock">Authorization</code> header and tries to <code class="short-inline-codeblock">select</code> from the <code class="short-inline-codeblock">objects</code> table. If a valid row is returned, the Storage API pipes the object back from S3. Similarly if the user is able to delete the row from the objects table, they can delete the object from the storage backend too.</p>
<p>For example, if you want to give &quot;read&quot; access to a bucket called <code class="short-inline-codeblock">avatars</code> you would use this policy:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>create policy &quot;Read access for avatars.&quot;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>on storage.objects for select using (</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>	bucket_id = &#x27;avatars&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>);</span></div></div><br/></code></div></div>
<p>Extending this example, if you want to give access to a subfolder only (in this case called <code class="short-inline-codeblock">public</code>):</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>create policy &quot;Read access for public avatars.&quot;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>on storage.objects for select using (</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>	bucket_id = &#x27;avatars&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>	and (storage.foldername(name))[1] = &#x27;public&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>);</span></div></div><br/></code></div></div>
<p>We have created helper functions like <code class="short-inline-codeblock">foldername()</code>, <code class="short-inline-codeblock">filename()</code> and <code class="short-inline-codeblock">extension()</code> in the storage schema to make Policies even simpler.</p>
<p>This system integrates with our <a href="../docs/guides/auth.html">User Management system</a>. Here is an example policy which gives access to a particular file to a Supabase user:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>create policy crud_uid_file</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>on storage.objects for all using (</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>	bucket_id = &#x27;avatars&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>	and name = &#x27;folder/only_uid.jpg&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>	and (select auth.uid()) = &#x27;d8c7bce9-cfeb-497b-bd61-e66ce2cbdaa2&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>);</span></div></div><br/></code></div></div>
<p>Using the power of Postgres&#x27; Row Level Security, you can create pretty much any policy imaginable.</p>
<h3 id="performance" class="group scroll-mt-24">Performance<a href="#performance" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>The storage server is built with <a href="https://www.fastify.io/">Fastify</a> and Typescript. Fastify is one of the <a href="https://www.fastify.io/benchmarks/">fastest</a> Node frameworks and our initial benchmark results look very promising.</p>
<p>We use Node streams everywhere. Objects are uploaded directly to S3 with minimal in-memory buffering. This minimizes RAM consumption even while uploading huge objects. The code does become a bit more complicated when using streams. For example, you can&#x27;t figure out the size of the object being uploaded before streaming it to S3. But we believe this tradeoff is worth it.</p>
<p>Postgres is another hidden gem for our storage performance. For example, what if you had a bucket with thousands of objects, and you needed to find all objects which a user has access to? Without Postgres, you would retrieve all objects from that folder, evaluate each policy in the storage middleware and only return the objects which the user has access to. But we can avoid this completely with Postgres! We use Postgres to evaluate all the polices, and the storage middleware just returns the objects which the user has access to.</p>
<p>Supabase Storage has some opinionated defaults with respect to caching. Objects retrieved from S3 typically do not have a <code class="short-inline-codeblock">Cache-Control</code> header. This isn&#x27;t optimal for a Storage system since browsers don&#x27;t cache objects completely without this header. Objects retrieved from Supabase Storage by default have a Cache-Control header of 1 hour. Of course, you can override this behaviour by specifying a different cache time when creating the object.</p>
<h2 id="storage-backend" class="group scroll-mt-24">Storage Backend<a href="#storage-backend" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>File objects are stored in an S3 bucket within the same region as your Supabase project. S3 has a high durability of 99.999999999% and is scalable as an object store.</p>
<p>While we started with S3, other storage backends like Wasabi, Backblaze and Openstack Swift expose an S3 compatible API. It will be simple to add more S3 compatible storage options in the future.</p>
<p>We have intentionally kept the API surface for the storage backend very small, in case the community also wants to add storage options which don&#x27;t support the S3 API.</p>
<h3 id="client-libraries" class="group scroll-mt-24">Client libraries<a href="#client-libraries" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>You may be itching to get started tinkering around with our new Supabase storage - we have an example app prepared for you which simulates a simple context of setting an avatar image for a user. This will help you to get a high level overview of how to use your project&#x27;s storage with our client library. For greater detail, refer to our storage API documentation <a href="https://supabase.com/docs/reference/javascript/storage-createbucket">here</a>.</p>
<p></p>
<p></p>
<h2 id="whats-next" class="group scroll-mt-24">What&#x27;s next<a href="#whats-next" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ul>
<li>We currently support S3 and will be adding more storage backends. Vote for the storage backends you would like to see us implement <a href="https://github.com/supabase/supabase/discussions/982">here</a>.</li>
<li>We will integrate our storage service with a Content Delivery Network. With a CDN, objects are cached on a global network. This leads to faster access times for your users around the world. **SHIPPED: <a href="storage-image-resizing-smart-cdn.html#smart-cdn">Supabase Storage v2: Image resizing and Smart CDN</a></li>
<li>We are working on transformations like resizing of images and automatic optimization of different media types. This makes it easy to embed Supabase objects directly in your websites and mobile applications without additional processing. **SHIPPED: <a href="storage-image-resizing-smart-cdn.html#image-resizing">Supabase Storage v2: Image resizing and Smart CDN</a></li>
<li>A better editor to make authoring policies easier and less error prone.</li>
<li>Reach out to us if you would like to help out with adding storage support in one of the <a href="https://supabase.com/docs/reference/javascript/installing">community maintained client libraries</a>.</li>
</ul>
<p>Take it for a spin on our <a href="https://supabase.com/dashboard/">dashboard</a> and let us know what you think!</p></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-storage&amp;text=Storage%20is%20now%20available%20in%20Supabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-storage&amp;text=Storage%20is%20now%20available%20in%20Supabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-storage&amp;t=Storage%20is%20now%20available%20in%20Supabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="supabase-cli.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Supabase CLI</h4><p class="small">31 March 2021</p></div></div></div></div></a></div><div><a href="pricing.html"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Supabase Beta Pricing</h4><p class="small">29 March 2021</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/supabase"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">supabase</div></a><a href="https://supabase.com/blog/tags/storage"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">storage</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#architecture">Architecture</a></li>
<li><a href="#frontend">Frontend</a></li>
<li><a href="#designing-the-storage-middleware">Designing the storage middleware</a></li>
<li><a href="#security">Security</a></li>
<li><a href="#storage-backend">Storage Backend</a></li>
<li><a href="#whats-next">What&#x27;s next</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-storage&amp;text=Storage%20is%20now%20available%20in%20Supabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-storage&amp;text=Storage%20is%20now%20available%20in%20Supabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-storage&amp;t=Storage%20is%20now%20available%20in%20Supabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"supabase-cli","title":"Supabase CLI","description":"Local development, database migrations, and self-hosting.","author":"soedirgo","image":"cli/cli-og.jpg","thumb":"cli/cli-og.jpg","categories":["product"],"tags":["supabase","storage"],"date":"03-31-2021","formattedDate":"31 March 2021","readingTime":"8 minute read","url":"/blog/supabase-cli","path":"/blog/supabase-cli"},"nextPost":{"slug":"pricing","title":"Supabase Beta Pricing","description":"Supabase launches Beta pricing structure","author":"rory_wilding","image":"pricing-og.jpg","thumb":"launch-week-pricing.jpg","categories":["company"],"tags":["supabase"],"date":"03-29-2021","formattedDate":"29 March 2021","readingTime":"5 minute read","url":"/blog/pricing","path":"/blog/pricing"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-storage","source":"\n\u003cdiv className=\"bg-gray-300 rounded-lg px-6 py-2 italic\"\u003e\n\n🆕 Storage has undergone significant enhancements with two major releases since its initial launch. Here is what is new:\n\n- [Supabase Storage v2: Image resizing and Smart CDN](https://supabase.com/blog/storage-image-resizing-smart-cdn)\n- [Supabase Storage v3: Resumable Uploads with support for 50GB files](https://supabase.com/blog/storage-v3-resumable-uploads)\n\n\u003c/div\u003e\n\nToday, we are launching one of most requested features - [Storage!](https://supabase.com/storage) When you sign up for Supabase, you get a Postgres database with realtime subscriptions, user management, auto-generated APIs and now a scalable object store. These services integrate very well with each other and you don't need to sign up for yet another service just for your storage requirements.\n\nAs with anything that we build in Supabase, Storage is fast, secure and scalable. You can use it to store terabytes of Machine Learning training data, your e-commerce product gallery or just your growing collection of JPEGs featuring cats playing synths in space.\n\nThis post outlines our design decisions while building Storage, and we'll show you how to use it in your own applications.\n\n## Architecture\n\nThere are three key components to Supabase Storage:\n\n- Backend (where the objects are actually stored).\n- Middleware (access and permissions). This consists of an API Server and Postgres.\n- Frontend (a nice UI).\n\nThe Storage API server sits behind Kong, an API Gateway. It talks to different storage backends like S3, Backblaze, etc to retrieve and store objects.\n\nObject metadata and security rules are stored in your Postgres database.\n\nWe have built a powerful file explorer right into the dashboard, and using our Client libraries you can integrate Storage into your applications.\n\n![Storage Infrastructure](/images/blog/storage/infra.png)\n\n## Frontend\n\nWe set out to build the most usable front-end for storing content. Using Lists to display file hierarchies is a poor experience for exploring files - the most common use-case of a shared File system.\n\nAs avid Mac users, we've defaulted to using the column-based explorer in Finder as it allow us to quickly drill into nested folders and provides a clear birds eye view of file hierarchies at the same time.\n\nWe put in a lot of effort to make sure that the dashboard is fast and easy to navigate with our miller-based column view, allowing you to get to deeply nested folders quickly. If you're a fan of List Views though, don't worry! We have that option available too. The choice is yours.\n\n![Frontend views](/images/blog/storage/ph-5.png)\n\nOur File Browser also has a rich preview for a wide set of file types including images, audio, and videos.\n\n![Frontend preview](/images/blog/storage/ph-2.png)\n\nAnd if you already know the location of a file but want to save a few clicks, you can paste the path into the location bar and navigate to it directly.\n\n![Frontend navigation](/images/blog/storage/ph-4.png)\n\n## Designing the storage middleware\n\nWe focused on performance, security and interoperability with the rest of the Supabase ecosystem.\n\n### Integration with the Supabase ecosystem\n\nSupabase is a [collection of open source tools](/docs#how-it-works) which integrate very well with each other. We evaluated open source object storage servers like [Ceph](https://ceph.io/), [Swift](https://www.openstack.org/software/releases/ocata/components/swift), [Minio](https://min.io/) and [Zenko](https://github.com/scality/Zenko) but none of these tools were a good fit for our existing ecosystem.\n\n**Postgres Compatibility**\n\nEach of these open source tools are amazing, but they all had a major drawback - we couldn't use Postgres as the server's datastore. If you haven't noticed yet, our team likes Postgres a lot 😉.\n\nFor example, Minio [uses etcd](https://docs.min.io/docs/minio-multi-user-quickstart-guide.html) (when used in multi-user gateway mode) and Zenko [requires mongodb and Kafka.](https://zenko.readthedocs.io/en/latest/operation/Architecture/index.html) Every project on Supabase is a dedicated Postgres database, so leveraging it for object and user metadata is more efficient, reducing the number of dependencies for anyone using the Supabase ecosystem of tools.\n\n**Smaller footprint**\n\nWe are using managed services like [S3](https://aws.amazon.com/s3/), [Wasabi](https://wasabi.com/) and [Backblaze](https://www.backblaze.com/) for our storage backend. We won't be managing our own hard disks and storage capacity, and so we don't require a lot of features offered by existing tools. For example, a large part of Minio's codebase is to offer erasure encoding and bitrot protection, automatically making use of new disks attached to the cluster.\n\n**Integration with Supabase Auth**\n\nExisting tools do not play well with our authentication system. For example, Minio is bundled with its own [auth system](https://docs.min.io/docs/minio-admin-complete-guide.html#user) and there is no easy way to map Minio users to [Supabase users](/docs/guides/auth).\n\nIn the end, we opted to build our own [Storage API server](https://github.com/supabase/storage-api).\n\n## Security\n\n### Authentication\n\nOur storage service sits behind [Kong](https://github.com/kong/kong) along with other services like [PostgREST](https://github.com/PostgREST/postgrest) and [Realtime](https://github.com/supabase/realtime). This allows us to reuse our existing Authentication system - any requests with a valid API key are authenticated and passed to the storage API.\n\n### Authorization\n\nFor Authorization, we wanted to avoid creating a new DSL like Firebase. Instead, we created one where you can write policies in the One True Language - SQL!\n\n![One True Language](/images/blog/storage/true-language.png)\n\nTo do this, we leverage Postgres' Row Level Security. We create a table for `buckets` and `objects` inside each Supabase project. These tables are namespaced in a separate schema called `storage`.\n\nThe idea is simple - if a user is able to `select` from the `objects` table, they can retrieve the object too. Using Postgres' Row Level Security, you can define fine-grained Policies to determine access levels for different users.\n\nWhen a user makes a request for a file, the API detects the user in the `Authorization` header and tries to `select` from the `objects` table. If a valid row is returned, the Storage API pipes the object back from S3. Similarly if the user is able to delete the row from the objects table, they can delete the object from the storage backend too.\n\nFor example, if you want to give \"read\" access to a bucket called `avatars` you would use this policy:\n\n```sql\ncreate policy \"Read access for avatars.\"\non storage.objects for select using (\n\tbucket_id = 'avatars'\n);\n```\n\nExtending this example, if you want to give access to a subfolder only (in this case called `public`):\n\n```sql\ncreate policy \"Read access for public avatars.\"\non storage.objects for select using (\n\tbucket_id = 'avatars'\n\tand (storage.foldername(name))[1] = 'public'\n);\n```\n\nWe have created helper functions like `foldername()`, `filename()` and `extension()` in the storage schema to make Policies even simpler.\n\nThis system integrates with our [User Management system](/docs/guides/auth). Here is an example policy which gives access to a particular file to a Supabase user:\n\n```sql\ncreate policy crud_uid_file\non storage.objects for all using (\n\tbucket_id = 'avatars'\n\tand name = 'folder/only_uid.jpg'\n\tand (select auth.uid()) = 'd8c7bce9-cfeb-497b-bd61-e66ce2cbdaa2'\n);\n```\n\nUsing the power of Postgres' Row Level Security, you can create pretty much any policy imaginable.\n\n### Performance\n\nThe storage server is built with [Fastify](https://www.fastify.io/) and Typescript. Fastify is one of the [fastest](https://www.fastify.io/benchmarks/) Node frameworks and our initial benchmark results look very promising.\n\nWe use Node streams everywhere. Objects are uploaded directly to S3 with minimal in-memory buffering. This minimizes RAM consumption even while uploading huge objects. The code does become a bit more complicated when using streams. For example, you can't figure out the size of the object being uploaded before streaming it to S3. But we believe this tradeoff is worth it.\n\nPostgres is another hidden gem for our storage performance. For example, what if you had a bucket with thousands of objects, and you needed to find all objects which a user has access to? Without Postgres, you would retrieve all objects from that folder, evaluate each policy in the storage middleware and only return the objects which the user has access to. But we can avoid this completely with Postgres! We use Postgres to evaluate all the polices, and the storage middleware just returns the objects which the user has access to.\n\nSupabase Storage has some opinionated defaults with respect to caching. Objects retrieved from S3 typically do not have a `Cache-Control` header. This isn't optimal for a Storage system since browsers don't cache objects completely without this header. Objects retrieved from Supabase Storage by default have a Cache-Control header of 1 hour. Of course, you can override this behaviour by specifying a different cache time when creating the object.\n\n## Storage Backend\n\nFile objects are stored in an S3 bucket within the same region as your Supabase project. S3 has a high durability of 99.999999999% and is scalable as an object store.\n\nWhile we started with S3, other storage backends like Wasabi, Backblaze and Openstack Swift expose an S3 compatible API. It will be simple to add more S3 compatible storage options in the future.\n\nWe have intentionally kept the API surface for the storage backend very small, in case the community also wants to add storage options which don't support the S3 API.\n\n### Client libraries\n\nYou may be itching to get started tinkering around with our new Supabase storage - we have an example app prepared for you which simulates a simple context of setting an avatar image for a user. This will help you to get a high level overview of how to use your project's storage with our client library. For greater detail, refer to our storage API documentation [here](/docs/reference/javascript/storage-createbucket).\n\n![Example app 1](/images/blog/storage/ph-7.png)\n\n![Example app 2](/images/blog/storage/ph-6.png)\n\n## What's next\n\n- We currently support S3 and will be adding more storage backends. Vote for the storage backends you would like to see us implement [here](https://github.com/supabase/supabase/discussions/982).\n- We will integrate our storage service with a Content Delivery Network. With a CDN, objects are cached on a global network. This leads to faster access times for your users around the world. \\*\\*SHIPPED: [Supabase Storage v2: Image resizing and Smart CDN](https://supabase.com/blog/storage-image-resizing-smart-cdn#smart-cdn)\n- We are working on transformations like resizing of images and automatic optimization of different media types. This makes it easy to embed Supabase objects directly in your websites and mobile applications without additional processing. \\*\\*SHIPPED: [Supabase Storage v2: Image resizing and Smart CDN](https://supabase.com/blog/storage-image-resizing-smart-cdn#image-resizing)\n- A better editor to make authoring policies easier and less error prone.\n- Reach out to us if you would like to help out with adding storage support in one of the [community maintained client libraries](/docs/reference/javascript/installing).\n\nTake it for a spin on our [dashboard](https://supabase.com/dashboard/) and let us know what you think!\n","title":"Storage is now available in Supabase","description":"Launching Supabase Storage and how you can use it in your apps","author":"inian","image":"storage/ph-1.png","thumb":"storage/ph-1.png","categories":["product"],"tags":["supabase","storage"],"date":"03-30-2021","content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    ul: \"ul\",\n    li: \"li\",\n    a: \"a\",\n    h2: \"h2\",\n    img: \"img\",\n    h3: \"h3\",\n    strong: \"strong\",\n    code: \"code\"\n  }, _provideComponents(), props.components), {CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsxs(\"div\", {\n      className: \"bg-gray-300 rounded-lg px-6 py-2 italic\",\n      children: [_jsx(_components.p, {\n        children: \"🆕 Storage has undergone significant enhancements with two major releases since its initial launch. Here is what is new:\"\n      }), _jsxs(_components.ul, {\n        children: [\"\\n\", _jsx(_components.li, {\n          children: _jsx(_components.a, {\n            href: \"https://supabase.com/blog/storage-image-resizing-smart-cdn\",\n            children: \"Supabase Storage v2: Image resizing and Smart CDN\"\n          })\n        }), \"\\n\", _jsx(_components.li, {\n          children: _jsx(_components.a, {\n            href: \"https://supabase.com/blog/storage-v3-resumable-uploads\",\n            children: \"Supabase Storage v3: Resumable Uploads with support for 50GB files\"\n          })\n        }), \"\\n\"]\n      })]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Today, we are launching one of most requested features - \", _jsx(_components.a, {\n        href: \"https://supabase.com/storage\",\n        children: \"Storage!\"\n      }), \" When you sign up for Supabase, you get a Postgres database with realtime subscriptions, user management, auto-generated APIs and now a scalable object store. These services integrate very well with each other and you don't need to sign up for yet another service just for your storage requirements.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"As with anything that we build in Supabase, Storage is fast, secure and scalable. You can use it to store terabytes of Machine Learning training data, your e-commerce product gallery or just your growing collection of JPEGs featuring cats playing synths in space.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This post outlines our design decisions while building Storage, and we'll show you how to use it in your own applications.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"architecture\",\n      children: \"Architecture\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"There are three key components to Supabase Storage:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Backend (where the objects are actually stored).\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Middleware (access and permissions). This consists of an API Server and Postgres.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Frontend (a nice UI).\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The Storage API server sits behind Kong, an API Gateway. It talks to different storage backends like S3, Backblaze, etc to retrieve and store objects.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Object metadata and security rules are stored in your Postgres database.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We have built a powerful file explorer right into the dashboard, and using our Client libraries you can integrate Storage into your applications.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/storage/infra.png\",\n        alt: \"Storage Infrastructure\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"frontend\",\n      children: \"Frontend\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We set out to build the most usable front-end for storing content. Using Lists to display file hierarchies is a poor experience for exploring files - the most common use-case of a shared File system.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"As avid Mac users, we've defaulted to using the column-based explorer in Finder as it allow us to quickly drill into nested folders and provides a clear birds eye view of file hierarchies at the same time.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We put in a lot of effort to make sure that the dashboard is fast and easy to navigate with our miller-based column view, allowing you to get to deeply nested folders quickly. If you're a fan of List Views though, don't worry! We have that option available too. The choice is yours.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/storage/ph-5.png\",\n        alt: \"Frontend views\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Our File Browser also has a rich preview for a wide set of file types including images, audio, and videos.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/storage/ph-2.png\",\n        alt: \"Frontend preview\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"And if you already know the location of a file but want to save a few clicks, you can paste the path into the location bar and navigate to it directly.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/storage/ph-4.png\",\n        alt: \"Frontend navigation\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"designing-the-storage-middleware\",\n      children: \"Designing the storage middleware\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We focused on performance, security and interoperability with the rest of the Supabase ecosystem.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"integration-with-the-supabase-ecosystem\",\n      children: \"Integration with the Supabase ecosystem\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Supabase is a \", _jsx(_components.a, {\n        href: \"/docs#how-it-works\",\n        children: \"collection of open source tools\"\n      }), \" which integrate very well with each other. We evaluated open source object storage servers like \", _jsx(_components.a, {\n        href: \"https://ceph.io/\",\n        children: \"Ceph\"\n      }), \", \", _jsx(_components.a, {\n        href: \"https://www.openstack.org/software/releases/ocata/components/swift\",\n        children: \"Swift\"\n      }), \", \", _jsx(_components.a, {\n        href: \"https://min.io/\",\n        children: \"Minio\"\n      }), \" and \", _jsx(_components.a, {\n        href: \"https://github.com/scality/Zenko\",\n        children: \"Zenko\"\n      }), \" but none of these tools were a good fit for our existing ecosystem.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.strong, {\n        children: \"Postgres Compatibility\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Each of these open source tools are amazing, but they all had a major drawback - we couldn't use Postgres as the server's datastore. If you haven't noticed yet, our team likes Postgres a lot 😉.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"For example, Minio \", _jsx(_components.a, {\n        href: \"https://docs.min.io/docs/minio-multi-user-quickstart-guide.html\",\n        children: \"uses etcd\"\n      }), \" (when used in multi-user gateway mode) and Zenko \", _jsx(_components.a, {\n        href: \"https://zenko.readthedocs.io/en/latest/operation/Architecture/index.html\",\n        children: \"requires mongodb and Kafka.\"\n      }), \" Every project on Supabase is a dedicated Postgres database, so leveraging it for object and user metadata is more efficient, reducing the number of dependencies for anyone using the Supabase ecosystem of tools.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.strong, {\n        children: \"Smaller footprint\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We are using managed services like \", _jsx(_components.a, {\n        href: \"https://aws.amazon.com/s3/\",\n        children: \"S3\"\n      }), \", \", _jsx(_components.a, {\n        href: \"https://wasabi.com/\",\n        children: \"Wasabi\"\n      }), \" and \", _jsx(_components.a, {\n        href: \"https://www.backblaze.com/\",\n        children: \"Backblaze\"\n      }), \" for our storage backend. We won't be managing our own hard disks and storage capacity, and so we don't require a lot of features offered by existing tools. For example, a large part of Minio's codebase is to offer erasure encoding and bitrot protection, automatically making use of new disks attached to the cluster.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.strong, {\n        children: \"Integration with Supabase Auth\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Existing tools do not play well with our authentication system. For example, Minio is bundled with its own \", _jsx(_components.a, {\n        href: \"https://docs.min.io/docs/minio-admin-complete-guide.html#user\",\n        children: \"auth system\"\n      }), \" and there is no easy way to map Minio users to \", _jsx(_components.a, {\n        href: \"/docs/guides/auth\",\n        children: \"Supabase users\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In the end, we opted to build our own \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/storage-api\",\n        children: \"Storage API server\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"security\",\n      children: \"Security\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"authentication\",\n      children: \"Authentication\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Our storage service sits behind \", _jsx(_components.a, {\n        href: \"https://github.com/kong/kong\",\n        children: \"Kong\"\n      }), \" along with other services like \", _jsx(_components.a, {\n        href: \"https://github.com/PostgREST/postgrest\",\n        children: \"PostgREST\"\n      }), \" and \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/realtime\",\n        children: \"Realtime\"\n      }), \". This allows us to reuse our existing Authentication system - any requests with a valid API key are authenticated and passed to the storage API.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"authorization\",\n      children: \"Authorization\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"For Authorization, we wanted to avoid creating a new DSL like Firebase. Instead, we created one where you can write policies in the One True Language - SQL!\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/storage/true-language.png\",\n        alt: \"One True Language\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To do this, we leverage Postgres' Row Level Security. We create a table for \", _jsx(_components.code, {\n        children: \"buckets\"\n      }), \" and \", _jsx(_components.code, {\n        children: \"objects\"\n      }), \" inside each Supabase project. These tables are namespaced in a separate schema called \", _jsx(_components.code, {\n        children: \"storage\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The idea is simple - if a user is able to \", _jsx(_components.code, {\n        children: \"select\"\n      }), \" from the \", _jsx(_components.code, {\n        children: \"objects\"\n      }), \" table, they can retrieve the object too. Using Postgres' Row Level Security, you can define fine-grained Policies to determine access levels for different users.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"When a user makes a request for a file, the API detects the user in the \", _jsx(_components.code, {\n        children: \"Authorization\"\n      }), \" header and tries to \", _jsx(_components.code, {\n        children: \"select\"\n      }), \" from the \", _jsx(_components.code, {\n        children: \"objects\"\n      }), \" table. If a valid row is returned, the Storage API pipes the object back from S3. Similarly if the user is able to delete the row from the objects table, they can delete the object from the storage backend too.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"For example, if you want to give \\\"read\\\" access to a bucket called \", _jsx(_components.code, {\n        children: \"avatars\"\n      }), \" you would use this policy:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"create policy \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"Read access for avatars.\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"on \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"storage\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"objects\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" for \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"select using\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\tbucket_id \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'avatars'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \");\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Extending this example, if you want to give access to a subfolder only (in this case called \", _jsx(_components.code, {\n        children: \"public\"\n      }), \"):\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"create policy \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"Read access for public avatars.\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"on \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"storage\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"objects\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" for \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"select using\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\tbucket_id \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'avatars'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\tand\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"storage\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"foldername\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"name\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"))[1] \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'public'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \");\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We have created helper functions like \", _jsx(_components.code, {\n        children: \"foldername()\"\n      }), \", \", _jsx(_components.code, {\n        children: \"filename()\"\n      }), \" and \", _jsx(_components.code, {\n        children: \"extension()\"\n      }), \" in the storage schema to make Policies even simpler.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"This system integrates with our \", _jsx(_components.a, {\n        href: \"/docs/guides/auth\",\n        children: \"User Management system\"\n      }), \". Here is an example policy which gives access to a particular file to a Supabase user:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"create policy\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" crud_uid_file\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"on \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"storage\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"objects\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" for all \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"using\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\tbucket_id \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'avatars'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\tand name = \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'folder/only_uid.jpg'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\tand\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"select \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"auth\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"uid\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"()) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'d8c7bce9-cfeb-497b-bd61-e66ce2cbdaa2'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \");\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Using the power of Postgres' Row Level Security, you can create pretty much any policy imaginable.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"performance\",\n      children: \"Performance\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The storage server is built with \", _jsx(_components.a, {\n        href: \"https://www.fastify.io/\",\n        children: \"Fastify\"\n      }), \" and Typescript. Fastify is one of the \", _jsx(_components.a, {\n        href: \"https://www.fastify.io/benchmarks/\",\n        children: \"fastest\"\n      }), \" Node frameworks and our initial benchmark results look very promising.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We use Node streams everywhere. Objects are uploaded directly to S3 with minimal in-memory buffering. This minimizes RAM consumption even while uploading huge objects. The code does become a bit more complicated when using streams. For example, you can't figure out the size of the object being uploaded before streaming it to S3. But we believe this tradeoff is worth it.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Postgres is another hidden gem for our storage performance. For example, what if you had a bucket with thousands of objects, and you needed to find all objects which a user has access to? Without Postgres, you would retrieve all objects from that folder, evaluate each policy in the storage middleware and only return the objects which the user has access to. But we can avoid this completely with Postgres! We use Postgres to evaluate all the polices, and the storage middleware just returns the objects which the user has access to.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Supabase Storage has some opinionated defaults with respect to caching. Objects retrieved from S3 typically do not have a \", _jsx(_components.code, {\n        children: \"Cache-Control\"\n      }), \" header. This isn't optimal for a Storage system since browsers don't cache objects completely without this header. Objects retrieved from Supabase Storage by default have a Cache-Control header of 1 hour. Of course, you can override this behaviour by specifying a different cache time when creating the object.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"storage-backend\",\n      children: \"Storage Backend\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"File objects are stored in an S3 bucket within the same region as your Supabase project. S3 has a high durability of 99.999999999% and is scalable as an object store.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"While we started with S3, other storage backends like Wasabi, Backblaze and Openstack Swift expose an S3 compatible API. It will be simple to add more S3 compatible storage options in the future.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We have intentionally kept the API surface for the storage backend very small, in case the community also wants to add storage options which don't support the S3 API.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"client-libraries\",\n      children: \"Client libraries\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You may be itching to get started tinkering around with our new Supabase storage - we have an example app prepared for you which simulates a simple context of setting an avatar image for a user. This will help you to get a high level overview of how to use your project's storage with our client library. For greater detail, refer to our storage API documentation \", _jsx(_components.a, {\n        href: \"/docs/reference/javascript/storage-createbucket\",\n        children: \"here\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/storage/ph-7.png\",\n        alt: \"Example app 1\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/storage/ph-6.png\",\n        alt: \"Example app 2\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"whats-next\",\n      children: \"What's next\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"We currently support S3 and will be adding more storage backends. Vote for the storage backends you would like to see us implement \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase/discussions/982\",\n          children: \"here\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"We will integrate our storage service with a Content Delivery Network. With a CDN, objects are cached on a global network. This leads to faster access times for your users around the world. **SHIPPED: \", _jsx(_components.a, {\n          href: \"https://supabase.com/blog/storage-image-resizing-smart-cdn#smart-cdn\",\n          children: \"Supabase Storage v2: Image resizing and Smart CDN\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"We are working on transformations like resizing of images and automatic optimization of different media types. This makes it easy to embed Supabase objects directly in your websites and mobile applications without additional processing. **SHIPPED: \", _jsx(_components.a, {\n          href: \"https://supabase.com/blog/storage-image-resizing-smart-cdn#image-resizing\",\n          children: \"Supabase Storage v2: Image resizing and Smart CDN\"\n        })]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"A better editor to make authoring policies easier and less error prone.\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Reach out to us if you would like to help out with adding storage support in one of the \", _jsx(_components.a, {\n          href: \"/docs/reference/javascript/installing\",\n          children: \"community maintained client libraries\"\n        }), \".\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Take it for a spin on our \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard/\",\n        children: \"dashboard\"\n      }), \" and let us know what you think!\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"Architecture","slug":"architecture","lvl":2,"i":0,"seen":0},{"content":"Frontend","slug":"frontend","lvl":2,"i":1,"seen":0},{"content":"Designing the storage middleware","slug":"designing-the-storage-middleware","lvl":2,"i":2,"seen":0},{"content":"Integration with the Supabase ecosystem","slug":"integration-with-the-supabase-ecosystem","lvl":3,"i":3,"seen":0},{"content":"Security","slug":"security","lvl":2,"i":4,"seen":0},{"content":"Authentication","slug":"authentication","lvl":3,"i":5,"seen":0},{"content":"Authorization","slug":"authorization","lvl":3,"i":6,"seen":0},{"content":"Performance","slug":"performance","lvl":3,"i":7,"seen":0},{"content":"Storage Backend","slug":"storage-backend","lvl":2,"i":8,"seen":0},{"content":"Client libraries","slug":"client-libraries","lvl":3,"i":9,"seen":0},{"content":"What's next","slug":"whats-next","lvl":2,"i":10,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,2],"level":0},{"type":"inline","content":"\u003cdiv className=\"bg-gray-300 rounded-lg px-6 py-2 italic\"\u003e","level":1,"lines":[1,2],"children":[{"type":"text","content":"\u003cdiv className=\"bg-gray-300 rounded-lg px-6 py-2 italic\"\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[3,4],"level":0},{"type":"inline","content":"🆕 Storage has undergone significant enhancements with two major releases since its initial launch. Here is what is new:","level":1,"lines":[3,4],"children":[{"type":"text","content":"🆕 Storage has undergone significant enhancements with two major releases since its initial launch. Here is what is new:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[5,8],"level":0},{"type":"list_item_open","lines":[5,6],"level":1},{"type":"paragraph_open","tight":true,"lines":[5,6],"level":2},{"type":"inline","content":"[Supabase Storage v2: Image resizing and Smart CDN](https://supabase.com/blog/storage-image-resizing-smart-cdn)","level":3,"lines":[5,6],"children":[{"type":"link_open","href":"https://supabase.com/blog/storage-image-resizing-smart-cdn","title":"","level":0},{"type":"text","content":"Supabase Storage v2: Image resizing and Smart CDN","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[6,8],"level":1},{"type":"paragraph_open","tight":true,"lines":[6,7],"level":2},{"type":"inline","content":"[Supabase Storage v3: Resumable Uploads with support for 50GB files](https://supabase.com/blog/storage-v3-resumable-uploads)","level":3,"lines":[6,7],"children":[{"type":"link_open","href":"https://supabase.com/blog/storage-v3-resumable-uploads","title":"","level":0},{"type":"text","content":"Supabase Storage v3: Resumable Uploads with support for 50GB files","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[8,9],"level":0},{"type":"inline","content":"\u003c/div\u003e","level":1,"lines":[8,9],"children":[{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[10,11],"level":0},{"type":"inline","content":"Today, we are launching one of most requested features - [Storage!](https://supabase.com/storage) When you sign up for Supabase, you get a Postgres database with realtime subscriptions, user management, auto-generated APIs and now a scalable object store. These services integrate very well with each other and you don't need to sign up for yet another service just for your storage requirements.","level":1,"lines":[10,11],"children":[{"type":"text","content":"Today, we are launching one of most requested features - ","level":0},{"type":"link_open","href":"https://supabase.com/storage","title":"","level":0},{"type":"text","content":"Storage!","level":1},{"type":"link_close","level":0},{"type":"text","content":" When you sign up for Supabase, you get a Postgres database with realtime subscriptions, user management, auto-generated APIs and now a scalable object store. These services integrate very well with each other and you don't need to sign up for yet another service just for your storage requirements.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[12,13],"level":0},{"type":"inline","content":"As with anything that we build in Supabase, Storage is fast, secure and scalable. You can use it to store terabytes of Machine Learning training data, your e-commerce product gallery or just your growing collection of JPEGs featuring cats playing synths in space.","level":1,"lines":[12,13],"children":[{"type":"text","content":"As with anything that we build in Supabase, Storage is fast, secure and scalable. You can use it to store terabytes of Machine Learning training data, your e-commerce product gallery or just your growing collection of JPEGs featuring cats playing synths in space.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[14,15],"level":0},{"type":"inline","content":"This post outlines our design decisions while building Storage, and we'll show you how to use it in your own applications.","level":1,"lines":[14,15],"children":[{"type":"text","content":"This post outlines our design decisions while building Storage, and we'll show you how to use it in your own applications.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[16,17],"level":0},{"type":"inline","content":"[Architecture](#architecture)","level":1,"lines":[16,17],"children":[{"type":"text","content":"Architecture","level":0}],"lvl":2,"i":0,"seen":0,"slug":"architecture"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[18,19],"level":0},{"type":"inline","content":"There are three key components to Supabase Storage:","level":1,"lines":[18,19],"children":[{"type":"text","content":"There are three key components to Supabase Storage:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[20,24],"level":0},{"type":"list_item_open","lines":[20,21],"level":1},{"type":"paragraph_open","tight":true,"lines":[20,21],"level":2},{"type":"inline","content":"Backend (where the objects are actually stored).","level":3,"lines":[20,21],"children":[{"type":"text","content":"Backend (where the objects are actually stored).","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[21,22],"level":1},{"type":"paragraph_open","tight":true,"lines":[21,22],"level":2},{"type":"inline","content":"Middleware (access and permissions). This consists of an API Server and Postgres.","level":3,"lines":[21,22],"children":[{"type":"text","content":"Middleware (access and permissions). This consists of an API Server and Postgres.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[22,24],"level":1},{"type":"paragraph_open","tight":true,"lines":[22,23],"level":2},{"type":"inline","content":"Frontend (a nice UI).","level":3,"lines":[22,23],"children":[{"type":"text","content":"Frontend (a nice UI).","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[24,25],"level":0},{"type":"inline","content":"The Storage API server sits behind Kong, an API Gateway. It talks to different storage backends like S3, Backblaze, etc to retrieve and store objects.","level":1,"lines":[24,25],"children":[{"type":"text","content":"The Storage API server sits behind Kong, an API Gateway. It talks to different storage backends like S3, Backblaze, etc to retrieve and store objects.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[26,27],"level":0},{"type":"inline","content":"Object metadata and security rules are stored in your Postgres database.","level":1,"lines":[26,27],"children":[{"type":"text","content":"Object metadata and security rules are stored in your Postgres database.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[28,29],"level":0},{"type":"inline","content":"We have built a powerful file explorer right into the dashboard, and using our Client libraries you can integrate Storage into your applications.","level":1,"lines":[28,29],"children":[{"type":"text","content":"We have built a powerful file explorer right into the dashboard, and using our Client libraries you can integrate Storage into your applications.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[30,31],"level":0},{"type":"inline","content":"![Storage Infrastructure](/images/blog/storage/infra.png)","level":1,"lines":[30,31],"children":[{"type":"image","src":"/images/blog/storage/infra.png","title":"","alt":"Storage Infrastructure","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[32,33],"level":0},{"type":"inline","content":"[Frontend](#frontend)","level":1,"lines":[32,33],"children":[{"type":"text","content":"Frontend","level":0}],"lvl":2,"i":1,"seen":0,"slug":"frontend"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[34,35],"level":0},{"type":"inline","content":"We set out to build the most usable front-end for storing content. Using Lists to display file hierarchies is a poor experience for exploring files - the most common use-case of a shared File system.","level":1,"lines":[34,35],"children":[{"type":"text","content":"We set out to build the most usable front-end for storing content. Using Lists to display file hierarchies is a poor experience for exploring files - the most common use-case of a shared File system.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[36,37],"level":0},{"type":"inline","content":"As avid Mac users, we've defaulted to using the column-based explorer in Finder as it allow us to quickly drill into nested folders and provides a clear birds eye view of file hierarchies at the same time.","level":1,"lines":[36,37],"children":[{"type":"text","content":"As avid Mac users, we've defaulted to using the column-based explorer in Finder as it allow us to quickly drill into nested folders and provides a clear birds eye view of file hierarchies at the same time.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[38,39],"level":0},{"type":"inline","content":"We put in a lot of effort to make sure that the dashboard is fast and easy to navigate with our miller-based column view, allowing you to get to deeply nested folders quickly. If you're a fan of List Views though, don't worry! We have that option available too. The choice is yours.","level":1,"lines":[38,39],"children":[{"type":"text","content":"We put in a lot of effort to make sure that the dashboard is fast and easy to navigate with our miller-based column view, allowing you to get to deeply nested folders quickly. If you're a fan of List Views though, don't worry! We have that option available too. The choice is yours.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[40,41],"level":0},{"type":"inline","content":"![Frontend views](/images/blog/storage/ph-5.png)","level":1,"lines":[40,41],"children":[{"type":"image","src":"/images/blog/storage/ph-5.png","title":"","alt":"Frontend views","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[42,43],"level":0},{"type":"inline","content":"Our File Browser also has a rich preview for a wide set of file types including images, audio, and videos.","level":1,"lines":[42,43],"children":[{"type":"text","content":"Our File Browser also has a rich preview for a wide set of file types including images, audio, and videos.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[44,45],"level":0},{"type":"inline","content":"![Frontend preview](/images/blog/storage/ph-2.png)","level":1,"lines":[44,45],"children":[{"type":"image","src":"/images/blog/storage/ph-2.png","title":"","alt":"Frontend preview","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[46,47],"level":0},{"type":"inline","content":"And if you already know the location of a file but want to save a few clicks, you can paste the path into the location bar and navigate to it directly.","level":1,"lines":[46,47],"children":[{"type":"text","content":"And if you already know the location of a file but want to save a few clicks, you can paste the path into the location bar and navigate to it directly.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[48,49],"level":0},{"type":"inline","content":"![Frontend navigation](/images/blog/storage/ph-4.png)","level":1,"lines":[48,49],"children":[{"type":"image","src":"/images/blog/storage/ph-4.png","title":"","alt":"Frontend navigation","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[50,51],"level":0},{"type":"inline","content":"[Designing the storage middleware](#designing-the-storage-middleware)","level":1,"lines":[50,51],"children":[{"type":"text","content":"Designing the storage middleware","level":0}],"lvl":2,"i":2,"seen":0,"slug":"designing-the-storage-middleware"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[52,53],"level":0},{"type":"inline","content":"We focused on performance, security and interoperability with the rest of the Supabase ecosystem.","level":1,"lines":[52,53],"children":[{"type":"text","content":"We focused on performance, security and interoperability with the rest of the Supabase ecosystem.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[54,55],"level":0},{"type":"inline","content":"[Integration with the Supabase ecosystem](#integration-with-the-supabase-ecosystem)","level":1,"lines":[54,55],"children":[{"type":"text","content":"Integration with the Supabase ecosystem","level":0}],"lvl":3,"i":3,"seen":0,"slug":"integration-with-the-supabase-ecosystem"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[56,57],"level":0},{"type":"inline","content":"Supabase is a [collection of open source tools](/docs#how-it-works) which integrate very well with each other. We evaluated open source object storage servers like [Ceph](https://ceph.io/), [Swift](https://www.openstack.org/software/releases/ocata/components/swift), [Minio](https://min.io/) and [Zenko](https://github.com/scality/Zenko) but none of these tools were a good fit for our existing ecosystem.","level":1,"lines":[56,57],"children":[{"type":"text","content":"Supabase is a ","level":0},{"type":"link_open","href":"/docs#how-it-works","title":"","level":0},{"type":"text","content":"collection of open source tools","level":1},{"type":"link_close","level":0},{"type":"text","content":" which integrate very well with each other. We evaluated open source object storage servers like ","level":0},{"type":"link_open","href":"https://ceph.io/","title":"","level":0},{"type":"text","content":"Ceph","level":1},{"type":"link_close","level":0},{"type":"text","content":", ","level":0},{"type":"link_open","href":"https://www.openstack.org/software/releases/ocata/components/swift","title":"","level":0},{"type":"text","content":"Swift","level":1},{"type":"link_close","level":0},{"type":"text","content":", ","level":0},{"type":"link_open","href":"https://min.io/","title":"","level":0},{"type":"text","content":"Minio","level":1},{"type":"link_close","level":0},{"type":"text","content":" and ","level":0},{"type":"link_open","href":"https://github.com/scality/Zenko","title":"","level":0},{"type":"text","content":"Zenko","level":1},{"type":"link_close","level":0},{"type":"text","content":" but none of these tools were a good fit for our existing ecosystem.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[58,59],"level":0},{"type":"inline","content":"**Postgres Compatibility**","level":1,"lines":[58,59],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Postgres Compatibility","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[60,61],"level":0},{"type":"inline","content":"Each of these open source tools are amazing, but they all had a major drawback - we couldn't use Postgres as the server's datastore. If you haven't noticed yet, our team likes Postgres a lot 😉.","level":1,"lines":[60,61],"children":[{"type":"text","content":"Each of these open source tools are amazing, but they all had a major drawback - we couldn't use Postgres as the server's datastore. If you haven't noticed yet, our team likes Postgres a lot 😉.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[62,63],"level":0},{"type":"inline","content":"For example, Minio [uses etcd](https://docs.min.io/docs/minio-multi-user-quickstart-guide.html) (when used in multi-user gateway mode) and Zenko [requires mongodb and Kafka.](https://zenko.readthedocs.io/en/latest/operation/Architecture/index.html) Every project on Supabase is a dedicated Postgres database, so leveraging it for object and user metadata is more efficient, reducing the number of dependencies for anyone using the Supabase ecosystem of tools.","level":1,"lines":[62,63],"children":[{"type":"text","content":"For example, Minio ","level":0},{"type":"link_open","href":"https://docs.min.io/docs/minio-multi-user-quickstart-guide.html","title":"","level":0},{"type":"text","content":"uses etcd","level":1},{"type":"link_close","level":0},{"type":"text","content":" (when used in multi-user gateway mode) and Zenko ","level":0},{"type":"link_open","href":"https://zenko.readthedocs.io/en/latest/operation/Architecture/index.html","title":"","level":0},{"type":"text","content":"requires mongodb and Kafka.","level":1},{"type":"link_close","level":0},{"type":"text","content":" Every project on Supabase is a dedicated Postgres database, so leveraging it for object and user metadata is more efficient, reducing the number of dependencies for anyone using the Supabase ecosystem of tools.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[64,65],"level":0},{"type":"inline","content":"**Smaller footprint**","level":1,"lines":[64,65],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Smaller footprint","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[66,67],"level":0},{"type":"inline","content":"We are using managed services like [S3](https://aws.amazon.com/s3/), [Wasabi](https://wasabi.com/) and [Backblaze](https://www.backblaze.com/) for our storage backend. We won't be managing our own hard disks and storage capacity, and so we don't require a lot of features offered by existing tools. For example, a large part of Minio's codebase is to offer erasure encoding and bitrot protection, automatically making use of new disks attached to the cluster.","level":1,"lines":[66,67],"children":[{"type":"text","content":"We are using managed services like ","level":0},{"type":"link_open","href":"https://aws.amazon.com/s3/","title":"","level":0},{"type":"text","content":"S3","level":1},{"type":"link_close","level":0},{"type":"text","content":", ","level":0},{"type":"link_open","href":"https://wasabi.com/","title":"","level":0},{"type":"text","content":"Wasabi","level":1},{"type":"link_close","level":0},{"type":"text","content":" and ","level":0},{"type":"link_open","href":"https://www.backblaze.com/","title":"","level":0},{"type":"text","content":"Backblaze","level":1},{"type":"link_close","level":0},{"type":"text","content":" for our storage backend. We won't be managing our own hard disks and storage capacity, and so we don't require a lot of features offered by existing tools. For example, a large part of Minio's codebase is to offer erasure encoding and bitrot protection, automatically making use of new disks attached to the cluster.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[68,69],"level":0},{"type":"inline","content":"**Integration with Supabase Auth**","level":1,"lines":[68,69],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Integration with Supabase Auth","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[70,71],"level":0},{"type":"inline","content":"Existing tools do not play well with our authentication system. For example, Minio is bundled with its own [auth system](https://docs.min.io/docs/minio-admin-complete-guide.html#user) and there is no easy way to map Minio users to [Supabase users](/docs/guides/auth).","level":1,"lines":[70,71],"children":[{"type":"text","content":"Existing tools do not play well with our authentication system. For example, Minio is bundled with its own ","level":0},{"type":"link_open","href":"https://docs.min.io/docs/minio-admin-complete-guide.html#user","title":"","level":0},{"type":"text","content":"auth system","level":1},{"type":"link_close","level":0},{"type":"text","content":" and there is no easy way to map Minio users to ","level":0},{"type":"link_open","href":"/docs/guides/auth","title":"","level":0},{"type":"text","content":"Supabase users","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[72,73],"level":0},{"type":"inline","content":"In the end, we opted to build our own [Storage API server](https://github.com/supabase/storage-api).","level":1,"lines":[72,73],"children":[{"type":"text","content":"In the end, we opted to build our own ","level":0},{"type":"link_open","href":"https://github.com/supabase/storage-api","title":"","level":0},{"type":"text","content":"Storage API server","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[74,75],"level":0},{"type":"inline","content":"[Security](#security)","level":1,"lines":[74,75],"children":[{"type":"text","content":"Security","level":0}],"lvl":2,"i":4,"seen":0,"slug":"security"},{"type":"heading_close","hLevel":2,"level":0},{"type":"heading_open","hLevel":3,"lines":[76,77],"level":0},{"type":"inline","content":"[Authentication](#authentication)","level":1,"lines":[76,77],"children":[{"type":"text","content":"Authentication","level":0}],"lvl":3,"i":5,"seen":0,"slug":"authentication"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[78,79],"level":0},{"type":"inline","content":"Our storage service sits behind [Kong](https://github.com/kong/kong) along with other services like [PostgREST](https://github.com/PostgREST/postgrest) and [Realtime](https://github.com/supabase/realtime). This allows us to reuse our existing Authentication system - any requests with a valid API key are authenticated and passed to the storage API.","level":1,"lines":[78,79],"children":[{"type":"text","content":"Our storage service sits behind ","level":0},{"type":"link_open","href":"https://github.com/kong/kong","title":"","level":0},{"type":"text","content":"Kong","level":1},{"type":"link_close","level":0},{"type":"text","content":" along with other services like ","level":0},{"type":"link_open","href":"https://github.com/PostgREST/postgrest","title":"","level":0},{"type":"text","content":"PostgREST","level":1},{"type":"link_close","level":0},{"type":"text","content":" and ","level":0},{"type":"link_open","href":"https://github.com/supabase/realtime","title":"","level":0},{"type":"text","content":"Realtime","level":1},{"type":"link_close","level":0},{"type":"text","content":". This allows us to reuse our existing Authentication system - any requests with a valid API key are authenticated and passed to the storage API.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[80,81],"level":0},{"type":"inline","content":"[Authorization](#authorization)","level":1,"lines":[80,81],"children":[{"type":"text","content":"Authorization","level":0}],"lvl":3,"i":6,"seen":0,"slug":"authorization"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[82,83],"level":0},{"type":"inline","content":"For Authorization, we wanted to avoid creating a new DSL like Firebase. Instead, we created one where you can write policies in the One True Language - SQL!","level":1,"lines":[82,83],"children":[{"type":"text","content":"For Authorization, we wanted to avoid creating a new DSL like Firebase. Instead, we created one where you can write policies in the One True Language - SQL!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[84,85],"level":0},{"type":"inline","content":"![One True Language](/images/blog/storage/true-language.png)","level":1,"lines":[84,85],"children":[{"type":"image","src":"/images/blog/storage/true-language.png","title":"","alt":"One True Language","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[86,87],"level":0},{"type":"inline","content":"To do this, we leverage Postgres' Row Level Security. We create a table for `buckets` and `objects` inside each Supabase project. These tables are namespaced in a separate schema called `storage`.","level":1,"lines":[86,87],"children":[{"type":"text","content":"To do this, we leverage Postgres' Row Level Security. We create a table for ","level":0},{"type":"code","content":"buckets","block":false,"level":0},{"type":"text","content":" and ","level":0},{"type":"code","content":"objects","block":false,"level":0},{"type":"text","content":" inside each Supabase project. These tables are namespaced in a separate schema called ","level":0},{"type":"code","content":"storage","block":false,"level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[88,89],"level":0},{"type":"inline","content":"The idea is simple - if a user is able to `select` from the `objects` table, they can retrieve the object too. Using Postgres' Row Level Security, you can define fine-grained Policies to determine access levels for different users.","level":1,"lines":[88,89],"children":[{"type":"text","content":"The idea is simple - if a user is able to ","level":0},{"type":"code","content":"select","block":false,"level":0},{"type":"text","content":" from the ","level":0},{"type":"code","content":"objects","block":false,"level":0},{"type":"text","content":" table, they can retrieve the object too. Using Postgres' Row Level Security, you can define fine-grained Policies to determine access levels for different users.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[90,91],"level":0},{"type":"inline","content":"When a user makes a request for a file, the API detects the user in the `Authorization` header and tries to `select` from the `objects` table. If a valid row is returned, the Storage API pipes the object back from S3. Similarly if the user is able to delete the row from the objects table, they can delete the object from the storage backend too.","level":1,"lines":[90,91],"children":[{"type":"text","content":"When a user makes a request for a file, the API detects the user in the ","level":0},{"type":"code","content":"Authorization","block":false,"level":0},{"type":"text","content":" header and tries to ","level":0},{"type":"code","content":"select","block":false,"level":0},{"type":"text","content":" from the ","level":0},{"type":"code","content":"objects","block":false,"level":0},{"type":"text","content":" table. If a valid row is returned, the Storage API pipes the object back from S3. Similarly if the user is able to delete the row from the objects table, they can delete the object from the storage backend too.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[92,93],"level":0},{"type":"inline","content":"For example, if you want to give \"read\" access to a bucket called `avatars` you would use this policy:","level":1,"lines":[92,93],"children":[{"type":"text","content":"For example, if you want to give \"read\" access to a bucket called ","level":0},{"type":"code","content":"avatars","block":false,"level":0},{"type":"text","content":" you would use this policy:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql","content":"create policy \"Read access for avatars.\"\non storage.objects for select using (\n    bucket_id = 'avatars'\n);\n","lines":[94,100],"level":0},{"type":"paragraph_open","tight":false,"lines":[101,102],"level":0},{"type":"inline","content":"Extending this example, if you want to give access to a subfolder only (in this case called `public`):","level":1,"lines":[101,102],"children":[{"type":"text","content":"Extending this example, if you want to give access to a subfolder only (in this case called ","level":0},{"type":"code","content":"public","block":false,"level":0},{"type":"text","content":"):","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql","content":"create policy \"Read access for public avatars.\"\non storage.objects for select using (\n    bucket_id = 'avatars'\n    and (storage.foldername(name))[1] = 'public'\n);\n","lines":[103,110],"level":0},{"type":"paragraph_open","tight":false,"lines":[111,112],"level":0},{"type":"inline","content":"We have created helper functions like `foldername()`, `filename()` and `extension()` in the storage schema to make Policies even simpler.","level":1,"lines":[111,112],"children":[{"type":"text","content":"We have created helper functions like ","level":0},{"type":"code","content":"foldername()","block":false,"level":0},{"type":"text","content":", ","level":0},{"type":"code","content":"filename()","block":false,"level":0},{"type":"text","content":" and ","level":0},{"type":"code","content":"extension()","block":false,"level":0},{"type":"text","content":" in the storage schema to make Policies even simpler.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[113,114],"level":0},{"type":"inline","content":"This system integrates with our [User Management system](/docs/guides/auth). Here is an example policy which gives access to a particular file to a Supabase user:","level":1,"lines":[113,114],"children":[{"type":"text","content":"This system integrates with our ","level":0},{"type":"link_open","href":"/docs/guides/auth","title":"","level":0},{"type":"text","content":"User Management system","level":1},{"type":"link_close","level":0},{"type":"text","content":". Here is an example policy which gives access to a particular file to a Supabase user:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql","content":"create policy crud_uid_file\non storage.objects for all using (\n    bucket_id = 'avatars'\n    and name = 'folder/only_uid.jpg'\n    and (select auth.uid()) = 'd8c7bce9-cfeb-497b-bd61-e66ce2cbdaa2'\n);\n","lines":[115,123],"level":0},{"type":"paragraph_open","tight":false,"lines":[124,125],"level":0},{"type":"inline","content":"Using the power of Postgres' Row Level Security, you can create pretty much any policy imaginable.","level":1,"lines":[124,125],"children":[{"type":"text","content":"Using the power of Postgres' Row Level Security, you can create pretty much any policy imaginable.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[126,127],"level":0},{"type":"inline","content":"[Performance](#performance)","level":1,"lines":[126,127],"children":[{"type":"text","content":"Performance","level":0}],"lvl":3,"i":7,"seen":0,"slug":"performance"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[128,129],"level":0},{"type":"inline","content":"The storage server is built with [Fastify](https://www.fastify.io/) and Typescript. Fastify is one of the [fastest](https://www.fastify.io/benchmarks/) Node frameworks and our initial benchmark results look very promising.","level":1,"lines":[128,129],"children":[{"type":"text","content":"The storage server is built with ","level":0},{"type":"link_open","href":"https://www.fastify.io/","title":"","level":0},{"type":"text","content":"Fastify","level":1},{"type":"link_close","level":0},{"type":"text","content":" and Typescript. Fastify is one of the ","level":0},{"type":"link_open","href":"https://www.fastify.io/benchmarks/","title":"","level":0},{"type":"text","content":"fastest","level":1},{"type":"link_close","level":0},{"type":"text","content":" Node frameworks and our initial benchmark results look very promising.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[130,131],"level":0},{"type":"inline","content":"We use Node streams everywhere. Objects are uploaded directly to S3 with minimal in-memory buffering. This minimizes RAM consumption even while uploading huge objects. The code does become a bit more complicated when using streams. For example, you can't figure out the size of the object being uploaded before streaming it to S3. But we believe this tradeoff is worth it.","level":1,"lines":[130,131],"children":[{"type":"text","content":"We use Node streams everywhere. Objects are uploaded directly to S3 with minimal in-memory buffering. This minimizes RAM consumption even while uploading huge objects. The code does become a bit more complicated when using streams. For example, you can't figure out the size of the object being uploaded before streaming it to S3. But we believe this tradeoff is worth it.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[132,133],"level":0},{"type":"inline","content":"Postgres is another hidden gem for our storage performance. For example, what if you had a bucket with thousands of objects, and you needed to find all objects which a user has access to? Without Postgres, you would retrieve all objects from that folder, evaluate each policy in the storage middleware and only return the objects which the user has access to. But we can avoid this completely with Postgres! We use Postgres to evaluate all the polices, and the storage middleware just returns the objects which the user has access to.","level":1,"lines":[132,133],"children":[{"type":"text","content":"Postgres is another hidden gem for our storage performance. For example, what if you had a bucket with thousands of objects, and you needed to find all objects which a user has access to? Without Postgres, you would retrieve all objects from that folder, evaluate each policy in the storage middleware and only return the objects which the user has access to. But we can avoid this completely with Postgres! We use Postgres to evaluate all the polices, and the storage middleware just returns the objects which the user has access to.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[134,135],"level":0},{"type":"inline","content":"Supabase Storage has some opinionated defaults with respect to caching. Objects retrieved from S3 typically do not have a `Cache-Control` header. This isn't optimal for a Storage system since browsers don't cache objects completely without this header. Objects retrieved from Supabase Storage by default have a Cache-Control header of 1 hour. Of course, you can override this behaviour by specifying a different cache time when creating the object.","level":1,"lines":[134,135],"children":[{"type":"text","content":"Supabase Storage has some opinionated defaults with respect to caching. Objects retrieved from S3 typically do not have a ","level":0},{"type":"code","content":"Cache-Control","block":false,"level":0},{"type":"text","content":" header. This isn't optimal for a Storage system since browsers don't cache objects completely without this header. Objects retrieved from Supabase Storage by default have a Cache-Control header of 1 hour. Of course, you can override this behaviour by specifying a different cache time when creating the object.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[136,137],"level":0},{"type":"inline","content":"[Storage Backend](#storage-backend)","level":1,"lines":[136,137],"children":[{"type":"text","content":"Storage Backend","level":0}],"lvl":2,"i":8,"seen":0,"slug":"storage-backend"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[138,139],"level":0},{"type":"inline","content":"File objects are stored in an S3 bucket within the same region as your Supabase project. S3 has a high durability of 99.999999999% and is scalable as an object store.","level":1,"lines":[138,139],"children":[{"type":"text","content":"File objects are stored in an S3 bucket within the same region as your Supabase project. S3 has a high durability of 99.999999999% and is scalable as an object store.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[140,141],"level":0},{"type":"inline","content":"While we started with S3, other storage backends like Wasabi, Backblaze and Openstack Swift expose an S3 compatible API. It will be simple to add more S3 compatible storage options in the future.","level":1,"lines":[140,141],"children":[{"type":"text","content":"While we started with S3, other storage backends like Wasabi, Backblaze and Openstack Swift expose an S3 compatible API. It will be simple to add more S3 compatible storage options in the future.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[142,143],"level":0},{"type":"inline","content":"We have intentionally kept the API surface for the storage backend very small, in case the community also wants to add storage options which don't support the S3 API.","level":1,"lines":[142,143],"children":[{"type":"text","content":"We have intentionally kept the API surface for the storage backend very small, in case the community also wants to add storage options which don't support the S3 API.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[144,145],"level":0},{"type":"inline","content":"[Client libraries](#client-libraries)","level":1,"lines":[144,145],"children":[{"type":"text","content":"Client libraries","level":0}],"lvl":3,"i":9,"seen":0,"slug":"client-libraries"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[146,147],"level":0},{"type":"inline","content":"You may be itching to get started tinkering around with our new Supabase storage - we have an example app prepared for you which simulates a simple context of setting an avatar image for a user. This will help you to get a high level overview of how to use your project's storage with our client library. For greater detail, refer to our storage API documentation [here](/docs/reference/javascript/storage-createbucket).","level":1,"lines":[146,147],"children":[{"type":"text","content":"You may be itching to get started tinkering around with our new Supabase storage - we have an example app prepared for you which simulates a simple context of setting an avatar image for a user. This will help you to get a high level overview of how to use your project's storage with our client library. For greater detail, refer to our storage API documentation ","level":0},{"type":"link_open","href":"/docs/reference/javascript/storage-createbucket","title":"","level":0},{"type":"text","content":"here","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[148,149],"level":0},{"type":"inline","content":"![Example app 1](/images/blog/storage/ph-7.png)","level":1,"lines":[148,149],"children":[{"type":"image","src":"/images/blog/storage/ph-7.png","title":"","alt":"Example app 1","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[150,151],"level":0},{"type":"inline","content":"![Example app 2](/images/blog/storage/ph-6.png)","level":1,"lines":[150,151],"children":[{"type":"image","src":"/images/blog/storage/ph-6.png","title":"","alt":"Example app 2","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[152,153],"level":0},{"type":"inline","content":"[What's next](#whats-next)","level":1,"lines":[152,153],"children":[{"type":"text","content":"What's next","level":0}],"lvl":2,"i":10,"seen":0,"slug":"whats-next"},{"type":"heading_close","hLevel":2,"level":0},{"type":"bullet_list_open","lines":[154,160],"level":0},{"type":"list_item_open","lines":[154,155],"level":1},{"type":"paragraph_open","tight":true,"lines":[154,155],"level":2},{"type":"inline","content":"We currently support S3 and will be adding more storage backends. Vote for the storage backends you would like to see us implement [here](https://github.com/supabase/supabase/discussions/982).","level":3,"lines":[154,155],"children":[{"type":"text","content":"We currently support S3 and will be adding more storage backends. Vote for the storage backends you would like to see us implement ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase/discussions/982","title":"","level":0},{"type":"text","content":"here","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[155,156],"level":1},{"type":"paragraph_open","tight":true,"lines":[155,156],"level":2},{"type":"inline","content":"We will integrate our storage service with a Content Delivery Network. With a CDN, objects are cached on a global network. This leads to faster access times for your users around the world. \\*\\*SHIPPED: [Supabase Storage v2: Image resizing and Smart CDN](https://supabase.com/blog/storage-image-resizing-smart-cdn#smart-cdn)","level":3,"lines":[155,156],"children":[{"type":"text","content":"We will integrate our storage service with a Content Delivery Network. With a CDN, objects are cached on a global network. This leads to faster access times for your users around the world. **SHIPPED: ","level":0},{"type":"link_open","href":"https://supabase.com/blog/storage-image-resizing-smart-cdn#smart-cdn","title":"","level":0},{"type":"text","content":"Supabase Storage v2: Image resizing and Smart CDN","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[156,157],"level":1},{"type":"paragraph_open","tight":true,"lines":[156,157],"level":2},{"type":"inline","content":"We are working on transformations like resizing of images and automatic optimization of different media types. This makes it easy to embed Supabase objects directly in your websites and mobile applications without additional processing. \\*\\*SHIPPED: [Supabase Storage v2: Image resizing and Smart CDN](https://supabase.com/blog/storage-image-resizing-smart-cdn#image-resizing)","level":3,"lines":[156,157],"children":[{"type":"text","content":"We are working on transformations like resizing of images and automatic optimization of different media types. This makes it easy to embed Supabase objects directly in your websites and mobile applications without additional processing. **SHIPPED: ","level":0},{"type":"link_open","href":"https://supabase.com/blog/storage-image-resizing-smart-cdn#image-resizing","title":"","level":0},{"type":"text","content":"Supabase Storage v2: Image resizing and Smart CDN","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[157,158],"level":1},{"type":"paragraph_open","tight":true,"lines":[157,158],"level":2},{"type":"inline","content":"A better editor to make authoring policies easier and less error prone.","level":3,"lines":[157,158],"children":[{"type":"text","content":"A better editor to make authoring policies easier and less error prone.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[158,160],"level":1},{"type":"paragraph_open","tight":true,"lines":[158,159],"level":2},{"type":"inline","content":"Reach out to us if you would like to help out with adding storage support in one of the [community maintained client libraries](/docs/reference/javascript/installing).","level":3,"lines":[158,159],"children":[{"type":"text","content":"Reach out to us if you would like to help out with adding storage support in one of the ","level":0},{"type":"link_open","href":"/docs/reference/javascript/installing","title":"","level":0},{"type":"text","content":"community maintained client libraries","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[160,161],"level":0},{"type":"inline","content":"Take it for a spin on our [dashboard](https://supabase.com/dashboard/) and let us know what you think!","level":1,"lines":[160,161],"children":[{"type":"text","content":"Take it for a spin on our ","level":0},{"type":"link_open","href":"https://supabase.com/dashboard/","title":"","level":0},{"type":"text","content":"dashboard","level":1},{"type":"link_close","level":0},{"type":"text","content":" and let us know what you think!","level":0}]},{"type":"paragraph_close","tight":false,"level":0}],"content":"- [Architecture](#architecture)\n- [Frontend](#frontend)\n- [Designing the storage middleware](#designing-the-storage-middleware)\n- [Security](#security)\n- [Storage Backend](#storage-backend)\n- [What's next](#whats-next)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-storage"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>