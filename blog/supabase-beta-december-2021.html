<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase Beta December 2021</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="New crypto extension, Postgres videos, and a bunch of cool integrations." data-next-head=""/><meta property="og:title" content="Supabase Beta December 2021" data-next-head=""/><meta property="og:description" content="New crypto extension, Postgres videos, and a bunch of cool integrations." data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-beta-december-2021" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2022-01-20" data-next-head=""/><meta property="article:author" content="https://github.com/kiwicopple" data-next-head=""/><meta property="article:tag" content="release-notes" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/2021-dec/release-dec-2021.png" data-next-head=""/><meta property="og:image:alt" content="Supabase Beta December 2021 thumbnail" data-next-head=""/><meta property="og:video" content="https://www.youtube.com/v/I6nnp9AINJk" data-next-head=""/><meta property="og:video:type" content="application/x-shockwave-flash" data-next-head=""/><meta property="og:video:width" content="640" data-next-head=""/><meta property="og:video:height" content="385" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Supabase Beta December 2021</h1><div class="text-light flex space-x-3 text-sm"><p>20 Jan 2022</p><p>•</p><p>4 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/kiwicopple"><div class="flex items-center gap-3"><div class="w-10"><img alt="Paul Copplestone avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fkiwicopple.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fkiwicopple.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fkiwicopple.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Paul Copplestone</span><span class="text-foreground-lighter mb-0 text-xs">CEO and Co-Founder</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Supabase Beta December 2021" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2021-dec%2Frelease-dec-2021-cover.png&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2021-dec%2Frelease-dec-2021-cover.png&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2021-dec%2Frelease-dec-2021-cover.png&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2021-dec%2Frelease-dec-2021-cover.png&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2021-dec%2Frelease-dec-2021-cover.png&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2021-dec%2Frelease-dec-2021-cover.png&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2021-dec%2Frelease-dec-2021-cover.png&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2021-dec%2Frelease-dec-2021-cover.png&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2021-dec%2Frelease-dec-2021-cover.png&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2021-dec%2Frelease-dec-2021-cover.png&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2021-dec%2Frelease-dec-2021-cover.png&amp;w=3840&amp;q=100"/></div><p>Happy 2022. We&#x27;re looking forward to a year of shipping product and helping our users build great things!</p>
<p>Here&#x27;s a bunch of stuff we shipped throughout December...</p>
<h2 id="hackathon-winners" class="group scroll-mt-24">Hackathon Winners<a href="#hackathon-winners" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We had a ton of awesome projects <a href="holiday-hackdays-winners-2021.html">submitted for our Holiday Hackdays</a>, all of the submissions are open source so come and check out the code for some of our top picks!</p>
<p><a href="holiday-hackdays-winners-2021.html"></a></p>
<h2 id="new-crypto-extension---pg_crypto" class="group scroll-mt-24">New crypto extension - pg_crypto<a href="#new-crypto-extension---pg_crypto" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We just added pg_sodium as an extension inside all postgres instances.
Which means you can easily do encryption, decryption, hashing, and cryptographic signing from within your postgres functions and queries.
Enable it from the Database &gt; Extensions tab in the <a href="../dashboard/org.html">Supabase Dashboard</a>.</p>
<p><a href="../dashboard/org.html"></a></p>
<h2 id="postgresql-videos" class="group scroll-mt-24">PostgreSQL Videos<a href="#postgresql-videos" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<h3 id="call-postgres-functions-from-javascript-with-rpc" class="group scroll-mt-24">Call Postgres functions from JavaScript with RPC<a href="#call-postgres-functions-from-javascript-with-rpc" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Jon Meyers walks you through how to write more complex queries and logic in your database, and invoke it via the API.</p>
<div class="video-container"><iframe class="video-with-border w-full" src="https://www.youtube-nocookie.com/embed/I6nnp9AINJk" frameBorder="1" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen=""></iframe></div>
<h3 id="call-any-api-using-postgresql-functions" class="group scroll-mt-24">Call any API using PostgreSQL functions<a href="#call-any-api-using-postgresql-functions" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We recently added the ability to call any external HTTP endpoint from your postgres functions. Jon shows you how!</p>
<div class="video-container"><iframe class="video-with-border w-full" src="https://www.youtube-nocookie.com/embed/rARgrELRCwY" frameBorder="1" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen=""></iframe></div>
<h3 id="create-postgresql-functions-with-supabase" class="group scroll-mt-24">Create PostgreSQL Functions with Supabase<a href="#create-postgresql-functions-with-supabase" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Triggers allow you to execute SQL in response to any table changes. Learn how to write PostgreSQL triggers with Jon!</p>
<div class="video-container"><iframe class="video-with-border w-full" src="https://www.youtube-nocookie.com/embed/MJZCCpCYEqk" frameBorder="1" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen=""></iframe></div>
<h3 id="hiring" class="group scroll-mt-24">Hiring<a href="#hiring" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We&#x27;re Hiring - for a ton of new roles across engineering, marketing, and HR. We hire fully remotely, and you&#x27;ll get to work alongside some incredible people, on one of the fastest growing open source companies. <a href="https://about.supabase.com/careers">See which roles we&#x27;re hiring for</a>.</p>
<p><a href="https://about.supabase.com/careers"></a></p>
<h2 id="community" class="group scroll-mt-24">Community<a href="#community" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>There was a lot of activity this month.</p>
<h3 id="learn-with-jason" class="group scroll-mt-24">Learn With Jason<a href="#learn-with-jason" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Let Jason and Jon show you how to build an app with Next.js and Supabase on top of Netlify!</p>
<div class="video-container"><iframe class="video-with-border w-full" src="https://www.youtube-nocookie.com/embed/8vqY1KT4TLU" frameBorder="1" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen=""></iframe></div>
<h3 id="divjoy-integration" class="group scroll-mt-24">Divjoy integration<a href="#divjoy-integration" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Divjoy announced their <a href="https://divjoy.com/?database=supabase">Supabase Integration</a> - The React codebase generator allows you to choose your stack, choose your template, and generate your codebase.</p>
<p></p>
<h3 id="n8n--supabase" class="group scroll-mt-24">n8n + Supabase<a href="#n8n--supabase" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p><a href="https://n8n.io/">n8n</a> added a Supabase node.</p>
<blockquote class="twitter-tweet" data-theme="dark"><p lang="en" dir="ltr"><p>We released n8n@0.158.0 with new nodes for
<a href="https://twitter.com/supabase?ref_src=twsrc%5Etfw">@supabase</a>,<a href="https://twitter.com/syncromsp?ref_src=twsrc%5Etfw">
@syncromsp
</a>, and
<a href="https://twitter.com/Microsoft?ref_src=twsrc%5Etfw">@Microsoft</a> Graph Security. We
also made improvements to existing nodes ✨
<a href="https://t.co/IdeUe4eWBK">pic.twitter.com/IdeUe4eWBK</a></p></p><p>— n8n.io (@n8n_io) <a href="https://twitter.com/n8n_io/status/1480502781320572931?ref_src=twsrc%5Etfw">January 10, 2022</a></p></blockquote>
<script async="" src="https://platform.twitter.com/widgets.js" charset="utf-8"></script>
<h3 id="python-upgrades" class="group scroll-mt-24">Python Upgrades<a href="#python-upgrades" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>The Community released a whole bunch of updated Python libs including: supabase v0.0.4 - gotrue v0.3.0 - realtime v0.0.4 - storage3 v0.1.0</p>
<p>See the full list of <a href="https://github.com/supabase-community/">community supported libs</a>.</p>
<p><a href="https://github.com/supabase-community/"></a></p>
<h3 id="twitter" class="group scroll-mt-24">Twitter<a href="#twitter" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We&#x27;re having a 24/7*365 meme-fest on <a href="https://twitter.com/supabase">Twitter</a></p>
<blockquote class="twitter-tweet" data-theme="dark"><p lang="en" dir="ltr"><p>invest in good relationships <a href="https://t.co/bGiBE7FRFQ">pic.twitter.com/bGiBE7FRFQ</a></p></p><p>— Supabase (@supabase) <a href="https://twitter.com/supabase/status/1475876907212316678?ref_src=twsrc%5Etfw">December 28, 2021</a></p></blockquote>
<script async="" src="https://platform.twitter.com/widgets.js" charset="utf-8"></script>
<h3 id="tiktok" class="group scroll-mt-24">TikTok<a href="#tiktok" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Check us out on <a href="https://www.tiktok.com/@supabase.com?fromUrl=%2Fsupabase.com&amp;lang=en">TikTok</a></p>
<p><a href="https://www.tiktok.com/@supabase.com?fromUrl=%2Fsupabase.com&amp;lang=en"></a></p>
<h3 id="swag-store" class="group scroll-mt-24">Swag Store<a href="#swag-store" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>A reminder that we have a <a href="https://supabase.store">Supabase Swag Store</a></p>
<p><a href="https://supabase.store"></a></p>
<h3 id="github" class="group scroll-mt-24">GitHub<a href="#github" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We hit 26K stars!!: <a href="http://github.com/supabase/supabase">github.com/supabase/supabase</a></p>
<p></p>
<p>Source: <a href="https://repository.surf/supabase">repository.surf/supabase</a></p>
<p>Check out some of our other community stats in our latest <a href="https://supabase.com/blog/supabase-series-a">Series A Blog Post</a>.</p>
<h2 id="get-started" class="group scroll-mt-24">Get started<a href="#get-started" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ul>
<li>Start using Supabase today: <strong><a href="https://supabase.com/dashboard/">supabase.com/dashboard</a></strong></li>
<li>Make sure to <strong><a href="https://github.com/supabase/supabase">star us on GitHub</a></strong></li>
<li>Follow us <strong><a href="https://twitter.com/supabase">on Twitter</a></strong></li>
<li>Subscribe to our <strong><a href="https://www.youtube.com/c/supabase">YouTube channel</a></strong></li>
<li>Become a <strong><a href="https://github.com/sponsors/supabase">sponsor</a></strong></li>
</ul></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-december-2021&amp;text=Supabase%20Beta%20December%202021"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-december-2021&amp;text=Supabase%20Beta%20December%202021"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-december-2021&amp;t=Supabase%20Beta%20December%202021"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="product-hunt-golden-kitty-awards-2021.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Golden Kitty Awards Ceremony Watch Party with Supabase</h4><p class="small">20 January 2022</p></div></div></div></div></a></div><div><a href="holiday-hackdays-winners-2021.html"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Holiday Hackdays Winners 2021</h4><p class="small">17 December 2021</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/release-notes"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">release-notes</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#hackathon-winners">Hackathon Winners</a></li>
<li><a href="#new-crypto-extension---pg_crypto">New crypto extension - pg_crypto</a></li>
<li><a href="#postgresql-videos">PostgreSQL Videos</a>
<ul>
<li><a href="#call-postgres-functions-from-javascript-with-rpc">Call Postgres functions from JavaScript with RPC</a></li>
<li><a href="#call-any-api-using-postgresql-functions">Call any API using PostgreSQL functions</a></li>
<li><a href="#create-postgresql-functions-with-supabase">Create PostgreSQL Functions with Supabase</a></li>
<li><a href="#hiring">Hiring</a></li>
</ul>
</li>
<li><a href="#community">Community</a>
<ul>
<li><a href="#learn-with-jason">Learn With Jason</a></li>
<li><a href="#divjoy-integration">Divjoy integration</a></li>
<li><a href="#n8n--supabase">n8n + Supabase</a></li>
<li><a href="#python-upgrades">Python Upgrades</a></li>
<li><a href="#twitter">Twitter</a></li>
<li><a href="#tiktok">TikTok</a></li>
<li><a href="#swag-store">Swag Store</a></li>
<li><a href="#github">GitHub</a></li>
</ul>
</li>
<li><a href="#get-started">Get started</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-december-2021&amp;text=Supabase%20Beta%20December%202021"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-december-2021&amp;text=Supabase%20Beta%20December%202021"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-december-2021&amp;t=Supabase%20Beta%20December%202021"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"product-hunt-golden-kitty-awards-2021","title":"Golden Kitty Awards Ceremony Watch Party with Supabase","description":"Hang out with us while watching the Product Hunt Golden Kitty Awards Ceremony","author":"thor_schaeff","author_url":"https://github.com/thorwebdev","author_image_url":"https://github.com/thorwebdev.png","image":"product-hunt-golden-kitty-awards-2021.png","thumb":"product-hunt-golden-kitty-awards-2021-thumb.png","categories":["developers"],"tags":["community"],"date":"2022-01-20","toc_depth":2,"formattedDate":"20 January 2022","readingTime":"2 minute read","url":"/blog/product-hunt-golden-kitty-awards-2021","path":"/blog/product-hunt-golden-kitty-awards-2021"},"nextPost":{"slug":"holiday-hackdays-winners-2021","title":"Holiday Hackdays Winners 2021","description":"Celebrating many amazing projects submitted to our Holiday Hackdays Hackathon.","author":"thor_schaeff","author_url":"https://github.com/thorwebdev","author_image_url":"https://github.com/thorwebdev.png","image":"hackathon-winners/holiday-hackdays/holiday-hackdays-winners-og.png","thumb":"hackathon-winners/holiday-hackdays/holiday-hackdays-winners-og.png","categories":["developers"],"tags":["community","hackathon"],"date":"2021-12-17","toc_depth":2,"formattedDate":"17 December 2021","readingTime":"4 minute read","url":"/blog/holiday-hackdays-winners-2021","path":"/blog/holiday-hackdays-winners-2021"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-beta-december-2021","source":"\nHappy 2022. We're looking forward to a year of shipping product and helping our users build great things!\n\nHere's a bunch of stuff we shipped throughout December...\n\n## Hackathon Winners\n\nWe had a ton of awesome projects [submitted for our Holiday Hackdays](/blog/holiday-hackdays-winners-2021), all of the submissions are open source so come and check out the code for some of our top picks!\n\n[![hackathon winners](/images/blog/2021-dec/hackathon.png)](/blog/holiday-hackdays-winners-2021)\n\n## New crypto extension - pg_crypto\n\nWe just added pg_sodium as an extension inside all postgres instances.\nWhich means you can easily do encryption, decryption, hashing, and cryptographic signing from within your postgres functions and queries.\nEnable it from the Database \u003e Extensions tab in the [Supabase Dashboard](https://supabase.com/dashboard).\n\n[![pg sodium support](/images/blog/2021-dec/pgsodium.png)](https://supabase.com/dashboard)\n\n## PostgreSQL Videos\n\n### Call Postgres functions from JavaScript with RPC\n\nJon Meyers walks you through how to write more complex queries and logic in your database, and invoke it via the API.\n\n\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"video-with-border w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/I6nnp9AINJk\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen\n  \u003e\u003c/iframe\u003e\n\u003c/div\u003e\n\n### Call any API using PostgreSQL functions\n\nWe recently added the ability to call any external HTTP endpoint from your postgres functions. Jon shows you how!\n\n\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"video-with-border w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/rARgrELRCwY\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen\n  \u003e\u003c/iframe\u003e\n\u003c/div\u003e\n\n### Create PostgreSQL Functions with Supabase\n\nTriggers allow you to execute SQL in response to any table changes. Learn how to write PostgreSQL triggers with Jon!\n\n\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"video-with-border w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/MJZCCpCYEqk\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen\n  \u003e\u003c/iframe\u003e\n\u003c/div\u003e\n\n### Hiring\n\nWe're Hiring - for a ton of new roles across engineering, marketing, and HR. We hire fully remotely, and you'll get to work alongside some incredible people, on one of the fastest growing open source companies. [See which roles we're hiring for](https://about.supabase.com/careers).\n\n[![supabase is hiring](/images/blog/2021-dec/hiring.png)](https://about.supabase.com/careers)\n\n## Community\n\nThere was a lot of activity this month.\n\n### Learn With Jason\n\nLet Jason and Jon show you how to build an app with Next.js and Supabase on top of Netlify!\n\n\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"video-with-border w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/8vqY1KT4TLU\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen\n  \u003e\u003c/iframe\u003e\n\u003c/div\u003e\n\n### Divjoy integration\n\nDivjoy announced their [Supabase Integration](https://divjoy.com/?database=supabase) - The React codebase generator allows you to choose your stack, choose your template, and generate your codebase.\n\n![pg sodium support](/images/blog/2021-dec/divjoy.png)\n\n### n8n + Supabase\n\n[n8n](https://n8n.io/) added a Supabase node.\n\n\u003cblockquote class=\"twitter-tweet\" data-theme=\"dark\"\u003e\n  \u003cp lang=\"en\" dir=\"ltr\"\u003e\n    We released n8n@0.158.0 with new nodes for\n    \u003ca href=\"https://twitter.com/supabase?ref_src=twsrc%5Etfw\"\u003e@supabase\u003c/a\u003e,\u003ca href=\"https://twitter.com/syncromsp?ref_src=twsrc%5Etfw\"\u003e\n      @syncromsp\n    \u003c/a\u003e, and\n    \u003ca href=\"https://twitter.com/Microsoft?ref_src=twsrc%5Etfw\"\u003e@Microsoft\u003c/a\u003e Graph Security. We\n    also made improvements to existing nodes ✨\n    \u003ca href=\"https://t.co/IdeUe4eWBK\"\u003epic.twitter.com/IdeUe4eWBK\u003c/a\u003e\n  \u003c/p\u003e\n  \u0026mdash; n8n.io (@n8n_io) \u003ca href=\"https://twitter.com/n8n_io/status/1480502781320572931?ref_src=twsrc%5Etfw\"\u003eJanuary 10, 2022\u003c/a\u003e\n\u003c/blockquote\u003e \u003cscript async src=\"https://platform.twitter.com/widgets.js\" charset=\"utf-8\"\u003e\u003c/script\u003e\n\n### Python Upgrades\n\nThe Community released a whole bunch of updated Python libs including: supabase v0.0.4 - gotrue v0.3.0 - realtime v0.0.4 - storage3 v0.1.0\n\nSee the full list of [community supported libs](https://github.com/supabase-community/).\n\n[![supabase community libs](/images/blog/2021-dec/python.png)](https://github.com/supabase-community/)\n\n### Twitter\n\nWe're having a 24/7\\*365 meme-fest on [Twitter](https://twitter.com/supabase)\n\n\u003cblockquote class=\"twitter-tweet\" data-theme=\"dark\"\u003e\n  \u003cp lang=\"en\" dir=\"ltr\"\u003e\n    invest in good relationships \u003ca href=\"https://t.co/bGiBE7FRFQ\"\u003epic.twitter.com/bGiBE7FRFQ\u003c/a\u003e\n  \u003c/p\u003e\n  \u0026mdash; Supabase (@supabase) \u003ca href=\"https://twitter.com/supabase/status/1475876907212316678?ref_src=twsrc%5Etfw\"\u003eDecember 28, 2021\u003c/a\u003e\n\u003c/blockquote\u003e \u003cscript async src=\"https://platform.twitter.com/widgets.js\" charset=\"utf-8\"\u003e\u003c/script\u003e\n\n### TikTok\n\nCheck us out on [TikTok](https://www.tiktok.com/@supabase.com?fromUrl=%2Fsupabase.com\u0026lang=en)\n\n[![tiktok](/images/blog/2021-dec/tiktok.png)](https://www.tiktok.com/@supabase.com?fromUrl=%2Fsupabase.com\u0026lang=en)\n\n### Swag Store\n\nA reminder that we have a [Supabase Swag Store](https://supabase.store)\n\n[![swag store](/images/blog/2021-dec/swag.jpg)](https://supabase.store)\n\n### GitHub\n\nWe hit 26K stars!!: [github.com/supabase/supabase](http://github.com/supabase/supabase)\n\n![GitHub](/images/blog/2021-dec/stars.png)\n\nSource: [repository.surf/supabase](https://repository.surf/supabase)\n\nCheck out some of our other community stats in our latest [Series A Blog Post](/blog/supabase-series-a).\n\n## Get started\n\n- Start using Supabase today: **[supabase.com/dashboard](https://supabase.com/dashboard/)**\n- Make sure to **[star us on GitHub](https://github.com/supabase/supabase)**\n- Follow us **[on Twitter](https://twitter.com/supabase)**\n- Subscribe to our **[YouTube channel](https://www.youtube.com/c/supabase)**\n- Become a **[sponsor](https://github.com/sponsors/supabase)**\n","title":"Supabase Beta December 2021","description":"New crypto extension, Postgres videos, and a bunch of cool integrations.","author":"paul_copplestone","author_url":"https://github.com/kiwicopple","author_image_url":"https://github.com/kiwicopple.png","image":"2021-dec/release-dec-2021.png","thumb":"2021-dec/release-dec-2021-cover.png","categories":["product"],"tags":["release-notes"],"date":"2022-01-20","toc_depth":3,"video":"https://www.youtube.com/v/I6nnp9AINJk","content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    h2: \"h2\",\n    a: \"a\",\n    img: \"img\",\n    h3: \"h3\",\n    ul: \"ul\",\n    li: \"li\",\n    strong: \"strong\"\n  }, _provideComponents(), props.components);\n  return _jsxs(_Fragment, {\n    children: [_jsx(_components.p, {\n      children: \"Happy 2022. We're looking forward to a year of shipping product and helping our users build great things!\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Here's a bunch of stuff we shipped throughout December...\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"hackathon-winners\",\n      children: \"Hackathon Winners\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We had a ton of awesome projects \", _jsx(_components.a, {\n        href: \"/blog/holiday-hackdays-winners-2021\",\n        children: \"submitted for our Holiday Hackdays\"\n      }), \", all of the submissions are open source so come and check out the code for some of our top picks!\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"/blog/holiday-hackdays-winners-2021\",\n        children: _jsx(_components.img, {\n          src: \"/images/blog/2021-dec/hackathon.png\",\n          alt: \"hackathon winners\"\n        })\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"new-crypto-extension---pg_crypto\",\n      children: \"New crypto extension - pg_crypto\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We just added pg_sodium as an extension inside all postgres instances.\\nWhich means you can easily do encryption, decryption, hashing, and cryptographic signing from within your postgres functions and queries.\\nEnable it from the Database \u003e Extensions tab in the \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard\",\n        children: \"Supabase Dashboard\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard\",\n        children: _jsx(_components.img, {\n          src: \"/images/blog/2021-dec/pgsodium.png\",\n          alt: \"pg sodium support\"\n        })\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"postgresql-videos\",\n      children: \"PostgreSQL Videos\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"call-postgres-functions-from-javascript-with-rpc\",\n      children: \"Call Postgres functions from JavaScript with RPC\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Jon Meyers walks you through how to write more complex queries and logic in your database, and invoke it via the API.\"\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"video-container\",\n      children: _jsx(\"iframe\", {\n        className: \"video-with-border w-full\",\n        src: \"https://www.youtube-nocookie.com/embed/I6nnp9AINJk\",\n        frameBorder: \"1\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n        allowFullScreen: true\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"call-any-api-using-postgresql-functions\",\n      children: \"Call any API using PostgreSQL functions\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We recently added the ability to call any external HTTP endpoint from your postgres functions. Jon shows you how!\"\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"video-container\",\n      children: _jsx(\"iframe\", {\n        className: \"video-with-border w-full\",\n        src: \"https://www.youtube-nocookie.com/embed/rARgrELRCwY\",\n        frameBorder: \"1\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n        allowFullScreen: true\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"create-postgresql-functions-with-supabase\",\n      children: \"Create PostgreSQL Functions with Supabase\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Triggers allow you to execute SQL in response to any table changes. Learn how to write PostgreSQL triggers with Jon!\"\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"video-container\",\n      children: _jsx(\"iframe\", {\n        className: \"video-with-border w-full\",\n        src: \"https://www.youtube-nocookie.com/embed/MJZCCpCYEqk\",\n        frameBorder: \"1\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n        allowFullScreen: true\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"hiring\",\n      children: \"Hiring\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We're Hiring - for a ton of new roles across engineering, marketing, and HR. We hire fully remotely, and you'll get to work alongside some incredible people, on one of the fastest growing open source companies. \", _jsx(_components.a, {\n        href: \"https://about.supabase.com/careers\",\n        children: \"See which roles we're hiring for\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://about.supabase.com/careers\",\n        children: _jsx(_components.img, {\n          src: \"/images/blog/2021-dec/hiring.png\",\n          alt: \"supabase is hiring\"\n        })\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"community\",\n      children: \"Community\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"There was a lot of activity this month.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"learn-with-jason\",\n      children: \"Learn With Jason\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Let Jason and Jon show you how to build an app with Next.js and Supabase on top of Netlify!\"\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"video-container\",\n      children: _jsx(\"iframe\", {\n        className: \"video-with-border w-full\",\n        src: \"https://www.youtube-nocookie.com/embed/8vqY1KT4TLU\",\n        frameBorder: \"1\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n        allowFullScreen: true\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"divjoy-integration\",\n      children: \"Divjoy integration\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Divjoy announced their \", _jsx(_components.a, {\n        href: \"https://divjoy.com/?database=supabase\",\n        children: \"Supabase Integration\"\n      }), \" - The React codebase generator allows you to choose your stack, choose your template, and generate your codebase.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2021-dec/divjoy.png\",\n        alt: \"pg sodium support\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"n8n--supabase\",\n      children: \"n8n + Supabase\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://n8n.io/\",\n        children: \"n8n\"\n      }), \" added a Supabase node.\"]\n    }), \"\\n\", _jsxs(\"blockquote\", {\n      class: \"twitter-tweet\",\n      \"data-theme\": \"dark\",\n      children: [_jsx(\"p\", {\n        lang: \"en\",\n        dir: \"ltr\",\n        children: _jsxs(_components.p, {\n          children: [\"We released n8n@0.158.0 with new nodes for\\n\", _jsx(\"a\", {\n            href: \"https://twitter.com/supabase?ref_src=twsrc%5Etfw\",\n            children: \"@supabase\"\n          }), \",\", _jsx(\"a\", {\n            href: \"https://twitter.com/syncromsp?ref_src=twsrc%5Etfw\",\n            children: \"\\n@syncromsp\\n\"\n          }), \", and\\n\", _jsx(\"a\", {\n            href: \"https://twitter.com/Microsoft?ref_src=twsrc%5Etfw\",\n            children: \"@Microsoft\"\n          }), \" Graph Security. We\\nalso made improvements to existing nodes ✨\\n\", _jsx(\"a\", {\n            href: \"https://t.co/IdeUe4eWBK\",\n            children: \"pic.twitter.com/IdeUe4eWBK\"\n          })]\n        })\n      }), _jsxs(_components.p, {\n        children: [\"— n8n.io (@n8n_io) \", _jsx(\"a\", {\n          href: \"https://twitter.com/n8n_io/status/1480502781320572931?ref_src=twsrc%5Etfw\",\n          children: \"January 10, 2022\"\n        })]\n      })]\n    }), \"\\n\", _jsx(\"script\", {\n      async: true,\n      src: \"https://platform.twitter.com/widgets.js\",\n      charset: \"utf-8\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"python-upgrades\",\n      children: \"Python Upgrades\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The Community released a whole bunch of updated Python libs including: supabase v0.0.4 - gotrue v0.3.0 - realtime v0.0.4 - storage3 v0.1.0\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"See the full list of \", _jsx(_components.a, {\n        href: \"https://github.com/supabase-community/\",\n        children: \"community supported libs\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://github.com/supabase-community/\",\n        children: _jsx(_components.img, {\n          src: \"/images/blog/2021-dec/python.png\",\n          alt: \"supabase community libs\"\n        })\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"twitter\",\n      children: \"Twitter\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We're having a 24/7*365 meme-fest on \", _jsx(_components.a, {\n        href: \"https://twitter.com/supabase\",\n        children: \"Twitter\"\n      })]\n    }), \"\\n\", _jsxs(\"blockquote\", {\n      class: \"twitter-tweet\",\n      \"data-theme\": \"dark\",\n      children: [_jsx(\"p\", {\n        lang: \"en\",\n        dir: \"ltr\",\n        children: _jsxs(_components.p, {\n          children: [\"invest in good relationships \", _jsx(\"a\", {\n            href: \"https://t.co/bGiBE7FRFQ\",\n            children: \"pic.twitter.com/bGiBE7FRFQ\"\n          })]\n        })\n      }), _jsxs(_components.p, {\n        children: [\"— Supabase (@supabase) \", _jsx(\"a\", {\n          href: \"https://twitter.com/supabase/status/1475876907212316678?ref_src=twsrc%5Etfw\",\n          children: \"December 28, 2021\"\n        })]\n      })]\n    }), \"\\n\", _jsx(\"script\", {\n      async: true,\n      src: \"https://platform.twitter.com/widgets.js\",\n      charset: \"utf-8\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"tiktok\",\n      children: \"TikTok\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Check us out on \", _jsx(_components.a, {\n        href: \"https://www.tiktok.com/@supabase.com?fromUrl=%2Fsupabase.com\u0026lang=en\",\n        children: \"TikTok\"\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://www.tiktok.com/@supabase.com?fromUrl=%2Fsupabase.com\u0026lang=en\",\n        children: _jsx(_components.img, {\n          src: \"/images/blog/2021-dec/tiktok.png\",\n          alt: \"tiktok\"\n        })\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"swag-store\",\n      children: \"Swag Store\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"A reminder that we have a \", _jsx(_components.a, {\n        href: \"https://supabase.store\",\n        children: \"Supabase Swag Store\"\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://supabase.store\",\n        children: _jsx(_components.img, {\n          src: \"/images/blog/2021-dec/swag.jpg\",\n          alt: \"swag store\"\n        })\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"github\",\n      children: \"GitHub\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We hit 26K stars!!: \", _jsx(_components.a, {\n        href: \"http://github.com/supabase/supabase\",\n        children: \"github.com/supabase/supabase\"\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2021-dec/stars.png\",\n        alt: \"GitHub\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Source: \", _jsx(_components.a, {\n        href: \"https://repository.surf/supabase\",\n        children: \"repository.surf/supabase\"\n      })]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Check out some of our other community stats in our latest \", _jsx(_components.a, {\n        href: \"/blog/supabase-series-a\",\n        children: \"Series A Blog Post\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"get-started\",\n      children: \"Get started\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Start using Supabase today: \", _jsx(_components.strong, {\n          children: _jsx(_components.a, {\n            href: \"https://supabase.com/dashboard/\",\n            children: \"supabase.com/dashboard\"\n          })\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Make sure to \", _jsx(_components.strong, {\n          children: _jsx(_components.a, {\n            href: \"https://github.com/supabase/supabase\",\n            children: \"star us on GitHub\"\n          })\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Follow us \", _jsx(_components.strong, {\n          children: _jsx(_components.a, {\n            href: \"https://twitter.com/supabase\",\n            children: \"on Twitter\"\n          })\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Subscribe to our \", _jsx(_components.strong, {\n          children: _jsx(_components.a, {\n            href: \"https://www.youtube.com/c/supabase\",\n            children: \"YouTube channel\"\n          })\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Become a \", _jsx(_components.strong, {\n          children: _jsx(_components.a, {\n            href: \"https://github.com/sponsors/supabase\",\n            children: \"sponsor\"\n          })\n        })]\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"Hackathon Winners","slug":"hackathon-winners","lvl":2,"i":0,"seen":0},{"content":"New crypto extension - pg_crypto","slug":"new-crypto-extension---pg_crypto","lvl":2,"i":1,"seen":0},{"content":"PostgreSQL Videos","slug":"postgresql-videos","lvl":2,"i":2,"seen":0},{"content":"Call Postgres functions from JavaScript with RPC","slug":"call-postgres-functions-from-javascript-with-rpc","lvl":3,"i":3,"seen":0},{"content":"Call any API using PostgreSQL functions","slug":"call-any-api-using-postgresql-functions","lvl":3,"i":4,"seen":0},{"content":"Create PostgreSQL Functions with Supabase","slug":"create-postgresql-functions-with-supabase","lvl":3,"i":5,"seen":0},{"content":"Hiring","slug":"hiring","lvl":3,"i":6,"seen":0},{"content":"Community","slug":"community","lvl":2,"i":7,"seen":0},{"content":"Learn With Jason","slug":"learn-with-jason","lvl":3,"i":8,"seen":0},{"content":"Divjoy integration","slug":"divjoy-integration","lvl":3,"i":9,"seen":0},{"content":"n8n + Supabase","slug":"n8n--supabase","lvl":3,"i":10,"seen":0},{"content":"Python Upgrades","slug":"python-upgrades","lvl":3,"i":11,"seen":0},{"content":"Twitter","slug":"twitter","lvl":3,"i":12,"seen":0},{"content":"TikTok","slug":"tiktok","lvl":3,"i":13,"seen":0},{"content":"Swag Store","slug":"swag-store","lvl":3,"i":14,"seen":0},{"content":"GitHub","slug":"github","lvl":3,"i":15,"seen":0},{"content":"Get started","slug":"get-started","lvl":2,"i":16,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,2],"level":0},{"type":"inline","content":"Happy 2022. We're looking forward to a year of shipping product and helping our users build great things!","level":1,"lines":[1,2],"children":[{"type":"text","content":"Happy 2022. We're looking forward to a year of shipping product and helping our users build great things!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[3,4],"level":0},{"type":"inline","content":"Here's a bunch of stuff we shipped throughout December...","level":1,"lines":[3,4],"children":[{"type":"text","content":"Here's a bunch of stuff we shipped throughout December...","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[5,6],"level":0},{"type":"inline","content":"[Hackathon Winners](#hackathon-winners)","level":1,"lines":[5,6],"children":[{"type":"text","content":"Hackathon Winners","level":0}],"lvl":2,"i":0,"seen":0,"slug":"hackathon-winners"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[7,8],"level":0},{"type":"inline","content":"We had a ton of awesome projects [submitted for our Holiday Hackdays](/blog/holiday-hackdays-winners-2021), all of the submissions are open source so come and check out the code for some of our top picks!","level":1,"lines":[7,8],"children":[{"type":"text","content":"We had a ton of awesome projects ","level":0},{"type":"link_open","href":"/blog/holiday-hackdays-winners-2021","title":"","level":0},{"type":"text","content":"submitted for our Holiday Hackdays","level":1},{"type":"link_close","level":0},{"type":"text","content":", all of the submissions are open source so come and check out the code for some of our top picks!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[9,10],"level":0},{"type":"inline","content":"[![hackathon winners](/images/blog/2021-dec/hackathon.png)](/blog/holiday-hackdays-winners-2021)","level":1,"lines":[9,10],"children":[{"type":"link_open","href":"/blog/holiday-hackdays-winners-2021","title":"","level":0},{"type":"image","src":"/images/blog/2021-dec/hackathon.png","title":"","alt":"hackathon winners","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[11,12],"level":0},{"type":"inline","content":"[New crypto extension - pg_crypto](#new-crypto-extension---pg_crypto)","level":1,"lines":[11,12],"children":[{"type":"text","content":"New crypto extension - pg_crypto","level":0}],"lvl":2,"i":1,"seen":0,"slug":"new-crypto-extension---pg_crypto"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[13,16],"level":0},{"type":"inline","content":"We just added pg_sodium as an extension inside all postgres instances.\nWhich means you can easily do encryption, decryption, hashing, and cryptographic signing from within your postgres functions and queries.\nEnable it from the Database \u003e Extensions tab in the [Supabase Dashboard](https://supabase.com/dashboard).","level":1,"lines":[13,16],"children":[{"type":"text","content":"We just added pg_sodium as an extension inside all postgres instances.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Which means you can easily do encryption, decryption, hashing, and cryptographic signing from within your postgres functions and queries.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Enable it from the Database \u003e Extensions tab in the ","level":0},{"type":"link_open","href":"https://supabase.com/dashboard","title":"","level":0},{"type":"text","content":"Supabase Dashboard","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[17,18],"level":0},{"type":"inline","content":"[![pg sodium support](/images/blog/2021-dec/pgsodium.png)](https://supabase.com/dashboard)","level":1,"lines":[17,18],"children":[{"type":"link_open","href":"https://supabase.com/dashboard","title":"","level":0},{"type":"image","src":"/images/blog/2021-dec/pgsodium.png","title":"","alt":"pg sodium support","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[19,20],"level":0},{"type":"inline","content":"[PostgreSQL Videos](#postgresql-videos)","level":1,"lines":[19,20],"children":[{"type":"text","content":"PostgreSQL Videos","level":0}],"lvl":2,"i":2,"seen":0,"slug":"postgresql-videos"},{"type":"heading_close","hLevel":2,"level":0},{"type":"heading_open","hLevel":3,"lines":[21,22],"level":0},{"type":"inline","content":"[Call Postgres functions from JavaScript with RPC](#call-postgres-functions-from-javascript-with-rpc)","level":1,"lines":[21,22],"children":[{"type":"text","content":"Call Postgres functions from JavaScript with RPC","level":0}],"lvl":3,"i":3,"seen":0,"slug":"call-postgres-functions-from-javascript-with-rpc"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[23,24],"level":0},{"type":"inline","content":"Jon Meyers walks you through how to write more complex queries and logic in your database, and invoke it via the API.","level":1,"lines":[23,24],"children":[{"type":"text","content":"Jon Meyers walks you through how to write more complex queries and logic in your database, and invoke it via the API.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[25,32],"level":0},{"type":"inline","content":"\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"video-with-border w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/I6nnp9AINJk\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen","level":1,"lines":[25,32],"children":[{"type":"text","content":"\u003cdiv className=\"video-container\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ciframe","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"video-with-border w-full\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://www.youtube-nocookie.com/embed/I6nnp9AINJk\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"frameBorder=\"1\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allowFullScreen","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"blockquote_open","lines":[32,34],"level":0},{"type":"paragraph_open","tight":false,"lines":[32,34],"level":1},{"type":"inline","content":"\u003c/iframe\u003e\n\u003c/div\u003e","level":2,"lines":[32,34],"children":[{"type":"text","content":"\u003c/iframe\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"heading_open","hLevel":3,"lines":[35,36],"level":0},{"type":"inline","content":"[Call any API using PostgreSQL functions](#call-any-api-using-postgresql-functions)","level":1,"lines":[35,36],"children":[{"type":"text","content":"Call any API using PostgreSQL functions","level":0}],"lvl":3,"i":4,"seen":0,"slug":"call-any-api-using-postgresql-functions"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[37,38],"level":0},{"type":"inline","content":"We recently added the ability to call any external HTTP endpoint from your postgres functions. Jon shows you how!","level":1,"lines":[37,38],"children":[{"type":"text","content":"We recently added the ability to call any external HTTP endpoint from your postgres functions. Jon shows you how!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[39,46],"level":0},{"type":"inline","content":"\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"video-with-border w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/rARgrELRCwY\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen","level":1,"lines":[39,46],"children":[{"type":"text","content":"\u003cdiv className=\"video-container\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ciframe","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"video-with-border w-full\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://www.youtube-nocookie.com/embed/rARgrELRCwY\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"frameBorder=\"1\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allowFullScreen","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"blockquote_open","lines":[46,48],"level":0},{"type":"paragraph_open","tight":false,"lines":[46,48],"level":1},{"type":"inline","content":"\u003c/iframe\u003e\n\u003c/div\u003e","level":2,"lines":[46,48],"children":[{"type":"text","content":"\u003c/iframe\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"heading_open","hLevel":3,"lines":[49,50],"level":0},{"type":"inline","content":"[Create PostgreSQL Functions with Supabase](#create-postgresql-functions-with-supabase)","level":1,"lines":[49,50],"children":[{"type":"text","content":"Create PostgreSQL Functions with Supabase","level":0}],"lvl":3,"i":5,"seen":0,"slug":"create-postgresql-functions-with-supabase"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[51,52],"level":0},{"type":"inline","content":"Triggers allow you to execute SQL in response to any table changes. Learn how to write PostgreSQL triggers with Jon!","level":1,"lines":[51,52],"children":[{"type":"text","content":"Triggers allow you to execute SQL in response to any table changes. Learn how to write PostgreSQL triggers with Jon!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[53,60],"level":0},{"type":"inline","content":"\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"video-with-border w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/MJZCCpCYEqk\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen","level":1,"lines":[53,60],"children":[{"type":"text","content":"\u003cdiv className=\"video-container\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ciframe","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"video-with-border w-full\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://www.youtube-nocookie.com/embed/MJZCCpCYEqk\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"frameBorder=\"1\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allowFullScreen","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"blockquote_open","lines":[60,62],"level":0},{"type":"paragraph_open","tight":false,"lines":[60,62],"level":1},{"type":"inline","content":"\u003c/iframe\u003e\n\u003c/div\u003e","level":2,"lines":[60,62],"children":[{"type":"text","content":"\u003c/iframe\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"heading_open","hLevel":3,"lines":[63,64],"level":0},{"type":"inline","content":"[Hiring](#hiring)","level":1,"lines":[63,64],"children":[{"type":"text","content":"Hiring","level":0}],"lvl":3,"i":6,"seen":0,"slug":"hiring"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[65,66],"level":0},{"type":"inline","content":"We're Hiring - for a ton of new roles across engineering, marketing, and HR. We hire fully remotely, and you'll get to work alongside some incredible people, on one of the fastest growing open source companies. [See which roles we're hiring for](https://about.supabase.com/careers).","level":1,"lines":[65,66],"children":[{"type":"text","content":"We're Hiring - for a ton of new roles across engineering, marketing, and HR. We hire fully remotely, and you'll get to work alongside some incredible people, on one of the fastest growing open source companies. ","level":0},{"type":"link_open","href":"https://about.supabase.com/careers","title":"","level":0},{"type":"text","content":"See which roles we're hiring for","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[67,68],"level":0},{"type":"inline","content":"[![supabase is hiring](/images/blog/2021-dec/hiring.png)](https://about.supabase.com/careers)","level":1,"lines":[67,68],"children":[{"type":"link_open","href":"https://about.supabase.com/careers","title":"","level":0},{"type":"image","src":"/images/blog/2021-dec/hiring.png","title":"","alt":"supabase is hiring","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[69,70],"level":0},{"type":"inline","content":"[Community](#community)","level":1,"lines":[69,70],"children":[{"type":"text","content":"Community","level":0}],"lvl":2,"i":7,"seen":0,"slug":"community"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[71,72],"level":0},{"type":"inline","content":"There was a lot of activity this month.","level":1,"lines":[71,72],"children":[{"type":"text","content":"There was a lot of activity this month.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[73,74],"level":0},{"type":"inline","content":"[Learn With Jason](#learn-with-jason)","level":1,"lines":[73,74],"children":[{"type":"text","content":"Learn With Jason","level":0}],"lvl":3,"i":8,"seen":0,"slug":"learn-with-jason"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[75,76],"level":0},{"type":"inline","content":"Let Jason and Jon show you how to build an app with Next.js and Supabase on top of Netlify!","level":1,"lines":[75,76],"children":[{"type":"text","content":"Let Jason and Jon show you how to build an app with Next.js and Supabase on top of Netlify!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[77,84],"level":0},{"type":"inline","content":"\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"video-with-border w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/8vqY1KT4TLU\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen","level":1,"lines":[77,84],"children":[{"type":"text","content":"\u003cdiv className=\"video-container\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ciframe","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"video-with-border w-full\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://www.youtube-nocookie.com/embed/8vqY1KT4TLU\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"frameBorder=\"1\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allowFullScreen","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"blockquote_open","lines":[84,85],"level":0},{"type":"paragraph_open","tight":false,"lines":[84,85],"level":1},{"type":"inline","content":"\u003c/iframe\u003e","level":2,"lines":[84,85],"children":[{"type":"text","content":"\u003c/iframe\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"paragraph_open","tight":false,"lines":[85,86],"level":0},{"type":"inline","content":"\u003c/div\u003e","level":1,"lines":[85,86],"children":[{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[87,88],"level":0},{"type":"inline","content":"[Divjoy integration](#divjoy-integration)","level":1,"lines":[87,88],"children":[{"type":"text","content":"Divjoy integration","level":0}],"lvl":3,"i":9,"seen":0,"slug":"divjoy-integration"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[89,90],"level":0},{"type":"inline","content":"Divjoy announced their [Supabase Integration](https://divjoy.com/?database=supabase) - The React codebase generator allows you to choose your stack, choose your template, and generate your codebase.","level":1,"lines":[89,90],"children":[{"type":"text","content":"Divjoy announced their ","level":0},{"type":"link_open","href":"https://divjoy.com/?database=supabase","title":"","level":0},{"type":"text","content":"Supabase Integration","level":1},{"type":"link_close","level":0},{"type":"text","content":" - The React codebase generator allows you to choose your stack, choose your template, and generate your codebase.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[91,92],"level":0},{"type":"inline","content":"![pg sodium support](/images/blog/2021-dec/divjoy.png)","level":1,"lines":[91,92],"children":[{"type":"image","src":"/images/blog/2021-dec/divjoy.png","title":"","alt":"pg sodium support","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[93,94],"level":0},{"type":"inline","content":"[n8n + Supabase](#n8n--supabase)","level":1,"lines":[93,94],"children":[{"type":"text","content":"n8n + Supabase","level":0}],"lvl":3,"i":10,"seen":0,"slug":"n8n--supabase"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[95,96],"level":0},{"type":"inline","content":"[n8n](https://n8n.io/) added a Supabase node.","level":1,"lines":[95,96],"children":[{"type":"link_open","href":"https://n8n.io/","title":"","level":0},{"type":"text","content":"n8n","level":1},{"type":"link_close","level":0},{"type":"text","content":" added a Supabase node.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[97,109],"level":0},{"type":"inline","content":"\u003cblockquote class=\"twitter-tweet\" data-theme=\"dark\"\u003e\n  \u003cp lang=\"en\" dir=\"ltr\"\u003e\n    We released n8n@0.158.0 with new nodes for\n    \u003ca href=\"https://twitter.com/supabase?ref_src=twsrc%5Etfw\"\u003e@supabase\u003c/a\u003e,\u003ca href=\"https://twitter.com/syncromsp?ref_src=twsrc%5Etfw\"\u003e\n      @syncromsp\n    \u003c/a\u003e, and\n    \u003ca href=\"https://twitter.com/Microsoft?ref_src=twsrc%5Etfw\"\u003e@Microsoft\u003c/a\u003e Graph Security. We\n    also made improvements to existing nodes ✨\n    \u003ca href=\"https://t.co/IdeUe4eWBK\"\u003epic.twitter.com/IdeUe4eWBK\u003c/a\u003e\n  \u003c/p\u003e\n  \u0026mdash; n8n.io (@n8n_io) \u003ca href=\"https://twitter.com/n8n_io/status/1480502781320572931?ref_src=twsrc%5Etfw\"\u003eJanuary 10, 2022\u003c/a\u003e\n\u003c/blockquote\u003e \u003cscript async src=\"https://platform.twitter.com/widgets.js\" charset=\"utf-8\"\u003e\u003c/script\u003e","level":1,"lines":[97,109],"children":[{"type":"text","content":"\u003cblockquote class=\"twitter-tweet\" data-theme=\"dark\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cp lang=\"en\" dir=\"ltr\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"We released n8n@0.158.0 with new nodes for","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ca href=\"https://twitter.com/supabase?ref_src=twsrc%5Etfw\"\u003e@supabase\u003c/a\u003e,\u003ca href=\"https://twitter.com/syncromsp?ref_src=twsrc%5Etfw\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"@syncromsp","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/a\u003e, and","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ca href=\"https://twitter.com/Microsoft?ref_src=twsrc%5Etfw\"\u003e@Microsoft\u003c/a\u003e Graph Security. We","level":0},{"type":"softbreak","level":0},{"type":"text","content":"also made improvements to existing nodes ✨","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ca href=\"https://t.co/IdeUe4eWBK\"\u003epic.twitter.com/IdeUe4eWBK\u003c/a\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/p\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"— n8n.io (@n8n_io) \u003ca href=\"https://twitter.com/n8n_io/status/1480502781320572931?ref_src=twsrc%5Etfw\"\u003eJanuary 10, 2022\u003c/a\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/blockquote\u003e \u003cscript async src=\"https://platform.twitter.com/widgets.js\" charset=\"utf-8\"\u003e\u003c/script\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[110,111],"level":0},{"type":"inline","content":"[Python Upgrades](#python-upgrades)","level":1,"lines":[110,111],"children":[{"type":"text","content":"Python Upgrades","level":0}],"lvl":3,"i":11,"seen":0,"slug":"python-upgrades"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[112,113],"level":0},{"type":"inline","content":"The Community released a whole bunch of updated Python libs including: supabase v0.0.4 - gotrue v0.3.0 - realtime v0.0.4 - storage3 v0.1.0","level":1,"lines":[112,113],"children":[{"type":"text","content":"The Community released a whole bunch of updated Python libs including: supabase v0.0.4 - gotrue v0.3.0 - realtime v0.0.4 - storage3 v0.1.0","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[114,115],"level":0},{"type":"inline","content":"See the full list of [community supported libs](https://github.com/supabase-community/).","level":1,"lines":[114,115],"children":[{"type":"text","content":"See the full list of ","level":0},{"type":"link_open","href":"https://github.com/supabase-community/","title":"","level":0},{"type":"text","content":"community supported libs","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[116,117],"level":0},{"type":"inline","content":"[![supabase community libs](/images/blog/2021-dec/python.png)](https://github.com/supabase-community/)","level":1,"lines":[116,117],"children":[{"type":"link_open","href":"https://github.com/supabase-community/","title":"","level":0},{"type":"image","src":"/images/blog/2021-dec/python.png","title":"","alt":"supabase community libs","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[118,119],"level":0},{"type":"inline","content":"[Twitter](#twitter)","level":1,"lines":[118,119],"children":[{"type":"text","content":"Twitter","level":0}],"lvl":3,"i":12,"seen":0,"slug":"twitter"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[120,121],"level":0},{"type":"inline","content":"We're having a 24/7\\*365 meme-fest on [Twitter](https://twitter.com/supabase)","level":1,"lines":[120,121],"children":[{"type":"text","content":"We're having a 24/7*365 meme-fest on ","level":0},{"type":"link_open","href":"https://twitter.com/supabase","title":"","level":0},{"type":"text","content":"Twitter","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[122,128],"level":0},{"type":"inline","content":"\u003cblockquote class=\"twitter-tweet\" data-theme=\"dark\"\u003e\n  \u003cp lang=\"en\" dir=\"ltr\"\u003e\n    invest in good relationships \u003ca href=\"https://t.co/bGiBE7FRFQ\"\u003epic.twitter.com/bGiBE7FRFQ\u003c/a\u003e\n  \u003c/p\u003e\n  \u0026mdash; Supabase (@supabase) \u003ca href=\"https://twitter.com/supabase/status/1475876907212316678?ref_src=twsrc%5Etfw\"\u003eDecember 28, 2021\u003c/a\u003e\n\u003c/blockquote\u003e \u003cscript async src=\"https://platform.twitter.com/widgets.js\" charset=\"utf-8\"\u003e\u003c/script\u003e","level":1,"lines":[122,128],"children":[{"type":"text","content":"\u003cblockquote class=\"twitter-tweet\" data-theme=\"dark\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cp lang=\"en\" dir=\"ltr\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"invest in good relationships \u003ca href=\"https://t.co/bGiBE7FRFQ\"\u003epic.twitter.com/bGiBE7FRFQ\u003c/a\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/p\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"— Supabase (@supabase) \u003ca href=\"https://twitter.com/supabase/status/1475876907212316678?ref_src=twsrc%5Etfw\"\u003eDecember 28, 2021\u003c/a\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/blockquote\u003e \u003cscript async src=\"https://platform.twitter.com/widgets.js\" charset=\"utf-8\"\u003e\u003c/script\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[129,130],"level":0},{"type":"inline","content":"[TikTok](#tiktok)","level":1,"lines":[129,130],"children":[{"type":"text","content":"TikTok","level":0}],"lvl":3,"i":13,"seen":0,"slug":"tiktok"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[131,132],"level":0},{"type":"inline","content":"Check us out on [TikTok](https://www.tiktok.com/@supabase.com?fromUrl=%2Fsupabase.com\u0026lang=en)","level":1,"lines":[131,132],"children":[{"type":"text","content":"Check us out on ","level":0},{"type":"link_open","href":"https://www.tiktok.com/@supabase.com?fromUrl=%2Fsupabase.com\u0026lang=en","title":"","level":0},{"type":"text","content":"TikTok","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[133,134],"level":0},{"type":"inline","content":"[![tiktok](/images/blog/2021-dec/tiktok.png)](https://www.tiktok.com/@supabase.com?fromUrl=%2Fsupabase.com\u0026lang=en)","level":1,"lines":[133,134],"children":[{"type":"link_open","href":"https://www.tiktok.com/@supabase.com?fromUrl=%2Fsupabase.com\u0026lang=en","title":"","level":0},{"type":"image","src":"/images/blog/2021-dec/tiktok.png","title":"","alt":"tiktok","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[135,136],"level":0},{"type":"inline","content":"[Swag Store](#swag-store)","level":1,"lines":[135,136],"children":[{"type":"text","content":"Swag Store","level":0}],"lvl":3,"i":14,"seen":0,"slug":"swag-store"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[137,138],"level":0},{"type":"inline","content":"A reminder that we have a [Supabase Swag Store](https://supabase.store)","level":1,"lines":[137,138],"children":[{"type":"text","content":"A reminder that we have a ","level":0},{"type":"link_open","href":"https://supabase.store","title":"","level":0},{"type":"text","content":"Supabase Swag Store","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[139,140],"level":0},{"type":"inline","content":"[![swag store](/images/blog/2021-dec/swag.jpg)](https://supabase.store)","level":1,"lines":[139,140],"children":[{"type":"link_open","href":"https://supabase.store","title":"","level":0},{"type":"image","src":"/images/blog/2021-dec/swag.jpg","title":"","alt":"swag store","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[141,142],"level":0},{"type":"inline","content":"[GitHub](#github)","level":1,"lines":[141,142],"children":[{"type":"text","content":"GitHub","level":0}],"lvl":3,"i":15,"seen":0,"slug":"github"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[143,144],"level":0},{"type":"inline","content":"We hit 26K stars!!: [github.com/supabase/supabase](http://github.com/supabase/supabase)","level":1,"lines":[143,144],"children":[{"type":"text","content":"We hit 26K stars!!: ","level":0},{"type":"link_open","href":"http://github.com/supabase/supabase","title":"","level":0},{"type":"text","content":"github.com/supabase/supabase","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[145,146],"level":0},{"type":"inline","content":"![GitHub](/images/blog/2021-dec/stars.png)","level":1,"lines":[145,146],"children":[{"type":"image","src":"/images/blog/2021-dec/stars.png","title":"","alt":"GitHub","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[147,148],"level":0},{"type":"inline","content":"Source: [repository.surf/supabase](https://repository.surf/supabase)","level":1,"lines":[147,148],"children":[{"type":"text","content":"Source: ","level":0},{"type":"link_open","href":"https://repository.surf/supabase","title":"","level":0},{"type":"text","content":"repository.surf/supabase","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[149,150],"level":0},{"type":"inline","content":"Check out some of our other community stats in our latest [Series A Blog Post](/blog/supabase-series-a).","level":1,"lines":[149,150],"children":[{"type":"text","content":"Check out some of our other community stats in our latest ","level":0},{"type":"link_open","href":"/blog/supabase-series-a","title":"","level":0},{"type":"text","content":"Series A Blog Post","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[151,152],"level":0},{"type":"inline","content":"[Get started](#get-started)","level":1,"lines":[151,152],"children":[{"type":"text","content":"Get started","level":0}],"lvl":2,"i":16,"seen":0,"slug":"get-started"},{"type":"heading_close","hLevel":2,"level":0},{"type":"bullet_list_open","lines":[153,158],"level":0},{"type":"list_item_open","lines":[153,154],"level":1},{"type":"paragraph_open","tight":true,"lines":[153,154],"level":2},{"type":"inline","content":"Start using Supabase today: **[supabase.com/dashboard](https://supabase.com/dashboard/)**","level":3,"lines":[153,154],"children":[{"type":"text","content":"Start using Supabase today: ","level":0},{"type":"strong_open","level":0},{"type":"link_open","href":"https://supabase.com/dashboard/","title":"","level":1},{"type":"text","content":"supabase.com/dashboard","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[154,155],"level":1},{"type":"paragraph_open","tight":true,"lines":[154,155],"level":2},{"type":"inline","content":"Make sure to **[star us on GitHub](https://github.com/supabase/supabase)**","level":3,"lines":[154,155],"children":[{"type":"text","content":"Make sure to ","level":0},{"type":"strong_open","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase","title":"","level":1},{"type":"text","content":"star us on GitHub","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[155,156],"level":1},{"type":"paragraph_open","tight":true,"lines":[155,156],"level":2},{"type":"inline","content":"Follow us **[on Twitter](https://twitter.com/supabase)**","level":3,"lines":[155,156],"children":[{"type":"text","content":"Follow us ","level":0},{"type":"strong_open","level":0},{"type":"link_open","href":"https://twitter.com/supabase","title":"","level":1},{"type":"text","content":"on Twitter","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[156,157],"level":1},{"type":"paragraph_open","tight":true,"lines":[156,157],"level":2},{"type":"inline","content":"Subscribe to our **[YouTube channel](https://www.youtube.com/c/supabase)**","level":3,"lines":[156,157],"children":[{"type":"text","content":"Subscribe to our ","level":0},{"type":"strong_open","level":0},{"type":"link_open","href":"https://www.youtube.com/c/supabase","title":"","level":1},{"type":"text","content":"YouTube channel","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[157,158],"level":1},{"type":"paragraph_open","tight":true,"lines":[157,158],"level":2},{"type":"inline","content":"Become a **[sponsor](https://github.com/sponsors/supabase)**","level":3,"lines":[157,158],"children":[{"type":"text","content":"Become a ","level":0},{"type":"strong_open","level":0},{"type":"link_open","href":"https://github.com/sponsors/supabase","title":"","level":1},{"type":"text","content":"sponsor","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0}],"content":"- [Hackathon Winners](#hackathon-winners)\n- [New crypto extension - pg_crypto](#new-crypto-extension---pg_crypto)\n- [PostgreSQL Videos](#postgresql-videos)\n  * [Call Postgres functions from JavaScript with RPC](#call-postgres-functions-from-javascript-with-rpc)\n  * [Call any API using PostgreSQL functions](#call-any-api-using-postgresql-functions)\n  * [Create PostgreSQL Functions with Supabase](#create-postgresql-functions-with-supabase)\n  * [Hiring](#hiring)\n- [Community](#community)\n  * [Learn With Jason](#learn-with-jason)\n  * [Divjoy integration](#divjoy-integration)\n  * [n8n + Supabase](#n8n--supabase)\n  * [Python Upgrades](#python-upgrades)\n  * [Twitter](#twitter)\n  * [TikTok](#tiktok)\n  * [Swag Store](#swag-store)\n  * [GitHub](#github)\n- [Get started](#get-started)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-beta-december-2021"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>