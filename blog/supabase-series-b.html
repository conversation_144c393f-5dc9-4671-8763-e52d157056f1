<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase Series B</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Supabase raised $80M in May, bringing our total funding to $116M." data-next-head=""/><meta property="og:title" content="Supabase Series B" data-next-head=""/><meta property="og:description" content="Supabase raised $80M in May, bringing our total funding to $116M." data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-series-b" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2022-08-12" data-next-head=""/><meta property="article:author" content="https://github.com/kiwicopple" data-next-head=""/><meta property="article:tag" content="supabase" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/series-b/supabase-series-b.png" data-next-head=""/><meta property="og:image:alt" content="Supabase Series B thumbnail" data-next-head=""/><meta property="og:video" content="https://www.youtube.com/v/4t_63HT3rZY" data-next-head=""/><meta property="og:video:type" content="application/x-shockwave-flash" data-next-head=""/><meta property="og:video:width" content="640" data-next-head=""/><meta property="og:video:height" content="385" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Supabase Series B</h1><div class="text-light flex space-x-3 text-sm"><p>12 Aug 2022</p><p>•</p><p>7 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/kiwicopple"><div class="flex items-center gap-3"><div class="w-10"><img alt="Paul Copplestone avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fkiwicopple.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fkiwicopple.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fkiwicopple.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Paul Copplestone</span><span class="text-foreground-lighter mb-0 text-xs">CEO and Co-Founder</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Supabase Series B" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fseries-b%2Fsupabase-series-b.png&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fseries-b%2Fsupabase-series-b.png&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fseries-b%2Fsupabase-series-b.png&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fseries-b%2Fsupabase-series-b.png&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fseries-b%2Fsupabase-series-b.png&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fseries-b%2Fsupabase-series-b.png&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fseries-b%2Fsupabase-series-b.png&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fseries-b%2Fsupabase-series-b.png&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fseries-b%2Fsupabase-series-b.png&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fseries-b%2Fsupabase-series-b.png&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fseries-b%2Fsupabase-series-b.png&amp;w=3840&amp;q=100"/></div><p>Supabase raised $80M in May, bringing our total funding to $116M. This comes one year after our Series A, and so we&#x27;re revisiting the plans
we outlined in our <a href="https://supabase.com/blog/supabase-series-a">Series A blog post</a> tk hold ourselves accountable for the promises we made.</p>
<h2 id="where-weve-been" class="group scroll-mt-24">Where we&#x27;ve been<a href="#where-weve-been" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We shared a few metrics in our Series A post. Since then we&#x27;ve grown a lot.</p>
<h3 id="database-growth" class="group scroll-mt-24">Database growth<a href="#database-growth" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>At Series A we&#x27;d launched over 50,000 PostgreSQL databases (on our hosted platform).</p>
<p></p>
<p>As of today, we&#x27;ve launched over 150,000 PostgreSQL databases.</p>
<p></p>
<h3 id="developer-signups" class="group scroll-mt-24">Developer Signups<a href="#developer-signups" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>At Series A we had 40,000 developers.</p>
<p></p>
<p>Today we have more than 110,000 developers.</p>
<p></p>
<h3 id="community" class="group scroll-mt-24">Community<a href="#community" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We&#x27;ve seen a lot of growth in our community:</p>
<ul>
<li>Discord: 4000 → 8000</li>
<li>Twitter: 18,000 → 37,000</li>
<li>GitHub Stars: 19,000 → 36,000</li>
</ul>
<p><a href="https://star-history.com/#supabase/supabase&amp;facebook/react&amp;Timeline">Supabase vs React</a> (GitHub stars)</p>
<p></p>
<p>It&#x27;s early days but we&#x27;re doing well for a database company.</p>
<h2 id="about-the-round" class="group scroll-mt-24">About the round<a href="#about-the-round" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Our Series B is led by <a href="https://www.felicis.com/">Felicis</a>, an amazing team of people who support the long-term approach we&#x27;re taking to build an open source business.</p>
<blockquote class="text-foreground"><p><p>We are super excited to be investing in Supabase and partner with the team on their next phase
of growth.</p></p><p><p>Supabase&#x27;s team is made up of 15% former founders and 70% developers, and they deeply understand
the pain points that developers overcome to rapidly develop products. Supabase truly enables
developers to build their applications without repeating the same tedious tasks and manage their
application&#x27;s database, authentication, storage, and edge functions.</p></p><div class="align-center m-0 flex h-8 items-center gap-3"><img alt="Aydin Senkut, Founder and Managing Partner of Felicis. avatar" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="h-8 w-8 rounded-full object-cover text-center m-0" style="color:transparent" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Favatars%2Faydin.jpeg&amp;w=32&amp;q=75 1x, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Favatars%2Faydin.jpeg&amp;w=64&amp;q=75 2x" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Favatars%2Faydin.jpeg&amp;w=64&amp;q=75"/><figcaption style="margin-top:0" class="text-foreground-lighter font-normal not-italic not-prose"><p>Aydin Senkut, Founder and Managing Partner of Felicis.</p></figcaption></div></blockquote>
<p>Joining the round are Coatue (who led our Series A &amp; Seed round), <a href="https://lsvp.com/">Lightspeed</a>, and <a href="https://www.squarepegcap.com/">Square Peg Capital</a>.</p>
<h2 id="giving-back" class="group scroll-mt-24">Giving back<a href="#giving-back" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Supabase is a collaborative company. We aim to support existing open source tools before developing anything ourselves.
When we do develop ourselves, we try to include our community as maintainers in a variety of ways. We&#x27;ve been experimenting
with several models to support the open source ecosystem.</p>
<h3 id="open-collective" class="group scroll-mt-24">Open collective<a href="#open-collective" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>To date, we&#x27;ve <a href="https://opencollective.com/supabase#category-BUDGET">paid over $80,000</a> to contributors and maintainers through our open collective.
This is for everything from maintaining our libraries to moderating our Discord.</p>
<p>We&#x27;ve had some amazing open source stories like <a href="https://github.com/Isaiah-Hamilton">Isaiah</a>, a 15-year old who started contributing after his school day.
Or <a href="https://github.com/Olyno">Olyno</a>, who created a Supabase Discord on their own initiative and is now an official moderator.
Or <a href="https://github.com/zernonia/">Zernonia</a>, who created <a href="https://www.madewithsupabase.com/">madewithsupabase.com</a> which is now central to every Supabase Hackathon.</p>
<h3 id="postgrest" class="group scroll-mt-24">PostgREST<a href="#postgrest" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p><a href="https://postgrest.org">PostgREST</a> is a automatically generate RESTful APIs from your Postgres schema, and it&#x27;s one of the core pieces of the Supabase stack.
As well as being a gold sponsor, Supabase hired <a href="https://github.com/steve-chavez/">Steve</a>, the maintainer of PostgREST in 2020 to work primarily on PostgREST.
This model has been surprisingly successful and we hope that more companies will consider this approach for supporting open source projects.</p>
<h3 id="elixir-type-support" class="group scroll-mt-24">Elixir Type support<a href="#elixir-type-support" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Elixir is a functional programming language that enables huge horizontal scalability. We use it at Supabase for our Realtime
engine and our logging infrastructure. Recently <a href="https://twitter.com/josevalim/status/1535008937640181760?s=20&amp;t=BLqeO2YpdhYfCZxgsmlNug">announced by José</a> at ElixirConf EU,
the Elixir team is investigating a type system. Supabase is sponsoring this research and development.</p>
<h3 id="deno" class="group scroll-mt-24">Deno<a href="#deno" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p><a href="../edge-functions.html">Functions</a> was one of the most demanded features of Supabase, and we explored a huge number of options before settling on Deno.
Deno checked all the boxes - most important for us was their approach to open source. Both Supabase and Netlify now support Deno Edge Functions,
and we hope that our backing will help convince others to adopt the Deno runtime too.</p>
<h3 id="database-encryption" class="group scroll-mt-24">Database encryption<a href="#database-encryption" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>A teaser for next week&#x27;s <a href="../launch-week.html">Launch Week</a> - a few months ago we hired <a href="https://github.com/michelp">Michel</a>,
the maintainer of <a href="https://github.com/michelp/pgsodium">pgsodium</a>, to re-think what a modern database encryption system and
database secrets management would look like. Since then he has released two major versions of
pgsodium: <a href="https://www.postgresql.org/about/news/pgsodium-200-modern-cryptography-for-postgresql-2389/">2.0</a>
and <a href="https://github.com/michelp/pgsodium/releases/tag/v3.0.0">3.0</a> to
support <a href="https://github.com/michelp/pgsodium#transparent-column-encryption">Transparent Column Encryption</a>.</p>
<div class="video-container"><iframe class="video-with-border w-full" src="https://www.youtube-nocookie.com/embed/4t_63HT3rZY" frameBorder="1" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen=""></iframe></div>
<small><i><p>Founder Chat: Paul Copplestone and Ant Wilson discuss the several ways we give back to the
community, and what Open Source means for Supabase.</p></i></small>
<h2 id="where-were-going" class="group scroll-mt-24">Where we&#x27;re going<a href="#where-were-going" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>In our Series A post, we outlined our plans for Supabase <a href="https://supabase.com/blog/supabase-series-a#building-supabase">in three phases</a>:</p>
<ul>
<li>Phase 1: <a href="https://supabase.com/blog/supabase-series-a#phase-1-start-with-scalability">Start with scalability</a></li>
<li>Phase 2: <a href="https://supabase.com/blog/supabase-series-a#phase-2-postgres-tooling">Postgres Tooling</a></li>
<li>Phase 3: <a href="https://supabase.com/blog/supabase-series-a#phase-3-cloud-native-postgresql">Cloud-native PostgreSQL</a></li>
</ul>
<p>We&#x27;re now moving into Phase 3, with the same goals that we outlined in 2021:</p>
<ul>
<li><strong>Branching:</strong> <br/>
Developers should be able to &quot;fork&quot; a database at any point. This is particularly important for Jamstack developers who run deployments for every Git branch.</li>
<li><strong>Scalable storage:</strong> <br/>
Storage should grow and shrink without the user needing to provision more space themselves.</li>
<li><strong>Distributed:</strong> <br/>
An entire fleet of databases, distributed around the world, should feel like a single database (and even better if all nodes can accept read/write workloads).</li>
<li><strong>Ephemeral compute:</strong> <br/>
Developers don&#x27;t want to be charged for a database which isn&#x27;t doing anything. Likewise - if they are running huge computation then the database should scale up seamlessly, and scale down to zero when it&#x27;s unused.</li>
<li><strong>Snapshots and time-travel:</strong> <br/>
Developers should be able to roll back to any point in time, or take a copy of their database with just a click of a button.</li>
</ul>
<p>We have a lot of work ahead of us, but we&#x27;re excited to announce one of the ways that we&#x27;re supporting the community is through our investment into OrioleDB.</p>
<p>OrioleDB is a <a href="https://github.com/OrioleDB/OrioleDB">PostgreSQL extension</a> which solves a lot
of <a href="https://www.socallinuxexpo.org/sites/default/files/presentations/solving-postgres-wicked-problems.pdf">“wicked” problems</a> with Postgres.</p>
<p>While it <em>currently</em> requires some modifications to the Postgres core (about 1000 lines), the goal is to upstream those changes so
that <em>anyone</em> can build a Storage extension like OrioleDB. Giving developers low-level access to the underlying storage capabilities of
Postgres will unlock a slew of new capabilities in Postgres.
You can read more about OrioleDB&#x27;s plans for Snapshots and Branching <a href="https://github.com/OrioleDB/OrioleDB/wiki/Database-branching">in their wiki</a>.</p>
<p>Supabase has invested $500K into OrioleDB to support their efforts, and we&#x27;ve hired a developer at Supabase to work full-time on OrioleDB, with more to come.
<a href="https://boards.greenhouse.io/supabase/jobs/4307456004">Apply here</a> if you&#x27;re interested in working on Postgres full time.</p>
<div class="bg-gray-300 rounded-lg p-6 italic"><p>Note: we are not running OrioleDB on the Supabase platform. Our promise to you is “no vendor-lockin”, and therefore we will never run a fork of Postgres.
In the future, if all of the OrioleDB core changes are up-streamed, then we might offer it on the platform.</p><p>If you want to try out OrioleDB today, you can switch the Postgres docker image to OrioleDB in the <a href="https://github.com/supabase/supabase/blob/ec6085b8f852a903f2f45e715add1377cf89d850/docker/docker-compose.yml#L159">self-hosted setup</a>.</p></div>
<h2 id="join-us" class="group scroll-mt-24">Join us<a href="#join-us" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Achieving the goals we&#x27;ve outlined above will be a long journey requiring collaboration from many companies besides Supabase.</p>
<p>If you want to help build the future of cloud-native Postgres, <a href="https://boards.greenhouse.io/supabase/jobs/4307456004">join us</a> at Supabase.
If you&#x27;re already working towards the same goals, reach out and let&#x27;s do it together.</p>
<h2 id="by-the-way" class="group scroll-mt-24">By the way<a href="#by-the-way" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Launch Week 5 is starting Monday 15th August, we will launch one new feature every day for a week. If you want to follow along check out <a href="../launch-week.html">supabase.com/launch-week</a>.</p>
<ul>
<li><a href="launch-week-5-hackathon.html">Launch Week 5 Hackathon</a></li>
<li><a href="supabase-cli-v1-and-admin-api-beta.html">Day 1 - Supabase CLI v1 and Management API Beta</a></li>
<li><a href="https://www.youtube.com/watch?v=OpPOaJI_Z28&amp;feature=emb_title">Youtube video - Supabase CLI v1 and Management API Beta</a></li>
<li><a href="supabase-js-v2.html">Day 2 - supabase-js v2 Release Candidate</a></li>
<li><a href="https://www.youtube.com/watch?v=iqZlPtl_b-I">Youtube Video - supabase-js v2 Release Candidate</a></li>
<li><a href="supabase-soc2.html">Day 3 - Supabase is SOC2 compliant</a></li>
<li><a href="https://www.youtube.com/watch?v=6bGQotxisoY">Youtube video - Security Day</a></li>
</ul></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-series-b&amp;text=Supabase%20Series%20B"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-series-b&amp;text=Supabase%20Series%20B"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-series-b&amp;t=Supabase%20Series%20B"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="supabase-cli-v1-and-admin-api-beta.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Supabase CLI v1 and Management API Beta</h4><p class="small">15 August 2022</p></div></div></div></div></a></div><div><a href="launch-week-5-hackathon.html"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Launch Week 5 Hackathon</h4><p class="small">10 August 2022</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/supabase"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">supabase</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#where-weve-been">Where we&#x27;ve been</a>
<ul>
<li><a href="#database-growth">Database growth</a></li>
<li><a href="#developer-signups">Developer Signups</a></li>
<li><a href="#community">Community</a></li>
</ul>
</li>
<li><a href="#about-the-round">About the round</a></li>
<li><a href="#giving-back">Giving back</a>
<ul>
<li><a href="#open-collective">Open collective</a></li>
<li><a href="#postgrest">PostgREST</a></li>
<li><a href="#elixir-type-support">Elixir Type support</a></li>
<li><a href="#deno">Deno</a></li>
<li><a href="#database-encryption">Database encryption</a></li>
</ul>
</li>
<li><a href="#where-were-going">Where we&#x27;re going</a></li>
<li><a href="#join-us">Join us</a></li>
<li><a href="#by-the-way">By the way</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-series-b&amp;text=Supabase%20Series%20B"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-series-b&amp;text=Supabase%20Series%20B"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-series-b&amp;t=Supabase%20Series%20B"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"supabase-cli-v1-and-admin-api-beta","title":"Supabase CLI v1 and Management API Beta","description":"We are moving Supabase CLI v1 out of beta, and releasing Management API beta.","author":"soedirgo,qiao","image":"lw5-cli/thumbnail.jpg","thumb":"lw5-cli/thumbnail.jpg","categories":["product"],"tags":["launch-week"],"date":"2022-08-15","toc_depth":3,"video":"https://www.youtube.com/v/OpPOaJI_Z28","formattedDate":"15 August 2022","readingTime":"5 minute read","url":"/blog/supabase-cli-v1-and-admin-api-beta","path":"/blog/supabase-cli-v1-and-admin-api-beta"},"nextPost":{"slug":"launch-week-5-hackathon","title":"Launch Week 5 Hackathon","description":"Build to win $1500 - Friday 12th to Monday 21st August 2022","author":"ant_wilson","image":"lw5-hackathon/thumbnail.jpg","thumb":"lw5-hackathon/thumbnail.jpg","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2022-08-10","toc_depth":3,"video":"https://www.youtube.com/v/rI3Ik7GyYEw","formattedDate":"10 August 2022","readingTime":"5 minute read","url":"/blog/launch-week-5-hackathon","path":"/blog/launch-week-5-hackathon"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-series-b","source":"\nSupabase raised $80M in May, bringing our total funding to $116M. This comes one year after our Series A, and so we're revisiting the plans\nwe outlined in our [Series A blog post](https://supabase.com/blog/supabase-series-a) tk hold ourselves accountable for the promises we made.\n\n## Where we've been\n\nWe shared a few metrics in our Series A post. Since then we've grown a lot.\n\n### Database growth\n\nAt Series A we'd launched over 50,000 PostgreSQL databases (on our hosted platform).\n\n![Series A databases](/images/blog/series-b/databases-2021.png)\n\nAs of today, we've launched over 150,000 PostgreSQL databases.\n\n![Series B databases](/images/blog/series-b/databases-2022.png)\n\n### Developer Signups\n\nAt Series A we had 40,000 developers.\n\n![Series A developers](/images/blog/series-b/developers-2021.png)\n\nToday we have more than 110,000 developers.\n\n![Series B Developers](/images/blog/series-b/developers-2022.png)\n\n### Community\n\nWe've seen a lot of growth in our community:\n\n- Discord: 4000 → 8000\n- Twitter: 18,000 → 37,000\n- GitHub Stars: 19,000 → 36,000\n\n[Supabase vs React](https://star-history.com/#supabase/supabase\u0026facebook/react\u0026Timeline) (GitHub stars)\n\n![Supabase vs React](/images/blog/series-b/supabase_react_github.png)\n\nIt's early days but we're doing well for a database company.\n\n## About the round\n\nOur Series B is led by [Felicis](https://www.felicis.com/), an amazing team of people who support the long-term approach we're taking to build an open source business.\n\n\u003cQuote img=\"aydin.jpeg\" caption=\"Aydin Senkut, Founder and Managing Partner of Felicis.\"\u003e\n  \u003cp\u003e\n    We are super excited to be investing in Supabase and partner with the team on their next phase\n    of growth.\n  \u003c/p\u003e\n  \u003cp\u003e\n    Supabase's team is made up of 15% former founders and 70% developers, and they deeply understand\n    the pain points that developers overcome to rapidly develop products. Supabase truly enables\n    developers to build their applications without repeating the same tedious tasks and manage their\n    application's database, authentication, storage, and edge functions.\n  \u003c/p\u003e\n\u003c/Quote\u003e\n\nJoining the round are Coatue (who led our Series A \u0026 Seed round), [Lightspeed](https://lsvp.com/), and [Square Peg Capital](https://www.squarepegcap.com/).\n\n## Giving back\n\nSupabase is a collaborative company. We aim to support existing open source tools before developing anything ourselves.\nWhen we do develop ourselves, we try to include our community as maintainers in a variety of ways. We've been experimenting\nwith several models to support the open source ecosystem.\n\n### Open collective\n\nTo date, we've [paid over $80,000](https://opencollective.com/supabase#category-BUDGET) to contributors and maintainers through our open collective.\nThis is for everything from maintaining our libraries to moderating our Discord.\n\nWe've had some amazing open source stories like [Isaiah](https://github.com/Isaiah-Hamilton), a 15-year old who started contributing after his school day.\nOr [Olyno](https://github.com/Olyno), who created a Supabase Discord on their own initiative and is now an official moderator.\nOr [Zernonia](https://github.com/zernonia/), who created [madewithsupabase.com](https://www.madewithsupabase.com/) which is now central to every Supabase Hackathon.\n\n### PostgREST\n\n[PostgREST](https://postgrest.org) is a automatically generate RESTful APIs from your Postgres schema, and it's one of the core pieces of the Supabase stack.\nAs well as being a gold sponsor, Supabase hired [Steve](https://github.com/steve-chavez/), the maintainer of PostgREST in 2020 to work primarily on PostgREST.\nThis model has been surprisingly successful and we hope that more companies will consider this approach for supporting open source projects.\n\n### Elixir Type support\n\nElixir is a functional programming language that enables huge horizontal scalability. We use it at Supabase for our Realtime\nengine and our logging infrastructure. Recently [announced by José](https://twitter.com/josevalim/status/1535008937640181760?s=20\u0026t=BLqeO2YpdhYfCZxgsmlNug) at ElixirConf EU,\nthe Elixir team is investigating a type system. Supabase is sponsoring this research and development.\n\n### Deno\n\n[Functions](https://supabase.com/edge-functions) was one of the most demanded features of Supabase, and we explored a huge number of options before settling on Deno.\nDeno checked all the boxes - most important for us was their approach to open source. Both Supabase and Netlify now support Deno Edge Functions,\nand we hope that our backing will help convince others to adopt the Deno runtime too.\n\n### Database encryption\n\nA teaser for next week's [Launch Week](https://supabase.com/launch-week) - a few months ago we hired [Michel](https://github.com/michelp),\nthe maintainer of [pgsodium](https://github.com/michelp/pgsodium), to re-think what a modern database encryption system and\ndatabase secrets management would look like. Since then he has released two major versions of\npgsodium: [2.0](https://www.postgresql.org/about/news/pgsodium-200-modern-cryptography-for-postgresql-2389/)\nand [3.0](https://github.com/michelp/pgsodium/releases/tag/v3.0.0) to\nsupport [Transparent Column Encryption](https://github.com/michelp/pgsodium#transparent-column-encryption).\n\n\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"video-with-border w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/4t_63HT3rZY\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen\n  \u003e\u003c/iframe\u003e\n\u003c/div\u003e\n\n\u003csmall\u003e\n  \u003ci\u003e\n    Founder Chat: Paul Copplestone and Ant Wilson discuss the several ways we give back to the\n    community, and what Open Source means for Supabase.\n  \u003c/i\u003e\n\u003c/small\u003e\n\n## Where we're going\n\nIn our Series A post, we outlined our plans for Supabase [in three phases](https://supabase.com/blog/supabase-series-a#building-supabase):\n\n- Phase 1: [Start with scalability](https://supabase.com/blog/supabase-series-a#phase-1-start-with-scalability)\n- Phase 2: [Postgres Tooling](https://supabase.com/blog/supabase-series-a#phase-2-postgres-tooling)\n- Phase 3: [Cloud-native PostgreSQL](https://supabase.com/blog/supabase-series-a#phase-3-cloud-native-postgresql)\n\nWe're now moving into Phase 3, with the same goals that we outlined in 2021:\n\n- **Branching:** \u003cbr /\u003e\n  Developers should be able to \"fork\" a database at any point. This is particularly important for Jamstack developers who run deployments for every Git branch.\n- **Scalable storage:** \u003cbr /\u003e\n  Storage should grow and shrink without the user needing to provision more space themselves.\n- **Distributed:** \u003cbr /\u003e\n  An entire fleet of databases, distributed around the world, should feel like a single database (and even better if all nodes can accept read/write workloads).\n- **Ephemeral compute:** \u003cbr /\u003e\n  Developers don't want to be charged for a database which isn't doing anything. Likewise - if they are running huge computation then the database should scale up seamlessly, and scale down to zero when it's unused.\n- **Snapshots and time-travel:** \u003cbr /\u003e\n  Developers should be able to roll back to any point in time, or take a copy of their database with just a click of a button.\n\nWe have a lot of work ahead of us, but we're excited to announce one of the ways that we're supporting the community is through our investment into OrioleDB.\n\nOrioleDB is a [PostgreSQL extension](https://github.com/OrioleDB/OrioleDB) which solves a lot\nof [“wicked” problems](https://www.socallinuxexpo.org/sites/default/files/presentations/solving-postgres-wicked-problems.pdf) with Postgres.\n\nWhile it _currently_ requires some modifications to the Postgres core (about 1000 lines), the goal is to upstream those changes so\nthat _anyone_ can build a Storage extension like OrioleDB. Giving developers low-level access to the underlying storage capabilities of\nPostgres will unlock a slew of new capabilities in Postgres.\nYou can read more about OrioleDB's plans for Snapshots and Branching [in their wiki](https://github.com/OrioleDB/OrioleDB/wiki/Database-branching).\n\nSupabase has invested $500K into OrioleDB to support their efforts, and we've hired a developer at Supabase to work full-time on OrioleDB, with more to come.\n[Apply here](https://boards.greenhouse.io/supabase/jobs/4307456004) if you're interested in working on Postgres full time.\n\n\u003cdiv className=\"bg-gray-300 rounded-lg p-6 italic\"\u003e\n Note: we are not running OrioleDB on the Supabase platform. Our promise to you is “no vendor-lockin”, and therefore we will never run a fork of Postgres.\n In the future, if all of the OrioleDB core changes are up-streamed, then we might offer it on the platform.\n\nIf you want to try out OrioleDB today, you can switch the Postgres docker image to OrioleDB in the [self-hosted setup](https://github.com/supabase/supabase/blob/ec6085b8f852a903f2f45e715add1377cf89d850/docker/docker-compose.yml#L159).\n\n\u003c/div\u003e\n\n## Join us\n\nAchieving the goals we've outlined above will be a long journey requiring collaboration from many companies besides Supabase.\n\nIf you want to help build the future of cloud-native Postgres, [join us](https://boards.greenhouse.io/supabase/jobs/4307456004) at Supabase.\nIf you're already working towards the same goals, reach out and let's do it together.\n\n## By the way\n\nLaunch Week 5 is starting Monday 15th August, we will launch one new feature every day for a week. If you want to follow along check out [supabase.com/launch-week](https://supabase.com/launch-week).\n\n- [Launch Week 5 Hackathon](https://supabase.com/blog/launch-week-5-hackathon)\n- [Day 1 - Supabase CLI v1 and Management API Beta](https://supabase.com/blog/supabase-cli-v1-and-admin-api-beta)\n- [Youtube video - Supabase CLI v1 and Management API Beta](https://www.youtube.com/watch?v=OpPOaJI_Z28\u0026feature=emb_title)\n- [Day 2 - supabase-js v2 Release Candidate](https://supabase.com/blog/supabase-js-v2)\n- [Youtube Video - supabase-js v2 Release Candidate](https://www.youtube.com/watch?v=iqZlPtl_b-I)\n- [Day 3 - Supabase is SOC2 compliant](https://supabase.com/blog/supabase-soc2)\n- [Youtube video - Security Day](https://www.youtube.com/watch?v=6bGQotxisoY)\n","title":"Supabase Series B","description":"Supabase raised $80M in May, bringing our total funding to $116M.","author":"paul_copplestone","image":"series-b/supabase-series-b.png","thumb":"series-b/supabase-series-b.png","categories":["company"],"tags":["supabase"],"date":"2022-08-12","toc_depth":3,"video":"https://www.youtube.com/v/4t_63HT3rZY","content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    a: \"a\",\n    h2: \"h2\",\n    h3: \"h3\",\n    img: \"img\",\n    ul: \"ul\",\n    li: \"li\",\n    strong: \"strong\",\n    em: \"em\"\n  }, _provideComponents(), props.components), {Quote} = _components;\n  if (!Quote) _missingMdxReference(\"Quote\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsxs(_components.p, {\n      children: [\"Supabase raised $80M in May, bringing our total funding to $116M. This comes one year after our Series A, and so we're revisiting the plans\\nwe outlined in our \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/supabase-series-a\",\n        children: \"Series A blog post\"\n      }), \" tk hold ourselves accountable for the promises we made.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"where-weve-been\",\n      children: \"Where we've been\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We shared a few metrics in our Series A post. Since then we've grown a lot.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"database-growth\",\n      children: \"Database growth\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"At Series A we'd launched over 50,000 PostgreSQL databases (on our hosted platform).\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/series-b/databases-2021.png\",\n        alt: \"Series A databases\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"As of today, we've launched over 150,000 PostgreSQL databases.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/series-b/databases-2022.png\",\n        alt: \"Series B databases\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"developer-signups\",\n      children: \"Developer Signups\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"At Series A we had 40,000 developers.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/series-b/developers-2021.png\",\n        alt: \"Series A developers\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Today we have more than 110,000 developers.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/series-b/developers-2022.png\",\n        alt: \"Series B Developers\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"community\",\n      children: \"Community\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We've seen a lot of growth in our community:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Discord: 4000 → 8000\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Twitter: 18,000 → 37,000\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"GitHub Stars: 19,000 → 36,000\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://star-history.com/#supabase/supabase\u0026facebook/react\u0026Timeline\",\n        children: \"Supabase vs React\"\n      }), \" (GitHub stars)\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/series-b/supabase_react_github.png\",\n        alt: \"Supabase vs React\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"It's early days but we're doing well for a database company.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"about-the-round\",\n      children: \"About the round\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Our Series B is led by \", _jsx(_components.a, {\n        href: \"https://www.felicis.com/\",\n        children: \"Felicis\"\n      }), \", an amazing team of people who support the long-term approach we're taking to build an open source business.\"]\n    }), \"\\n\", _jsxs(Quote, {\n      img: \"aydin.jpeg\",\n      caption: \"Aydin Senkut, Founder and Managing Partner of Felicis.\",\n      children: [_jsx(\"p\", {\n        children: _jsx(_components.p, {\n          children: \"We are super excited to be investing in Supabase and partner with the team on their next phase\\nof growth.\"\n        })\n      }), _jsx(\"p\", {\n        children: _jsx(_components.p, {\n          children: \"Supabase's team is made up of 15% former founders and 70% developers, and they deeply understand\\nthe pain points that developers overcome to rapidly develop products. Supabase truly enables\\ndevelopers to build their applications without repeating the same tedious tasks and manage their\\napplication's database, authentication, storage, and edge functions.\"\n        })\n      })]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Joining the round are Coatue (who led our Series A \u0026 Seed round), \", _jsx(_components.a, {\n        href: \"https://lsvp.com/\",\n        children: \"Lightspeed\"\n      }), \", and \", _jsx(_components.a, {\n        href: \"https://www.squarepegcap.com/\",\n        children: \"Square Peg Capital\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"giving-back\",\n      children: \"Giving back\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Supabase is a collaborative company. We aim to support existing open source tools before developing anything ourselves.\\nWhen we do develop ourselves, we try to include our community as maintainers in a variety of ways. We've been experimenting\\nwith several models to support the open source ecosystem.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"open-collective\",\n      children: \"Open collective\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To date, we've \", _jsx(_components.a, {\n        href: \"https://opencollective.com/supabase#category-BUDGET\",\n        children: \"paid over $80,000\"\n      }), \" to contributors and maintainers through our open collective.\\nThis is for everything from maintaining our libraries to moderating our Discord.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We've had some amazing open source stories like \", _jsx(_components.a, {\n        href: \"https://github.com/Isaiah-Hamilton\",\n        children: \"Isaiah\"\n      }), \", a 15-year old who started contributing after his school day.\\nOr \", _jsx(_components.a, {\n        href: \"https://github.com/Olyno\",\n        children: \"Olyno\"\n      }), \", who created a Supabase Discord on their own initiative and is now an official moderator.\\nOr \", _jsx(_components.a, {\n        href: \"https://github.com/zernonia/\",\n        children: \"Zernonia\"\n      }), \", who created \", _jsx(_components.a, {\n        href: \"https://www.madewithsupabase.com/\",\n        children: \"madewithsupabase.com\"\n      }), \" which is now central to every Supabase Hackathon.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"postgrest\",\n      children: \"PostgREST\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://postgrest.org\",\n        children: \"PostgREST\"\n      }), \" is a automatically generate RESTful APIs from your Postgres schema, and it's one of the core pieces of the Supabase stack.\\nAs well as being a gold sponsor, Supabase hired \", _jsx(_components.a, {\n        href: \"https://github.com/steve-chavez/\",\n        children: \"Steve\"\n      }), \", the maintainer of PostgREST in 2020 to work primarily on PostgREST.\\nThis model has been surprisingly successful and we hope that more companies will consider this approach for supporting open source projects.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"elixir-type-support\",\n      children: \"Elixir Type support\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Elixir is a functional programming language that enables huge horizontal scalability. We use it at Supabase for our Realtime\\nengine and our logging infrastructure. Recently \", _jsx(_components.a, {\n        href: \"https://twitter.com/josevalim/status/1535008937640181760?s=20\u0026t=BLqeO2YpdhYfCZxgsmlNug\",\n        children: \"announced by José\"\n      }), \" at ElixirConf EU,\\nthe Elixir team is investigating a type system. Supabase is sponsoring this research and development.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"deno\",\n      children: \"Deno\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://supabase.com/edge-functions\",\n        children: \"Functions\"\n      }), \" was one of the most demanded features of Supabase, and we explored a huge number of options before settling on Deno.\\nDeno checked all the boxes - most important for us was their approach to open source. Both Supabase and Netlify now support Deno Edge Functions,\\nand we hope that our backing will help convince others to adopt the Deno runtime too.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"database-encryption\",\n      children: \"Database encryption\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"A teaser for next week's \", _jsx(_components.a, {\n        href: \"https://supabase.com/launch-week\",\n        children: \"Launch Week\"\n      }), \" - a few months ago we hired \", _jsx(_components.a, {\n        href: \"https://github.com/michelp\",\n        children: \"Michel\"\n      }), \",\\nthe maintainer of \", _jsx(_components.a, {\n        href: \"https://github.com/michelp/pgsodium\",\n        children: \"pgsodium\"\n      }), \", to re-think what a modern database encryption system and\\ndatabase secrets management would look like. Since then he has released two major versions of\\npgsodium: \", _jsx(_components.a, {\n        href: \"https://www.postgresql.org/about/news/pgsodium-200-modern-cryptography-for-postgresql-2389/\",\n        children: \"2.0\"\n      }), \"\\nand \", _jsx(_components.a, {\n        href: \"https://github.com/michelp/pgsodium/releases/tag/v3.0.0\",\n        children: \"3.0\"\n      }), \" to\\nsupport \", _jsx(_components.a, {\n        href: \"https://github.com/michelp/pgsodium#transparent-column-encryption\",\n        children: \"Transparent Column Encryption\"\n      }), \".\"]\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"video-container\",\n      children: _jsx(\"iframe\", {\n        className: \"video-with-border w-full\",\n        src: \"https://www.youtube-nocookie.com/embed/4t_63HT3rZY\",\n        frameBorder: \"1\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n        allowFullScreen: true\n      })\n    }), \"\\n\", _jsx(\"small\", {\n      children: _jsx(\"i\", {\n        children: _jsx(_components.p, {\n          children: \"Founder Chat: Paul Copplestone and Ant Wilson discuss the several ways we give back to the\\ncommunity, and what Open Source means for Supabase.\"\n        })\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"where-were-going\",\n      children: \"Where we're going\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In our Series A post, we outlined our plans for Supabase \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/supabase-series-a#building-supabase\",\n        children: \"in three phases\"\n      }), \":\"]\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Phase 1: \", _jsx(_components.a, {\n          href: \"https://supabase.com/blog/supabase-series-a#phase-1-start-with-scalability\",\n          children: \"Start with scalability\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Phase 2: \", _jsx(_components.a, {\n          href: \"https://supabase.com/blog/supabase-series-a#phase-2-postgres-tooling\",\n          children: \"Postgres Tooling\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Phase 3: \", _jsx(_components.a, {\n          href: \"https://supabase.com/blog/supabase-series-a#phase-3-cloud-native-postgresql\",\n          children: \"Cloud-native PostgreSQL\"\n        })]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We're now moving into Phase 3, with the same goals that we outlined in 2021:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Branching:\"\n        }), \" \", _jsx(\"br\", {}), \"\\nDevelopers should be able to \\\"fork\\\" a database at any point. This is particularly important for Jamstack developers who run deployments for every Git branch.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Scalable storage:\"\n        }), \" \", _jsx(\"br\", {}), \"\\nStorage should grow and shrink without the user needing to provision more space themselves.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Distributed:\"\n        }), \" \", _jsx(\"br\", {}), \"\\nAn entire fleet of databases, distributed around the world, should feel like a single database (and even better if all nodes can accept read/write workloads).\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Ephemeral compute:\"\n        }), \" \", _jsx(\"br\", {}), \"\\nDevelopers don't want to be charged for a database which isn't doing anything. Likewise - if they are running huge computation then the database should scale up seamlessly, and scale down to zero when it's unused.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Snapshots and time-travel:\"\n        }), \" \", _jsx(\"br\", {}), \"\\nDevelopers should be able to roll back to any point in time, or take a copy of their database with just a click of a button.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We have a lot of work ahead of us, but we're excited to announce one of the ways that we're supporting the community is through our investment into OrioleDB.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"OrioleDB is a \", _jsx(_components.a, {\n        href: \"https://github.com/OrioleDB/OrioleDB\",\n        children: \"PostgreSQL extension\"\n      }), \" which solves a lot\\nof \", _jsx(_components.a, {\n        href: \"https://www.socallinuxexpo.org/sites/default/files/presentations/solving-postgres-wicked-problems.pdf\",\n        children: \"“wicked” problems\"\n      }), \" with Postgres.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"While it \", _jsx(_components.em, {\n        children: \"currently\"\n      }), \" requires some modifications to the Postgres core (about 1000 lines), the goal is to upstream those changes so\\nthat \", _jsx(_components.em, {\n        children: \"anyone\"\n      }), \" can build a Storage extension like OrioleDB. Giving developers low-level access to the underlying storage capabilities of\\nPostgres will unlock a slew of new capabilities in Postgres.\\nYou can read more about OrioleDB's plans for Snapshots and Branching \", _jsx(_components.a, {\n        href: \"https://github.com/OrioleDB/OrioleDB/wiki/Database-branching\",\n        children: \"in their wiki\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Supabase has invested $500K into OrioleDB to support their efforts, and we've hired a developer at Supabase to work full-time on OrioleDB, with more to come.\\n\", _jsx(_components.a, {\n        href: \"https://boards.greenhouse.io/supabase/jobs/4307456004\",\n        children: \"Apply here\"\n      }), \" if you're interested in working on Postgres full time.\"]\n    }), \"\\n\", _jsxs(\"div\", {\n      className: \"bg-gray-300 rounded-lg p-6 italic\",\n      children: [_jsx(_components.p, {\n        children: \"Note: we are not running OrioleDB on the Supabase platform. Our promise to you is “no vendor-lockin”, and therefore we will never run a fork of Postgres.\\nIn the future, if all of the OrioleDB core changes are up-streamed, then we might offer it on the platform.\"\n      }), _jsxs(_components.p, {\n        children: [\"If you want to try out OrioleDB today, you can switch the Postgres docker image to OrioleDB in the \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase/blob/ec6085b8f852a903f2f45e715add1377cf89d850/docker/docker-compose.yml#L159\",\n          children: \"self-hosted setup\"\n        }), \".\"]\n      })]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"join-us\",\n      children: \"Join us\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Achieving the goals we've outlined above will be a long journey requiring collaboration from many companies besides Supabase.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you want to help build the future of cloud-native Postgres, \", _jsx(_components.a, {\n        href: \"https://boards.greenhouse.io/supabase/jobs/4307456004\",\n        children: \"join us\"\n      }), \" at Supabase.\\nIf you're already working towards the same goals, reach out and let's do it together.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"by-the-way\",\n      children: \"By the way\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Launch Week 5 is starting Monday 15th August, we will launch one new feature every day for a week. If you want to follow along check out \", _jsx(_components.a, {\n        href: \"https://supabase.com/launch-week\",\n        children: \"supabase.com/launch-week\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/blog/launch-week-5-hackathon\",\n          children: \"Launch Week 5 Hackathon\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/blog/supabase-cli-v1-and-admin-api-beta\",\n          children: \"Day 1 - Supabase CLI v1 and Management API Beta\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=OpPOaJI_Z28\u0026feature=emb_title\",\n          children: \"Youtube video - Supabase CLI v1 and Management API Beta\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/blog/supabase-js-v2\",\n          children: \"Day 2 - supabase-js v2 Release Candidate\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=iqZlPtl_b-I\",\n          children: \"Youtube Video - supabase-js v2 Release Candidate\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/blog/supabase-soc2\",\n          children: \"Day 3 - Supabase is SOC2 compliant\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=6bGQotxisoY\",\n          children: \"Youtube video - Security Day\"\n        })\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"Where we've been","slug":"where-weve-been","lvl":2,"i":0,"seen":0},{"content":"Database growth","slug":"database-growth","lvl":3,"i":1,"seen":0},{"content":"Developer Signups","slug":"developer-signups","lvl":3,"i":2,"seen":0},{"content":"Community","slug":"community","lvl":3,"i":3,"seen":0},{"content":"About the round","slug":"about-the-round","lvl":2,"i":4,"seen":0},{"content":"Giving back","slug":"giving-back","lvl":2,"i":5,"seen":0},{"content":"Open collective","slug":"open-collective","lvl":3,"i":6,"seen":0},{"content":"PostgREST","slug":"postgrest","lvl":3,"i":7,"seen":0},{"content":"Elixir Type support","slug":"elixir-type-support","lvl":3,"i":8,"seen":0},{"content":"Deno","slug":"deno","lvl":3,"i":9,"seen":0},{"content":"Database encryption","slug":"database-encryption","lvl":3,"i":10,"seen":0},{"content":"Where we're going","slug":"where-were-going","lvl":2,"i":11,"seen":0},{"content":"Join us","slug":"join-us","lvl":2,"i":12,"seen":0},{"content":"By the way","slug":"by-the-way","lvl":2,"i":13,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,3],"level":0},{"type":"inline","content":"Supabase raised $80M in May, bringing our total funding to $116M. This comes one year after our Series A, and so we're revisiting the plans\nwe outlined in our [Series A blog post](https://supabase.com/blog/supabase-series-a) tk hold ourselves accountable for the promises we made.","level":1,"lines":[1,3],"children":[{"type":"text","content":"Supabase raised $80M in May, bringing our total funding to $116M. This comes one year after our Series A, and so we're revisiting the plans","level":0},{"type":"softbreak","level":0},{"type":"text","content":"we outlined in our ","level":0},{"type":"link_open","href":"https://supabase.com/blog/supabase-series-a","title":"","level":0},{"type":"text","content":"Series A blog post","level":1},{"type":"link_close","level":0},{"type":"text","content":" tk hold ourselves accountable for the promises we made.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[4,5],"level":0},{"type":"inline","content":"[Where we've been](#where-weve-been)","level":1,"lines":[4,5],"children":[{"type":"text","content":"Where we've been","level":0}],"lvl":2,"i":0,"seen":0,"slug":"where-weve-been"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[6,7],"level":0},{"type":"inline","content":"We shared a few metrics in our Series A post. Since then we've grown a lot.","level":1,"lines":[6,7],"children":[{"type":"text","content":"We shared a few metrics in our Series A post. Since then we've grown a lot.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[8,9],"level":0},{"type":"inline","content":"[Database growth](#database-growth)","level":1,"lines":[8,9],"children":[{"type":"text","content":"Database growth","level":0}],"lvl":3,"i":1,"seen":0,"slug":"database-growth"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[10,11],"level":0},{"type":"inline","content":"At Series A we'd launched over 50,000 PostgreSQL databases (on our hosted platform).","level":1,"lines":[10,11],"children":[{"type":"text","content":"At Series A we'd launched over 50,000 PostgreSQL databases (on our hosted platform).","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[12,13],"level":0},{"type":"inline","content":"![Series A databases](/images/blog/series-b/databases-2021.png)","level":1,"lines":[12,13],"children":[{"type":"image","src":"/images/blog/series-b/databases-2021.png","title":"","alt":"Series A databases","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[14,15],"level":0},{"type":"inline","content":"As of today, we've launched over 150,000 PostgreSQL databases.","level":1,"lines":[14,15],"children":[{"type":"text","content":"As of today, we've launched over 150,000 PostgreSQL databases.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[16,17],"level":0},{"type":"inline","content":"![Series B databases](/images/blog/series-b/databases-2022.png)","level":1,"lines":[16,17],"children":[{"type":"image","src":"/images/blog/series-b/databases-2022.png","title":"","alt":"Series B databases","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[18,19],"level":0},{"type":"inline","content":"[Developer Signups](#developer-signups)","level":1,"lines":[18,19],"children":[{"type":"text","content":"Developer Signups","level":0}],"lvl":3,"i":2,"seen":0,"slug":"developer-signups"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[20,21],"level":0},{"type":"inline","content":"At Series A we had 40,000 developers.","level":1,"lines":[20,21],"children":[{"type":"text","content":"At Series A we had 40,000 developers.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[22,23],"level":0},{"type":"inline","content":"![Series A developers](/images/blog/series-b/developers-2021.png)","level":1,"lines":[22,23],"children":[{"type":"image","src":"/images/blog/series-b/developers-2021.png","title":"","alt":"Series A developers","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[24,25],"level":0},{"type":"inline","content":"Today we have more than 110,000 developers.","level":1,"lines":[24,25],"children":[{"type":"text","content":"Today we have more than 110,000 developers.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[26,27],"level":0},{"type":"inline","content":"![Series B Developers](/images/blog/series-b/developers-2022.png)","level":1,"lines":[26,27],"children":[{"type":"image","src":"/images/blog/series-b/developers-2022.png","title":"","alt":"Series B Developers","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[28,29],"level":0},{"type":"inline","content":"[Community](#community)","level":1,"lines":[28,29],"children":[{"type":"text","content":"Community","level":0}],"lvl":3,"i":3,"seen":0,"slug":"community"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[30,31],"level":0},{"type":"inline","content":"We've seen a lot of growth in our community:","level":1,"lines":[30,31],"children":[{"type":"text","content":"We've seen a lot of growth in our community:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[32,36],"level":0},{"type":"list_item_open","lines":[32,33],"level":1},{"type":"paragraph_open","tight":true,"lines":[32,33],"level":2},{"type":"inline","content":"Discord: 4000 → 8000","level":3,"lines":[32,33],"children":[{"type":"text","content":"Discord: 4000 → 8000","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[33,34],"level":1},{"type":"paragraph_open","tight":true,"lines":[33,34],"level":2},{"type":"inline","content":"Twitter: 18,000 → 37,000","level":3,"lines":[33,34],"children":[{"type":"text","content":"Twitter: 18,000 → 37,000","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[34,36],"level":1},{"type":"paragraph_open","tight":true,"lines":[34,35],"level":2},{"type":"inline","content":"GitHub Stars: 19,000 → 36,000","level":3,"lines":[34,35],"children":[{"type":"text","content":"GitHub Stars: 19,000 → 36,000","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[36,37],"level":0},{"type":"inline","content":"[Supabase vs React](https://star-history.com/#supabase/supabase\u0026facebook/react\u0026Timeline) (GitHub stars)","level":1,"lines":[36,37],"children":[{"type":"link_open","href":"https://star-history.com/#supabase/supabase\u0026facebook/react\u0026Timeline","title":"","level":0},{"type":"text","content":"Supabase vs React","level":1},{"type":"link_close","level":0},{"type":"text","content":" (GitHub stars)","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[38,39],"level":0},{"type":"inline","content":"![Supabase vs React](/images/blog/series-b/supabase_react_github.png)","level":1,"lines":[38,39],"children":[{"type":"image","src":"/images/blog/series-b/supabase_react_github.png","title":"","alt":"Supabase vs React","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[40,41],"level":0},{"type":"inline","content":"It's early days but we're doing well for a database company.","level":1,"lines":[40,41],"children":[{"type":"text","content":"It's early days but we're doing well for a database company.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[42,43],"level":0},{"type":"inline","content":"[About the round](#about-the-round)","level":1,"lines":[42,43],"children":[{"type":"text","content":"About the round","level":0}],"lvl":2,"i":4,"seen":0,"slug":"about-the-round"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[44,45],"level":0},{"type":"inline","content":"Our Series B is led by [Felicis](https://www.felicis.com/), an amazing team of people who support the long-term approach we're taking to build an open source business.","level":1,"lines":[44,45],"children":[{"type":"text","content":"Our Series B is led by ","level":0},{"type":"link_open","href":"https://www.felicis.com/","title":"","level":0},{"type":"text","content":"Felicis","level":1},{"type":"link_close","level":0},{"type":"text","content":", an amazing team of people who support the long-term approach we're taking to build an open source business.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[46,58],"level":0},{"type":"inline","content":"\u003cQuote img=\"aydin.jpeg\" caption=\"Aydin Senkut, Founder and Managing Partner of Felicis.\"\u003e\n  \u003cp\u003e\n    We are super excited to be investing in Supabase and partner with the team on their next phase\n    of growth.\n  \u003c/p\u003e\n  \u003cp\u003e\n    Supabase's team is made up of 15% former founders and 70% developers, and they deeply understand\n    the pain points that developers overcome to rapidly develop products. Supabase truly enables\n    developers to build their applications without repeating the same tedious tasks and manage their\n    application's database, authentication, storage, and edge functions.\n  \u003c/p\u003e\n\u003c/Quote\u003e","level":1,"lines":[46,58],"children":[{"type":"text","content":"\u003cQuote img=\"aydin.jpeg\" caption=\"Aydin Senkut, Founder and Managing Partner of Felicis.\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cp\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"We are super excited to be investing in Supabase and partner with the team on their next phase","level":0},{"type":"softbreak","level":0},{"type":"text","content":"of growth.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/p\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cp\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Supabase's team is made up of 15% former founders and 70% developers, and they deeply understand","level":0},{"type":"softbreak","level":0},{"type":"text","content":"the pain points that developers overcome to rapidly develop products. Supabase truly enables","level":0},{"type":"softbreak","level":0},{"type":"text","content":"developers to build their applications without repeating the same tedious tasks and manage their","level":0},{"type":"softbreak","level":0},{"type":"text","content":"application's database, authentication, storage, and edge functions.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/p\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/Quote\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[59,60],"level":0},{"type":"inline","content":"Joining the round are Coatue (who led our Series A \u0026 Seed round), [Lightspeed](https://lsvp.com/), and [Square Peg Capital](https://www.squarepegcap.com/).","level":1,"lines":[59,60],"children":[{"type":"text","content":"Joining the round are Coatue (who led our Series A \u0026 Seed round), ","level":0},{"type":"link_open","href":"https://lsvp.com/","title":"","level":0},{"type":"text","content":"Lightspeed","level":1},{"type":"link_close","level":0},{"type":"text","content":", and ","level":0},{"type":"link_open","href":"https://www.squarepegcap.com/","title":"","level":0},{"type":"text","content":"Square Peg Capital","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[61,62],"level":0},{"type":"inline","content":"[Giving back](#giving-back)","level":1,"lines":[61,62],"children":[{"type":"text","content":"Giving back","level":0}],"lvl":2,"i":5,"seen":0,"slug":"giving-back"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[63,66],"level":0},{"type":"inline","content":"Supabase is a collaborative company. We aim to support existing open source tools before developing anything ourselves.\nWhen we do develop ourselves, we try to include our community as maintainers in a variety of ways. We've been experimenting\nwith several models to support the open source ecosystem.","level":1,"lines":[63,66],"children":[{"type":"text","content":"Supabase is a collaborative company. We aim to support existing open source tools before developing anything ourselves.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"When we do develop ourselves, we try to include our community as maintainers in a variety of ways. We've been experimenting","level":0},{"type":"softbreak","level":0},{"type":"text","content":"with several models to support the open source ecosystem.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[67,68],"level":0},{"type":"inline","content":"[Open collective](#open-collective)","level":1,"lines":[67,68],"children":[{"type":"text","content":"Open collective","level":0}],"lvl":3,"i":6,"seen":0,"slug":"open-collective"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[69,71],"level":0},{"type":"inline","content":"To date, we've [paid over $80,000](https://opencollective.com/supabase#category-BUDGET) to contributors and maintainers through our open collective.\nThis is for everything from maintaining our libraries to moderating our Discord.","level":1,"lines":[69,71],"children":[{"type":"text","content":"To date, we've ","level":0},{"type":"link_open","href":"https://opencollective.com/supabase#category-BUDGET","title":"","level":0},{"type":"text","content":"paid over $80,000","level":1},{"type":"link_close","level":0},{"type":"text","content":" to contributors and maintainers through our open collective.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"This is for everything from maintaining our libraries to moderating our Discord.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[72,75],"level":0},{"type":"inline","content":"We've had some amazing open source stories like [Isaiah](https://github.com/Isaiah-Hamilton), a 15-year old who started contributing after his school day.\nOr [Olyno](https://github.com/Olyno), who created a Supabase Discord on their own initiative and is now an official moderator.\nOr [Zernonia](https://github.com/zernonia/), who created [madewithsupabase.com](https://www.madewithsupabase.com/) which is now central to every Supabase Hackathon.","level":1,"lines":[72,75],"children":[{"type":"text","content":"We've had some amazing open source stories like ","level":0},{"type":"link_open","href":"https://github.com/Isaiah-Hamilton","title":"","level":0},{"type":"text","content":"Isaiah","level":1},{"type":"link_close","level":0},{"type":"text","content":", a 15-year old who started contributing after his school day.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Or ","level":0},{"type":"link_open","href":"https://github.com/Olyno","title":"","level":0},{"type":"text","content":"Olyno","level":1},{"type":"link_close","level":0},{"type":"text","content":", who created a Supabase Discord on their own initiative and is now an official moderator.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Or ","level":0},{"type":"link_open","href":"https://github.com/zernonia/","title":"","level":0},{"type":"text","content":"Zernonia","level":1},{"type":"link_close","level":0},{"type":"text","content":", who created ","level":0},{"type":"link_open","href":"https://www.madewithsupabase.com/","title":"","level":0},{"type":"text","content":"madewithsupabase.com","level":1},{"type":"link_close","level":0},{"type":"text","content":" which is now central to every Supabase Hackathon.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[76,77],"level":0},{"type":"inline","content":"[PostgREST](#postgrest)","level":1,"lines":[76,77],"children":[{"type":"text","content":"PostgREST","level":0}],"lvl":3,"i":7,"seen":0,"slug":"postgrest"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[78,81],"level":0},{"type":"inline","content":"[PostgREST](https://postgrest.org) is a automatically generate RESTful APIs from your Postgres schema, and it's one of the core pieces of the Supabase stack.\nAs well as being a gold sponsor, Supabase hired [Steve](https://github.com/steve-chavez/), the maintainer of PostgREST in 2020 to work primarily on PostgREST.\nThis model has been surprisingly successful and we hope that more companies will consider this approach for supporting open source projects.","level":1,"lines":[78,81],"children":[{"type":"link_open","href":"https://postgrest.org","title":"","level":0},{"type":"text","content":"PostgREST","level":1},{"type":"link_close","level":0},{"type":"text","content":" is a automatically generate RESTful APIs from your Postgres schema, and it's one of the core pieces of the Supabase stack.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"As well as being a gold sponsor, Supabase hired ","level":0},{"type":"link_open","href":"https://github.com/steve-chavez/","title":"","level":0},{"type":"text","content":"Steve","level":1},{"type":"link_close","level":0},{"type":"text","content":", the maintainer of PostgREST in 2020 to work primarily on PostgREST.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"This model has been surprisingly successful and we hope that more companies will consider this approach for supporting open source projects.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[82,83],"level":0},{"type":"inline","content":"[Elixir Type support](#elixir-type-support)","level":1,"lines":[82,83],"children":[{"type":"text","content":"Elixir Type support","level":0}],"lvl":3,"i":8,"seen":0,"slug":"elixir-type-support"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[84,87],"level":0},{"type":"inline","content":"Elixir is a functional programming language that enables huge horizontal scalability. We use it at Supabase for our Realtime\nengine and our logging infrastructure. Recently [announced by José](https://twitter.com/josevalim/status/1535008937640181760?s=20\u0026t=BLqeO2YpdhYfCZxgsmlNug) at ElixirConf EU,\nthe Elixir team is investigating a type system. Supabase is sponsoring this research and development.","level":1,"lines":[84,87],"children":[{"type":"text","content":"Elixir is a functional programming language that enables huge horizontal scalability. We use it at Supabase for our Realtime","level":0},{"type":"softbreak","level":0},{"type":"text","content":"engine and our logging infrastructure. Recently ","level":0},{"type":"link_open","href":"https://twitter.com/josevalim/status/1535008937640181760?s=20\u0026t=BLqeO2YpdhYfCZxgsmlNug","title":"","level":0},{"type":"text","content":"announced by José","level":1},{"type":"link_close","level":0},{"type":"text","content":" at ElixirConf EU,","level":0},{"type":"softbreak","level":0},{"type":"text","content":"the Elixir team is investigating a type system. Supabase is sponsoring this research and development.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[88,89],"level":0},{"type":"inline","content":"[Deno](#deno)","level":1,"lines":[88,89],"children":[{"type":"text","content":"Deno","level":0}],"lvl":3,"i":9,"seen":0,"slug":"deno"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[90,93],"level":0},{"type":"inline","content":"[Functions](https://supabase.com/edge-functions) was one of the most demanded features of Supabase, and we explored a huge number of options before settling on Deno.\nDeno checked all the boxes - most important for us was their approach to open source. Both Supabase and Netlify now support Deno Edge Functions,\nand we hope that our backing will help convince others to adopt the Deno runtime too.","level":1,"lines":[90,93],"children":[{"type":"link_open","href":"https://supabase.com/edge-functions","title":"","level":0},{"type":"text","content":"Functions","level":1},{"type":"link_close","level":0},{"type":"text","content":" was one of the most demanded features of Supabase, and we explored a huge number of options before settling on Deno.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Deno checked all the boxes - most important for us was their approach to open source. Both Supabase and Netlify now support Deno Edge Functions,","level":0},{"type":"softbreak","level":0},{"type":"text","content":"and we hope that our backing will help convince others to adopt the Deno runtime too.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[94,95],"level":0},{"type":"inline","content":"[Database encryption](#database-encryption)","level":1,"lines":[94,95],"children":[{"type":"text","content":"Database encryption","level":0}],"lvl":3,"i":10,"seen":0,"slug":"database-encryption"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[96,102],"level":0},{"type":"inline","content":"A teaser for next week's [Launch Week](https://supabase.com/launch-week) - a few months ago we hired [Michel](https://github.com/michelp),\nthe maintainer of [pgsodium](https://github.com/michelp/pgsodium), to re-think what a modern database encryption system and\ndatabase secrets management would look like. Since then he has released two major versions of\npgsodium: [2.0](https://www.postgresql.org/about/news/pgsodium-200-modern-cryptography-for-postgresql-2389/)\nand [3.0](https://github.com/michelp/pgsodium/releases/tag/v3.0.0) to\nsupport [Transparent Column Encryption](https://github.com/michelp/pgsodium#transparent-column-encryption).","level":1,"lines":[96,102],"children":[{"type":"text","content":"A teaser for next week's ","level":0},{"type":"link_open","href":"https://supabase.com/launch-week","title":"","level":0},{"type":"text","content":"Launch Week","level":1},{"type":"link_close","level":0},{"type":"text","content":" - a few months ago we hired ","level":0},{"type":"link_open","href":"https://github.com/michelp","title":"","level":0},{"type":"text","content":"Michel","level":1},{"type":"link_close","level":0},{"type":"text","content":",","level":0},{"type":"softbreak","level":0},{"type":"text","content":"the maintainer of ","level":0},{"type":"link_open","href":"https://github.com/michelp/pgsodium","title":"","level":0},{"type":"text","content":"pgsodium","level":1},{"type":"link_close","level":0},{"type":"text","content":", to re-think what a modern database encryption system and","level":0},{"type":"softbreak","level":0},{"type":"text","content":"database secrets management would look like. Since then he has released two major versions of","level":0},{"type":"softbreak","level":0},{"type":"text","content":"pgsodium: ","level":0},{"type":"link_open","href":"https://www.postgresql.org/about/news/pgsodium-200-modern-cryptography-for-postgresql-2389/","title":"","level":0},{"type":"text","content":"2.0","level":1},{"type":"link_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"and ","level":0},{"type":"link_open","href":"https://github.com/michelp/pgsodium/releases/tag/v3.0.0","title":"","level":0},{"type":"text","content":"3.0","level":1},{"type":"link_close","level":0},{"type":"text","content":" to","level":0},{"type":"softbreak","level":0},{"type":"text","content":"support ","level":0},{"type":"link_open","href":"https://github.com/michelp/pgsodium#transparent-column-encryption","title":"","level":0},{"type":"text","content":"Transparent Column Encryption","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[103,110],"level":0},{"type":"inline","content":"\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"video-with-border w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/4t_63HT3rZY\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen","level":1,"lines":[103,110],"children":[{"type":"text","content":"\u003cdiv className=\"video-container\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ciframe","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"video-with-border w-full\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://www.youtube-nocookie.com/embed/4t_63HT3rZY\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"frameBorder=\"1\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allowFullScreen","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"blockquote_open","lines":[110,112],"level":0},{"type":"paragraph_open","tight":false,"lines":[110,112],"level":1},{"type":"inline","content":"\u003c/iframe\u003e\n\u003c/div\u003e","level":2,"lines":[110,112],"children":[{"type":"text","content":"\u003c/iframe\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"paragraph_open","tight":false,"lines":[113,119],"level":0},{"type":"inline","content":"\u003csmall\u003e\n  \u003ci\u003e\n    Founder Chat: Paul Copplestone and Ant Wilson discuss the several ways we give back to the\n    community, and what Open Source means for Supabase.\n  \u003c/i\u003e\n\u003c/small\u003e","level":1,"lines":[113,119],"children":[{"type":"text","content":"\u003csmall\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ci\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Founder Chat: Paul Copplestone and Ant Wilson discuss the several ways we give back to the","level":0},{"type":"softbreak","level":0},{"type":"text","content":"community, and what Open Source means for Supabase.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/i\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/small\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[120,121],"level":0},{"type":"inline","content":"[Where we're going](#where-were-going)","level":1,"lines":[120,121],"children":[{"type":"text","content":"Where we're going","level":0}],"lvl":2,"i":11,"seen":0,"slug":"where-were-going"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[122,123],"level":0},{"type":"inline","content":"In our Series A post, we outlined our plans for Supabase [in three phases](https://supabase.com/blog/supabase-series-a#building-supabase):","level":1,"lines":[122,123],"children":[{"type":"text","content":"In our Series A post, we outlined our plans for Supabase ","level":0},{"type":"link_open","href":"https://supabase.com/blog/supabase-series-a#building-supabase","title":"","level":0},{"type":"text","content":"in three phases","level":1},{"type":"link_close","level":0},{"type":"text","content":":","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[124,128],"level":0},{"type":"list_item_open","lines":[124,125],"level":1},{"type":"paragraph_open","tight":true,"lines":[124,125],"level":2},{"type":"inline","content":"Phase 1: [Start with scalability](https://supabase.com/blog/supabase-series-a#phase-1-start-with-scalability)","level":3,"lines":[124,125],"children":[{"type":"text","content":"Phase 1: ","level":0},{"type":"link_open","href":"https://supabase.com/blog/supabase-series-a#phase-1-start-with-scalability","title":"","level":0},{"type":"text","content":"Start with scalability","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[125,126],"level":1},{"type":"paragraph_open","tight":true,"lines":[125,126],"level":2},{"type":"inline","content":"Phase 2: [Postgres Tooling](https://supabase.com/blog/supabase-series-a#phase-2-postgres-tooling)","level":3,"lines":[125,126],"children":[{"type":"text","content":"Phase 2: ","level":0},{"type":"link_open","href":"https://supabase.com/blog/supabase-series-a#phase-2-postgres-tooling","title":"","level":0},{"type":"text","content":"Postgres Tooling","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[126,128],"level":1},{"type":"paragraph_open","tight":true,"lines":[126,127],"level":2},{"type":"inline","content":"Phase 3: [Cloud-native PostgreSQL](https://supabase.com/blog/supabase-series-a#phase-3-cloud-native-postgresql)","level":3,"lines":[126,127],"children":[{"type":"text","content":"Phase 3: ","level":0},{"type":"link_open","href":"https://supabase.com/blog/supabase-series-a#phase-3-cloud-native-postgresql","title":"","level":0},{"type":"text","content":"Cloud-native PostgreSQL","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[128,129],"level":0},{"type":"inline","content":"We're now moving into Phase 3, with the same goals that we outlined in 2021:","level":1,"lines":[128,129],"children":[{"type":"text","content":"We're now moving into Phase 3, with the same goals that we outlined in 2021:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[130,141],"level":0},{"type":"list_item_open","lines":[130,132],"level":1},{"type":"paragraph_open","tight":true,"lines":[130,132],"level":2},{"type":"inline","content":"**Branching:** \u003cbr /\u003e\nDevelopers should be able to \"fork\" a database at any point. This is particularly important for Jamstack developers who run deployments for every Git branch.","level":3,"lines":[130,132],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Branching:","level":1},{"type":"strong_close","level":0},{"type":"text","content":" \u003cbr /\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Developers should be able to \"fork\" a database at any point. This is particularly important for Jamstack developers who run deployments for every Git branch.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[132,134],"level":1},{"type":"paragraph_open","tight":true,"lines":[132,134],"level":2},{"type":"inline","content":"**Scalable storage:** \u003cbr /\u003e\nStorage should grow and shrink without the user needing to provision more space themselves.","level":3,"lines":[132,134],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Scalable storage:","level":1},{"type":"strong_close","level":0},{"type":"text","content":" \u003cbr /\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Storage should grow and shrink without the user needing to provision more space themselves.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[134,136],"level":1},{"type":"paragraph_open","tight":true,"lines":[134,136],"level":2},{"type":"inline","content":"**Distributed:** \u003cbr /\u003e\nAn entire fleet of databases, distributed around the world, should feel like a single database (and even better if all nodes can accept read/write workloads).","level":3,"lines":[134,136],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Distributed:","level":1},{"type":"strong_close","level":0},{"type":"text","content":" \u003cbr /\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"An entire fleet of databases, distributed around the world, should feel like a single database (and even better if all nodes can accept read/write workloads).","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[136,138],"level":1},{"type":"paragraph_open","tight":true,"lines":[136,138],"level":2},{"type":"inline","content":"**Ephemeral compute:** \u003cbr /\u003e\nDevelopers don't want to be charged for a database which isn't doing anything. Likewise - if they are running huge computation then the database should scale up seamlessly, and scale down to zero when it's unused.","level":3,"lines":[136,138],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Ephemeral compute:","level":1},{"type":"strong_close","level":0},{"type":"text","content":" \u003cbr /\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Developers don't want to be charged for a database which isn't doing anything. Likewise - if they are running huge computation then the database should scale up seamlessly, and scale down to zero when it's unused.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[138,141],"level":1},{"type":"paragraph_open","tight":true,"lines":[138,140],"level":2},{"type":"inline","content":"**Snapshots and time-travel:** \u003cbr /\u003e\nDevelopers should be able to roll back to any point in time, or take a copy of their database with just a click of a button.","level":3,"lines":[138,140],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Snapshots and time-travel:","level":1},{"type":"strong_close","level":0},{"type":"text","content":" \u003cbr /\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Developers should be able to roll back to any point in time, or take a copy of their database with just a click of a button.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[141,142],"level":0},{"type":"inline","content":"We have a lot of work ahead of us, but we're excited to announce one of the ways that we're supporting the community is through our investment into OrioleDB.","level":1,"lines":[141,142],"children":[{"type":"text","content":"We have a lot of work ahead of us, but we're excited to announce one of the ways that we're supporting the community is through our investment into OrioleDB.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[143,145],"level":0},{"type":"inline","content":"OrioleDB is a [PostgreSQL extension](https://github.com/OrioleDB/OrioleDB) which solves a lot\nof [“wicked” problems](https://www.socallinuxexpo.org/sites/default/files/presentations/solving-postgres-wicked-problems.pdf) with Postgres.","level":1,"lines":[143,145],"children":[{"type":"text","content":"OrioleDB is a ","level":0},{"type":"link_open","href":"https://github.com/OrioleDB/OrioleDB","title":"","level":0},{"type":"text","content":"PostgreSQL extension","level":1},{"type":"link_close","level":0},{"type":"text","content":" which solves a lot","level":0},{"type":"softbreak","level":0},{"type":"text","content":"of ","level":0},{"type":"link_open","href":"https://www.socallinuxexpo.org/sites/default/files/presentations/solving-postgres-wicked-problems.pdf","title":"","level":0},{"type":"text","content":"“wicked” problems","level":1},{"type":"link_close","level":0},{"type":"text","content":" with Postgres.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[146,150],"level":0},{"type":"inline","content":"While it _currently_ requires some modifications to the Postgres core (about 1000 lines), the goal is to upstream those changes so\nthat _anyone_ can build a Storage extension like OrioleDB. Giving developers low-level access to the underlying storage capabilities of\nPostgres will unlock a slew of new capabilities in Postgres.\nYou can read more about OrioleDB's plans for Snapshots and Branching [in their wiki](https://github.com/OrioleDB/OrioleDB/wiki/Database-branching).","level":1,"lines":[146,150],"children":[{"type":"text","content":"While it ","level":0},{"type":"em_open","level":0},{"type":"text","content":"currently","level":1},{"type":"em_close","level":0},{"type":"text","content":" requires some modifications to the Postgres core (about 1000 lines), the goal is to upstream those changes so","level":0},{"type":"softbreak","level":0},{"type":"text","content":"that ","level":0},{"type":"em_open","level":0},{"type":"text","content":"anyone","level":1},{"type":"em_close","level":0},{"type":"text","content":" can build a Storage extension like OrioleDB. Giving developers low-level access to the underlying storage capabilities of","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Postgres will unlock a slew of new capabilities in Postgres.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"You can read more about OrioleDB's plans for Snapshots and Branching ","level":0},{"type":"link_open","href":"https://github.com/OrioleDB/OrioleDB/wiki/Database-branching","title":"","level":0},{"type":"text","content":"in their wiki","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[151,153],"level":0},{"type":"inline","content":"Supabase has invested $500K into OrioleDB to support their efforts, and we've hired a developer at Supabase to work full-time on OrioleDB, with more to come.\n[Apply here](https://boards.greenhouse.io/supabase/jobs/4307456004) if you're interested in working on Postgres full time.","level":1,"lines":[151,153],"children":[{"type":"text","content":"Supabase has invested $500K into OrioleDB to support their efforts, and we've hired a developer at Supabase to work full-time on OrioleDB, with more to come.","level":0},{"type":"softbreak","level":0},{"type":"link_open","href":"https://boards.greenhouse.io/supabase/jobs/4307456004","title":"","level":0},{"type":"text","content":"Apply here","level":1},{"type":"link_close","level":0},{"type":"text","content":" if you're interested in working on Postgres full time.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[154,157],"level":0},{"type":"inline","content":"\u003cdiv className=\"bg-gray-300 rounded-lg p-6 italic\"\u003e\n Note: we are not running OrioleDB on the Supabase platform. Our promise to you is “no vendor-lockin”, and therefore we will never run a fork of Postgres.\n In the future, if all of the OrioleDB core changes are up-streamed, then we might offer it on the platform.","level":1,"lines":[154,157],"children":[{"type":"text","content":"\u003cdiv className=\"bg-gray-300 rounded-lg p-6 italic\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Note: we are not running OrioleDB on the Supabase platform. Our promise to you is “no vendor-lockin”, and therefore we will never run a fork of Postgres.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"In the future, if all of the OrioleDB core changes are up-streamed, then we might offer it on the platform.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[158,159],"level":0},{"type":"inline","content":"If you want to try out OrioleDB today, you can switch the Postgres docker image to OrioleDB in the [self-hosted setup](https://github.com/supabase/supabase/blob/ec6085b8f852a903f2f45e715add1377cf89d850/docker/docker-compose.yml#L159).","level":1,"lines":[158,159],"children":[{"type":"text","content":"If you want to try out OrioleDB today, you can switch the Postgres docker image to OrioleDB in the ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase/blob/ec6085b8f852a903f2f45e715add1377cf89d850/docker/docker-compose.yml#L159","title":"","level":0},{"type":"text","content":"self-hosted setup","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[160,161],"level":0},{"type":"inline","content":"\u003c/div\u003e","level":1,"lines":[160,161],"children":[{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[162,163],"level":0},{"type":"inline","content":"[Join us](#join-us)","level":1,"lines":[162,163],"children":[{"type":"text","content":"Join us","level":0}],"lvl":2,"i":12,"seen":0,"slug":"join-us"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[164,165],"level":0},{"type":"inline","content":"Achieving the goals we've outlined above will be a long journey requiring collaboration from many companies besides Supabase.","level":1,"lines":[164,165],"children":[{"type":"text","content":"Achieving the goals we've outlined above will be a long journey requiring collaboration from many companies besides Supabase.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[166,168],"level":0},{"type":"inline","content":"If you want to help build the future of cloud-native Postgres, [join us](https://boards.greenhouse.io/supabase/jobs/4307456004) at Supabase.\nIf you're already working towards the same goals, reach out and let's do it together.","level":1,"lines":[166,168],"children":[{"type":"text","content":"If you want to help build the future of cloud-native Postgres, ","level":0},{"type":"link_open","href":"https://boards.greenhouse.io/supabase/jobs/4307456004","title":"","level":0},{"type":"text","content":"join us","level":1},{"type":"link_close","level":0},{"type":"text","content":" at Supabase.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"If you're already working towards the same goals, reach out and let's do it together.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[169,170],"level":0},{"type":"inline","content":"[By the way](#by-the-way)","level":1,"lines":[169,170],"children":[{"type":"text","content":"By the way","level":0}],"lvl":2,"i":13,"seen":0,"slug":"by-the-way"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[171,172],"level":0},{"type":"inline","content":"Launch Week 5 is starting Monday 15th August, we will launch one new feature every day for a week. If you want to follow along check out [supabase.com/launch-week](https://supabase.com/launch-week).","level":1,"lines":[171,172],"children":[{"type":"text","content":"Launch Week 5 is starting Monday 15th August, we will launch one new feature every day for a week. If you want to follow along check out ","level":0},{"type":"link_open","href":"https://supabase.com/launch-week","title":"","level":0},{"type":"text","content":"supabase.com/launch-week","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[173,180],"level":0},{"type":"list_item_open","lines":[173,174],"level":1},{"type":"paragraph_open","tight":true,"lines":[173,174],"level":2},{"type":"inline","content":"[Launch Week 5 Hackathon](https://supabase.com/blog/launch-week-5-hackathon)","level":3,"lines":[173,174],"children":[{"type":"link_open","href":"https://supabase.com/blog/launch-week-5-hackathon","title":"","level":0},{"type":"text","content":"Launch Week 5 Hackathon","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[174,175],"level":1},{"type":"paragraph_open","tight":true,"lines":[174,175],"level":2},{"type":"inline","content":"[Day 1 - Supabase CLI v1 and Management API Beta](https://supabase.com/blog/supabase-cli-v1-and-admin-api-beta)","level":3,"lines":[174,175],"children":[{"type":"link_open","href":"https://supabase.com/blog/supabase-cli-v1-and-admin-api-beta","title":"","level":0},{"type":"text","content":"Day 1 - Supabase CLI v1 and Management API Beta","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[175,176],"level":1},{"type":"paragraph_open","tight":true,"lines":[175,176],"level":2},{"type":"inline","content":"[Youtube video - Supabase CLI v1 and Management API Beta](https://www.youtube.com/watch?v=OpPOaJI_Z28\u0026feature=emb_title)","level":3,"lines":[175,176],"children":[{"type":"link_open","href":"https://www.youtube.com/watch?v=OpPOaJI_Z28\u0026feature=emb_title","title":"","level":0},{"type":"text","content":"Youtube video - Supabase CLI v1 and Management API Beta","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[176,177],"level":1},{"type":"paragraph_open","tight":true,"lines":[176,177],"level":2},{"type":"inline","content":"[Day 2 - supabase-js v2 Release Candidate](https://supabase.com/blog/supabase-js-v2)","level":3,"lines":[176,177],"children":[{"type":"link_open","href":"https://supabase.com/blog/supabase-js-v2","title":"","level":0},{"type":"text","content":"Day 2 - supabase-js v2 Release Candidate","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[177,178],"level":1},{"type":"paragraph_open","tight":true,"lines":[177,178],"level":2},{"type":"inline","content":"[Youtube Video - supabase-js v2 Release Candidate](https://www.youtube.com/watch?v=iqZlPtl_b-I)","level":3,"lines":[177,178],"children":[{"type":"link_open","href":"https://www.youtube.com/watch?v=iqZlPtl_b-I","title":"","level":0},{"type":"text","content":"Youtube Video - supabase-js v2 Release Candidate","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[178,179],"level":1},{"type":"paragraph_open","tight":true,"lines":[178,179],"level":2},{"type":"inline","content":"[Day 3 - Supabase is SOC2 compliant](https://supabase.com/blog/supabase-soc2)","level":3,"lines":[178,179],"children":[{"type":"link_open","href":"https://supabase.com/blog/supabase-soc2","title":"","level":0},{"type":"text","content":"Day 3 - Supabase is SOC2 compliant","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[179,180],"level":1},{"type":"paragraph_open","tight":true,"lines":[179,180],"level":2},{"type":"inline","content":"[Youtube video - Security Day](https://www.youtube.com/watch?v=6bGQotxisoY)","level":3,"lines":[179,180],"children":[{"type":"link_open","href":"https://www.youtube.com/watch?v=6bGQotxisoY","title":"","level":0},{"type":"text","content":"Youtube video - Security Day","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0}],"content":"- [Where we've been](#where-weve-been)\n  * [Database growth](#database-growth)\n  * [Developer Signups](#developer-signups)\n  * [Community](#community)\n- [About the round](#about-the-round)\n- [Giving back](#giving-back)\n  * [Open collective](#open-collective)\n  * [PostgREST](#postgrest)\n  * [Elixir Type support](#elixir-type-support)\n  * [Deno](#deno)\n  * [Database encryption](#database-encryption)\n- [Where we're going](#where-were-going)\n- [Join us](#join-us)\n- [By the way](#by-the-way)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-series-b"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>