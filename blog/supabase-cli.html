<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase CLI</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Local development, database migrations, and self-hosting." data-next-head=""/><meta property="og:title" content="Supabase CLI" data-next-head=""/><meta property="og:description" content="Local development, database migrations, and self-hosting." data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-cli" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="03-31-2021" data-next-head=""/><meta property="article:author" content="https://github.com/soedirgo" data-next-head=""/><meta property="article:tag" content="supabase" data-next-head=""/><meta property="article:tag" content="storage" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/cli/cli-og.jpg" data-next-head=""/><meta property="og:image:alt" content="Supabase CLI thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Supabase CLI</h1><div class="text-light flex space-x-3 text-sm"><p>31 Mar 2021</p><p>•</p><p>8 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/soedirgo"><div class="flex items-center gap-3"><div class="w-10"><img alt="Bobbie Soedirgo avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fsoedirgo.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fsoedirgo.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fsoedirgo.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Bobbie Soedirgo</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Supabase CLI" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fcli%2Fcli-og.jpg&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fcli%2Fcli-og.jpg&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fcli%2Fcli-og.jpg&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fcli%2Fcli-og.jpg&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fcli%2Fcli-og.jpg&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fcli%2Fcli-og.jpg&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fcli%2Fcli-og.jpg&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fcli%2Fcli-og.jpg&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fcli%2Fcli-og.jpg&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fcli%2Fcli-og.jpg&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fcli%2Fcli-og.jpg&amp;w=3840&amp;q=100"/></div><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p><strong>UPDATE 15/08/2022:</strong> <a href="supabase-cli-v1-and-admin-api-beta.html">Supabase CLI V1 is Generally Available</a> and we also released a Management API (in beta).</p>
<p>Today is Day 3 of <a href="launch-week.html">Launch Week</a>, and as promised - we&#x27;re releasing our CLI.</p>
<p>This is the first step in a long journey of features we plan to deliver:</p>
<ul>
<li>Running Supabase locally</li>
<li>Managing database migrations</li>
<li>Generating types directly from your database schema</li>
<li>Generating API and validation schemas from your database</li>
<li>Managing your Supabase projects</li>
<li>Pushing your local changes to production</li>
</ul>
<p>Here are some of the items we have completed so far.</p>
<h2 id="running-supabase-locally" class="group scroll-mt-24">Running Supabase Locally<a href="#running-supabase-locally" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>You can now run Supabase on your local machine, using Docker Compose. This Docker setup is 100% compatible with every project on Supabase - the tools used for local development are exactly the same as production.</p>
<p>We have released a full set of documentation <a href="https://supabase.com/docs/guides/local-development">here</a>. In this post we thought it would be useful to highlight how easy it is to get started.</p>
<p>A lot of Supabase developers are familiar with React, so here are the steps you would use to create a new React project which uses Supabase as a backend.</p>
<p>Install the CLI:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>npm install -g supabase</span></div></div><br/></code></div></div>
<p>Set up your React app:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span># create a fresh React app</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>npx create-react-app react-demo --use-npm</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span># move into the new folder</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>cd react-demo</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span># Save the install supabase-js library</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>npm install --save @supabase/supabase-js</span></div></div><br/></code></div></div>
<p>Set up Supabase:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>supabase init</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span># ✔ Port for Supabase URL: · 8000</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span># ✔ Port for PostgreSQL database: · 5432</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span># ✔ Project initialized.</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span># Supabase URL: http://localhost:8000</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span># Supabase Key (anon, public): eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsImlhdCI6MTYwMzk2ODgzNCwiZXhwIjoyNTUwNjUzNjM0LCJyb2xlIjoiYW5vbiJ9.36fUebxgx1mcBo4s19v0SzqmzunP--hm_hep0uLX0ew</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span># Supabase Key (service_role, private): eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsImlhdCI6MTYwMzk2ODgzNCwiZXhwIjoyNTUwNjUzNjM0LCJyb2xlIjoiYW5vbiJ9.36fUebxgx1mcBo4s19v0SzqmzunP--hm_hep0uLX0ew</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span># Database URL: postgres://postgres:postgres@localhost:5432/postgres</span></div></div><br/></code></div></div>
<p>Now that your application is now prepared, you can use Supabase anywhere in your application (for example, <code class="short-inline-codeblock">App.js</code>):</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>import { createClient } from &#x27;@supabase/supabase-js&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>const SUPABASE_URL = &#x27;http://localhost:8000&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>const SUPABASE_ANON_KEY =</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  &#x27;eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsImlhdCI6MTYwMzk2ODgzNCwiZXhwIjoyNTUwNjUzNjM0LCJyb2xlIjoiYW5vbiJ9.36fUebxgx1mcBo4s19v0SzqmzunP--hm_hep0uLX0ew&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)</span></div></div><br/></code></div></div>
<p>Then start the backend and the frontend:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>supabase start  # Start Supabase</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>npm start       # Start the React app</span></div></div><br/></code></div></div>
<p>If everything is working you should have a React app running on <code class="short-inline-codeblock">http://localhost:3000</code> and Supabase services running on <code class="short-inline-codeblock">http://localhost:8000</code>!</p>
<h3 id="next-steps" class="group scroll-mt-24">Next steps:<a href="#next-steps" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Soon we will give you the ability to push your changes from your local machine to your Production project. How will we do that? Migrations!</p>
<h2 id="migrations" class="group scroll-mt-24">Migrations<a href="#migrations" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Database Migrations are a process to &quot;change&quot; your database schema. In a NoSQL database you don&#x27;t need migrations, because you can insert any JSON data without validation. However with Relational databases you define your schema upfront, and the database will reject data which doesn&#x27;t &quot;fit&quot; the schema. This is one of the reasons Relational databases are so scalable - schemas ensure data integrity.</p>
<p>Just like an application though, a database schema needs to be constantly updated. And that&#x27;s where migrations fit! Migrations are simply a set of SQL scripts which change your database schema.</p>
<p>There is one problem however: there is <a href="https://news.ycombinator.com/item?id=21405501">no &quot;right&quot; way</a> to do migrations.</p>
<h3 id="diffs" class="group scroll-mt-24">Diffs<a href="#diffs" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>At Supabase, we do have one strong preference. We like schema &quot;diffing&quot; over &quot;manual migrations&quot;.</p>
<p>Manual migrations work like this:</p>
<ul>
<li>A developer thinks about all the changes they want to make to their database</li>
<li>They create a SQL script which will cause those changes</li>
<li>They run that script on their database</li>
</ul>
<p>After a while though, these scripts pile up - the <code class="short-inline-codeblock">migrations</code> folder can contain hundreds of migration scripts. This method is also &quot;version control&quot; on top of another version control system (i.e. git).</p>
<p>A &quot;diff&quot; tool works like this:</p>
<ul>
<li>a developer makes all the changes they desire to a local database</li>
<li>they use a tool to compare their local database to the production database</li>
<li>the tool then generates all the SQL scripts that are required, and runs them on the target database</li>
</ul>
<p>In this case, the tool does all the hard work. This is obviously an ideal state. Databases schemas are declarative, and when you check them into git, you can see their evolution over time. The hard part is finding a tool which can handle all the edge-cases of database diff&#x27;ing.</p>
<h3 id="choosing-the-best-diff-tool" class="group scroll-mt-24">Choosing the best diff tool<a href="#choosing-the-best-diff-tool" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>After evaluating these OSS tools:</p>
<ul>
<li><a href="https://github.com/djrobstep/migra">migra</a> (Python)</li>
<li><a href="https://github.com/gimenete/dbdiff">dbdiff</a> (JS)</li>
<li><a href="https://github.com/joncrlsn/pgdiff">pgdiff</a> (Go)</li>
<li><a href="https://github.com/fordfrog/apgdiff">apgdiff</a> (Java)</li>
<li><a href="https://github.com/eulerto/pgquarrel">pgquarrel</a> (C)</li>
<li><a href="https://www.pgadmin.org/docs/pgadmin4/development/schema_diff.html">pgAdmin Schema Diff</a> (Python)</li>
</ul>
<p>We found that the most complete one was the pgAdmin Schema Diff, migra came a close second.</p>
<p>The deciding factor was if the tool could track an owner change for a VIEW.</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>ALTER VIEW my_view OWNER TO authenticated;</span></div></div><br/></code></div></div>
<p>This is critical for Row Level Security to work with views. For policies to kick in on views, the owner must not have <code class="short-inline-codeblock">superuser</code> or <code class="short-inline-codeblock">bypassrls</code> privileges. Currently migra doesn&#x27;t track this change (<a href="https://github.com/djrobstep/migra/issues/160">issue</a>), while the pgAdmin Schema Diff does.</p>
<p>There was a problem in using the <a href="https://www.pgadmin.org/docs/pgadmin4/development/schema_diff.html">pgAdmin Schema Diff</a> though, it&#x27;s a GUI-only tool.</p>
<p></p>
<p>So we did what we always strive to do - improve existing open source software. We created a CLI mode for the Schema Diff on <a href="https://github.com/supabase/pgadmin4/blob/cli/web/cli.py">our repo</a>. We&#x27;ve also released a <a href="https://hub.docker.com/r/supabase/pgadmin-schema-diff">docker image</a> for a quick start.</p>
<p>The CLI offers the same functionality as the GUI version. You can diff two databases by specifying the connection strings like shown below.</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>docker run supabase/pgadmin-schema-diff \</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>  &#x27;*******************************/diff_source&#x27; \</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>  &#x27;************************************/diff_target&#x27; \</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>  &gt; diff_demo.sql</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>Starting schema diff...</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>Comparison started......0%</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>Comparing Event Triggers...2%</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>Comparing Extensions...4%</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>Comparing Languages...8%</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>Comparing Foreign Servers...14%</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>Comparing Foreign Tables of schema &#x27;public&#x27;...28%</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>Comparing Tables of schema &#x27;public&#x27;...50%</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>Comparing Domains of schema &#x27;test_schema_diff&#x27;...66%</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>Comparing Foreign Tables of schema &#x27;test_schema_diff&#x27;...68%</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>Comparing FTS Templates of schema &#x27;test_schema_diff&#x27;...76%</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>Comparing Functions of schema &#x27;test_schema_diff&#x27;...78%</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>Comparing Procedures of schema &#x27;test_schema_diff&#x27;...80%</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>Comparing Tables of schema &#x27;test_schema_diff&#x27;...90%</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>Comparing Types of schema &#x27;test_schema_diff&#x27;...92%</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>Comparing Materialized Views of schema &#x27;test_schema_diff&#x27;...96%</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>Done.</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>## the diff is written to diff_demo.sql</span></div></div><br/></code></div></div>
<p>A sample diff can be seen on this <a href="https://gist.github.com/steve-chavez/3f286a233806aeee0bcea4a47f97f0b5">gist</a>. This was generated by diffing these <a href="https://github.com/supabase/pgadmin4/tree/cli/web/pgadmin/tools/schema_diff/tests/pg/10_plus">two databases</a>.</p>
<p>On these <a href="https://gist.github.com/steve-chavez/3f286a233806aeee0bcea4a47f97f0b5#file-diff_demo-sql-L1022-L1023">lines</a>, you can see how it tracks the view&#x27;s owner change (note: the <code class="short-inline-codeblock">ALTER TABLE</code> statement is interchangeable with <code class="short-inline-codeblock">ALTER VIEW</code> in this case). Additionally, you can see that it handles <a href="https://gist.github.com/steve-chavez/3f286a233806aeee0bcea4a47f97f0b5#file-diff_demo-sql-L278-L287">domains</a> just fine, this is an edge-case that other diff tools don&#x27;t handle.</p>
<p>Also, similarly to the pgAdmin GUI:</p>
<ul>
<li>You can include and exclude database objects from the diff with <code class="short-inline-codeblock">--include-objects</code> or <code class="short-inline-codeblock">--exclude-objects</code></li>
<li>You can choose a single schema to diff with the <code class="short-inline-codeblock">--schema</code> argument or you can pick different schemas to compare with the --source-schema and --target-schema arguments. We recommend you do this for Supabase databases. Diffing the whole database can take a while because of the <code class="short-inline-codeblock">extensions</code> schema (especially if you enable PostGIS, which adds many functions).</li>
</ul>
<h3 id="next-steps-1" class="group scroll-mt-24">Next steps<a href="#next-steps-1" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Once we have added logins to the CLI, we will be able to use Migrations to create a seamless workflow between local development and your production database.</p>
<p>Also, the pgAdmin team has showed <a href="https://www.postgresql.org/message-id/CA%2BOCxoyjZhV9stFMAQ-QhHuA0%2BdLQD5XD_YT%2BQo2vY0GhkBKFw%40mail.gmail.com">interest</a> in including our Schema Diff CLI in the official pgAdmin. We&#x27;ll be working with them to include this change upstream to benefit the whole community.</p>
<h2 id="self-hosting" class="group scroll-mt-24">Self Hosting<a href="#self-hosting" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Finally, we are adding one critical command to our CLI for everybody who wants to self-host:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>supabase eject</span></div></div><br/></code></div></div>
<p>This gives you everything you need to run the Supabase stack.</p>
<p>After running the command inside the terminal, you will see three items:</p>
<ul>
<li><code class="short-inline-codeblock">docker-compose.yml</code> (file)</li>
<li><code class="short-inline-codeblock">kong</code> (directory)</li>
<li><code class="short-inline-codeblock">postgres</code> (directory)</li>
</ul>
<p>If you have an existing Postgres database running elsewhere you can easily drop the Postgres directory but first make sure you do these three things:</p>
<ul>
<li>run the .sql files from the Postgres directory on your existing database</li>
<li>update all references to the DB URI in <code class="short-inline-codeblock">docker-compose.yml</code> to your existing database</li>
<li>run <a href="https://github.com/supabase/realtime#server">these steps</a> to enable replication inside the database, so that the realtime engine can stream changes from your database</li>
</ul>
<p>You may also want to play with the environment variables for each application inside <code class="short-inline-codeblock">docker-compose.yml</code>. <a href="https://postgrest.org/en/v7.0.0/configuration.html">PostgREST</a> has many additional configuration options, as does <a href="https://github.com/supabase/gotrue#configuration">GoTrue</a>. In the hosted version of Supabase we connect our own SMTP service to GoTrue for sending auth emails, so you may also want to add these settings here in order to enable this.</p>
<p>Also check <code class="short-inline-codeblock">kong.yml</code> inside the <code class="short-inline-codeblock">kong</code> directory where you&#x27;ll see how all the services are routed to, and with what rules, down the bottom you&#x27;ll find the JWTs capable of accessing services that require API Key access.</p>
<p>Once you&#x27;re all set, you can start the stack by running:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>docker compose up</span></div></div><br/></code></div></div>
<p>Head over to the <a href="../docs/guides/self-hosting.html">Self Hosting Docs</a> for a more complete walk through, it also includes several <a href="../docs/guides/self-hosting.html#one-click-deploys">one-click deploys</a>, so you can easily deploy into your own cloud hosting provider.</p>
<p>If you require any assistance feel free to reach out in our <a href="https://github.com/supabase/supabase/discussions">github discussions</a> or at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
<p>Check out the CLI VI <a href="supabase-cli-v1-and-admin-api-beta.html">launched Monday 15th August, 2022</a>, contribute to the <a href="https://github.com/supabase/cli">CLI repo</a>, or go here for the <a href="../dashboard/org.html">hosted version</a>.</p></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-cli&amp;text=Supabase%20CLI"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-cli&amp;text=Supabase%20CLI"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-cli&amp;t=Supabase%20CLI"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="supabase-nft-marketplace.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Supabase Launches NFT Marketplace</h4><p class="small">1 April 2021</p></div></div></div></div></a></div><div><a href="https://supabase.com/blog/supabase-storage"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Storage is now available in Supabase</h4><p class="small">30 March 2021</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/supabase"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">supabase</div></a><a href="https://supabase.com/blog/tags/storage"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">storage</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#running-supabase-locally">Running Supabase Locally</a></li>
<li><a href="#migrations">Migrations</a></li>
<li><a href="#self-hosting">Self Hosting</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-cli&amp;text=Supabase%20CLI"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-cli&amp;text=Supabase%20CLI"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-cli&amp;t=Supabase%20CLI"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"supabase-nft-marketplace","title":"Supabase Launches NFT Marketplace","description":"A fully encrypted NFT platform to protect and transact your digital assets","author":"ant_wilson","author_title":"Supabase","author_url":"https://github.com/awalias","author_image_url":"https://github.com/awalias.png","image":"nft/nft-3.png","thumb":"nft/nft-3.png","categories":["company"],"tags":["supabase","nfts"],"date":"04-01-2021","formattedDate":"1 April 2021","readingTime":"3 minute read","url":"/blog/supabase-nft-marketplace","path":"/blog/supabase-nft-marketplace"},"nextPost":{"slug":"supabase-storage","title":"Storage is now available in Supabase","description":"Launching Supabase Storage and how you can use it in your apps","author":"inian","image":"storage/ph-1.png","thumb":"storage/ph-1.png","categories":["product"],"tags":["supabase","storage"],"date":"03-30-2021","formattedDate":"30 March 2021","readingTime":"9 minute read","url":"/blog/supabase-storage","path":"/blog/supabase-storage"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-cli","source":"\n**UPDATE 15/08/2022:** [Supabase CLI V1 is Generally Available](/blog/supabase-cli-v1-and-admin-api-beta) and we also released a Management API (in beta).\n\nToday is Day 3 of [Launch Week](/blog/launch-week), and as promised - we're releasing our CLI.\n\nThis is the first step in a long journey of features we plan to deliver:\n\n- Running Supabase locally\n- Managing database migrations\n- Generating types directly from your database schema\n- Generating API and validation schemas from your database\n- Managing your Supabase projects\n- Pushing your local changes to production\n\nHere are some of the items we have completed so far.\n\n## Running Supabase Locally\n\nYou can now run Supabase on your local machine, using Docker Compose. This Docker setup is 100% compatible with every project on Supabase - the tools used for local development are exactly the same as production.\n\nWe have released a full set of documentation [here](/docs/guides/local-development). In this post we thought it would be useful to highlight how easy it is to get started.\n\nA lot of Supabase developers are familiar with React, so here are the steps you would use to create a new React project which uses Supabase as a backend.\n\nInstall the CLI:\n\n```bash\nnpm install -g supabase\n```\n\nSet up your React app:\n\n```bash\n# create a fresh React app\nnpx create-react-app react-demo --use-npm\n\n# move into the new folder\ncd react-demo\n\n# Save the install supabase-js library\nnpm install --save @supabase/supabase-js\n```\n\nSet up Supabase:\n\n```bash\nsupabase init\n\n# ✔ Port for Supabase URL: · 8000\n# ✔ Port for PostgreSQL database: · 5432\n# ✔ Project initialized.\n# Supabase URL: http://localhost:8000\n# Supabase Key (anon, public): eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsImlhdCI6MTYwMzk2ODgzNCwiZXhwIjoyNTUwNjUzNjM0LCJyb2xlIjoiYW5vbiJ9.36fUebxgx1mcBo4s19v0SzqmzunP--hm_hep0uLX0ew\n# Supabase Key (service_role, private): eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsImlhdCI6MTYwMzk2ODgzNCwiZXhwIjoyNTUwNjUzNjM0LCJyb2xlIjoiYW5vbiJ9.36fUebxgx1mcBo4s19v0SzqmzunP--hm_hep0uLX0ew\n# Database URL: postgres://postgres:postgres@localhost:5432/postgres\n```\n\nNow that your application is now prepared, you can use Supabase anywhere in your application (for example, `App.js`):\n\n```jsx\nimport { createClient } from '@supabase/supabase-js'\nconst SUPABASE_URL = 'http://localhost:8000'\nconst SUPABASE_ANON_KEY =\n  'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsImlhdCI6MTYwMzk2ODgzNCwiZXhwIjoyNTUwNjUzNjM0LCJyb2xlIjoiYW5vbiJ9.36fUebxgx1mcBo4s19v0SzqmzunP--hm_hep0uLX0ew'\nconst supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)\n```\n\nThen start the backend and the frontend:\n\n```bash\nsupabase start  # Start Supabase\nnpm start       # Start the React app\n```\n\nIf everything is working you should have a React app running on `http://localhost:3000` and Supabase services running on `http://localhost:8000`!\n\n### Next steps:\n\nSoon we will give you the ability to push your changes from your local machine to your Production project. How will we do that? Migrations!\n\n## Migrations\n\nDatabase Migrations are a process to \"change\" your database schema. In a NoSQL database you don't need migrations, because you can insert any JSON data without validation. However with Relational databases you define your schema upfront, and the database will reject data which doesn't \"fit\" the schema. This is one of the reasons Relational databases are so scalable - schemas ensure data integrity.\n\nJust like an application though, a database schema needs to be constantly updated. And that's where migrations fit! Migrations are simply a set of SQL scripts which change your database schema.\n\nThere is one problem however: there is [no \"right\" way](https://news.ycombinator.com/item?id=21405501) to do migrations.\n\n### Diffs\n\nAt Supabase, we do have one strong preference. We like schema \"diffing\" over \"manual migrations\".\n\nManual migrations work like this:\n\n- A developer thinks about all the changes they want to make to their database\n- They create a SQL script which will cause those changes\n- They run that script on their database\n\nAfter a while though, these scripts pile up - the `migrations` folder can contain hundreds of migration scripts. This method is also \"version control\" on top of another version control system (i.e. git).\n\nA \"diff\" tool works like this:\n\n- a developer makes all the changes they desire to a local database\n- they use a tool to compare their local database to the production database\n- the tool then generates all the SQL scripts that are required, and runs them on the target database\n\nIn this case, the tool does all the hard work. This is obviously an ideal state. Databases schemas are declarative, and when you check them into git, you can see their evolution over time. The hard part is finding a tool which can handle all the edge-cases of database diff'ing.\n\n### Choosing the best diff tool\n\nAfter evaluating these OSS tools:\n\n- [migra](https://github.com/djrobstep/migra) (Python)\n- [dbdiff](https://github.com/gimenete/dbdiff) (JS)\n- [pgdiff](https://github.com/joncrlsn/pgdiff) (Go)\n- [apgdiff](https://github.com/fordfrog/apgdiff) (Java)\n- [pgquarrel](https://github.com/eulerto/pgquarrel) (C)\n- [pgAdmin Schema Diff](https://www.pgadmin.org/docs/pgadmin4/development/schema_diff.html) (Python)\n\nWe found that the most complete one was the pgAdmin Schema Diff, migra came a close second.\n\nThe deciding factor was if the tool could track an owner change for a VIEW.\n\n```sql\nALTER VIEW my_view OWNER TO authenticated;\n```\n\nThis is critical for Row Level Security to work with views. For policies to kick in on views, the owner must not have `superuser` or `bypassrls` privileges. Currently migra doesn't track this change ([issue](https://github.com/djrobstep/migra/issues/160)), while the pgAdmin Schema Diff does.\n\nThere was a problem in using the [pgAdmin Schema Diff](https://www.pgadmin.org/docs/pgadmin4/development/schema_diff.html) though, it's a GUI-only tool.\n\n![pgadmin diff](/images/blog/cli/pgadmin-diff.png)\n\nSo we did what we always strive to do - improve existing open source software. We created a CLI mode for the Schema Diff on [our repo](https://github.com/supabase/pgadmin4/blob/cli/web/cli.py). We've also released a [docker image](https://hub.docker.com/r/supabase/pgadmin-schema-diff) for a quick start.\n\nThe CLI offers the same functionality as the GUI version. You can diff two databases by specifying the connection strings like shown below.\n\n```bash\ndocker run supabase/pgadmin-schema-diff \\\n  '*******************************/diff_source' \\\n  '************************************/diff_target' \\\n  \u003e diff_demo.sql\n\nStarting schema diff...\nComparison started......0%\nComparing Event Triggers...2%\nComparing Extensions...4%\nComparing Languages...8%\nComparing Foreign Servers...14%\nComparing Foreign Tables of schema 'public'...28%\nComparing Tables of schema 'public'...50%\nComparing Domains of schema 'test_schema_diff'...66%\nComparing Foreign Tables of schema 'test_schema_diff'...68%\nComparing FTS Templates of schema 'test_schema_diff'...76%\nComparing Functions of schema 'test_schema_diff'...78%\nComparing Procedures of schema 'test_schema_diff'...80%\nComparing Tables of schema 'test_schema_diff'...90%\nComparing Types of schema 'test_schema_diff'...92%\nComparing Materialized Views of schema 'test_schema_diff'...96%\nDone.\n\n## the diff is written to diff_demo.sql\n```\n\nA sample diff can be seen on this [gist](https://gist.github.com/steve-chavez/3f286a233806aeee0bcea4a47f97f0b5). This was generated by diffing these [two databases](https://github.com/supabase/pgadmin4/tree/cli/web/pgadmin/tools/schema_diff/tests/pg/10_plus).\n\nOn these [lines](https://gist.github.com/steve-chavez/3f286a233806aeee0bcea4a47f97f0b5#file-diff_demo-sql-L1022-L1023), you can see how it tracks the view's owner change (note: the `ALTER TABLE` statement is interchangeable with `ALTER VIEW` in this case). Additionally, you can see that it handles [domains](https://gist.github.com/steve-chavez/3f286a233806aeee0bcea4a47f97f0b5#file-diff_demo-sql-L278-L287) just fine, this is an edge-case that other diff tools don't handle.\n\nAlso, similarly to the pgAdmin GUI:\n\n- You can include and exclude database objects from the diff with `--include-objects` or `--exclude-objects`\n- You can choose a single schema to diff with the `--schema` argument or you can pick different schemas to compare with the --source-schema and --target-schema arguments. We recommend you do this for Supabase databases. Diffing the whole database can take a while because of the `extensions` schema (especially if you enable PostGIS, which adds many functions).\n\n### Next steps\n\nOnce we have added logins to the CLI, we will be able to use Migrations to create a seamless workflow between local development and your production database.\n\nAlso, the pgAdmin team has showed [interest](https://www.postgresql.org/message-id/CA%2BOCxoyjZhV9stFMAQ-QhHuA0%2BdLQD5XD_YT%2BQo2vY0GhkBKFw%40mail.gmail.com) in including our Schema Diff CLI in the official pgAdmin. We'll be working with them to include this change upstream to benefit the whole community.\n\n## Self Hosting\n\nFinally, we are adding one critical command to our CLI for everybody who wants to self-host:\n\n```bash\nsupabase eject\n```\n\nThis gives you everything you need to run the Supabase stack.\n\nAfter running the command inside the terminal, you will see three items:\n\n- `docker-compose.yml` (file)\n- `kong` (directory)\n- `postgres` (directory)\n\nIf you have an existing Postgres database running elsewhere you can easily drop the Postgres directory but first make sure you do these three things:\n\n- run the .sql files from the Postgres directory on your existing database\n- update all references to the DB URI in `docker-compose.yml` to your existing database\n- run [these steps](https://github.com/supabase/realtime#server) to enable replication inside the database, so that the realtime engine can stream changes from your database\n\nYou may also want to play with the environment variables for each application inside `docker-compose.yml`. [PostgREST](https://postgrest.org/en/v7.0.0/configuration.html) has many additional configuration options, as does [GoTrue](https://github.com/supabase/gotrue#configuration). In the hosted version of Supabase we connect our own SMTP service to GoTrue for sending auth emails, so you may also want to add these settings here in order to enable this.\n\nAlso check `kong.yml` inside the `kong` directory where you'll see how all the services are routed to, and with what rules, down the bottom you'll find the JWTs capable of accessing services that require API Key access.\n\nOnce you're all set, you can start the stack by running:\n\n```bash\ndocker compose up\n```\n\nHead over to the [Self Hosting Docs](/docs/guides/self-hosting) for a more complete walk through, it also includes several [one-click deploys](/docs/guides/self-hosting#one-click-deploys), so you can easily deploy into your own cloud hosting provider.\n\nIf you require any assistance feel free to reach out in our [github discussions](https://github.com/supabase/supabase/discussions) <NAME_EMAIL>.\n\nCheck out the CLI VI [launched Monday 15th August, 2022](/blog/supabase-cli-v1-and-admin-api-beta), contribute to the [CLI repo](https://github.com/supabase/cli), or go here for the [hosted version](https://supabase.com/dashboard).\n","title":"Supabase CLI","description":"Local development, database migrations, and self-hosting.","author":"soedirgo","image":"cli/cli-og.jpg","thumb":"cli/cli-og.jpg","categories":["product"],"tags":["supabase","storage"],"date":"03-31-2021","content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    strong: \"strong\",\n    a: \"a\",\n    ul: \"ul\",\n    li: \"li\",\n    h2: \"h2\",\n    code: \"code\",\n    h3: \"h3\",\n    img: \"img\"\n  }, _provideComponents(), props.components), {CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.strong, {\n        children: \"UPDATE 15/08/2022:\"\n      }), \" \", _jsx(_components.a, {\n        href: \"/blog/supabase-cli-v1-and-admin-api-beta\",\n        children: \"Supabase CLI V1 is Generally Available\"\n      }), \" and we also released a Management API (in beta).\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Today is Day 3 of \", _jsx(_components.a, {\n        href: \"/blog/launch-week\",\n        children: \"Launch Week\"\n      }), \", and as promised - we're releasing our CLI.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This is the first step in a long journey of features we plan to deliver:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Running Supabase locally\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Managing database migrations\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Generating types directly from your database schema\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Generating API and validation schemas from your database\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Managing your Supabase projects\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Pushing your local changes to production\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Here are some of the items we have completed so far.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"running-supabase-locally\",\n      children: \"Running Supabase Locally\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You can now run Supabase on your local machine, using Docker Compose. This Docker setup is 100% compatible with every project on Supabase - the tools used for local development are exactly the same as production.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We have released a full set of documentation \", _jsx(_components.a, {\n        href: \"/docs/guides/local-development\",\n        children: \"here\"\n      }), \". In this post we thought it would be useful to highlight how easy it is to get started.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"A lot of Supabase developers are familiar with React, so here are the steps you would use to create a new React project which uses Supabase as a backend.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Install the CLI:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"npm \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"install \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"-g \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"supabase\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Set up your React app:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"# create a fresh React app\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"npx \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"create-react-app react-demo \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"--use-npm\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"# move into the new folder\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"cd \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"react-demo\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"# Save the install supabase-js library\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"npm \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"install \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"--save \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"@supabase/supabase-js\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Set up Supabase:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"supabase \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"init\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"# ✔ Port for Supabase URL: · 8000\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"# ✔ Port for PostgreSQL database: · 5432\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"# ✔ Project initialized.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"# Supabase URL: http://localhost:8000\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"# Supabase Key (anon, public): eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsImlhdCI6MTYwMzk2ODgzNCwiZXhwIjoyNTUwNjUzNjM0LCJyb2xlIjoiYW5vbiJ9.36fUebxgx1mcBo4s19v0SzqmzunP--hm_hep0uLX0ew\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"# Supabase Key (service_role, private): eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsImlhdCI6MTYwMzk2ODgzNCwiZXhwIjoyNTUwNjUzNjM0LCJyb2xlIjoiYW5vbiJ9.36fUebxgx1mcBo4s19v0SzqmzunP--hm_hep0uLX0ew\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"# Database URL: postgres://postgres:postgres@localhost:5432/postgres\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Now that your application is now prepared, you can use Supabase anywhere in your application (for example, \", _jsx(_components.code, {\n        children: \"App.js\"\n      }), \"):\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { createClient } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'@supabase/supabase-js'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"SUPABASE_URL \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'http://localhost:8000'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"SUPABASE_ANON_KEY \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsImlhdCI6MTYwMzk2ODgzNCwiZXhwIjoyNTUwNjUzNjM0LCJyb2xlIjoiYW5vbiJ9.36fUebxgx1mcBo4s19v0SzqmzunP--hm_hep0uLX0ew'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"supabase \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"createClient\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"SUPABASE_URL\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"SUPABASE_ANON_KEY\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"jsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Then start the backend and the frontend:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"supabase \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"start  \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"# Start Supabase\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"npm \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"start       \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"# Start the React app\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If everything is working you should have a React app running on \", _jsx(_components.code, {\n        children: \"http://localhost:3000\"\n      }), \" and Supabase services running on \", _jsx(_components.code, {\n        children: \"http://localhost:8000\"\n      }), \"!\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"next-steps\",\n      children: \"Next steps:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Soon we will give you the ability to push your changes from your local machine to your Production project. How will we do that? Migrations!\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"migrations\",\n      children: \"Migrations\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Database Migrations are a process to \\\"change\\\" your database schema. In a NoSQL database you don't need migrations, because you can insert any JSON data without validation. However with Relational databases you define your schema upfront, and the database will reject data which doesn't \\\"fit\\\" the schema. This is one of the reasons Relational databases are so scalable - schemas ensure data integrity.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Just like an application though, a database schema needs to be constantly updated. And that's where migrations fit! Migrations are simply a set of SQL scripts which change your database schema.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"There is one problem however: there is \", _jsx(_components.a, {\n        href: \"https://news.ycombinator.com/item?id=21405501\",\n        children: \"no \\\"right\\\" way\"\n      }), \" to do migrations.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"diffs\",\n      children: \"Diffs\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"At Supabase, we do have one strong preference. We like schema \\\"diffing\\\" over \\\"manual migrations\\\".\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Manual migrations work like this:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"A developer thinks about all the changes they want to make to their database\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"They create a SQL script which will cause those changes\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"They run that script on their database\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"After a while though, these scripts pile up - the \", _jsx(_components.code, {\n        children: \"migrations\"\n      }), \" folder can contain hundreds of migration scripts. This method is also \\\"version control\\\" on top of another version control system (i.e. git).\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"A \\\"diff\\\" tool works like this:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"a developer makes all the changes they desire to a local database\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"they use a tool to compare their local database to the production database\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"the tool then generates all the SQL scripts that are required, and runs them on the target database\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In this case, the tool does all the hard work. This is obviously an ideal state. Databases schemas are declarative, and when you check them into git, you can see their evolution over time. The hard part is finding a tool which can handle all the edge-cases of database diff'ing.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"choosing-the-best-diff-tool\",\n      children: \"Choosing the best diff tool\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"After evaluating these OSS tools:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://github.com/djrobstep/migra\",\n          children: \"migra\"\n        }), \" (Python)\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://github.com/gimenete/dbdiff\",\n          children: \"dbdiff\"\n        }), \" (JS)\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://github.com/joncrlsn/pgdiff\",\n          children: \"pgdiff\"\n        }), \" (Go)\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://github.com/fordfrog/apgdiff\",\n          children: \"apgdiff\"\n        }), \" (Java)\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://github.com/eulerto/pgquarrel\",\n          children: \"pgquarrel\"\n        }), \" (C)\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://www.pgadmin.org/docs/pgadmin4/development/schema_diff.html\",\n          children: \"pgAdmin Schema Diff\"\n        }), \" (Python)\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We found that the most complete one was the pgAdmin Schema Diff, migra came a close second.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The deciding factor was if the tool could track an owner change for a VIEW.\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"ALTER VIEW\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" my_view \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"OWNER TO\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" authenticated;\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"This is critical for Row Level Security to work with views. For policies to kick in on views, the owner must not have \", _jsx(_components.code, {\n        children: \"superuser\"\n      }), \" or \", _jsx(_components.code, {\n        children: \"bypassrls\"\n      }), \" privileges. Currently migra doesn't track this change (\", _jsx(_components.a, {\n        href: \"https://github.com/djrobstep/migra/issues/160\",\n        children: \"issue\"\n      }), \"), while the pgAdmin Schema Diff does.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"There was a problem in using the \", _jsx(_components.a, {\n        href: \"https://www.pgadmin.org/docs/pgadmin4/development/schema_diff.html\",\n        children: \"pgAdmin Schema Diff\"\n      }), \" though, it's a GUI-only tool.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/cli/pgadmin-diff.png\",\n        alt: \"pgadmin diff\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"So we did what we always strive to do - improve existing open source software. We created a CLI mode for the Schema Diff on \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/pgadmin4/blob/cli/web/cli.py\",\n        children: \"our repo\"\n      }), \". We've also released a \", _jsx(_components.a, {\n        href: \"https://hub.docker.com/r/supabase/pgadmin-schema-diff\",\n        children: \"docker image\"\n      }), \" for a quick start.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The CLI offers the same functionality as the GUI version. You can diff two databases by specifying the connection strings like shown below.\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"docker \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"run supabase/pgadmin-schema-diff \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\\\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  '*******************************/diff_source' \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\\\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  '************************************/diff_target' \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\\\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \u003e \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"diff_demo.sql\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"Starting \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"schema diff...\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"Comparison \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"started......0%\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"Comparing \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"Event Triggers...2%\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"Comparing \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"Extensions...4%\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"Comparing \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"Languages...8%\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"Comparing \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"Foreign Servers...14%\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"Comparing \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"Foreign Tables of schema 'public'...28%\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"Comparing \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"Tables of schema 'public'...50%\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"Comparing \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"Domains of schema 'test_schema_diff'...66%\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"Comparing \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"Foreign Tables of schema 'test_schema_diff'...68%\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"Comparing \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"FTS Templates of schema 'test_schema_diff'...76%\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"Comparing \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"Functions of schema 'test_schema_diff'...78%\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"Comparing \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"Procedures of schema 'test_schema_diff'...80%\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"Comparing \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"Tables of schema 'test_schema_diff'...90%\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"Comparing \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"Types of schema 'test_schema_diff'...92%\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"Comparing \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"Materialized Views of schema 'test_schema_diff'...96%\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"Done.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"## the diff is written to diff_demo.sql\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"A sample diff can be seen on this \", _jsx(_components.a, {\n        href: \"https://gist.github.com/steve-chavez/3f286a233806aeee0bcea4a47f97f0b5\",\n        children: \"gist\"\n      }), \". This was generated by diffing these \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/pgadmin4/tree/cli/web/pgadmin/tools/schema_diff/tests/pg/10_plus\",\n        children: \"two databases\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"On these \", _jsx(_components.a, {\n        href: \"https://gist.github.com/steve-chavez/3f286a233806aeee0bcea4a47f97f0b5#file-diff_demo-sql-L1022-L1023\",\n        children: \"lines\"\n      }), \", you can see how it tracks the view's owner change (note: the \", _jsx(_components.code, {\n        children: \"ALTER TABLE\"\n      }), \" statement is interchangeable with \", _jsx(_components.code, {\n        children: \"ALTER VIEW\"\n      }), \" in this case). Additionally, you can see that it handles \", _jsx(_components.a, {\n        href: \"https://gist.github.com/steve-chavez/3f286a233806aeee0bcea4a47f97f0b5#file-diff_demo-sql-L278-L287\",\n        children: \"domains\"\n      }), \" just fine, this is an edge-case that other diff tools don't handle.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Also, similarly to the pgAdmin GUI:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"You can include and exclude database objects from the diff with \", _jsx(_components.code, {\n          children: \"--include-objects\"\n        }), \" or \", _jsx(_components.code, {\n          children: \"--exclude-objects\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"You can choose a single schema to diff with the \", _jsx(_components.code, {\n          children: \"--schema\"\n        }), \" argument or you can pick different schemas to compare with the --source-schema and --target-schema arguments. We recommend you do this for Supabase databases. Diffing the whole database can take a while because of the \", _jsx(_components.code, {\n          children: \"extensions\"\n        }), \" schema (especially if you enable PostGIS, which adds many functions).\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"next-steps-1\",\n      children: \"Next steps\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Once we have added logins to the CLI, we will be able to use Migrations to create a seamless workflow between local development and your production database.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Also, the pgAdmin team has showed \", _jsx(_components.a, {\n        href: \"https://www.postgresql.org/message-id/CA%2BOCxoyjZhV9stFMAQ-QhHuA0%2BdLQD5XD_YT%2BQo2vY0GhkBKFw%40mail.gmail.com\",\n        children: \"interest\"\n      }), \" in including our Schema Diff CLI in the official pgAdmin. We'll be working with them to include this change upstream to benefit the whole community.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"self-hosting\",\n      children: \"Self Hosting\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Finally, we are adding one critical command to our CLI for everybody who wants to self-host:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"supabase \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"eject\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This gives you everything you need to run the Supabase stack.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"After running the command inside the terminal, you will see three items:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.code, {\n          children: \"docker-compose.yml\"\n        }), \" (file)\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.code, {\n          children: \"kong\"\n        }), \" (directory)\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.code, {\n          children: \"postgres\"\n        }), \" (directory)\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"If you have an existing Postgres database running elsewhere you can easily drop the Postgres directory but first make sure you do these three things:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"run the .sql files from the Postgres directory on your existing database\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"update all references to the DB URI in \", _jsx(_components.code, {\n          children: \"docker-compose.yml\"\n        }), \" to your existing database\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"run \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/realtime#server\",\n          children: \"these steps\"\n        }), \" to enable replication inside the database, so that the realtime engine can stream changes from your database\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You may also want to play with the environment variables for each application inside \", _jsx(_components.code, {\n        children: \"docker-compose.yml\"\n      }), \". \", _jsx(_components.a, {\n        href: \"https://postgrest.org/en/v7.0.0/configuration.html\",\n        children: \"PostgREST\"\n      }), \" has many additional configuration options, as does \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/gotrue#configuration\",\n        children: \"GoTrue\"\n      }), \". In the hosted version of Supabase we connect our own SMTP service to GoTrue for sending auth emails, so you may also want to add these settings here in order to enable this.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Also check \", _jsx(_components.code, {\n        children: \"kong.yml\"\n      }), \" inside the \", _jsx(_components.code, {\n        children: \"kong\"\n      }), \" directory where you'll see how all the services are routed to, and with what rules, down the bottom you'll find the JWTs capable of accessing services that require API Key access.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Once you're all set, you can start the stack by running:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"docker \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"compose up\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Head over to the \", _jsx(_components.a, {\n        href: \"/docs/guides/self-hosting\",\n        children: \"Self Hosting Docs\"\n      }), \" for a more complete walk through, it also includes several \", _jsx(_components.a, {\n        href: \"/docs/guides/self-hosting#one-click-deploys\",\n        children: \"one-click deploys\"\n      }), \", so you can easily deploy into your own cloud hosting provider.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you require any assistance feel free to reach out in our \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/supabase/discussions\",\n        children: \"github discussions\"\n      }), \" or at \", _jsx(_components.a, {\n        href: \"mailto:<EMAIL>\",\n        children: \"<EMAIL>\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Check out the CLI VI \", _jsx(_components.a, {\n        href: \"/blog/supabase-cli-v1-and-admin-api-beta\",\n        children: \"launched Monday 15th August, 2022\"\n      }), \", contribute to the \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/cli\",\n        children: \"CLI repo\"\n      }), \", or go here for the \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard\",\n        children: \"hosted version\"\n      }), \".\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"Running Supabase Locally","slug":"running-supabase-locally","lvl":2,"i":0,"seen":0},{"content":"Next steps:","slug":"next-steps","lvl":3,"i":1,"seen":0},{"content":"Migrations","slug":"migrations","lvl":2,"i":2,"seen":0},{"content":"Diffs","slug":"diffs","lvl":3,"i":3,"seen":0},{"content":"Choosing the best diff tool","slug":"choosing-the-best-diff-tool","lvl":3,"i":4,"seen":0},{"content":"Next steps","slug":"next-steps","lvl":3,"i":5,"seen":0},{"content":"Self Hosting","slug":"self-hosting","lvl":2,"i":6,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,2],"level":0},{"type":"inline","content":"**UPDATE 15/08/2022:** [Supabase CLI V1 is Generally Available](/blog/supabase-cli-v1-and-admin-api-beta) and we also released a Management API (in beta).","level":1,"lines":[1,2],"children":[{"type":"strong_open","level":0},{"type":"text","content":"UPDATE 15/08/2022:","level":1},{"type":"strong_close","level":0},{"type":"text","content":" ","level":0},{"type":"link_open","href":"/blog/supabase-cli-v1-and-admin-api-beta","title":"","level":0},{"type":"text","content":"Supabase CLI V1 is Generally Available","level":1},{"type":"link_close","level":0},{"type":"text","content":" and we also released a Management API (in beta).","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[3,4],"level":0},{"type":"inline","content":"Today is Day 3 of [Launch Week](/blog/launch-week), and as promised - we're releasing our CLI.","level":1,"lines":[3,4],"children":[{"type":"text","content":"Today is Day 3 of ","level":0},{"type":"link_open","href":"/blog/launch-week","title":"","level":0},{"type":"text","content":"Launch Week","level":1},{"type":"link_close","level":0},{"type":"text","content":", and as promised - we're releasing our CLI.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[5,6],"level":0},{"type":"inline","content":"This is the first step in a long journey of features we plan to deliver:","level":1,"lines":[5,6],"children":[{"type":"text","content":"This is the first step in a long journey of features we plan to deliver:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[7,14],"level":0},{"type":"list_item_open","lines":[7,8],"level":1},{"type":"paragraph_open","tight":true,"lines":[7,8],"level":2},{"type":"inline","content":"Running Supabase locally","level":3,"lines":[7,8],"children":[{"type":"text","content":"Running Supabase locally","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[8,9],"level":1},{"type":"paragraph_open","tight":true,"lines":[8,9],"level":2},{"type":"inline","content":"Managing database migrations","level":3,"lines":[8,9],"children":[{"type":"text","content":"Managing database migrations","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[9,10],"level":1},{"type":"paragraph_open","tight":true,"lines":[9,10],"level":2},{"type":"inline","content":"Generating types directly from your database schema","level":3,"lines":[9,10],"children":[{"type":"text","content":"Generating types directly from your database schema","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[10,11],"level":1},{"type":"paragraph_open","tight":true,"lines":[10,11],"level":2},{"type":"inline","content":"Generating API and validation schemas from your database","level":3,"lines":[10,11],"children":[{"type":"text","content":"Generating API and validation schemas from your database","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[11,12],"level":1},{"type":"paragraph_open","tight":true,"lines":[11,12],"level":2},{"type":"inline","content":"Managing your Supabase projects","level":3,"lines":[11,12],"children":[{"type":"text","content":"Managing your Supabase projects","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[12,14],"level":1},{"type":"paragraph_open","tight":true,"lines":[12,13],"level":2},{"type":"inline","content":"Pushing your local changes to production","level":3,"lines":[12,13],"children":[{"type":"text","content":"Pushing your local changes to production","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[14,15],"level":0},{"type":"inline","content":"Here are some of the items we have completed so far.","level":1,"lines":[14,15],"children":[{"type":"text","content":"Here are some of the items we have completed so far.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[16,17],"level":0},{"type":"inline","content":"[Running Supabase Locally](#running-supabase-locally)","level":1,"lines":[16,17],"children":[{"type":"text","content":"Running Supabase Locally","level":0}],"lvl":2,"i":0,"seen":0,"slug":"running-supabase-locally"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[18,19],"level":0},{"type":"inline","content":"You can now run Supabase on your local machine, using Docker Compose. This Docker setup is 100% compatible with every project on Supabase - the tools used for local development are exactly the same as production.","level":1,"lines":[18,19],"children":[{"type":"text","content":"You can now run Supabase on your local machine, using Docker Compose. This Docker setup is 100% compatible with every project on Supabase - the tools used for local development are exactly the same as production.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[20,21],"level":0},{"type":"inline","content":"We have released a full set of documentation [here](/docs/guides/local-development). In this post we thought it would be useful to highlight how easy it is to get started.","level":1,"lines":[20,21],"children":[{"type":"text","content":"We have released a full set of documentation ","level":0},{"type":"link_open","href":"/docs/guides/local-development","title":"","level":0},{"type":"text","content":"here","level":1},{"type":"link_close","level":0},{"type":"text","content":". In this post we thought it would be useful to highlight how easy it is to get started.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[22,23],"level":0},{"type":"inline","content":"A lot of Supabase developers are familiar with React, so here are the steps you would use to create a new React project which uses Supabase as a backend.","level":1,"lines":[22,23],"children":[{"type":"text","content":"A lot of Supabase developers are familiar with React, so here are the steps you would use to create a new React project which uses Supabase as a backend.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[24,25],"level":0},{"type":"inline","content":"Install the CLI:","level":1,"lines":[24,25],"children":[{"type":"text","content":"Install the CLI:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"bash","content":"npm install -g supabase\n","lines":[26,29],"level":0},{"type":"paragraph_open","tight":false,"lines":[30,31],"level":0},{"type":"inline","content":"Set up your React app:","level":1,"lines":[30,31],"children":[{"type":"text","content":"Set up your React app:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"bash","content":"# create a fresh React app\nnpx create-react-app react-demo --use-npm\n\n# move into the new folder\ncd react-demo\n\n# Save the install supabase-js library\nnpm install --save @supabase/supabase-js\n","lines":[32,42],"level":0},{"type":"paragraph_open","tight":false,"lines":[43,44],"level":0},{"type":"inline","content":"Set up Supabase:","level":1,"lines":[43,44],"children":[{"type":"text","content":"Set up Supabase:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"bash","content":"supabase init\n\n# ✔ Port for Supabase URL: · 8000\n# ✔ Port for PostgreSQL database: · 5432\n# ✔ Project initialized.\n# Supabase URL: http://localhost:8000\n# Supabase Key (anon, public): eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsImlhdCI6MTYwMzk2ODgzNCwiZXhwIjoyNTUwNjUzNjM0LCJyb2xlIjoiYW5vbiJ9.36fUebxgx1mcBo4s19v0SzqmzunP--hm_hep0uLX0ew\n# Supabase Key (service_role, private): eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsImlhdCI6MTYwMzk2ODgzNCwiZXhwIjoyNTUwNjUzNjM0LCJyb2xlIjoiYW5vbiJ9.36fUebxgx1mcBo4s19v0SzqmzunP--hm_hep0uLX0ew\n# Database URL: postgres://postgres:postgres@localhost:5432/postgres\n","lines":[45,56],"level":0},{"type":"paragraph_open","tight":false,"lines":[57,58],"level":0},{"type":"inline","content":"Now that your application is now prepared, you can use Supabase anywhere in your application (for example, `App.js`):","level":1,"lines":[57,58],"children":[{"type":"text","content":"Now that your application is now prepared, you can use Supabase anywhere in your application (for example, ","level":0},{"type":"code","content":"App.js","block":false,"level":0},{"type":"text","content":"):","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"jsx","content":"import { createClient } from '@supabase/supabase-js'\nconst SUPABASE_URL = 'http://localhost:8000'\nconst SUPABASE_ANON_KEY =\n  'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsImlhdCI6MTYwMzk2ODgzNCwiZXhwIjoyNTUwNjUzNjM0LCJyb2xlIjoiYW5vbiJ9.36fUebxgx1mcBo4s19v0SzqmzunP--hm_hep0uLX0ew'\nconst supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)\n","lines":[59,66],"level":0},{"type":"paragraph_open","tight":false,"lines":[67,68],"level":0},{"type":"inline","content":"Then start the backend and the frontend:","level":1,"lines":[67,68],"children":[{"type":"text","content":"Then start the backend and the frontend:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"bash","content":"supabase start  # Start Supabase\nnpm start       # Start the React app\n","lines":[69,73],"level":0},{"type":"paragraph_open","tight":false,"lines":[74,75],"level":0},{"type":"inline","content":"If everything is working you should have a React app running on `http://localhost:3000` and Supabase services running on `http://localhost:8000`!","level":1,"lines":[74,75],"children":[{"type":"text","content":"If everything is working you should have a React app running on ","level":0},{"type":"code","content":"http://localhost:3000","block":false,"level":0},{"type":"text","content":" and Supabase services running on ","level":0},{"type":"code","content":"http://localhost:8000","block":false,"level":0},{"type":"text","content":"!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[76,77],"level":0},{"type":"inline","content":"[Next steps:](#next-steps)","level":1,"lines":[76,77],"children":[{"type":"text","content":"Next steps:","level":0}],"lvl":3,"i":1,"seen":0,"slug":"next-steps"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[78,79],"level":0},{"type":"inline","content":"Soon we will give you the ability to push your changes from your local machine to your Production project. How will we do that? Migrations!","level":1,"lines":[78,79],"children":[{"type":"text","content":"Soon we will give you the ability to push your changes from your local machine to your Production project. How will we do that? Migrations!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[80,81],"level":0},{"type":"inline","content":"[Migrations](#migrations)","level":1,"lines":[80,81],"children":[{"type":"text","content":"Migrations","level":0}],"lvl":2,"i":2,"seen":0,"slug":"migrations"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[82,83],"level":0},{"type":"inline","content":"Database Migrations are a process to \"change\" your database schema. In a NoSQL database you don't need migrations, because you can insert any JSON data without validation. However with Relational databases you define your schema upfront, and the database will reject data which doesn't \"fit\" the schema. This is one of the reasons Relational databases are so scalable - schemas ensure data integrity.","level":1,"lines":[82,83],"children":[{"type":"text","content":"Database Migrations are a process to \"change\" your database schema. In a NoSQL database you don't need migrations, because you can insert any JSON data without validation. However with Relational databases you define your schema upfront, and the database will reject data which doesn't \"fit\" the schema. This is one of the reasons Relational databases are so scalable - schemas ensure data integrity.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[84,85],"level":0},{"type":"inline","content":"Just like an application though, a database schema needs to be constantly updated. And that's where migrations fit! Migrations are simply a set of SQL scripts which change your database schema.","level":1,"lines":[84,85],"children":[{"type":"text","content":"Just like an application though, a database schema needs to be constantly updated. And that's where migrations fit! Migrations are simply a set of SQL scripts which change your database schema.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[86,87],"level":0},{"type":"inline","content":"There is one problem however: there is [no \"right\" way](https://news.ycombinator.com/item?id=21405501) to do migrations.","level":1,"lines":[86,87],"children":[{"type":"text","content":"There is one problem however: there is ","level":0},{"type":"link_open","href":"https://news.ycombinator.com/item?id=21405501","title":"","level":0},{"type":"text","content":"no \"right\" way","level":1},{"type":"link_close","level":0},{"type":"text","content":" to do migrations.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[88,89],"level":0},{"type":"inline","content":"[Diffs](#diffs)","level":1,"lines":[88,89],"children":[{"type":"text","content":"Diffs","level":0}],"lvl":3,"i":3,"seen":0,"slug":"diffs"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[90,91],"level":0},{"type":"inline","content":"At Supabase, we do have one strong preference. We like schema \"diffing\" over \"manual migrations\".","level":1,"lines":[90,91],"children":[{"type":"text","content":"At Supabase, we do have one strong preference. We like schema \"diffing\" over \"manual migrations\".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[92,93],"level":0},{"type":"inline","content":"Manual migrations work like this:","level":1,"lines":[92,93],"children":[{"type":"text","content":"Manual migrations work like this:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[94,98],"level":0},{"type":"list_item_open","lines":[94,95],"level":1},{"type":"paragraph_open","tight":true,"lines":[94,95],"level":2},{"type":"inline","content":"A developer thinks about all the changes they want to make to their database","level":3,"lines":[94,95],"children":[{"type":"text","content":"A developer thinks about all the changes they want to make to their database","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[95,96],"level":1},{"type":"paragraph_open","tight":true,"lines":[95,96],"level":2},{"type":"inline","content":"They create a SQL script which will cause those changes","level":3,"lines":[95,96],"children":[{"type":"text","content":"They create a SQL script which will cause those changes","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[96,98],"level":1},{"type":"paragraph_open","tight":true,"lines":[96,97],"level":2},{"type":"inline","content":"They run that script on their database","level":3,"lines":[96,97],"children":[{"type":"text","content":"They run that script on their database","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[98,99],"level":0},{"type":"inline","content":"After a while though, these scripts pile up - the `migrations` folder can contain hundreds of migration scripts. This method is also \"version control\" on top of another version control system (i.e. git).","level":1,"lines":[98,99],"children":[{"type":"text","content":"After a while though, these scripts pile up - the ","level":0},{"type":"code","content":"migrations","block":false,"level":0},{"type":"text","content":" folder can contain hundreds of migration scripts. This method is also \"version control\" on top of another version control system (i.e. git).","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[100,101],"level":0},{"type":"inline","content":"A \"diff\" tool works like this:","level":1,"lines":[100,101],"children":[{"type":"text","content":"A \"diff\" tool works like this:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[102,106],"level":0},{"type":"list_item_open","lines":[102,103],"level":1},{"type":"paragraph_open","tight":true,"lines":[102,103],"level":2},{"type":"inline","content":"a developer makes all the changes they desire to a local database","level":3,"lines":[102,103],"children":[{"type":"text","content":"a developer makes all the changes they desire to a local database","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[103,104],"level":1},{"type":"paragraph_open","tight":true,"lines":[103,104],"level":2},{"type":"inline","content":"they use a tool to compare their local database to the production database","level":3,"lines":[103,104],"children":[{"type":"text","content":"they use a tool to compare their local database to the production database","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[104,106],"level":1},{"type":"paragraph_open","tight":true,"lines":[104,105],"level":2},{"type":"inline","content":"the tool then generates all the SQL scripts that are required, and runs them on the target database","level":3,"lines":[104,105],"children":[{"type":"text","content":"the tool then generates all the SQL scripts that are required, and runs them on the target database","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[106,107],"level":0},{"type":"inline","content":"In this case, the tool does all the hard work. This is obviously an ideal state. Databases schemas are declarative, and when you check them into git, you can see their evolution over time. The hard part is finding a tool which can handle all the edge-cases of database diff'ing.","level":1,"lines":[106,107],"children":[{"type":"text","content":"In this case, the tool does all the hard work. This is obviously an ideal state. Databases schemas are declarative, and when you check them into git, you can see their evolution over time. The hard part is finding a tool which can handle all the edge-cases of database diff'ing.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[108,109],"level":0},{"type":"inline","content":"[Choosing the best diff tool](#choosing-the-best-diff-tool)","level":1,"lines":[108,109],"children":[{"type":"text","content":"Choosing the best diff tool","level":0}],"lvl":3,"i":4,"seen":0,"slug":"choosing-the-best-diff-tool"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[110,111],"level":0},{"type":"inline","content":"After evaluating these OSS tools:","level":1,"lines":[110,111],"children":[{"type":"text","content":"After evaluating these OSS tools:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[112,119],"level":0},{"type":"list_item_open","lines":[112,113],"level":1},{"type":"paragraph_open","tight":true,"lines":[112,113],"level":2},{"type":"inline","content":"[migra](https://github.com/djrobstep/migra) (Python)","level":3,"lines":[112,113],"children":[{"type":"link_open","href":"https://github.com/djrobstep/migra","title":"","level":0},{"type":"text","content":"migra","level":1},{"type":"link_close","level":0},{"type":"text","content":" (Python)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[113,114],"level":1},{"type":"paragraph_open","tight":true,"lines":[113,114],"level":2},{"type":"inline","content":"[dbdiff](https://github.com/gimenete/dbdiff) (JS)","level":3,"lines":[113,114],"children":[{"type":"link_open","href":"https://github.com/gimenete/dbdiff","title":"","level":0},{"type":"text","content":"dbdiff","level":1},{"type":"link_close","level":0},{"type":"text","content":" (JS)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[114,115],"level":1},{"type":"paragraph_open","tight":true,"lines":[114,115],"level":2},{"type":"inline","content":"[pgdiff](https://github.com/joncrlsn/pgdiff) (Go)","level":3,"lines":[114,115],"children":[{"type":"link_open","href":"https://github.com/joncrlsn/pgdiff","title":"","level":0},{"type":"text","content":"pgdiff","level":1},{"type":"link_close","level":0},{"type":"text","content":" (Go)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[115,116],"level":1},{"type":"paragraph_open","tight":true,"lines":[115,116],"level":2},{"type":"inline","content":"[apgdiff](https://github.com/fordfrog/apgdiff) (Java)","level":3,"lines":[115,116],"children":[{"type":"link_open","href":"https://github.com/fordfrog/apgdiff","title":"","level":0},{"type":"text","content":"apgdiff","level":1},{"type":"link_close","level":0},{"type":"text","content":" (Java)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[116,117],"level":1},{"type":"paragraph_open","tight":true,"lines":[116,117],"level":2},{"type":"inline","content":"[pgquarrel](https://github.com/eulerto/pgquarrel) (C)","level":3,"lines":[116,117],"children":[{"type":"link_open","href":"https://github.com/eulerto/pgquarrel","title":"","level":0},{"type":"text","content":"pgquarrel","level":1},{"type":"link_close","level":0},{"type":"text","content":" (C)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[117,119],"level":1},{"type":"paragraph_open","tight":true,"lines":[117,118],"level":2},{"type":"inline","content":"[pgAdmin Schema Diff](https://www.pgadmin.org/docs/pgadmin4/development/schema_diff.html) (Python)","level":3,"lines":[117,118],"children":[{"type":"link_open","href":"https://www.pgadmin.org/docs/pgadmin4/development/schema_diff.html","title":"","level":0},{"type":"text","content":"pgAdmin Schema Diff","level":1},{"type":"link_close","level":0},{"type":"text","content":" (Python)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[119,120],"level":0},{"type":"inline","content":"We found that the most complete one was the pgAdmin Schema Diff, migra came a close second.","level":1,"lines":[119,120],"children":[{"type":"text","content":"We found that the most complete one was the pgAdmin Schema Diff, migra came a close second.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[121,122],"level":0},{"type":"inline","content":"The deciding factor was if the tool could track an owner change for a VIEW.","level":1,"lines":[121,122],"children":[{"type":"text","content":"The deciding factor was if the tool could track an owner change for a VIEW.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql","content":"ALTER VIEW my_view OWNER TO authenticated;\n","lines":[123,126],"level":0},{"type":"paragraph_open","tight":false,"lines":[127,128],"level":0},{"type":"inline","content":"This is critical for Row Level Security to work with views. For policies to kick in on views, the owner must not have `superuser` or `bypassrls` privileges. Currently migra doesn't track this change ([issue](https://github.com/djrobstep/migra/issues/160)), while the pgAdmin Schema Diff does.","level":1,"lines":[127,128],"children":[{"type":"text","content":"This is critical for Row Level Security to work with views. For policies to kick in on views, the owner must not have ","level":0},{"type":"code","content":"superuser","block":false,"level":0},{"type":"text","content":" or ","level":0},{"type":"code","content":"bypassrls","block":false,"level":0},{"type":"text","content":" privileges. Currently migra doesn't track this change (","level":0},{"type":"link_open","href":"https://github.com/djrobstep/migra/issues/160","title":"","level":0},{"type":"text","content":"issue","level":1},{"type":"link_close","level":0},{"type":"text","content":"), while the pgAdmin Schema Diff does.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[129,130],"level":0},{"type":"inline","content":"There was a problem in using the [pgAdmin Schema Diff](https://www.pgadmin.org/docs/pgadmin4/development/schema_diff.html) though, it's a GUI-only tool.","level":1,"lines":[129,130],"children":[{"type":"text","content":"There was a problem in using the ","level":0},{"type":"link_open","href":"https://www.pgadmin.org/docs/pgadmin4/development/schema_diff.html","title":"","level":0},{"type":"text","content":"pgAdmin Schema Diff","level":1},{"type":"link_close","level":0},{"type":"text","content":" though, it's a GUI-only tool.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[131,132],"level":0},{"type":"inline","content":"![pgadmin diff](/images/blog/cli/pgadmin-diff.png)","level":1,"lines":[131,132],"children":[{"type":"image","src":"/images/blog/cli/pgadmin-diff.png","title":"","alt":"pgadmin diff","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[133,134],"level":0},{"type":"inline","content":"So we did what we always strive to do - improve existing open source software. We created a CLI mode for the Schema Diff on [our repo](https://github.com/supabase/pgadmin4/blob/cli/web/cli.py). We've also released a [docker image](https://hub.docker.com/r/supabase/pgadmin-schema-diff) for a quick start.","level":1,"lines":[133,134],"children":[{"type":"text","content":"So we did what we always strive to do - improve existing open source software. We created a CLI mode for the Schema Diff on ","level":0},{"type":"link_open","href":"https://github.com/supabase/pgadmin4/blob/cli/web/cli.py","title":"","level":0},{"type":"text","content":"our repo","level":1},{"type":"link_close","level":0},{"type":"text","content":". We've also released a ","level":0},{"type":"link_open","href":"https://hub.docker.com/r/supabase/pgadmin-schema-diff","title":"","level":0},{"type":"text","content":"docker image","level":1},{"type":"link_close","level":0},{"type":"text","content":" for a quick start.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[135,136],"level":0},{"type":"inline","content":"The CLI offers the same functionality as the GUI version. You can diff two databases by specifying the connection strings like shown below.","level":1,"lines":[135,136],"children":[{"type":"text","content":"The CLI offers the same functionality as the GUI version. You can diff two databases by specifying the connection strings like shown below.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"bash","content":"docker run supabase/pgadmin-schema-diff \\\n  '*******************************/diff_source' \\\n  '************************************/diff_target' \\\n  \u003e diff_demo.sql\n\nStarting schema diff...\nComparison started......0%\nComparing Event Triggers...2%\nComparing Extensions...4%\nComparing Languages...8%\nComparing Foreign Servers...14%\nComparing Foreign Tables of schema 'public'...28%\nComparing Tables of schema 'public'...50%\nComparing Domains of schema 'test_schema_diff'...66%\nComparing Foreign Tables of schema 'test_schema_diff'...68%\nComparing FTS Templates of schema 'test_schema_diff'...76%\nComparing Functions of schema 'test_schema_diff'...78%\nComparing Procedures of schema 'test_schema_diff'...80%\nComparing Tables of schema 'test_schema_diff'...90%\nComparing Types of schema 'test_schema_diff'...92%\nComparing Materialized Views of schema 'test_schema_diff'...96%\nDone.\n\n## the diff is written to diff_demo.sql\n","lines":[137,163],"level":0},{"type":"paragraph_open","tight":false,"lines":[164,165],"level":0},{"type":"inline","content":"A sample diff can be seen on this [gist](https://gist.github.com/steve-chavez/3f286a233806aeee0bcea4a47f97f0b5). This was generated by diffing these [two databases](https://github.com/supabase/pgadmin4/tree/cli/web/pgadmin/tools/schema_diff/tests/pg/10_plus).","level":1,"lines":[164,165],"children":[{"type":"text","content":"A sample diff can be seen on this ","level":0},{"type":"link_open","href":"https://gist.github.com/steve-chavez/3f286a233806aeee0bcea4a47f97f0b5","title":"","level":0},{"type":"text","content":"gist","level":1},{"type":"link_close","level":0},{"type":"text","content":". This was generated by diffing these ","level":0},{"type":"link_open","href":"https://github.com/supabase/pgadmin4/tree/cli/web/pgadmin/tools/schema_diff/tests/pg/10_plus","title":"","level":0},{"type":"text","content":"two databases","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[166,167],"level":0},{"type":"inline","content":"On these [lines](https://gist.github.com/steve-chavez/3f286a233806aeee0bcea4a47f97f0b5#file-diff_demo-sql-L1022-L1023), you can see how it tracks the view's owner change (note: the `ALTER TABLE` statement is interchangeable with `ALTER VIEW` in this case). Additionally, you can see that it handles [domains](https://gist.github.com/steve-chavez/3f286a233806aeee0bcea4a47f97f0b5#file-diff_demo-sql-L278-L287) just fine, this is an edge-case that other diff tools don't handle.","level":1,"lines":[166,167],"children":[{"type":"text","content":"On these ","level":0},{"type":"link_open","href":"https://gist.github.com/steve-chavez/3f286a233806aeee0bcea4a47f97f0b5#file-diff_demo-sql-L1022-L1023","title":"","level":0},{"type":"text","content":"lines","level":1},{"type":"link_close","level":0},{"type":"text","content":", you can see how it tracks the view's owner change (note: the ","level":0},{"type":"code","content":"ALTER TABLE","block":false,"level":0},{"type":"text","content":" statement is interchangeable with ","level":0},{"type":"code","content":"ALTER VIEW","block":false,"level":0},{"type":"text","content":" in this case). Additionally, you can see that it handles ","level":0},{"type":"link_open","href":"https://gist.github.com/steve-chavez/3f286a233806aeee0bcea4a47f97f0b5#file-diff_demo-sql-L278-L287","title":"","level":0},{"type":"text","content":"domains","level":1},{"type":"link_close","level":0},{"type":"text","content":" just fine, this is an edge-case that other diff tools don't handle.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[168,169],"level":0},{"type":"inline","content":"Also, similarly to the pgAdmin GUI:","level":1,"lines":[168,169],"children":[{"type":"text","content":"Also, similarly to the pgAdmin GUI:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[170,173],"level":0},{"type":"list_item_open","lines":[170,171],"level":1},{"type":"paragraph_open","tight":true,"lines":[170,171],"level":2},{"type":"inline","content":"You can include and exclude database objects from the diff with `--include-objects` or `--exclude-objects`","level":3,"lines":[170,171],"children":[{"type":"text","content":"You can include and exclude database objects from the diff with ","level":0},{"type":"code","content":"--include-objects","block":false,"level":0},{"type":"text","content":" or ","level":0},{"type":"code","content":"--exclude-objects","block":false,"level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[171,173],"level":1},{"type":"paragraph_open","tight":true,"lines":[171,172],"level":2},{"type":"inline","content":"You can choose a single schema to diff with the `--schema` argument or you can pick different schemas to compare with the --source-schema and --target-schema arguments. We recommend you do this for Supabase databases. Diffing the whole database can take a while because of the `extensions` schema (especially if you enable PostGIS, which adds many functions).","level":3,"lines":[171,172],"children":[{"type":"text","content":"You can choose a single schema to diff with the ","level":0},{"type":"code","content":"--schema","block":false,"level":0},{"type":"text","content":" argument or you can pick different schemas to compare with the --source-schema and --target-schema arguments. We recommend you do this for Supabase databases. Diffing the whole database can take a while because of the ","level":0},{"type":"code","content":"extensions","block":false,"level":0},{"type":"text","content":" schema (especially if you enable PostGIS, which adds many functions).","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":3,"lines":[173,174],"level":0},{"type":"inline","content":"[Next steps](#next-steps)","level":1,"lines":[173,174],"children":[{"type":"text","content":"Next steps","level":0}],"lvl":3,"i":5,"seen":0,"slug":"next-steps"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[175,176],"level":0},{"type":"inline","content":"Once we have added logins to the CLI, we will be able to use Migrations to create a seamless workflow between local development and your production database.","level":1,"lines":[175,176],"children":[{"type":"text","content":"Once we have added logins to the CLI, we will be able to use Migrations to create a seamless workflow between local development and your production database.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[177,178],"level":0},{"type":"inline","content":"Also, the pgAdmin team has showed [interest](https://www.postgresql.org/message-id/CA%2BOCxoyjZhV9stFMAQ-QhHuA0%2BdLQD5XD_YT%2BQo2vY0GhkBKFw%40mail.gmail.com) in including our Schema Diff CLI in the official pgAdmin. We'll be working with them to include this change upstream to benefit the whole community.","level":1,"lines":[177,178],"children":[{"type":"text","content":"Also, the pgAdmin team has showed ","level":0},{"type":"link_open","href":"https://www.postgresql.org/message-id/CA%2BOCxoyjZhV9stFMAQ-QhHuA0%2BdLQD5XD_YT%2BQo2vY0GhkBKFw%40mail.gmail.com","title":"","level":0},{"type":"text","content":"interest","level":1},{"type":"link_close","level":0},{"type":"text","content":" in including our Schema Diff CLI in the official pgAdmin. We'll be working with them to include this change upstream to benefit the whole community.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[179,180],"level":0},{"type":"inline","content":"[Self Hosting](#self-hosting)","level":1,"lines":[179,180],"children":[{"type":"text","content":"Self Hosting","level":0}],"lvl":2,"i":6,"seen":0,"slug":"self-hosting"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[181,182],"level":0},{"type":"inline","content":"Finally, we are adding one critical command to our CLI for everybody who wants to self-host:","level":1,"lines":[181,182],"children":[{"type":"text","content":"Finally, we are adding one critical command to our CLI for everybody who wants to self-host:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"bash","content":"supabase eject\n","lines":[183,186],"level":0},{"type":"paragraph_open","tight":false,"lines":[187,188],"level":0},{"type":"inline","content":"This gives you everything you need to run the Supabase stack.","level":1,"lines":[187,188],"children":[{"type":"text","content":"This gives you everything you need to run the Supabase stack.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[189,190],"level":0},{"type":"inline","content":"After running the command inside the terminal, you will see three items:","level":1,"lines":[189,190],"children":[{"type":"text","content":"After running the command inside the terminal, you will see three items:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[191,195],"level":0},{"type":"list_item_open","lines":[191,192],"level":1},{"type":"paragraph_open","tight":true,"lines":[191,192],"level":2},{"type":"inline","content":"`docker-compose.yml` (file)","level":3,"lines":[191,192],"children":[{"type":"code","content":"docker-compose.yml","block":false,"level":0},{"type":"text","content":" (file)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[192,193],"level":1},{"type":"paragraph_open","tight":true,"lines":[192,193],"level":2},{"type":"inline","content":"`kong` (directory)","level":3,"lines":[192,193],"children":[{"type":"code","content":"kong","block":false,"level":0},{"type":"text","content":" (directory)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[193,195],"level":1},{"type":"paragraph_open","tight":true,"lines":[193,194],"level":2},{"type":"inline","content":"`postgres` (directory)","level":3,"lines":[193,194],"children":[{"type":"code","content":"postgres","block":false,"level":0},{"type":"text","content":" (directory)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[195,196],"level":0},{"type":"inline","content":"If you have an existing Postgres database running elsewhere you can easily drop the Postgres directory but first make sure you do these three things:","level":1,"lines":[195,196],"children":[{"type":"text","content":"If you have an existing Postgres database running elsewhere you can easily drop the Postgres directory but first make sure you do these three things:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[197,201],"level":0},{"type":"list_item_open","lines":[197,198],"level":1},{"type":"paragraph_open","tight":true,"lines":[197,198],"level":2},{"type":"inline","content":"run the .sql files from the Postgres directory on your existing database","level":3,"lines":[197,198],"children":[{"type":"text","content":"run the .sql files from the Postgres directory on your existing database","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[198,199],"level":1},{"type":"paragraph_open","tight":true,"lines":[198,199],"level":2},{"type":"inline","content":"update all references to the DB URI in `docker-compose.yml` to your existing database","level":3,"lines":[198,199],"children":[{"type":"text","content":"update all references to the DB URI in ","level":0},{"type":"code","content":"docker-compose.yml","block":false,"level":0},{"type":"text","content":" to your existing database","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[199,201],"level":1},{"type":"paragraph_open","tight":true,"lines":[199,200],"level":2},{"type":"inline","content":"run [these steps](https://github.com/supabase/realtime#server) to enable replication inside the database, so that the realtime engine can stream changes from your database","level":3,"lines":[199,200],"children":[{"type":"text","content":"run ","level":0},{"type":"link_open","href":"https://github.com/supabase/realtime#server","title":"","level":0},{"type":"text","content":"these steps","level":1},{"type":"link_close","level":0},{"type":"text","content":" to enable replication inside the database, so that the realtime engine can stream changes from your database","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[201,202],"level":0},{"type":"inline","content":"You may also want to play with the environment variables for each application inside `docker-compose.yml`. [PostgREST](https://postgrest.org/en/v7.0.0/configuration.html) has many additional configuration options, as does [GoTrue](https://github.com/supabase/gotrue#configuration). In the hosted version of Supabase we connect our own SMTP service to GoTrue for sending auth emails, so you may also want to add these settings here in order to enable this.","level":1,"lines":[201,202],"children":[{"type":"text","content":"You may also want to play with the environment variables for each application inside ","level":0},{"type":"code","content":"docker-compose.yml","block":false,"level":0},{"type":"text","content":". ","level":0},{"type":"link_open","href":"https://postgrest.org/en/v7.0.0/configuration.html","title":"","level":0},{"type":"text","content":"PostgREST","level":1},{"type":"link_close","level":0},{"type":"text","content":" has many additional configuration options, as does ","level":0},{"type":"link_open","href":"https://github.com/supabase/gotrue#configuration","title":"","level":0},{"type":"text","content":"GoTrue","level":1},{"type":"link_close","level":0},{"type":"text","content":". In the hosted version of Supabase we connect our own SMTP service to GoTrue for sending auth emails, so you may also want to add these settings here in order to enable this.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[203,204],"level":0},{"type":"inline","content":"Also check `kong.yml` inside the `kong` directory where you'll see how all the services are routed to, and with what rules, down the bottom you'll find the JWTs capable of accessing services that require API Key access.","level":1,"lines":[203,204],"children":[{"type":"text","content":"Also check ","level":0},{"type":"code","content":"kong.yml","block":false,"level":0},{"type":"text","content":" inside the ","level":0},{"type":"code","content":"kong","block":false,"level":0},{"type":"text","content":" directory where you'll see how all the services are routed to, and with what rules, down the bottom you'll find the JWTs capable of accessing services that require API Key access.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[205,206],"level":0},{"type":"inline","content":"Once you're all set, you can start the stack by running:","level":1,"lines":[205,206],"children":[{"type":"text","content":"Once you're all set, you can start the stack by running:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"bash","content":"docker compose up\n","lines":[207,210],"level":0},{"type":"paragraph_open","tight":false,"lines":[211,212],"level":0},{"type":"inline","content":"Head over to the [Self Hosting Docs](/docs/guides/self-hosting) for a more complete walk through, it also includes several [one-click deploys](/docs/guides/self-hosting#one-click-deploys), so you can easily deploy into your own cloud hosting provider.","level":1,"lines":[211,212],"children":[{"type":"text","content":"Head over to the ","level":0},{"type":"link_open","href":"/docs/guides/self-hosting","title":"","level":0},{"type":"text","content":"Self Hosting Docs","level":1},{"type":"link_close","level":0},{"type":"text","content":" for a more complete walk through, it also includes several ","level":0},{"type":"link_open","href":"/docs/guides/self-hosting#one-click-deploys","title":"","level":0},{"type":"text","content":"one-click deploys","level":1},{"type":"link_close","level":0},{"type":"text","content":", so you can easily deploy into your own cloud hosting provider.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[213,214],"level":0},{"type":"inline","content":"If you require any assistance feel free to reach out in our [github discussions](https://github.com/supabase/supabase/discussions) <NAME_EMAIL>.","level":1,"lines":[213,214],"children":[{"type":"text","content":"If you require any assistance feel free to reach out in our ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase/discussions","title":"","level":0},{"type":"text","content":"github discussions","level":1},{"type":"link_close","level":0},{"type":"text","content":" <NAME_EMAIL>.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[215,216],"level":0},{"type":"inline","content":"Check out the CLI VI [launched Monday 15th August, 2022](/blog/supabase-cli-v1-and-admin-api-beta), contribute to the [CLI repo](https://github.com/supabase/cli), or go here for the [hosted version](https://supabase.com/dashboard).","level":1,"lines":[215,216],"children":[{"type":"text","content":"Check out the CLI VI ","level":0},{"type":"link_open","href":"/blog/supabase-cli-v1-and-admin-api-beta","title":"","level":0},{"type":"text","content":"launched Monday 15th August, 2022","level":1},{"type":"link_close","level":0},{"type":"text","content":", contribute to the ","level":0},{"type":"link_open","href":"https://github.com/supabase/cli","title":"","level":0},{"type":"text","content":"CLI repo","level":1},{"type":"link_close","level":0},{"type":"text","content":", or go here for the ","level":0},{"type":"link_open","href":"https://supabase.com/dashboard","title":"","level":0},{"type":"text","content":"hosted version","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0}],"content":"- [Running Supabase Locally](#running-supabase-locally)\n- [Migrations](#migrations)\n- [Self Hosting](#self-hosting)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-cli"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>