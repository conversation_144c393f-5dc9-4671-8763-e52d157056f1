<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase Beta October 2022</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="New SDKs, quickstarts, Functions tricks, and more. But, more importantly, Launch Week 6️ has a date!" data-next-head=""/><meta property="og:title" content="Supabase Beta October 2022" data-next-head=""/><meta property="og:description" content="New SDKs, quickstarts, Functions tricks, and more. But, more importantly, Launch Week 6️ has a date!" data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-beta-update-october-2022" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2022-11-02" data-next-head=""/><meta property="article:author" content="https://github.com/awalias" data-next-head=""/><meta property="article:tag" content="release-notes" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/2022-october/monthly-update-october-2022.jpg" data-next-head=""/><meta property="og:image:alt" content="Supabase Beta October 2022 thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Supabase Beta October 2022</h1><div class="text-light flex space-x-3 text-sm"><p>02 Nov 2022</p><p>•</p><p>4 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/awalias"><div class="flex items-center gap-3"><div class="w-10"><img alt="Ant Wilson avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fawalias.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fawalias.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fawalias.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Ant Wilson</span><span class="text-foreground-lighter mb-0 text-xs">CTO and Co-Founder</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Supabase Beta October 2022" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-october%2Fmonthly-update-october-2022.jpg&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-october%2Fmonthly-update-october-2022.jpg&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-october%2Fmonthly-update-october-2022.jpg&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-october%2Fmonthly-update-october-2022.jpg&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-october%2Fmonthly-update-october-2022.jpg&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-october%2Fmonthly-update-october-2022.jpg&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-october%2Fmonthly-update-october-2022.jpg&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-october%2Fmonthly-update-october-2022.jpg&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-october%2Fmonthly-update-october-2022.jpg&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-october%2Fmonthly-update-october-2022.jpg&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-october%2Fmonthly-update-october-2022.jpg&amp;w=3840&amp;q=100"/></div><p>During October, we shipped something for everybody: new SDKs, quickstarts, Functions tricks, and more. All are listed here just for you. But, more importantly, Launch Week 6️⃣ has a date!</p>
<h2 id="supabase-js-v2-and-supabase-flutter-v1-released" class="group scroll-mt-24">supabase-js v2 and supabase-flutter v1 released<a href="#supabase-js-v2-and-supabase-flutter-v1-released" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>The new versions of our two most popular SDKs have been fully released. It couldn’t have happened without our amazing community, thanks to everyone involved. Now, it’s time to build 🛠</p>
<p><a href="https://supabase.com/docs/reference/javascript/">Try supabase-js V2</a><br/>
<a href="https://supabase.com/docs/reference/dart">Try flutter-supabase V1</a></p>
<h2 id="new-nextjs-quickstart--nextjs-13-example" class="group scroll-mt-24">New Next.js quickstart &amp; Next.js 13 example<a href="#new-nextjs-quickstart--nextjs-13-example" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>Next.js is all the rage right now 🔥</p>
<p>Next.js Conf raised the bar for dev conferences. We had the honor of being a Gold Sponsor, so we revamped our <a href="https://supabase.com/docs/guides/with-nextjs">Next.js Quickstart guide</a> to include our pre-built Auth UI and <a href="https://supabase.com/docs/guides/auth/auth-helpers/nextjs">Auth Helpers.</a></p>
<p>And Next.js 13 was announced! Making it extremely easy to fetch and cache data from our <a href="https://supabase.com/docs/guides/database/api">Serverless API</a>. So we put together an <a href="https://github.com/supabase/supabase/tree/master/examples/caching/with-nextjs-13">example to try it out</a>.</p>
<h2 id="supabase-auth-and-server-side-rendering" class="group scroll-mt-24">Supabase Auth and Server-Side Rendering<a href="#supabase-auth-and-server-side-rendering" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>The Auth team published an in-depth doc explaining how Supabase Auth supports server-side rendering. Includes an explanation of the authentication flow and answers to some of the more common questions.</p>
<p><a href="https://supabase.com/docs/guides/auth/server-side-rendering">Read the docs</a>.</p>
<h2 id="database-testing-with-pgtap" class="group scroll-mt-24">Database Testing with pgTAP<a href="#database-testing-with-pgtap" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>Do you have 100% code coverage? Probably not, because that’s usually the last thing you think of, but <em>definitely</em> not if you don’t have database tests. We just shipped a framework for Database Tests which makes it incredibly easy to test your database using pgTAP and pg_prove.</p>
<p><a href="https://supabase.com/docs/guides/database/testing">Learn how it works</a>.</p>
<h2 id="edge-functions-update" class="group scroll-mt-24">Edge Functions Update<a href="#edge-functions-update" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>Functions now support GET requests! Other HTTP verbs such as PUT, PATCH, and DELETE are supported too.</p>
<p><a href="https://github.com/supabase/supabase/blob/master/examples/edge-functions/supabase/functions/restful-tasks/index.ts">Check this example</a></p>
<h2 id="quick-product-updates" class="group scroll-mt-24">Quick Product Updates<a href="#quick-product-updates" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ul>
<li>supabase-py 🐍 now has functions support! <a href="https://github.com/supabase-community/supabase-py/pull/179">PR</a></li>
<li>You can now detect users’ location from Edge Functions easily by using <code class="short-inline-codeblock">X-Forwarded-For header</code>. <a href="https://github.com/supabase/supabase/tree/master/examples/edge-functions/supabase/functions/location">Example</a></li>
<li>Return <code class="short-inline-codeblock">provider_refresh_token</code> along with <code class="short-inline-codeblock">provider_access_token</code>. <a href="https://github.com/supabase/gotrue/pull/641">PR</a></li>
<li>Added a <code class="short-inline-codeblock">refreshSession</code> method to allow users to forcefully refresh a session instead of waiting for it to autorefresh upon expiry. Thanks to <a href="https://github.com/j4w8n">@j4w8n</a> for the <a href="https://github.com/supabase/gotrue-js/pull/505">PR</a> 🙇🏻‍♂️</li>
<li>Logging: realtime, storage, postgrest, and pgbouncer released.</li>
<li>Trigger a file download by adding the download query parameter to your storage objects. storage-api. <a href="https://github.com/supabase/storage-api/issues/122">PR</a></li>
</ul>
<h2 id="made-with-supabase" class="group scroll-mt-24">Made with Supabase<a href="#made-with-supabase" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>There were so many amazing apps Made with Supabase this month that we couldn’t choose just one… so here are three:</p>
<p>🚰 <a href="https://twitter.com/SomangshuG/status/1581888060643774465">Remote Water Feeder</a> by Somangshu Goswami <br/>
📊 <a href="https://github.com/decileapp/decile">Decile App</a> by <a href="https://twitter.com/ntkris">Krishna</a> <br/>
📑 <a href="https://www.explainpaper.com/">Explainpaper</a> by <a href="https://twitter.com/amanjha__">Aman Jha</a> and <a href="https://twitter.com/functionofjade">Jade</a></p>
<p>If you want to see +100 other apps, check <a href="https://twitter.com/supabase/status/1585226233742229504">the replies</a>.</p>
<h2 id="extended-community-highlights" class="group scroll-mt-24">Extended Community Highlights<a href="#extended-community-highlights" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<ul>
<li>Mark Buggraf sat down to chat about postgres-wasm with LogRocket. <a href="https://podrocket.logrocket.com/opensourcing-postgres-wasm">Podcast</a></li>
<li>A collection of awesome Supabase resources. <a href="https://github.com/lyqht/awesome-supabase">awesome-supabase</a></li>
<li>All the Happy Hour Hacktoberfest episodes. <a href="https://www.youtube.com/watch?v=qRSYenBLHHQ">Episode 1</a> | <a href="https://www.youtube.com/watch?v=-RlivqzNIso">Episode 2</a> | <a href="https://www.youtube.com/watch?v=LzDIptBGtUw">Episode 3</a></li>
<li>Hacking the PostgREST Headers: Oh, the Things You Can Do! <a href="https://dev.to/burggraf/hacking-the-postgrest-headers-oh-the-things-you-can-do-ck2">Article</a></li>
<li>Why we’re moving away from Firebase. <a href="https://koptional.com/article/why-we%E2%80%99re-moving-away-from-firebase">Blog post</a></li>
<li>Basejump SaaS starter for Supabase. <a href="https://github.com/usebasejump/basejump">Repo</a></li>
<li>SvelteKit + Supabase CRUD with RLS: Row-Level Security! <a href="https://www.youtube.com/watch?v=iKzjfHARXpc">Video</a></li>
<li>Building a startup (build log 2#): Authenticating students with Supabase. <a href="https://dev.to/paul_emechebe/building-a-startup-build-log-2-authenticating-users-with-supabase-1p1b">Blog Post</a></li>
<li>CRUD with Supabase in Flutter. <a href="https://sadabwasim.medium.com/crud-with-supabase-in-flutter-dbe97fa0c15">Tutorial</a></li>
<li>Phone Auth with self-hosted Supabase. <a href="https://www.youtube.com/watch?v=z3sE-ix2uok">Video</a></li>
<li>My Future with Elixir: set-theoretic types. <a href="https://elixir-lang.org/blog/2022/10/05/my-future-with-elixir-set-theoretic-types/">Blog Post</a></li>
<li>Upload media to Supabase from remote URL with nodejs. <a href="https://www.antoinemesnil.com/posts/upload-media-supabase">Blog Post</a></li>
<li>Say goodbye to backend development. <a href="https://www.youtube.com/watch?v=9CuTxeioKF4">Video</a></li>
</ul>
<h2 id="launch-week-6-date-announced" class="group scroll-mt-24">Launch Week 6 date announced<a href="#launch-week-6-date-announced" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We are ending 2022 on a high note ✊. Mark your calendars: Launch Week 6 is happening on <strong>December 12-16</strong>.</p>
<p>In the meantime, <a href="../launch-week.html">revisit LW5</a>.</p>
<h2 id="were-hiring" class="group scroll-mt-24">We’re hiring<a href="#were-hiring" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Come join one of the fastest-growing open source projects ever 🤗</p>
<ul>
<li><a href="https://boards.greenhouse.io/supabase/jobs/4711141004">Sales Engineer</a></li>
<li><a href="https://boards.greenhouse.io/supabase/jobs/4652333004">Lead Billing and API Engineer</a></li>
<li><a href="https://boards.greenhouse.io/supabase">View all our openings</a></li>
</ul>
<h2 id="meme-zone" class="group scroll-mt-24">Meme Zone<a href="#meme-zone" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>If you made it this far in the blog post you deserve a treat. <a href="https://twitter.com/supabase">Follow us on Twitter</a> for more.</p>
<p></p>
<h2 id="get-started" class="group scroll-mt-24">Get started<a href="#get-started" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ul>
<li>Start using Supabase today: <strong><a href="https://supabase.com/dashboard/">supabase.com/dashboard</a></strong></li>
<li>Make sure to <strong><a href="https://github.com/supabase/supabase">star us on GitHub</a></strong></li>
<li>Follow us <strong><a href="https://twitter.com/supabase">on Twitter</a></strong></li>
<li>Subscribe to our <strong><a href="https://www.youtube.com/c/supabase">YouTube channel</a></strong></li>
</ul></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-update-october-2022&amp;text=Supabase%20Beta%20October%202022"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-update-october-2022&amp;text=Supabase%20Beta%20October%202022"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-update-october-2022&amp;t=Supabase%20Beta%20October%202022"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="https://supabase.com/blog/authentication-in-ionic-angular"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Authentication in Ionic Angular with Supabase</h4><p class="small">8 November 2022</p></div></div></div></div></a></div><div><a href="https://supabase.com/blog/postgresql-commitfest"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">What is PostgreSQL commitfest and how to contribute</h4><p class="small">27 October 2022</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/release-notes"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">release-notes</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#supabase-js-v2-and-supabase-flutter-v1-released">supabase-js v2 and supabase-flutter v1 released</a></li>
<li><a href="#new-nextjs-quickstart--nextjs-13-example">New Next.js quickstart &amp; Next.js 13 example</a></li>
<li><a href="#supabase-auth-and-server-side-rendering">Supabase Auth and Server-Side Rendering</a></li>
<li><a href="#database-testing-with-pgtap">Database Testing with pgTAP</a></li>
<li><a href="#edge-functions-update">Edge Functions Update</a></li>
<li><a href="#quick-product-updates">Quick Product Updates</a></li>
<li><a href="#made-with-supabase">Made with Supabase</a></li>
<li><a href="#extended-community-highlights">Extended Community Highlights</a></li>
<li><a href="#launch-week-6-date-announced">Launch Week 6 date announced</a></li>
<li><a href="#were-hiring">We’re hiring</a></li>
<li><a href="#meme-zone">Meme Zone</a></li>
<li><a href="#get-started">Get started</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-update-october-2022&amp;text=Supabase%20Beta%20October%202022"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-update-october-2022&amp;text=Supabase%20Beta%20October%202022"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-update-october-2022&amp;t=Supabase%20Beta%20October%202022"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"authentication-in-ionic-angular","title":"Authentication in Ionic Angular with Supabase","description":"Learn how to build an Ionic Angular app with authentication, Row Level Security, and Magic Link auth.","author":"simon_grimm","image":"simmon-grim-ionic-angular-authentication/ionic-angular-supabase.jpg","thumb":"simmon-grim-ionic-angular-authentication/ionic-angular-supabase.jpg","categories":["developers"],"tags":["ionic","angular","community"],"date":"2022-11-08","toc_depth":2,"formattedDate":"8 November 2022","readingTime":"55 minute read","url":"/blog/authentication-in-ionic-angular","path":"/blog/authentication-in-ionic-angular"},"nextPost":{"slug":"postgresql-commitfest","title":"What is PostgreSQL commitfest and how to contribute","description":"A time-tested method for contributing to the core Postgres code","author":"pavel","image":"what-is-postgres-commitfest/what-is-postgres-commitfest.jpg","thumb":"what-is-postgres-commitfest/what-is-postgres-commitfest.jpg","categories":["postgres"],"tags":["postgres","planetpg"],"date":"2022-10-27","toc_depth":3,"formattedDate":"27 October 2022","readingTime":"11 minute read","url":"/blog/postgresql-commitfest","path":"/blog/postgresql-commitfest"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-beta-update-october-2022","source":"\nDuring October, we shipped something for everybody: new SDKs, quickstarts, Functions tricks, and more. All are listed here just for you. But, more importantly, Launch Week 6️⃣ has a date!\n\n## supabase-js v2 and supabase-flutter v1 released\n\n![supabase-js v2 and supabase-flutter v1 released](/images/blog/2022-october/supabase-js-v2-supabase-flutter-v1-header.jpg)\n\nThe new versions of our two most popular SDKs have been fully released. It couldn’t have happened without our amazing community, thanks to everyone involved. Now, it’s time to build 🛠\n\n[Try supabase-js V2](https://supabase.com/docs/reference/javascript/)\u003cbr/\u003e\n[Try flutter-supabase V1](https://supabase.com/docs/reference/dart)\n\n## New Next.js quickstart \u0026 Next.js 13 example\n\n![New Next.js quickstart \u0026 Next.js 13 example](/images/blog/2022-october/next-header.jpg)\n\nNext.js is all the rage right now 🔥\n\nNext.js Conf raised the bar for dev conferences. We had the honor of being a Gold Sponsor, so we revamped our [Next.js Quickstart guide](https://supabase.com/docs/guides/with-nextjs) to include our pre-built Auth UI and [Auth Helpers.](https://supabase.com/docs/guides/auth/auth-helpers/nextjs)\n\nAnd Next.js 13 was announced! Making it extremely easy to fetch and cache data from our [Serverless API](https://supabase.com/docs/guides/database/api). So we put together an [example to try it out](https://github.com/supabase/supabase/tree/master/examples/caching/with-nextjs-13).\n\n## Supabase Auth and Server-Side Rendering\n\n![Supabase Auth and Server-Side Rendering](/images/blog/2022-october/authentication-server-side-docs.jpg)\n\nThe Auth team published an in-depth doc explaining how Supabase Auth supports server-side rendering. Includes an explanation of the authentication flow and answers to some of the more common questions.\n\n[Read the docs](https://supabase.com/docs/guides/auth/server-side-rendering).\n\n## Database Testing with pgTAP\n\n![Database Testing with pgTAP](/images/blog/2022-october/database-testing-postgres-pgtap.jpg)\n\nDo you have 100% code coverage? Probably not, because that’s usually the last thing you think of, but _definitely_ not if you don’t have database tests. We just shipped a framework for Database Tests which makes it incredibly easy to test your database using pgTAP and pg_prove.\n\n[Learn how it works](https://supabase.com/docs/guides/database/testing).\n\n## Edge Functions Update\n\n![Edge Functions Update](/images/blog/2022-october/functions-new-methods.jpg)\n\nFunctions now support GET requests! Other HTTP verbs such as PUT, PATCH, and DELETE are supported too.\n\n[Check this example](https://github.com/supabase/supabase/blob/master/examples/edge-functions/supabase/functions/restful-tasks/index.ts)\n\n## Quick Product Updates\n\n- supabase-py 🐍 now has functions support! [PR](https://github.com/supabase-community/supabase-py/pull/179)\n- You can now detect users’ location from Edge Functions easily by using `X-Forwarded-For header`. [Example](https://github.com/supabase/supabase/tree/master/examples/edge-functions/supabase/functions/location)\n- Return `provider_refresh_token` along with `provider_access_token`. [PR](https://github.com/supabase/gotrue/pull/641)\n- Added a `refreshSession` method to allow users to forcefully refresh a session instead of waiting for it to autorefresh upon expiry. Thanks to [@j4w8n](https://github.com/j4w8n) for the [PR](https://github.com/supabase/gotrue-js/pull/505) 🙇🏻‍♂️\n- Logging: realtime, storage, postgrest, and pgbouncer released.\n- Trigger a file download by adding the download query parameter to your storage objects. storage-api. [PR](https://github.com/supabase/storage-api/issues/122)\n\n## Made with Supabase\n\n![Made with Supabase](/images/blog/2022-october/twitter-card-header.jpg)\n\nThere were so many amazing apps Made with Supabase this month that we couldn’t choose just one… so here are three:\n\n🚰 [Remote Water Feeder](https://twitter.com/SomangshuG/status/1581888060643774465) by Somangshu Goswami \u003cbr/\u003e\n📊 [Decile App](https://github.com/decileapp/decile) by [Krishna](https://twitter.com/ntkris) \u003cbr/\u003e\n📑 [Explainpaper](https://www.explainpaper.com/) by [Aman Jha](https://twitter.com/amanjha__) and [Jade](https://twitter.com/functionofjade)\n\nIf you want to see +100 other apps, check [the replies](https://twitter.com/supabase/status/1585226233742229504).\n\n## Extended Community Highlights\n\n![Community](/images/blog/2022-june/community.jpg)\n\n- Mark Buggraf sat down to chat about postgres-wasm with LogRocket. [Podcast](https://podrocket.logrocket.com/opensourcing-postgres-wasm)\n- A collection of awesome Supabase resources. [awesome-supabase](https://github.com/lyqht/awesome-supabase)\n- All the Happy Hour Hacktoberfest episodes. [Episode 1](https://www.youtube.com/watch?v=qRSYenBLHHQ) | [Episode 2](https://www.youtube.com/watch?v=-RlivqzNIso) | [Episode 3](https://www.youtube.com/watch?v=LzDIptBGtUw)\n- Hacking the PostgREST Headers: Oh, the Things You Can Do! [Article](https://dev.to/burggraf/hacking-the-postgrest-headers-oh-the-things-you-can-do-ck2)\n- Why we’re moving away from Firebase. [Blog post](https://koptional.com/article/why-we%E2%80%99re-moving-away-from-firebase)\n- Basejump SaaS starter for Supabase. [Repo](https://github.com/usebasejump/basejump)\n- SvelteKit + Supabase CRUD with RLS: Row-Level Security! [Video](https://www.youtube.com/watch?v=iKzjfHARXpc)\n- Building a startup (build log 2#): Authenticating students with Supabase. [Blog Post](https://dev.to/paul_emechebe/building-a-startup-build-log-2-authenticating-users-with-supabase-1p1b)\n- CRUD with Supabase in Flutter. [Tutorial](https://sadabwasim.medium.com/crud-with-supabase-in-flutter-dbe97fa0c15)\n- Phone Auth with self-hosted Supabase. [Video](https://www.youtube.com/watch?v=z3sE-ix2uok)\n- My Future with Elixir: set-theoretic types. [Blog Post](https://elixir-lang.org/blog/2022/10/05/my-future-with-elixir-set-theoretic-types/)\n- Upload media to Supabase from remote URL with nodejs. [Blog Post](https://www.antoinemesnil.com/posts/upload-media-supabase)\n- Say goodbye to backend development. [Video](https://www.youtube.com/watch?v=9CuTxeioKF4)\n\n## Launch Week 6 date announced\n\nWe are ending 2022 on a high note ✊. Mark your calendars: Launch Week 6 is happening on **December 12-16**.\n\nIn the meantime, [revisit LW5](https://supabase.com/launch-week).\n\n## We’re hiring\n\nCome join one of the fastest-growing open source projects ever 🤗\n\n- [Sales Engineer](https://boards.greenhouse.io/supabase/jobs/4711141004)\n- [Lead Billing and API Engineer](https://boards.greenhouse.io/supabase/jobs/4652333004)\n- [View all our openings](https://boards.greenhouse.io/supabase)\n\n## Meme Zone\n\nIf you made it this far in the blog post you deserve a treat. [Follow us on Twitter](https://twitter.com/supabase) for more.\n\n![Supabase meme October 2022](/images/blog/2022-october/beta-update-october-meme.jpeg)\n\n## Get started\n\n- Start using Supabase today: **[supabase.com/dashboard](https://supabase.com/dashboard/)**\n- Make sure to **[star us on GitHub](https://github.com/supabase/supabase)**\n- Follow us **[on Twitter](https://twitter.com/supabase)**\n- Subscribe to our **[YouTube channel](https://www.youtube.com/c/supabase)**\n","title":"Supabase Beta October 2022","description":"New SDKs, quickstarts, Functions tricks, and more. But, more importantly, Launch Week 6️ has a date!","author":"ant_wilson","image":"2022-october/monthly-update-october-2022.jpg","thumb":"2022-october/monthly-update-october-2022.jpg","categories":["product"],"tags":["release-notes"],"date":"2022-11-02","toc_depth":3,"content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    h2: \"h2\",\n    img: \"img\",\n    a: \"a\",\n    em: \"em\",\n    ul: \"ul\",\n    li: \"li\",\n    code: \"code\",\n    strong: \"strong\"\n  }, _provideComponents(), props.components);\n  return _jsxs(_Fragment, {\n    children: [_jsx(_components.p, {\n      children: \"During October, we shipped something for everybody: new SDKs, quickstarts, Functions tricks, and more. All are listed here just for you. But, more importantly, Launch Week 6️⃣ has a date!\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"supabase-js-v2-and-supabase-flutter-v1-released\",\n      children: \"supabase-js v2 and supabase-flutter v1 released\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2022-october/supabase-js-v2-supabase-flutter-v1-header.jpg\",\n        alt: \"supabase-js v2 and supabase-flutter v1 released\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The new versions of our two most popular SDKs have been fully released. It couldn’t have happened without our amazing community, thanks to everyone involved. Now, it’s time to build 🛠\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://supabase.com/docs/reference/javascript/\",\n        children: \"Try supabase-js V2\"\n      }), _jsx(\"br\", {}), \"\\n\", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/reference/dart\",\n        children: \"Try flutter-supabase V1\"\n      })]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"new-nextjs-quickstart--nextjs-13-example\",\n      children: \"New Next.js quickstart \u0026 Next.js 13 example\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2022-october/next-header.jpg\",\n        alt: \"New Next.js quickstart \u0026 Next.js 13 example\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Next.js is all the rage right now 🔥\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Next.js Conf raised the bar for dev conferences. We had the honor of being a Gold Sponsor, so we revamped our \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/with-nextjs\",\n        children: \"Next.js Quickstart guide\"\n      }), \" to include our pre-built Auth UI and \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/auth/auth-helpers/nextjs\",\n        children: \"Auth Helpers.\"\n      })]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"And Next.js 13 was announced! Making it extremely easy to fetch and cache data from our \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/database/api\",\n        children: \"Serverless API\"\n      }), \". So we put together an \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/supabase/tree/master/examples/caching/with-nextjs-13\",\n        children: \"example to try it out\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"supabase-auth-and-server-side-rendering\",\n      children: \"Supabase Auth and Server-Side Rendering\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2022-october/authentication-server-side-docs.jpg\",\n        alt: \"Supabase Auth and Server-Side Rendering\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The Auth team published an in-depth doc explaining how Supabase Auth supports server-side rendering. Includes an explanation of the authentication flow and answers to some of the more common questions.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/auth/server-side-rendering\",\n        children: \"Read the docs\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"database-testing-with-pgtap\",\n      children: \"Database Testing with pgTAP\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2022-october/database-testing-postgres-pgtap.jpg\",\n        alt: \"Database Testing with pgTAP\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Do you have 100% code coverage? Probably not, because that’s usually the last thing you think of, but \", _jsx(_components.em, {\n        children: \"definitely\"\n      }), \" not if you don’t have database tests. We just shipped a framework for Database Tests which makes it incredibly easy to test your database using pgTAP and pg_prove.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/database/testing\",\n        children: \"Learn how it works\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"edge-functions-update\",\n      children: \"Edge Functions Update\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2022-october/functions-new-methods.jpg\",\n        alt: \"Edge Functions Update\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Functions now support GET requests! Other HTTP verbs such as PUT, PATCH, and DELETE are supported too.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://github.com/supabase/supabase/blob/master/examples/edge-functions/supabase/functions/restful-tasks/index.ts\",\n        children: \"Check this example\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"quick-product-updates\",\n      children: \"Quick Product Updates\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"supabase-py 🐍 now has functions support! \", _jsx(_components.a, {\n          href: \"https://github.com/supabase-community/supabase-py/pull/179\",\n          children: \"PR\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"You can now detect users’ location from Edge Functions easily by using \", _jsx(_components.code, {\n          children: \"X-Forwarded-For header\"\n        }), \". \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase/tree/master/examples/edge-functions/supabase/functions/location\",\n          children: \"Example\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Return \", _jsx(_components.code, {\n          children: \"provider_refresh_token\"\n        }), \" along with \", _jsx(_components.code, {\n          children: \"provider_access_token\"\n        }), \". \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/gotrue/pull/641\",\n          children: \"PR\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Added a \", _jsx(_components.code, {\n          children: \"refreshSession\"\n        }), \" method to allow users to forcefully refresh a session instead of waiting for it to autorefresh upon expiry. Thanks to \", _jsx(_components.a, {\n          href: \"https://github.com/j4w8n\",\n          children: \"@j4w8n\"\n        }), \" for the \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/gotrue-js/pull/505\",\n          children: \"PR\"\n        }), \" 🙇🏻‍♂️\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Logging: realtime, storage, postgrest, and pgbouncer released.\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Trigger a file download by adding the download query parameter to your storage objects. storage-api. \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/storage-api/issues/122\",\n          children: \"PR\"\n        })]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"made-with-supabase\",\n      children: \"Made with Supabase\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2022-october/twitter-card-header.jpg\",\n        alt: \"Made with Supabase\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"There were so many amazing apps Made with Supabase this month that we couldn’t choose just one… so here are three:\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"🚰 \", _jsx(_components.a, {\n        href: \"https://twitter.com/SomangshuG/status/1581888060643774465\",\n        children: \"Remote Water Feeder\"\n      }), \" by Somangshu Goswami \", _jsx(\"br\", {}), \"\\n📊 \", _jsx(_components.a, {\n        href: \"https://github.com/decileapp/decile\",\n        children: \"Decile App\"\n      }), \" by \", _jsx(_components.a, {\n        href: \"https://twitter.com/ntkris\",\n        children: \"Krishna\"\n      }), \" \", _jsx(\"br\", {}), \"\\n📑 \", _jsx(_components.a, {\n        href: \"https://www.explainpaper.com/\",\n        children: \"Explainpaper\"\n      }), \" by \", _jsx(_components.a, {\n        href: \"https://twitter.com/amanjha__\",\n        children: \"Aman Jha\"\n      }), \" and \", _jsx(_components.a, {\n        href: \"https://twitter.com/functionofjade\",\n        children: \"Jade\"\n      })]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you want to see +100 other apps, check \", _jsx(_components.a, {\n        href: \"https://twitter.com/supabase/status/1585226233742229504\",\n        children: \"the replies\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"extended-community-highlights\",\n      children: \"Extended Community Highlights\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2022-june/community.jpg\",\n        alt: \"Community\"\n      })\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Mark Buggraf sat down to chat about postgres-wasm with LogRocket. \", _jsx(_components.a, {\n          href: \"https://podrocket.logrocket.com/opensourcing-postgres-wasm\",\n          children: \"Podcast\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"A collection of awesome Supabase resources. \", _jsx(_components.a, {\n          href: \"https://github.com/lyqht/awesome-supabase\",\n          children: \"awesome-supabase\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"All the Happy Hour Hacktoberfest episodes. \", _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=qRSYenBLHHQ\",\n          children: \"Episode 1\"\n        }), \" | \", _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=-RlivqzNIso\",\n          children: \"Episode 2\"\n        }), \" | \", _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=LzDIptBGtUw\",\n          children: \"Episode 3\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Hacking the PostgREST Headers: Oh, the Things You Can Do! \", _jsx(_components.a, {\n          href: \"https://dev.to/burggraf/hacking-the-postgrest-headers-oh-the-things-you-can-do-ck2\",\n          children: \"Article\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Why we’re moving away from Firebase. \", _jsx(_components.a, {\n          href: \"https://koptional.com/article/why-we%E2%80%99re-moving-away-from-firebase\",\n          children: \"Blog post\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Basejump SaaS starter for Supabase. \", _jsx(_components.a, {\n          href: \"https://github.com/usebasejump/basejump\",\n          children: \"Repo\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"SvelteKit + Supabase CRUD with RLS: Row-Level Security! \", _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=iKzjfHARXpc\",\n          children: \"Video\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Building a startup (build log 2#): Authenticating students with Supabase. \", _jsx(_components.a, {\n          href: \"https://dev.to/paul_emechebe/building-a-startup-build-log-2-authenticating-users-with-supabase-1p1b\",\n          children: \"Blog Post\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"CRUD with Supabase in Flutter. \", _jsx(_components.a, {\n          href: \"https://sadabwasim.medium.com/crud-with-supabase-in-flutter-dbe97fa0c15\",\n          children: \"Tutorial\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Phone Auth with self-hosted Supabase. \", _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=z3sE-ix2uok\",\n          children: \"Video\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"My Future with Elixir: set-theoretic types. \", _jsx(_components.a, {\n          href: \"https://elixir-lang.org/blog/2022/10/05/my-future-with-elixir-set-theoretic-types/\",\n          children: \"Blog Post\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Upload media to Supabase from remote URL with nodejs. \", _jsx(_components.a, {\n          href: \"https://www.antoinemesnil.com/posts/upload-media-supabase\",\n          children: \"Blog Post\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Say goodbye to backend development. \", _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=9CuTxeioKF4\",\n          children: \"Video\"\n        })]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"launch-week-6-date-announced\",\n      children: \"Launch Week 6 date announced\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We are ending 2022 on a high note ✊. Mark your calendars: Launch Week 6 is happening on \", _jsx(_components.strong, {\n        children: \"December 12-16\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In the meantime, \", _jsx(_components.a, {\n        href: \"https://supabase.com/launch-week\",\n        children: \"revisit LW5\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"were-hiring\",\n      children: \"We’re hiring\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Come join one of the fastest-growing open source projects ever 🤗\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://boards.greenhouse.io/supabase/jobs/4711141004\",\n          children: \"Sales Engineer\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://boards.greenhouse.io/supabase/jobs/4652333004\",\n          children: \"Lead Billing and API Engineer\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://boards.greenhouse.io/supabase\",\n          children: \"View all our openings\"\n        })\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"meme-zone\",\n      children: \"Meme Zone\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you made it this far in the blog post you deserve a treat. \", _jsx(_components.a, {\n        href: \"https://twitter.com/supabase\",\n        children: \"Follow us on Twitter\"\n      }), \" for more.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2022-october/beta-update-october-meme.jpeg\",\n        alt: \"Supabase meme October 2022\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"get-started\",\n      children: \"Get started\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Start using Supabase today: \", _jsx(_components.strong, {\n          children: _jsx(_components.a, {\n            href: \"https://supabase.com/dashboard/\",\n            children: \"supabase.com/dashboard\"\n          })\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Make sure to \", _jsx(_components.strong, {\n          children: _jsx(_components.a, {\n            href: \"https://github.com/supabase/supabase\",\n            children: \"star us on GitHub\"\n          })\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Follow us \", _jsx(_components.strong, {\n          children: _jsx(_components.a, {\n            href: \"https://twitter.com/supabase\",\n            children: \"on Twitter\"\n          })\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Subscribe to our \", _jsx(_components.strong, {\n          children: _jsx(_components.a, {\n            href: \"https://www.youtube.com/c/supabase\",\n            children: \"YouTube channel\"\n          })\n        })]\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"supabase-js v2 and supabase-flutter v1 released","slug":"supabase-js-v2-and-supabase-flutter-v1-released","lvl":2,"i":0,"seen":0},{"content":"New Next.js quickstart \u0026 Next.js 13 example","slug":"new-nextjs-quickstart--nextjs-13-example","lvl":2,"i":1,"seen":0},{"content":"Supabase Auth and Server-Side Rendering","slug":"supabase-auth-and-server-side-rendering","lvl":2,"i":2,"seen":0},{"content":"Database Testing with pgTAP","slug":"database-testing-with-pgtap","lvl":2,"i":3,"seen":0},{"content":"Edge Functions Update","slug":"edge-functions-update","lvl":2,"i":4,"seen":0},{"content":"Quick Product Updates","slug":"quick-product-updates","lvl":2,"i":5,"seen":0},{"content":"Made with Supabase","slug":"made-with-supabase","lvl":2,"i":6,"seen":0},{"content":"Extended Community Highlights","slug":"extended-community-highlights","lvl":2,"i":7,"seen":0},{"content":"Launch Week 6 date announced","slug":"launch-week-6-date-announced","lvl":2,"i":8,"seen":0},{"content":"We’re hiring","slug":"were-hiring","lvl":2,"i":9,"seen":0},{"content":"Meme Zone","slug":"meme-zone","lvl":2,"i":10,"seen":0},{"content":"Get started","slug":"get-started","lvl":2,"i":11,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,2],"level":0},{"type":"inline","content":"During October, we shipped something for everybody: new SDKs, quickstarts, Functions tricks, and more. All are listed here just for you. But, more importantly, Launch Week 6️⃣ has a date!","level":1,"lines":[1,2],"children":[{"type":"text","content":"During October, we shipped something for everybody: new SDKs, quickstarts, Functions tricks, and more. All are listed here just for you. But, more importantly, Launch Week 6️⃣ has a date!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[3,4],"level":0},{"type":"inline","content":"[supabase-js v2 and supabase-flutter v1 released](#supabase-js-v2-and-supabase-flutter-v1-released)","level":1,"lines":[3,4],"children":[{"type":"text","content":"supabase-js v2 and supabase-flutter v1 released","level":0}],"lvl":2,"i":0,"seen":0,"slug":"supabase-js-v2-and-supabase-flutter-v1-released"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[5,6],"level":0},{"type":"inline","content":"![supabase-js v2 and supabase-flutter v1 released](/images/blog/2022-october/supabase-js-v2-supabase-flutter-v1-header.jpg)","level":1,"lines":[5,6],"children":[{"type":"image","src":"/images/blog/2022-october/supabase-js-v2-supabase-flutter-v1-header.jpg","title":"","alt":"supabase-js v2 and supabase-flutter v1 released","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[7,8],"level":0},{"type":"inline","content":"The new versions of our two most popular SDKs have been fully released. It couldn’t have happened without our amazing community, thanks to everyone involved. Now, it’s time to build 🛠","level":1,"lines":[7,8],"children":[{"type":"text","content":"The new versions of our two most popular SDKs have been fully released. It couldn’t have happened without our amazing community, thanks to everyone involved. Now, it’s time to build 🛠","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[9,11],"level":0},{"type":"inline","content":"[Try supabase-js V2](https://supabase.com/docs/reference/javascript/)\u003cbr/\u003e\n[Try flutter-supabase V1](https://supabase.com/docs/reference/dart)","level":1,"lines":[9,11],"children":[{"type":"link_open","href":"https://supabase.com/docs/reference/javascript/","title":"","level":0},{"type":"text","content":"Try supabase-js V2","level":1},{"type":"link_close","level":0},{"type":"text","content":"\u003cbr/\u003e","level":0},{"type":"softbreak","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/dart","title":"","level":0},{"type":"text","content":"Try flutter-supabase V1","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[12,13],"level":0},{"type":"inline","content":"[New Next.js quickstart \u0026 Next.js 13 example](#new-nextjs-quickstart--nextjs-13-example)","level":1,"lines":[12,13],"children":[{"type":"text","content":"New Next.js quickstart \u0026 Next.js 13 example","level":0}],"lvl":2,"i":1,"seen":0,"slug":"new-nextjs-quickstart--nextjs-13-example"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[14,15],"level":0},{"type":"inline","content":"![New Next.js quickstart \u0026 Next.js 13 example](/images/blog/2022-october/next-header.jpg)","level":1,"lines":[14,15],"children":[{"type":"image","src":"/images/blog/2022-october/next-header.jpg","title":"","alt":"New Next.js quickstart \u0026 Next.js 13 example","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[16,17],"level":0},{"type":"inline","content":"Next.js is all the rage right now 🔥","level":1,"lines":[16,17],"children":[{"type":"text","content":"Next.js is all the rage right now 🔥","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[18,19],"level":0},{"type":"inline","content":"Next.js Conf raised the bar for dev conferences. We had the honor of being a Gold Sponsor, so we revamped our [Next.js Quickstart guide](https://supabase.com/docs/guides/with-nextjs) to include our pre-built Auth UI and [Auth Helpers.](https://supabase.com/docs/guides/auth/auth-helpers/nextjs)","level":1,"lines":[18,19],"children":[{"type":"text","content":"Next.js Conf raised the bar for dev conferences. We had the honor of being a Gold Sponsor, so we revamped our ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/with-nextjs","title":"","level":0},{"type":"text","content":"Next.js Quickstart guide","level":1},{"type":"link_close","level":0},{"type":"text","content":" to include our pre-built Auth UI and ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/auth/auth-helpers/nextjs","title":"","level":0},{"type":"text","content":"Auth Helpers.","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[20,21],"level":0},{"type":"inline","content":"And Next.js 13 was announced! Making it extremely easy to fetch and cache data from our [Serverless API](https://supabase.com/docs/guides/database/api). So we put together an [example to try it out](https://github.com/supabase/supabase/tree/master/examples/caching/with-nextjs-13).","level":1,"lines":[20,21],"children":[{"type":"text","content":"And Next.js 13 was announced! Making it extremely easy to fetch and cache data from our ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/database/api","title":"","level":0},{"type":"text","content":"Serverless API","level":1},{"type":"link_close","level":0},{"type":"text","content":". So we put together an ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase/tree/master/examples/caching/with-nextjs-13","title":"","level":0},{"type":"text","content":"example to try it out","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[22,23],"level":0},{"type":"inline","content":"[Supabase Auth and Server-Side Rendering](#supabase-auth-and-server-side-rendering)","level":1,"lines":[22,23],"children":[{"type":"text","content":"Supabase Auth and Server-Side Rendering","level":0}],"lvl":2,"i":2,"seen":0,"slug":"supabase-auth-and-server-side-rendering"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[24,25],"level":0},{"type":"inline","content":"![Supabase Auth and Server-Side Rendering](/images/blog/2022-october/authentication-server-side-docs.jpg)","level":1,"lines":[24,25],"children":[{"type":"image","src":"/images/blog/2022-october/authentication-server-side-docs.jpg","title":"","alt":"Supabase Auth and Server-Side Rendering","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[26,27],"level":0},{"type":"inline","content":"The Auth team published an in-depth doc explaining how Supabase Auth supports server-side rendering. Includes an explanation of the authentication flow and answers to some of the more common questions.","level":1,"lines":[26,27],"children":[{"type":"text","content":"The Auth team published an in-depth doc explaining how Supabase Auth supports server-side rendering. Includes an explanation of the authentication flow and answers to some of the more common questions.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[28,29],"level":0},{"type":"inline","content":"[Read the docs](https://supabase.com/docs/guides/auth/server-side-rendering).","level":1,"lines":[28,29],"children":[{"type":"link_open","href":"https://supabase.com/docs/guides/auth/server-side-rendering","title":"","level":0},{"type":"text","content":"Read the docs","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[30,31],"level":0},{"type":"inline","content":"[Database Testing with pgTAP](#database-testing-with-pgtap)","level":1,"lines":[30,31],"children":[{"type":"text","content":"Database Testing with pgTAP","level":0}],"lvl":2,"i":3,"seen":0,"slug":"database-testing-with-pgtap"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[32,33],"level":0},{"type":"inline","content":"![Database Testing with pgTAP](/images/blog/2022-october/database-testing-postgres-pgtap.jpg)","level":1,"lines":[32,33],"children":[{"type":"image","src":"/images/blog/2022-october/database-testing-postgres-pgtap.jpg","title":"","alt":"Database Testing with pgTAP","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[34,35],"level":0},{"type":"inline","content":"Do you have 100% code coverage? Probably not, because that’s usually the last thing you think of, but _definitely_ not if you don’t have database tests. We just shipped a framework for Database Tests which makes it incredibly easy to test your database using pgTAP and pg_prove.","level":1,"lines":[34,35],"children":[{"type":"text","content":"Do you have 100% code coverage? Probably not, because that’s usually the last thing you think of, but ","level":0},{"type":"em_open","level":0},{"type":"text","content":"definitely","level":1},{"type":"em_close","level":0},{"type":"text","content":" not if you don’t have database tests. We just shipped a framework for Database Tests which makes it incredibly easy to test your database using pgTAP and pg_prove.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[36,37],"level":0},{"type":"inline","content":"[Learn how it works](https://supabase.com/docs/guides/database/testing).","level":1,"lines":[36,37],"children":[{"type":"link_open","href":"https://supabase.com/docs/guides/database/testing","title":"","level":0},{"type":"text","content":"Learn how it works","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[38,39],"level":0},{"type":"inline","content":"[Edge Functions Update](#edge-functions-update)","level":1,"lines":[38,39],"children":[{"type":"text","content":"Edge Functions Update","level":0}],"lvl":2,"i":4,"seen":0,"slug":"edge-functions-update"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[40,41],"level":0},{"type":"inline","content":"![Edge Functions Update](/images/blog/2022-october/functions-new-methods.jpg)","level":1,"lines":[40,41],"children":[{"type":"image","src":"/images/blog/2022-october/functions-new-methods.jpg","title":"","alt":"Edge Functions Update","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[42,43],"level":0},{"type":"inline","content":"Functions now support GET requests! Other HTTP verbs such as PUT, PATCH, and DELETE are supported too.","level":1,"lines":[42,43],"children":[{"type":"text","content":"Functions now support GET requests! Other HTTP verbs such as PUT, PATCH, and DELETE are supported too.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[44,45],"level":0},{"type":"inline","content":"[Check this example](https://github.com/supabase/supabase/blob/master/examples/edge-functions/supabase/functions/restful-tasks/index.ts)","level":1,"lines":[44,45],"children":[{"type":"link_open","href":"https://github.com/supabase/supabase/blob/master/examples/edge-functions/supabase/functions/restful-tasks/index.ts","title":"","level":0},{"type":"text","content":"Check this example","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[46,47],"level":0},{"type":"inline","content":"[Quick Product Updates](#quick-product-updates)","level":1,"lines":[46,47],"children":[{"type":"text","content":"Quick Product Updates","level":0}],"lvl":2,"i":5,"seen":0,"slug":"quick-product-updates"},{"type":"heading_close","hLevel":2,"level":0},{"type":"bullet_list_open","lines":[48,55],"level":0},{"type":"list_item_open","lines":[48,49],"level":1},{"type":"paragraph_open","tight":true,"lines":[48,49],"level":2},{"type":"inline","content":"supabase-py 🐍 now has functions support! [PR](https://github.com/supabase-community/supabase-py/pull/179)","level":3,"lines":[48,49],"children":[{"type":"text","content":"supabase-py 🐍 now has functions support! ","level":0},{"type":"link_open","href":"https://github.com/supabase-community/supabase-py/pull/179","title":"","level":0},{"type":"text","content":"PR","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[49,50],"level":1},{"type":"paragraph_open","tight":true,"lines":[49,50],"level":2},{"type":"inline","content":"You can now detect users’ location from Edge Functions easily by using `X-Forwarded-For header`. [Example](https://github.com/supabase/supabase/tree/master/examples/edge-functions/supabase/functions/location)","level":3,"lines":[49,50],"children":[{"type":"text","content":"You can now detect users’ location from Edge Functions easily by using ","level":0},{"type":"code","content":"X-Forwarded-For header","block":false,"level":0},{"type":"text","content":". ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase/tree/master/examples/edge-functions/supabase/functions/location","title":"","level":0},{"type":"text","content":"Example","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[50,51],"level":1},{"type":"paragraph_open","tight":true,"lines":[50,51],"level":2},{"type":"inline","content":"Return `provider_refresh_token` along with `provider_access_token`. [PR](https://github.com/supabase/gotrue/pull/641)","level":3,"lines":[50,51],"children":[{"type":"text","content":"Return ","level":0},{"type":"code","content":"provider_refresh_token","block":false,"level":0},{"type":"text","content":" along with ","level":0},{"type":"code","content":"provider_access_token","block":false,"level":0},{"type":"text","content":". ","level":0},{"type":"link_open","href":"https://github.com/supabase/gotrue/pull/641","title":"","level":0},{"type":"text","content":"PR","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[51,52],"level":1},{"type":"paragraph_open","tight":true,"lines":[51,52],"level":2},{"type":"inline","content":"Added a `refreshSession` method to allow users to forcefully refresh a session instead of waiting for it to autorefresh upon expiry. Thanks to [@j4w8n](https://github.com/j4w8n) for the [PR](https://github.com/supabase/gotrue-js/pull/505) 🙇🏻‍♂️","level":3,"lines":[51,52],"children":[{"type":"text","content":"Added a ","level":0},{"type":"code","content":"refreshSession","block":false,"level":0},{"type":"text","content":" method to allow users to forcefully refresh a session instead of waiting for it to autorefresh upon expiry. Thanks to ","level":0},{"type":"link_open","href":"https://github.com/j4w8n","title":"","level":0},{"type":"text","content":"@j4w8n","level":1},{"type":"link_close","level":0},{"type":"text","content":" for the ","level":0},{"type":"link_open","href":"https://github.com/supabase/gotrue-js/pull/505","title":"","level":0},{"type":"text","content":"PR","level":1},{"type":"link_close","level":0},{"type":"text","content":" 🙇🏻‍♂️","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[52,53],"level":1},{"type":"paragraph_open","tight":true,"lines":[52,53],"level":2},{"type":"inline","content":"Logging: realtime, storage, postgrest, and pgbouncer released.","level":3,"lines":[52,53],"children":[{"type":"text","content":"Logging: realtime, storage, postgrest, and pgbouncer released.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[53,55],"level":1},{"type":"paragraph_open","tight":true,"lines":[53,54],"level":2},{"type":"inline","content":"Trigger a file download by adding the download query parameter to your storage objects. storage-api. [PR](https://github.com/supabase/storage-api/issues/122)","level":3,"lines":[53,54],"children":[{"type":"text","content":"Trigger a file download by adding the download query parameter to your storage objects. storage-api. ","level":0},{"type":"link_open","href":"https://github.com/supabase/storage-api/issues/122","title":"","level":0},{"type":"text","content":"PR","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":2,"lines":[55,56],"level":0},{"type":"inline","content":"[Made with Supabase](#made-with-supabase)","level":1,"lines":[55,56],"children":[{"type":"text","content":"Made with Supabase","level":0}],"lvl":2,"i":6,"seen":0,"slug":"made-with-supabase"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[57,58],"level":0},{"type":"inline","content":"![Made with Supabase](/images/blog/2022-october/twitter-card-header.jpg)","level":1,"lines":[57,58],"children":[{"type":"image","src":"/images/blog/2022-october/twitter-card-header.jpg","title":"","alt":"Made with Supabase","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[59,60],"level":0},{"type":"inline","content":"There were so many amazing apps Made with Supabase this month that we couldn’t choose just one… so here are three:","level":1,"lines":[59,60],"children":[{"type":"text","content":"There were so many amazing apps Made with Supabase this month that we couldn’t choose just one… so here are three:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[61,64],"level":0},{"type":"inline","content":"🚰 [Remote Water Feeder](https://twitter.com/SomangshuG/status/1581888060643774465) by Somangshu Goswami \u003cbr/\u003e\n📊 [Decile App](https://github.com/decileapp/decile) by [Krishna](https://twitter.com/ntkris) \u003cbr/\u003e\n📑 [Explainpaper](https://www.explainpaper.com/) by [Aman Jha](https://twitter.com/amanjha__) and [Jade](https://twitter.com/functionofjade)","level":1,"lines":[61,64],"children":[{"type":"text","content":"🚰 ","level":0},{"type":"link_open","href":"https://twitter.com/SomangshuG/status/1581888060643774465","title":"","level":0},{"type":"text","content":"Remote Water Feeder","level":1},{"type":"link_close","level":0},{"type":"text","content":" by Somangshu Goswami \u003cbr/\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"📊 ","level":0},{"type":"link_open","href":"https://github.com/decileapp/decile","title":"","level":0},{"type":"text","content":"Decile App","level":1},{"type":"link_close","level":0},{"type":"text","content":" by ","level":0},{"type":"link_open","href":"https://twitter.com/ntkris","title":"","level":0},{"type":"text","content":"Krishna","level":1},{"type":"link_close","level":0},{"type":"text","content":" \u003cbr/\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"📑 ","level":0},{"type":"link_open","href":"https://www.explainpaper.com/","title":"","level":0},{"type":"text","content":"Explainpaper","level":1},{"type":"link_close","level":0},{"type":"text","content":" by ","level":0},{"type":"link_open","href":"https://twitter.com/amanjha__","title":"","level":0},{"type":"text","content":"Aman Jha","level":1},{"type":"link_close","level":0},{"type":"text","content":" and ","level":0},{"type":"link_open","href":"https://twitter.com/functionofjade","title":"","level":0},{"type":"text","content":"Jade","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[65,66],"level":0},{"type":"inline","content":"If you want to see +100 other apps, check [the replies](https://twitter.com/supabase/status/1585226233742229504).","level":1,"lines":[65,66],"children":[{"type":"text","content":"If you want to see +100 other apps, check ","level":0},{"type":"link_open","href":"https://twitter.com/supabase/status/1585226233742229504","title":"","level":0},{"type":"text","content":"the replies","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[67,68],"level":0},{"type":"inline","content":"[Extended Community Highlights](#extended-community-highlights)","level":1,"lines":[67,68],"children":[{"type":"text","content":"Extended Community Highlights","level":0}],"lvl":2,"i":7,"seen":0,"slug":"extended-community-highlights"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[69,70],"level":0},{"type":"inline","content":"![Community](/images/blog/2022-june/community.jpg)","level":1,"lines":[69,70],"children":[{"type":"image","src":"/images/blog/2022-june/community.jpg","title":"","alt":"Community","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[71,85],"level":0},{"type":"list_item_open","lines":[71,72],"level":1},{"type":"paragraph_open","tight":true,"lines":[71,72],"level":2},{"type":"inline","content":"Mark Buggraf sat down to chat about postgres-wasm with LogRocket. [Podcast](https://podrocket.logrocket.com/opensourcing-postgres-wasm)","level":3,"lines":[71,72],"children":[{"type":"text","content":"Mark Buggraf sat down to chat about postgres-wasm with LogRocket. ","level":0},{"type":"link_open","href":"https://podrocket.logrocket.com/opensourcing-postgres-wasm","title":"","level":0},{"type":"text","content":"Podcast","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[72,73],"level":1},{"type":"paragraph_open","tight":true,"lines":[72,73],"level":2},{"type":"inline","content":"A collection of awesome Supabase resources. [awesome-supabase](https://github.com/lyqht/awesome-supabase)","level":3,"lines":[72,73],"children":[{"type":"text","content":"A collection of awesome Supabase resources. ","level":0},{"type":"link_open","href":"https://github.com/lyqht/awesome-supabase","title":"","level":0},{"type":"text","content":"awesome-supabase","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[73,74],"level":1},{"type":"paragraph_open","tight":true,"lines":[73,74],"level":2},{"type":"inline","content":"All the Happy Hour Hacktoberfest episodes. [Episode 1](https://www.youtube.com/watch?v=qRSYenBLHHQ) | [Episode 2](https://www.youtube.com/watch?v=-RlivqzNIso) | [Episode 3](https://www.youtube.com/watch?v=LzDIptBGtUw)","level":3,"lines":[73,74],"children":[{"type":"text","content":"All the Happy Hour Hacktoberfest episodes. ","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=qRSYenBLHHQ","title":"","level":0},{"type":"text","content":"Episode 1","level":1},{"type":"link_close","level":0},{"type":"text","content":" | ","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=-RlivqzNIso","title":"","level":0},{"type":"text","content":"Episode 2","level":1},{"type":"link_close","level":0},{"type":"text","content":" | ","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=LzDIptBGtUw","title":"","level":0},{"type":"text","content":"Episode 3","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[74,75],"level":1},{"type":"paragraph_open","tight":true,"lines":[74,75],"level":2},{"type":"inline","content":"Hacking the PostgREST Headers: Oh, the Things You Can Do! [Article](https://dev.to/burggraf/hacking-the-postgrest-headers-oh-the-things-you-can-do-ck2)","level":3,"lines":[74,75],"children":[{"type":"text","content":"Hacking the PostgREST Headers: Oh, the Things You Can Do! ","level":0},{"type":"link_open","href":"https://dev.to/burggraf/hacking-the-postgrest-headers-oh-the-things-you-can-do-ck2","title":"","level":0},{"type":"text","content":"Article","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[75,76],"level":1},{"type":"paragraph_open","tight":true,"lines":[75,76],"level":2},{"type":"inline","content":"Why we’re moving away from Firebase. [Blog post](https://koptional.com/article/why-we%E2%80%99re-moving-away-from-firebase)","level":3,"lines":[75,76],"children":[{"type":"text","content":"Why we’re moving away from Firebase. ","level":0},{"type":"link_open","href":"https://koptional.com/article/why-we%E2%80%99re-moving-away-from-firebase","title":"","level":0},{"type":"text","content":"Blog post","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[76,77],"level":1},{"type":"paragraph_open","tight":true,"lines":[76,77],"level":2},{"type":"inline","content":"Basejump SaaS starter for Supabase. [Repo](https://github.com/usebasejump/basejump)","level":3,"lines":[76,77],"children":[{"type":"text","content":"Basejump SaaS starter for Supabase. ","level":0},{"type":"link_open","href":"https://github.com/usebasejump/basejump","title":"","level":0},{"type":"text","content":"Repo","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[77,78],"level":1},{"type":"paragraph_open","tight":true,"lines":[77,78],"level":2},{"type":"inline","content":"SvelteKit + Supabase CRUD with RLS: Row-Level Security! [Video](https://www.youtube.com/watch?v=iKzjfHARXpc)","level":3,"lines":[77,78],"children":[{"type":"text","content":"SvelteKit + Supabase CRUD with RLS: Row-Level Security! ","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=iKzjfHARXpc","title":"","level":0},{"type":"text","content":"Video","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[78,79],"level":1},{"type":"paragraph_open","tight":true,"lines":[78,79],"level":2},{"type":"inline","content":"Building a startup (build log 2#): Authenticating students with Supabase. [Blog Post](https://dev.to/paul_emechebe/building-a-startup-build-log-2-authenticating-users-with-supabase-1p1b)","level":3,"lines":[78,79],"children":[{"type":"text","content":"Building a startup (build log 2#): Authenticating students with Supabase. ","level":0},{"type":"link_open","href":"https://dev.to/paul_emechebe/building-a-startup-build-log-2-authenticating-users-with-supabase-1p1b","title":"","level":0},{"type":"text","content":"Blog Post","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[79,80],"level":1},{"type":"paragraph_open","tight":true,"lines":[79,80],"level":2},{"type":"inline","content":"CRUD with Supabase in Flutter. [Tutorial](https://sadabwasim.medium.com/crud-with-supabase-in-flutter-dbe97fa0c15)","level":3,"lines":[79,80],"children":[{"type":"text","content":"CRUD with Supabase in Flutter. ","level":0},{"type":"link_open","href":"https://sadabwasim.medium.com/crud-with-supabase-in-flutter-dbe97fa0c15","title":"","level":0},{"type":"text","content":"Tutorial","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[80,81],"level":1},{"type":"paragraph_open","tight":true,"lines":[80,81],"level":2},{"type":"inline","content":"Phone Auth with self-hosted Supabase. [Video](https://www.youtube.com/watch?v=z3sE-ix2uok)","level":3,"lines":[80,81],"children":[{"type":"text","content":"Phone Auth with self-hosted Supabase. ","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=z3sE-ix2uok","title":"","level":0},{"type":"text","content":"Video","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[81,82],"level":1},{"type":"paragraph_open","tight":true,"lines":[81,82],"level":2},{"type":"inline","content":"My Future with Elixir: set-theoretic types. [Blog Post](https://elixir-lang.org/blog/2022/10/05/my-future-with-elixir-set-theoretic-types/)","level":3,"lines":[81,82],"children":[{"type":"text","content":"My Future with Elixir: set-theoretic types. ","level":0},{"type":"link_open","href":"https://elixir-lang.org/blog/2022/10/05/my-future-with-elixir-set-theoretic-types/","title":"","level":0},{"type":"text","content":"Blog Post","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[82,83],"level":1},{"type":"paragraph_open","tight":true,"lines":[82,83],"level":2},{"type":"inline","content":"Upload media to Supabase from remote URL with nodejs. [Blog Post](https://www.antoinemesnil.com/posts/upload-media-supabase)","level":3,"lines":[82,83],"children":[{"type":"text","content":"Upload media to Supabase from remote URL with nodejs. ","level":0},{"type":"link_open","href":"https://www.antoinemesnil.com/posts/upload-media-supabase","title":"","level":0},{"type":"text","content":"Blog Post","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[83,85],"level":1},{"type":"paragraph_open","tight":true,"lines":[83,84],"level":2},{"type":"inline","content":"Say goodbye to backend development. [Video](https://www.youtube.com/watch?v=9CuTxeioKF4)","level":3,"lines":[83,84],"children":[{"type":"text","content":"Say goodbye to backend development. ","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=9CuTxeioKF4","title":"","level":0},{"type":"text","content":"Video","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":2,"lines":[85,86],"level":0},{"type":"inline","content":"[Launch Week 6 date announced](#launch-week-6-date-announced)","level":1,"lines":[85,86],"children":[{"type":"text","content":"Launch Week 6 date announced","level":0}],"lvl":2,"i":8,"seen":0,"slug":"launch-week-6-date-announced"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[87,88],"level":0},{"type":"inline","content":"We are ending 2022 on a high note ✊. Mark your calendars: Launch Week 6 is happening on **December 12-16**.","level":1,"lines":[87,88],"children":[{"type":"text","content":"We are ending 2022 on a high note ✊. Mark your calendars: Launch Week 6 is happening on ","level":0},{"type":"strong_open","level":0},{"type":"text","content":"December 12-16","level":1},{"type":"strong_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[89,90],"level":0},{"type":"inline","content":"In the meantime, [revisit LW5](https://supabase.com/launch-week).","level":1,"lines":[89,90],"children":[{"type":"text","content":"In the meantime, ","level":0},{"type":"link_open","href":"https://supabase.com/launch-week","title":"","level":0},{"type":"text","content":"revisit LW5","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[91,92],"level":0},{"type":"inline","content":"[We’re hiring](#were-hiring)","level":1,"lines":[91,92],"children":[{"type":"text","content":"We’re hiring","level":0}],"lvl":2,"i":9,"seen":0,"slug":"were-hiring"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[93,94],"level":0},{"type":"inline","content":"Come join one of the fastest-growing open source projects ever 🤗","level":1,"lines":[93,94],"children":[{"type":"text","content":"Come join one of the fastest-growing open source projects ever 🤗","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[95,99],"level":0},{"type":"list_item_open","lines":[95,96],"level":1},{"type":"paragraph_open","tight":true,"lines":[95,96],"level":2},{"type":"inline","content":"[Sales Engineer](https://boards.greenhouse.io/supabase/jobs/4711141004)","level":3,"lines":[95,96],"children":[{"type":"link_open","href":"https://boards.greenhouse.io/supabase/jobs/4711141004","title":"","level":0},{"type":"text","content":"Sales Engineer","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[96,97],"level":1},{"type":"paragraph_open","tight":true,"lines":[96,97],"level":2},{"type":"inline","content":"[Lead Billing and API Engineer](https://boards.greenhouse.io/supabase/jobs/4652333004)","level":3,"lines":[96,97],"children":[{"type":"link_open","href":"https://boards.greenhouse.io/supabase/jobs/4652333004","title":"","level":0},{"type":"text","content":"Lead Billing and API Engineer","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[97,99],"level":1},{"type":"paragraph_open","tight":true,"lines":[97,98],"level":2},{"type":"inline","content":"[View all our openings](https://boards.greenhouse.io/supabase)","level":3,"lines":[97,98],"children":[{"type":"link_open","href":"https://boards.greenhouse.io/supabase","title":"","level":0},{"type":"text","content":"View all our openings","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":2,"lines":[99,100],"level":0},{"type":"inline","content":"[Meme Zone](#meme-zone)","level":1,"lines":[99,100],"children":[{"type":"text","content":"Meme Zone","level":0}],"lvl":2,"i":10,"seen":0,"slug":"meme-zone"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[101,102],"level":0},{"type":"inline","content":"If you made it this far in the blog post you deserve a treat. [Follow us on Twitter](https://twitter.com/supabase) for more.","level":1,"lines":[101,102],"children":[{"type":"text","content":"If you made it this far in the blog post you deserve a treat. ","level":0},{"type":"link_open","href":"https://twitter.com/supabase","title":"","level":0},{"type":"text","content":"Follow us on Twitter","level":1},{"type":"link_close","level":0},{"type":"text","content":" for more.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[103,104],"level":0},{"type":"inline","content":"![Supabase meme October 2022](/images/blog/2022-october/beta-update-october-meme.jpeg)","level":1,"lines":[103,104],"children":[{"type":"image","src":"/images/blog/2022-october/beta-update-october-meme.jpeg","title":"","alt":"Supabase meme October 2022","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[105,106],"level":0},{"type":"inline","content":"[Get started](#get-started)","level":1,"lines":[105,106],"children":[{"type":"text","content":"Get started","level":0}],"lvl":2,"i":11,"seen":0,"slug":"get-started"},{"type":"heading_close","hLevel":2,"level":0},{"type":"bullet_list_open","lines":[107,111],"level":0},{"type":"list_item_open","lines":[107,108],"level":1},{"type":"paragraph_open","tight":true,"lines":[107,108],"level":2},{"type":"inline","content":"Start using Supabase today: **[supabase.com/dashboard](https://supabase.com/dashboard/)**","level":3,"lines":[107,108],"children":[{"type":"text","content":"Start using Supabase today: ","level":0},{"type":"strong_open","level":0},{"type":"link_open","href":"https://supabase.com/dashboard/","title":"","level":1},{"type":"text","content":"supabase.com/dashboard","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[108,109],"level":1},{"type":"paragraph_open","tight":true,"lines":[108,109],"level":2},{"type":"inline","content":"Make sure to **[star us on GitHub](https://github.com/supabase/supabase)**","level":3,"lines":[108,109],"children":[{"type":"text","content":"Make sure to ","level":0},{"type":"strong_open","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase","title":"","level":1},{"type":"text","content":"star us on GitHub","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[109,110],"level":1},{"type":"paragraph_open","tight":true,"lines":[109,110],"level":2},{"type":"inline","content":"Follow us **[on Twitter](https://twitter.com/supabase)**","level":3,"lines":[109,110],"children":[{"type":"text","content":"Follow us ","level":0},{"type":"strong_open","level":0},{"type":"link_open","href":"https://twitter.com/supabase","title":"","level":1},{"type":"text","content":"on Twitter","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[110,111],"level":1},{"type":"paragraph_open","tight":true,"lines":[110,111],"level":2},{"type":"inline","content":"Subscribe to our **[YouTube channel](https://www.youtube.com/c/supabase)**","level":3,"lines":[110,111],"children":[{"type":"text","content":"Subscribe to our ","level":0},{"type":"strong_open","level":0},{"type":"link_open","href":"https://www.youtube.com/c/supabase","title":"","level":1},{"type":"text","content":"YouTube channel","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0}],"content":"- [supabase-js v2 and supabase-flutter v1 released](#supabase-js-v2-and-supabase-flutter-v1-released)\n- [New Next.js quickstart \u0026 Next.js 13 example](#new-nextjs-quickstart--nextjs-13-example)\n- [Supabase Auth and Server-Side Rendering](#supabase-auth-and-server-side-rendering)\n- [Database Testing with pgTAP](#database-testing-with-pgtap)\n- [Edge Functions Update](#edge-functions-update)\n- [Quick Product Updates](#quick-product-updates)\n- [Made with Supabase](#made-with-supabase)\n- [Extended Community Highlights](#extended-community-highlights)\n- [Launch Week 6 date announced](#launch-week-6-date-announced)\n- [We’re hiring](#were-hiring)\n- [Meme Zone](#meme-zone)\n- [Get started](#get-started)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-beta-update-october-2022"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>