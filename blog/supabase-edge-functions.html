<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Edge Functions are now available in Supabase</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Today we&#x27;re launching Edge Functions. Edge Functions let you execute Typescript code close to your users, no matter where they&#x27;re located." data-next-head=""/><meta property="og:title" content="Edge Functions are now available in Supabase" data-next-head=""/><meta property="og:description" content="Today we&#x27;re launching Edge Functions. Edge Functions let you execute Typescript code close to your users, no matter where they&#x27;re located." data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-edge-functions" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2022-03-31" data-next-head=""/><meta property="article:author" content="https://twitter.com/everConfusedGuy" data-next-head=""/><meta property="article:tag" content="launch-week" data-next-head=""/><meta property="article:tag" content="functions" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/launch-week-4/thursday-functions/functions-thumb.png" data-next-head=""/><meta property="og:image:alt" content="Edge Functions are now available in Supabase thumbnail" data-next-head=""/><meta property="og:video" content="https://www.youtube.com/v/5OWH9c4u68M" data-next-head=""/><meta property="og:video:type" content="application/x-shockwave-flash" data-next-head=""/><meta property="og:video:width" content="640" data-next-head=""/><meta property="og:video:height" content="385" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Edge Functions are now available in Supabase</h1><div class="text-light flex space-x-3 text-sm"><p>31 Mar 2022</p><p>•</p><p>10 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://twitter.com/everConfusedGuy"><div class="flex items-center gap-3"><div class="w-10"><img alt="Inian Parameshwaran avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Favatars.githubusercontent.com%2Fu%2F2155545%3Fv%3D4&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Favatars.githubusercontent.com%2Fu%2F2155545%3Fv%3D4&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Favatars.githubusercontent.com%2Fu%2F2155545%3Fv%3D4&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Inian Parameshwaran</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Edge Functions are now available in Supabase" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-4%2Fthursday-functions%2Ffunctions-thumb.png&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-4%2Fthursday-functions%2Ffunctions-thumb.png&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-4%2Fthursday-functions%2Ffunctions-thumb.png&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-4%2Fthursday-functions%2Ffunctions-thumb.png&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-4%2Fthursday-functions%2Ffunctions-thumb.png&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-4%2Fthursday-functions%2Ffunctions-thumb.png&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-4%2Fthursday-functions%2Ffunctions-thumb.png&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-4%2Fthursday-functions%2Ffunctions-thumb.png&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-4%2Fthursday-functions%2Ffunctions-thumb.png&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-4%2Fthursday-functions%2Ffunctions-thumb.png&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-4%2Fthursday-functions%2Ffunctions-thumb.png&amp;w=3840&amp;q=100"/></div><div class="bg-gray-300 rounded-lg px-6 py-2 bold"><p>🆕✨ Edge Functions now natively supports npm modules and Node built-in APIs. <a href="edge-functions-node-npm.html">Learn more</a></p></div>
<p>Today we&#x27;re launching one of our <a href="https://twitter.com/lau_cazanove/status/1506530181946691592">most</a> <a href="https://twitter.com/edgarasben/status/1506653203458363393">requested</a> and <a href="https://twitter.com/runjep/status/1507462077216088069">highly-</a><a href="https://twitter.com/marcopolotwo/status/1506431782362632195">anticipated</a> <a href="https://github.com/supabase/supabase/discussions/4269">features</a> — Edge Functions. Edge Functions let you execute Typescript code close to your users, no matter where they&#x27;re located.</p>
<p>Edge Functions are executed on the secure-by-default <a href="https://deno.land/">Deno runtime</a>, and deployed around the world in seconds using Deno&#x27;s <a href="https://deno.com/deploy">hosted Deno Deploy offering</a>. However, as a user of Supabase Edge Functions, you don&#x27;t need to know or worry about any of that; we handle all the gnarly details, delivering a seamless experience that allows you to focus on your business logic.</p>
<h2 id="choosing-a-platform" class="group scroll-mt-24">Choosing a platform<a href="#choosing-a-platform" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Serverless compute options can be broken down into two broad categories:</p>
<ul>
<li>Containers as a Service (e.g. Google Cloud Run, Fly.io)</li>
<li>Functions as a Service (e.g. AWS Lambda, Cloudflare Workers, Fastly Compute @ Edge, Suborbital)</li>
</ul>
<p>Additionally, there are self-hosted offerings within each category as well (e.g. Firecracker sort-of for the former, OpenFaaS for the latter).</p>
<p>Our first critical decision was to choose Functions as a Service (”FaaS”) over Containers and other alternatives. While Containers can allow for more flexible workloads, authoring and maintaining Functions allows for a substantially simpler developer experience-something Supabase has always focused on. As a Supabase user, all you need to think about is the business logic you wanted to write a Function for in the first place; you don&#x27;t need to think about optimizing docker image builds, updating your system dependencies, etc.</p>
<p>Additionally, while a lot of work has been invested in improving container start-up times, Functions generally still enjoy significantly faster start-ups.</p>
<p>Our next major decision was to choose global deployments instead of a deploying to a specific geographical region.
<!-- -->
Launching Functions in a single region (e.g. close to your database) can present a lot of advantages if there are a lot of database operations being executed within your business logic.</p>
<p>However, Supabase already offers a flexible solution for that - <a href="https://supabase.com/docs/guides/database/functions">Database Functions</a>! As such, for Supabase [Edge] Functions, we decided to deploy far-and-wide so that they are as close to your end-users as possible. You can even invoke a Database Function from an Edge Function, allowing you to enjoy the best of both worlds.</p>
<h2 id="the-deno-runtime" class="group scroll-mt-24">The Deno runtime<a href="#the-deno-runtime" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Deno is a modern Typescript and JavaScript runtime created with first-class support for web technologies. Here some of the reasons why we&#x27;re excited about Deno:</p>
<h3 id="open-source" class="group scroll-mt-24">Open-source<a href="#open-source" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p><em>Deno Deploy</em> is Deno&#x27;s hosted service and, although it is proprietary, the Deno CLI is open source. This means that the open source offering from Deno maintains feature-parity with the proprietary service. If you use Deno to develop functions locally, its behavior will be the same as when deployed on the edge, unlocking an incredible developer experience. We couldn&#x27;t achieve a similarly seamless experience with e.g. AWS Lambda, no matter how much we tried.</p>
<p>Deno even goes a step further than maintaining feature-parity, releasing all new APIs to open-source <strong>first</strong> before rolling them out to their hosted offering. This ensures that you can always develop and test bleeding-edge functionality locally, with all the tools you&#x27;re used to and familiar with, before deploying to a hosted cloud environment.</p>
<h3 id="batteries-included" class="group scroll-mt-24">Batteries included<a href="#batteries-included" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Deno comes with all the tools needed for a modern, productive developer experience, so you can spend less time searching through third party modules, and more time being productive. Out of the box, it includes support for Typescript, ES modules, and provides <a href="https://deno.land/manual/testing">test runners</a>, <a href="https://deno.land/manual/tools/formatter">formatters</a>, <a href="https://deno.land/manual/tools/linter">linters</a>, <a href="https://deno.land/manual/tools/bundler">bundlers</a> and a package manager.</p>
<h3 id="secure-by-default" class="group scroll-mt-24">Secure by default<a href="#secure-by-default" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Like modern web browsers, the Deno runtime executes your code in a secure sandbox by default. Functions cannot access environment variables, network or filesystem unless they are explicitly allowed. Supply chain attacks have been a daunting problem in the Node ecosystem and Deno mitigates many of these attacks by having a security-first model.</p>
<h2 id="the-supa-deno-platform" class="group scroll-mt-24">The Supa-Deno platform<a href="#the-supa-deno-platform" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>When a request comes in to an Edge Function you deploy on Supabase, it first lands at a “Relay”. The <a href="https://github.com/supabase/functions-relay">Relay</a> acts as an API gateway, authenticating the JWT, and providing some additional functionality like logging and rate-limiting. Upon receiving the incoming request, the Relay retrieves the metadata for your Function, and uses it to construct a unique identifier (”Deployment ID”) for the Function. This Deployment ID then gets passed over to the Deno Deploy platform, which takes care of invoking your Function securely, and passing your output back to the Relay, which in turn passes it back to your end-user.</p>
<p>If a user&#x27;s request comes in for your Function before it has been cached by the Deno Deploy platform (a ”cold start”), it reaches out to Supabase to retrieve your Functions code, and any associated permissions and secrets. Even though this might sound like a lot of work, we&#x27;ve worked to ensure that these cold starts are still pretty spiffy.</p>
<p></p>
<p>The primary goal of this architecture is to reduce the amount of work done in the hot path (marked in purple above). Each component in the hot path is globally distributed so that Edge Functions start up and respond to your users&#x27; requests fast.</p>
<h3 id="region-earth-" class="group scroll-mt-24">Region: Earth 🌎<a href="#region-earth-" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Supabase Edge Functions are deployed to one region - Earth (and we&#x27;re exploring Mars as an additional region 👽). Edge Functions are deployed to over 30 data centers in a matter of seconds, ensuring fast response times no matter where your users are located.</p>
<p><a href="https://supabase.com/docs/guides/database/functions">Database Functions</a> (also served via our <a href="https://supabase.com/docs/guides/database/api">REST and GraphQL APIs</a>) are colocated with your Postgres database, making them ideal for DB-intensive operations. These APIs can also be called seamlessly from your Supabase Edge Functions, making for a powerful combination.</p>
<h3 id="scale-to-zero" class="group scroll-mt-24">Scale to zero<a href="#scale-to-zero" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Functions scale down to zero and you only pay for what you use. This makes them great for an extremely wide variety of workloads-whether you&#x27;re starting out with extremely sporadic usage, or bursty request patterns, or using them consistently and heavily.</p>
<h2 id="a-fun-and-efficient-developer-experience" class="group scroll-mt-24">A fun and efficient Developer Experience<a href="#a-fun-and-efficient-developer-experience" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Delivering a phenomenal Developer Experience is something we care about a lot at Supabase. In the serverless functions space, we&#x27;ve found that local development and testing, coupled with fast deploys are critical.</p>
<p>We&#x27;ve <a href="https://supabase.com/docs/guides/local-development">extended the Supabase CLI</a> to optimize the Edge Functions DX along these two dimensions. You get live-reloading, and a local Deno environment that matches the production environment, making for a great local iteration environment.</p>
<video width="99%" autoplay="" muted="" playsinline="" controls=""><source src="https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/marketing/blog/launch-week-4/functions-hot-reloading.mp4" type="video/mp4"/></video>
<p>Once you&#x27;ve tested your Function locally (or gone straight to production cos you live <em>on the edge</em> ... get it?) it takes just seconds to deploy a new function and see the logs streaming in your Dashboard—ensuring a tight iteration loop with your production environment.</p>
<video width="99%" autoplay="" muted="" playsinline="" controls=""><source src="https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/marketing/blog/launch-week-4/logs-polish.mov" type="video/mp4"/></video>
<p>Our observability pipeline has been built <a href="https://supabase.com/blog/supabase-acquires-logflare">using Logflare</a>. Structured logs from your Edge Functions get sent to Logflare, which also processes them into metrics. Your Supabase Dashboard is able to reach out to Logflare endpoints, and with some of the <a href="https://supabase.com/blog/supabase-studio">(not so) secret sauce</a> from our front-end team, you get responsive charts and logs for your Edge Functions.</p>
<p>
new —&gt; deploy —&gt; invoke —&gt; logs</p>
<p>To round off the end-to-end experience, <code class="short-inline-codeblock">supabase-js</code> now works in Deno, making it easy to interact with the rest of your Supabase project from within your Edge Functions. Your project&#x27;s URLs, API keys, and database connection strings are made available as environment variables within your Function to make this even easier. And if you want to interact with third-party APIs which require a secret key, such as Stripe, you can easily and securely make these available to your Function as environment variables via the Supabase CLI. To get an idea for what is possible, check out our examples page <a href="https://github.com/supabase/supabase/tree/master/examples/edge-functions">here</a>.</p>
<h2 id="quickstart" class="group scroll-mt-24">Quickstart<a href="#quickstart" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<div class="video-container"><iframe class="video-with-border w-full" src="https://www.youtube-nocookie.com/embed/5OWH9c4u68M" frameBorder="1" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen=""></iframe></div>
<p>Can&#x27;t wait to get started? Develop, test, and deploy your first Supabase Edge Function in less than 7 minutes with our <a href="https://youtu.be/5OWH9c4u68M">Quickstart Video Tutorial</a>!
Check the <a href="../docs/guides/functions.html">Edge Functions Docs</a> for the steps.</p>
<h2 id="example-apps-mobile-payments-with-react-native-and-flutter" class="group scroll-mt-24">Example apps: Mobile payments with React Native and Flutter<a href="#example-apps-mobile-payments-with-react-native-and-flutter" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>When researching use-cases with all of you, <a href="https://github.com/supabase/supabase/discussions/4269#discussioncomment-1748067">processing payments</a> was being pointed out the most, so naturally, to support the launch, we&#x27;ve created example apps in collaboration with Stripe for <a href="https://github.com/supabase-community/expo-stripe-payments-with-supabase-functions">React Native</a> and <a href="https://github.com/supabase-community/flutter-stripe-payments-with-supabase-functions">Flutter</a>, to allow you to get started and earn money even faster.</p>
<p></p>
<h2 id="can-i-host-static-sites" class="group scroll-mt-24">Can I host static sites<a href="#can-i-host-static-sites" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Even though Deno supports <a href="https://deno.land/manual/jsx_dom/jsx">rendering <code class="short-inline-codeblock">jsx</code> and <code class="short-inline-codeblock">tsx</code> files</a>, we don&#x27;t plan on becoming a website hosting provider with Edge Functions. For now, Edge Functions can only be invoked via the <code class="short-inline-codeblock">POST</code> method. Offering secure and fast web hosting with a good developer experience is a hard problem to solve and there are <a href="https://www.netlify.com/">other</a> <a href="https://vercel.com/">fantastic</a> <a href="https://pages.cloudflare.com/">providers</a> which do exactly that.</p>
<p>From our experience launching Supabase Storage last year, becoming a hosting provider is not something you want to take lightly. Malicious folks will upload their phishing sites whenever they get a chance and this can quickly spiral out of control, causing you to land on the denylist in the <a href="https://safebrowsing.google.com/">Safe Browsing dataset</a>. Even though we removed the project within minutes after being notified, various ISPs had already cached the information from the Safe browsing dataset and were “helping” their users by restricting access to the Supabase domain. This isn&#x27;t something we&#x27;re planning to repeat. But as always, we&#x27;d love to hear your use cases and feature requests, we&#x27;re just one <a href="https://github.com/supabase/supabase/discussions">GitHub discussion</a> away!</p>
<h2 id="roadmap" class="group scroll-mt-24">Roadmap<a href="#roadmap" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>These are some of the features that are on our roadmap for functions.</p>
<ul>
<li>Cron functions to trigger your function on a schedule</li>
<li>Studio support and self hosting multiple functions</li>
<li>Optimizing cold start times</li>
<li>Wasm is a great fit for running functions and companies like <a href="https://suborbital.dev/">Suborbital</a> are leading the charge by making it easy to write functions that run in a Wasm-based runtime. Deno supports Wasm natively and we plan on allowing users to write their functions in languages that compile down to Wasm like Rust.</li>
<li>We can expose Deno&#x27;s permission model so that you can lock down your functions further by restricting outbound connections to specific domains.</li>
<li>To invoke functions, you need a valid JWT signed by your project&#x27;s JWT secret. We plan on making this verification optional. This enables more use cases like hooking up Supabase functions to Stripe webhooks where the function manages it&#x27;s own authentication.</li>
</ul>
<p>Give us a shout with your feedback and excited to see what you build in the hackathon!</p></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-edge-functions&amp;text=Edge%20Functions%20are%20now%20available%20in%20Supabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-edge-functions&amp;text=Edge%20Functions%20are%20now%20available%20in%20Supabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-edge-functions&amp;t=Edge%20Functions%20are%20now%20available%20in%20Supabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="supabrew.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Supabrew - Never Code Thirsty</h4><p class="small">1 April 2022</p></div></div></div></div></a></div><div><a href="supabase-enterprise.html"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Introducing Supabase Enterprise</h4><p class="small">30 March 2022</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/launch-week"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">launch-week</div></a><a href="https://supabase.com/blog/tags/functions"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">functions</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#choosing-a-platform">Choosing a platform</a></li>
<li><a href="#the-deno-runtime">The Deno runtime</a>
<ul>
<li><a href="#open-source">Open-source</a></li>
<li><a href="#batteries-included">Batteries included</a></li>
<li><a href="#secure-by-default">Secure by default</a></li>
</ul>
</li>
<li><a href="#the-supa-deno-platform">The Supa-Deno platform</a>
<ul>
<li><a href="#region-earth-%F0%9F%8C%8E">Region: Earth 🌎</a></li>
<li><a href="#scale-to-zero">Scale to zero</a></li>
</ul>
</li>
<li><a href="#a-fun-and-efficient-developer-experience">A fun and efficient Developer Experience</a></li>
<li><a href="#quickstart">Quickstart</a></li>
<li><a href="#example-apps-mobile-payments-with-react-native-and-flutter">Example apps: Mobile payments with React Native and Flutter</a></li>
<li><a href="#can-i-host-static-sites">Can I host static sites</a></li>
<li><a href="#roadmap">Roadmap</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-edge-functions&amp;text=Edge%20Functions%20are%20now%20available%20in%20Supabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-edge-functions&amp;text=Edge%20Functions%20are%20now%20available%20in%20Supabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-edge-functions&amp;t=Edge%20Functions%20are%20now%20available%20in%20Supabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"supabrew","title":"Supabrew - Never Code Thirsty","description":"A light and refreshing non-alcoholic beer for devs.","author":"ant_wilson","image":"launch-week-4/friday-supabrew/productShot1.jpg","thumb":"launch-week-4/friday-supabrew/productShot1.png","categories":["company"],"tags":["launch-week"],"date":"2022-04-01T11:00:00","toc_depth":3,"formattedDate":"1 April 2022","readingTime":"3 minute read","url":"/blog/supabrew","path":"/blog/supabrew"},"nextPost":{"slug":"supabase-enterprise","title":"Introducing Supabase Enterprise","description":"Today we are releasing Supabase Enterprise, a suite of features to scale your project.","author":"rory_wilding,paul_copplestone","image":"launch-week-4/enterprise-day/enterprise-thumb.png","thumb":"launch-week-4/enterprise-day/enterprise-thumb.png","categories":["company"],"tags":["launch-week","enterprise"],"date":"2022-03-30","toc_depth":3,"formattedDate":"30 March 2022","readingTime":"10 minute read","url":"/blog/supabase-enterprise","path":"/blog/supabase-enterprise"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-edge-functions","source":"\n\u003cdiv className=\"bg-gray-300 rounded-lg px-6 py-2 bold\"\u003e\n\n🆕✨ Edge Functions now natively supports npm modules and Node built-in APIs. [Learn more](https://supabase.com/blog/edge-functions-node-npm)\n\n\u003c/div\u003e\n\nToday we're launching one of our [most](https://twitter.com/lau_cazanove/status/1506530181946691592) [requested](https://twitter.com/edgarasben/status/1506653203458363393) and [highly-](https://twitter.com/runjep/status/1507462077216088069)[anticipated](https://twitter.com/marcopolotwo/status/1506431782362632195) [features](https://github.com/supabase/supabase/discussions/4269) — Edge Functions. Edge Functions let you execute Typescript code close to your users, no matter where they're located.\n\nEdge Functions are executed on the secure-by-default [Deno runtime](https://deno.land/), and deployed around the world in seconds using Deno's [hosted Deno Deploy offering](https://deno.com/deploy). However, as a user of Supabase Edge Functions, you don't need to know or worry about any of that; we handle all the gnarly details, delivering a seamless experience that allows you to focus on your business logic.\n\n## Choosing a platform\n\nServerless compute options can be broken down into two broad categories:\n\n- Containers as a Service (e.g. Google Cloud Run, Fly.io)\n- Functions as a Service (e.g. AWS Lambda, Cloudflare Workers, Fastly Compute @ Edge, Suborbital)\n\nAdditionally, there are self-hosted offerings within each category as well (e.g. Firecracker sort-of for the former, OpenFaaS for the latter).\n\nOur first critical decision was to choose Functions as a Service (”FaaS”) over Containers and other alternatives. While Containers can allow for more flexible workloads, authoring and maintaining Functions allows for a substantially simpler developer experience-something Supabase has always focused on. As a Supabase user, all you need to think about is the business logic you wanted to write a Function for in the first place; you don't need to think about optimizing docker image builds, updating your system dependencies, etc.\n\nAdditionally, while a lot of work has been invested in improving container start-up times, Functions generally still enjoy significantly faster start-ups.\n\nOur next major decision was to choose global deployments instead of a deploying to a specific geographical region.\n![Functions deployment region](/images/blog/launch-week-4/thursday-functions/where-functions-deploy.png)\nLaunching Functions in a single region (e.g. close to your database) can present a lot of advantages if there are a lot of database operations being executed within your business logic.\n\nHowever, Supabase already offers a flexible solution for that - [Database Functions](https://supabase.com/docs/guides/database/functions)! As such, for Supabase [Edge] Functions, we decided to deploy far-and-wide so that they are as close to your end-users as possible. You can even invoke a Database Function from an Edge Function, allowing you to enjoy the best of both worlds.\n\n## The Deno runtime\n\nDeno is a modern Typescript and JavaScript runtime created with first-class support for web technologies. Here some of the reasons why we're excited about Deno:\n\n### Open-source\n\n_Deno Deploy_ is Deno's hosted service and, although it is proprietary, the Deno CLI is open source. This means that the open source offering from Deno maintains feature-parity with the proprietary service. If you use Deno to develop functions locally, its behavior will be the same as when deployed on the edge, unlocking an incredible developer experience. We couldn't achieve a similarly seamless experience with e.g. AWS Lambda, no matter how much we tried.\n\nDeno even goes a step further than maintaining feature-parity, releasing all new APIs to open-source **first** before rolling them out to their hosted offering. This ensures that you can always develop and test bleeding-edge functionality locally, with all the tools you're used to and familiar with, before deploying to a hosted cloud environment.\n\n### Batteries included\n\nDeno comes with all the tools needed for a modern, productive developer experience, so you can spend less time searching through third party modules, and more time being productive. Out of the box, it includes support for Typescript, ES modules, and provides [test runners](https://deno.land/manual/testing), [formatters](https://deno.land/manual/tools/formatter), [linters](https://deno.land/manual/tools/linter), [bundlers](https://deno.land/manual/tools/bundler) and a package manager.\n\n### Secure by default\n\nLike modern web browsers, the Deno runtime executes your code in a secure sandbox by default. Functions cannot access environment variables, network or filesystem unless they are explicitly allowed. Supply chain attacks have been a daunting problem in the Node ecosystem and Deno mitigates many of these attacks by having a security-first model.\n\n## The Supa-Deno platform\n\nWhen a request comes in to an Edge Function you deploy on Supabase, it first lands at a “Relay”. The [Relay](https://github.com/supabase/functions-relay) acts as an API gateway, authenticating the JWT, and providing some additional functionality like logging and rate-limiting. Upon receiving the incoming request, the Relay retrieves the metadata for your Function, and uses it to construct a unique identifier (”Deployment ID”) for the Function. This Deployment ID then gets passed over to the Deno Deploy platform, which takes care of invoking your Function securely, and passing your output back to the Relay, which in turn passes it back to your end-user.\n\nIf a user's request comes in for your Function before it has been cached by the Deno Deploy platform (a ”cold start”), it reaches out to Supabase to retrieve your Functions code, and any associated permissions and secrets. Even though this might sound like a lot of work, we've worked to ensure that these cold starts are still pretty spiffy.\n\n![Functions architecture](/images/blog/launch-week-4/thursday-functions/functions-architecture.png)\n\nThe primary goal of this architecture is to reduce the amount of work done in the hot path (marked in purple above). Each component in the hot path is globally distributed so that Edge Functions start up and respond to your users' requests fast.\n\n### Region: Earth 🌎\n\nSupabase Edge Functions are deployed to one region - Earth (and we're exploring Mars as an additional region 👽). Edge Functions are deployed to over 30 data centers in a matter of seconds, ensuring fast response times no matter where your users are located.\n\n[Database Functions](https://supabase.com/docs/guides/database/functions) (also served via our [REST and GraphQL APIs](https://supabase.com/docs/guides/database/api)) are colocated with your Postgres database, making them ideal for DB-intensive operations. These APIs can also be called seamlessly from your Supabase Edge Functions, making for a powerful combination.\n\n### Scale to zero\n\nFunctions scale down to zero and you only pay for what you use. This makes them great for an extremely wide variety of workloads-whether you're starting out with extremely sporadic usage, or bursty request patterns, or using them consistently and heavily.\n\n## A fun and efficient Developer Experience\n\nDelivering a phenomenal Developer Experience is something we care about a lot at Supabase. In the serverless functions space, we've found that local development and testing, coupled with fast deploys are critical.\n\nWe've [extended the Supabase CLI](https://supabase.com/docs/guides/local-development) to optimize the Edge Functions DX along these two dimensions. You get live-reloading, and a local Deno environment that matches the production environment, making for a great local iteration environment.\n\n\u003cvideo width=\"99%\" autoPlay muted playsInline controls={true}\u003e\n  \u003csource\n    src=\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/marketing/blog/launch-week-4/functions-hot-reloading.mp4\"\n    type=\"video/mp4\"\n  /\u003e\n\u003c/video\u003e\n\nOnce you've tested your Function locally (or gone straight to production cos you live _on the edge_ ... get it?) it takes just seconds to deploy a new function and see the logs streaming in your Dashboard—ensuring a tight iteration loop with your production environment.\n\n\u003cvideo width=\"99%\" autoPlay muted playsInline controls={true}\u003e\n  \u003csource\n    src=\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/marketing/blog/launch-week-4/logs-polish.mov\"\n    type=\"video/mp4\"\n  /\u003e\n\u003c/video\u003e\n\nOur observability pipeline has been built [using Logflare](https://supabase.com/blog/supabase-acquires-logflare). Structured logs from your Edge Functions get sent to Logflare, which also processes them into metrics. Your Supabase Dashboard is able to reach out to Logflare endpoints, and with some of the [(not so) secret sauce](https://supabase.com/blog/supabase-studio) from our front-end team, you get responsive charts and logs for your Edge Functions.\n\n![new —\u003e deploy —\u003e invoke —\u003e logs](/images/blog/launch-week-4/thursday-functions/functions-new-deploy-invoke-logs.gif)\nnew —\u003e deploy —\u003e invoke —\u003e logs\n\nTo round off the end-to-end experience, `supabase-js` now works in Deno, making it easy to interact with the rest of your Supabase project from within your Edge Functions. Your project's URLs, API keys, and database connection strings are made available as environment variables within your Function to make this even easier. And if you want to interact with third-party APIs which require a secret key, such as Stripe, you can easily and securely make these available to your Function as environment variables via the Supabase CLI. To get an idea for what is possible, check out our examples page [here](https://github.com/supabase/supabase/tree/master/examples/edge-functions).\n\n## Quickstart\n\n\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"video-with-border w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/5OWH9c4u68M\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen\n  \u003e\u003c/iframe\u003e\n\u003c/div\u003e\n\nCan't wait to get started? Develop, test, and deploy your first Supabase Edge Function in less than 7 minutes with our [Quickstart Video Tutorial](https://youtu.be/5OWH9c4u68M)!\nCheck the [Edge Functions Docs](https://supabase.com/docs/guides/functions) for the steps.\n\n## Example apps: Mobile payments with React Native and Flutter\n\nWhen researching use-cases with all of you, [processing payments](https://github.com/supabase/supabase/discussions/4269#discussioncomment-1748067) was being pointed out the most, so naturally, to support the launch, we've created example apps in collaboration with Stripe for [React Native](https://github.com/supabase-community/expo-stripe-payments-with-supabase-functions) and [Flutter](https://github.com/supabase-community/flutter-stripe-payments-with-supabase-functions), to allow you to get started and earn money even faster.\n\n![functions-payment-example](/images/blog/launch-week-4/thursday-functions/functions-payment.gif)\n\n## Can I host static sites\n\nEven though Deno supports [rendering `jsx` and `tsx` files](https://deno.land/manual/jsx_dom/jsx), we don't plan on becoming a website hosting provider with Edge Functions. For now, Edge Functions can only be invoked via the `POST` method. Offering secure and fast web hosting with a good developer experience is a hard problem to solve and there are [other](https://www.netlify.com/) [fantastic](https://vercel.com/) [providers](https://pages.cloudflare.com/) which do exactly that.\n\nFrom our experience launching Supabase Storage last year, becoming a hosting provider is not something you want to take lightly. Malicious folks will upload their phishing sites whenever they get a chance and this can quickly spiral out of control, causing you to land on the denylist in the [Safe Browsing dataset](https://safebrowsing.google.com/). Even though we removed the project within minutes after being notified, various ISPs had already cached the information from the Safe browsing dataset and were “helping” their users by restricting access to the Supabase domain. This isn't something we're planning to repeat. But as always, we'd love to hear your use cases and feature requests, we're just one [GitHub discussion](https://github.com/supabase/supabase/discussions) away!\n\n## Roadmap\n\nThese are some of the features that are on our roadmap for functions.\n\n- Cron functions to trigger your function on a schedule\n- Studio support and self hosting multiple functions\n- Optimizing cold start times\n- Wasm is a great fit for running functions and companies like [Suborbital](https://suborbital.dev/) are leading the charge by making it easy to write functions that run in a Wasm-based runtime. Deno supports Wasm natively and we plan on allowing users to write their functions in languages that compile down to Wasm like Rust.\n- We can expose Deno's permission model so that you can lock down your functions further by restricting outbound connections to specific domains.\n- To invoke functions, you need a valid JWT signed by your project's JWT secret. We plan on making this verification optional. This enables more use cases like hooking up Supabase functions to Stripe webhooks where the function manages it's own authentication.\n\nGive us a shout with your feedback and excited to see what you build in the hackathon!\n","title":"Edge Functions are now available in Supabase","description":"Today we're launching Edge Functions. Edge Functions let you execute Typescript code close to your users, no matter where they're located.","author":"inian","image":"launch-week-4/thursday-functions/functions-thumb.png","thumb":"launch-week-4/thursday-functions/functions-thumb.png","categories":["product"],"tags":["launch-week","functions"],"date":"2022-03-31","toc_depth":3,"video":"https://www.youtube.com/v/5OWH9c4u68M","content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    a: \"a\",\n    h2: \"h2\",\n    ul: \"ul\",\n    li: \"li\",\n    img: \"img\",\n    h3: \"h3\",\n    em: \"em\",\n    strong: \"strong\",\n    code: \"code\"\n  }, _provideComponents(), props.components);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"div\", {\n      className: \"bg-gray-300 rounded-lg px-6 py-2 bold\",\n      children: _jsxs(_components.p, {\n        children: [\"🆕✨ Edge Functions now natively supports npm modules and Node built-in APIs. \", _jsx(_components.a, {\n          href: \"https://supabase.com/blog/edge-functions-node-npm\",\n          children: \"Learn more\"\n        })]\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Today we're launching one of our \", _jsx(_components.a, {\n        href: \"https://twitter.com/lau_cazanove/status/1506530181946691592\",\n        children: \"most\"\n      }), \" \", _jsx(_components.a, {\n        href: \"https://twitter.com/edgarasben/status/1506653203458363393\",\n        children: \"requested\"\n      }), \" and \", _jsx(_components.a, {\n        href: \"https://twitter.com/runjep/status/1507462077216088069\",\n        children: \"highly-\"\n      }), _jsx(_components.a, {\n        href: \"https://twitter.com/marcopolotwo/status/1506431782362632195\",\n        children: \"anticipated\"\n      }), \" \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/supabase/discussions/4269\",\n        children: \"features\"\n      }), \" — Edge Functions. Edge Functions let you execute Typescript code close to your users, no matter where they're located.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Edge Functions are executed on the secure-by-default \", _jsx(_components.a, {\n        href: \"https://deno.land/\",\n        children: \"Deno runtime\"\n      }), \", and deployed around the world in seconds using Deno's \", _jsx(_components.a, {\n        href: \"https://deno.com/deploy\",\n        children: \"hosted Deno Deploy offering\"\n      }), \". However, as a user of Supabase Edge Functions, you don't need to know or worry about any of that; we handle all the gnarly details, delivering a seamless experience that allows you to focus on your business logic.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"choosing-a-platform\",\n      children: \"Choosing a platform\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Serverless compute options can be broken down into two broad categories:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Containers as a Service (e.g. Google Cloud Run, Fly.io)\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Functions as a Service (e.g. AWS Lambda, Cloudflare Workers, Fastly Compute @ Edge, Suborbital)\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Additionally, there are self-hosted offerings within each category as well (e.g. Firecracker sort-of for the former, OpenFaaS for the latter).\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Our first critical decision was to choose Functions as a Service (”FaaS”) over Containers and other alternatives. While Containers can allow for more flexible workloads, authoring and maintaining Functions allows for a substantially simpler developer experience-something Supabase has always focused on. As a Supabase user, all you need to think about is the business logic you wanted to write a Function for in the first place; you don't need to think about optimizing docker image builds, updating your system dependencies, etc.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Additionally, while a lot of work has been invested in improving container start-up times, Functions generally still enjoy significantly faster start-ups.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Our next major decision was to choose global deployments instead of a deploying to a specific geographical region.\\n\", _jsx(_components.img, {\n        src: \"/images/blog/launch-week-4/thursday-functions/where-functions-deploy.png\",\n        alt: \"Functions deployment region\"\n      }), \"\\nLaunching Functions in a single region (e.g. close to your database) can present a lot of advantages if there are a lot of database operations being executed within your business logic.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"However, Supabase already offers a flexible solution for that - \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/database/functions\",\n        children: \"Database Functions\"\n      }), \"! As such, for Supabase [Edge] Functions, we decided to deploy far-and-wide so that they are as close to your end-users as possible. You can even invoke a Database Function from an Edge Function, allowing you to enjoy the best of both worlds.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"the-deno-runtime\",\n      children: \"The Deno runtime\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Deno is a modern Typescript and JavaScript runtime created with first-class support for web technologies. Here some of the reasons why we're excited about Deno:\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"open-source\",\n      children: \"Open-source\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.em, {\n        children: \"Deno Deploy\"\n      }), \" is Deno's hosted service and, although it is proprietary, the Deno CLI is open source. This means that the open source offering from Deno maintains feature-parity with the proprietary service. If you use Deno to develop functions locally, its behavior will be the same as when deployed on the edge, unlocking an incredible developer experience. We couldn't achieve a similarly seamless experience with e.g. AWS Lambda, no matter how much we tried.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Deno even goes a step further than maintaining feature-parity, releasing all new APIs to open-source \", _jsx(_components.strong, {\n        children: \"first\"\n      }), \" before rolling them out to their hosted offering. This ensures that you can always develop and test bleeding-edge functionality locally, with all the tools you're used to and familiar with, before deploying to a hosted cloud environment.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"batteries-included\",\n      children: \"Batteries included\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Deno comes with all the tools needed for a modern, productive developer experience, so you can spend less time searching through third party modules, and more time being productive. Out of the box, it includes support for Typescript, ES modules, and provides \", _jsx(_components.a, {\n        href: \"https://deno.land/manual/testing\",\n        children: \"test runners\"\n      }), \", \", _jsx(_components.a, {\n        href: \"https://deno.land/manual/tools/formatter\",\n        children: \"formatters\"\n      }), \", \", _jsx(_components.a, {\n        href: \"https://deno.land/manual/tools/linter\",\n        children: \"linters\"\n      }), \", \", _jsx(_components.a, {\n        href: \"https://deno.land/manual/tools/bundler\",\n        children: \"bundlers\"\n      }), \" and a package manager.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"secure-by-default\",\n      children: \"Secure by default\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Like modern web browsers, the Deno runtime executes your code in a secure sandbox by default. Functions cannot access environment variables, network or filesystem unless they are explicitly allowed. Supply chain attacks have been a daunting problem in the Node ecosystem and Deno mitigates many of these attacks by having a security-first model.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"the-supa-deno-platform\",\n      children: \"The Supa-Deno platform\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"When a request comes in to an Edge Function you deploy on Supabase, it first lands at a “Relay”. The \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/functions-relay\",\n        children: \"Relay\"\n      }), \" acts as an API gateway, authenticating the JWT, and providing some additional functionality like logging and rate-limiting. Upon receiving the incoming request, the Relay retrieves the metadata for your Function, and uses it to construct a unique identifier (”Deployment ID”) for the Function. This Deployment ID then gets passed over to the Deno Deploy platform, which takes care of invoking your Function securely, and passing your output back to the Relay, which in turn passes it back to your end-user.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"If a user's request comes in for your Function before it has been cached by the Deno Deploy platform (a ”cold start”), it reaches out to Supabase to retrieve your Functions code, and any associated permissions and secrets. Even though this might sound like a lot of work, we've worked to ensure that these cold starts are still pretty spiffy.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-4/thursday-functions/functions-architecture.png\",\n        alt: \"Functions architecture\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The primary goal of this architecture is to reduce the amount of work done in the hot path (marked in purple above). Each component in the hot path is globally distributed so that Edge Functions start up and respond to your users' requests fast.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"region-earth-\",\n      children: \"Region: Earth 🌎\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Supabase Edge Functions are deployed to one region - Earth (and we're exploring Mars as an additional region 👽). Edge Functions are deployed to over 30 data centers in a matter of seconds, ensuring fast response times no matter where your users are located.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/database/functions\",\n        children: \"Database Functions\"\n      }), \" (also served via our \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/database/api\",\n        children: \"REST and GraphQL APIs\"\n      }), \") are colocated with your Postgres database, making them ideal for DB-intensive operations. These APIs can also be called seamlessly from your Supabase Edge Functions, making for a powerful combination.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"scale-to-zero\",\n      children: \"Scale to zero\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Functions scale down to zero and you only pay for what you use. This makes them great for an extremely wide variety of workloads-whether you're starting out with extremely sporadic usage, or bursty request patterns, or using them consistently and heavily.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"a-fun-and-efficient-developer-experience\",\n      children: \"A fun and efficient Developer Experience\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Delivering a phenomenal Developer Experience is something we care about a lot at Supabase. In the serverless functions space, we've found that local development and testing, coupled with fast deploys are critical.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We've \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/local-development\",\n        children: \"extended the Supabase CLI\"\n      }), \" to optimize the Edge Functions DX along these two dimensions. You get live-reloading, and a local Deno environment that matches the production environment, making for a great local iteration environment.\"]\n    }), \"\\n\", _jsx(\"video\", {\n      width: \"99%\",\n      autoPlay: true,\n      muted: true,\n      playsInline: true,\n      controls: true,\n      children: _jsx(\"source\", {\n        src: \"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/marketing/blog/launch-week-4/functions-hot-reloading.mp4\",\n        type: \"video/mp4\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Once you've tested your Function locally (or gone straight to production cos you live \", _jsx(_components.em, {\n        children: \"on the edge\"\n      }), \" ... get it?) it takes just seconds to deploy a new function and see the logs streaming in your Dashboard—ensuring a tight iteration loop with your production environment.\"]\n    }), \"\\n\", _jsx(\"video\", {\n      width: \"99%\",\n      autoPlay: true,\n      muted: true,\n      playsInline: true,\n      controls: true,\n      children: _jsx(\"source\", {\n        src: \"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/marketing/blog/launch-week-4/logs-polish.mov\",\n        type: \"video/mp4\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Our observability pipeline has been built \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/supabase-acquires-logflare\",\n        children: \"using Logflare\"\n      }), \". Structured logs from your Edge Functions get sent to Logflare, which also processes them into metrics. Your Supabase Dashboard is able to reach out to Logflare endpoints, and with some of the \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/supabase-studio\",\n        children: \"(not so) secret sauce\"\n      }), \" from our front-end team, you get responsive charts and logs for your Edge Functions.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.img, {\n        src: \"/images/blog/launch-week-4/thursday-functions/functions-new-deploy-invoke-logs.gif\",\n        alt: \"new —\u003e deploy —\u003e invoke —\u003e logs\"\n      }), \"\\nnew —\u003e deploy —\u003e invoke —\u003e logs\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To round off the end-to-end experience, \", _jsx(_components.code, {\n        children: \"supabase-js\"\n      }), \" now works in Deno, making it easy to interact with the rest of your Supabase project from within your Edge Functions. Your project's URLs, API keys, and database connection strings are made available as environment variables within your Function to make this even easier. And if you want to interact with third-party APIs which require a secret key, such as Stripe, you can easily and securely make these available to your Function as environment variables via the Supabase CLI. To get an idea for what is possible, check out our examples page \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/supabase/tree/master/examples/edge-functions\",\n        children: \"here\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"quickstart\",\n      children: \"Quickstart\"\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"video-container\",\n      children: _jsx(\"iframe\", {\n        className: \"video-with-border w-full\",\n        src: \"https://www.youtube-nocookie.com/embed/5OWH9c4u68M\",\n        frameBorder: \"1\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n        allowFullScreen: true\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Can't wait to get started? Develop, test, and deploy your first Supabase Edge Function in less than 7 minutes with our \", _jsx(_components.a, {\n        href: \"https://youtu.be/5OWH9c4u68M\",\n        children: \"Quickstart Video Tutorial\"\n      }), \"!\\nCheck the \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/functions\",\n        children: \"Edge Functions Docs\"\n      }), \" for the steps.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"example-apps-mobile-payments-with-react-native-and-flutter\",\n      children: \"Example apps: Mobile payments with React Native and Flutter\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"When researching use-cases with all of you, \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/supabase/discussions/4269#discussioncomment-1748067\",\n        children: \"processing payments\"\n      }), \" was being pointed out the most, so naturally, to support the launch, we've created example apps in collaboration with Stripe for \", _jsx(_components.a, {\n        href: \"https://github.com/supabase-community/expo-stripe-payments-with-supabase-functions\",\n        children: \"React Native\"\n      }), \" and \", _jsx(_components.a, {\n        href: \"https://github.com/supabase-community/flutter-stripe-payments-with-supabase-functions\",\n        children: \"Flutter\"\n      }), \", to allow you to get started and earn money even faster.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-4/thursday-functions/functions-payment.gif\",\n        alt: \"functions-payment-example\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"can-i-host-static-sites\",\n      children: \"Can I host static sites\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Even though Deno supports \", _jsxs(_components.a, {\n        href: \"https://deno.land/manual/jsx_dom/jsx\",\n        children: [\"rendering \", _jsx(_components.code, {\n          children: \"jsx\"\n        }), \" and \", _jsx(_components.code, {\n          children: \"tsx\"\n        }), \" files\"]\n      }), \", we don't plan on becoming a website hosting provider with Edge Functions. For now, Edge Functions can only be invoked via the \", _jsx(_components.code, {\n        children: \"POST\"\n      }), \" method. Offering secure and fast web hosting with a good developer experience is a hard problem to solve and there are \", _jsx(_components.a, {\n        href: \"https://www.netlify.com/\",\n        children: \"other\"\n      }), \" \", _jsx(_components.a, {\n        href: \"https://vercel.com/\",\n        children: \"fantastic\"\n      }), \" \", _jsx(_components.a, {\n        href: \"https://pages.cloudflare.com/\",\n        children: \"providers\"\n      }), \" which do exactly that.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"From our experience launching Supabase Storage last year, becoming a hosting provider is not something you want to take lightly. Malicious folks will upload their phishing sites whenever they get a chance and this can quickly spiral out of control, causing you to land on the denylist in the \", _jsx(_components.a, {\n        href: \"https://safebrowsing.google.com/\",\n        children: \"Safe Browsing dataset\"\n      }), \". Even though we removed the project within minutes after being notified, various ISPs had already cached the information from the Safe browsing dataset and were “helping” their users by restricting access to the Supabase domain. This isn't something we're planning to repeat. But as always, we'd love to hear your use cases and feature requests, we're just one \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/supabase/discussions\",\n        children: \"GitHub discussion\"\n      }), \" away!\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"roadmap\",\n      children: \"Roadmap\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"These are some of the features that are on our roadmap for functions.\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Cron functions to trigger your function on a schedule\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Studio support and self hosting multiple functions\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Optimizing cold start times\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Wasm is a great fit for running functions and companies like \", _jsx(_components.a, {\n          href: \"https://suborbital.dev/\",\n          children: \"Suborbital\"\n        }), \" are leading the charge by making it easy to write functions that run in a Wasm-based runtime. Deno supports Wasm natively and we plan on allowing users to write their functions in languages that compile down to Wasm like Rust.\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"We can expose Deno's permission model so that you can lock down your functions further by restricting outbound connections to specific domains.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"To invoke functions, you need a valid JWT signed by your project's JWT secret. We plan on making this verification optional. This enables more use cases like hooking up Supabase functions to Stripe webhooks where the function manages it's own authentication.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Give us a shout with your feedback and excited to see what you build in the hackathon!\"\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"Choosing a platform","slug":"choosing-a-platform","lvl":2,"i":0,"seen":0},{"content":"The Deno runtime","slug":"the-deno-runtime","lvl":2,"i":1,"seen":0},{"content":"Open-source","slug":"open-source","lvl":3,"i":2,"seen":0},{"content":"Batteries included","slug":"batteries-included","lvl":3,"i":3,"seen":0},{"content":"Secure by default","slug":"secure-by-default","lvl":3,"i":4,"seen":0},{"content":"The Supa-Deno platform","slug":"the-supa-deno-platform","lvl":2,"i":5,"seen":0},{"content":"Region: Earth 🌎","slug":"region-earth-🌎","lvl":3,"i":6,"seen":0},{"content":"Scale to zero","slug":"scale-to-zero","lvl":3,"i":7,"seen":0},{"content":"A fun and efficient Developer Experience","slug":"a-fun-and-efficient-developer-experience","lvl":2,"i":8,"seen":0},{"content":"Quickstart","slug":"quickstart","lvl":2,"i":9,"seen":0},{"content":"Example apps: Mobile payments with React Native and Flutter","slug":"example-apps-mobile-payments-with-react-native-and-flutter","lvl":2,"i":10,"seen":0},{"content":"Can I host static sites","slug":"can-i-host-static-sites","lvl":2,"i":11,"seen":0},{"content":"Roadmap","slug":"roadmap","lvl":2,"i":12,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,2],"level":0},{"type":"inline","content":"\u003cdiv className=\"bg-gray-300 rounded-lg px-6 py-2 bold\"\u003e","level":1,"lines":[1,2],"children":[{"type":"text","content":"\u003cdiv className=\"bg-gray-300 rounded-lg px-6 py-2 bold\"\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[3,4],"level":0},{"type":"inline","content":"🆕✨ Edge Functions now natively supports npm modules and Node built-in APIs. [Learn more](https://supabase.com/blog/edge-functions-node-npm)","level":1,"lines":[3,4],"children":[{"type":"text","content":"🆕✨ Edge Functions now natively supports npm modules and Node built-in APIs. ","level":0},{"type":"link_open","href":"https://supabase.com/blog/edge-functions-node-npm","title":"","level":0},{"type":"text","content":"Learn more","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[5,6],"level":0},{"type":"inline","content":"\u003c/div\u003e","level":1,"lines":[5,6],"children":[{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[7,8],"level":0},{"type":"inline","content":"Today we're launching one of our [most](https://twitter.com/lau_cazanove/status/1506530181946691592) [requested](https://twitter.com/edgarasben/status/1506653203458363393) and [highly-](https://twitter.com/runjep/status/1507462077216088069)[anticipated](https://twitter.com/marcopolotwo/status/1506431782362632195) [features](https://github.com/supabase/supabase/discussions/4269) — Edge Functions. Edge Functions let you execute Typescript code close to your users, no matter where they're located.","level":1,"lines":[7,8],"children":[{"type":"text","content":"Today we're launching one of our ","level":0},{"type":"link_open","href":"https://twitter.com/lau_cazanove/status/1506530181946691592","title":"","level":0},{"type":"text","content":"most","level":1},{"type":"link_close","level":0},{"type":"text","content":" ","level":0},{"type":"link_open","href":"https://twitter.com/edgarasben/status/1506653203458363393","title":"","level":0},{"type":"text","content":"requested","level":1},{"type":"link_close","level":0},{"type":"text","content":" and ","level":0},{"type":"link_open","href":"https://twitter.com/runjep/status/1507462077216088069","title":"","level":0},{"type":"text","content":"highly-","level":1},{"type":"link_close","level":0},{"type":"link_open","href":"https://twitter.com/marcopolotwo/status/1506431782362632195","title":"","level":0},{"type":"text","content":"anticipated","level":1},{"type":"link_close","level":0},{"type":"text","content":" ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase/discussions/4269","title":"","level":0},{"type":"text","content":"features","level":1},{"type":"link_close","level":0},{"type":"text","content":" — Edge Functions. Edge Functions let you execute Typescript code close to your users, no matter where they're located.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[9,10],"level":0},{"type":"inline","content":"Edge Functions are executed on the secure-by-default [Deno runtime](https://deno.land/), and deployed around the world in seconds using Deno's [hosted Deno Deploy offering](https://deno.com/deploy). However, as a user of Supabase Edge Functions, you don't need to know or worry about any of that; we handle all the gnarly details, delivering a seamless experience that allows you to focus on your business logic.","level":1,"lines":[9,10],"children":[{"type":"text","content":"Edge Functions are executed on the secure-by-default ","level":0},{"type":"link_open","href":"https://deno.land/","title":"","level":0},{"type":"text","content":"Deno runtime","level":1},{"type":"link_close","level":0},{"type":"text","content":", and deployed around the world in seconds using Deno's ","level":0},{"type":"link_open","href":"https://deno.com/deploy","title":"","level":0},{"type":"text","content":"hosted Deno Deploy offering","level":1},{"type":"link_close","level":0},{"type":"text","content":". However, as a user of Supabase Edge Functions, you don't need to know or worry about any of that; we handle all the gnarly details, delivering a seamless experience that allows you to focus on your business logic.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[11,12],"level":0},{"type":"inline","content":"[Choosing a platform](#choosing-a-platform)","level":1,"lines":[11,12],"children":[{"type":"text","content":"Choosing a platform","level":0}],"lvl":2,"i":0,"seen":0,"slug":"choosing-a-platform"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[13,14],"level":0},{"type":"inline","content":"Serverless compute options can be broken down into two broad categories:","level":1,"lines":[13,14],"children":[{"type":"text","content":"Serverless compute options can be broken down into two broad categories:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[15,18],"level":0},{"type":"list_item_open","lines":[15,16],"level":1},{"type":"paragraph_open","tight":true,"lines":[15,16],"level":2},{"type":"inline","content":"Containers as a Service (e.g. Google Cloud Run, Fly.io)","level":3,"lines":[15,16],"children":[{"type":"text","content":"Containers as a Service (e.g. Google Cloud Run, Fly.io)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[16,18],"level":1},{"type":"paragraph_open","tight":true,"lines":[16,17],"level":2},{"type":"inline","content":"Functions as a Service (e.g. AWS Lambda, Cloudflare Workers, Fastly Compute @ Edge, Suborbital)","level":3,"lines":[16,17],"children":[{"type":"text","content":"Functions as a Service (e.g. AWS Lambda, Cloudflare Workers, Fastly Compute @ Edge, Suborbital)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[18,19],"level":0},{"type":"inline","content":"Additionally, there are self-hosted offerings within each category as well (e.g. Firecracker sort-of for the former, OpenFaaS for the latter).","level":1,"lines":[18,19],"children":[{"type":"text","content":"Additionally, there are self-hosted offerings within each category as well (e.g. Firecracker sort-of for the former, OpenFaaS for the latter).","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[20,21],"level":0},{"type":"inline","content":"Our first critical decision was to choose Functions as a Service (”FaaS”) over Containers and other alternatives. While Containers can allow for more flexible workloads, authoring and maintaining Functions allows for a substantially simpler developer experience-something Supabase has always focused on. As a Supabase user, all you need to think about is the business logic you wanted to write a Function for in the first place; you don't need to think about optimizing docker image builds, updating your system dependencies, etc.","level":1,"lines":[20,21],"children":[{"type":"text","content":"Our first critical decision was to choose Functions as a Service (”FaaS”) over Containers and other alternatives. While Containers can allow for more flexible workloads, authoring and maintaining Functions allows for a substantially simpler developer experience-something Supabase has always focused on. As a Supabase user, all you need to think about is the business logic you wanted to write a Function for in the first place; you don't need to think about optimizing docker image builds, updating your system dependencies, etc.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[22,23],"level":0},{"type":"inline","content":"Additionally, while a lot of work has been invested in improving container start-up times, Functions generally still enjoy significantly faster start-ups.","level":1,"lines":[22,23],"children":[{"type":"text","content":"Additionally, while a lot of work has been invested in improving container start-up times, Functions generally still enjoy significantly faster start-ups.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[24,27],"level":0},{"type":"inline","content":"Our next major decision was to choose global deployments instead of a deploying to a specific geographical region.\n![Functions deployment region](/images/blog/launch-week-4/thursday-functions/where-functions-deploy.png)\nLaunching Functions in a single region (e.g. close to your database) can present a lot of advantages if there are a lot of database operations being executed within your business logic.","level":1,"lines":[24,27],"children":[{"type":"text","content":"Our next major decision was to choose global deployments instead of a deploying to a specific geographical region.","level":0},{"type":"softbreak","level":0},{"type":"image","src":"/images/blog/launch-week-4/thursday-functions/where-functions-deploy.png","title":"","alt":"Functions deployment region","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Launching Functions in a single region (e.g. close to your database) can present a lot of advantages if there are a lot of database operations being executed within your business logic.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[28,29],"level":0},{"type":"inline","content":"However, Supabase already offers a flexible solution for that - [Database Functions](https://supabase.com/docs/guides/database/functions)! As such, for Supabase [Edge] Functions, we decided to deploy far-and-wide so that they are as close to your end-users as possible. You can even invoke a Database Function from an Edge Function, allowing you to enjoy the best of both worlds.","level":1,"lines":[28,29],"children":[{"type":"text","content":"However, Supabase already offers a flexible solution for that - ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/database/functions","title":"","level":0},{"type":"text","content":"Database Functions","level":1},{"type":"link_close","level":0},{"type":"text","content":"! As such, for Supabase [Edge] Functions, we decided to deploy far-and-wide so that they are as close to your end-users as possible. You can even invoke a Database Function from an Edge Function, allowing you to enjoy the best of both worlds.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[30,31],"level":0},{"type":"inline","content":"[The Deno runtime](#the-deno-runtime)","level":1,"lines":[30,31],"children":[{"type":"text","content":"The Deno runtime","level":0}],"lvl":2,"i":1,"seen":0,"slug":"the-deno-runtime"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[32,33],"level":0},{"type":"inline","content":"Deno is a modern Typescript and JavaScript runtime created with first-class support for web technologies. Here some of the reasons why we're excited about Deno:","level":1,"lines":[32,33],"children":[{"type":"text","content":"Deno is a modern Typescript and JavaScript runtime created with first-class support for web technologies. Here some of the reasons why we're excited about Deno:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[34,35],"level":0},{"type":"inline","content":"[Open-source](#open-source)","level":1,"lines":[34,35],"children":[{"type":"text","content":"Open-source","level":0}],"lvl":3,"i":2,"seen":0,"slug":"open-source"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[36,37],"level":0},{"type":"inline","content":"_Deno Deploy_ is Deno's hosted service and, although it is proprietary, the Deno CLI is open source. This means that the open source offering from Deno maintains feature-parity with the proprietary service. If you use Deno to develop functions locally, its behavior will be the same as when deployed on the edge, unlocking an incredible developer experience. We couldn't achieve a similarly seamless experience with e.g. AWS Lambda, no matter how much we tried.","level":1,"lines":[36,37],"children":[{"type":"em_open","level":0},{"type":"text","content":"Deno Deploy","level":1},{"type":"em_close","level":0},{"type":"text","content":" is Deno's hosted service and, although it is proprietary, the Deno CLI is open source. This means that the open source offering from Deno maintains feature-parity with the proprietary service. If you use Deno to develop functions locally, its behavior will be the same as when deployed on the edge, unlocking an incredible developer experience. We couldn't achieve a similarly seamless experience with e.g. AWS Lambda, no matter how much we tried.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[38,39],"level":0},{"type":"inline","content":"Deno even goes a step further than maintaining feature-parity, releasing all new APIs to open-source **first** before rolling them out to their hosted offering. This ensures that you can always develop and test bleeding-edge functionality locally, with all the tools you're used to and familiar with, before deploying to a hosted cloud environment.","level":1,"lines":[38,39],"children":[{"type":"text","content":"Deno even goes a step further than maintaining feature-parity, releasing all new APIs to open-source ","level":0},{"type":"strong_open","level":0},{"type":"text","content":"first","level":1},{"type":"strong_close","level":0},{"type":"text","content":" before rolling them out to their hosted offering. This ensures that you can always develop and test bleeding-edge functionality locally, with all the tools you're used to and familiar with, before deploying to a hosted cloud environment.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[40,41],"level":0},{"type":"inline","content":"[Batteries included](#batteries-included)","level":1,"lines":[40,41],"children":[{"type":"text","content":"Batteries included","level":0}],"lvl":3,"i":3,"seen":0,"slug":"batteries-included"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[42,43],"level":0},{"type":"inline","content":"Deno comes with all the tools needed for a modern, productive developer experience, so you can spend less time searching through third party modules, and more time being productive. Out of the box, it includes support for Typescript, ES modules, and provides [test runners](https://deno.land/manual/testing), [formatters](https://deno.land/manual/tools/formatter), [linters](https://deno.land/manual/tools/linter), [bundlers](https://deno.land/manual/tools/bundler) and a package manager.","level":1,"lines":[42,43],"children":[{"type":"text","content":"Deno comes with all the tools needed for a modern, productive developer experience, so you can spend less time searching through third party modules, and more time being productive. Out of the box, it includes support for Typescript, ES modules, and provides ","level":0},{"type":"link_open","href":"https://deno.land/manual/testing","title":"","level":0},{"type":"text","content":"test runners","level":1},{"type":"link_close","level":0},{"type":"text","content":", ","level":0},{"type":"link_open","href":"https://deno.land/manual/tools/formatter","title":"","level":0},{"type":"text","content":"formatters","level":1},{"type":"link_close","level":0},{"type":"text","content":", ","level":0},{"type":"link_open","href":"https://deno.land/manual/tools/linter","title":"","level":0},{"type":"text","content":"linters","level":1},{"type":"link_close","level":0},{"type":"text","content":", ","level":0},{"type":"link_open","href":"https://deno.land/manual/tools/bundler","title":"","level":0},{"type":"text","content":"bundlers","level":1},{"type":"link_close","level":0},{"type":"text","content":" and a package manager.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[44,45],"level":0},{"type":"inline","content":"[Secure by default](#secure-by-default)","level":1,"lines":[44,45],"children":[{"type":"text","content":"Secure by default","level":0}],"lvl":3,"i":4,"seen":0,"slug":"secure-by-default"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[46,47],"level":0},{"type":"inline","content":"Like modern web browsers, the Deno runtime executes your code in a secure sandbox by default. Functions cannot access environment variables, network or filesystem unless they are explicitly allowed. Supply chain attacks have been a daunting problem in the Node ecosystem and Deno mitigates many of these attacks by having a security-first model.","level":1,"lines":[46,47],"children":[{"type":"text","content":"Like modern web browsers, the Deno runtime executes your code in a secure sandbox by default. Functions cannot access environment variables, network or filesystem unless they are explicitly allowed. Supply chain attacks have been a daunting problem in the Node ecosystem and Deno mitigates many of these attacks by having a security-first model.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[48,49],"level":0},{"type":"inline","content":"[The Supa-Deno platform](#the-supa-deno-platform)","level":1,"lines":[48,49],"children":[{"type":"text","content":"The Supa-Deno platform","level":0}],"lvl":2,"i":5,"seen":0,"slug":"the-supa-deno-platform"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[50,51],"level":0},{"type":"inline","content":"When a request comes in to an Edge Function you deploy on Supabase, it first lands at a “Relay”. The [Relay](https://github.com/supabase/functions-relay) acts as an API gateway, authenticating the JWT, and providing some additional functionality like logging and rate-limiting. Upon receiving the incoming request, the Relay retrieves the metadata for your Function, and uses it to construct a unique identifier (”Deployment ID”) for the Function. This Deployment ID then gets passed over to the Deno Deploy platform, which takes care of invoking your Function securely, and passing your output back to the Relay, which in turn passes it back to your end-user.","level":1,"lines":[50,51],"children":[{"type":"text","content":"When a request comes in to an Edge Function you deploy on Supabase, it first lands at a “Relay”. The ","level":0},{"type":"link_open","href":"https://github.com/supabase/functions-relay","title":"","level":0},{"type":"text","content":"Relay","level":1},{"type":"link_close","level":0},{"type":"text","content":" acts as an API gateway, authenticating the JWT, and providing some additional functionality like logging and rate-limiting. Upon receiving the incoming request, the Relay retrieves the metadata for your Function, and uses it to construct a unique identifier (”Deployment ID”) for the Function. This Deployment ID then gets passed over to the Deno Deploy platform, which takes care of invoking your Function securely, and passing your output back to the Relay, which in turn passes it back to your end-user.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[52,53],"level":0},{"type":"inline","content":"If a user's request comes in for your Function before it has been cached by the Deno Deploy platform (a ”cold start”), it reaches out to Supabase to retrieve your Functions code, and any associated permissions and secrets. Even though this might sound like a lot of work, we've worked to ensure that these cold starts are still pretty spiffy.","level":1,"lines":[52,53],"children":[{"type":"text","content":"If a user's request comes in for your Function before it has been cached by the Deno Deploy platform (a ”cold start”), it reaches out to Supabase to retrieve your Functions code, and any associated permissions and secrets. Even though this might sound like a lot of work, we've worked to ensure that these cold starts are still pretty spiffy.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[54,55],"level":0},{"type":"inline","content":"![Functions architecture](/images/blog/launch-week-4/thursday-functions/functions-architecture.png)","level":1,"lines":[54,55],"children":[{"type":"image","src":"/images/blog/launch-week-4/thursday-functions/functions-architecture.png","title":"","alt":"Functions architecture","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[56,57],"level":0},{"type":"inline","content":"The primary goal of this architecture is to reduce the amount of work done in the hot path (marked in purple above). Each component in the hot path is globally distributed so that Edge Functions start up and respond to your users' requests fast.","level":1,"lines":[56,57],"children":[{"type":"text","content":"The primary goal of this architecture is to reduce the amount of work done in the hot path (marked in purple above). Each component in the hot path is globally distributed so that Edge Functions start up and respond to your users' requests fast.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[58,59],"level":0},{"type":"inline","content":"[Region: Earth 🌎](#region-earth-%F0%9F%8C%8E)","level":1,"lines":[58,59],"children":[{"type":"text","content":"Region: Earth 🌎","level":0}],"lvl":3,"i":6,"seen":0,"slug":"region-earth-🌎"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[60,61],"level":0},{"type":"inline","content":"Supabase Edge Functions are deployed to one region - Earth (and we're exploring Mars as an additional region 👽). Edge Functions are deployed to over 30 data centers in a matter of seconds, ensuring fast response times no matter where your users are located.","level":1,"lines":[60,61],"children":[{"type":"text","content":"Supabase Edge Functions are deployed to one region - Earth (and we're exploring Mars as an additional region 👽). Edge Functions are deployed to over 30 data centers in a matter of seconds, ensuring fast response times no matter where your users are located.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[62,63],"level":0},{"type":"inline","content":"[Database Functions](https://supabase.com/docs/guides/database/functions) (also served via our [REST and GraphQL APIs](https://supabase.com/docs/guides/database/api)) are colocated with your Postgres database, making them ideal for DB-intensive operations. These APIs can also be called seamlessly from your Supabase Edge Functions, making for a powerful combination.","level":1,"lines":[62,63],"children":[{"type":"link_open","href":"https://supabase.com/docs/guides/database/functions","title":"","level":0},{"type":"text","content":"Database Functions","level":1},{"type":"link_close","level":0},{"type":"text","content":" (also served via our ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/database/api","title":"","level":0},{"type":"text","content":"REST and GraphQL APIs","level":1},{"type":"link_close","level":0},{"type":"text","content":") are colocated with your Postgres database, making them ideal for DB-intensive operations. These APIs can also be called seamlessly from your Supabase Edge Functions, making for a powerful combination.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[64,65],"level":0},{"type":"inline","content":"[Scale to zero](#scale-to-zero)","level":1,"lines":[64,65],"children":[{"type":"text","content":"Scale to zero","level":0}],"lvl":3,"i":7,"seen":0,"slug":"scale-to-zero"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[66,67],"level":0},{"type":"inline","content":"Functions scale down to zero and you only pay for what you use. This makes them great for an extremely wide variety of workloads-whether you're starting out with extremely sporadic usage, or bursty request patterns, or using them consistently and heavily.","level":1,"lines":[66,67],"children":[{"type":"text","content":"Functions scale down to zero and you only pay for what you use. This makes them great for an extremely wide variety of workloads-whether you're starting out with extremely sporadic usage, or bursty request patterns, or using them consistently and heavily.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[68,69],"level":0},{"type":"inline","content":"[A fun and efficient Developer Experience](#a-fun-and-efficient-developer-experience)","level":1,"lines":[68,69],"children":[{"type":"text","content":"A fun and efficient Developer Experience","level":0}],"lvl":2,"i":8,"seen":0,"slug":"a-fun-and-efficient-developer-experience"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[70,71],"level":0},{"type":"inline","content":"Delivering a phenomenal Developer Experience is something we care about a lot at Supabase. In the serverless functions space, we've found that local development and testing, coupled with fast deploys are critical.","level":1,"lines":[70,71],"children":[{"type":"text","content":"Delivering a phenomenal Developer Experience is something we care about a lot at Supabase. In the serverless functions space, we've found that local development and testing, coupled with fast deploys are critical.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[72,73],"level":0},{"type":"inline","content":"We've [extended the Supabase CLI](https://supabase.com/docs/guides/local-development) to optimize the Edge Functions DX along these two dimensions. You get live-reloading, and a local Deno environment that matches the production environment, making for a great local iteration environment.","level":1,"lines":[72,73],"children":[{"type":"text","content":"We've ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/local-development","title":"","level":0},{"type":"text","content":"extended the Supabase CLI","level":1},{"type":"link_close","level":0},{"type":"text","content":" to optimize the Edge Functions DX along these two dimensions. You get live-reloading, and a local Deno environment that matches the production environment, making for a great local iteration environment.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[74,80],"level":0},{"type":"inline","content":"\u003cvideo width=\"99%\" autoPlay muted playsInline controls={true}\u003e\n  \u003csource\n    src=\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/marketing/blog/launch-week-4/functions-hot-reloading.mp4\"\n    type=\"video/mp4\"\n  /\u003e\n\u003c/video\u003e","level":1,"lines":[74,80],"children":[{"type":"text","content":"\u003cvideo width=\"99%\" autoPlay muted playsInline controls={true}\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003csource","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/marketing/blog/launch-week-4/functions-hot-reloading.mp4\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"type=\"video/mp4\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/video\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[81,82],"level":0},{"type":"inline","content":"Once you've tested your Function locally (or gone straight to production cos you live _on the edge_ ... get it?) it takes just seconds to deploy a new function and see the logs streaming in your Dashboard—ensuring a tight iteration loop with your production environment.","level":1,"lines":[81,82],"children":[{"type":"text","content":"Once you've tested your Function locally (or gone straight to production cos you live ","level":0},{"type":"em_open","level":0},{"type":"text","content":"on the edge","level":1},{"type":"em_close","level":0},{"type":"text","content":" ... get it?) it takes just seconds to deploy a new function and see the logs streaming in your Dashboard—ensuring a tight iteration loop with your production environment.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[83,89],"level":0},{"type":"inline","content":"\u003cvideo width=\"99%\" autoPlay muted playsInline controls={true}\u003e\n  \u003csource\n    src=\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/marketing/blog/launch-week-4/logs-polish.mov\"\n    type=\"video/mp4\"\n  /\u003e\n\u003c/video\u003e","level":1,"lines":[83,89],"children":[{"type":"text","content":"\u003cvideo width=\"99%\" autoPlay muted playsInline controls={true}\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003csource","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/marketing/blog/launch-week-4/logs-polish.mov\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"type=\"video/mp4\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/video\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[90,91],"level":0},{"type":"inline","content":"Our observability pipeline has been built [using Logflare](https://supabase.com/blog/supabase-acquires-logflare). Structured logs from your Edge Functions get sent to Logflare, which also processes them into metrics. Your Supabase Dashboard is able to reach out to Logflare endpoints, and with some of the [(not so) secret sauce](https://supabase.com/blog/supabase-studio) from our front-end team, you get responsive charts and logs for your Edge Functions.","level":1,"lines":[90,91],"children":[{"type":"text","content":"Our observability pipeline has been built ","level":0},{"type":"link_open","href":"https://supabase.com/blog/supabase-acquires-logflare","title":"","level":0},{"type":"text","content":"using Logflare","level":1},{"type":"link_close","level":0},{"type":"text","content":". Structured logs from your Edge Functions get sent to Logflare, which also processes them into metrics. Your Supabase Dashboard is able to reach out to Logflare endpoints, and with some of the ","level":0},{"type":"link_open","href":"https://supabase.com/blog/supabase-studio","title":"","level":0},{"type":"text","content":"(not so) secret sauce","level":1},{"type":"link_close","level":0},{"type":"text","content":" from our front-end team, you get responsive charts and logs for your Edge Functions.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[92,94],"level":0},{"type":"inline","content":"![new —\u003e deploy —\u003e invoke —\u003e logs](/images/blog/launch-week-4/thursday-functions/functions-new-deploy-invoke-logs.gif)\nnew —\u003e deploy —\u003e invoke —\u003e logs","level":1,"lines":[92,94],"children":[{"type":"image","src":"/images/blog/launch-week-4/thursday-functions/functions-new-deploy-invoke-logs.gif","title":"","alt":"new —\u003e deploy —\u003e invoke —\u003e logs","level":0},{"type":"softbreak","level":0},{"type":"text","content":"new —\u003e deploy —\u003e invoke —\u003e logs","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[95,96],"level":0},{"type":"inline","content":"To round off the end-to-end experience, `supabase-js` now works in Deno, making it easy to interact with the rest of your Supabase project from within your Edge Functions. Your project's URLs, API keys, and database connection strings are made available as environment variables within your Function to make this even easier. And if you want to interact with third-party APIs which require a secret key, such as Stripe, you can easily and securely make these available to your Function as environment variables via the Supabase CLI. To get an idea for what is possible, check out our examples page [here](https://github.com/supabase/supabase/tree/master/examples/edge-functions).","level":1,"lines":[95,96],"children":[{"type":"text","content":"To round off the end-to-end experience, ","level":0},{"type":"code","content":"supabase-js","block":false,"level":0},{"type":"text","content":" now works in Deno, making it easy to interact with the rest of your Supabase project from within your Edge Functions. Your project's URLs, API keys, and database connection strings are made available as environment variables within your Function to make this even easier. And if you want to interact with third-party APIs which require a secret key, such as Stripe, you can easily and securely make these available to your Function as environment variables via the Supabase CLI. To get an idea for what is possible, check out our examples page ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase/tree/master/examples/edge-functions","title":"","level":0},{"type":"text","content":"here","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[97,98],"level":0},{"type":"inline","content":"[Quickstart](#quickstart)","level":1,"lines":[97,98],"children":[{"type":"text","content":"Quickstart","level":0}],"lvl":2,"i":9,"seen":0,"slug":"quickstart"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[99,106],"level":0},{"type":"inline","content":"\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"video-with-border w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/5OWH9c4u68M\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen","level":1,"lines":[99,106],"children":[{"type":"text","content":"\u003cdiv className=\"video-container\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ciframe","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"video-with-border w-full\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://www.youtube-nocookie.com/embed/5OWH9c4u68M\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"frameBorder=\"1\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allowFullScreen","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"blockquote_open","lines":[106,108],"level":0},{"type":"paragraph_open","tight":false,"lines":[106,108],"level":1},{"type":"inline","content":"\u003c/iframe\u003e\n\u003c/div\u003e","level":2,"lines":[106,108],"children":[{"type":"text","content":"\u003c/iframe\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"paragraph_open","tight":false,"lines":[109,111],"level":0},{"type":"inline","content":"Can't wait to get started? Develop, test, and deploy your first Supabase Edge Function in less than 7 minutes with our [Quickstart Video Tutorial](https://youtu.be/5OWH9c4u68M)!\nCheck the [Edge Functions Docs](https://supabase.com/docs/guides/functions) for the steps.","level":1,"lines":[109,111],"children":[{"type":"text","content":"Can't wait to get started? Develop, test, and deploy your first Supabase Edge Function in less than 7 minutes with our ","level":0},{"type":"link_open","href":"https://youtu.be/5OWH9c4u68M","title":"","level":0},{"type":"text","content":"Quickstart Video Tutorial","level":1},{"type":"link_close","level":0},{"type":"text","content":"!","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Check the ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/functions","title":"","level":0},{"type":"text","content":"Edge Functions Docs","level":1},{"type":"link_close","level":0},{"type":"text","content":" for the steps.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[112,113],"level":0},{"type":"inline","content":"[Example apps: Mobile payments with React Native and Flutter](#example-apps-mobile-payments-with-react-native-and-flutter)","level":1,"lines":[112,113],"children":[{"type":"text","content":"Example apps: Mobile payments with React Native and Flutter","level":0}],"lvl":2,"i":10,"seen":0,"slug":"example-apps-mobile-payments-with-react-native-and-flutter"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[114,115],"level":0},{"type":"inline","content":"When researching use-cases with all of you, [processing payments](https://github.com/supabase/supabase/discussions/4269#discussioncomment-1748067) was being pointed out the most, so naturally, to support the launch, we've created example apps in collaboration with Stripe for [React Native](https://github.com/supabase-community/expo-stripe-payments-with-supabase-functions) and [Flutter](https://github.com/supabase-community/flutter-stripe-payments-with-supabase-functions), to allow you to get started and earn money even faster.","level":1,"lines":[114,115],"children":[{"type":"text","content":"When researching use-cases with all of you, ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase/discussions/4269#discussioncomment-1748067","title":"","level":0},{"type":"text","content":"processing payments","level":1},{"type":"link_close","level":0},{"type":"text","content":" was being pointed out the most, so naturally, to support the launch, we've created example apps in collaboration with Stripe for ","level":0},{"type":"link_open","href":"https://github.com/supabase-community/expo-stripe-payments-with-supabase-functions","title":"","level":0},{"type":"text","content":"React Native","level":1},{"type":"link_close","level":0},{"type":"text","content":" and ","level":0},{"type":"link_open","href":"https://github.com/supabase-community/flutter-stripe-payments-with-supabase-functions","title":"","level":0},{"type":"text","content":"Flutter","level":1},{"type":"link_close","level":0},{"type":"text","content":", to allow you to get started and earn money even faster.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[116,117],"level":0},{"type":"inline","content":"![functions-payment-example](/images/blog/launch-week-4/thursday-functions/functions-payment.gif)","level":1,"lines":[116,117],"children":[{"type":"image","src":"/images/blog/launch-week-4/thursday-functions/functions-payment.gif","title":"","alt":"functions-payment-example","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[118,119],"level":0},{"type":"inline","content":"[Can I host static sites](#can-i-host-static-sites)","level":1,"lines":[118,119],"children":[{"type":"text","content":"Can I host static sites","level":0}],"lvl":2,"i":11,"seen":0,"slug":"can-i-host-static-sites"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[120,121],"level":0},{"type":"inline","content":"Even though Deno supports [rendering `jsx` and `tsx` files](https://deno.land/manual/jsx_dom/jsx), we don't plan on becoming a website hosting provider with Edge Functions. For now, Edge Functions can only be invoked via the `POST` method. Offering secure and fast web hosting with a good developer experience is a hard problem to solve and there are [other](https://www.netlify.com/) [fantastic](https://vercel.com/) [providers](https://pages.cloudflare.com/) which do exactly that.","level":1,"lines":[120,121],"children":[{"type":"text","content":"Even though Deno supports ","level":0},{"type":"link_open","href":"https://deno.land/manual/jsx_dom/jsx","title":"","level":0},{"type":"text","content":"rendering ","level":1},{"type":"code","content":"jsx","block":false,"level":1},{"type":"text","content":" and ","level":1},{"type":"code","content":"tsx","block":false,"level":1},{"type":"text","content":" files","level":1},{"type":"link_close","level":0},{"type":"text","content":", we don't plan on becoming a website hosting provider with Edge Functions. For now, Edge Functions can only be invoked via the ","level":0},{"type":"code","content":"POST","block":false,"level":0},{"type":"text","content":" method. Offering secure and fast web hosting with a good developer experience is a hard problem to solve and there are ","level":0},{"type":"link_open","href":"https://www.netlify.com/","title":"","level":0},{"type":"text","content":"other","level":1},{"type":"link_close","level":0},{"type":"text","content":" ","level":0},{"type":"link_open","href":"https://vercel.com/","title":"","level":0},{"type":"text","content":"fantastic","level":1},{"type":"link_close","level":0},{"type":"text","content":" ","level":0},{"type":"link_open","href":"https://pages.cloudflare.com/","title":"","level":0},{"type":"text","content":"providers","level":1},{"type":"link_close","level":0},{"type":"text","content":" which do exactly that.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[122,123],"level":0},{"type":"inline","content":"From our experience launching Supabase Storage last year, becoming a hosting provider is not something you want to take lightly. Malicious folks will upload their phishing sites whenever they get a chance and this can quickly spiral out of control, causing you to land on the denylist in the [Safe Browsing dataset](https://safebrowsing.google.com/). Even though we removed the project within minutes after being notified, various ISPs had already cached the information from the Safe browsing dataset and were “helping” their users by restricting access to the Supabase domain. This isn't something we're planning to repeat. But as always, we'd love to hear your use cases and feature requests, we're just one [GitHub discussion](https://github.com/supabase/supabase/discussions) away!","level":1,"lines":[122,123],"children":[{"type":"text","content":"From our experience launching Supabase Storage last year, becoming a hosting provider is not something you want to take lightly. Malicious folks will upload their phishing sites whenever they get a chance and this can quickly spiral out of control, causing you to land on the denylist in the ","level":0},{"type":"link_open","href":"https://safebrowsing.google.com/","title":"","level":0},{"type":"text","content":"Safe Browsing dataset","level":1},{"type":"link_close","level":0},{"type":"text","content":". Even though we removed the project within minutes after being notified, various ISPs had already cached the information from the Safe browsing dataset and were “helping” their users by restricting access to the Supabase domain. This isn't something we're planning to repeat. But as always, we'd love to hear your use cases and feature requests, we're just one ","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase/discussions","title":"","level":0},{"type":"text","content":"GitHub discussion","level":1},{"type":"link_close","level":0},{"type":"text","content":" away!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[124,125],"level":0},{"type":"inline","content":"[Roadmap](#roadmap)","level":1,"lines":[124,125],"children":[{"type":"text","content":"Roadmap","level":0}],"lvl":2,"i":12,"seen":0,"slug":"roadmap"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[126,127],"level":0},{"type":"inline","content":"These are some of the features that are on our roadmap for functions.","level":1,"lines":[126,127],"children":[{"type":"text","content":"These are some of the features that are on our roadmap for functions.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[128,135],"level":0},{"type":"list_item_open","lines":[128,129],"level":1},{"type":"paragraph_open","tight":true,"lines":[128,129],"level":2},{"type":"inline","content":"Cron functions to trigger your function on a schedule","level":3,"lines":[128,129],"children":[{"type":"text","content":"Cron functions to trigger your function on a schedule","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[129,130],"level":1},{"type":"paragraph_open","tight":true,"lines":[129,130],"level":2},{"type":"inline","content":"Studio support and self hosting multiple functions","level":3,"lines":[129,130],"children":[{"type":"text","content":"Studio support and self hosting multiple functions","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[130,131],"level":1},{"type":"paragraph_open","tight":true,"lines":[130,131],"level":2},{"type":"inline","content":"Optimizing cold start times","level":3,"lines":[130,131],"children":[{"type":"text","content":"Optimizing cold start times","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[131,132],"level":1},{"type":"paragraph_open","tight":true,"lines":[131,132],"level":2},{"type":"inline","content":"Wasm is a great fit for running functions and companies like [Suborbital](https://suborbital.dev/) are leading the charge by making it easy to write functions that run in a Wasm-based runtime. Deno supports Wasm natively and we plan on allowing users to write their functions in languages that compile down to Wasm like Rust.","level":3,"lines":[131,132],"children":[{"type":"text","content":"Wasm is a great fit for running functions and companies like ","level":0},{"type":"link_open","href":"https://suborbital.dev/","title":"","level":0},{"type":"text","content":"Suborbital","level":1},{"type":"link_close","level":0},{"type":"text","content":" are leading the charge by making it easy to write functions that run in a Wasm-based runtime. Deno supports Wasm natively and we plan on allowing users to write their functions in languages that compile down to Wasm like Rust.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[132,133],"level":1},{"type":"paragraph_open","tight":true,"lines":[132,133],"level":2},{"type":"inline","content":"We can expose Deno's permission model so that you can lock down your functions further by restricting outbound connections to specific domains.","level":3,"lines":[132,133],"children":[{"type":"text","content":"We can expose Deno's permission model so that you can lock down your functions further by restricting outbound connections to specific domains.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[133,135],"level":1},{"type":"paragraph_open","tight":true,"lines":[133,134],"level":2},{"type":"inline","content":"To invoke functions, you need a valid JWT signed by your project's JWT secret. We plan on making this verification optional. This enables more use cases like hooking up Supabase functions to Stripe webhooks where the function manages it's own authentication.","level":3,"lines":[133,134],"children":[{"type":"text","content":"To invoke functions, you need a valid JWT signed by your project's JWT secret. We plan on making this verification optional. This enables more use cases like hooking up Supabase functions to Stripe webhooks where the function manages it's own authentication.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[135,136],"level":0},{"type":"inline","content":"Give us a shout with your feedback and excited to see what you build in the hackathon!","level":1,"lines":[135,136],"children":[{"type":"text","content":"Give us a shout with your feedback and excited to see what you build in the hackathon!","level":0}]},{"type":"paragraph_close","tight":false,"level":0}],"content":"- [Choosing a platform](#choosing-a-platform)\n- [The Deno runtime](#the-deno-runtime)\n  * [Open-source](#open-source)\n  * [Batteries included](#batteries-included)\n  * [Secure by default](#secure-by-default)\n- [The Supa-Deno platform](#the-supa-deno-platform)\n  * [Region: Earth 🌎](#region-earth-%F0%9F%8C%8E)\n  * [Scale to zero](#scale-to-zero)\n- [A fun and efficient Developer Experience](#a-fun-and-efficient-developer-experience)\n- [Quickstart](#quickstart)\n- [Example apps: Mobile payments with React Native and Flutter](#example-apps-mobile-payments-with-react-native-and-flutter)\n- [Can I host static sites](#can-i-host-static-sites)\n- [Roadmap](#roadmap)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-edge-functions"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>