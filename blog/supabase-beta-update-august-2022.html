<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase Beta August 2022</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Launch Week Special. See everything we shipped, plus winners of the Hackathon and the extended Community Highlights" data-next-head=""/><meta property="og:title" content="Supabase Beta August 2022" data-next-head=""/><meta property="og:description" content="Launch Week Special. See everything we shipped, plus winners of the Hackathon and the extended Community Highlights" data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-beta-update-august-2022" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2022-09-07" data-next-head=""/><meta property="article:author" content="https://github.com/awalias" data-next-head=""/><meta property="article:tag" content="release-notes" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/2022-august/monthly-update-august-2022.jpg" data-next-head=""/><meta property="og:image:alt" content="Supabase Beta August 2022 thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Supabase Beta August 2022</h1><div class="text-light flex space-x-3 text-sm"><p>07 Sep 2022</p><p>•</p><p>5 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/awalias"><div class="flex items-center gap-3"><div class="w-10"><img alt="Ant Wilson avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fawalias.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fawalias.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fawalias.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Ant Wilson</span><span class="text-foreground-lighter mb-0 text-xs">CTO and Co-Founder</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Supabase Beta August 2022" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-august%2Fmonthly-update-august-2022.jpg&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-august%2Fmonthly-update-august-2022.jpg&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-august%2Fmonthly-update-august-2022.jpg&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-august%2Fmonthly-update-august-2022.jpg&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-august%2Fmonthly-update-august-2022.jpg&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-august%2Fmonthly-update-august-2022.jpg&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-august%2Fmonthly-update-august-2022.jpg&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-august%2Fmonthly-update-august-2022.jpg&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-august%2Fmonthly-update-august-2022.jpg&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-august%2Fmonthly-update-august-2022.jpg&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2F2022-august%2Fmonthly-update-august-2022.jpg&amp;w=3840&amp;q=100"/></div><p>This month the Beta Update is a Launch Week special. #SupaLaunchWeek 5 just happened and it was a big one, so we revisit everything we shipped, congratulate the winners of the Supa Hackathon, and of course we have the extended the Community Highlights.</p>
<h2 id="day-1---supabase-cli-v1-and-management-api-beta" class="group scroll-mt-24">Day 1 - Supabase CLI v1 and Management API Beta<a href="#day-1---supabase-cli-v1-and-management-api-beta" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>The Supabase CLI is now in v1.0 (including the ability to generate TypeScript types 🎉). We also released the Management API Beta, a REST API that opens the door to a whole new suite of integrations (Zapier, Terraform, Pulumi, you name it). Full programmatic control of your projects and orgs is on the way.</p>
<p><a href="https://supabase.com/blog/supabase-cli-v1-and-admin-api-beta">Blog Post</a></p>
<p><a href="https://www.youtube.com/watch?v=OpPOaJI_Z28">Video Announcement</a></p>
<h2 id="day-2---supabase-js-v2-release-candidate" class="group scroll-mt-24">Day 2 - supabase-js v2 Release Candidate<a href="#day-2---supabase-js-v2-release-candidate" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p><strong><a href="https://github.com/supabase/supabase-js">supabase-js v2</a></strong> focuses on &quot;quality-of-life&quot; improvements for developers and includes Type Support, new Auth Methods, async Auth overhaul, improvements for Edge Functions, and more. We couldn&#x27;t have done this without our amazing Community, so thanks a lot to everyone who contributed.</p>
<p>Try it out by running <code class="short-inline-codeblock">npm i @supabase/supabase-js@rc</code></p>
<p><a href="https://supabase.com/blog/supabase-js-v2">Blog Post</a></p>
<p><a href="https://youtu.be/iqZlPtl_b-I">Video Announcement</a></p>
<h2 id="day-3---supabase-is-soc2-compliant" class="group scroll-mt-24">Day 3 - Supabase is SOC2 compliant<a href="#day-3---supabase-is-soc2-compliant" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>Our customers can rest assured knowing their information is secure and private 🔒. The blog post explains the process we went through to get there and is very useful for anyone building a SaaS product.</p>
<p><a href="https://supabase.com/blog/supabase-soc2">Blog Post</a></p>
<p><a href="../security.html">Security at Supabase</a></p>
<p><a href="https://youtu.be/6bGQotxisoY">Video Announcement</a></p>
<h2 id="day-4---realtime-multiplayer-edition" class="group scroll-mt-24">Day 4 - Realtime: Multiplayer Edition<a href="#day-4---realtime-multiplayer-edition" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>Next level Realtime is here ⚡️. Presence and Broadcast are two key blocks developers can use to build the digital experiences users want. All projects now have access to these features. Try it out in combination with supabase-js v2.</p>
<p><a href="https://supabase.com/blog/supabase-realtime-multiplayer-general-availability">Blog Post</a></p>
<p><a href="https://youtu.be/CGZr5tybW18">Video Announcement</a></p>
<h2 id="day-5---community-day-and-one-more-thing" class="group scroll-mt-24">Day 5 - Community Day and One More Thing<a href="#day-5---community-day-and-one-more-thing" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>We wrapped Launch Week 5 with contributors, partners, and friends and the traditional One More Thing... that was actually SIX more things: <a href="https://supabase.com/blog/supabase-vault">Supabase Vault</a>, Auth UI, Dashboard permissions, <a href="https://supabase.com/blog/pg-jsonschema-a-postgres-extension-for-json-validation">JSON schema validation</a>, pg_graphql v0.4.0, MFA early-access.</p>
<p><a href="https://supabase.com/blog/launch-week-5-community-day">Community Day Blog Post</a></p>
<p><a href="https://www.youtube.com/watch?v=s9UePQjLT0U">Community Day Video Announcement</a></p>
<p><a href="https://supabase.com/blog/launch-week-5-one-more-thing">One More Thing Blog Post</a></p>
<h2 id="launch-week-5-hackathon" class="group scroll-mt-24">Launch Week 5 Hackathon<a href="#launch-week-5-hackathon" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>We had a huge amount of open source submissions 🤯. The selection process was not easy as it wasn&#x27;t only quantity, but also quality. After a thorough review, we declared <a href="https://github.com/psteinroe/supabase-cache-helpers">Supabase Cache Helpers</a> the overall winner of the $1500 GitHub sponsorship and Gold SupaCap. Congratulations to <a href="https://twitter.com/psteinroe">@psteinroe</a> 👏</p>
<p><a href="launch-week-5-hackathon-winners.html">Full list of winners</a></p>
<p><a href="https://www.madewithsupabase.com/launch-week-5">Check all the submissions in Made with Supabase</a></p>
<hr/>
<h2 id="platform-updates" class="group scroll-mt-24">Platform Updates<a href="#platform-updates" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>The following changes to the Supabase Platform will take effect from September 11th at 7 pm PDT.</p>
<ul>
<li>HTTP API requests to Supabase will automatically be redirected to HTTPS.</li>
<li>The API Key was passed to Supabase both in the <code class="short-inline-codeblock">Authorization</code> header and in a separate <code class="short-inline-codeblock">apiKey</code> header. This led to confusion among new users of Supabase who used the API directly. It is no longer required to send the anon key or service key via the <code class="short-inline-codeblock">apiKey</code> header. If you are using Supabase via our client libraries, no change is required from your side.</li>
</ul>
<h2 id="supabase-migration-guides" class="group scroll-mt-24">Supabase Migration Guides<a href="#supabase-migration-guides" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>Our guides and tools make it super easy to migrate your projects to Supabase:</p>
<ul>
<li><a href="https://supabase.com/docs/guides/migrations/firebase-auth">Firebase Auth Migration</a></li>
<li><a href="https://supabase.com/docs/guides/migrations/firestore-data">Firestore Data Migration</a></li>
<li><a href="https://supabase.com/docs/guides/migrations/firebase-storage">Firebase Storage Migration</a></li>
<li><a href="https://supabase.com/docs/guides/migrations/heroku">Migrate from Heroku to Supabase</a></li>
</ul>
<h2 id="webinar-how-netlify-and-supabase-enables-supa-dx" class="group scroll-mt-24">Webinar: How Netlify and Supabase Enables “Supa” DX<a href="#webinar-how-netlify-and-supabase-enables-supa-dx" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>Our friends from Netlify invited Ant to their new webinar series. He and Netlify&#x27;s VP of Partnerships &amp; Ecosystems, Steven Larsen, will show you how to:</p>
<ul>
<li>Deploy Supabase&#x27;s open source backend together with Netlify</li>
<li>Build User Management without handing over user data to any third parties</li>
<li>Upload files and folders to the cloud without needing to tack on additional tools</li>
</ul>
<p><a href="https://www.netlify.com/resources/webinars/how-netlify-supabase-enables-supa-dx/">Save your seat.</a></p>
<h2 id="extended-community-highlights" class="group scroll-mt-24">Extended Community Highlights<a href="#extended-community-highlights" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<ul>
<li>Inian shared our journey with Cloudflare. <a href="https://twitter.com/Cloudflare/status/1557728943901675520">Video</a></li>
<li>Supabase Tips: Introduction to Supabase Storage. <a href="https://www.youtube.com/watch?v=J9mTPY8rIXE">Video</a></li>
<li>Zack DeRose playing around with Supabase and Nx. <a href="https://www.youtube.com/watch?v=OTh5GBBfr4E">Part 1</a> | <a href="https://www.youtube.com/watch?v=_5gJi_xwpzk">Part 2</a></li>
<li>A new guide showing how to combine Supabase and Directus. <a href="https://supabase.com/partners/integrations/directus">Guide</a></li>
<li>Supabase with Flutter course on raywenderlich. <a href="https://www.raywenderlich.com/33619647-supabase-with-flutter">Course</a></li>
<li>Build a full-stack app with Next.js and Supabase on the LogRocket blog. <a href="https://blog.logrocket.com/build-full-stack-app-next-js-supabase/">Tutorial</a></li>
<li>Supabase Crash Course by The Net Ninja. <a href="https://www.youtube.com/watch?v=ydz7Dj5QHKY&amp;list=PL4cUxeGkcC9hUb6sHthUEwG7r9VDPBMKO">Video Courses</a></li>
<li>How to build a Grocery Application with Webflow CMS using DhiWise. <a href="https://dev.to/saloni137/how-to-build-a-grocery-application-with-webflow-cms-using-dhiwise-1a72">Tutorial</a></li>
<li>How To Create a Full Stack app with SolidJS, Supabase, and TailwindCSS <a href="https://blog.chetanverma.com/how-to-create-a-full-stack-app-with-solidjs-supabase-and-tailwindcss">Video and Tutorial.</a></li>
<li>The WalletConnect Cloud now supports Sign in with Ethereum. <a href="https://twitter.com/TheHarryET/status/1559861021845643265">Announcement</a>.</li>
<li>Building In Public: Cartta Tech Stack. <a href="https://dev.to/fvaldes33/building-in-public-cartta-tech-stack-5en0">Article</a></li>
<li>Supabase + Vue 3 in 12 minutes. <a href="https://www.youtube.com/watch?v=YN32uVqAXw8&amp;feature=emb_title">Video</a></li>
</ul>
<h2 id="were-hiring" class="group scroll-mt-24">We&#x27;re hiring<a href="#were-hiring" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Come join one of the fastest growing open source projects ever 🤗</p>
<ul>
<li><a href="https://boards.greenhouse.io/supabase/jobs/4652333004">Lead Billing Engineer</a></li>
<li><a href="https://boards.greenhouse.io/supabase/jobs/4594393004">Customer Success (US time zone)</a></li>
<li><a href="https://boards.greenhouse.io/supabase/jobs/4191650004">Support Engineers</a></li>
<li><a href="https://boards.greenhouse.io/supabase">View all our openings</a></li>
</ul>
<hr/>
<h2 id="meme-zone" class="group scroll-mt-24">Meme Zone<a href="#meme-zone" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>If you made it this far in the blog post you deserve a treat. <a href="https://twitter.com/supabase">Follow us on Twitter</a> for more.</p>
<p></p>
<h2 id="get-started" class="group scroll-mt-24">Get started<a href="#get-started" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ul>
<li>Start using Supabase today: <strong><a href="https://supabase.com/dashboard/">supabase.com/dashboard</a></strong></li>
<li>Make sure to <strong><a href="https://github.com/supabase/supabase">star us on GitHub</a></strong></li>
<li>Follow us <strong><a href="https://twitter.com/supabase">on Twitter</a></strong></li>
<li>Subscribe to our <strong><a href="https://www.youtube.com/c/supabase">YouTube channel</a></strong></li>
</ul></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-update-august-2022&amp;text=Supabase%20Beta%20August%202022"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-update-august-2022&amp;text=Supabase%20Beta%20August%202022"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-update-august-2022&amp;t=Supabase%20Beta%20August%202022"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="choosing-a-postgres-primary-key.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Choosing a Postgres Primary Key</h4><p class="small">8 September 2022</p></div></div></div></div></a></div><div><a href="launch-week-5-hackathon-winners.html"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Launch Week 5 Hackathon Winners</h4><p class="small">25 August 2022</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/release-notes"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">release-notes</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#day-1---supabase-cli-v1-and-management-api-beta">Day 1 - Supabase CLI v1 and Management API Beta</a></li>
<li><a href="#day-2---supabase-js-v2-release-candidate">Day 2 - supabase-js v2 Release Candidate</a></li>
<li><a href="#day-3---supabase-is-soc2-compliant">Day 3 - Supabase is SOC2 compliant</a></li>
<li><a href="#day-4---realtime-multiplayer-edition">Day 4 - Realtime: Multiplayer Edition</a></li>
<li><a href="#day-5---community-day-and-one-more-thing">Day 5 - Community Day and One More Thing</a></li>
<li><a href="#launch-week-5-hackathon">Launch Week 5 Hackathon</a></li>
<li><a href="#platform-updates">Platform Updates</a></li>
<li><a href="#supabase-migration-guides">Supabase Migration Guides</a></li>
<li><a href="#webinar-how-netlify-and-supabase-enables-supa-dx">Webinar: How Netlify and Supabase Enables “Supa” DX</a></li>
<li><a href="#extended-community-highlights">Extended Community Highlights</a></li>
<li><a href="#were-hiring">We&#x27;re hiring</a></li>
<li><a href="#meme-zone">Meme Zone</a></li>
<li><a href="#get-started">Get started</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-update-august-2022&amp;text=Supabase%20Beta%20August%202022"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-update-august-2022&amp;text=Supabase%20Beta%20August%202022"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-beta-update-august-2022&amp;t=Supabase%20Beta%20August%202022"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"choosing-a-postgres-primary-key","title":"Choosing a Postgres Primary Key","description":"Turns out the question of which identifier to use as a Primary Key is complicated -- we're going to dive into some of the complexity and inherent trade-offs, and figure things out","author":"victor","image":"primary-keys/postgres-primary-key.jpg","thumb":"primary-keys/postgres-primary-key.jpg","categories":["postgres"],"tags":["postgres","planetpg"],"date":"2022-09-08","toc_depth":3,"formattedDate":"8 September 2022","readingTime":"18 minute read","url":"/blog/choosing-a-postgres-primary-key","path":"/blog/choosing-a-postgres-primary-key"},"nextPost":{"slug":"launch-week-5-hackathon-winners","title":"Launch Week 5 Hackathon Winners","description":"Announcing the winners of the Launch Week 5 Hackathon!","author":"tyler_shukert","image":"lw5-hackathon-winners/launch-week-5-hackathon-winners.jpeg","thumb":"lw5-hackathon-winners/launch-week-5-hackathon-winners.jpeg","categories":["developers"],"tags":["hackathon","community"],"date":"2022-08-25","toc_depth":2,"formattedDate":"25 August 2022","readingTime":"5 minute read","url":"/blog/launch-week-5-hackathon-winners","path":"/blog/launch-week-5-hackathon-winners"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-beta-update-august-2022","source":"\nThis month the Beta Update is a Launch Week special. #SupaLaunchWeek 5 just happened and it was a big one, so we revisit everything we shipped, congratulate the winners of the Supa Hackathon, and of course we have the extended the Community Highlights.\n\n## Day 1 - Supabase CLI v1 and Management API Beta\n\n![Day 1 - Supabase CLI v1 and Management API Beta](/images/blog/2022-august/CLI.png)\n\nThe Supabase CLI is now in v1.0 (including the ability to generate TypeScript types 🎉). We also released the Management API Beta, a REST API that opens the door to a whole new suite of integrations (Zapier, Terraform, Pulumi, you name it). Full programmatic control of your projects and orgs is on the way.\n\n[Blog Post](https://supabase.com/blog/supabase-cli-v1-and-admin-api-beta)\n\n[Video Announcement](https://www.youtube.com/watch?v=OpPOaJI_Z28)\n\n## Day 2 - supabase-js v2 Release Candidate\n\n![Day 2 - supabase-js v2 Release Candidate](/images/blog/2022-august/supabase_js.png)\n\n**[supabase-js v2](https://github.com/supabase/supabase-js)** focuses on \"quality-of-life\" improvements for developers and includes Type Support, new Auth Methods, async Auth overhaul, improvements for Edge Functions, and more. We couldn't have done this without our amazing Community, so thanks a lot to everyone who contributed.\n\nTry it out by running `npm i @supabase/supabase-js@rc`\n\n[Blog Post](https://supabase.com/blog/supabase-js-v2)\n\n[Video Announcement](https://youtu.be/iqZlPtl_b-I)\n\n## Day 3 - Supabase is SOC2 compliant\n\n![Day 3 - Supabase is SOC2 compliant](/images/blog/2022-august/security.png)\n\nOur customers can rest assured knowing their information is secure and private 🔒. The blog post explains the process we went through to get there and is very useful for anyone building a SaaS product.\n\n[Blog Post](https://supabase.com/blog/supabase-soc2)\n\n[Security at Supabase](https://supabase.com/security)\n\n[Video Announcement](https://youtu.be/6bGQotxisoY)\n\n## Day 4 - Realtime: Multiplayer Edition\n\n![Day 4 - Realtime: Multiplayer Edition](/images/blog/2022-august/realtime.png)\n\nNext level Realtime is here ⚡️. Presence and Broadcast are two key blocks developers can use to build the digital experiences users want. All projects now have access to these features. Try it out in combination with supabase-js v2.\n\n[Blog Post](https://supabase.com/blog/supabase-realtime-multiplayer-general-availability)\n\n[Video Announcement](https://youtu.be/CGZr5tybW18)\n\n## Day 5 - Community Day and One More Thing\n\n![Day 5 - Community Day and One More Thing](/images/blog/2022-august/day-5.png)\n\nWe wrapped Launch Week 5 with contributors, partners, and friends and the traditional One More Thing... that was actually SIX more things: [Supabase Vault](https://supabase.com/blog/supabase-vault), Auth UI, Dashboard permissions, [JSON schema validation](https://supabase.com/blog/pg-jsonschema-a-postgres-extension-for-json-validation), pg_graphql v0.4.0, MFA early-access.\n\n[Community Day Blog Post](https://supabase.com/blog/launch-week-5-community-day)\n\n[Community Day Video Announcement](https://www.youtube.com/watch?v=s9UePQjLT0U)\n\n[One More Thing Blog Post](https://supabase.com/blog/launch-week-5-one-more-thing)\n\n## Launch Week 5 Hackathon\n\n![Launch Week 5 Hackathon](/images/blog/2022-august/hackathon.png)\n\nWe had a huge amount of open source submissions 🤯. The selection process was not easy as it wasn't only quantity, but also quality. After a thorough review, we declared [Supabase Cache Helpers](https://github.com/psteinroe/supabase-cache-helpers) the overall winner of the $1500 GitHub sponsorship and Gold SupaCap. Congratulations to [@psteinroe](https://twitter.com/psteinroe) 👏\n\n[Full list of winners](https://supabase.com/blog/launch-week-5-hackathon-winners)\n\n[Check all the submissions in Made with Supabase](https://www.madewithsupabase.com/launch-week-5)\n\n---\n\n## Platform Updates\n\nThe following changes to the Supabase Platform will take effect from September 11th at 7 pm PDT.\n\n- HTTP API requests to Supabase will automatically be redirected to HTTPS.\n- The API Key was passed to Supabase both in the `Authorization` header and in a separate `apiKey` header. This led to confusion among new users of Supabase who used the API directly. It is no longer required to send the anon key or service key via the `apiKey` header. If you are using Supabase via our client libraries, no change is required from your side.\n\n## Supabase Migration Guides\n\n![Supabase Migration Guides](/images/blog/2022-august/migration-to-supabase-guides.jpg)\n\nOur guides and tools make it super easy to migrate your projects to Supabase:\n\n- [Firebase Auth Migration](https://supabase.com/docs/guides/migrations/firebase-auth)\n- [Firestore Data Migration](https://supabase.com/docs/guides/migrations/firestore-data)\n- [Firebase Storage Migration](https://supabase.com/docs/guides/migrations/firebase-storage)\n- [Migrate from Heroku to Supabase](https://supabase.com/docs/guides/migrations/heroku)\n\n## Webinar: How Netlify and Supabase Enables “Supa” DX\n\n![Webinar: How Netlify and Supabase Enables “Supa” DX](/images/blog/2022-august/webinar.jpg)\n\nOur friends from Netlify invited Ant to their new webinar series. He and Netlify's VP of Partnerships \u0026 Ecosystems, Steven Larsen, will show you how to:\n\n- Deploy Supabase's open source backend together with Netlify\n- Build User Management without handing over user data to any third parties\n- Upload files and folders to the cloud without needing to tack on additional tools\n\n[Save your seat.](https://www.netlify.com/resources/webinars/how-netlify-supabase-enables-supa-dx/)\n\n## Extended Community Highlights\n\n![Community](/images/blog/2022-june/community.jpg)\n\n- Inian shared our journey with Cloudflare. [Video](https://twitter.com/Cloudflare/status/1557728943901675520)\n- Supabase Tips: Introduction to Supabase Storage. [Video](https://www.youtube.com/watch?v=J9mTPY8rIXE)\n- Zack DeRose playing around with Supabase and Nx. [Part 1](https://www.youtube.com/watch?v=OTh5GBBfr4E) | [Part 2](https://www.youtube.com/watch?v=_5gJi_xwpzk)\n- A new guide showing how to combine Supabase and Directus. [Guide](https://supabase.com/partners/integrations/directus)\n- Supabase with Flutter course on raywenderlich. [Course](https://www.raywenderlich.com/33619647-supabase-with-flutter)\n- Build a full-stack app with Next.js and Supabase on the LogRocket blog. [Tutorial](https://blog.logrocket.com/build-full-stack-app-next-js-supabase/)\n- Supabase Crash Course by The Net Ninja. [Video Courses](https://www.youtube.com/watch?v=ydz7Dj5QHKY\u0026list=PL4cUxeGkcC9hUb6sHthUEwG7r9VDPBMKO)\n- How to build a Grocery Application with Webflow CMS using DhiWise. [Tutorial](https://dev.to/saloni137/how-to-build-a-grocery-application-with-webflow-cms-using-dhiwise-1a72)\n- How To Create a Full Stack app with SolidJS, Supabase, and TailwindCSS [Video and Tutorial.](https://blog.chetanverma.com/how-to-create-a-full-stack-app-with-solidjs-supabase-and-tailwindcss)\n- The WalletConnect Cloud now supports Sign in with Ethereum. [Announcement](https://twitter.com/TheHarryET/status/1559861021845643265).\n- Building In Public: Cartta Tech Stack. [Article](https://dev.to/fvaldes33/building-in-public-cartta-tech-stack-5en0)\n- Supabase + Vue 3 in 12 minutes. [Video](https://www.youtube.com/watch?v=YN32uVqAXw8\u0026feature=emb_title)\n\n## We're hiring\n\nCome join one of the fastest growing open source projects ever 🤗\n\n- [Lead Billing Engineer](https://boards.greenhouse.io/supabase/jobs/4652333004)\n- [Customer Success (US time zone)](https://boards.greenhouse.io/supabase/jobs/4594393004)\n- [Support Engineers](https://boards.greenhouse.io/supabase/jobs/4191650004)\n- [View all our openings](https://boards.greenhouse.io/supabase)\n\n---\n\n## Meme Zone\n\nIf you made it this far in the blog post you deserve a treat. [Follow us on Twitter](https://twitter.com/supabase) for more.\n\n![Supabase meme august 2022](/images/blog/2022-august/supabase-beta-update-august-2022-meme.png)\n\n## Get started\n\n- Start using Supabase today: **[supabase.com/dashboard](https://supabase.com/dashboard/)**\n- Make sure to **[star us on GitHub](https://github.com/supabase/supabase)**\n- Follow us **[on Twitter](https://twitter.com/supabase)**\n- Subscribe to our **[YouTube channel](https://www.youtube.com/c/supabase)**\n","title":"Supabase Beta August 2022","description":"Launch Week Special. See everything we shipped, plus winners of the Hackathon and the extended Community Highlights","author":"ant_wilson","image":"2022-august/monthly-update-august-2022.jpg","thumb":"2022-august/monthly-update-august-2022.jpg","categories":["product"],"tags":["release-notes"],"date":"2022-09-07","toc_depth":3,"content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    h2: \"h2\",\n    img: \"img\",\n    a: \"a\",\n    strong: \"strong\",\n    code: \"code\",\n    hr: \"hr\",\n    ul: \"ul\",\n    li: \"li\"\n  }, _provideComponents(), props.components);\n  return _jsxs(_Fragment, {\n    children: [_jsx(_components.p, {\n      children: \"This month the Beta Update is a Launch Week special. #SupaLaunchWeek 5 just happened and it was a big one, so we revisit everything we shipped, congratulate the winners of the Supa Hackathon, and of course we have the extended the Community Highlights.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"day-1---supabase-cli-v1-and-management-api-beta\",\n      children: \"Day 1 - Supabase CLI v1 and Management API Beta\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2022-august/CLI.png\",\n        alt: \"Day 1 - Supabase CLI v1 and Management API Beta\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The Supabase CLI is now in v1.0 (including the ability to generate TypeScript types 🎉). We also released the Management API Beta, a REST API that opens the door to a whole new suite of integrations (Zapier, Terraform, Pulumi, you name it). Full programmatic control of your projects and orgs is on the way.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://supabase.com/blog/supabase-cli-v1-and-admin-api-beta\",\n        children: \"Blog Post\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://www.youtube.com/watch?v=OpPOaJI_Z28\",\n        children: \"Video Announcement\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"day-2---supabase-js-v2-release-candidate\",\n      children: \"Day 2 - supabase-js v2 Release Candidate\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2022-august/supabase_js.png\",\n        alt: \"Day 2 - supabase-js v2 Release Candidate\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.strong, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase/supabase-js\",\n          children: \"supabase-js v2\"\n        })\n      }), \" focuses on \\\"quality-of-life\\\" improvements for developers and includes Type Support, new Auth Methods, async Auth overhaul, improvements for Edge Functions, and more. We couldn't have done this without our amazing Community, so thanks a lot to everyone who contributed.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Try it out by running \", _jsx(_components.code, {\n        children: \"npm i @supabase/supabase-js@rc\"\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://supabase.com/blog/supabase-js-v2\",\n        children: \"Blog Post\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://youtu.be/iqZlPtl_b-I\",\n        children: \"Video Announcement\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"day-3---supabase-is-soc2-compliant\",\n      children: \"Day 3 - Supabase is SOC2 compliant\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2022-august/security.png\",\n        alt: \"Day 3 - Supabase is SOC2 compliant\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Our customers can rest assured knowing their information is secure and private 🔒. The blog post explains the process we went through to get there and is very useful for anyone building a SaaS product.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://supabase.com/blog/supabase-soc2\",\n        children: \"Blog Post\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://supabase.com/security\",\n        children: \"Security at Supabase\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://youtu.be/6bGQotxisoY\",\n        children: \"Video Announcement\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"day-4---realtime-multiplayer-edition\",\n      children: \"Day 4 - Realtime: Multiplayer Edition\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2022-august/realtime.png\",\n        alt: \"Day 4 - Realtime: Multiplayer Edition\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Next level Realtime is here ⚡️. Presence and Broadcast are two key blocks developers can use to build the digital experiences users want. All projects now have access to these features. Try it out in combination with supabase-js v2.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://supabase.com/blog/supabase-realtime-multiplayer-general-availability\",\n        children: \"Blog Post\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://youtu.be/CGZr5tybW18\",\n        children: \"Video Announcement\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"day-5---community-day-and-one-more-thing\",\n      children: \"Day 5 - Community Day and One More Thing\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2022-august/day-5.png\",\n        alt: \"Day 5 - Community Day and One More Thing\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We wrapped Launch Week 5 with contributors, partners, and friends and the traditional One More Thing... that was actually SIX more things: \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/supabase-vault\",\n        children: \"Supabase Vault\"\n      }), \", Auth UI, Dashboard permissions, \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/pg-jsonschema-a-postgres-extension-for-json-validation\",\n        children: \"JSON schema validation\"\n      }), \", pg_graphql v0.4.0, MFA early-access.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://supabase.com/blog/launch-week-5-community-day\",\n        children: \"Community Day Blog Post\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://www.youtube.com/watch?v=s9UePQjLT0U\",\n        children: \"Community Day Video Announcement\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://supabase.com/blog/launch-week-5-one-more-thing\",\n        children: \"One More Thing Blog Post\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"launch-week-5-hackathon\",\n      children: \"Launch Week 5 Hackathon\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2022-august/hackathon.png\",\n        alt: \"Launch Week 5 Hackathon\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We had a huge amount of open source submissions 🤯. The selection process was not easy as it wasn't only quantity, but also quality. After a thorough review, we declared \", _jsx(_components.a, {\n        href: \"https://github.com/psteinroe/supabase-cache-helpers\",\n        children: \"Supabase Cache Helpers\"\n      }), \" the overall winner of the $1500 GitHub sponsorship and Gold SupaCap. Congratulations to \", _jsx(_components.a, {\n        href: \"https://twitter.com/psteinroe\",\n        children: \"@psteinroe\"\n      }), \" 👏\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://supabase.com/blog/launch-week-5-hackathon-winners\",\n        children: \"Full list of winners\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://www.madewithsupabase.com/launch-week-5\",\n        children: \"Check all the submissions in Made with Supabase\"\n      })\n    }), \"\\n\", _jsx(_components.hr, {}), \"\\n\", _jsx(_components.h2, {\n      id: \"platform-updates\",\n      children: \"Platform Updates\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The following changes to the Supabase Platform will take effect from September 11th at 7 pm PDT.\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"HTTP API requests to Supabase will automatically be redirected to HTTPS.\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"The API Key was passed to Supabase both in the \", _jsx(_components.code, {\n          children: \"Authorization\"\n        }), \" header and in a separate \", _jsx(_components.code, {\n          children: \"apiKey\"\n        }), \" header. This led to confusion among new users of Supabase who used the API directly. It is no longer required to send the anon key or service key via the \", _jsx(_components.code, {\n          children: \"apiKey\"\n        }), \" header. If you are using Supabase via our client libraries, no change is required from your side.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"supabase-migration-guides\",\n      children: \"Supabase Migration Guides\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2022-august/migration-to-supabase-guides.jpg\",\n        alt: \"Supabase Migration Guides\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Our guides and tools make it super easy to migrate your projects to Supabase:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/docs/guides/migrations/firebase-auth\",\n          children: \"Firebase Auth Migration\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/docs/guides/migrations/firestore-data\",\n          children: \"Firestore Data Migration\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/docs/guides/migrations/firebase-storage\",\n          children: \"Firebase Storage Migration\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/docs/guides/migrations/heroku\",\n          children: \"Migrate from Heroku to Supabase\"\n        })\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"webinar-how-netlify-and-supabase-enables-supa-dx\",\n      children: \"Webinar: How Netlify and Supabase Enables “Supa” DX\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2022-august/webinar.jpg\",\n        alt: \"Webinar: How Netlify and Supabase Enables “Supa” DX\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Our friends from Netlify invited Ant to their new webinar series. He and Netlify's VP of Partnerships \u0026 Ecosystems, Steven Larsen, will show you how to:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Deploy Supabase's open source backend together with Netlify\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Build User Management without handing over user data to any third parties\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Upload files and folders to the cloud without needing to tack on additional tools\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://www.netlify.com/resources/webinars/how-netlify-supabase-enables-supa-dx/\",\n        children: \"Save your seat.\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"extended-community-highlights\",\n      children: \"Extended Community Highlights\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2022-june/community.jpg\",\n        alt: \"Community\"\n      })\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Inian shared our journey with Cloudflare. \", _jsx(_components.a, {\n          href: \"https://twitter.com/Cloudflare/status/1557728943901675520\",\n          children: \"Video\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Supabase Tips: Introduction to Supabase Storage. \", _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=J9mTPY8rIXE\",\n          children: \"Video\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Zack DeRose playing around with Supabase and Nx. \", _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=OTh5GBBfr4E\",\n          children: \"Part 1\"\n        }), \" | \", _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=_5gJi_xwpzk\",\n          children: \"Part 2\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"A new guide showing how to combine Supabase and Directus. \", _jsx(_components.a, {\n          href: \"https://supabase.com/partners/integrations/directus\",\n          children: \"Guide\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Supabase with Flutter course on raywenderlich. \", _jsx(_components.a, {\n          href: \"https://www.raywenderlich.com/33619647-supabase-with-flutter\",\n          children: \"Course\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Build a full-stack app with Next.js and Supabase on the LogRocket blog. \", _jsx(_components.a, {\n          href: \"https://blog.logrocket.com/build-full-stack-app-next-js-supabase/\",\n          children: \"Tutorial\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Supabase Crash Course by The Net Ninja. \", _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=ydz7Dj5QHKY\u0026list=PL4cUxeGkcC9hUb6sHthUEwG7r9VDPBMKO\",\n          children: \"Video Courses\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"How to build a Grocery Application with Webflow CMS using DhiWise. \", _jsx(_components.a, {\n          href: \"https://dev.to/saloni137/how-to-build-a-grocery-application-with-webflow-cms-using-dhiwise-1a72\",\n          children: \"Tutorial\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"How To Create a Full Stack app with SolidJS, Supabase, and TailwindCSS \", _jsx(_components.a, {\n          href: \"https://blog.chetanverma.com/how-to-create-a-full-stack-app-with-solidjs-supabase-and-tailwindcss\",\n          children: \"Video and Tutorial.\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"The WalletConnect Cloud now supports Sign in with Ethereum. \", _jsx(_components.a, {\n          href: \"https://twitter.com/TheHarryET/status/1559861021845643265\",\n          children: \"Announcement\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Building In Public: Cartta Tech Stack. \", _jsx(_components.a, {\n          href: \"https://dev.to/fvaldes33/building-in-public-cartta-tech-stack-5en0\",\n          children: \"Article\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Supabase + Vue 3 in 12 minutes. \", _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=YN32uVqAXw8\u0026feature=emb_title\",\n          children: \"Video\"\n        })]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"were-hiring\",\n      children: \"We're hiring\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Come join one of the fastest growing open source projects ever 🤗\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://boards.greenhouse.io/supabase/jobs/4652333004\",\n          children: \"Lead Billing Engineer\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://boards.greenhouse.io/supabase/jobs/4594393004\",\n          children: \"Customer Success (US time zone)\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://boards.greenhouse.io/supabase/jobs/4191650004\",\n          children: \"Support Engineers\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://boards.greenhouse.io/supabase\",\n          children: \"View all our openings\"\n        })\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.hr, {}), \"\\n\", _jsx(_components.h2, {\n      id: \"meme-zone\",\n      children: \"Meme Zone\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you made it this far in the blog post you deserve a treat. \", _jsx(_components.a, {\n        href: \"https://twitter.com/supabase\",\n        children: \"Follow us on Twitter\"\n      }), \" for more.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/2022-august/supabase-beta-update-august-2022-meme.png\",\n        alt: \"Supabase meme august 2022\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"get-started\",\n      children: \"Get started\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Start using Supabase today: \", _jsx(_components.strong, {\n          children: _jsx(_components.a, {\n            href: \"https://supabase.com/dashboard/\",\n            children: \"supabase.com/dashboard\"\n          })\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Make sure to \", _jsx(_components.strong, {\n          children: _jsx(_components.a, {\n            href: \"https://github.com/supabase/supabase\",\n            children: \"star us on GitHub\"\n          })\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Follow us \", _jsx(_components.strong, {\n          children: _jsx(_components.a, {\n            href: \"https://twitter.com/supabase\",\n            children: \"on Twitter\"\n          })\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Subscribe to our \", _jsx(_components.strong, {\n          children: _jsx(_components.a, {\n            href: \"https://www.youtube.com/c/supabase\",\n            children: \"YouTube channel\"\n          })\n        })]\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"Day 1 - Supabase CLI v1 and Management API Beta","slug":"day-1---supabase-cli-v1-and-management-api-beta","lvl":2,"i":0,"seen":0},{"content":"Day 2 - supabase-js v2 Release Candidate","slug":"day-2---supabase-js-v2-release-candidate","lvl":2,"i":1,"seen":0},{"content":"Day 3 - Supabase is SOC2 compliant","slug":"day-3---supabase-is-soc2-compliant","lvl":2,"i":2,"seen":0},{"content":"Day 4 - Realtime: Multiplayer Edition","slug":"day-4---realtime-multiplayer-edition","lvl":2,"i":3,"seen":0},{"content":"Day 5 - Community Day and One More Thing","slug":"day-5---community-day-and-one-more-thing","lvl":2,"i":4,"seen":0},{"content":"Launch Week 5 Hackathon","slug":"launch-week-5-hackathon","lvl":2,"i":5,"seen":0},{"content":"Platform Updates","slug":"platform-updates","lvl":2,"i":6,"seen":0},{"content":"Supabase Migration Guides","slug":"supabase-migration-guides","lvl":2,"i":7,"seen":0},{"content":"Webinar: How Netlify and Supabase Enables “Supa” DX","slug":"webinar-how-netlify-and-supabase-enables-supa-dx","lvl":2,"i":8,"seen":0},{"content":"Extended Community Highlights","slug":"extended-community-highlights","lvl":2,"i":9,"seen":0},{"content":"We're hiring","slug":"were-hiring","lvl":2,"i":10,"seen":0},{"content":"Meme Zone","slug":"meme-zone","lvl":2,"i":11,"seen":0},{"content":"Get started","slug":"get-started","lvl":2,"i":12,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,2],"level":0},{"type":"inline","content":"This month the Beta Update is a Launch Week special. #SupaLaunchWeek 5 just happened and it was a big one, so we revisit everything we shipped, congratulate the winners of the Supa Hackathon, and of course we have the extended the Community Highlights.","level":1,"lines":[1,2],"children":[{"type":"text","content":"This month the Beta Update is a Launch Week special. #SupaLaunchWeek 5 just happened and it was a big one, so we revisit everything we shipped, congratulate the winners of the Supa Hackathon, and of course we have the extended the Community Highlights.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[3,4],"level":0},{"type":"inline","content":"[Day 1 - Supabase CLI v1 and Management API Beta](#day-1---supabase-cli-v1-and-management-api-beta)","level":1,"lines":[3,4],"children":[{"type":"text","content":"Day 1 - Supabase CLI v1 and Management API Beta","level":0}],"lvl":2,"i":0,"seen":0,"slug":"day-1---supabase-cli-v1-and-management-api-beta"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[5,6],"level":0},{"type":"inline","content":"![Day 1 - Supabase CLI v1 and Management API Beta](/images/blog/2022-august/CLI.png)","level":1,"lines":[5,6],"children":[{"type":"image","src":"/images/blog/2022-august/CLI.png","title":"","alt":"Day 1 - Supabase CLI v1 and Management API Beta","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[7,8],"level":0},{"type":"inline","content":"The Supabase CLI is now in v1.0 (including the ability to generate TypeScript types 🎉). We also released the Management API Beta, a REST API that opens the door to a whole new suite of integrations (Zapier, Terraform, Pulumi, you name it). Full programmatic control of your projects and orgs is on the way.","level":1,"lines":[7,8],"children":[{"type":"text","content":"The Supabase CLI is now in v1.0 (including the ability to generate TypeScript types 🎉). We also released the Management API Beta, a REST API that opens the door to a whole new suite of integrations (Zapier, Terraform, Pulumi, you name it). Full programmatic control of your projects and orgs is on the way.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[9,10],"level":0},{"type":"inline","content":"[Blog Post](https://supabase.com/blog/supabase-cli-v1-and-admin-api-beta)","level":1,"lines":[9,10],"children":[{"type":"link_open","href":"https://supabase.com/blog/supabase-cli-v1-and-admin-api-beta","title":"","level":0},{"type":"text","content":"Blog Post","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[11,12],"level":0},{"type":"inline","content":"[Video Announcement](https://www.youtube.com/watch?v=OpPOaJI_Z28)","level":1,"lines":[11,12],"children":[{"type":"link_open","href":"https://www.youtube.com/watch?v=OpPOaJI_Z28","title":"","level":0},{"type":"text","content":"Video Announcement","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[13,14],"level":0},{"type":"inline","content":"[Day 2 - supabase-js v2 Release Candidate](#day-2---supabase-js-v2-release-candidate)","level":1,"lines":[13,14],"children":[{"type":"text","content":"Day 2 - supabase-js v2 Release Candidate","level":0}],"lvl":2,"i":1,"seen":0,"slug":"day-2---supabase-js-v2-release-candidate"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[15,16],"level":0},{"type":"inline","content":"![Day 2 - supabase-js v2 Release Candidate](/images/blog/2022-august/supabase_js.png)","level":1,"lines":[15,16],"children":[{"type":"image","src":"/images/blog/2022-august/supabase_js.png","title":"","alt":"Day 2 - supabase-js v2 Release Candidate","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[17,18],"level":0},{"type":"inline","content":"**[supabase-js v2](https://github.com/supabase/supabase-js)** focuses on \"quality-of-life\" improvements for developers and includes Type Support, new Auth Methods, async Auth overhaul, improvements for Edge Functions, and more. We couldn't have done this without our amazing Community, so thanks a lot to everyone who contributed.","level":1,"lines":[17,18],"children":[{"type":"strong_open","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase-js","title":"","level":1},{"type":"text","content":"supabase-js v2","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0},{"type":"text","content":" focuses on \"quality-of-life\" improvements for developers and includes Type Support, new Auth Methods, async Auth overhaul, improvements for Edge Functions, and more. We couldn't have done this without our amazing Community, so thanks a lot to everyone who contributed.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[19,20],"level":0},{"type":"inline","content":"Try it out by running `npm i @supabase/supabase-js@rc`","level":1,"lines":[19,20],"children":[{"type":"text","content":"Try it out by running ","level":0},{"type":"code","content":"npm i @supabase/supabase-js@rc","block":false,"level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[21,22],"level":0},{"type":"inline","content":"[Blog Post](https://supabase.com/blog/supabase-js-v2)","level":1,"lines":[21,22],"children":[{"type":"link_open","href":"https://supabase.com/blog/supabase-js-v2","title":"","level":0},{"type":"text","content":"Blog Post","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[23,24],"level":0},{"type":"inline","content":"[Video Announcement](https://youtu.be/iqZlPtl_b-I)","level":1,"lines":[23,24],"children":[{"type":"link_open","href":"https://youtu.be/iqZlPtl_b-I","title":"","level":0},{"type":"text","content":"Video Announcement","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[25,26],"level":0},{"type":"inline","content":"[Day 3 - Supabase is SOC2 compliant](#day-3---supabase-is-soc2-compliant)","level":1,"lines":[25,26],"children":[{"type":"text","content":"Day 3 - Supabase is SOC2 compliant","level":0}],"lvl":2,"i":2,"seen":0,"slug":"day-3---supabase-is-soc2-compliant"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[27,28],"level":0},{"type":"inline","content":"![Day 3 - Supabase is SOC2 compliant](/images/blog/2022-august/security.png)","level":1,"lines":[27,28],"children":[{"type":"image","src":"/images/blog/2022-august/security.png","title":"","alt":"Day 3 - Supabase is SOC2 compliant","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[29,30],"level":0},{"type":"inline","content":"Our customers can rest assured knowing their information is secure and private 🔒. The blog post explains the process we went through to get there and is very useful for anyone building a SaaS product.","level":1,"lines":[29,30],"children":[{"type":"text","content":"Our customers can rest assured knowing their information is secure and private 🔒. The blog post explains the process we went through to get there and is very useful for anyone building a SaaS product.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[31,32],"level":0},{"type":"inline","content":"[Blog Post](https://supabase.com/blog/supabase-soc2)","level":1,"lines":[31,32],"children":[{"type":"link_open","href":"https://supabase.com/blog/supabase-soc2","title":"","level":0},{"type":"text","content":"Blog Post","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[33,34],"level":0},{"type":"inline","content":"[Security at Supabase](https://supabase.com/security)","level":1,"lines":[33,34],"children":[{"type":"link_open","href":"https://supabase.com/security","title":"","level":0},{"type":"text","content":"Security at Supabase","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[35,36],"level":0},{"type":"inline","content":"[Video Announcement](https://youtu.be/6bGQotxisoY)","level":1,"lines":[35,36],"children":[{"type":"link_open","href":"https://youtu.be/6bGQotxisoY","title":"","level":0},{"type":"text","content":"Video Announcement","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[37,38],"level":0},{"type":"inline","content":"[Day 4 - Realtime: Multiplayer Edition](#day-4---realtime-multiplayer-edition)","level":1,"lines":[37,38],"children":[{"type":"text","content":"Day 4 - Realtime: Multiplayer Edition","level":0}],"lvl":2,"i":3,"seen":0,"slug":"day-4---realtime-multiplayer-edition"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[39,40],"level":0},{"type":"inline","content":"![Day 4 - Realtime: Multiplayer Edition](/images/blog/2022-august/realtime.png)","level":1,"lines":[39,40],"children":[{"type":"image","src":"/images/blog/2022-august/realtime.png","title":"","alt":"Day 4 - Realtime: Multiplayer Edition","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[41,42],"level":0},{"type":"inline","content":"Next level Realtime is here ⚡️. Presence and Broadcast are two key blocks developers can use to build the digital experiences users want. All projects now have access to these features. Try it out in combination with supabase-js v2.","level":1,"lines":[41,42],"children":[{"type":"text","content":"Next level Realtime is here ⚡️. Presence and Broadcast are two key blocks developers can use to build the digital experiences users want. All projects now have access to these features. Try it out in combination with supabase-js v2.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[43,44],"level":0},{"type":"inline","content":"[Blog Post](https://supabase.com/blog/supabase-realtime-multiplayer-general-availability)","level":1,"lines":[43,44],"children":[{"type":"link_open","href":"https://supabase.com/blog/supabase-realtime-multiplayer-general-availability","title":"","level":0},{"type":"text","content":"Blog Post","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[45,46],"level":0},{"type":"inline","content":"[Video Announcement](https://youtu.be/CGZr5tybW18)","level":1,"lines":[45,46],"children":[{"type":"link_open","href":"https://youtu.be/CGZr5tybW18","title":"","level":0},{"type":"text","content":"Video Announcement","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[47,48],"level":0},{"type":"inline","content":"[Day 5 - Community Day and One More Thing](#day-5---community-day-and-one-more-thing)","level":1,"lines":[47,48],"children":[{"type":"text","content":"Day 5 - Community Day and One More Thing","level":0}],"lvl":2,"i":4,"seen":0,"slug":"day-5---community-day-and-one-more-thing"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[49,50],"level":0},{"type":"inline","content":"![Day 5 - Community Day and One More Thing](/images/blog/2022-august/day-5.png)","level":1,"lines":[49,50],"children":[{"type":"image","src":"/images/blog/2022-august/day-5.png","title":"","alt":"Day 5 - Community Day and One More Thing","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[51,52],"level":0},{"type":"inline","content":"We wrapped Launch Week 5 with contributors, partners, and friends and the traditional One More Thing... that was actually SIX more things: [Supabase Vault](https://supabase.com/blog/supabase-vault), Auth UI, Dashboard permissions, [JSON schema validation](https://supabase.com/blog/pg-jsonschema-a-postgres-extension-for-json-validation), pg_graphql v0.4.0, MFA early-access.","level":1,"lines":[51,52],"children":[{"type":"text","content":"We wrapped Launch Week 5 with contributors, partners, and friends and the traditional One More Thing... that was actually SIX more things: ","level":0},{"type":"link_open","href":"https://supabase.com/blog/supabase-vault","title":"","level":0},{"type":"text","content":"Supabase Vault","level":1},{"type":"link_close","level":0},{"type":"text","content":", Auth UI, Dashboard permissions, ","level":0},{"type":"link_open","href":"https://supabase.com/blog/pg-jsonschema-a-postgres-extension-for-json-validation","title":"","level":0},{"type":"text","content":"JSON schema validation","level":1},{"type":"link_close","level":0},{"type":"text","content":", pg_graphql v0.4.0, MFA early-access.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[53,54],"level":0},{"type":"inline","content":"[Community Day Blog Post](https://supabase.com/blog/launch-week-5-community-day)","level":1,"lines":[53,54],"children":[{"type":"link_open","href":"https://supabase.com/blog/launch-week-5-community-day","title":"","level":0},{"type":"text","content":"Community Day Blog Post","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[55,56],"level":0},{"type":"inline","content":"[Community Day Video Announcement](https://www.youtube.com/watch?v=s9UePQjLT0U)","level":1,"lines":[55,56],"children":[{"type":"link_open","href":"https://www.youtube.com/watch?v=s9UePQjLT0U","title":"","level":0},{"type":"text","content":"Community Day Video Announcement","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[57,58],"level":0},{"type":"inline","content":"[One More Thing Blog Post](https://supabase.com/blog/launch-week-5-one-more-thing)","level":1,"lines":[57,58],"children":[{"type":"link_open","href":"https://supabase.com/blog/launch-week-5-one-more-thing","title":"","level":0},{"type":"text","content":"One More Thing Blog Post","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[59,60],"level":0},{"type":"inline","content":"[Launch Week 5 Hackathon](#launch-week-5-hackathon)","level":1,"lines":[59,60],"children":[{"type":"text","content":"Launch Week 5 Hackathon","level":0}],"lvl":2,"i":5,"seen":0,"slug":"launch-week-5-hackathon"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[61,62],"level":0},{"type":"inline","content":"![Launch Week 5 Hackathon](/images/blog/2022-august/hackathon.png)","level":1,"lines":[61,62],"children":[{"type":"image","src":"/images/blog/2022-august/hackathon.png","title":"","alt":"Launch Week 5 Hackathon","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[63,64],"level":0},{"type":"inline","content":"We had a huge amount of open source submissions 🤯. The selection process was not easy as it wasn't only quantity, but also quality. After a thorough review, we declared [Supabase Cache Helpers](https://github.com/psteinroe/supabase-cache-helpers) the overall winner of the $1500 GitHub sponsorship and Gold SupaCap. Congratulations to [@psteinroe](https://twitter.com/psteinroe) 👏","level":1,"lines":[63,64],"children":[{"type":"text","content":"We had a huge amount of open source submissions 🤯. The selection process was not easy as it wasn't only quantity, but also quality. After a thorough review, we declared ","level":0},{"type":"link_open","href":"https://github.com/psteinroe/supabase-cache-helpers","title":"","level":0},{"type":"text","content":"Supabase Cache Helpers","level":1},{"type":"link_close","level":0},{"type":"text","content":" the overall winner of the $1500 GitHub sponsorship and Gold SupaCap. Congratulations to ","level":0},{"type":"link_open","href":"https://twitter.com/psteinroe","title":"","level":0},{"type":"text","content":"@psteinroe","level":1},{"type":"link_close","level":0},{"type":"text","content":" 👏","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[65,66],"level":0},{"type":"inline","content":"[Full list of winners](https://supabase.com/blog/launch-week-5-hackathon-winners)","level":1,"lines":[65,66],"children":[{"type":"link_open","href":"https://supabase.com/blog/launch-week-5-hackathon-winners","title":"","level":0},{"type":"text","content":"Full list of winners","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[67,68],"level":0},{"type":"inline","content":"[Check all the submissions in Made with Supabase](https://www.madewithsupabase.com/launch-week-5)","level":1,"lines":[67,68],"children":[{"type":"link_open","href":"https://www.madewithsupabase.com/launch-week-5","title":"","level":0},{"type":"text","content":"Check all the submissions in Made with Supabase","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"hr","lines":[69,70],"level":0},{"type":"heading_open","hLevel":2,"lines":[71,72],"level":0},{"type":"inline","content":"[Platform Updates](#platform-updates)","level":1,"lines":[71,72],"children":[{"type":"text","content":"Platform Updates","level":0}],"lvl":2,"i":6,"seen":0,"slug":"platform-updates"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[73,74],"level":0},{"type":"inline","content":"The following changes to the Supabase Platform will take effect from September 11th at 7 pm PDT.","level":1,"lines":[73,74],"children":[{"type":"text","content":"The following changes to the Supabase Platform will take effect from September 11th at 7 pm PDT.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[75,78],"level":0},{"type":"list_item_open","lines":[75,76],"level":1},{"type":"paragraph_open","tight":true,"lines":[75,76],"level":2},{"type":"inline","content":"HTTP API requests to Supabase will automatically be redirected to HTTPS.","level":3,"lines":[75,76],"children":[{"type":"text","content":"HTTP API requests to Supabase will automatically be redirected to HTTPS.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[76,78],"level":1},{"type":"paragraph_open","tight":true,"lines":[76,77],"level":2},{"type":"inline","content":"The API Key was passed to Supabase both in the `Authorization` header and in a separate `apiKey` header. This led to confusion among new users of Supabase who used the API directly. It is no longer required to send the anon key or service key via the `apiKey` header. If you are using Supabase via our client libraries, no change is required from your side.","level":3,"lines":[76,77],"children":[{"type":"text","content":"The API Key was passed to Supabase both in the ","level":0},{"type":"code","content":"Authorization","block":false,"level":0},{"type":"text","content":" header and in a separate ","level":0},{"type":"code","content":"apiKey","block":false,"level":0},{"type":"text","content":" header. This led to confusion among new users of Supabase who used the API directly. It is no longer required to send the anon key or service key via the ","level":0},{"type":"code","content":"apiKey","block":false,"level":0},{"type":"text","content":" header. If you are using Supabase via our client libraries, no change is required from your side.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":2,"lines":[78,79],"level":0},{"type":"inline","content":"[Supabase Migration Guides](#supabase-migration-guides)","level":1,"lines":[78,79],"children":[{"type":"text","content":"Supabase Migration Guides","level":0}],"lvl":2,"i":7,"seen":0,"slug":"supabase-migration-guides"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[80,81],"level":0},{"type":"inline","content":"![Supabase Migration Guides](/images/blog/2022-august/migration-to-supabase-guides.jpg)","level":1,"lines":[80,81],"children":[{"type":"image","src":"/images/blog/2022-august/migration-to-supabase-guides.jpg","title":"","alt":"Supabase Migration Guides","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[82,83],"level":0},{"type":"inline","content":"Our guides and tools make it super easy to migrate your projects to Supabase:","level":1,"lines":[82,83],"children":[{"type":"text","content":"Our guides and tools make it super easy to migrate your projects to Supabase:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[84,89],"level":0},{"type":"list_item_open","lines":[84,85],"level":1},{"type":"paragraph_open","tight":true,"lines":[84,85],"level":2},{"type":"inline","content":"[Firebase Auth Migration](https://supabase.com/docs/guides/migrations/firebase-auth)","level":3,"lines":[84,85],"children":[{"type":"link_open","href":"https://supabase.com/docs/guides/migrations/firebase-auth","title":"","level":0},{"type":"text","content":"Firebase Auth Migration","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[85,86],"level":1},{"type":"paragraph_open","tight":true,"lines":[85,86],"level":2},{"type":"inline","content":"[Firestore Data Migration](https://supabase.com/docs/guides/migrations/firestore-data)","level":3,"lines":[85,86],"children":[{"type":"link_open","href":"https://supabase.com/docs/guides/migrations/firestore-data","title":"","level":0},{"type":"text","content":"Firestore Data Migration","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[86,87],"level":1},{"type":"paragraph_open","tight":true,"lines":[86,87],"level":2},{"type":"inline","content":"[Firebase Storage Migration](https://supabase.com/docs/guides/migrations/firebase-storage)","level":3,"lines":[86,87],"children":[{"type":"link_open","href":"https://supabase.com/docs/guides/migrations/firebase-storage","title":"","level":0},{"type":"text","content":"Firebase Storage Migration","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[87,89],"level":1},{"type":"paragraph_open","tight":true,"lines":[87,88],"level":2},{"type":"inline","content":"[Migrate from Heroku to Supabase](https://supabase.com/docs/guides/migrations/heroku)","level":3,"lines":[87,88],"children":[{"type":"link_open","href":"https://supabase.com/docs/guides/migrations/heroku","title":"","level":0},{"type":"text","content":"Migrate from Heroku to Supabase","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":2,"lines":[89,90],"level":0},{"type":"inline","content":"[Webinar: How Netlify and Supabase Enables “Supa” DX](#webinar-how-netlify-and-supabase-enables-supa-dx)","level":1,"lines":[89,90],"children":[{"type":"text","content":"Webinar: How Netlify and Supabase Enables “Supa” DX","level":0}],"lvl":2,"i":8,"seen":0,"slug":"webinar-how-netlify-and-supabase-enables-supa-dx"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[91,92],"level":0},{"type":"inline","content":"![Webinar: How Netlify and Supabase Enables “Supa” DX](/images/blog/2022-august/webinar.jpg)","level":1,"lines":[91,92],"children":[{"type":"image","src":"/images/blog/2022-august/webinar.jpg","title":"","alt":"Webinar: How Netlify and Supabase Enables “Supa” DX","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[93,94],"level":0},{"type":"inline","content":"Our friends from Netlify invited Ant to their new webinar series. He and Netlify's VP of Partnerships \u0026 Ecosystems, Steven Larsen, will show you how to:","level":1,"lines":[93,94],"children":[{"type":"text","content":"Our friends from Netlify invited Ant to their new webinar series. He and Netlify's VP of Partnerships \u0026 Ecosystems, Steven Larsen, will show you how to:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[95,99],"level":0},{"type":"list_item_open","lines":[95,96],"level":1},{"type":"paragraph_open","tight":true,"lines":[95,96],"level":2},{"type":"inline","content":"Deploy Supabase's open source backend together with Netlify","level":3,"lines":[95,96],"children":[{"type":"text","content":"Deploy Supabase's open source backend together with Netlify","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[96,97],"level":1},{"type":"paragraph_open","tight":true,"lines":[96,97],"level":2},{"type":"inline","content":"Build User Management without handing over user data to any third parties","level":3,"lines":[96,97],"children":[{"type":"text","content":"Build User Management without handing over user data to any third parties","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[97,99],"level":1},{"type":"paragraph_open","tight":true,"lines":[97,98],"level":2},{"type":"inline","content":"Upload files and folders to the cloud without needing to tack on additional tools","level":3,"lines":[97,98],"children":[{"type":"text","content":"Upload files and folders to the cloud without needing to tack on additional tools","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[99,100],"level":0},{"type":"inline","content":"[Save your seat.](https://www.netlify.com/resources/webinars/how-netlify-supabase-enables-supa-dx/)","level":1,"lines":[99,100],"children":[{"type":"link_open","href":"https://www.netlify.com/resources/webinars/how-netlify-supabase-enables-supa-dx/","title":"","level":0},{"type":"text","content":"Save your seat.","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[101,102],"level":0},{"type":"inline","content":"[Extended Community Highlights](#extended-community-highlights)","level":1,"lines":[101,102],"children":[{"type":"text","content":"Extended Community Highlights","level":0}],"lvl":2,"i":9,"seen":0,"slug":"extended-community-highlights"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[103,104],"level":0},{"type":"inline","content":"![Community](/images/blog/2022-june/community.jpg)","level":1,"lines":[103,104],"children":[{"type":"image","src":"/images/blog/2022-june/community.jpg","title":"","alt":"Community","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[105,118],"level":0},{"type":"list_item_open","lines":[105,106],"level":1},{"type":"paragraph_open","tight":true,"lines":[105,106],"level":2},{"type":"inline","content":"Inian shared our journey with Cloudflare. [Video](https://twitter.com/Cloudflare/status/1557728943901675520)","level":3,"lines":[105,106],"children":[{"type":"text","content":"Inian shared our journey with Cloudflare. ","level":0},{"type":"link_open","href":"https://twitter.com/Cloudflare/status/1557728943901675520","title":"","level":0},{"type":"text","content":"Video","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[106,107],"level":1},{"type":"paragraph_open","tight":true,"lines":[106,107],"level":2},{"type":"inline","content":"Supabase Tips: Introduction to Supabase Storage. [Video](https://www.youtube.com/watch?v=J9mTPY8rIXE)","level":3,"lines":[106,107],"children":[{"type":"text","content":"Supabase Tips: Introduction to Supabase Storage. ","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=J9mTPY8rIXE","title":"","level":0},{"type":"text","content":"Video","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[107,108],"level":1},{"type":"paragraph_open","tight":true,"lines":[107,108],"level":2},{"type":"inline","content":"Zack DeRose playing around with Supabase and Nx. [Part 1](https://www.youtube.com/watch?v=OTh5GBBfr4E) | [Part 2](https://www.youtube.com/watch?v=_5gJi_xwpzk)","level":3,"lines":[107,108],"children":[{"type":"text","content":"Zack DeRose playing around with Supabase and Nx. ","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=OTh5GBBfr4E","title":"","level":0},{"type":"text","content":"Part 1","level":1},{"type":"link_close","level":0},{"type":"text","content":" | ","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=_5gJi_xwpzk","title":"","level":0},{"type":"text","content":"Part 2","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[108,109],"level":1},{"type":"paragraph_open","tight":true,"lines":[108,109],"level":2},{"type":"inline","content":"A new guide showing how to combine Supabase and Directus. [Guide](https://supabase.com/partners/integrations/directus)","level":3,"lines":[108,109],"children":[{"type":"text","content":"A new guide showing how to combine Supabase and Directus. ","level":0},{"type":"link_open","href":"https://supabase.com/partners/integrations/directus","title":"","level":0},{"type":"text","content":"Guide","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[109,110],"level":1},{"type":"paragraph_open","tight":true,"lines":[109,110],"level":2},{"type":"inline","content":"Supabase with Flutter course on raywenderlich. [Course](https://www.raywenderlich.com/33619647-supabase-with-flutter)","level":3,"lines":[109,110],"children":[{"type":"text","content":"Supabase with Flutter course on raywenderlich. ","level":0},{"type":"link_open","href":"https://www.raywenderlich.com/33619647-supabase-with-flutter","title":"","level":0},{"type":"text","content":"Course","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[110,111],"level":1},{"type":"paragraph_open","tight":true,"lines":[110,111],"level":2},{"type":"inline","content":"Build a full-stack app with Next.js and Supabase on the LogRocket blog. [Tutorial](https://blog.logrocket.com/build-full-stack-app-next-js-supabase/)","level":3,"lines":[110,111],"children":[{"type":"text","content":"Build a full-stack app with Next.js and Supabase on the LogRocket blog. ","level":0},{"type":"link_open","href":"https://blog.logrocket.com/build-full-stack-app-next-js-supabase/","title":"","level":0},{"type":"text","content":"Tutorial","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[111,112],"level":1},{"type":"paragraph_open","tight":true,"lines":[111,112],"level":2},{"type":"inline","content":"Supabase Crash Course by The Net Ninja. [Video Courses](https://www.youtube.com/watch?v=ydz7Dj5QHKY\u0026list=PL4cUxeGkcC9hUb6sHthUEwG7r9VDPBMKO)","level":3,"lines":[111,112],"children":[{"type":"text","content":"Supabase Crash Course by The Net Ninja. ","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=ydz7Dj5QHKY\u0026list=PL4cUxeGkcC9hUb6sHthUEwG7r9VDPBMKO","title":"","level":0},{"type":"text","content":"Video Courses","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[112,113],"level":1},{"type":"paragraph_open","tight":true,"lines":[112,113],"level":2},{"type":"inline","content":"How to build a Grocery Application with Webflow CMS using DhiWise. [Tutorial](https://dev.to/saloni137/how-to-build-a-grocery-application-with-webflow-cms-using-dhiwise-1a72)","level":3,"lines":[112,113],"children":[{"type":"text","content":"How to build a Grocery Application with Webflow CMS using DhiWise. ","level":0},{"type":"link_open","href":"https://dev.to/saloni137/how-to-build-a-grocery-application-with-webflow-cms-using-dhiwise-1a72","title":"","level":0},{"type":"text","content":"Tutorial","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[113,114],"level":1},{"type":"paragraph_open","tight":true,"lines":[113,114],"level":2},{"type":"inline","content":"How To Create a Full Stack app with SolidJS, Supabase, and TailwindCSS [Video and Tutorial.](https://blog.chetanverma.com/how-to-create-a-full-stack-app-with-solidjs-supabase-and-tailwindcss)","level":3,"lines":[113,114],"children":[{"type":"text","content":"How To Create a Full Stack app with SolidJS, Supabase, and TailwindCSS ","level":0},{"type":"link_open","href":"https://blog.chetanverma.com/how-to-create-a-full-stack-app-with-solidjs-supabase-and-tailwindcss","title":"","level":0},{"type":"text","content":"Video and Tutorial.","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[114,115],"level":1},{"type":"paragraph_open","tight":true,"lines":[114,115],"level":2},{"type":"inline","content":"The WalletConnect Cloud now supports Sign in with Ethereum. [Announcement](https://twitter.com/TheHarryET/status/1559861021845643265).","level":3,"lines":[114,115],"children":[{"type":"text","content":"The WalletConnect Cloud now supports Sign in with Ethereum. ","level":0},{"type":"link_open","href":"https://twitter.com/TheHarryET/status/1559861021845643265","title":"","level":0},{"type":"text","content":"Announcement","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[115,116],"level":1},{"type":"paragraph_open","tight":true,"lines":[115,116],"level":2},{"type":"inline","content":"Building In Public: Cartta Tech Stack. [Article](https://dev.to/fvaldes33/building-in-public-cartta-tech-stack-5en0)","level":3,"lines":[115,116],"children":[{"type":"text","content":"Building In Public: Cartta Tech Stack. ","level":0},{"type":"link_open","href":"https://dev.to/fvaldes33/building-in-public-cartta-tech-stack-5en0","title":"","level":0},{"type":"text","content":"Article","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[116,118],"level":1},{"type":"paragraph_open","tight":true,"lines":[116,117],"level":2},{"type":"inline","content":"Supabase + Vue 3 in 12 minutes. [Video](https://www.youtube.com/watch?v=YN32uVqAXw8\u0026feature=emb_title)","level":3,"lines":[116,117],"children":[{"type":"text","content":"Supabase + Vue 3 in 12 minutes. ","level":0},{"type":"link_open","href":"https://www.youtube.com/watch?v=YN32uVqAXw8\u0026feature=emb_title","title":"","level":0},{"type":"text","content":"Video","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":2,"lines":[118,119],"level":0},{"type":"inline","content":"[We're hiring](#were-hiring)","level":1,"lines":[118,119],"children":[{"type":"text","content":"We're hiring","level":0}],"lvl":2,"i":10,"seen":0,"slug":"were-hiring"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[120,121],"level":0},{"type":"inline","content":"Come join one of the fastest growing open source projects ever 🤗","level":1,"lines":[120,121],"children":[{"type":"text","content":"Come join one of the fastest growing open source projects ever 🤗","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[122,127],"level":0},{"type":"list_item_open","lines":[122,123],"level":1},{"type":"paragraph_open","tight":true,"lines":[122,123],"level":2},{"type":"inline","content":"[Lead Billing Engineer](https://boards.greenhouse.io/supabase/jobs/4652333004)","level":3,"lines":[122,123],"children":[{"type":"link_open","href":"https://boards.greenhouse.io/supabase/jobs/4652333004","title":"","level":0},{"type":"text","content":"Lead Billing Engineer","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[123,124],"level":1},{"type":"paragraph_open","tight":true,"lines":[123,124],"level":2},{"type":"inline","content":"[Customer Success (US time zone)](https://boards.greenhouse.io/supabase/jobs/4594393004)","level":3,"lines":[123,124],"children":[{"type":"link_open","href":"https://boards.greenhouse.io/supabase/jobs/4594393004","title":"","level":0},{"type":"text","content":"Customer Success (US time zone)","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[124,125],"level":1},{"type":"paragraph_open","tight":true,"lines":[124,125],"level":2},{"type":"inline","content":"[Support Engineers](https://boards.greenhouse.io/supabase/jobs/4191650004)","level":3,"lines":[124,125],"children":[{"type":"link_open","href":"https://boards.greenhouse.io/supabase/jobs/4191650004","title":"","level":0},{"type":"text","content":"Support Engineers","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[125,127],"level":1},{"type":"paragraph_open","tight":true,"lines":[125,126],"level":2},{"type":"inline","content":"[View all our openings](https://boards.greenhouse.io/supabase)","level":3,"lines":[125,126],"children":[{"type":"link_open","href":"https://boards.greenhouse.io/supabase","title":"","level":0},{"type":"text","content":"View all our openings","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"hr","lines":[127,128],"level":0},{"type":"heading_open","hLevel":2,"lines":[129,130],"level":0},{"type":"inline","content":"[Meme Zone](#meme-zone)","level":1,"lines":[129,130],"children":[{"type":"text","content":"Meme Zone","level":0}],"lvl":2,"i":11,"seen":0,"slug":"meme-zone"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[131,132],"level":0},{"type":"inline","content":"If you made it this far in the blog post you deserve a treat. [Follow us on Twitter](https://twitter.com/supabase) for more.","level":1,"lines":[131,132],"children":[{"type":"text","content":"If you made it this far in the blog post you deserve a treat. ","level":0},{"type":"link_open","href":"https://twitter.com/supabase","title":"","level":0},{"type":"text","content":"Follow us on Twitter","level":1},{"type":"link_close","level":0},{"type":"text","content":" for more.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[133,134],"level":0},{"type":"inline","content":"![Supabase meme august 2022](/images/blog/2022-august/supabase-beta-update-august-2022-meme.png)","level":1,"lines":[133,134],"children":[{"type":"image","src":"/images/blog/2022-august/supabase-beta-update-august-2022-meme.png","title":"","alt":"Supabase meme august 2022","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[135,136],"level":0},{"type":"inline","content":"[Get started](#get-started)","level":1,"lines":[135,136],"children":[{"type":"text","content":"Get started","level":0}],"lvl":2,"i":12,"seen":0,"slug":"get-started"},{"type":"heading_close","hLevel":2,"level":0},{"type":"bullet_list_open","lines":[137,141],"level":0},{"type":"list_item_open","lines":[137,138],"level":1},{"type":"paragraph_open","tight":true,"lines":[137,138],"level":2},{"type":"inline","content":"Start using Supabase today: **[supabase.com/dashboard](https://supabase.com/dashboard/)**","level":3,"lines":[137,138],"children":[{"type":"text","content":"Start using Supabase today: ","level":0},{"type":"strong_open","level":0},{"type":"link_open","href":"https://supabase.com/dashboard/","title":"","level":1},{"type":"text","content":"supabase.com/dashboard","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[138,139],"level":1},{"type":"paragraph_open","tight":true,"lines":[138,139],"level":2},{"type":"inline","content":"Make sure to **[star us on GitHub](https://github.com/supabase/supabase)**","level":3,"lines":[138,139],"children":[{"type":"text","content":"Make sure to ","level":0},{"type":"strong_open","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase","title":"","level":1},{"type":"text","content":"star us on GitHub","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[139,140],"level":1},{"type":"paragraph_open","tight":true,"lines":[139,140],"level":2},{"type":"inline","content":"Follow us **[on Twitter](https://twitter.com/supabase)**","level":3,"lines":[139,140],"children":[{"type":"text","content":"Follow us ","level":0},{"type":"strong_open","level":0},{"type":"link_open","href":"https://twitter.com/supabase","title":"","level":1},{"type":"text","content":"on Twitter","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[140,141],"level":1},{"type":"paragraph_open","tight":true,"lines":[140,141],"level":2},{"type":"inline","content":"Subscribe to our **[YouTube channel](https://www.youtube.com/c/supabase)**","level":3,"lines":[140,141],"children":[{"type":"text","content":"Subscribe to our ","level":0},{"type":"strong_open","level":0},{"type":"link_open","href":"https://www.youtube.com/c/supabase","title":"","level":1},{"type":"text","content":"YouTube channel","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0}],"content":"- [Day 1 - Supabase CLI v1 and Management API Beta](#day-1---supabase-cli-v1-and-management-api-beta)\n- [Day 2 - supabase-js v2 Release Candidate](#day-2---supabase-js-v2-release-candidate)\n- [Day 3 - Supabase is SOC2 compliant](#day-3---supabase-is-soc2-compliant)\n- [Day 4 - Realtime: Multiplayer Edition](#day-4---realtime-multiplayer-edition)\n- [Day 5 - Community Day and One More Thing](#day-5---community-day-and-one-more-thing)\n- [Launch Week 5 Hackathon](#launch-week-5-hackathon)\n- [Platform Updates](#platform-updates)\n- [Supabase Migration Guides](#supabase-migration-guides)\n- [Webinar: How Netlify and Supabase Enables “Supa” DX](#webinar-how-netlify-and-supabase-enables-supa-dx)\n- [Extended Community Highlights](#extended-community-highlights)\n- [We're hiring](#were-hiring)\n- [Meme Zone](#meme-zone)\n- [Get started](#get-started)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-beta-update-august-2022"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>