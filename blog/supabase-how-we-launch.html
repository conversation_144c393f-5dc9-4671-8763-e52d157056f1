<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">How we launch at Supabase</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="The history and methodology of Supabase Launch Week." data-next-head=""/><meta property="og:title" content="How we launch at Supabase" data-next-head=""/><meta property="og:description" content="The history and methodology of Supabase Launch Week." data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-how-we-launch" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2021-11-26" data-next-head=""/><meta property="article:author" content="https://github.com/awalias" data-next-head=""/><meta property="article:tag" content="tech" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/how-we-launch/how-we-launch-og.jpg" data-next-head=""/><meta property="og:image:alt" content="How we launch at Supabase thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">How we launch at Supabase</h1><div class="text-light flex space-x-3 text-sm"><p>26 Nov 2021</p><p>•</p><p>15 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/awalias"><div class="flex items-center gap-3"><div class="w-10"><img alt="Ant Wilson avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fawalias.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fawalias.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fawalias.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Ant Wilson</span><span class="text-foreground-lighter mb-0 text-xs">CTO and Co-Founder</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="How we launch at Supabase" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fhow-we-launch%2Fhow-we-launch-thumb.jpg&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fhow-we-launch%2Fhow-we-launch-thumb.jpg&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fhow-we-launch%2Fhow-we-launch-thumb.jpg&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fhow-we-launch%2Fhow-we-launch-thumb.jpg&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fhow-we-launch%2Fhow-we-launch-thumb.jpg&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fhow-we-launch%2Fhow-we-launch-thumb.jpg&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fhow-we-launch%2Fhow-we-launch-thumb.jpg&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fhow-we-launch%2Fhow-we-launch-thumb.jpg&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fhow-we-launch%2Fhow-we-launch-thumb.jpg&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fhow-we-launch%2Fhow-we-launch-thumb.jpg&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fhow-we-launch%2Fhow-we-launch-thumb.jpg&amp;w=3840&amp;q=100"/></div><p>Next week is Supabase Launch Week. It&#x27;s our third Launch Week this year, and is a key part of a Product-Led Growth strategy that has enabled us to increase the number of databases we manage 47% month-on-month for the last 18 months.</p>
<p>We often get asked about how we&#x27;re able to ship so relentlessly, and being an open source company we thought it appropriate to start &quot;open sourcing&quot; some of our methods around building and shipping. Our user base is constructed of companies and individuals who themselves are building for the web and marketing to enormous audiences. Hopefully they can learn some of our tricks and in turn contribute back to the community with their own launch strategies and tactics, helping us continuously learn and improve.</p>
<p>Before I go into the nuts and bolts of what Launch Week is, and the exact processes we follow when executing one, it&#x27;s probably useful if I explain how we landed on this methodology in the first place.</p>
<p></p>
<h3 id="a-brief-history-of-growth-at-supabase" class="group scroll-mt-24">A brief history of growth at Supabase<a href="#a-brief-history-of-growth-at-supabase" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We started work on Supabase in January 2020. Our growth plan from the start included being accepted into YCombinator. Dev Tool companies who enter YC gain a huge advantage from day one. You&#x27;re placed in a cohort of hundreds of other early-stage startups, the majority of whom are developing software, and have just been given a bunch of funding, making them ideal early customers! This, however, turned out to be only a small part of what YC ultimately gifted us.</p>
<p>At the time we joined the batch, we had around 80 alpha users of varying levels of activeness. Our plan was to spend the batch iterating the product around these early alpha users, and to do a big public launch a week or so before Demo Day (an event where you pitch to a shiver of investors). Our plan was foiled however, when an early user shared our site on Hacker News. The post stayed on the home page for several days, and ended up being one of the most upvoted dev tools launches ever (second only to Stripe!). Overnight, the number of databases we were hosting increased ten-fold. Bugs, breakages and feature requests flooded in from every angle; a fantastic problem to have for a fledgling startup. Over the course of the next 2 months we worked hard on a few major items, including Supabase Auth, and migrating our entire stack between cloud providers, along with hundreds of other smaller fixes and incremental improvements - both to our product, and to our company.</p>
<p>A few weeks before Demo Day, we announced Auth, and shouted about it as loudly as possible. We posted it everywhere, and recruited our recently onboarded squad of <a href="../company.html#investors">Technical Angel Investors</a> to help us boost our message on social media. The response was solid, and led to an increase in the rate of user acquisition, and activation/retention rates.</p>
<p>Post YC can often be a sink or swim moment for companies. Throughout the batch, you have regular check-ins with world-class operators, your batch mates, and inspirational talks from founders several steps ahead. When you leave, the external pressure drops, and it&#x27;s up to you to put the frameworks in place to maintain your rate of progress. You no longer have the likes of Michael Siebel asking you why it&#x27;s taking you so long to get Auth out the door and in the hands of users. When faced with this problem we sat down and said, &quot;why don&#x27;t we just pretend that we&#x27;re starting the batch again, and do our best to recreate the conditions of an accelerator internally, complete with our very own Demo Day?&quot;. We found the actual YC Demo Day is kind of an arbitrary deadline useful for motivating all kinds of internal initiatives, whether it&#x27;s fundraising, growth, or product. So we said to the team, let&#x27;s just choose an arbitrary date 3 months from now, and we&#x27;ll ship <em>something</em> huge.</p>
<p>We went the whole hog. We had kick-off events, we invited external people to come in and talk to the team about their origin stories, and even had custom swag made up specific to this product cycle. The <em>something</em>, turned out to be <a href="https://supabase.com/beta">Supabase Beta</a>, an announcement that marked a huge advancement in the Stability, Performance, and Security of our hosted platform. This announcement resulted in a stark increase in the rate of developer sign-ups and activation. Our &quot;fixed timeline, flexible scope&quot; approach to launching products had proved successful with the team. And what&#x27;s more, the build-up to launch, and the launch itself, was some of the most fun any of us had had. Our small team was flying high on adrenaline, and everyone could see for themselves the immediate and direct impact their work had on Supabase&#x27;s viral adoption.</p>
<p>The very next week, still high on adrenaline, we met for a retrospective. The debrief, however, quickly turned into a planning meeting, and the question was raised &quot;what is the MOST ambitious thing we could hope to ship 3 months from now?&quot;. A few projects got kicked about until someone suggested, &quot;why just have one launch? Why can&#x27;t we aim to ship one major feature or announcement every day for a week?&quot;. It was quickly agreed that Launch Day would instead become Launch Week.</p>
<p>During the last week of March 2021, we went on to ship <a href="https://supabase.com/blog/launch-week#friday-one-more-thing">7 major features</a>. We once again saw an immediate uptick in growth rate which helped solidify our strategy.</p>
<p>More recently we ran &quot;<a href="https://supabase.com/blog/supabase-launch-week-sql">Launch Week II: The SQL</a>&quot;, where we bumped the versions of several key products in our stack, and added a <a href="https://supabase.com/blog/supabase-community-day">Community Day</a> with the aim of boosting visibility and support of various open source tools and projects that we rely on, along with products, frameworks, and tooling built by the rapidly expanding <a href="https://github.com/supabase-community/">Supabase Community</a>.</p>
<p>We&#x27;re now just a few days away from Launch Week III, and over time have incrementally improved upon many of the processes and tactics that help us maximize output whilst minimizing stress on the team. So let&#x27;s get into some specifics about how we run a product cycle and the resulting Launch Week.</p>
<h3 id="the-planning-meeting" class="group scroll-mt-24">The Planning Meeting<a href="#the-planning-meeting" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Spanning 12 different countries makes it challenging to find a time that works for everyone, but gathering together online is the first step.</p>
<p>We always start the meeting by firing up a Google Jam Board and collectively constructing &quot;The Supabase Universe&quot;, this is a giant brain dump of terms and concepts related to Supabase. What we&#x27;re building, who it&#x27;s for, who we&#x27;re trying to hire, what our goals and metrics are. Literally everything that&#x27;s anything to do with Supabase goes down on this virtual whiteboard. The purpose is to ensure everyone gets out of their daily mode of operation and loads up the business as a whole into their brains. Developers should be loading up marketing concepts, and the marketers should be thinking about engineering. If we&#x27;re overly focused on one particular area (often engineering), then we risk setting a course in the wrong direction for the entire quarter. It&#x27;s also a method of getting all the &quot;obvious&quot; stuff down on paper so you can move on and focus the creative efforts of everyone on new ideas; a technique borrowed from the creative industries.</p>
<p></p>
<p>Once everyone is warmed up, we dive into the high-level goals and metrics. Are our metrics still relevant? And if so, which ones should we be aiming to boost and why? The more direct a connection between actions and metrics the better, so it&#x27;s important to remind everyone of what the north star is before we start scheming on how to reach it.</p>
<p>Then comes the main course - what are we going to build or do that&#x27;s going to move the needle? List down as many ideas as possible, and go through a process of sorting and filtering as you go. Try not to go too deep on any particular idea, in-depth analysis and exploration can be done later in break out sessions, make sure you have someone responsible for pulling you out of these rabbit holes if the team starts to get drawn in to too much detail.</p>
<p>For us, an essential part of this process is the idea that the scope is flexible - at this stage of ideation it&#x27;s often impossible to tell whether an engineering project is going to take 2 or 102 days. We will always try our best to have it ready for Launch Week, but if not no worries, we&#x27;ll shout about something else, and include it in the next one.</p>
<p>Lastly we will give some discussion to hiring and any other business, and schedule any follow-up meetings or breakouts required.</p>
<h3 id="the-kick-off-meeting" class="group scroll-mt-24">The Kick Off Meeting<a href="#the-kick-off-meeting" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>After the breakouts we typically have a much clearer idea of what we want to aim for, and if the projects themselves are realistic. Our list at this point often sits around 12 items in length. Some projects will drop out and others will be introduced during the course of the next few months, which is ok. Each project typically has a lead assigned, and another couple of people will register their interest in helping out.</p>
<p>For the kick-off, I always try and come up with some grotesquely garish branding (this is an internal project after all) and find some loose theme to roll with. The purpose of the meeting is to remind everyone of the goals, the timeline, and the current list of announcements we plan to make during the launch.</p>
<p></p>
<h3 id="time-to-work" class="group scroll-mt-24">Time to Work<a href="#time-to-work" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Then it&#x27;s time to get our heads down. One important point to make here is that the 3-month cycle doesn&#x27;t necessarily replace more traditional project management methodologies. We leave it up to the project leads to decide how to run their project. Some teams opt for kanban boards, and stand-ups. Other projects run like open source repos, with RFCs, issue management, and fully async comms.</p>
<p>Another key thing to mention here is that we very rarely hold back features until Launch Week. Actually, quite the opposite is true, we encourage everyone to ship features as early as possible. We announce features as they ship in our monthly Beta Update blog and newsletter - which is often limited to a blast radius containing existing users. Full write-ups, marketing copy, and broad Top-of-Funnel type marketing efforts will often happen during Launch Week to hype up these features. Actually, we&#x27;ve found that you can launch a new feature many times over and always manage to reach people who either forgot, ignored, or just plain missed it the first few times. We&#x27;re so close to our own projects that we often overestimate the reach of our online marketing efforts.</p>
<p>In the weeks leading up to Launch Week, we review more closely the list of items we want to Launch and start to plan for what supporting materials we&#x27;ll need. There are some things such as press, which needs to be organized up to 3 weeks before you plan to Launch, so you better get started early on those items. Also being aware of which third parties are going to be involved is important. Maybe we&#x27;re shipping an integration with some other tool or company, cross-company communication can be a blocker, so better to start early.</p>
<p></p>
<h3 id="pre-launch-week" class="group scroll-mt-24">Pre-Launch Week<a href="#pre-launch-week" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>One of our learnings from previous Launch Weeks was to no longer ship to prod on the day of the Launch itself :). We had a blast live-debugging prod issues on Discord last time, but this was not so conducive to getting enough sleep. Our Launch Weeks have become a marathon, so it&#x27;s ideal to get all the major integrations done a week early. Also writing the content and getting someone to edit can take a couple of iterations. This all seems obvious written down here, but it took us a few iterations to start being more regimented.</p>
<p>We have more regular check-ins throughout this week, and make sure everyone is on the same page. We have a process of &quot;swarming&quot; around sticky issues and blockers and a good amount of testing will be done by people who were not involved in the development of a given feature. Particularly around DX, which is an extremely high priority topic at Supabase.</p>
<h3 id="launch-week" class="group scroll-mt-24">Launch Week<a href="#launch-week" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>I can&#x27;t think of many things more fun than executing on a Supabase Launch Week. It&#x27;s the culmination of 3 months of epic work from a highly skilled team of folks distributed across every corner of the planet. The awesome thing is that, with most of the features we ship, the person who implemented the code will be the same person who writes the marketing content. This means that the content usually includes deep technical discussion. They&#x27;re also probably the best person to decide which will be the most effective channels to market that particular feature. As an example, when we launched <a href="http://ui.supabase.com">ui.supabase.com</a>, we made sure to hit up all the front end and design channels, made sure it ranked on Product Hunt, and reached out to some friendly designers to help boost our message. This is very different from the approach we took to marketing our <a href="https://supabase.com/blog/supabase-storage">file storage offering</a>. The technical design discussion of Storage is better suited to channels like Hacker News, and technical conference audiences who may be more interested in DX than UI components.</p>
<p>When it comes to the day of each launch we&#x27;ve gotten into the habit of constructing a finely detailed schedule to follow. It will contain items such as:</p>
<ul>
<li>7:30am Twitter spaces reminder tweet</li>
<li>7:55am Product Hunt goes live</li>
<li>8:00am Blog post goes live</li>
<li>8:05am Post launch tweet 1</li>
<li>8:10am Share tweet with Angels</li>
<li>8:15am Twitter spaces go live</li>
</ul>
<p>Of course, it&#x27;s possible to run this all without such a fine-grained schedule, but the launches can get kind of hectic, so it&#x27;s easier to not have to think - just do.</p>
<h3 id="the-rest" class="group scroll-mt-24">The Rest<a href="#the-rest" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Despite being a ton of fun, it&#x27;s inevitable that some team members will have pulled long days and nights to get their feature out the door, as well as pumping it on as many marketing channels as possible. Launching new things also includes managing support requests, bug fixes, responding to questions, and even managing PRs from the community excited to contribute to new repos or areas of the codebase. This is unsustainable without proper rest so each cycle ends with a good amount of downtime and maintenance mode. Having said that, the adrenaline of Launch Week often spills over into the following days and weeks, so it&#x27;s not uncommon for members of the team to continue in Launch Mode for a while before taking some downtime.</p>
<h3 id="the-retrospective" class="group scroll-mt-24">The Retrospective<a href="#the-retrospective" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Once the dust has settled, the <strong>most</strong> <strong>important</strong> phase of the entire process can begin. To skip the retro would be to rubbish the entire process, as we would never learn from mistakes or adapt to capitalize on new tactics discovered throughout the preceding weeks and months. We always make sure to cover what went well, what went badly, and what can be improved. This list is then read aloud again at the start of the next planning meeting. The process as it exists today is the product of 4 previous iterations, and is still far from its final form. Amongst other things, our team is growing fast, so it&#x27;s yet to be seen if the same processes will extend to so many team members, and in which ways they will have to adapt. Lastly, we always make sure to discuss the question &quot;Is Launch Week still relevant?&quot;, and &quot;How would we know if we were approaching some local maxima in terms of process and productive output?&quot;.</p>
<p></p>
<h3 id="some-other-thoughts" class="group scroll-mt-24">Some Other Thoughts<a href="#some-other-thoughts" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>This &quot;internal accelerator&quot; model has worked well within Supabase as a 2-person team and is still proving effective now at 30, but there&#x27;s no telling whether it would work as well in other organizations. I would be very keen to learn from those who also do something similar.</p>
<p>In some ways, running Supabase is like launching many startups at the same time, we have to build and maintain many different parallel projects in order to match Firebase&#x27;s incredible suite of products, which could be one reason why it lends itself so well to this acceleration model. And, in fact, being a Firebase alternative is only a small part of <a href="https://supabase.com/blog/supabase-series-a">what we want to achieve with Supabase</a>, so we have many, many more initiatives to run as we grow.</p>
<p>Running an accelerator model may also be why we&#x27;ve had success <a href="https://about.supabase.com/careers/founders">hiring so many other founders</a>. We&#x27;re able to give a high degree of autonomy to individuals when it comes to managing projects and initiatives. If this kind of work environment sounds interesting to you, we have tons of <a href="https://about.supabase.com/careers">open roles</a>. We&#x27;ve already hired people who joined because they &quot;couldn&#x27;t stand to watch another Launch Week from the sidelines&quot; and team members often quote experiencing their first Launch Week as one of the highlights of their Supabase journey so far. It clearly has benefits that extend beyond just growth and user activation.</p>
<p>Make sure you follow us on <a href="https://twitter.com/supabase">Twitter</a>, join our <a href="https://discord.supabase.com">Discord</a>, or <a href="../dashboard/org.html">sign up for Supabase</a> in order to keep up to date on all things Supabase.</p>
<p>Supabase is the Open Source Firebase Alternative.</p></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-how-we-launch&amp;text=How%20we%20launch%20at%20Supabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-how-we-launch&amp;text=How%20we%20launch%20at%20Supabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-how-we-launch&amp;t=How%20we%20launch%20at%20Supabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="postgrest-9.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">PostgREST 9</h4><p class="small">27 November 2021</p></div></div></div></div></a></div><div><a href="supabase-launch-week-the-trilogy.html"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Supabase Launch Week III: Holiday Special</h4><p class="small">26 November 2021</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/tech"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">tech</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#a-brief-history-of-growth-at-supabase">A brief history of growth at Supabase</a></li>
<li><a href="#the-planning-meeting">The Planning Meeting</a></li>
<li><a href="#the-kick-off-meeting">The Kick Off Meeting</a></li>
<li><a href="#time-to-work">Time to Work</a></li>
<li><a href="#pre-launch-week">Pre-Launch Week</a></li>
<li><a href="#launch-week">Launch Week</a></li>
<li><a href="#the-rest">The Rest</a></li>
<li><a href="#the-retrospective">The Retrospective</a></li>
<li><a href="#some-other-thoughts">Some Other Thoughts</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-how-we-launch&amp;text=How%20we%20launch%20at%20Supabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-how-we-launch&amp;text=How%20we%20launch%20at%20Supabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-how-we-launch&amp;t=How%20we%20launch%20at%20Supabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"postgrest-9","title":"PostgREST 9","description":"New features and updates in PostgREST version 9.","author":"steve_chavez","author_url":"https://github.com/steve-chavez","author_image_url":"https://github.com/steve-chavez.png","image":"postgrest-9/whats-new-in-postgrest-9-og.png","thumb":"postgrest-9/whats-new-in-postgrest-9-thumb.png","categories":["postgres"],"tags":["launch-week","release-notes","tech","community"],"date":"2021-11-27","toc_depth":2,"formattedDate":"27 November 2021","readingTime":"4 minute read","url":"/blog/postgrest-9","path":"/blog/postgrest-9"},"nextPost":{"slug":"supabase-launch-week-the-trilogy","title":"Supabase Launch Week III: Holiday Special","description":"Tis the season to be shipping.","author":"paul_copplestone","author_url":"https://github.com/kiwicopple","author_image_url":"https://github.com/kiwicopple.png","image":"launch-week-three/launch-week-three-og.jpg","thumb":"launch-week-three/launch-week-three-thumb.jpg","categories":["launch-week"],"tags":["launch-week"],"date":"2021-11-26","toc_depth":2,"formattedDate":"26 November 2021","readingTime":"2 minute read","url":"/blog/supabase-launch-week-the-trilogy","path":"/blog/supabase-launch-week-the-trilogy"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-how-we-launch","source":"\nNext week is Supabase Launch Week. It's our third Launch Week this year, and is a key part of a Product-Led Growth strategy that has enabled us to increase the number of databases we manage 47% month-on-month for the last 18 months.\n\nWe often get asked about how we're able to ship so relentlessly, and being an open source company we thought it appropriate to start \"open sourcing\" some of our methods around building and shipping. Our user base is constructed of companies and individuals who themselves are building for the web and marketing to enormous audiences. Hopefully they can learn some of our tricks and in turn contribute back to the community with their own launch strategies and tactics, helping us continuously learn and improve.\n\nBefore I go into the nuts and bolts of what Launch Week is, and the exact processes we follow when executing one, it's probably useful if I explain how we landed on this methodology in the first place.\n\n![supabase monthly growth](/images/blog/how-we-launch/total-databases-launched.jpg)\n\n### A brief history of growth at Supabase\n\nWe started work on Supabase in January 2020. Our growth plan from the start included being accepted into YCombinator. Dev Tool companies who enter YC gain a huge advantage from day one. You're placed in a cohort of hundreds of other early-stage startups, the majority of whom are developing software, and have just been given a bunch of funding, making them ideal early customers! This, however, turned out to be only a small part of what YC ultimately gifted us.\n\nAt the time we joined the batch, we had around 80 alpha users of varying levels of activeness. Our plan was to spend the batch iterating the product around these early alpha users, and to do a big public launch a week or so before Demo Day (an event where you pitch to a shiver of investors). Our plan was foiled however, when an early user shared our site on Hacker News. The post stayed on the home page for several days, and ended up being one of the most upvoted dev tools launches ever (second only to Stripe!). Overnight, the number of databases we were hosting increased ten-fold. Bugs, breakages and feature requests flooded in from every angle; a fantastic problem to have for a fledgling startup. Over the course of the next 2 months we worked hard on a few major items, including Supabase Auth, and migrating our entire stack between cloud providers, along with hundreds of other smaller fixes and incremental improvements - both to our product, and to our company.\n\nA few weeks before Demo Day, we announced Auth, and shouted about it as loudly as possible. We posted it everywhere, and recruited our recently onboarded squad of [Technical Angel Investors](https://supabase.com/company#investors) to help us boost our message on social media. The response was solid, and led to an increase in the rate of user acquisition, and activation/retention rates.\n\nPost YC can often be a sink or swim moment for companies. Throughout the batch, you have regular check-ins with world-class operators, your batch mates, and inspirational talks from founders several steps ahead. When you leave, the external pressure drops, and it's up to you to put the frameworks in place to maintain your rate of progress. You no longer have the likes of Michael Siebel asking you why it's taking you so long to get Auth out the door and in the hands of users. When faced with this problem we sat down and said, \"why don't we just pretend that we're starting the batch again, and do our best to recreate the conditions of an accelerator internally, complete with our very own Demo Day?\". We found the actual YC Demo Day is kind of an arbitrary deadline useful for motivating all kinds of internal initiatives, whether it's fundraising, growth, or product. So we said to the team, let's just choose an arbitrary date 3 months from now, and we'll ship _something_ huge.\n\nWe went the whole hog. We had kick-off events, we invited external people to come in and talk to the team about their origin stories, and even had custom swag made up specific to this product cycle. The _something_, turned out to be [Supabase Beta](https://supabase.com/beta), an announcement that marked a huge advancement in the Stability, Performance, and Security of our hosted platform. This announcement resulted in a stark increase in the rate of developer sign-ups and activation. Our \"fixed timeline, flexible scope\" approach to launching products had proved successful with the team. And what's more, the build-up to launch, and the launch itself, was some of the most fun any of us had had. Our small team was flying high on adrenaline, and everyone could see for themselves the immediate and direct impact their work had on Supabase's viral adoption.\n\nThe very next week, still high on adrenaline, we met for a retrospective. The debrief, however, quickly turned into a planning meeting, and the question was raised \"what is the MOST ambitious thing we could hope to ship 3 months from now?\". A few projects got kicked about until someone suggested, \"why just have one launch? Why can't we aim to ship one major feature or announcement every day for a week?\". It was quickly agreed that Launch Day would instead become Launch Week.\n\nDuring the last week of March 2021, we went on to ship [7 major features](/blog/launch-week#friday-one-more-thing). We once again saw an immediate uptick in growth rate which helped solidify our strategy.\n\nMore recently we ran \"[Launch Week II: The SQL](/blog/supabase-launch-week-sql)\", where we bumped the versions of several key products in our stack, and added a [Community Day](https://supabase.com/blog/supabase-community-day) with the aim of boosting visibility and support of various open source tools and projects that we rely on, along with products, frameworks, and tooling built by the rapidly expanding [Supabase Community](https://github.com/supabase-community/).\n\nWe're now just a few days away from Launch Week III, and over time have incrementally improved upon many of the processes and tactics that help us maximize output whilst minimizing stress on the team. So let's get into some specifics about how we run a product cycle and the resulting Launch Week.\n\n### The Planning Meeting\n\nSpanning 12 different countries makes it challenging to find a time that works for everyone, but gathering together online is the first step.\n\nWe always start the meeting by firing up a Google Jam Board and collectively constructing \"The Supabase Universe\", this is a giant brain dump of terms and concepts related to Supabase. What we're building, who it's for, who we're trying to hire, what our goals and metrics are. Literally everything that's anything to do with Supabase goes down on this virtual whiteboard. The purpose is to ensure everyone gets out of their daily mode of operation and loads up the business as a whole into their brains. Developers should be loading up marketing concepts, and the marketers should be thinking about engineering. If we're overly focused on one particular area (often engineering), then we risk setting a course in the wrong direction for the entire quarter. It's also a method of getting all the \"obvious\" stuff down on paper so you can move on and focus the creative efforts of everyone on new ideas; a technique borrowed from the creative industries.\n\n![supabase monthly growth](/images/blog/how-we-launch/supabase-universe.jpg)\n\nOnce everyone is warmed up, we dive into the high-level goals and metrics. Are our metrics still relevant? And if so, which ones should we be aiming to boost and why? The more direct a connection between actions and metrics the better, so it's important to remind everyone of what the north star is before we start scheming on how to reach it.\n\nThen comes the main course - what are we going to build or do that's going to move the needle? List down as many ideas as possible, and go through a process of sorting and filtering as you go. Try not to go too deep on any particular idea, in-depth analysis and exploration can be done later in break out sessions, make sure you have someone responsible for pulling you out of these rabbit holes if the team starts to get drawn in to too much detail.\n\nFor us, an essential part of this process is the idea that the scope is flexible - at this stage of ideation it's often impossible to tell whether an engineering project is going to take 2 or 102 days. We will always try our best to have it ready for Launch Week, but if not no worries, we'll shout about something else, and include it in the next one.\n\nLastly we will give some discussion to hiring and any other business, and schedule any follow-up meetings or breakouts required.\n\n### The Kick Off Meeting\n\nAfter the breakouts we typically have a much clearer idea of what we want to aim for, and if the projects themselves are realistic. Our list at this point often sits around 12 items in length. Some projects will drop out and others will be introduced during the course of the next few months, which is ok. Each project typically has a lead assigned, and another couple of people will register their interest in helping out.\n\nFor the kick-off, I always try and come up with some grotesquely garish branding (this is an internal project after all) and find some loose theme to roll with. The purpose of the meeting is to remind everyone of the goals, the timeline, and the current list of announcements we plan to make during the launch.\n\n![supabase monthly growth](/images/blog/how-we-launch/how-we-launch-stickers.jpg)\n\n### Time to Work\n\nThen it's time to get our heads down. One important point to make here is that the 3-month cycle doesn't necessarily replace more traditional project management methodologies. We leave it up to the project leads to decide how to run their project. Some teams opt for kanban boards, and stand-ups. Other projects run like open source repos, with RFCs, issue management, and fully async comms.\n\nAnother key thing to mention here is that we very rarely hold back features until Launch Week. Actually, quite the opposite is true, we encourage everyone to ship features as early as possible. We announce features as they ship in our monthly Beta Update blog and newsletter - which is often limited to a blast radius containing existing users. Full write-ups, marketing copy, and broad Top-of-Funnel type marketing efforts will often happen during Launch Week to hype up these features. Actually, we've found that you can launch a new feature many times over and always manage to reach people who either forgot, ignored, or just plain missed it the first few times. We're so close to our own projects that we often overestimate the reach of our online marketing efforts.\n\nIn the weeks leading up to Launch Week, we review more closely the list of items we want to Launch and start to plan for what supporting materials we'll need. There are some things such as press, which needs to be organized up to 3 weeks before you plan to Launch, so you better get started early on those items. Also being aware of which third parties are going to be involved is important. Maybe we're shipping an integration with some other tool or company, cross-company communication can be a blocker, so better to start early.\n\n![supabase monthly growth](/images/blog/how-we-launch/how-we-launch-timeline.jpg)\n\n### Pre-Launch Week\n\nOne of our learnings from previous Launch Weeks was to no longer ship to prod on the day of the Launch itself :). We had a blast live-debugging prod issues on Discord last time, but this was not so conducive to getting enough sleep. Our Launch Weeks have become a marathon, so it's ideal to get all the major integrations done a week early. Also writing the content and getting someone to edit can take a couple of iterations. This all seems obvious written down here, but it took us a few iterations to start being more regimented.\n\nWe have more regular check-ins throughout this week, and make sure everyone is on the same page. We have a process of \"swarming\" around sticky issues and blockers and a good amount of testing will be done by people who were not involved in the development of a given feature. Particularly around DX, which is an extremely high priority topic at Supabase.\n\n### Launch Week\n\nI can't think of many things more fun than executing on a Supabase Launch Week. It's the culmination of 3 months of epic work from a highly skilled team of folks distributed across every corner of the planet. The awesome thing is that, with most of the features we ship, the person who implemented the code will be the same person who writes the marketing content. This means that the content usually includes deep technical discussion. They're also probably the best person to decide which will be the most effective channels to market that particular feature. As an example, when we launched [ui.supabase.com](http://ui.supabase.com), we made sure to hit up all the front end and design channels, made sure it ranked on Product Hunt, and reached out to some friendly designers to help boost our message. This is very different from the approach we took to marketing our [file storage offering](https://supabase.com/blog/supabase-storage). The technical design discussion of Storage is better suited to channels like Hacker News, and technical conference audiences who may be more interested in DX than UI components.\n\nWhen it comes to the day of each launch we've gotten into the habit of constructing a finely detailed schedule to follow. It will contain items such as:\n\n- 7:30am Twitter spaces reminder tweet\n- 7:55am Product Hunt goes live\n- 8:00am Blog post goes live\n- 8:05am Post launch tweet 1\n- 8:10am Share tweet with Angels\n- 8:15am Twitter spaces go live\n\nOf course, it's possible to run this all without such a fine-grained schedule, but the launches can get kind of hectic, so it's easier to not have to think - just do.\n\n### The Rest\n\nDespite being a ton of fun, it's inevitable that some team members will have pulled long days and nights to get their feature out the door, as well as pumping it on as many marketing channels as possible. Launching new things also includes managing support requests, bug fixes, responding to questions, and even managing PRs from the community excited to contribute to new repos or areas of the codebase. This is unsustainable without proper rest so each cycle ends with a good amount of downtime and maintenance mode. Having said that, the adrenaline of Launch Week often spills over into the following days and weeks, so it's not uncommon for members of the team to continue in Launch Mode for a while before taking some downtime.\n\n### The Retrospective\n\nOnce the dust has settled, the **most** **important** phase of the entire process can begin. To skip the retro would be to rubbish the entire process, as we would never learn from mistakes or adapt to capitalize on new tactics discovered throughout the preceding weeks and months. We always make sure to cover what went well, what went badly, and what can be improved. This list is then read aloud again at the start of the next planning meeting. The process as it exists today is the product of 4 previous iterations, and is still far from its final form. Amongst other things, our team is growing fast, so it's yet to be seen if the same processes will extend to so many team members, and in which ways they will have to adapt. Lastly, we always make sure to discuss the question \"Is Launch Week still relevant?\", and \"How would we know if we were approaching some local maxima in terms of process and productive output?\".\n\n![supabase monthly growth](/images/blog/how-we-launch/how-we-launch-retrospective.jpg)\n\n### Some Other Thoughts\n\nThis \"internal accelerator\" model has worked well within Supabase as a 2-person team and is still proving effective now at 30, but there's no telling whether it would work as well in other organizations. I would be very keen to learn from those who also do something similar.\n\nIn some ways, running Supabase is like launching many startups at the same time, we have to build and maintain many different parallel projects in order to match Firebase's incredible suite of products, which could be one reason why it lends itself so well to this acceleration model. And, in fact, being a Firebase alternative is only a small part of [what we want to achieve with Supabase](https://supabase.com/blog/supabase-series-a), so we have many, many more initiatives to run as we grow.\n\nRunning an accelerator model may also be why we've had success [hiring so many other founders](https://about.supabase.com/careers/founders). We're able to give a high degree of autonomy to individuals when it comes to managing projects and initiatives. If this kind of work environment sounds interesting to you, we have tons of [open roles](https://about.supabase.com/careers). We've already hired people who joined because they \"couldn't stand to watch another Launch Week from the sidelines\" and team members often quote experiencing their first Launch Week as one of the highlights of their Supabase journey so far. It clearly has benefits that extend beyond just growth and user activation.\n\nMake sure you follow us on [Twitter](https://twitter.com/supabase), join our [Discord](https://discord.supabase.com), or [sign up for Supabase](https://supabase.com/dashboard) in order to keep up to date on all things Supabase.\n\nSupabase is the Open Source Firebase Alternative.\n","title":"How we launch at Supabase","description":"The history and methodology of Supabase Launch Week.","author":"ant_wilson","author_url":"https://github.com/awalias","author_image_url":"https://github.com/awalias.png","image":"how-we-launch/how-we-launch-og.jpg","thumb":"how-we-launch/how-we-launch-thumb.jpg","categories":["company"],"tags":["tech"],"date":"2021-11-26","toc_depth":3,"content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    img: \"img\",\n    h3: \"h3\",\n    a: \"a\",\n    em: \"em\",\n    ul: \"ul\",\n    li: \"li\",\n    strong: \"strong\"\n  }, _provideComponents(), props.components);\n  return _jsxs(_Fragment, {\n    children: [_jsx(_components.p, {\n      children: \"Next week is Supabase Launch Week. It's our third Launch Week this year, and is a key part of a Product-Led Growth strategy that has enabled us to increase the number of databases we manage 47% month-on-month for the last 18 months.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We often get asked about how we're able to ship so relentlessly, and being an open source company we thought it appropriate to start \\\"open sourcing\\\" some of our methods around building and shipping. Our user base is constructed of companies and individuals who themselves are building for the web and marketing to enormous audiences. Hopefully they can learn some of our tricks and in turn contribute back to the community with their own launch strategies and tactics, helping us continuously learn and improve.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Before I go into the nuts and bolts of what Launch Week is, and the exact processes we follow when executing one, it's probably useful if I explain how we landed on this methodology in the first place.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/how-we-launch/total-databases-launched.jpg\",\n        alt: \"supabase monthly growth\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"a-brief-history-of-growth-at-supabase\",\n      children: \"A brief history of growth at Supabase\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We started work on Supabase in January 2020. Our growth plan from the start included being accepted into YCombinator. Dev Tool companies who enter YC gain a huge advantage from day one. You're placed in a cohort of hundreds of other early-stage startups, the majority of whom are developing software, and have just been given a bunch of funding, making them ideal early customers! This, however, turned out to be only a small part of what YC ultimately gifted us.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"At the time we joined the batch, we had around 80 alpha users of varying levels of activeness. Our plan was to spend the batch iterating the product around these early alpha users, and to do a big public launch a week or so before Demo Day (an event where you pitch to a shiver of investors). Our plan was foiled however, when an early user shared our site on Hacker News. The post stayed on the home page for several days, and ended up being one of the most upvoted dev tools launches ever (second only to Stripe!). Overnight, the number of databases we were hosting increased ten-fold. Bugs, breakages and feature requests flooded in from every angle; a fantastic problem to have for a fledgling startup. Over the course of the next 2 months we worked hard on a few major items, including Supabase Auth, and migrating our entire stack between cloud providers, along with hundreds of other smaller fixes and incremental improvements - both to our product, and to our company.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"A few weeks before Demo Day, we announced Auth, and shouted about it as loudly as possible. We posted it everywhere, and recruited our recently onboarded squad of \", _jsx(_components.a, {\n        href: \"https://supabase.com/company#investors\",\n        children: \"Technical Angel Investors\"\n      }), \" to help us boost our message on social media. The response was solid, and led to an increase in the rate of user acquisition, and activation/retention rates.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Post YC can often be a sink or swim moment for companies. Throughout the batch, you have regular check-ins with world-class operators, your batch mates, and inspirational talks from founders several steps ahead. When you leave, the external pressure drops, and it's up to you to put the frameworks in place to maintain your rate of progress. You no longer have the likes of Michael Siebel asking you why it's taking you so long to get Auth out the door and in the hands of users. When faced with this problem we sat down and said, \\\"why don't we just pretend that we're starting the batch again, and do our best to recreate the conditions of an accelerator internally, complete with our very own Demo Day?\\\". We found the actual YC Demo Day is kind of an arbitrary deadline useful for motivating all kinds of internal initiatives, whether it's fundraising, growth, or product. So we said to the team, let's just choose an arbitrary date 3 months from now, and we'll ship \", _jsx(_components.em, {\n        children: \"something\"\n      }), \" huge.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We went the whole hog. We had kick-off events, we invited external people to come in and talk to the team about their origin stories, and even had custom swag made up specific to this product cycle. The \", _jsx(_components.em, {\n        children: \"something\"\n      }), \", turned out to be \", _jsx(_components.a, {\n        href: \"https://supabase.com/beta\",\n        children: \"Supabase Beta\"\n      }), \", an announcement that marked a huge advancement in the Stability, Performance, and Security of our hosted platform. This announcement resulted in a stark increase in the rate of developer sign-ups and activation. Our \\\"fixed timeline, flexible scope\\\" approach to launching products had proved successful with the team. And what's more, the build-up to launch, and the launch itself, was some of the most fun any of us had had. Our small team was flying high on adrenaline, and everyone could see for themselves the immediate and direct impact their work had on Supabase's viral adoption.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The very next week, still high on adrenaline, we met for a retrospective. The debrief, however, quickly turned into a planning meeting, and the question was raised \\\"what is the MOST ambitious thing we could hope to ship 3 months from now?\\\". A few projects got kicked about until someone suggested, \\\"why just have one launch? Why can't we aim to ship one major feature or announcement every day for a week?\\\". It was quickly agreed that Launch Day would instead become Launch Week.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"During the last week of March 2021, we went on to ship \", _jsx(_components.a, {\n        href: \"/blog/launch-week#friday-one-more-thing\",\n        children: \"7 major features\"\n      }), \". We once again saw an immediate uptick in growth rate which helped solidify our strategy.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"More recently we ran \\\"\", _jsx(_components.a, {\n        href: \"/blog/supabase-launch-week-sql\",\n        children: \"Launch Week II: The SQL\"\n      }), \"\\\", where we bumped the versions of several key products in our stack, and added a \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/supabase-community-day\",\n        children: \"Community Day\"\n      }), \" with the aim of boosting visibility and support of various open source tools and projects that we rely on, along with products, frameworks, and tooling built by the rapidly expanding \", _jsx(_components.a, {\n        href: \"https://github.com/supabase-community/\",\n        children: \"Supabase Community\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We're now just a few days away from Launch Week III, and over time have incrementally improved upon many of the processes and tactics that help us maximize output whilst minimizing stress on the team. So let's get into some specifics about how we run a product cycle and the resulting Launch Week.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"the-planning-meeting\",\n      children: \"The Planning Meeting\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Spanning 12 different countries makes it challenging to find a time that works for everyone, but gathering together online is the first step.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We always start the meeting by firing up a Google Jam Board and collectively constructing \\\"The Supabase Universe\\\", this is a giant brain dump of terms and concepts related to Supabase. What we're building, who it's for, who we're trying to hire, what our goals and metrics are. Literally everything that's anything to do with Supabase goes down on this virtual whiteboard. The purpose is to ensure everyone gets out of their daily mode of operation and loads up the business as a whole into their brains. Developers should be loading up marketing concepts, and the marketers should be thinking about engineering. If we're overly focused on one particular area (often engineering), then we risk setting a course in the wrong direction for the entire quarter. It's also a method of getting all the \\\"obvious\\\" stuff down on paper so you can move on and focus the creative efforts of everyone on new ideas; a technique borrowed from the creative industries.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/how-we-launch/supabase-universe.jpg\",\n        alt: \"supabase monthly growth\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Once everyone is warmed up, we dive into the high-level goals and metrics. Are our metrics still relevant? And if so, which ones should we be aiming to boost and why? The more direct a connection between actions and metrics the better, so it's important to remind everyone of what the north star is before we start scheming on how to reach it.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Then comes the main course - what are we going to build or do that's going to move the needle? List down as many ideas as possible, and go through a process of sorting and filtering as you go. Try not to go too deep on any particular idea, in-depth analysis and exploration can be done later in break out sessions, make sure you have someone responsible for pulling you out of these rabbit holes if the team starts to get drawn in to too much detail.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"For us, an essential part of this process is the idea that the scope is flexible - at this stage of ideation it's often impossible to tell whether an engineering project is going to take 2 or 102 days. We will always try our best to have it ready for Launch Week, but if not no worries, we'll shout about something else, and include it in the next one.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Lastly we will give some discussion to hiring and any other business, and schedule any follow-up meetings or breakouts required.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"the-kick-off-meeting\",\n      children: \"The Kick Off Meeting\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"After the breakouts we typically have a much clearer idea of what we want to aim for, and if the projects themselves are realistic. Our list at this point often sits around 12 items in length. Some projects will drop out and others will be introduced during the course of the next few months, which is ok. Each project typically has a lead assigned, and another couple of people will register their interest in helping out.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"For the kick-off, I always try and come up with some grotesquely garish branding (this is an internal project after all) and find some loose theme to roll with. The purpose of the meeting is to remind everyone of the goals, the timeline, and the current list of announcements we plan to make during the launch.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/how-we-launch/how-we-launch-stickers.jpg\",\n        alt: \"supabase monthly growth\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"time-to-work\",\n      children: \"Time to Work\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Then it's time to get our heads down. One important point to make here is that the 3-month cycle doesn't necessarily replace more traditional project management methodologies. We leave it up to the project leads to decide how to run their project. Some teams opt for kanban boards, and stand-ups. Other projects run like open source repos, with RFCs, issue management, and fully async comms.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Another key thing to mention here is that we very rarely hold back features until Launch Week. Actually, quite the opposite is true, we encourage everyone to ship features as early as possible. We announce features as they ship in our monthly Beta Update blog and newsletter - which is often limited to a blast radius containing existing users. Full write-ups, marketing copy, and broad Top-of-Funnel type marketing efforts will often happen during Launch Week to hype up these features. Actually, we've found that you can launch a new feature many times over and always manage to reach people who either forgot, ignored, or just plain missed it the first few times. We're so close to our own projects that we often overestimate the reach of our online marketing efforts.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In the weeks leading up to Launch Week, we review more closely the list of items we want to Launch and start to plan for what supporting materials we'll need. There are some things such as press, which needs to be organized up to 3 weeks before you plan to Launch, so you better get started early on those items. Also being aware of which third parties are going to be involved is important. Maybe we're shipping an integration with some other tool or company, cross-company communication can be a blocker, so better to start early.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/how-we-launch/how-we-launch-timeline.jpg\",\n        alt: \"supabase monthly growth\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"pre-launch-week\",\n      children: \"Pre-Launch Week\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"One of our learnings from previous Launch Weeks was to no longer ship to prod on the day of the Launch itself :). We had a blast live-debugging prod issues on Discord last time, but this was not so conducive to getting enough sleep. Our Launch Weeks have become a marathon, so it's ideal to get all the major integrations done a week early. Also writing the content and getting someone to edit can take a couple of iterations. This all seems obvious written down here, but it took us a few iterations to start being more regimented.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We have more regular check-ins throughout this week, and make sure everyone is on the same page. We have a process of \\\"swarming\\\" around sticky issues and blockers and a good amount of testing will be done by people who were not involved in the development of a given feature. Particularly around DX, which is an extremely high priority topic at Supabase.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"launch-week\",\n      children: \"Launch Week\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"I can't think of many things more fun than executing on a Supabase Launch Week. It's the culmination of 3 months of epic work from a highly skilled team of folks distributed across every corner of the planet. The awesome thing is that, with most of the features we ship, the person who implemented the code will be the same person who writes the marketing content. This means that the content usually includes deep technical discussion. They're also probably the best person to decide which will be the most effective channels to market that particular feature. As an example, when we launched \", _jsx(_components.a, {\n        href: \"http://ui.supabase.com\",\n        children: \"ui.supabase.com\"\n      }), \", we made sure to hit up all the front end and design channels, made sure it ranked on Product Hunt, and reached out to some friendly designers to help boost our message. This is very different from the approach we took to marketing our \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/supabase-storage\",\n        children: \"file storage offering\"\n      }), \". The technical design discussion of Storage is better suited to channels like Hacker News, and technical conference audiences who may be more interested in DX than UI components.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"When it comes to the day of each launch we've gotten into the habit of constructing a finely detailed schedule to follow. It will contain items such as:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"7:30am Twitter spaces reminder tweet\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"7:55am Product Hunt goes live\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"8:00am Blog post goes live\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"8:05am Post launch tweet 1\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"8:10am Share tweet with Angels\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"8:15am Twitter spaces go live\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Of course, it's possible to run this all without such a fine-grained schedule, but the launches can get kind of hectic, so it's easier to not have to think - just do.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"the-rest\",\n      children: \"The Rest\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Despite being a ton of fun, it's inevitable that some team members will have pulled long days and nights to get their feature out the door, as well as pumping it on as many marketing channels as possible. Launching new things also includes managing support requests, bug fixes, responding to questions, and even managing PRs from the community excited to contribute to new repos or areas of the codebase. This is unsustainable without proper rest so each cycle ends with a good amount of downtime and maintenance mode. Having said that, the adrenaline of Launch Week often spills over into the following days and weeks, so it's not uncommon for members of the team to continue in Launch Mode for a while before taking some downtime.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"the-retrospective\",\n      children: \"The Retrospective\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Once the dust has settled, the \", _jsx(_components.strong, {\n        children: \"most\"\n      }), \" \", _jsx(_components.strong, {\n        children: \"important\"\n      }), \" phase of the entire process can begin. To skip the retro would be to rubbish the entire process, as we would never learn from mistakes or adapt to capitalize on new tactics discovered throughout the preceding weeks and months. We always make sure to cover what went well, what went badly, and what can be improved. This list is then read aloud again at the start of the next planning meeting. The process as it exists today is the product of 4 previous iterations, and is still far from its final form. Amongst other things, our team is growing fast, so it's yet to be seen if the same processes will extend to so many team members, and in which ways they will have to adapt. Lastly, we always make sure to discuss the question \\\"Is Launch Week still relevant?\\\", and \\\"How would we know if we were approaching some local maxima in terms of process and productive output?\\\".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/how-we-launch/how-we-launch-retrospective.jpg\",\n        alt: \"supabase monthly growth\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"some-other-thoughts\",\n      children: \"Some Other Thoughts\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This \\\"internal accelerator\\\" model has worked well within Supabase as a 2-person team and is still proving effective now at 30, but there's no telling whether it would work as well in other organizations. I would be very keen to learn from those who also do something similar.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In some ways, running Supabase is like launching many startups at the same time, we have to build and maintain many different parallel projects in order to match Firebase's incredible suite of products, which could be one reason why it lends itself so well to this acceleration model. And, in fact, being a Firebase alternative is only a small part of \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/supabase-series-a\",\n        children: \"what we want to achieve with Supabase\"\n      }), \", so we have many, many more initiatives to run as we grow.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Running an accelerator model may also be why we've had success \", _jsx(_components.a, {\n        href: \"https://about.supabase.com/careers/founders\",\n        children: \"hiring so many other founders\"\n      }), \". We're able to give a high degree of autonomy to individuals when it comes to managing projects and initiatives. If this kind of work environment sounds interesting to you, we have tons of \", _jsx(_components.a, {\n        href: \"https://about.supabase.com/careers\",\n        children: \"open roles\"\n      }), \". We've already hired people who joined because they \\\"couldn't stand to watch another Launch Week from the sidelines\\\" and team members often quote experiencing their first Launch Week as one of the highlights of their Supabase journey so far. It clearly has benefits that extend beyond just growth and user activation.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Make sure you follow us on \", _jsx(_components.a, {\n        href: \"https://twitter.com/supabase\",\n        children: \"Twitter\"\n      }), \", join our \", _jsx(_components.a, {\n        href: \"https://discord.supabase.com\",\n        children: \"Discord\"\n      }), \", or \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard\",\n        children: \"sign up for Supabase\"\n      }), \" in order to keep up to date on all things Supabase.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Supabase is the Open Source Firebase Alternative.\"\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"A brief history of growth at Supabase","slug":"a-brief-history-of-growth-at-supabase","lvl":3,"i":0,"seen":0},{"content":"The Planning Meeting","slug":"the-planning-meeting","lvl":3,"i":1,"seen":0},{"content":"The Kick Off Meeting","slug":"the-kick-off-meeting","lvl":3,"i":2,"seen":0},{"content":"Time to Work","slug":"time-to-work","lvl":3,"i":3,"seen":0},{"content":"Pre-Launch Week","slug":"pre-launch-week","lvl":3,"i":4,"seen":0},{"content":"Launch Week","slug":"launch-week","lvl":3,"i":5,"seen":0},{"content":"The Rest","slug":"the-rest","lvl":3,"i":6,"seen":0},{"content":"The Retrospective","slug":"the-retrospective","lvl":3,"i":7,"seen":0},{"content":"Some Other Thoughts","slug":"some-other-thoughts","lvl":3,"i":8,"seen":0}],"highest":3,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,2],"level":0},{"type":"inline","content":"Next week is Supabase Launch Week. It's our third Launch Week this year, and is a key part of a Product-Led Growth strategy that has enabled us to increase the number of databases we manage 47% month-on-month for the last 18 months.","level":1,"lines":[1,2],"children":[{"type":"text","content":"Next week is Supabase Launch Week. It's our third Launch Week this year, and is a key part of a Product-Led Growth strategy that has enabled us to increase the number of databases we manage 47% month-on-month for the last 18 months.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[3,4],"level":0},{"type":"inline","content":"We often get asked about how we're able to ship so relentlessly, and being an open source company we thought it appropriate to start \"open sourcing\" some of our methods around building and shipping. Our user base is constructed of companies and individuals who themselves are building for the web and marketing to enormous audiences. Hopefully they can learn some of our tricks and in turn contribute back to the community with their own launch strategies and tactics, helping us continuously learn and improve.","level":1,"lines":[3,4],"children":[{"type":"text","content":"We often get asked about how we're able to ship so relentlessly, and being an open source company we thought it appropriate to start \"open sourcing\" some of our methods around building and shipping. Our user base is constructed of companies and individuals who themselves are building for the web and marketing to enormous audiences. Hopefully they can learn some of our tricks and in turn contribute back to the community with their own launch strategies and tactics, helping us continuously learn and improve.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[5,6],"level":0},{"type":"inline","content":"Before I go into the nuts and bolts of what Launch Week is, and the exact processes we follow when executing one, it's probably useful if I explain how we landed on this methodology in the first place.","level":1,"lines":[5,6],"children":[{"type":"text","content":"Before I go into the nuts and bolts of what Launch Week is, and the exact processes we follow when executing one, it's probably useful if I explain how we landed on this methodology in the first place.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[7,8],"level":0},{"type":"inline","content":"![supabase monthly growth](/images/blog/how-we-launch/total-databases-launched.jpg)","level":1,"lines":[7,8],"children":[{"type":"image","src":"/images/blog/how-we-launch/total-databases-launched.jpg","title":"","alt":"supabase monthly growth","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[9,10],"level":0},{"type":"inline","content":"[A brief history of growth at Supabase](#a-brief-history-of-growth-at-supabase)","level":1,"lines":[9,10],"children":[{"type":"text","content":"A brief history of growth at Supabase","level":0}],"lvl":3,"i":0,"seen":0,"slug":"a-brief-history-of-growth-at-supabase"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[11,12],"level":0},{"type":"inline","content":"We started work on Supabase in January 2020. Our growth plan from the start included being accepted into YCombinator. Dev Tool companies who enter YC gain a huge advantage from day one. You're placed in a cohort of hundreds of other early-stage startups, the majority of whom are developing software, and have just been given a bunch of funding, making them ideal early customers! This, however, turned out to be only a small part of what YC ultimately gifted us.","level":1,"lines":[11,12],"children":[{"type":"text","content":"We started work on Supabase in January 2020. Our growth plan from the start included being accepted into YCombinator. Dev Tool companies who enter YC gain a huge advantage from day one. You're placed in a cohort of hundreds of other early-stage startups, the majority of whom are developing software, and have just been given a bunch of funding, making them ideal early customers! This, however, turned out to be only a small part of what YC ultimately gifted us.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[13,14],"level":0},{"type":"inline","content":"At the time we joined the batch, we had around 80 alpha users of varying levels of activeness. Our plan was to spend the batch iterating the product around these early alpha users, and to do a big public launch a week or so before Demo Day (an event where you pitch to a shiver of investors). Our plan was foiled however, when an early user shared our site on Hacker News. The post stayed on the home page for several days, and ended up being one of the most upvoted dev tools launches ever (second only to Stripe!). Overnight, the number of databases we were hosting increased ten-fold. Bugs, breakages and feature requests flooded in from every angle; a fantastic problem to have for a fledgling startup. Over the course of the next 2 months we worked hard on a few major items, including Supabase Auth, and migrating our entire stack between cloud providers, along with hundreds of other smaller fixes and incremental improvements - both to our product, and to our company.","level":1,"lines":[13,14],"children":[{"type":"text","content":"At the time we joined the batch, we had around 80 alpha users of varying levels of activeness. Our plan was to spend the batch iterating the product around these early alpha users, and to do a big public launch a week or so before Demo Day (an event where you pitch to a shiver of investors). Our plan was foiled however, when an early user shared our site on Hacker News. The post stayed on the home page for several days, and ended up being one of the most upvoted dev tools launches ever (second only to Stripe!). Overnight, the number of databases we were hosting increased ten-fold. Bugs, breakages and feature requests flooded in from every angle; a fantastic problem to have for a fledgling startup. Over the course of the next 2 months we worked hard on a few major items, including Supabase Auth, and migrating our entire stack between cloud providers, along with hundreds of other smaller fixes and incremental improvements - both to our product, and to our company.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[15,16],"level":0},{"type":"inline","content":"A few weeks before Demo Day, we announced Auth, and shouted about it as loudly as possible. We posted it everywhere, and recruited our recently onboarded squad of [Technical Angel Investors](https://supabase.com/company#investors) to help us boost our message on social media. The response was solid, and led to an increase in the rate of user acquisition, and activation/retention rates.","level":1,"lines":[15,16],"children":[{"type":"text","content":"A few weeks before Demo Day, we announced Auth, and shouted about it as loudly as possible. We posted it everywhere, and recruited our recently onboarded squad of ","level":0},{"type":"link_open","href":"https://supabase.com/company#investors","title":"","level":0},{"type":"text","content":"Technical Angel Investors","level":1},{"type":"link_close","level":0},{"type":"text","content":" to help us boost our message on social media. The response was solid, and led to an increase in the rate of user acquisition, and activation/retention rates.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[17,18],"level":0},{"type":"inline","content":"Post YC can often be a sink or swim moment for companies. Throughout the batch, you have regular check-ins with world-class operators, your batch mates, and inspirational talks from founders several steps ahead. When you leave, the external pressure drops, and it's up to you to put the frameworks in place to maintain your rate of progress. You no longer have the likes of Michael Siebel asking you why it's taking you so long to get Auth out the door and in the hands of users. When faced with this problem we sat down and said, \"why don't we just pretend that we're starting the batch again, and do our best to recreate the conditions of an accelerator internally, complete with our very own Demo Day?\". We found the actual YC Demo Day is kind of an arbitrary deadline useful for motivating all kinds of internal initiatives, whether it's fundraising, growth, or product. So we said to the team, let's just choose an arbitrary date 3 months from now, and we'll ship _something_ huge.","level":1,"lines":[17,18],"children":[{"type":"text","content":"Post YC can often be a sink or swim moment for companies. Throughout the batch, you have regular check-ins with world-class operators, your batch mates, and inspirational talks from founders several steps ahead. When you leave, the external pressure drops, and it's up to you to put the frameworks in place to maintain your rate of progress. You no longer have the likes of Michael Siebel asking you why it's taking you so long to get Auth out the door and in the hands of users. When faced with this problem we sat down and said, \"why don't we just pretend that we're starting the batch again, and do our best to recreate the conditions of an accelerator internally, complete with our very own Demo Day?\". We found the actual YC Demo Day is kind of an arbitrary deadline useful for motivating all kinds of internal initiatives, whether it's fundraising, growth, or product. So we said to the team, let's just choose an arbitrary date 3 months from now, and we'll ship ","level":0},{"type":"em_open","level":0},{"type":"text","content":"something","level":1},{"type":"em_close","level":0},{"type":"text","content":" huge.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[19,20],"level":0},{"type":"inline","content":"We went the whole hog. We had kick-off events, we invited external people to come in and talk to the team about their origin stories, and even had custom swag made up specific to this product cycle. The _something_, turned out to be [Supabase Beta](https://supabase.com/beta), an announcement that marked a huge advancement in the Stability, Performance, and Security of our hosted platform. This announcement resulted in a stark increase in the rate of developer sign-ups and activation. Our \"fixed timeline, flexible scope\" approach to launching products had proved successful with the team. And what's more, the build-up to launch, and the launch itself, was some of the most fun any of us had had. Our small team was flying high on adrenaline, and everyone could see for themselves the immediate and direct impact their work had on Supabase's viral adoption.","level":1,"lines":[19,20],"children":[{"type":"text","content":"We went the whole hog. We had kick-off events, we invited external people to come in and talk to the team about their origin stories, and even had custom swag made up specific to this product cycle. The ","level":0},{"type":"em_open","level":0},{"type":"text","content":"something","level":1},{"type":"em_close","level":0},{"type":"text","content":", turned out to be ","level":0},{"type":"link_open","href":"https://supabase.com/beta","title":"","level":0},{"type":"text","content":"Supabase Beta","level":1},{"type":"link_close","level":0},{"type":"text","content":", an announcement that marked a huge advancement in the Stability, Performance, and Security of our hosted platform. This announcement resulted in a stark increase in the rate of developer sign-ups and activation. Our \"fixed timeline, flexible scope\" approach to launching products had proved successful with the team. And what's more, the build-up to launch, and the launch itself, was some of the most fun any of us had had. Our small team was flying high on adrenaline, and everyone could see for themselves the immediate and direct impact their work had on Supabase's viral adoption.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[21,22],"level":0},{"type":"inline","content":"The very next week, still high on adrenaline, we met for a retrospective. The debrief, however, quickly turned into a planning meeting, and the question was raised \"what is the MOST ambitious thing we could hope to ship 3 months from now?\". A few projects got kicked about until someone suggested, \"why just have one launch? Why can't we aim to ship one major feature or announcement every day for a week?\". It was quickly agreed that Launch Day would instead become Launch Week.","level":1,"lines":[21,22],"children":[{"type":"text","content":"The very next week, still high on adrenaline, we met for a retrospective. The debrief, however, quickly turned into a planning meeting, and the question was raised \"what is the MOST ambitious thing we could hope to ship 3 months from now?\". A few projects got kicked about until someone suggested, \"why just have one launch? Why can't we aim to ship one major feature or announcement every day for a week?\". It was quickly agreed that Launch Day would instead become Launch Week.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[23,24],"level":0},{"type":"inline","content":"During the last week of March 2021, we went on to ship [7 major features](/blog/launch-week#friday-one-more-thing). We once again saw an immediate uptick in growth rate which helped solidify our strategy.","level":1,"lines":[23,24],"children":[{"type":"text","content":"During the last week of March 2021, we went on to ship ","level":0},{"type":"link_open","href":"/blog/launch-week#friday-one-more-thing","title":"","level":0},{"type":"text","content":"7 major features","level":1},{"type":"link_close","level":0},{"type":"text","content":". We once again saw an immediate uptick in growth rate which helped solidify our strategy.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[25,26],"level":0},{"type":"inline","content":"More recently we ran \"[Launch Week II: The SQL](/blog/supabase-launch-week-sql)\", where we bumped the versions of several key products in our stack, and added a [Community Day](https://supabase.com/blog/supabase-community-day) with the aim of boosting visibility and support of various open source tools and projects that we rely on, along with products, frameworks, and tooling built by the rapidly expanding [Supabase Community](https://github.com/supabase-community/).","level":1,"lines":[25,26],"children":[{"type":"text","content":"More recently we ran \"","level":0},{"type":"link_open","href":"/blog/supabase-launch-week-sql","title":"","level":0},{"type":"text","content":"Launch Week II: The SQL","level":1},{"type":"link_close","level":0},{"type":"text","content":"\", where we bumped the versions of several key products in our stack, and added a ","level":0},{"type":"link_open","href":"https://supabase.com/blog/supabase-community-day","title":"","level":0},{"type":"text","content":"Community Day","level":1},{"type":"link_close","level":0},{"type":"text","content":" with the aim of boosting visibility and support of various open source tools and projects that we rely on, along with products, frameworks, and tooling built by the rapidly expanding ","level":0},{"type":"link_open","href":"https://github.com/supabase-community/","title":"","level":0},{"type":"text","content":"Supabase Community","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[27,28],"level":0},{"type":"inline","content":"We're now just a few days away from Launch Week III, and over time have incrementally improved upon many of the processes and tactics that help us maximize output whilst minimizing stress on the team. So let's get into some specifics about how we run a product cycle and the resulting Launch Week.","level":1,"lines":[27,28],"children":[{"type":"text","content":"We're now just a few days away from Launch Week III, and over time have incrementally improved upon many of the processes and tactics that help us maximize output whilst minimizing stress on the team. So let's get into some specifics about how we run a product cycle and the resulting Launch Week.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[29,30],"level":0},{"type":"inline","content":"[The Planning Meeting](#the-planning-meeting)","level":1,"lines":[29,30],"children":[{"type":"text","content":"The Planning Meeting","level":0}],"lvl":3,"i":1,"seen":0,"slug":"the-planning-meeting"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[31,32],"level":0},{"type":"inline","content":"Spanning 12 different countries makes it challenging to find a time that works for everyone, but gathering together online is the first step.","level":1,"lines":[31,32],"children":[{"type":"text","content":"Spanning 12 different countries makes it challenging to find a time that works for everyone, but gathering together online is the first step.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[33,34],"level":0},{"type":"inline","content":"We always start the meeting by firing up a Google Jam Board and collectively constructing \"The Supabase Universe\", this is a giant brain dump of terms and concepts related to Supabase. What we're building, who it's for, who we're trying to hire, what our goals and metrics are. Literally everything that's anything to do with Supabase goes down on this virtual whiteboard. The purpose is to ensure everyone gets out of their daily mode of operation and loads up the business as a whole into their brains. Developers should be loading up marketing concepts, and the marketers should be thinking about engineering. If we're overly focused on one particular area (often engineering), then we risk setting a course in the wrong direction for the entire quarter. It's also a method of getting all the \"obvious\" stuff down on paper so you can move on and focus the creative efforts of everyone on new ideas; a technique borrowed from the creative industries.","level":1,"lines":[33,34],"children":[{"type":"text","content":"We always start the meeting by firing up a Google Jam Board and collectively constructing \"The Supabase Universe\", this is a giant brain dump of terms and concepts related to Supabase. What we're building, who it's for, who we're trying to hire, what our goals and metrics are. Literally everything that's anything to do with Supabase goes down on this virtual whiteboard. The purpose is to ensure everyone gets out of their daily mode of operation and loads up the business as a whole into their brains. Developers should be loading up marketing concepts, and the marketers should be thinking about engineering. If we're overly focused on one particular area (often engineering), then we risk setting a course in the wrong direction for the entire quarter. It's also a method of getting all the \"obvious\" stuff down on paper so you can move on and focus the creative efforts of everyone on new ideas; a technique borrowed from the creative industries.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[35,36],"level":0},{"type":"inline","content":"![supabase monthly growth](/images/blog/how-we-launch/supabase-universe.jpg)","level":1,"lines":[35,36],"children":[{"type":"image","src":"/images/blog/how-we-launch/supabase-universe.jpg","title":"","alt":"supabase monthly growth","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[37,38],"level":0},{"type":"inline","content":"Once everyone is warmed up, we dive into the high-level goals and metrics. Are our metrics still relevant? And if so, which ones should we be aiming to boost and why? The more direct a connection between actions and metrics the better, so it's important to remind everyone of what the north star is before we start scheming on how to reach it.","level":1,"lines":[37,38],"children":[{"type":"text","content":"Once everyone is warmed up, we dive into the high-level goals and metrics. Are our metrics still relevant? And if so, which ones should we be aiming to boost and why? The more direct a connection between actions and metrics the better, so it's important to remind everyone of what the north star is before we start scheming on how to reach it.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[39,40],"level":0},{"type":"inline","content":"Then comes the main course - what are we going to build or do that's going to move the needle? List down as many ideas as possible, and go through a process of sorting and filtering as you go. Try not to go too deep on any particular idea, in-depth analysis and exploration can be done later in break out sessions, make sure you have someone responsible for pulling you out of these rabbit holes if the team starts to get drawn in to too much detail.","level":1,"lines":[39,40],"children":[{"type":"text","content":"Then comes the main course - what are we going to build or do that's going to move the needle? List down as many ideas as possible, and go through a process of sorting and filtering as you go. Try not to go too deep on any particular idea, in-depth analysis and exploration can be done later in break out sessions, make sure you have someone responsible for pulling you out of these rabbit holes if the team starts to get drawn in to too much detail.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[41,42],"level":0},{"type":"inline","content":"For us, an essential part of this process is the idea that the scope is flexible - at this stage of ideation it's often impossible to tell whether an engineering project is going to take 2 or 102 days. We will always try our best to have it ready for Launch Week, but if not no worries, we'll shout about something else, and include it in the next one.","level":1,"lines":[41,42],"children":[{"type":"text","content":"For us, an essential part of this process is the idea that the scope is flexible - at this stage of ideation it's often impossible to tell whether an engineering project is going to take 2 or 102 days. We will always try our best to have it ready for Launch Week, but if not no worries, we'll shout about something else, and include it in the next one.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[43,44],"level":0},{"type":"inline","content":"Lastly we will give some discussion to hiring and any other business, and schedule any follow-up meetings or breakouts required.","level":1,"lines":[43,44],"children":[{"type":"text","content":"Lastly we will give some discussion to hiring and any other business, and schedule any follow-up meetings or breakouts required.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[45,46],"level":0},{"type":"inline","content":"[The Kick Off Meeting](#the-kick-off-meeting)","level":1,"lines":[45,46],"children":[{"type":"text","content":"The Kick Off Meeting","level":0}],"lvl":3,"i":2,"seen":0,"slug":"the-kick-off-meeting"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[47,48],"level":0},{"type":"inline","content":"After the breakouts we typically have a much clearer idea of what we want to aim for, and if the projects themselves are realistic. Our list at this point often sits around 12 items in length. Some projects will drop out and others will be introduced during the course of the next few months, which is ok. Each project typically has a lead assigned, and another couple of people will register their interest in helping out.","level":1,"lines":[47,48],"children":[{"type":"text","content":"After the breakouts we typically have a much clearer idea of what we want to aim for, and if the projects themselves are realistic. Our list at this point often sits around 12 items in length. Some projects will drop out and others will be introduced during the course of the next few months, which is ok. Each project typically has a lead assigned, and another couple of people will register their interest in helping out.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[49,50],"level":0},{"type":"inline","content":"For the kick-off, I always try and come up with some grotesquely garish branding (this is an internal project after all) and find some loose theme to roll with. The purpose of the meeting is to remind everyone of the goals, the timeline, and the current list of announcements we plan to make during the launch.","level":1,"lines":[49,50],"children":[{"type":"text","content":"For the kick-off, I always try and come up with some grotesquely garish branding (this is an internal project after all) and find some loose theme to roll with. The purpose of the meeting is to remind everyone of the goals, the timeline, and the current list of announcements we plan to make during the launch.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[51,52],"level":0},{"type":"inline","content":"![supabase monthly growth](/images/blog/how-we-launch/how-we-launch-stickers.jpg)","level":1,"lines":[51,52],"children":[{"type":"image","src":"/images/blog/how-we-launch/how-we-launch-stickers.jpg","title":"","alt":"supabase monthly growth","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[53,54],"level":0},{"type":"inline","content":"[Time to Work](#time-to-work)","level":1,"lines":[53,54],"children":[{"type":"text","content":"Time to Work","level":0}],"lvl":3,"i":3,"seen":0,"slug":"time-to-work"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[55,56],"level":0},{"type":"inline","content":"Then it's time to get our heads down. One important point to make here is that the 3-month cycle doesn't necessarily replace more traditional project management methodologies. We leave it up to the project leads to decide how to run their project. Some teams opt for kanban boards, and stand-ups. Other projects run like open source repos, with RFCs, issue management, and fully async comms.","level":1,"lines":[55,56],"children":[{"type":"text","content":"Then it's time to get our heads down. One important point to make here is that the 3-month cycle doesn't necessarily replace more traditional project management methodologies. We leave it up to the project leads to decide how to run their project. Some teams opt for kanban boards, and stand-ups. Other projects run like open source repos, with RFCs, issue management, and fully async comms.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[57,58],"level":0},{"type":"inline","content":"Another key thing to mention here is that we very rarely hold back features until Launch Week. Actually, quite the opposite is true, we encourage everyone to ship features as early as possible. We announce features as they ship in our monthly Beta Update blog and newsletter - which is often limited to a blast radius containing existing users. Full write-ups, marketing copy, and broad Top-of-Funnel type marketing efforts will often happen during Launch Week to hype up these features. Actually, we've found that you can launch a new feature many times over and always manage to reach people who either forgot, ignored, or just plain missed it the first few times. We're so close to our own projects that we often overestimate the reach of our online marketing efforts.","level":1,"lines":[57,58],"children":[{"type":"text","content":"Another key thing to mention here is that we very rarely hold back features until Launch Week. Actually, quite the opposite is true, we encourage everyone to ship features as early as possible. We announce features as they ship in our monthly Beta Update blog and newsletter - which is often limited to a blast radius containing existing users. Full write-ups, marketing copy, and broad Top-of-Funnel type marketing efforts will often happen during Launch Week to hype up these features. Actually, we've found that you can launch a new feature many times over and always manage to reach people who either forgot, ignored, or just plain missed it the first few times. We're so close to our own projects that we often overestimate the reach of our online marketing efforts.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[59,60],"level":0},{"type":"inline","content":"In the weeks leading up to Launch Week, we review more closely the list of items we want to Launch and start to plan for what supporting materials we'll need. There are some things such as press, which needs to be organized up to 3 weeks before you plan to Launch, so you better get started early on those items. Also being aware of which third parties are going to be involved is important. Maybe we're shipping an integration with some other tool or company, cross-company communication can be a blocker, so better to start early.","level":1,"lines":[59,60],"children":[{"type":"text","content":"In the weeks leading up to Launch Week, we review more closely the list of items we want to Launch and start to plan for what supporting materials we'll need. There are some things such as press, which needs to be organized up to 3 weeks before you plan to Launch, so you better get started early on those items. Also being aware of which third parties are going to be involved is important. Maybe we're shipping an integration with some other tool or company, cross-company communication can be a blocker, so better to start early.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[61,62],"level":0},{"type":"inline","content":"![supabase monthly growth](/images/blog/how-we-launch/how-we-launch-timeline.jpg)","level":1,"lines":[61,62],"children":[{"type":"image","src":"/images/blog/how-we-launch/how-we-launch-timeline.jpg","title":"","alt":"supabase monthly growth","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[63,64],"level":0},{"type":"inline","content":"[Pre-Launch Week](#pre-launch-week)","level":1,"lines":[63,64],"children":[{"type":"text","content":"Pre-Launch Week","level":0}],"lvl":3,"i":4,"seen":0,"slug":"pre-launch-week"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[65,66],"level":0},{"type":"inline","content":"One of our learnings from previous Launch Weeks was to no longer ship to prod on the day of the Launch itself :). We had a blast live-debugging prod issues on Discord last time, but this was not so conducive to getting enough sleep. Our Launch Weeks have become a marathon, so it's ideal to get all the major integrations done a week early. Also writing the content and getting someone to edit can take a couple of iterations. This all seems obvious written down here, but it took us a few iterations to start being more regimented.","level":1,"lines":[65,66],"children":[{"type":"text","content":"One of our learnings from previous Launch Weeks was to no longer ship to prod on the day of the Launch itself :). We had a blast live-debugging prod issues on Discord last time, but this was not so conducive to getting enough sleep. Our Launch Weeks have become a marathon, so it's ideal to get all the major integrations done a week early. Also writing the content and getting someone to edit can take a couple of iterations. This all seems obvious written down here, but it took us a few iterations to start being more regimented.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[67,68],"level":0},{"type":"inline","content":"We have more regular check-ins throughout this week, and make sure everyone is on the same page. We have a process of \"swarming\" around sticky issues and blockers and a good amount of testing will be done by people who were not involved in the development of a given feature. Particularly around DX, which is an extremely high priority topic at Supabase.","level":1,"lines":[67,68],"children":[{"type":"text","content":"We have more regular check-ins throughout this week, and make sure everyone is on the same page. We have a process of \"swarming\" around sticky issues and blockers and a good amount of testing will be done by people who were not involved in the development of a given feature. Particularly around DX, which is an extremely high priority topic at Supabase.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[69,70],"level":0},{"type":"inline","content":"[Launch Week](#launch-week)","level":1,"lines":[69,70],"children":[{"type":"text","content":"Launch Week","level":0}],"lvl":3,"i":5,"seen":0,"slug":"launch-week"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[71,72],"level":0},{"type":"inline","content":"I can't think of many things more fun than executing on a Supabase Launch Week. It's the culmination of 3 months of epic work from a highly skilled team of folks distributed across every corner of the planet. The awesome thing is that, with most of the features we ship, the person who implemented the code will be the same person who writes the marketing content. This means that the content usually includes deep technical discussion. They're also probably the best person to decide which will be the most effective channels to market that particular feature. As an example, when we launched [ui.supabase.com](http://ui.supabase.com), we made sure to hit up all the front end and design channels, made sure it ranked on Product Hunt, and reached out to some friendly designers to help boost our message. This is very different from the approach we took to marketing our [file storage offering](https://supabase.com/blog/supabase-storage). The technical design discussion of Storage is better suited to channels like Hacker News, and technical conference audiences who may be more interested in DX than UI components.","level":1,"lines":[71,72],"children":[{"type":"text","content":"I can't think of many things more fun than executing on a Supabase Launch Week. It's the culmination of 3 months of epic work from a highly skilled team of folks distributed across every corner of the planet. The awesome thing is that, with most of the features we ship, the person who implemented the code will be the same person who writes the marketing content. This means that the content usually includes deep technical discussion. They're also probably the best person to decide which will be the most effective channels to market that particular feature. As an example, when we launched ","level":0},{"type":"link_open","href":"http://ui.supabase.com","title":"","level":0},{"type":"text","content":"ui.supabase.com","level":1},{"type":"link_close","level":0},{"type":"text","content":", we made sure to hit up all the front end and design channels, made sure it ranked on Product Hunt, and reached out to some friendly designers to help boost our message. This is very different from the approach we took to marketing our ","level":0},{"type":"link_open","href":"https://supabase.com/blog/supabase-storage","title":"","level":0},{"type":"text","content":"file storage offering","level":1},{"type":"link_close","level":0},{"type":"text","content":". The technical design discussion of Storage is better suited to channels like Hacker News, and technical conference audiences who may be more interested in DX than UI components.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[73,74],"level":0},{"type":"inline","content":"When it comes to the day of each launch we've gotten into the habit of constructing a finely detailed schedule to follow. It will contain items such as:","level":1,"lines":[73,74],"children":[{"type":"text","content":"When it comes to the day of each launch we've gotten into the habit of constructing a finely detailed schedule to follow. It will contain items such as:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[75,82],"level":0},{"type":"list_item_open","lines":[75,76],"level":1},{"type":"paragraph_open","tight":true,"lines":[75,76],"level":2},{"type":"inline","content":"7:30am Twitter spaces reminder tweet","level":3,"lines":[75,76],"children":[{"type":"text","content":"7:30am Twitter spaces reminder tweet","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[76,77],"level":1},{"type":"paragraph_open","tight":true,"lines":[76,77],"level":2},{"type":"inline","content":"7:55am Product Hunt goes live","level":3,"lines":[76,77],"children":[{"type":"text","content":"7:55am Product Hunt goes live","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[77,78],"level":1},{"type":"paragraph_open","tight":true,"lines":[77,78],"level":2},{"type":"inline","content":"8:00am Blog post goes live","level":3,"lines":[77,78],"children":[{"type":"text","content":"8:00am Blog post goes live","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[78,79],"level":1},{"type":"paragraph_open","tight":true,"lines":[78,79],"level":2},{"type":"inline","content":"8:05am Post launch tweet 1","level":3,"lines":[78,79],"children":[{"type":"text","content":"8:05am Post launch tweet 1","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[79,80],"level":1},{"type":"paragraph_open","tight":true,"lines":[79,80],"level":2},{"type":"inline","content":"8:10am Share tweet with Angels","level":3,"lines":[79,80],"children":[{"type":"text","content":"8:10am Share tweet with Angels","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[80,82],"level":1},{"type":"paragraph_open","tight":true,"lines":[80,81],"level":2},{"type":"inline","content":"8:15am Twitter spaces go live","level":3,"lines":[80,81],"children":[{"type":"text","content":"8:15am Twitter spaces go live","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[82,83],"level":0},{"type":"inline","content":"Of course, it's possible to run this all without such a fine-grained schedule, but the launches can get kind of hectic, so it's easier to not have to think - just do.","level":1,"lines":[82,83],"children":[{"type":"text","content":"Of course, it's possible to run this all without such a fine-grained schedule, but the launches can get kind of hectic, so it's easier to not have to think - just do.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[84,85],"level":0},{"type":"inline","content":"[The Rest](#the-rest)","level":1,"lines":[84,85],"children":[{"type":"text","content":"The Rest","level":0}],"lvl":3,"i":6,"seen":0,"slug":"the-rest"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[86,87],"level":0},{"type":"inline","content":"Despite being a ton of fun, it's inevitable that some team members will have pulled long days and nights to get their feature out the door, as well as pumping it on as many marketing channels as possible. Launching new things also includes managing support requests, bug fixes, responding to questions, and even managing PRs from the community excited to contribute to new repos or areas of the codebase. This is unsustainable without proper rest so each cycle ends with a good amount of downtime and maintenance mode. Having said that, the adrenaline of Launch Week often spills over into the following days and weeks, so it's not uncommon for members of the team to continue in Launch Mode for a while before taking some downtime.","level":1,"lines":[86,87],"children":[{"type":"text","content":"Despite being a ton of fun, it's inevitable that some team members will have pulled long days and nights to get their feature out the door, as well as pumping it on as many marketing channels as possible. Launching new things also includes managing support requests, bug fixes, responding to questions, and even managing PRs from the community excited to contribute to new repos or areas of the codebase. This is unsustainable without proper rest so each cycle ends with a good amount of downtime and maintenance mode. Having said that, the adrenaline of Launch Week often spills over into the following days and weeks, so it's not uncommon for members of the team to continue in Launch Mode for a while before taking some downtime.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[88,89],"level":0},{"type":"inline","content":"[The Retrospective](#the-retrospective)","level":1,"lines":[88,89],"children":[{"type":"text","content":"The Retrospective","level":0}],"lvl":3,"i":7,"seen":0,"slug":"the-retrospective"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[90,91],"level":0},{"type":"inline","content":"Once the dust has settled, the **most** **important** phase of the entire process can begin. To skip the retro would be to rubbish the entire process, as we would never learn from mistakes or adapt to capitalize on new tactics discovered throughout the preceding weeks and months. We always make sure to cover what went well, what went badly, and what can be improved. This list is then read aloud again at the start of the next planning meeting. The process as it exists today is the product of 4 previous iterations, and is still far from its final form. Amongst other things, our team is growing fast, so it's yet to be seen if the same processes will extend to so many team members, and in which ways they will have to adapt. Lastly, we always make sure to discuss the question \"Is Launch Week still relevant?\", and \"How would we know if we were approaching some local maxima in terms of process and productive output?\".","level":1,"lines":[90,91],"children":[{"type":"text","content":"Once the dust has settled, the ","level":0},{"type":"strong_open","level":0},{"type":"text","content":"most","level":1},{"type":"strong_close","level":0},{"type":"text","content":" ","level":0},{"type":"strong_open","level":0},{"type":"text","content":"important","level":1},{"type":"strong_close","level":0},{"type":"text","content":" phase of the entire process can begin. To skip the retro would be to rubbish the entire process, as we would never learn from mistakes or adapt to capitalize on new tactics discovered throughout the preceding weeks and months. We always make sure to cover what went well, what went badly, and what can be improved. This list is then read aloud again at the start of the next planning meeting. The process as it exists today is the product of 4 previous iterations, and is still far from its final form. Amongst other things, our team is growing fast, so it's yet to be seen if the same processes will extend to so many team members, and in which ways they will have to adapt. Lastly, we always make sure to discuss the question \"Is Launch Week still relevant?\", and \"How would we know if we were approaching some local maxima in terms of process and productive output?\".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[92,93],"level":0},{"type":"inline","content":"![supabase monthly growth](/images/blog/how-we-launch/how-we-launch-retrospective.jpg)","level":1,"lines":[92,93],"children":[{"type":"image","src":"/images/blog/how-we-launch/how-we-launch-retrospective.jpg","title":"","alt":"supabase monthly growth","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[94,95],"level":0},{"type":"inline","content":"[Some Other Thoughts](#some-other-thoughts)","level":1,"lines":[94,95],"children":[{"type":"text","content":"Some Other Thoughts","level":0}],"lvl":3,"i":8,"seen":0,"slug":"some-other-thoughts"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[96,97],"level":0},{"type":"inline","content":"This \"internal accelerator\" model has worked well within Supabase as a 2-person team and is still proving effective now at 30, but there's no telling whether it would work as well in other organizations. I would be very keen to learn from those who also do something similar.","level":1,"lines":[96,97],"children":[{"type":"text","content":"This \"internal accelerator\" model has worked well within Supabase as a 2-person team and is still proving effective now at 30, but there's no telling whether it would work as well in other organizations. I would be very keen to learn from those who also do something similar.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[98,99],"level":0},{"type":"inline","content":"In some ways, running Supabase is like launching many startups at the same time, we have to build and maintain many different parallel projects in order to match Firebase's incredible suite of products, which could be one reason why it lends itself so well to this acceleration model. And, in fact, being a Firebase alternative is only a small part of [what we want to achieve with Supabase](https://supabase.com/blog/supabase-series-a), so we have many, many more initiatives to run as we grow.","level":1,"lines":[98,99],"children":[{"type":"text","content":"In some ways, running Supabase is like launching many startups at the same time, we have to build and maintain many different parallel projects in order to match Firebase's incredible suite of products, which could be one reason why it lends itself so well to this acceleration model. And, in fact, being a Firebase alternative is only a small part of ","level":0},{"type":"link_open","href":"https://supabase.com/blog/supabase-series-a","title":"","level":0},{"type":"text","content":"what we want to achieve with Supabase","level":1},{"type":"link_close","level":0},{"type":"text","content":", so we have many, many more initiatives to run as we grow.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[100,101],"level":0},{"type":"inline","content":"Running an accelerator model may also be why we've had success [hiring so many other founders](https://about.supabase.com/careers/founders). We're able to give a high degree of autonomy to individuals when it comes to managing projects and initiatives. If this kind of work environment sounds interesting to you, we have tons of [open roles](https://about.supabase.com/careers). We've already hired people who joined because they \"couldn't stand to watch another Launch Week from the sidelines\" and team members often quote experiencing their first Launch Week as one of the highlights of their Supabase journey so far. It clearly has benefits that extend beyond just growth and user activation.","level":1,"lines":[100,101],"children":[{"type":"text","content":"Running an accelerator model may also be why we've had success ","level":0},{"type":"link_open","href":"https://about.supabase.com/careers/founders","title":"","level":0},{"type":"text","content":"hiring so many other founders","level":1},{"type":"link_close","level":0},{"type":"text","content":". We're able to give a high degree of autonomy to individuals when it comes to managing projects and initiatives. If this kind of work environment sounds interesting to you, we have tons of ","level":0},{"type":"link_open","href":"https://about.supabase.com/careers","title":"","level":0},{"type":"text","content":"open roles","level":1},{"type":"link_close","level":0},{"type":"text","content":". We've already hired people who joined because they \"couldn't stand to watch another Launch Week from the sidelines\" and team members often quote experiencing their first Launch Week as one of the highlights of their Supabase journey so far. It clearly has benefits that extend beyond just growth and user activation.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[102,103],"level":0},{"type":"inline","content":"Make sure you follow us on [Twitter](https://twitter.com/supabase), join our [Discord](https://discord.supabase.com), or [sign up for Supabase](https://supabase.com/dashboard) in order to keep up to date on all things Supabase.","level":1,"lines":[102,103],"children":[{"type":"text","content":"Make sure you follow us on ","level":0},{"type":"link_open","href":"https://twitter.com/supabase","title":"","level":0},{"type":"text","content":"Twitter","level":1},{"type":"link_close","level":0},{"type":"text","content":", join our ","level":0},{"type":"link_open","href":"https://discord.supabase.com","title":"","level":0},{"type":"text","content":"Discord","level":1},{"type":"link_close","level":0},{"type":"text","content":", or ","level":0},{"type":"link_open","href":"https://supabase.com/dashboard","title":"","level":0},{"type":"text","content":"sign up for Supabase","level":1},{"type":"link_close","level":0},{"type":"text","content":" in order to keep up to date on all things Supabase.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[104,105],"level":0},{"type":"inline","content":"Supabase is the Open Source Firebase Alternative.","level":1,"lines":[104,105],"children":[{"type":"text","content":"Supabase is the Open Source Firebase Alternative.","level":0}]},{"type":"paragraph_close","tight":false,"level":0}],"content":"- [A brief history of growth at Supabase](#a-brief-history-of-growth-at-supabase)\n- [The Planning Meeting](#the-planning-meeting)\n- [The Kick Off Meeting](#the-kick-off-meeting)\n- [Time to Work](#time-to-work)\n- [Pre-Launch Week](#pre-launch-week)\n- [Launch Week](#launch-week)\n- [The Rest](#the-rest)\n- [The Retrospective](#the-retrospective)\n- [Some Other Thoughts](#some-other-thoughts)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-how-we-launch"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>