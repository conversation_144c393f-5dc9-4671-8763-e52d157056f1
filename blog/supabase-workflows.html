<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Workflows are coming to Supabase</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Functions are great, but you know what&#x27;s better?" data-next-head=""/><meta property="og:title" content="Workflows are coming to Supabase" data-next-head=""/><meta property="og:description" content="Functions are great, but you know what&#x27;s better?" data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-workflows" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2021-04-02" data-next-head=""/><meta property="article:author" content="https://github.com/fracek" data-next-head=""/><meta property="article:tag" content="functions" data-next-head=""/><meta property="article:tag" content="workflows" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/workflows/workflows-og.jpg" data-next-head=""/><meta property="og:image:alt" content="Workflows are coming to Supabase thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Workflows are coming to Supabase</h1><div class="text-light flex space-x-3 text-sm"><p>02 Apr 2021</p><p>•</p><p>9 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/fracek"><div class="flex items-center gap-3"><div class="w-10"><img alt="Francesco Ceccon avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Ffracek.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Ffracek.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Ffracek.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Francesco Ceccon</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Workflows are coming to Supabase" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fworkflows%2Fworkflows-thumb.jpg&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fworkflows%2Fworkflows-thumb.jpg&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fworkflows%2Fworkflows-thumb.jpg&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fworkflows%2Fworkflows-thumb.jpg&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fworkflows%2Fworkflows-thumb.jpg&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fworkflows%2Fworkflows-thumb.jpg&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fworkflows%2Fworkflows-thumb.jpg&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fworkflows%2Fworkflows-thumb.jpg&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fworkflows%2Fworkflows-thumb.jpg&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fworkflows%2Fworkflows-thumb.jpg&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fworkflows%2Fworkflows-thumb.jpg&amp;w=3840&amp;q=100"/></div><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p>This week we <a href="https://supabase.com/blog/supabase-storage">launched Supabase Storage</a>, which leaves one other huge piece of the
stack that everyone is asking for: Functions.</p>
<h2 id="tldr" class="group scroll-mt-24">TLDR<a href="#tldr" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We&#x27;re not releasing Functions today. Trust us, we know you want them. They are coming, just not today.</p>
<p>But we are building something that we think you&#x27;re going to like: Workflows. We haven&#x27;t finished building it yet, but Workflows are
a &quot;piece&quot; of the Function story and arguably an even more exciting feature.</p>
<p></p>
<h2 id="firebase-functions" class="group scroll-mt-24">Firebase Functions<a href="#firebase-functions" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Firebase functions are relatively simple. If you use Serverless, AWS Lambda, Cloudflare Workers, Next.js API routes, or
Netlify Functions, then you know how they work. A Firebase function executes some code which you provide, without you managing a server.</p>
<p>Specifically for Firebase, they have another key feature - they can be triggered by database events. For example, you can
trigger a function whenever a Firestore Document is updated.</p>
<p>This is great, but it is still limited for a few real-world use cases. For example, what if you want to send an email to a user
one day after a user signs up. Or one year? There is no queuing functionality in Firebase. You&#x27;d have to manage a process like
this yourself, probably using a cron-job.</p>
<h2 id="a-better-solution" class="group scroll-mt-24">A better solution?<a href="#a-better-solution" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We searched for some open source tools which we think are solving this problem well. We looked at
<a href="https://supabase.com/blog/supabase-storage#designing-the-storage-middleware">NodeRed</a>, <a href="https://n8n.io/">n8n</a>,
<a href="http://airflow.apache.org/blog/airflow-two-point-oh-is-here/">Airflow</a>, and about 10 other tools. They are amazing tools on their
own, but for the Supabase Stack they ultimately had the
<a href="https://supabase.com/blog/supabase-storage#integration-with-the-supabase-ecosystem">same shortcomings</a> that we found
with Storage providers - most of them lacked deep Postgres integration.</p>
<p>We went back to the drawing board and asked, &quot;if we could wave a wand and get the perfect solution, what would it look like?&quot;.
The tool that came very close is <a href="https://aws.amazon.com/step-functions/">AWS Step Functions</a>. The only problem: it&#x27;s not open source.
Luckily, their <a href="https://states-language.net/spec.html">state language</a> is.</p>
<p>Using this states language, we are <a href="https://github.com/supabase/workflows">building an open source orchestration engine</a> for
managing very complex Workflows with queueing, etc. It will be built with Elixir.</p>
<p>This engine won&#x27;t execute code. Instead, it will coordinate and execute existing functions wherever they live: AWS, GCP, Azure,
OpenFaas, and of course Postgres.</p>
<p>We plan to add &quot;modules&quot; which work natively: email services, notification services, and other platforms.</p>
<p>The engine is deeply integrated with Postgres. <code class="short-inline-codeblock">Jobs</code>, <code class="short-inline-codeblock">queues</code>, and <code class="short-inline-codeblock">logs</code> will be stored and accessible by SQL. We&#x27;d like to give a shoutout to the <a href="https://getoban.pro">Oban</a> team here, their robust job processing was a big missing piece for the engine. And most importantly, it&#x27;s backed by Postgres!</p>
<p>Once ready, we will make this available in the Supabase Dashboard with a Zapier-like interface.</p>
<h2 id="what-are-workflows" class="group scroll-mt-24">What are Workflows<a href="#what-are-workflows" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Workflows orchestrate and execute functions in response to a database event (insert, update, delete) or a HTTP call (direct invocation).</p>
<p>You can use them to rapidly develop microservices (once we have functions) without worrying about servers.</p>
<p>Workflows are stateless - the output of a state becomes the input of the next state.</p>
<p>Workflows are defined using Amazon States Languages, so you can import your workflows from AWS (although we are still building handlers
for most AWS resources).</p>
<p>Workflows can be <em>persistent</em> (the default). This means they are tolerant to server restarts, but it also means they need to use
the database to store their state.</p>
<p>Workers can be <em>transient.</em> These are fully in-memory if you don&#x27;t want to store the execution state (for example, IoT
applications that trigger workflows very often). Transient workflows are not restarted if the server crashes or is restarted.</p>
<h2 id="example" class="group scroll-mt-24">Example<a href="#example" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>A typical use-case for workflows is sending emails. For example, you might want to send a user an email one day after they
sign up. In database terms we can say: &quot;trigger an email workflow whenever there is an insert on the <code class="short-inline-codeblock">users</code> table.&quot;</p>
<p>Let&#x27;s break this down into steps, then tie it all together at the end:</p>
<h3 id="sending-an-email" class="group scroll-mt-24">Sending an email<a href="#sending-an-email" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>SendEmail:</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  Type: Task</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  Next: Complete</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  Resource: my-email-service</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  Parameters:</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    api_key: my-api-key</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    template_id: welcome-email</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    payload:</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>      name.$: &#x27;$.record.name&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>      email.$: &#x27;$.record.email&#x27;</span></div></div><br/></code></div></div>
<p>Here we have a &quot;Task&quot; which triggers a call to an email service (like Mailgun or Postmark). Specifically, it&#x27;s telling
the service to send the <code class="short-inline-codeblock">welcome-email</code> template, and it&#x27;s providing it a <code class="short-inline-codeblock">name</code> and an <code class="short-inline-codeblock">email</code> as parameters.</p>
<h3 id="waiting-a-day" class="group scroll-mt-24">Waiting a day<a href="#waiting-a-day" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Since we don&#x27;t want to send the email immediately, we need to tell Workflows to wait one day</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>WaitOneDay:</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  Type: Wait</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  Next: SendEmail</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  Seconds: 86400</span></div></div><br/></code></div></div>
<p>Here &quot;one day&quot; is specified in seconds.</p>
<h3 id="trigger-on-insert" class="group scroll-mt-24">Trigger on insert<a href="#trigger-on-insert" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We mentioned that you could trigger a workflow whenever there is an &quot;insert&quot; on the <code class="short-inline-codeblock">users</code> table. But what if you insert
multiple users at once? Not a problem - we can loop through all the inserts with a <code class="short-inline-codeblock">Map</code>:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>EmailUsers:</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>  Type: Map</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>  End: true</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>  InputPath: &#x27;$.changes&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>  Iterator:</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>    StartAt: CheckInsert</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>    States:</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>      CheckInsert:</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>        Type: Choice</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>        Default: Complete</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>        Choices:</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>          - Variable: &#x27;$.type&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>            StringEquals: INSERT</span></div></div><div><span class="ch-code-line-number">_<!-- -->14</span><div style="display:inline-block;margin-left:16px"><span>            Next: WaitOneDay</span></div></div><br/></code></div></div>
<p>In this part, we have a task &quot;EmailUsers&quot;, which iterates through all the database events (<code class="short-inline-codeblock">$.changes</code>) and checks if they are INSERTs.</p>
<h3 id="tying-it-all-together" class="group scroll-mt-24">Tying it all together<a href="#tying-it-all-together" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Let&#x27;s see how it looks all together:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>---</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>Comment: Email users after one day</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>StartAt: EmailUsers</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>States:</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>  EmailUsers:</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>    Type: Map</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>    End: true</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>    InputPath: &#x27;$.changes&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>    Iterator:</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>      StartAt: CheckInsert</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>      States:</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>        CheckInsert:</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>          Type: Choice</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>          Default: Complete</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>          Choices:</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>            - Variable: &#x27;$.type&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>              StringEquals: INSERT</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>              Next: WaitOneDay</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>        WaitOneDay:</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>          Type: Wait</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>          Next: SendEmail</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>          Seconds: 86400</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>        SendEmail:</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>          Type: Task</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>          Next: Complete</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>          Resource: send-templated-email</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>          Parameters:</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>            api_key: my-api-key</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>            template_id: welcome-email</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>            payload:</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>              name.$: &#x27;$.record.name&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>              email.$: &#x27;$.record.email&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>        Complete:</span></div></div><div><span class="ch-code-line-number">_<!-- -->34</span><div style="display:inline-block;margin-left:16px"><span>          Type: Succeed</span></div></div><br/></code></div></div>
<p>The workflow receives the following JSON data from Supabase <a href="https://github.com/supabase/realtime">Realtime</a>:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>{</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>  &quot;changes&quot;: [</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>    {</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>      &quot;columns&quot;: [</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>        {</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>          &quot;flags&quot;: [&quot;key&quot;],</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>          &quot;name&quot;: &quot;id&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>          &quot;type&quot;: &quot;int8&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>          &quot;type_modifier&quot;: 4294967295</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>        },</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>        {</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>          &quot;flags&quot;: [],</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>          &quot;name&quot;: &quot;name&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>          &quot;type&quot;: &quot;text&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>          &quot;type_modifier&quot;: 4294967295</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>        },</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>        {</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>          &quot;flags&quot;: [],</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>          &quot;name&quot;: &quot;email&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>          &quot;type&quot;: &quot;text&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>          &quot;type_modifier&quot;: 4294967295</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>        }</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>      ],</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>      &quot;commit_timestamp&quot;: &quot;2021-03-17T14:00:26Z&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>      &quot;record&quot;: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>        &quot;id&quot;: &quot;101492&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>        &quot;name&quot;: &quot;Alfred&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>        &quot;email&quot;: &quot;<EMAIL>&quot;</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>      },</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>      &quot;schema&quot;: &quot;public&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>      &quot;table&quot;: &quot;users&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>      &quot;type&quot;: &quot;INSERT&quot;</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>    }</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>  ],</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>  &quot;commit_timestamp&quot;: &quot;2021-03-17T14:00:26Z&quot;</span></div></div><div><span class="ch-code-line-number">_<!-- -->36</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<h2 id="next-steps" class="group scroll-mt-24">Next Steps<a href="#next-steps" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We&#x27;ve already open sourced the Workflow interpreter <a href="https://github.com/supabase/workflows">here</a>. It&#x27;s built with Elixir,
so you can find it on Hex <a href="https://hexdocs.pm/workflows/Workflows.html">here</a>.</p>
<p>After we&#x27;ve ironed out a few bugs we will integrate it into the Supabase Stack. As with all Supabase features, we&#x27;ll add a
<a href="https://ui.supabase.com/">nice UI</a> to make prototyping extremely rapid. We&#x27;ll integrate the UI with the code (via Git) to make
sure everything is version controlled.</p></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-workflows&amp;text=Workflows%20are%20coming%20to%20Supabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-workflows&amp;text=Workflows%20are%20coming%20to%20Supabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-workflows&amp;t=Workflows%20are%20coming%20to%20Supabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="supabase-pgbouncer.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">PgBouncer is now available in Supabase</h4><p class="small">2 April 2021</p></div></div></div></div></a></div><div><a href="https://supabase.com/blog/supabase-nft-marketplace"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Supabase Launches NFT Marketplace</h4><p class="small">1 April 2021</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/functions"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">functions</div></a><a href="https://supabase.com/blog/tags/workflows"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">workflows</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#tldr">TLDR</a></li>
<li><a href="#firebase-functions">Firebase Functions</a></li>
<li><a href="#a-better-solution">A better solution?</a></li>
<li><a href="#what-are-workflows">What are Workflows</a></li>
<li><a href="#example">Example</a></li>
<li><a href="#next-steps">Next Steps</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-workflows&amp;text=Workflows%20are%20coming%20to%20Supabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-workflows&amp;text=Workflows%20are%20coming%20to%20Supabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-workflows&amp;t=Workflows%20are%20coming%20to%20Supabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"supabase-pgbouncer","title":"PgBouncer is now available in Supabase","description":"Better support for Serverless and Postgres.","author":"angelico_de_los_reyes","author_title":"Supabase","author_url":"https://github.com/dragarcia","author_image_url":"https://github.com/dragarcia.png","image":"bouncer/pgbouncer-og.jpg","thumb":"bouncer/pgbouncer-thumb.jpg","categories":["product"],"tags":["database","engineering"],"date":"2021-04-02","formattedDate":"2 April 2021","readingTime":"5 minute read","url":"/blog/supabase-pgbouncer","path":"/blog/supabase-pgbouncer"},"nextPost":{"slug":"supabase-nft-marketplace","title":"Supabase Launches NFT Marketplace","description":"A fully encrypted NFT platform to protect and transact your digital assets","author":"ant_wilson","author_title":"Supabase","author_url":"https://github.com/awalias","author_image_url":"https://github.com/awalias.png","image":"nft/nft-3.png","thumb":"nft/nft-3.png","categories":["company"],"tags":["supabase","nfts"],"date":"04-01-2021","formattedDate":"1 April 2021","readingTime":"3 minute read","url":"/blog/supabase-nft-marketplace","path":"/blog/supabase-nft-marketplace"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-workflows","source":"\nThis week we [launched Supabase Storage](/blog/supabase-storage), which leaves one other huge piece of the\nstack that everyone is asking for: Functions.\n\n## TLDR\n\nWe're not releasing Functions today. Trust us, we know you want them. They are coming, just not today.\n\nBut we are building something that we think you're going to like: Workflows. We haven't finished building it yet, but Workflows are\na \"piece\" of the Function story and arguably an even more exciting feature.\n\n![Everyone wants functions](/images/blog/workflows/functions.jpg)\n\n## Firebase Functions\n\nFirebase functions are relatively simple. If you use Serverless, AWS Lambda, Cloudflare Workers, Next.js API routes, or\nNetlify Functions, then you know how they work. A Firebase function executes some code which you provide, without you managing a server.\n\nSpecifically for Firebase, they have another key feature - they can be triggered by database events. For example, you can\ntrigger a function whenever a Firestore Document is updated.\n\nThis is great, but it is still limited for a few real-world use cases. For example, what if you want to send an email to a user\none day after a user signs up. Or one year? There is no queuing functionality in Firebase. You'd have to manage a process like\nthis yourself, probably using a cron-job.\n\n## A better solution?\n\nWe searched for some open source tools which we think are solving this problem well. We looked at\n[NodeRed](/blog/supabase-storage#designing-the-storage-middleware), [n8n](https://n8n.io/),\n[Airflow](http://airflow.apache.org/blog/airflow-two-point-oh-is-here/), and about 10 other tools. They are amazing tools on their\nown, but for the Supabase Stack they ultimately had the\n[same shortcomings](/blog/supabase-storage#integration-with-the-supabase-ecosystem) that we found\nwith Storage providers - most of them lacked deep Postgres integration.\n\nWe went back to the drawing board and asked, \"if we could wave a wand and get the perfect solution, what would it look like?\".\nThe tool that came very close is [AWS Step Functions](https://aws.amazon.com/step-functions/). The only problem: it's not open source.\nLuckily, their [state language](https://states-language.net/spec.html) is.\n\nUsing this states language, we are [building an open source orchestration engine](https://github.com/supabase/workflows) for\nmanaging very complex Workflows with queueing, etc. It will be built with Elixir.\n\nThis engine won't execute code. Instead, it will coordinate and execute existing functions wherever they live: AWS, GCP, Azure,\nOpenFaas, and of course Postgres.\n\nWe plan to add \"modules\" which work natively: email services, notification services, and other platforms.\n\nThe engine is deeply integrated with Postgres. `Jobs`, `queues`, and `logs` will be stored and accessible by SQL. We'd like to give a shoutout to the [Oban](https://getoban.pro) team here, their robust job processing was a big missing piece for the engine. And most importantly, it's backed by Postgres!\n\nOnce ready, we will make this available in the Supabase Dashboard with a Zapier-like interface.\n\n## What are Workflows\n\nWorkflows orchestrate and execute functions in response to a database event (insert, update, delete) or a HTTP call (direct invocation).\n\nYou can use them to rapidly develop microservices (once we have functions) without worrying about servers.\n\nWorkflows are stateless - the output of a state becomes the input of the next state.\n\nWorkflows are defined using Amazon States Languages, so you can import your workflows from AWS (although we are still building handlers\nfor most AWS resources).\n\nWorkflows can be _persistent_ (the default). This means they are tolerant to server restarts, but it also means they need to use\nthe database to store their state.\n\nWorkers can be _transient._ These are fully in-memory if you don't want to store the execution state (for example, IoT\napplications that trigger workflows very often). Transient workflows are not restarted if the server crashes or is restarted.\n\n## Example\n\nA typical use-case for workflows is sending emails. For example, you might want to send a user an email one day after they\nsign up. In database terms we can say: \"trigger an email workflow whenever there is an insert on the `users` table.\"\n\nLet's break this down into steps, then tie it all together at the end:\n\n### Sending an email\n\n```yaml\nSendEmail:\n  Type: Task\n  Next: Complete\n  Resource: my-email-service\n  Parameters:\n    api_key: my-api-key\n    template_id: welcome-email\n    payload:\n      name.$: '$.record.name'\n      email.$: '$.record.email'\n```\n\nHere we have a \"Task\" which triggers a call to an email service (like Mailgun or Postmark). Specifically, it's telling\nthe service to send the `welcome-email` template, and it's providing it a `name` and an `email` as parameters.\n\n### Waiting a day\n\nSince we don't want to send the email immediately, we need to tell Workflows to wait one day\n\n```yaml\nWaitOneDay:\n  Type: Wait\n  Next: SendEmail\n  Seconds: 86400\n```\n\nHere \"one day\" is specified in seconds.\n\n### Trigger on insert\n\nWe mentioned that you could trigger a workflow whenever there is an \"insert\" on the `users` table. But what if you insert\nmultiple users at once? Not a problem - we can loop through all the inserts with a `Map`:\n\n```yaml\nEmailUsers:\n  Type: Map\n  End: true\n  InputPath: '$.changes'\n  Iterator:\n    StartAt: CheckInsert\n    States:\n      CheckInsert:\n        Type: Choice\n        Default: Complete\n        Choices:\n          - Variable: '$.type'\n            StringEquals: INSERT\n            Next: WaitOneDay\n```\n\nIn this part, we have a task \"EmailUsers\", which iterates through all the database events (`$.changes`) and checks if they are INSERTs.\n\n### Tying it all together\n\nLet's see how it looks all together:\n\n```yaml\n---\nComment: Email users after one day\nStartAt: EmailUsers\nStates:\n  EmailUsers:\n    Type: Map\n    End: true\n    InputPath: '$.changes'\n    Iterator:\n      StartAt: CheckInsert\n      States:\n        CheckInsert:\n          Type: Choice\n          Default: Complete\n          Choices:\n            - Variable: '$.type'\n              StringEquals: INSERT\n              Next: WaitOneDay\n        WaitOneDay:\n          Type: Wait\n          Next: SendEmail\n          Seconds: 86400\n        SendEmail:\n          Type: Task\n          Next: Complete\n          Resource: send-templated-email\n          Parameters:\n            api_key: my-api-key\n            template_id: welcome-email\n            payload:\n              name.$: '$.record.name'\n              email.$: '$.record.email'\n        Complete:\n          Type: Succeed\n```\n\nThe workflow receives the following JSON data from Supabase [Realtime](https://github.com/supabase/realtime):\n\n```json\n{\n  \"changes\": [\n    {\n      \"columns\": [\n        {\n          \"flags\": [\"key\"],\n          \"name\": \"id\",\n          \"type\": \"int8\",\n          \"type_modifier\": 4294967295\n        },\n        {\n          \"flags\": [],\n          \"name\": \"name\",\n          \"type\": \"text\",\n          \"type_modifier\": 4294967295\n        },\n        {\n          \"flags\": [],\n          \"name\": \"email\",\n          \"type\": \"text\",\n          \"type_modifier\": 4294967295\n        }\n      ],\n      \"commit_timestamp\": \"2021-03-17T14:00:26Z\",\n      \"record\": {\n        \"id\": \"101492\",\n        \"name\": \"Alfred\",\n        \"email\": \"<EMAIL>\"\n      },\n      \"schema\": \"public\",\n      \"table\": \"users\",\n      \"type\": \"INSERT\"\n    }\n  ],\n  \"commit_timestamp\": \"2021-03-17T14:00:26Z\"\n}\n```\n\n## Next Steps\n\nWe've already open sourced the Workflow interpreter [here](https://github.com/supabase/workflows). It's built with Elixir,\nso you can find it on Hex [here](https://hexdocs.pm/workflows/Workflows.html).\n\nAfter we've ironed out a few bugs we will integrate it into the Supabase Stack. As with all Supabase features, we'll add a\n[nice UI](https://ui.supabase.com/) to make prototyping extremely rapid. We'll integrate the UI with the code (via Git) to make\nsure everything is version controlled.\n","title":"Workflows are coming to Supabase","description":"Functions are great, but you know what's better?","author":"fracek","author_title":"Supabase","author_url":"https://github.com/fracek","author_image_url":"https://github.com/fracek.png","image":"workflows/workflows-og.jpg","thumb":"workflows/workflows-thumb.jpg","categories":["product"],"tags":["functions","workflows"],"date":"2021-04-02","content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    a: \"a\",\n    h2: \"h2\",\n    img: \"img\",\n    code: \"code\",\n    em: \"em\",\n    h3: \"h3\"\n  }, _provideComponents(), props.components), {CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"This week we \", _jsx(_components.a, {\n        href: \"/blog/supabase-storage\",\n        children: \"launched Supabase Storage\"\n      }), \", which leaves one other huge piece of the\\nstack that everyone is asking for: Functions.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"tldr\",\n      children: \"TLDR\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We're not releasing Functions today. Trust us, we know you want them. They are coming, just not today.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"But we are building something that we think you're going to like: Workflows. We haven't finished building it yet, but Workflows are\\na \\\"piece\\\" of the Function story and arguably an even more exciting feature.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/workflows/functions.jpg\",\n        alt: \"Everyone wants functions\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"firebase-functions\",\n      children: \"Firebase Functions\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Firebase functions are relatively simple. If you use Serverless, AWS Lambda, Cloudflare Workers, Next.js API routes, or\\nNetlify Functions, then you know how they work. A Firebase function executes some code which you provide, without you managing a server.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Specifically for Firebase, they have another key feature - they can be triggered by database events. For example, you can\\ntrigger a function whenever a Firestore Document is updated.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This is great, but it is still limited for a few real-world use cases. For example, what if you want to send an email to a user\\none day after a user signs up. Or one year? There is no queuing functionality in Firebase. You'd have to manage a process like\\nthis yourself, probably using a cron-job.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"a-better-solution\",\n      children: \"A better solution?\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We searched for some open source tools which we think are solving this problem well. We looked at\\n\", _jsx(_components.a, {\n        href: \"/blog/supabase-storage#designing-the-storage-middleware\",\n        children: \"NodeRed\"\n      }), \", \", _jsx(_components.a, {\n        href: \"https://n8n.io/\",\n        children: \"n8n\"\n      }), \",\\n\", _jsx(_components.a, {\n        href: \"http://airflow.apache.org/blog/airflow-two-point-oh-is-here/\",\n        children: \"Airflow\"\n      }), \", and about 10 other tools. They are amazing tools on their\\nown, but for the Supabase Stack they ultimately had the\\n\", _jsx(_components.a, {\n        href: \"/blog/supabase-storage#integration-with-the-supabase-ecosystem\",\n        children: \"same shortcomings\"\n      }), \" that we found\\nwith Storage providers - most of them lacked deep Postgres integration.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We went back to the drawing board and asked, \\\"if we could wave a wand and get the perfect solution, what would it look like?\\\".\\nThe tool that came very close is \", _jsx(_components.a, {\n        href: \"https://aws.amazon.com/step-functions/\",\n        children: \"AWS Step Functions\"\n      }), \". The only problem: it's not open source.\\nLuckily, their \", _jsx(_components.a, {\n        href: \"https://states-language.net/spec.html\",\n        children: \"state language\"\n      }), \" is.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Using this states language, we are \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/workflows\",\n        children: \"building an open source orchestration engine\"\n      }), \" for\\nmanaging very complex Workflows with queueing, etc. It will be built with Elixir.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This engine won't execute code. Instead, it will coordinate and execute existing functions wherever they live: AWS, GCP, Azure,\\nOpenFaas, and of course Postgres.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We plan to add \\\"modules\\\" which work natively: email services, notification services, and other platforms.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The engine is deeply integrated with Postgres. \", _jsx(_components.code, {\n        children: \"Jobs\"\n      }), \", \", _jsx(_components.code, {\n        children: \"queues\"\n      }), \", and \", _jsx(_components.code, {\n        children: \"logs\"\n      }), \" will be stored and accessible by SQL. We'd like to give a shoutout to the \", _jsx(_components.a, {\n        href: \"https://getoban.pro\",\n        children: \"Oban\"\n      }), \" team here, their robust job processing was a big missing piece for the engine. And most importantly, it's backed by Postgres!\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Once ready, we will make this available in the Supabase Dashboard with a Zapier-like interface.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"what-are-workflows\",\n      children: \"What are Workflows\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Workflows orchestrate and execute functions in response to a database event (insert, update, delete) or a HTTP call (direct invocation).\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You can use them to rapidly develop microservices (once we have functions) without worrying about servers.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Workflows are stateless - the output of a state becomes the input of the next state.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Workflows are defined using Amazon States Languages, so you can import your workflows from AWS (although we are still building handlers\\nfor most AWS resources).\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Workflows can be \", _jsx(_components.em, {\n        children: \"persistent\"\n      }), \" (the default). This means they are tolerant to server restarts, but it also means they need to use\\nthe database to store their state.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Workers can be \", _jsx(_components.em, {\n        children: \"transient.\"\n      }), \" These are fully in-memory if you don't want to store the execution state (for example, IoT\\napplications that trigger workflows very often). Transient workflows are not restarted if the server crashes or is restarted.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"example\",\n      children: \"Example\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"A typical use-case for workflows is sending emails. For example, you might want to send a user an email one day after they\\nsign up. In database terms we can say: \\\"trigger an email workflow whenever there is an insert on the \", _jsx(_components.code, {\n        children: \"users\"\n      }), \" table.\\\"\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Let's break this down into steps, then tie it all together at the end:\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"sending-an-email\",\n      children: \"Sending an email\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"SendEmail\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  Type\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Task\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  Next\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Complete\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  Resource\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"my-email-service\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  Parameters\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    api_key\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"my-api-key\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    template_id\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"welcome-email\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    payload\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      name.$\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'$.record.name'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      email.$\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'$.record.email'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"yaml\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Here we have a \\\"Task\\\" which triggers a call to an email service (like Mailgun or Postmark). Specifically, it's telling\\nthe service to send the \", _jsx(_components.code, {\n        children: \"welcome-email\"\n      }), \" template, and it's providing it a \", _jsx(_components.code, {\n        children: \"name\"\n      }), \" and an \", _jsx(_components.code, {\n        children: \"email\"\n      }), \" as parameters.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"waiting-a-day\",\n      children: \"Waiting a day\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Since we don't want to send the email immediately, we need to tell Workflows to wait one day\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"WaitOneDay\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  Type\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Wait\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  Next\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"SendEmail\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  Seconds\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"86400\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"yaml\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Here \\\"one day\\\" is specified in seconds.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"trigger-on-insert\",\n      children: \"Trigger on insert\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We mentioned that you could trigger a workflow whenever there is an \\\"insert\\\" on the \", _jsx(_components.code, {\n        children: \"users\"\n      }), \" table. But what if you insert\\nmultiple users at once? Not a problem - we can loop through all the inserts with a \", _jsx(_components.code, {\n        children: \"Map\"\n      }), \":\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"EmailUsers\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  Type\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Map\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  End\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"true\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  InputPath\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'$.changes'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  Iterator\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    StartAt\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"CheckInsert\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    States\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      CheckInsert\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        Type\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Choice\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        Default\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Complete\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        Choices\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          - \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Variable\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'$.type'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"            StringEquals\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"INSERT\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"            Next\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"WaitOneDay\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"yaml\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In this part, we have a task \\\"EmailUsers\\\", which iterates through all the database events (\", _jsx(_components.code, {\n        children: \"$.changes\"\n      }), \") and checks if they are INSERTs.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"tying-it-all-together\",\n      children: \"Tying it all together\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Let's see how it looks all together:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"---\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"Comment\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Email users after one day\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"StartAt\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"EmailUsers\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"States\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  EmailUsers\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    Type\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Map\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    End\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"true\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    InputPath\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'$.changes'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    Iterator\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      StartAt\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"CheckInsert\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      States\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        CheckInsert\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          Type\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Choice\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          Default\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Complete\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          Choices\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"            - \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Variable\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'$.type'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"              StringEquals\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"INSERT\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"              Next\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"WaitOneDay\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        WaitOneDay\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          Type\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Wait\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          Next\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"SendEmail\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          Seconds\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"86400\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        SendEmail\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          Type\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Task\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          Next\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Complete\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          Resource\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"send-templated-email\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          Parameters\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"            api_key\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"my-api-key\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"            template_id\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"welcome-email\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"            payload\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"              name.$\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'$.record.name'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"              email.$\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'$.record.email'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        Complete\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          Type\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Succeed\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"yaml\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The workflow receives the following JSON data from Supabase \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/realtime\",\n        children: \"Realtime\"\n      }), \":\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"changes\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": [\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"columns\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": [\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          \\\"flags\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": [\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"key\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"],\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          \\\"name\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"id\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          \\\"type\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"int8\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          \\\"type_modifier\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"4294967295\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          \\\"flags\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": [],\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          \\\"name\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"name\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          \\\"type\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"text\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          \\\"type_modifier\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"4294967295\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          \\\"flags\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": [],\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          \\\"name\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"email\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          \\\"type\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"text\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          \\\"type_modifier\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"4294967295\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      ],\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"commit_timestamp\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"2021-03-17T14:00:26Z\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"record\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \\\"id\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"101492\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \\\"name\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"Alfred\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \\\"email\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"<EMAIL>\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"schema\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"public\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"table\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"users\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"type\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"INSERT\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  ],\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"commit_timestamp\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"2021-03-17T14:00:26Z\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"json\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"next-steps\",\n      children: \"Next Steps\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We've already open sourced the Workflow interpreter \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/workflows\",\n        children: \"here\"\n      }), \". It's built with Elixir,\\nso you can find it on Hex \", _jsx(_components.a, {\n        href: \"https://hexdocs.pm/workflows/Workflows.html\",\n        children: \"here\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"After we've ironed out a few bugs we will integrate it into the Supabase Stack. As with all Supabase features, we'll add a\\n\", _jsx(_components.a, {\n        href: \"https://ui.supabase.com/\",\n        children: \"nice UI\"\n      }), \" to make prototyping extremely rapid. We'll integrate the UI with the code (via Git) to make\\nsure everything is version controlled.\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"TLDR","slug":"tldr","lvl":2,"i":0,"seen":0},{"content":"Firebase Functions","slug":"firebase-functions","lvl":2,"i":1,"seen":0},{"content":"A better solution?","slug":"a-better-solution","lvl":2,"i":2,"seen":0},{"content":"What are Workflows","slug":"what-are-workflows","lvl":2,"i":3,"seen":0},{"content":"Example","slug":"example","lvl":2,"i":4,"seen":0},{"content":"Sending an email","slug":"sending-an-email","lvl":3,"i":5,"seen":0},{"content":"Waiting a day","slug":"waiting-a-day","lvl":3,"i":6,"seen":0},{"content":"Trigger on insert","slug":"trigger-on-insert","lvl":3,"i":7,"seen":0},{"content":"Tying it all together","slug":"tying-it-all-together","lvl":3,"i":8,"seen":0},{"content":"Next Steps","slug":"next-steps","lvl":2,"i":9,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,3],"level":0},{"type":"inline","content":"This week we [launched Supabase Storage](/blog/supabase-storage), which leaves one other huge piece of the\nstack that everyone is asking for: Functions.","level":1,"lines":[1,3],"children":[{"type":"text","content":"This week we ","level":0},{"type":"link_open","href":"/blog/supabase-storage","title":"","level":0},{"type":"text","content":"launched Supabase Storage","level":1},{"type":"link_close","level":0},{"type":"text","content":", which leaves one other huge piece of the","level":0},{"type":"softbreak","level":0},{"type":"text","content":"stack that everyone is asking for: Functions.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[4,5],"level":0},{"type":"inline","content":"[TLDR](#tldr)","level":1,"lines":[4,5],"children":[{"type":"text","content":"TLDR","level":0}],"lvl":2,"i":0,"seen":0,"slug":"tldr"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[6,7],"level":0},{"type":"inline","content":"We're not releasing Functions today. Trust us, we know you want them. They are coming, just not today.","level":1,"lines":[6,7],"children":[{"type":"text","content":"We're not releasing Functions today. Trust us, we know you want them. They are coming, just not today.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[8,10],"level":0},{"type":"inline","content":"But we are building something that we think you're going to like: Workflows. We haven't finished building it yet, but Workflows are\na \"piece\" of the Function story and arguably an even more exciting feature.","level":1,"lines":[8,10],"children":[{"type":"text","content":"But we are building something that we think you're going to like: Workflows. We haven't finished building it yet, but Workflows are","level":0},{"type":"softbreak","level":0},{"type":"text","content":"a \"piece\" of the Function story and arguably an even more exciting feature.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[11,12],"level":0},{"type":"inline","content":"![Everyone wants functions](/images/blog/workflows/functions.jpg)","level":1,"lines":[11,12],"children":[{"type":"image","src":"/images/blog/workflows/functions.jpg","title":"","alt":"Everyone wants functions","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[13,14],"level":0},{"type":"inline","content":"[Firebase Functions](#firebase-functions)","level":1,"lines":[13,14],"children":[{"type":"text","content":"Firebase Functions","level":0}],"lvl":2,"i":1,"seen":0,"slug":"firebase-functions"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[15,17],"level":0},{"type":"inline","content":"Firebase functions are relatively simple. If you use Serverless, AWS Lambda, Cloudflare Workers, Next.js API routes, or\nNetlify Functions, then you know how they work. A Firebase function executes some code which you provide, without you managing a server.","level":1,"lines":[15,17],"children":[{"type":"text","content":"Firebase functions are relatively simple. If you use Serverless, AWS Lambda, Cloudflare Workers, Next.js API routes, or","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Netlify Functions, then you know how they work. A Firebase function executes some code which you provide, without you managing a server.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[18,20],"level":0},{"type":"inline","content":"Specifically for Firebase, they have another key feature - they can be triggered by database events. For example, you can\ntrigger a function whenever a Firestore Document is updated.","level":1,"lines":[18,20],"children":[{"type":"text","content":"Specifically for Firebase, they have another key feature - they can be triggered by database events. For example, you can","level":0},{"type":"softbreak","level":0},{"type":"text","content":"trigger a function whenever a Firestore Document is updated.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[21,24],"level":0},{"type":"inline","content":"This is great, but it is still limited for a few real-world use cases. For example, what if you want to send an email to a user\none day after a user signs up. Or one year? There is no queuing functionality in Firebase. You'd have to manage a process like\nthis yourself, probably using a cron-job.","level":1,"lines":[21,24],"children":[{"type":"text","content":"This is great, but it is still limited for a few real-world use cases. For example, what if you want to send an email to a user","level":0},{"type":"softbreak","level":0},{"type":"text","content":"one day after a user signs up. Or one year? There is no queuing functionality in Firebase. You'd have to manage a process like","level":0},{"type":"softbreak","level":0},{"type":"text","content":"this yourself, probably using a cron-job.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[25,26],"level":0},{"type":"inline","content":"[A better solution?](#a-better-solution)","level":1,"lines":[25,26],"children":[{"type":"text","content":"A better solution?","level":0}],"lvl":2,"i":2,"seen":0,"slug":"a-better-solution"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[27,33],"level":0},{"type":"inline","content":"We searched for some open source tools which we think are solving this problem well. We looked at\n[NodeRed](/blog/supabase-storage#designing-the-storage-middleware), [n8n](https://n8n.io/),\n[Airflow](http://airflow.apache.org/blog/airflow-two-point-oh-is-here/), and about 10 other tools. They are amazing tools on their\nown, but for the Supabase Stack they ultimately had the\n[same shortcomings](/blog/supabase-storage#integration-with-the-supabase-ecosystem) that we found\nwith Storage providers - most of them lacked deep Postgres integration.","level":1,"lines":[27,33],"children":[{"type":"text","content":"We searched for some open source tools which we think are solving this problem well. We looked at","level":0},{"type":"softbreak","level":0},{"type":"link_open","href":"/blog/supabase-storage#designing-the-storage-middleware","title":"","level":0},{"type":"text","content":"NodeRed","level":1},{"type":"link_close","level":0},{"type":"text","content":", ","level":0},{"type":"link_open","href":"https://n8n.io/","title":"","level":0},{"type":"text","content":"n8n","level":1},{"type":"link_close","level":0},{"type":"text","content":",","level":0},{"type":"softbreak","level":0},{"type":"link_open","href":"http://airflow.apache.org/blog/airflow-two-point-oh-is-here/","title":"","level":0},{"type":"text","content":"Airflow","level":1},{"type":"link_close","level":0},{"type":"text","content":", and about 10 other tools. They are amazing tools on their","level":0},{"type":"softbreak","level":0},{"type":"text","content":"own, but for the Supabase Stack they ultimately had the","level":0},{"type":"softbreak","level":0},{"type":"link_open","href":"/blog/supabase-storage#integration-with-the-supabase-ecosystem","title":"","level":0},{"type":"text","content":"same shortcomings","level":1},{"type":"link_close","level":0},{"type":"text","content":" that we found","level":0},{"type":"softbreak","level":0},{"type":"text","content":"with Storage providers - most of them lacked deep Postgres integration.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[34,37],"level":0},{"type":"inline","content":"We went back to the drawing board and asked, \"if we could wave a wand and get the perfect solution, what would it look like?\".\nThe tool that came very close is [AWS Step Functions](https://aws.amazon.com/step-functions/). The only problem: it's not open source.\nLuckily, their [state language](https://states-language.net/spec.html) is.","level":1,"lines":[34,37],"children":[{"type":"text","content":"We went back to the drawing board and asked, \"if we could wave a wand and get the perfect solution, what would it look like?\".","level":0},{"type":"softbreak","level":0},{"type":"text","content":"The tool that came very close is ","level":0},{"type":"link_open","href":"https://aws.amazon.com/step-functions/","title":"","level":0},{"type":"text","content":"AWS Step Functions","level":1},{"type":"link_close","level":0},{"type":"text","content":". The only problem: it's not open source.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Luckily, their ","level":0},{"type":"link_open","href":"https://states-language.net/spec.html","title":"","level":0},{"type":"text","content":"state language","level":1},{"type":"link_close","level":0},{"type":"text","content":" is.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[38,40],"level":0},{"type":"inline","content":"Using this states language, we are [building an open source orchestration engine](https://github.com/supabase/workflows) for\nmanaging very complex Workflows with queueing, etc. It will be built with Elixir.","level":1,"lines":[38,40],"children":[{"type":"text","content":"Using this states language, we are ","level":0},{"type":"link_open","href":"https://github.com/supabase/workflows","title":"","level":0},{"type":"text","content":"building an open source orchestration engine","level":1},{"type":"link_close","level":0},{"type":"text","content":" for","level":0},{"type":"softbreak","level":0},{"type":"text","content":"managing very complex Workflows with queueing, etc. It will be built with Elixir.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[41,43],"level":0},{"type":"inline","content":"This engine won't execute code. Instead, it will coordinate and execute existing functions wherever they live: AWS, GCP, Azure,\nOpenFaas, and of course Postgres.","level":1,"lines":[41,43],"children":[{"type":"text","content":"This engine won't execute code. Instead, it will coordinate and execute existing functions wherever they live: AWS, GCP, Azure,","level":0},{"type":"softbreak","level":0},{"type":"text","content":"OpenFaas, and of course Postgres.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[44,45],"level":0},{"type":"inline","content":"We plan to add \"modules\" which work natively: email services, notification services, and other platforms.","level":1,"lines":[44,45],"children":[{"type":"text","content":"We plan to add \"modules\" which work natively: email services, notification services, and other platforms.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[46,47],"level":0},{"type":"inline","content":"The engine is deeply integrated with Postgres. `Jobs`, `queues`, and `logs` will be stored and accessible by SQL. We'd like to give a shoutout to the [Oban](https://getoban.pro) team here, their robust job processing was a big missing piece for the engine. And most importantly, it's backed by Postgres!","level":1,"lines":[46,47],"children":[{"type":"text","content":"The engine is deeply integrated with Postgres. ","level":0},{"type":"code","content":"Jobs","block":false,"level":0},{"type":"text","content":", ","level":0},{"type":"code","content":"queues","block":false,"level":0},{"type":"text","content":", and ","level":0},{"type":"code","content":"logs","block":false,"level":0},{"type":"text","content":" will be stored and accessible by SQL. We'd like to give a shoutout to the ","level":0},{"type":"link_open","href":"https://getoban.pro","title":"","level":0},{"type":"text","content":"Oban","level":1},{"type":"link_close","level":0},{"type":"text","content":" team here, their robust job processing was a big missing piece for the engine. And most importantly, it's backed by Postgres!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[48,49],"level":0},{"type":"inline","content":"Once ready, we will make this available in the Supabase Dashboard with a Zapier-like interface.","level":1,"lines":[48,49],"children":[{"type":"text","content":"Once ready, we will make this available in the Supabase Dashboard with a Zapier-like interface.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[50,51],"level":0},{"type":"inline","content":"[What are Workflows](#what-are-workflows)","level":1,"lines":[50,51],"children":[{"type":"text","content":"What are Workflows","level":0}],"lvl":2,"i":3,"seen":0,"slug":"what-are-workflows"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[52,53],"level":0},{"type":"inline","content":"Workflows orchestrate and execute functions in response to a database event (insert, update, delete) or a HTTP call (direct invocation).","level":1,"lines":[52,53],"children":[{"type":"text","content":"Workflows orchestrate and execute functions in response to a database event (insert, update, delete) or a HTTP call (direct invocation).","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[54,55],"level":0},{"type":"inline","content":"You can use them to rapidly develop microservices (once we have functions) without worrying about servers.","level":1,"lines":[54,55],"children":[{"type":"text","content":"You can use them to rapidly develop microservices (once we have functions) without worrying about servers.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[56,57],"level":0},{"type":"inline","content":"Workflows are stateless - the output of a state becomes the input of the next state.","level":1,"lines":[56,57],"children":[{"type":"text","content":"Workflows are stateless - the output of a state becomes the input of the next state.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[58,60],"level":0},{"type":"inline","content":"Workflows are defined using Amazon States Languages, so you can import your workflows from AWS (although we are still building handlers\nfor most AWS resources).","level":1,"lines":[58,60],"children":[{"type":"text","content":"Workflows are defined using Amazon States Languages, so you can import your workflows from AWS (although we are still building handlers","level":0},{"type":"softbreak","level":0},{"type":"text","content":"for most AWS resources).","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[61,63],"level":0},{"type":"inline","content":"Workflows can be _persistent_ (the default). This means they are tolerant to server restarts, but it also means they need to use\nthe database to store their state.","level":1,"lines":[61,63],"children":[{"type":"text","content":"Workflows can be ","level":0},{"type":"em_open","level":0},{"type":"text","content":"persistent","level":1},{"type":"em_close","level":0},{"type":"text","content":" (the default). This means they are tolerant to server restarts, but it also means they need to use","level":0},{"type":"softbreak","level":0},{"type":"text","content":"the database to store their state.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[64,66],"level":0},{"type":"inline","content":"Workers can be _transient._ These are fully in-memory if you don't want to store the execution state (for example, IoT\napplications that trigger workflows very often). Transient workflows are not restarted if the server crashes or is restarted.","level":1,"lines":[64,66],"children":[{"type":"text","content":"Workers can be ","level":0},{"type":"em_open","level":0},{"type":"text","content":"transient.","level":1},{"type":"em_close","level":0},{"type":"text","content":" These are fully in-memory if you don't want to store the execution state (for example, IoT","level":0},{"type":"softbreak","level":0},{"type":"text","content":"applications that trigger workflows very often). Transient workflows are not restarted if the server crashes or is restarted.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[67,68],"level":0},{"type":"inline","content":"[Example](#example)","level":1,"lines":[67,68],"children":[{"type":"text","content":"Example","level":0}],"lvl":2,"i":4,"seen":0,"slug":"example"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[69,71],"level":0},{"type":"inline","content":"A typical use-case for workflows is sending emails. For example, you might want to send a user an email one day after they\nsign up. In database terms we can say: \"trigger an email workflow whenever there is an insert on the `users` table.\"","level":1,"lines":[69,71],"children":[{"type":"text","content":"A typical use-case for workflows is sending emails. For example, you might want to send a user an email one day after they","level":0},{"type":"softbreak","level":0},{"type":"text","content":"sign up. In database terms we can say: \"trigger an email workflow whenever there is an insert on the ","level":0},{"type":"code","content":"users","block":false,"level":0},{"type":"text","content":" table.\"","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[72,73],"level":0},{"type":"inline","content":"Let's break this down into steps, then tie it all together at the end:","level":1,"lines":[72,73],"children":[{"type":"text","content":"Let's break this down into steps, then tie it all together at the end:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[74,75],"level":0},{"type":"inline","content":"[Sending an email](#sending-an-email)","level":1,"lines":[74,75],"children":[{"type":"text","content":"Sending an email","level":0}],"lvl":3,"i":5,"seen":0,"slug":"sending-an-email"},{"type":"heading_close","hLevel":3,"level":0},{"type":"fence","params":"yaml","content":"SendEmail:\n  Type: Task\n  Next: Complete\n  Resource: my-email-service\n  Parameters:\n    api_key: my-api-key\n    template_id: welcome-email\n    payload:\n      name.$: '$.record.name'\n      email.$: '$.record.email'\n","lines":[76,88],"level":0},{"type":"paragraph_open","tight":false,"lines":[89,91],"level":0},{"type":"inline","content":"Here we have a \"Task\" which triggers a call to an email service (like Mailgun or Postmark). Specifically, it's telling\nthe service to send the `welcome-email` template, and it's providing it a `name` and an `email` as parameters.","level":1,"lines":[89,91],"children":[{"type":"text","content":"Here we have a \"Task\" which triggers a call to an email service (like Mailgun or Postmark). Specifically, it's telling","level":0},{"type":"softbreak","level":0},{"type":"text","content":"the service to send the ","level":0},{"type":"code","content":"welcome-email","block":false,"level":0},{"type":"text","content":" template, and it's providing it a ","level":0},{"type":"code","content":"name","block":false,"level":0},{"type":"text","content":" and an ","level":0},{"type":"code","content":"email","block":false,"level":0},{"type":"text","content":" as parameters.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[92,93],"level":0},{"type":"inline","content":"[Waiting a day](#waiting-a-day)","level":1,"lines":[92,93],"children":[{"type":"text","content":"Waiting a day","level":0}],"lvl":3,"i":6,"seen":0,"slug":"waiting-a-day"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[94,95],"level":0},{"type":"inline","content":"Since we don't want to send the email immediately, we need to tell Workflows to wait one day","level":1,"lines":[94,95],"children":[{"type":"text","content":"Since we don't want to send the email immediately, we need to tell Workflows to wait one day","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"yaml","content":"WaitOneDay:\n  Type: Wait\n  Next: SendEmail\n  Seconds: 86400\n","lines":[96,102],"level":0},{"type":"paragraph_open","tight":false,"lines":[103,104],"level":0},{"type":"inline","content":"Here \"one day\" is specified in seconds.","level":1,"lines":[103,104],"children":[{"type":"text","content":"Here \"one day\" is specified in seconds.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[105,106],"level":0},{"type":"inline","content":"[Trigger on insert](#trigger-on-insert)","level":1,"lines":[105,106],"children":[{"type":"text","content":"Trigger on insert","level":0}],"lvl":3,"i":7,"seen":0,"slug":"trigger-on-insert"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[107,109],"level":0},{"type":"inline","content":"We mentioned that you could trigger a workflow whenever there is an \"insert\" on the `users` table. But what if you insert\nmultiple users at once? Not a problem - we can loop through all the inserts with a `Map`:","level":1,"lines":[107,109],"children":[{"type":"text","content":"We mentioned that you could trigger a workflow whenever there is an \"insert\" on the ","level":0},{"type":"code","content":"users","block":false,"level":0},{"type":"text","content":" table. But what if you insert","level":0},{"type":"softbreak","level":0},{"type":"text","content":"multiple users at once? Not a problem - we can loop through all the inserts with a ","level":0},{"type":"code","content":"Map","block":false,"level":0},{"type":"text","content":":","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"yaml","content":"EmailUsers:\n  Type: Map\n  End: true\n  InputPath: '$.changes'\n  Iterator:\n    StartAt: CheckInsert\n    States:\n      CheckInsert:\n        Type: Choice\n        Default: Complete\n        Choices:\n          - Variable: '$.type'\n            StringEquals: INSERT\n            Next: WaitOneDay\n","lines":[110,126],"level":0},{"type":"paragraph_open","tight":false,"lines":[127,128],"level":0},{"type":"inline","content":"In this part, we have a task \"EmailUsers\", which iterates through all the database events (`$.changes`) and checks if they are INSERTs.","level":1,"lines":[127,128],"children":[{"type":"text","content":"In this part, we have a task \"EmailUsers\", which iterates through all the database events (","level":0},{"type":"code","content":"$.changes","block":false,"level":0},{"type":"text","content":") and checks if they are INSERTs.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[129,130],"level":0},{"type":"inline","content":"[Tying it all together](#tying-it-all-together)","level":1,"lines":[129,130],"children":[{"type":"text","content":"Tying it all together","level":0}],"lvl":3,"i":8,"seen":0,"slug":"tying-it-all-together"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[131,132],"level":0},{"type":"inline","content":"Let's see how it looks all together:","level":1,"lines":[131,132],"children":[{"type":"text","content":"Let's see how it looks all together:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"yaml","content":"---\nComment: Email users after one day\nStartAt: EmailUsers\nStates:\n  EmailUsers:\n    Type: Map\n    End: true\n    InputPath: '$.changes'\n    Iterator:\n      StartAt: CheckInsert\n      States:\n        CheckInsert:\n          Type: Choice\n          Default: Complete\n          Choices:\n            - Variable: '$.type'\n              StringEquals: INSERT\n              Next: WaitOneDay\n        WaitOneDay:\n          Type: Wait\n          Next: SendEmail\n          Seconds: 86400\n        SendEmail:\n          Type: Task\n          Next: Complete\n          Resource: send-templated-email\n          Parameters:\n            api_key: my-api-key\n            template_id: welcome-email\n            payload:\n              name.$: '$.record.name'\n              email.$: '$.record.email'\n        Complete:\n          Type: Succeed\n","lines":[133,169],"level":0},{"type":"paragraph_open","tight":false,"lines":[170,171],"level":0},{"type":"inline","content":"The workflow receives the following JSON data from Supabase [Realtime](https://github.com/supabase/realtime):","level":1,"lines":[170,171],"children":[{"type":"text","content":"The workflow receives the following JSON data from Supabase ","level":0},{"type":"link_open","href":"https://github.com/supabase/realtime","title":"","level":0},{"type":"text","content":"Realtime","level":1},{"type":"link_close","level":0},{"type":"text","content":":","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"json","content":"{\n  \"changes\": [\n    {\n      \"columns\": [\n        {\n          \"flags\": [\"key\"],\n          \"name\": \"id\",\n          \"type\": \"int8\",\n          \"type_modifier\": 4294967295\n        },\n        {\n          \"flags\": [],\n          \"name\": \"name\",\n          \"type\": \"text\",\n          \"type_modifier\": 4294967295\n        },\n        {\n          \"flags\": [],\n          \"name\": \"email\",\n          \"type\": \"text\",\n          \"type_modifier\": 4294967295\n        }\n      ],\n      \"commit_timestamp\": \"2021-03-17T14:00:26Z\",\n      \"record\": {\n        \"id\": \"101492\",\n        \"name\": \"Alfred\",\n        \"email\": \"<EMAIL>\"\n      },\n      \"schema\": \"public\",\n      \"table\": \"users\",\n      \"type\": \"INSERT\"\n    }\n  ],\n  \"commit_timestamp\": \"2021-03-17T14:00:26Z\"\n}\n","lines":[172,210],"level":0},{"type":"heading_open","hLevel":2,"lines":[211,212],"level":0},{"type":"inline","content":"[Next Steps](#next-steps)","level":1,"lines":[211,212],"children":[{"type":"text","content":"Next Steps","level":0}],"lvl":2,"i":9,"seen":0,"slug":"next-steps"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[213,215],"level":0},{"type":"inline","content":"We've already open sourced the Workflow interpreter [here](https://github.com/supabase/workflows). It's built with Elixir,\nso you can find it on Hex [here](https://hexdocs.pm/workflows/Workflows.html).","level":1,"lines":[213,215],"children":[{"type":"text","content":"We've already open sourced the Workflow interpreter ","level":0},{"type":"link_open","href":"https://github.com/supabase/workflows","title":"","level":0},{"type":"text","content":"here","level":1},{"type":"link_close","level":0},{"type":"text","content":". It's built with Elixir,","level":0},{"type":"softbreak","level":0},{"type":"text","content":"so you can find it on Hex ","level":0},{"type":"link_open","href":"https://hexdocs.pm/workflows/Workflows.html","title":"","level":0},{"type":"text","content":"here","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[216,219],"level":0},{"type":"inline","content":"After we've ironed out a few bugs we will integrate it into the Supabase Stack. As with all Supabase features, we'll add a\n[nice UI](https://ui.supabase.com/) to make prototyping extremely rapid. We'll integrate the UI with the code (via Git) to make\nsure everything is version controlled.","level":1,"lines":[216,219],"children":[{"type":"text","content":"After we've ironed out a few bugs we will integrate it into the Supabase Stack. As with all Supabase features, we'll add a","level":0},{"type":"softbreak","level":0},{"type":"link_open","href":"https://ui.supabase.com/","title":"","level":0},{"type":"text","content":"nice UI","level":1},{"type":"link_close","level":0},{"type":"text","content":" to make prototyping extremely rapid. We'll integrate the UI with the code (via Git) to make","level":0},{"type":"softbreak","level":0},{"type":"text","content":"sure everything is version controlled.","level":0}]},{"type":"paragraph_close","tight":false,"level":0}],"content":"- [TLDR](#tldr)\n- [Firebase Functions](#firebase-functions)\n- [A better solution?](#a-better-solution)\n- [What are Workflows](#what-are-workflows)\n- [Example](#example)\n- [Next Steps](#next-steps)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-workflows"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>