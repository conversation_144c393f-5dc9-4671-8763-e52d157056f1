<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase Branching</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="A Postgres database for every Pull Request." data-next-head=""/><meta property="og:title" content="Supabase Branching" data-next-head=""/><meta property="og:description" content="A Postgres database for every Pull Request." data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-branching" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2023-12-13" data-next-head=""/><meta property="article:author" content="https://github.com/sweatybridge" data-next-head=""/><meta property="article:author" content="https://github.com/mildtomato" data-next-head=""/><meta property="article:tag" content="launch-week" data-next-head=""/><meta property="article:tag" content="supavisor" data-next-head=""/><meta property="article:tag" content="postgres" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/lwx-supabase-branching/branching-og.png" data-next-head=""/><meta property="og:image:alt" content="Supabase Branching thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Supabase Branching</h1><div class="text-light flex space-x-3 text-sm"><p>13 Dec 2023</p><p>•</p><p>8 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/sweatybridge"><div class="flex items-center gap-3"><div class="w-10"><img alt="Qiao Han avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fsweatybridge.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fsweatybridge.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fsweatybridge.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Qiao Han</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/mildtomato"><div class="flex items-center gap-3"><div class="w-10"><img alt="Jonny Summers-Muir avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fmildtomato.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fmildtomato.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fmildtomato.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Jonny Summers-Muir</span><span class="text-foreground-lighter mb-0 text-xs">Product Design</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Supabase Branching" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flwx-supabase-branching%2Fbranching-thumb.png&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flwx-supabase-branching%2Fbranching-thumb.png&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flwx-supabase-branching%2Fbranching-thumb.png&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flwx-supabase-branching%2Fbranching-thumb.png&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flwx-supabase-branching%2Fbranching-thumb.png&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flwx-supabase-branching%2Fbranching-thumb.png&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flwx-supabase-branching%2Fbranching-thumb.png&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flwx-supabase-branching%2Fbranching-thumb.png&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flwx-supabase-branching%2Fbranching-thumb.png&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flwx-supabase-branching%2Fbranching-thumb.png&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flwx-supabase-branching%2Fbranching-thumb.png&amp;w=3840&amp;q=100"/></div><p>A few months ago we mentioned that we were working on Branching with a (somewhat ambitious) early-access form.</p>
<p>Today we are rolling out access to early-access subscribers. Internally, we were hoping to make this public access for this Launch Week but, well, <a href="https://supabase.com/blog/supabase-local-dev#supabase-branching-is-hard"><em>we did say this was hard</em></a>.</p>
<p>We&#x27;re operating on a first-signed-up, first-served basis, rolling it out in batches to paid orgs who registered for early access.</p>
<div class="video-container"><iframe class="w-full" src="https://www.youtube-nocookie.com/embed/peXKxavGnBo" title="YouTube video player" allow="accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share"></iframe></div>
<h2 id="whats-branching" class="group scroll-mt-24">What&#x27;s Branching?<a href="#whats-branching" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>At some point during development, you will probably need to experiment with your Postgres database. Today that&#x27;s possible on your local development machine using the Supabase CLI. When you run <code class="short-inline-codeblock">supabase start</code> with the CLI to get the entire Supabase stack running locally. You can play around with ideas and run <code class="short-inline-codeblock">supabase db reset</code> whenever you want to start again. When you want to capture your changes in a database migration, you can run <code class="short-inline-codeblock">supabase db diff</code>.</p>
<p>Branching is a natural extension of this, but instead of experimenting with just a <em>local</em> database you also get <em>remote</em> database. You continue to use the workflow above, and then when you commit your changes to Git we&#x27;ll run them on a Supabase Preview Branch.</p>
<!-- -->
<p>Each Git branch has a corresponding Supabase Preview, which automatically updates whenever you push an update. The rest of the workflow should feel familiar: when you merge a Pull Request into your main Git branch, Supabase will run your database migrations inside your Production database.</p>
<p>Your project&#x27;s Preview Branches are designed with safety in mind. They are isolated instances, each with a distinct set of API keys and passwords. Each instance contains every Supabase feature: a Postgres database, Auth, File Storage, Realtime, Edge Functions, and Data APIs.</p>
<!-- -->
<p>Even in relaxed developer environments, if one of your team accidentally leaks a key it won&#x27;t affect your Production branch.</p>
<h3 id="support-for-vercel-previews" class="group scroll-mt-24">Support for Vercel Previews<a href="#support-for-vercel-previews" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We&#x27;ve designed Supabase Branching to work perfectly with Vercel&#x27;s <a href="https://vercel.com/features/previews">Preview</a> deployments. This means that you get an <em>entire stack</em> with Branching.</p>
<!-- -->
<p>We&#x27;ve made several improvements to our <a href="https://vercel.com/integrations/supabase">Vercel Integration</a> to make the Vercel experience seamless. For example, since we provide distinct, secure database credentials for every Supabase Preview Branch, we automatically populate the environment variables on Vercel with the connection secrets your app needs to connect to the Preview Branch.</p>
<!-- -->
<h3 id="developing-on-the-hosted-preview-branch" class="group scroll-mt-24">Developing on the hosted Preview Branch<a href="#developing-on-the-hosted-preview-branch" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>One of the most-loved features of Supabase is the dashboard. Even if we <a href="https://supabase.com/docs/guides/platform/maturity-model#in-production">beg</a>, it seems that developers simply want to use it for everything - even in production.</p>
<p>The cool thing about Branching is that every Supabase Preview can be managed from the Dashboard. You can make schema changes, access the SQL Editor, and use the <a href="https://supabase.com/blog/studio-introducing-assistant">new AI Assistant</a>. Once you&#x27;re happy with your changes, you simply run <code class="short-inline-codeblock">supabase db diff</code> on your local machine to pull the changes and you can commit them to Git.</p>
<p>Just note that we <em>still</em> want you to develop locally! You should treat the Preview Branches <a href="https://devops.stackexchange.com/questions/653/what-is-the-definition-of-cattle-not-pets">like cattle, not pets</a>. Your Preview changes can be wiped at any time if one of your team pushes a destructive migration.</p>
<h3 id="database-migrations" class="group scroll-mt-24">Database migrations<a href="#database-migrations" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We&#x27;ve developed Branching to work with a Git provider, starting with GitHub.</p>
<p>Our <a href="https://github.com/apps/supabase">GitHub app</a> observes changes within a connected GitHub repository. When you open a Pull Request, it launches a Preview Branch and runs the migrations in <code class="short-inline-codeblock">./supabase/migrations</code>. If there are any errors they are logged to the <a href="https://docs.github.com/en/rest/checks/runs?apiVersion=2022-11-28">Check Run</a> associated with that git commit. When all checks turn green, your new Preview Branch is ready to use.</p>
<p>When you push a new migration file to the Git branch, the app runs it incrementally in your Preview Branch. This allows you to verify schema changes easily on existing seed data.</p>
<p>Finally, when you merge that PR, the app runs the new migrations on your Production environment. If you have other PRs already open, make sure to update those migration files to a later timestamp than the ones in the Production branch following a <a href="https://supabase.com/docs/guides/cli/managing-environments">standard migration practice</a>.</p>
<h3 id="data-seeding" class="group scroll-mt-24">Data seeding<a href="#data-seeding" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>You can seed your Preview branch in the same way that you <a href="https://supabase.com/docs/guides/cli/seeding-your-database">seed your local development environment</a>. Just add <code class="short-inline-codeblock">./supabase/seed.sql</code> in your repo and the seed script will run when the Preview Branch is created.</p>
<p>Optionally, you can reset the database by running <code class="short-inline-codeblock">supabase db reset --db-url &lt;branch-connection-string /&gt;</code>. The branch connection string can be retrieved using your Personal Access Token with Supabase CLI&#x27;s <a href="https://supabase.com/docs/reference/cli/supabase-branches-get">branch management</a> commands.</p>
<p>We&#x27;re investigating data masking techniques with a copy-on-write system so that you can emulate a production workload inside your Preview Branches. We plan for this to work with File Storage too.</p>
<div role="alert" class="relative w-full text-sm rounded-lg p-4 [&amp;&gt;svg~*]:pl-10 [&amp;&gt;svg+div]:translate-y-[-3px] [&amp;&gt;svg]:absolute [&amp;&gt;svg]:left-4 [&amp;&gt;svg]:top-4 [&amp;&gt;svg]:w-[23px] [&amp;&gt;svg]:h-[23px] [&amp;&gt;svg]:p-1 [&amp;&gt;svg]:flex [&amp;&gt;svg]:rounded text-foreground [&amp;&gt;svg]:text-background mb-2 [&amp;&gt;svg]:bg-foreground-muted bg-surface-200/25 border border-default"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 21 20" class="w-6 h-6" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M0.625 9.8252C0.625 4.44043 4.99023 0.0751953 10.375 0.0751953C15.7598 0.0751953 20.125 4.44043 20.125 9.8252C20.125 15.21 15.7598 19.5752 10.375 19.5752C4.99023 19.5752 0.625 15.21 0.625 9.8252ZM9.3584 4.38135C9.45117 4.28857 9.55518 4.20996 9.66699 4.14648C9.88086 4.02539 10.1245 3.96045 10.375 3.96045C10.5845 3.96045 10.7896 4.00586 10.9766 4.09229C11.1294 4.1626 11.2705 4.26025 11.3916 4.38135C11.6611 4.65088 11.8125 5.0166 11.8125 5.39795C11.8125 5.5249 11.7959 5.6499 11.7637 5.77002C11.6987 6.01172 11.5718 6.23438 11.3916 6.41455C11.1221 6.68408 10.7563 6.83545 10.375 6.83545C9.99365 6.83545 9.62793 6.68408 9.3584 6.41455C9.08887 6.14502 8.9375 5.7793 8.9375 5.39795C8.9375 5.29492 8.94873 5.19287 8.97021 5.09375C9.02783 4.82568 9.16162 4.57812 9.3584 4.38135ZM10.375 15.6899C10.0933 15.6899 9.82275 15.5781 9.62354 15.3789C9.42432 15.1797 9.3125 14.9092 9.3125 14.6274V9.31494C9.3125 9.0332 9.42432 8.7627 9.62354 8.56348C9.82275 8.36426 10.0933 8.25244 10.375 8.25244C10.6567 8.25244 10.9272 8.36426 11.1265 8.56348C11.3257 8.7627 11.4375 9.0332 11.4375 9.31494V14.6274C11.4375 14.7944 11.3979 14.9575 11.3242 15.104C11.2739 15.2046 11.2075 15.2979 11.1265 15.3789C10.9272 15.5781 10.6567 15.6899 10.375 15.6899Z"></path></svg><h5 class="mb-1 text mt-0.5 flex gap-3 text-sm [&amp;_p]:mb-1.5 [&amp;_p]:mt-0">Testing with product workloads today?</h5><div class="text-sm [&amp;_p]:leading-relaxed text-foreground-light font-normal [&amp;_p]:mb-1.5 [&amp;_p]:mt-0"><p>If you need to test with production workloads today, check out <a href="https://www.snaplet.dev/">Snaplet</a> and <a href="http://Postgres.ai">Postgres.ai</a>. Both are great partners of Supabase.</p></div></div>
<h2 id="future-considerations" class="group scroll-mt-24">Future considerations<a href="#future-considerations" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>That&#x27;s already a lot for Branching v0. Branching will be a core part of the developer workflow in the future. These are the themes we&#x27;ll explore next:</p>
<h3 id="declarative-config" class="group scroll-mt-24">Declarative config<a href="#declarative-config" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We&#x27;re still working on “configuration in code”. For example, you might want to try a different Google Auth in your Preview Branch than the one you use in Product. This would be a lot easier if the code was declarative, inside the <a href="https://supabase.com/docs/guides/cli/config">config.toml</a> file.</p>
<h3 id="automatic-dashboard-commits" class="group scroll-mt-24">Automatic dashboard commits<a href="#automatic-dashboard-commits" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>In the current version, when you use the dashboard to create a change on a Preview Branch, you need to run <code class="short-inline-codeblock">db diff</code> locally to pull that change into your Git repository. We plan to work on a feature to automatically capture your changes in a Git repo that you&#x27;ve connected.</p>
<h3 id="extended-seeding-behavior" class="group scroll-mt-24">Extended seeding behavior<a href="#extended-seeding-behavior" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>There are a multitude of different strategies for populating seed data. We&#x27;ve dabbled with AI to generate seed data, which was fun. We also like the approach of <a href="https://postgresql-anonymizer.readthedocs.io/en/stable/">postgresql-anonymizer</a> and <a href="https://docs.snaplet.dev/recipes/supabase">Snaplet</a>, which specialize in cloning production data while anonymizing the data for safe development.</p>
<h3 id="copy-on-write" class="group scroll-mt-24">Copy-on-write<a href="#copy-on-write" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We have something in development :). CoW means you can branch from database snapshot and then run tests on “production-like” workloads. This is the approach that <a href="http://Postgres.ai">Postgres.ai</a> uses. As we mentioned above, we need to figure out an approach that also works with <a href="../storage.html">File Storage</a>.</p>
<h2 id="interested-in-using-branching" class="group scroll-mt-24">Interested in using Branching?<a href="#interested-in-using-branching" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We&#x27;ll be onboarding organizations in batches over the next few weeks, and working with these early users on Pricing.</p>
<p><strong>Update 17th January 2024</strong> - Early access for Branching is now closed for the foreseeable future. We are now working hard towards releasing a public beta.</p>
<p>Check out the <a href="../docs/guides/deployment/branching.html">Branching docs</a> and also if you have any feedback, <a href="https://github.com/orgs/supabase/discussions/18937">you can join the discussion</a>.</p>
</div></article><div class="w-full border bg-alternative-200 flex flex-col rounded-lg text-foreground-lighter mt-12"><div class="w-full p-4 flex justify-between items-center"><a class="flex items-center gap-1.5 leading-none uppercase text-xs" href="../launch-week.html"><span class="text-foreground tracking-[1px]">Launch Week</span> <img alt="Supabase Launch Week X icon" loading="lazy" width="16" height="16" decoding="async" data-nimg="1" class="w-3 h-3" style="color:transparent" src="https://supabase.com/images/launchweek/lwx/logos/lwx_logo.svg"/></a><div class="font-mono uppercase tracking-wide text-xs">11-15 Dec</div></div><div class="pb-4 border-t p-4"><div class="font-mono uppercase text-xs text-foreground tracking-wide mb-3">Main Stage</div><ul class="flex flex-col gap-2"><ol><a class="group flex py-1 gap-2 hover:text-foreground" href="https://supabase.com/blog/studio-introducing-assistant"><span class="shrink-0 text-sm font-mono uppercase leading-6">Day <!-- -->1<!-- --> -</span><span class="leading-6">Supabase Studio: introducing an <strong>AI Assistant</strong>,<!-- --> <strong>Postgres roles</strong>, and <strong>user impersonation</strong></span></a></ol><ol><a class="group flex py-1 gap-2 hover:text-foreground" href="https://supabase.com/blog/edge-functions-node-npm"><span class="shrink-0 text-sm font-mono uppercase leading-6">Day <!-- -->2<!-- --> -</span><span class="leading-6">Edge Functions: <strong>Node</strong> and native <strong>npm</strong> compatibility</span></a></ol><ol><a class="group flex py-1 gap-2 hover:text-foreground" href="supabase-branching.html"><span class="shrink-0 text-sm font-mono uppercase leading-6">Day <!-- -->3<!-- --> -</span><span class="leading-6">Introducing Supabase <strong>Branching</strong>, a Postgres database for every pull request</span></a></ol><ol><a class="group flex py-1 gap-2 hover:text-foreground" href="supabase-auth-identity-linking-hooks.html"><span class="shrink-0 text-sm font-mono uppercase leading-6">Day <!-- -->4<!-- --> -</span><span class="leading-6">Supabase Auth: <strong>Identity Linking</strong>, <strong>Session Control</strong>,<!-- --> <strong>Password Protection</strong> and <strong>Hooks</strong></span></a></ol><ol><a class="group flex py-1 gap-2 hover:text-foreground" href="introducing-read-replicas.html"><span class="shrink-0 text-sm font-mono uppercase leading-6">Day <!-- -->5<!-- --> -</span><span class="leading-6">Introducing <strong>Read Replicas</strong> for low latency</span></a></ol></ul></div><div class="w-[calc(100%+2px)] bg-surface-100 flex flex-col gap-2 -m-px border rounded-lg"><div class="p-4"><div class="font-mono uppercase text-xs text-foreground tracking-wide">Build Stage</div><ul class="flex flex-col gap-2 mt-4"><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="https://supabase.productions/"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->1<!-- --> -</span>Supabase Album</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="https://supabase.com/blog/postgres-language-server-implementing-parser"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->2<!-- --> -</span>Postgres Language Server</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="https://supabase.com/blog/how-design-works-at-supabase"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->3<!-- --> -</span>Design at Supabase</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="https://github.com/supabase/supabase-grafana"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->4<!-- --> -</span>Supabase Grafana</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="https://supabase.com/blog/pg-graphql-postgres-functions"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->5<!-- --> -</span>pg_graphql: Postgres functions</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="postgrest-12.html"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->6<!-- --> -</span>PostgREST 12</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="https://supabase.com/blog/supavisor-postgres-connection-pooler"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->7<!-- --> -</span>Supavisor 1.0</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="supabase-wrappers-v02.html"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->8<!-- --> -</span>Supabase Wrappers v0.2</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="client-libraries-v2.html"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->9<!-- --> -</span>Supabase Libraries V2</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="postgres-on-fly-by-supabase.html"><span class="relative"><span class="font-mono uppercase mr-2">10<!-- --> -</span>Supabase x Fly.io</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="launch-week-x-best-launches.html"><span class="relative"><span class="font-mono uppercase mr-2">11<!-- --> -</span>Top 10 Launches of LWX</span></a></ol><ol class="border-t pt-4 mt-2"><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="https://supabase.com/blog/supabase-hackathon-lwx">Supabase Launch Week X Hackathon</a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="https://supabase.com/blog/community-meetups-lwx">Supabase Launch Week X Community Meetups</a></ol></ul></div></div></div><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-branching&amp;text=Supabase%20Branching"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-branching&amp;text=Supabase%20Branching"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-branching&amp;t=Supabase%20Branching"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="postgrest-12.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">PostgREST 12</h4><p class="small">13 December 2023</p></div></div></div></div></a></div><div><a href="https://supabase.com/blog/supavisor-postgres-connection-pooler"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Supavisor 1.0: a scalable connection pooler for Postgres</h4><p class="small">13 December 2023</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/launch-week"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">launch-week</div></a><a href="https://supabase.com/blog/tags/supavisor"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">supavisor</div></a><a href="https://supabase.com/blog/tags/postgres"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">postgres</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#whats-branching">What&#x27;s Branching?</a>
<ul>
<li><a href="#support-for-vercel-previews">Support for Vercel Previews</a></li>
<li><a href="#developing-on-the-hosted-preview-branch">Developing on the hosted Preview Branch</a></li>
<li><a href="#database-migrations">Database migrations</a></li>
<li><a href="#data-seeding">Data seeding</a></li>
</ul>
</li>
<li><a href="#future-considerations">Future considerations</a>
<ul>
<li><a href="#declarative-config">Declarative config</a></li>
<li><a href="#automatic-dashboard-commits">Automatic dashboard commits</a></li>
<li><a href="#extended-seeding-behavior">Extended seeding behavior</a></li>
<li><a href="#copy-on-write">Copy-on-write</a></li>
</ul>
</li>
<li><a href="#interested-in-using-branching">Interested in using Branching?</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-branching&amp;text=Supabase%20Branching"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-branching&amp;text=Supabase%20Branching"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-branching&amp;t=Supabase%20Branching"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"postgrest-12","title":"PostgREST 12","description":"PostgREST 12 is out and we take a look at some of the major new features like JWT Caching and Aggregate Functions","launchweek":"x","categories":["product"],"tags":["launch-week","postgrest","planetpg"],"date":"2023-12-13","toc_depth":3,"author":"oli_rice","image":"lwx-postgrest-12/postgrest12-OG.png","thumb":"lwx-postgrest-12/postgrest12-thumb.png","formattedDate":"13 December 2023","readingTime":"4 minute read","url":"/blog/postgrest-12","path":"/blog/postgrest-12"},"nextPost":{"slug":"supavisor-postgres-connection-pooler","title":"Supavisor 1.0: a scalable connection pooler for Postgres","description":"Supavisor is now used across all projects, providing a scalable and cloud-native Postgres connection pooler that can handle millions of connections","launchweek":"X","categories":["product"],"tags":["launch-week","supavisor","postgres","planetpg"],"date":"2023-12-13","toc_depth":3,"author":"stas","image":"lwx-supavisor/supavisor-og.png","thumb":"lwx-supavisor/supavisor-thumb.png","formattedDate":"13 December 2023","readingTime":"7 minute read","url":"/blog/supavisor-postgres-connection-pooler","path":"/blog/supavisor-postgres-connection-pooler"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-branching","source":"\nA few months ago we mentioned that we were working on Branching with a (somewhat ambitious) early-access form.\n\nToday we are rolling out access to early-access subscribers. Internally, we were hoping to make this public access for this Launch Week but, well, [_we did say this was hard_](/blog/supabase-local-dev#supabase-branching-is-hard).\n\nWe're operating on a first-signed-up, first-served basis, rolling it out in batches to paid orgs who registered for early access.\n\n\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/peXKxavGnBo\"\n    title=\"YouTube video player\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"\n    allowfullscreen\n  /\u003e\n\u003c/div\u003e\n\n## What's Branching?\n\nAt some point during development, you will probably need to experiment with your Postgres database. Today that's possible on your local development machine using the Supabase CLI. When you run `supabase start` with the CLI to get the entire Supabase stack running locally. You can play around with ideas and run `supabase db reset` whenever you want to start again. When you want to capture your changes in a database migration, you can run `supabase db diff`.\n\nBranching is a natural extension of this, but instead of experimenting with just a _local_ database you also get _remote_ database. You continue to use the workflow above, and then when you commit your changes to Git we'll run them on a Supabase Preview Branch.\n\n\u003cImg\n  alt=\"Each Git branch has a corresponding Supabase Preview.\"\n  src={{\n    light: '/images/blog/lwx-supabase-branching/feat-branches-examples--light.png',\n    dark: '/images/blog/lwx-supabase-branching/feat-branches-examples--dark.png',\n  }}\n  caption=\"Each Git branch has a corresponding Supabase Preview.\"\n  captionAlign=\"left\"\n/\u003e\n\nEach Git branch has a corresponding Supabase Preview, which automatically updates whenever you push an update. The rest of the workflow should feel familiar: when you merge a Pull Request into your main Git branch, Supabase will run your database migrations inside your Production database.\n\nYour project's Preview Branches are designed with safety in mind. They are isolated instances, each with a distinct set of API keys and passwords. Each instance contains every Supabase feature: a Postgres database, Auth, File Storage, Realtime, Edge Functions, and Data APIs.\n\n\u003cImg\n  alt=\"Every Supabase Preview is a dedicated instance, with a full suite of Supabase services.\"\n  src={{\n    light: '/images/blog/lwx-supabase-branching/isolated-instances--light.png',\n    dark: '/images/blog/lwx-supabase-branching/isolated-instances--dark.png',\n  }}\n  caption=\"Every Supabase Preview is a dedicated instance, with a full suite of Supabase services.\"\n  captionAlign=\"left\"\n/\u003e\n\nEven in relaxed developer environments, if one of your team accidentally leaks a key it won't affect your Production branch.\n\n### Support for Vercel Previews\n\nWe've designed Supabase Branching to work perfectly with Vercel's [Preview](https://vercel.com/features/previews) deployments. This means that you get an _entire stack_ with Branching.\n\n\u003cImg\n  alt=\"Vercel will build your preview deployments, and your preview deployment can connect to the Supabase services on your Preview Branch.\"\n  src={{\n    light: '/images/blog/lwx-supabase-branching/vercel-support--light.png',\n    dark: '/images/blog/lwx-supabase-branching/vercel-support--dark.png',\n  }}\n  caption=\"Vercel will build your preview deployments, and your preview deployment can connect to the Supabase services on your Preview Branch.\"\n  captionAlign=\"left\"\n/\u003e\n\nWe've made several improvements to our [Vercel Integration](https://vercel.com/integrations/supabase) to make the Vercel experience seamless. For example, since we provide distinct, secure database credentials for every Supabase Preview Branch, we automatically populate the environment variables on Vercel with the connection secrets your app needs to connect to the Preview Branch.\n\n\u003cImg\n  alt=\"Vercel environment variables settings page, showing the Git branch based env vars.\"\n  src={{\n    light: '/images/blog/lwx-supabase-branching/vercel-env-var-settings--light.png',\n    dark: '/images/blog/lwx-supabase-branching/vercel-env-var-settings--dark.png',\n  }}\n  wide={true}\n  caption=\"Vercel environment variables settings page, showing the Git branch based env vars.\"\n  captionAlign=\"left\"\n/\u003e\n\n### Developing on the hosted Preview Branch\n\nOne of the most-loved features of Supabase is the dashboard. Even if we [beg](/docs/guides/platform/maturity-model#in-production), it seems that developers simply want to use it for everything - even in production.\n\nThe cool thing about Branching is that every Supabase Preview can be managed from the Dashboard. You can make schema changes, access the SQL Editor, and use the [new AI Assistant](/blog/studio-introducing-assistant). Once you're happy with your changes, you simply run `supabase db diff` on your local machine to pull the changes and you can commit them to Git.\n\nJust note that we _still_ want you to develop locally! You should treat the Preview Branches [like cattle, not pets](https://devops.stackexchange.com/questions/653/what-is-the-definition-of-cattle-not-pets). Your Preview changes can be wiped at any time if one of your team pushes a destructive migration.\n\n### Database migrations\n\nWe've developed Branching to work with a Git provider, starting with GitHub.\n\nOur [GitHub app](https://github.com/apps/supabase) observes changes within a connected GitHub repository. When you open a Pull Request, it launches a Preview Branch and runs the migrations in `./supabase/migrations`. If there are any errors they are logged to the [Check Run](https://docs.github.com/en/rest/checks/runs?apiVersion=2022-11-28) associated with that git commit. When all checks turn green, your new Preview Branch is ready to use.\n\nWhen you push a new migration file to the Git branch, the app runs it incrementally in your Preview Branch. This allows you to verify schema changes easily on existing seed data.\n\nFinally, when you merge that PR, the app runs the new migrations on your Production environment. If you have other PRs already open, make sure to update those migration files to a later timestamp than the ones in the Production branch following a [standard migration practice](/docs/guides/cli/managing-environments).\n\n### Data seeding\n\nYou can seed your Preview branch in the same way that you [seed your local development environment](/docs/guides/cli/seeding-your-database). Just add `./supabase/seed.sql` in your repo and the seed script will run when the Preview Branch is created.\n\nOptionally, you can reset the database by running `supabase db reset --db-url \u003cbranch-connection-string\u003e`. The branch connection string can be retrieved using your Personal Access Token with Supabase CLI's [branch management](/docs/reference/cli/supabase-branches-get) commands.\n\nWe're investigating data masking techniques with a copy-on-write system so that you can emulate a production workload inside your Preview Branches. We plan for this to work with File Storage too.\n\n\u003cAdmonition type=\"note\" label=\"Testing with product workloads today?\"\u003e\n\nIf you need to test with production workloads today, check out [Snaplet](https://www.snaplet.dev/) and [Postgres.ai](http://Postgres.ai). Both are great partners of Supabase.\n\n\u003c/Admonition\u003e\n\n## Future considerations\n\nThat's already a lot for Branching v0. Branching will be a core part of the developer workflow in the future. These are the themes we'll explore next:\n\n### Declarative config\n\nWe're still working on “configuration in code”. For example, you might want to try a different Google Auth in your Preview Branch than the one you use in Product. This would be a lot easier if the code was declarative, inside the [config.toml](/docs/guides/cli/config) file.\n\n### Automatic dashboard commits\n\nIn the current version, when you use the dashboard to create a change on a Preview Branch, you need to run `db diff` locally to pull that change into your Git repository. We plan to work on a feature to automatically capture your changes in a Git repo that you've connected.\n\n### Extended seeding behavior\n\nThere are a multitude of different strategies for populating seed data. We've dabbled with AI to generate seed data, which was fun. We also like the approach of [postgresql-anonymizer](https://postgresql-anonymizer.readthedocs.io/en/stable/) and [Snaplet](https://docs.snaplet.dev/recipes/supabase), which specialize in cloning production data while anonymizing the data for safe development.\n\n### Copy-on-write\n\nWe have something in development :). CoW means you can branch from database snapshot and then run tests on “production-like” workloads. This is the approach that [Postgres.ai](http://Postgres.ai) uses. As we mentioned above, we need to figure out an approach that also works with [File Storage](/storage).\n\n## Interested in using Branching?\n\nWe'll be onboarding organizations in batches over the next few weeks, and working with these early users on Pricing.\n\n**Update 17th January 2024** - Early access for Branching is now closed for the foreseeable future. We are now working hard towards releasing a public beta.\n\nCheck out the [Branching docs](/docs/guides/platform/branching) and also if you have any feedback, [you can join the discussion](https://github.com/orgs/supabase/discussions/18937).\n\n\u003cImg\n  src={{\n    light: '/images/blog/lwx-supabase-branching/branching-ui--light.png',\n    dark: '/images/blog/lwx-supabase-branching/branching-ui--dark.png',\n  }}\n  wide={true}\n  caption=\"New Branching UI in the Supabase dashboard\"\n  captionAlign=\"left\"\n/\u003e\n","title":"Supabase Branching","description":"A Postgres database for every Pull Request.","launchweek":"X","categories":["product"],"tags":["launch-week","supavisor","postgres"],"date":"2023-12-13","toc_depth":3,"author":"qiao,jonny","image":"lwx-supabase-branching/branching-og.png","thumb":"lwx-supabase-branching/branching-thumb.png","content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    a: \"a\",\n    em: \"em\",\n    h2: \"h2\",\n    code: \"code\",\n    h3: \"h3\",\n    strong: \"strong\"\n  }, _provideComponents(), props.components), {Img, Admonition} = _components;\n  if (!Admonition) _missingMdxReference(\"Admonition\", true);\n  if (!Img) _missingMdxReference(\"Img\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(_components.p, {\n      children: \"A few months ago we mentioned that we were working on Branching with a (somewhat ambitious) early-access form.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Today we are rolling out access to early-access subscribers. Internally, we were hoping to make this public access for this Launch Week but, well, \", _jsx(_components.a, {\n        href: \"/blog/supabase-local-dev#supabase-branching-is-hard\",\n        children: _jsx(_components.em, {\n          children: \"we did say this was hard\"\n        })\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We're operating on a first-signed-up, first-served basis, rolling it out in batches to paid orgs who registered for early access.\"\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"video-container\",\n      children: _jsx(\"iframe\", {\n        className: \"w-full\",\n        src: \"https://www.youtube-nocookie.com/embed/peXKxavGnBo\",\n        title: \"YouTube video player\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\",\n        allowfullscreen: true\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"whats-branching\",\n      children: \"What's Branching?\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"At some point during development, you will probably need to experiment with your Postgres database. Today that's possible on your local development machine using the Supabase CLI. When you run \", _jsx(_components.code, {\n        children: \"supabase start\"\n      }), \" with the CLI to get the entire Supabase stack running locally. You can play around with ideas and run \", _jsx(_components.code, {\n        children: \"supabase db reset\"\n      }), \" whenever you want to start again. When you want to capture your changes in a database migration, you can run \", _jsx(_components.code, {\n        children: \"supabase db diff\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Branching is a natural extension of this, but instead of experimenting with just a \", _jsx(_components.em, {\n        children: \"local\"\n      }), \" database you also get \", _jsx(_components.em, {\n        children: \"remote\"\n      }), \" database. You continue to use the workflow above, and then when you commit your changes to Git we'll run them on a Supabase Preview Branch.\"]\n    }), \"\\n\", _jsx(Img, {\n      alt: \"Each Git branch has a corresponding Supabase Preview.\",\n      src: {\n        light: '/images/blog/lwx-supabase-branching/feat-branches-examples--light.png',\n        dark: '/images/blog/lwx-supabase-branching/feat-branches-examples--dark.png'\n      },\n      caption: \"Each Git branch has a corresponding Supabase Preview.\",\n      captionAlign: \"left\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Each Git branch has a corresponding Supabase Preview, which automatically updates whenever you push an update. The rest of the workflow should feel familiar: when you merge a Pull Request into your main Git branch, Supabase will run your database migrations inside your Production database.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Your project's Preview Branches are designed with safety in mind. They are isolated instances, each with a distinct set of API keys and passwords. Each instance contains every Supabase feature: a Postgres database, Auth, File Storage, Realtime, Edge Functions, and Data APIs.\"\n    }), \"\\n\", _jsx(Img, {\n      alt: \"Every Supabase Preview is a dedicated instance, with a full suite of Supabase services.\",\n      src: {\n        light: '/images/blog/lwx-supabase-branching/isolated-instances--light.png',\n        dark: '/images/blog/lwx-supabase-branching/isolated-instances--dark.png'\n      },\n      caption: \"Every Supabase Preview is a dedicated instance, with a full suite of Supabase services.\",\n      captionAlign: \"left\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Even in relaxed developer environments, if one of your team accidentally leaks a key it won't affect your Production branch.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"support-for-vercel-previews\",\n      children: \"Support for Vercel Previews\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We've designed Supabase Branching to work perfectly with Vercel's \", _jsx(_components.a, {\n        href: \"https://vercel.com/features/previews\",\n        children: \"Preview\"\n      }), \" deployments. This means that you get an \", _jsx(_components.em, {\n        children: \"entire stack\"\n      }), \" with Branching.\"]\n    }), \"\\n\", _jsx(Img, {\n      alt: \"Vercel will build your preview deployments, and your preview deployment can connect to the Supabase services on your Preview Branch.\",\n      src: {\n        light: '/images/blog/lwx-supabase-branching/vercel-support--light.png',\n        dark: '/images/blog/lwx-supabase-branching/vercel-support--dark.png'\n      },\n      caption: \"Vercel will build your preview deployments, and your preview deployment can connect to the Supabase services on your Preview Branch.\",\n      captionAlign: \"left\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We've made several improvements to our \", _jsx(_components.a, {\n        href: \"https://vercel.com/integrations/supabase\",\n        children: \"Vercel Integration\"\n      }), \" to make the Vercel experience seamless. For example, since we provide distinct, secure database credentials for every Supabase Preview Branch, we automatically populate the environment variables on Vercel with the connection secrets your app needs to connect to the Preview Branch.\"]\n    }), \"\\n\", _jsx(Img, {\n      alt: \"Vercel environment variables settings page, showing the Git branch based env vars.\",\n      src: {\n        light: '/images/blog/lwx-supabase-branching/vercel-env-var-settings--light.png',\n        dark: '/images/blog/lwx-supabase-branching/vercel-env-var-settings--dark.png'\n      },\n      wide: true,\n      caption: \"Vercel environment variables settings page, showing the Git branch based env vars.\",\n      captionAlign: \"left\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"developing-on-the-hosted-preview-branch\",\n      children: \"Developing on the hosted Preview Branch\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"One of the most-loved features of Supabase is the dashboard. Even if we \", _jsx(_components.a, {\n        href: \"/docs/guides/platform/maturity-model#in-production\",\n        children: \"beg\"\n      }), \", it seems that developers simply want to use it for everything - even in production.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The cool thing about Branching is that every Supabase Preview can be managed from the Dashboard. You can make schema changes, access the SQL Editor, and use the \", _jsx(_components.a, {\n        href: \"/blog/studio-introducing-assistant\",\n        children: \"new AI Assistant\"\n      }), \". Once you're happy with your changes, you simply run \", _jsx(_components.code, {\n        children: \"supabase db diff\"\n      }), \" on your local machine to pull the changes and you can commit them to Git.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Just note that we \", _jsx(_components.em, {\n        children: \"still\"\n      }), \" want you to develop locally! You should treat the Preview Branches \", _jsx(_components.a, {\n        href: \"https://devops.stackexchange.com/questions/653/what-is-the-definition-of-cattle-not-pets\",\n        children: \"like cattle, not pets\"\n      }), \". Your Preview changes can be wiped at any time if one of your team pushes a destructive migration.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"database-migrations\",\n      children: \"Database migrations\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We've developed Branching to work with a Git provider, starting with GitHub.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Our \", _jsx(_components.a, {\n        href: \"https://github.com/apps/supabase\",\n        children: \"GitHub app\"\n      }), \" observes changes within a connected GitHub repository. When you open a Pull Request, it launches a Preview Branch and runs the migrations in \", _jsx(_components.code, {\n        children: \"./supabase/migrations\"\n      }), \". If there are any errors they are logged to the \", _jsx(_components.a, {\n        href: \"https://docs.github.com/en/rest/checks/runs?apiVersion=2022-11-28\",\n        children: \"Check Run\"\n      }), \" associated with that git commit. When all checks turn green, your new Preview Branch is ready to use.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"When you push a new migration file to the Git branch, the app runs it incrementally in your Preview Branch. This allows you to verify schema changes easily on existing seed data.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Finally, when you merge that PR, the app runs the new migrations on your Production environment. If you have other PRs already open, make sure to update those migration files to a later timestamp than the ones in the Production branch following a \", _jsx(_components.a, {\n        href: \"/docs/guides/cli/managing-environments\",\n        children: \"standard migration practice\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"data-seeding\",\n      children: \"Data seeding\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You can seed your Preview branch in the same way that you \", _jsx(_components.a, {\n        href: \"/docs/guides/cli/seeding-your-database\",\n        children: \"seed your local development environment\"\n      }), \". Just add \", _jsx(_components.code, {\n        children: \"./supabase/seed.sql\"\n      }), \" in your repo and the seed script will run when the Preview Branch is created.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Optionally, you can reset the database by running \", _jsx(_components.code, {\n        children: \"supabase db reset --db-url \u003cbranch-connection-string /\u003e\"\n      }), \". The branch connection string can be retrieved using your Personal Access Token with Supabase CLI's \", _jsx(_components.a, {\n        href: \"/docs/reference/cli/supabase-branches-get\",\n        children: \"branch management\"\n      }), \" commands.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We're investigating data masking techniques with a copy-on-write system so that you can emulate a production workload inside your Preview Branches. We plan for this to work with File Storage too.\"\n    }), \"\\n\", _jsx(Admonition, {\n      type: \"note\",\n      label: \"Testing with product workloads today?\",\n      children: _jsxs(_components.p, {\n        children: [\"If you need to test with production workloads today, check out \", _jsx(_components.a, {\n          href: \"https://www.snaplet.dev/\",\n          children: \"Snaplet\"\n        }), \" and \", _jsx(_components.a, {\n          href: \"http://Postgres.ai\",\n          children: \"Postgres.ai\"\n        }), \". Both are great partners of Supabase.\"]\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"future-considerations\",\n      children: \"Future considerations\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"That's already a lot for Branching v0. Branching will be a core part of the developer workflow in the future. These are the themes we'll explore next:\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"declarative-config\",\n      children: \"Declarative config\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We're still working on “configuration in code”. For example, you might want to try a different Google Auth in your Preview Branch than the one you use in Product. This would be a lot easier if the code was declarative, inside the \", _jsx(_components.a, {\n        href: \"/docs/guides/cli/config\",\n        children: \"config.toml\"\n      }), \" file.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"automatic-dashboard-commits\",\n      children: \"Automatic dashboard commits\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In the current version, when you use the dashboard to create a change on a Preview Branch, you need to run \", _jsx(_components.code, {\n        children: \"db diff\"\n      }), \" locally to pull that change into your Git repository. We plan to work on a feature to automatically capture your changes in a Git repo that you've connected.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"extended-seeding-behavior\",\n      children: \"Extended seeding behavior\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"There are a multitude of different strategies for populating seed data. We've dabbled with AI to generate seed data, which was fun. We also like the approach of \", _jsx(_components.a, {\n        href: \"https://postgresql-anonymizer.readthedocs.io/en/stable/\",\n        children: \"postgresql-anonymizer\"\n      }), \" and \", _jsx(_components.a, {\n        href: \"https://docs.snaplet.dev/recipes/supabase\",\n        children: \"Snaplet\"\n      }), \", which specialize in cloning production data while anonymizing the data for safe development.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"copy-on-write\",\n      children: \"Copy-on-write\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We have something in development :). CoW means you can branch from database snapshot and then run tests on “production-like” workloads. This is the approach that \", _jsx(_components.a, {\n        href: \"http://Postgres.ai\",\n        children: \"Postgres.ai\"\n      }), \" uses. As we mentioned above, we need to figure out an approach that also works with \", _jsx(_components.a, {\n        href: \"/storage\",\n        children: \"File Storage\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"interested-in-using-branching\",\n      children: \"Interested in using Branching?\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We'll be onboarding organizations in batches over the next few weeks, and working with these early users on Pricing.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.strong, {\n        children: \"Update 17th January 2024\"\n      }), \" - Early access for Branching is now closed for the foreseeable future. We are now working hard towards releasing a public beta.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Check out the \", _jsx(_components.a, {\n        href: \"/docs/guides/platform/branching\",\n        children: \"Branching docs\"\n      }), \" and also if you have any feedback, \", _jsx(_components.a, {\n        href: \"https://github.com/orgs/supabase/discussions/18937\",\n        children: \"you can join the discussion\"\n      }), \".\"]\n    }), \"\\n\", _jsx(Img, {\n      src: {\n        light: '/images/blog/lwx-supabase-branching/branching-ui--light.png',\n        dark: '/images/blog/lwx-supabase-branching/branching-ui--dark.png'\n      },\n      wide: true,\n      caption: \"New Branching UI in the Supabase dashboard\",\n      captionAlign: \"left\"\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"What's Branching?","slug":"whats-branching","lvl":2,"i":0,"seen":0},{"content":"Support for Vercel Previews","slug":"support-for-vercel-previews","lvl":3,"i":1,"seen":0},{"content":"Developing on the hosted Preview Branch","slug":"developing-on-the-hosted-preview-branch","lvl":3,"i":2,"seen":0},{"content":"Database migrations","slug":"database-migrations","lvl":3,"i":3,"seen":0},{"content":"Data seeding","slug":"data-seeding","lvl":3,"i":4,"seen":0},{"content":"Future considerations","slug":"future-considerations","lvl":2,"i":5,"seen":0},{"content":"Declarative config","slug":"declarative-config","lvl":3,"i":6,"seen":0},{"content":"Automatic dashboard commits","slug":"automatic-dashboard-commits","lvl":3,"i":7,"seen":0},{"content":"Extended seeding behavior","slug":"extended-seeding-behavior","lvl":3,"i":8,"seen":0},{"content":"Copy-on-write","slug":"copy-on-write","lvl":3,"i":9,"seen":0},{"content":"Interested in using Branching?","slug":"interested-in-using-branching","lvl":2,"i":10,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,2],"level":0},{"type":"inline","content":"A few months ago we mentioned that we were working on Branching with a (somewhat ambitious) early-access form.","level":1,"lines":[1,2],"children":[{"type":"text","content":"A few months ago we mentioned that we were working on Branching with a (somewhat ambitious) early-access form.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[3,4],"level":0},{"type":"inline","content":"Today we are rolling out access to early-access subscribers. Internally, we were hoping to make this public access for this Launch Week but, well, [_we did say this was hard_](/blog/supabase-local-dev#supabase-branching-is-hard).","level":1,"lines":[3,4],"children":[{"type":"text","content":"Today we are rolling out access to early-access subscribers. Internally, we were hoping to make this public access for this Launch Week but, well, ","level":0},{"type":"link_open","href":"/blog/supabase-local-dev#supabase-branching-is-hard","title":"","level":0},{"type":"em_open","level":1},{"type":"text","content":"we did say this was hard","level":2},{"type":"em_close","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[5,6],"level":0},{"type":"inline","content":"We're operating on a first-signed-up, first-served basis, rolling it out in batches to paid orgs who registered for early access.","level":1,"lines":[5,6],"children":[{"type":"text","content":"We're operating on a first-signed-up, first-served basis, rolling it out in batches to paid orgs who registered for early access.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[7,16],"level":0},{"type":"inline","content":"\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/peXKxavGnBo\"\n    title=\"YouTube video player\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"\n    allowfullscreen\n  /\u003e\n\u003c/div\u003e","level":1,"lines":[7,16],"children":[{"type":"text","content":"\u003cdiv className=\"video-container\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ciframe","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"w-full\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://www.youtube-nocookie.com/embed/peXKxavGnBo\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"title=\"YouTube video player\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allowfullscreen","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[17,18],"level":0},{"type":"inline","content":"[What's Branching?](#whats-branching)","level":1,"lines":[17,18],"children":[{"type":"text","content":"What's Branching?","level":0}],"lvl":2,"i":0,"seen":0,"slug":"whats-branching"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[19,20],"level":0},{"type":"inline","content":"At some point during development, you will probably need to experiment with your Postgres database. Today that's possible on your local development machine using the Supabase CLI. When you run `supabase start` with the CLI to get the entire Supabase stack running locally. You can play around with ideas and run `supabase db reset` whenever you want to start again. When you want to capture your changes in a database migration, you can run `supabase db diff`.","level":1,"lines":[19,20],"children":[{"type":"text","content":"At some point during development, you will probably need to experiment with your Postgres database. Today that's possible on your local development machine using the Supabase CLI. When you run ","level":0},{"type":"code","content":"supabase start","block":false,"level":0},{"type":"text","content":" with the CLI to get the entire Supabase stack running locally. You can play around with ideas and run ","level":0},{"type":"code","content":"supabase db reset","block":false,"level":0},{"type":"text","content":" whenever you want to start again. When you want to capture your changes in a database migration, you can run ","level":0},{"type":"code","content":"supabase db diff","block":false,"level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[21,22],"level":0},{"type":"inline","content":"Branching is a natural extension of this, but instead of experimenting with just a _local_ database you also get _remote_ database. You continue to use the workflow above, and then when you commit your changes to Git we'll run them on a Supabase Preview Branch.","level":1,"lines":[21,22],"children":[{"type":"text","content":"Branching is a natural extension of this, but instead of experimenting with just a ","level":0},{"type":"em_open","level":0},{"type":"text","content":"local","level":1},{"type":"em_close","level":0},{"type":"text","content":" database you also get ","level":0},{"type":"em_open","level":0},{"type":"text","content":"remote","level":1},{"type":"em_close","level":0},{"type":"text","content":" database. You continue to use the workflow above, and then when you commit your changes to Git we'll run them on a Supabase Preview Branch.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[23,32],"level":0},{"type":"inline","content":"\u003cImg\n  alt=\"Each Git branch has a corresponding Supabase Preview.\"\n  src={{\n    light: '/images/blog/lwx-supabase-branching/feat-branches-examples--light.png',\n    dark: '/images/blog/lwx-supabase-branching/feat-branches-examples--dark.png',\n  }}\n  caption=\"Each Git branch has a corresponding Supabase Preview.\"\n  captionAlign=\"left\"\n/\u003e","level":1,"lines":[23,32],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"Each Git branch has a corresponding Supabase Preview.\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src={{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"light: '/images/blog/lwx-supabase-branching/feat-branches-examples--light.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"dark: '/images/blog/lwx-supabase-branching/feat-branches-examples--dark.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"}}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"caption=\"Each Git branch has a corresponding Supabase Preview.\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"captionAlign=\"left\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[33,34],"level":0},{"type":"inline","content":"Each Git branch has a corresponding Supabase Preview, which automatically updates whenever you push an update. The rest of the workflow should feel familiar: when you merge a Pull Request into your main Git branch, Supabase will run your database migrations inside your Production database.","level":1,"lines":[33,34],"children":[{"type":"text","content":"Each Git branch has a corresponding Supabase Preview, which automatically updates whenever you push an update. The rest of the workflow should feel familiar: when you merge a Pull Request into your main Git branch, Supabase will run your database migrations inside your Production database.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[35,36],"level":0},{"type":"inline","content":"Your project's Preview Branches are designed with safety in mind. They are isolated instances, each with a distinct set of API keys and passwords. Each instance contains every Supabase feature: a Postgres database, Auth, File Storage, Realtime, Edge Functions, and Data APIs.","level":1,"lines":[35,36],"children":[{"type":"text","content":"Your project's Preview Branches are designed with safety in mind. They are isolated instances, each with a distinct set of API keys and passwords. Each instance contains every Supabase feature: a Postgres database, Auth, File Storage, Realtime, Edge Functions, and Data APIs.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[37,46],"level":0},{"type":"inline","content":"\u003cImg\n  alt=\"Every Supabase Preview is a dedicated instance, with a full suite of Supabase services.\"\n  src={{\n    light: '/images/blog/lwx-supabase-branching/isolated-instances--light.png',\n    dark: '/images/blog/lwx-supabase-branching/isolated-instances--dark.png',\n  }}\n  caption=\"Every Supabase Preview is a dedicated instance, with a full suite of Supabase services.\"\n  captionAlign=\"left\"\n/\u003e","level":1,"lines":[37,46],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"Every Supabase Preview is a dedicated instance, with a full suite of Supabase services.\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src={{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"light: '/images/blog/lwx-supabase-branching/isolated-instances--light.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"dark: '/images/blog/lwx-supabase-branching/isolated-instances--dark.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"}}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"caption=\"Every Supabase Preview is a dedicated instance, with a full suite of Supabase services.\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"captionAlign=\"left\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[47,48],"level":0},{"type":"inline","content":"Even in relaxed developer environments, if one of your team accidentally leaks a key it won't affect your Production branch.","level":1,"lines":[47,48],"children":[{"type":"text","content":"Even in relaxed developer environments, if one of your team accidentally leaks a key it won't affect your Production branch.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[49,50],"level":0},{"type":"inline","content":"[Support for Vercel Previews](#support-for-vercel-previews)","level":1,"lines":[49,50],"children":[{"type":"text","content":"Support for Vercel Previews","level":0}],"lvl":3,"i":1,"seen":0,"slug":"support-for-vercel-previews"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[51,52],"level":0},{"type":"inline","content":"We've designed Supabase Branching to work perfectly with Vercel's [Preview](https://vercel.com/features/previews) deployments. This means that you get an _entire stack_ with Branching.","level":1,"lines":[51,52],"children":[{"type":"text","content":"We've designed Supabase Branching to work perfectly with Vercel's ","level":0},{"type":"link_open","href":"https://vercel.com/features/previews","title":"","level":0},{"type":"text","content":"Preview","level":1},{"type":"link_close","level":0},{"type":"text","content":" deployments. This means that you get an ","level":0},{"type":"em_open","level":0},{"type":"text","content":"entire stack","level":1},{"type":"em_close","level":0},{"type":"text","content":" with Branching.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[53,62],"level":0},{"type":"inline","content":"\u003cImg\n  alt=\"Vercel will build your preview deployments, and your preview deployment can connect to the Supabase services on your Preview Branch.\"\n  src={{\n    light: '/images/blog/lwx-supabase-branching/vercel-support--light.png',\n    dark: '/images/blog/lwx-supabase-branching/vercel-support--dark.png',\n  }}\n  caption=\"Vercel will build your preview deployments, and your preview deployment can connect to the Supabase services on your Preview Branch.\"\n  captionAlign=\"left\"\n/\u003e","level":1,"lines":[53,62],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"Vercel will build your preview deployments, and your preview deployment can connect to the Supabase services on your Preview Branch.\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src={{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"light: '/images/blog/lwx-supabase-branching/vercel-support--light.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"dark: '/images/blog/lwx-supabase-branching/vercel-support--dark.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"}}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"caption=\"Vercel will build your preview deployments, and your preview deployment can connect to the Supabase services on your Preview Branch.\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"captionAlign=\"left\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[63,64],"level":0},{"type":"inline","content":"We've made several improvements to our [Vercel Integration](https://vercel.com/integrations/supabase) to make the Vercel experience seamless. For example, since we provide distinct, secure database credentials for every Supabase Preview Branch, we automatically populate the environment variables on Vercel with the connection secrets your app needs to connect to the Preview Branch.","level":1,"lines":[63,64],"children":[{"type":"text","content":"We've made several improvements to our ","level":0},{"type":"link_open","href":"https://vercel.com/integrations/supabase","title":"","level":0},{"type":"text","content":"Vercel Integration","level":1},{"type":"link_close","level":0},{"type":"text","content":" to make the Vercel experience seamless. For example, since we provide distinct, secure database credentials for every Supabase Preview Branch, we automatically populate the environment variables on Vercel with the connection secrets your app needs to connect to the Preview Branch.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[65,75],"level":0},{"type":"inline","content":"\u003cImg\n  alt=\"Vercel environment variables settings page, showing the Git branch based env vars.\"\n  src={{\n    light: '/images/blog/lwx-supabase-branching/vercel-env-var-settings--light.png',\n    dark: '/images/blog/lwx-supabase-branching/vercel-env-var-settings--dark.png',\n  }}\n  wide={true}\n  caption=\"Vercel environment variables settings page, showing the Git branch based env vars.\"\n  captionAlign=\"left\"\n/\u003e","level":1,"lines":[65,75],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"Vercel environment variables settings page, showing the Git branch based env vars.\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src={{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"light: '/images/blog/lwx-supabase-branching/vercel-env-var-settings--light.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"dark: '/images/blog/lwx-supabase-branching/vercel-env-var-settings--dark.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"}}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"wide={true}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"caption=\"Vercel environment variables settings page, showing the Git branch based env vars.\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"captionAlign=\"left\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[76,77],"level":0},{"type":"inline","content":"[Developing on the hosted Preview Branch](#developing-on-the-hosted-preview-branch)","level":1,"lines":[76,77],"children":[{"type":"text","content":"Developing on the hosted Preview Branch","level":0}],"lvl":3,"i":2,"seen":0,"slug":"developing-on-the-hosted-preview-branch"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[78,79],"level":0},{"type":"inline","content":"One of the most-loved features of Supabase is the dashboard. Even if we [beg](/docs/guides/platform/maturity-model#in-production), it seems that developers simply want to use it for everything - even in production.","level":1,"lines":[78,79],"children":[{"type":"text","content":"One of the most-loved features of Supabase is the dashboard. Even if we ","level":0},{"type":"link_open","href":"/docs/guides/platform/maturity-model#in-production","title":"","level":0},{"type":"text","content":"beg","level":1},{"type":"link_close","level":0},{"type":"text","content":", it seems that developers simply want to use it for everything - even in production.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[80,81],"level":0},{"type":"inline","content":"The cool thing about Branching is that every Supabase Preview can be managed from the Dashboard. You can make schema changes, access the SQL Editor, and use the [new AI Assistant](/blog/studio-introducing-assistant). Once you're happy with your changes, you simply run `supabase db diff` on your local machine to pull the changes and you can commit them to Git.","level":1,"lines":[80,81],"children":[{"type":"text","content":"The cool thing about Branching is that every Supabase Preview can be managed from the Dashboard. You can make schema changes, access the SQL Editor, and use the ","level":0},{"type":"link_open","href":"/blog/studio-introducing-assistant","title":"","level":0},{"type":"text","content":"new AI Assistant","level":1},{"type":"link_close","level":0},{"type":"text","content":". Once you're happy with your changes, you simply run ","level":0},{"type":"code","content":"supabase db diff","block":false,"level":0},{"type":"text","content":" on your local machine to pull the changes and you can commit them to Git.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[82,83],"level":0},{"type":"inline","content":"Just note that we _still_ want you to develop locally! You should treat the Preview Branches [like cattle, not pets](https://devops.stackexchange.com/questions/653/what-is-the-definition-of-cattle-not-pets). Your Preview changes can be wiped at any time if one of your team pushes a destructive migration.","level":1,"lines":[82,83],"children":[{"type":"text","content":"Just note that we ","level":0},{"type":"em_open","level":0},{"type":"text","content":"still","level":1},{"type":"em_close","level":0},{"type":"text","content":" want you to develop locally! You should treat the Preview Branches ","level":0},{"type":"link_open","href":"https://devops.stackexchange.com/questions/653/what-is-the-definition-of-cattle-not-pets","title":"","level":0},{"type":"text","content":"like cattle, not pets","level":1},{"type":"link_close","level":0},{"type":"text","content":". Your Preview changes can be wiped at any time if one of your team pushes a destructive migration.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[84,85],"level":0},{"type":"inline","content":"[Database migrations](#database-migrations)","level":1,"lines":[84,85],"children":[{"type":"text","content":"Database migrations","level":0}],"lvl":3,"i":3,"seen":0,"slug":"database-migrations"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[86,87],"level":0},{"type":"inline","content":"We've developed Branching to work with a Git provider, starting with GitHub.","level":1,"lines":[86,87],"children":[{"type":"text","content":"We've developed Branching to work with a Git provider, starting with GitHub.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[88,89],"level":0},{"type":"inline","content":"Our [GitHub app](https://github.com/apps/supabase) observes changes within a connected GitHub repository. When you open a Pull Request, it launches a Preview Branch and runs the migrations in `./supabase/migrations`. If there are any errors they are logged to the [Check Run](https://docs.github.com/en/rest/checks/runs?apiVersion=2022-11-28) associated with that git commit. When all checks turn green, your new Preview Branch is ready to use.","level":1,"lines":[88,89],"children":[{"type":"text","content":"Our ","level":0},{"type":"link_open","href":"https://github.com/apps/supabase","title":"","level":0},{"type":"text","content":"GitHub app","level":1},{"type":"link_close","level":0},{"type":"text","content":" observes changes within a connected GitHub repository. When you open a Pull Request, it launches a Preview Branch and runs the migrations in ","level":0},{"type":"code","content":"./supabase/migrations","block":false,"level":0},{"type":"text","content":". If there are any errors they are logged to the ","level":0},{"type":"link_open","href":"https://docs.github.com/en/rest/checks/runs?apiVersion=2022-11-28","title":"","level":0},{"type":"text","content":"Check Run","level":1},{"type":"link_close","level":0},{"type":"text","content":" associated with that git commit. When all checks turn green, your new Preview Branch is ready to use.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[90,91],"level":0},{"type":"inline","content":"When you push a new migration file to the Git branch, the app runs it incrementally in your Preview Branch. This allows you to verify schema changes easily on existing seed data.","level":1,"lines":[90,91],"children":[{"type":"text","content":"When you push a new migration file to the Git branch, the app runs it incrementally in your Preview Branch. This allows you to verify schema changes easily on existing seed data.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[92,93],"level":0},{"type":"inline","content":"Finally, when you merge that PR, the app runs the new migrations on your Production environment. If you have other PRs already open, make sure to update those migration files to a later timestamp than the ones in the Production branch following a [standard migration practice](/docs/guides/cli/managing-environments).","level":1,"lines":[92,93],"children":[{"type":"text","content":"Finally, when you merge that PR, the app runs the new migrations on your Production environment. If you have other PRs already open, make sure to update those migration files to a later timestamp than the ones in the Production branch following a ","level":0},{"type":"link_open","href":"/docs/guides/cli/managing-environments","title":"","level":0},{"type":"text","content":"standard migration practice","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[94,95],"level":0},{"type":"inline","content":"[Data seeding](#data-seeding)","level":1,"lines":[94,95],"children":[{"type":"text","content":"Data seeding","level":0}],"lvl":3,"i":4,"seen":0,"slug":"data-seeding"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[96,97],"level":0},{"type":"inline","content":"You can seed your Preview branch in the same way that you [seed your local development environment](/docs/guides/cli/seeding-your-database). Just add `./supabase/seed.sql` in your repo and the seed script will run when the Preview Branch is created.","level":1,"lines":[96,97],"children":[{"type":"text","content":"You can seed your Preview branch in the same way that you ","level":0},{"type":"link_open","href":"/docs/guides/cli/seeding-your-database","title":"","level":0},{"type":"text","content":"seed your local development environment","level":1},{"type":"link_close","level":0},{"type":"text","content":". Just add ","level":0},{"type":"code","content":"./supabase/seed.sql","block":false,"level":0},{"type":"text","content":" in your repo and the seed script will run when the Preview Branch is created.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[98,99],"level":0},{"type":"inline","content":"Optionally, you can reset the database by running `supabase db reset --db-url \u003cbranch-connection-string\u003e`. The branch connection string can be retrieved using your Personal Access Token with Supabase CLI's [branch management](/docs/reference/cli/supabase-branches-get) commands.","level":1,"lines":[98,99],"children":[{"type":"text","content":"Optionally, you can reset the database by running ","level":0},{"type":"code","content":"supabase db reset --db-url \u003cbranch-connection-string\u003e","block":false,"level":0},{"type":"text","content":". The branch connection string can be retrieved using your Personal Access Token with Supabase CLI's ","level":0},{"type":"link_open","href":"/docs/reference/cli/supabase-branches-get","title":"","level":0},{"type":"text","content":"branch management","level":1},{"type":"link_close","level":0},{"type":"text","content":" commands.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[100,101],"level":0},{"type":"inline","content":"We're investigating data masking techniques with a copy-on-write system so that you can emulate a production workload inside your Preview Branches. We plan for this to work with File Storage too.","level":1,"lines":[100,101],"children":[{"type":"text","content":"We're investigating data masking techniques with a copy-on-write system so that you can emulate a production workload inside your Preview Branches. We plan for this to work with File Storage too.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[102,103],"level":0},{"type":"inline","content":"\u003cAdmonition type=\"note\" label=\"Testing with product workloads today?\"\u003e","level":1,"lines":[102,103],"children":[{"type":"text","content":"\u003cAdmonition type=\"note\" label=\"Testing with product workloads today?\"\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[104,105],"level":0},{"type":"inline","content":"If you need to test with production workloads today, check out [Snaplet](https://www.snaplet.dev/) and [Postgres.ai](http://Postgres.ai). Both are great partners of Supabase.","level":1,"lines":[104,105],"children":[{"type":"text","content":"If you need to test with production workloads today, check out ","level":0},{"type":"link_open","href":"https://www.snaplet.dev/","title":"","level":0},{"type":"text","content":"Snaplet","level":1},{"type":"link_close","level":0},{"type":"text","content":" and ","level":0},{"type":"link_open","href":"http://Postgres.ai","title":"","level":0},{"type":"text","content":"Postgres.ai","level":1},{"type":"link_close","level":0},{"type":"text","content":". Both are great partners of Supabase.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[106,107],"level":0},{"type":"inline","content":"\u003c/Admonition\u003e","level":1,"lines":[106,107],"children":[{"type":"text","content":"\u003c/Admonition\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[108,109],"level":0},{"type":"inline","content":"[Future considerations](#future-considerations)","level":1,"lines":[108,109],"children":[{"type":"text","content":"Future considerations","level":0}],"lvl":2,"i":5,"seen":0,"slug":"future-considerations"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[110,111],"level":0},{"type":"inline","content":"That's already a lot for Branching v0. Branching will be a core part of the developer workflow in the future. These are the themes we'll explore next:","level":1,"lines":[110,111],"children":[{"type":"text","content":"That's already a lot for Branching v0. Branching will be a core part of the developer workflow in the future. These are the themes we'll explore next:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[112,113],"level":0},{"type":"inline","content":"[Declarative config](#declarative-config)","level":1,"lines":[112,113],"children":[{"type":"text","content":"Declarative config","level":0}],"lvl":3,"i":6,"seen":0,"slug":"declarative-config"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[114,115],"level":0},{"type":"inline","content":"We're still working on “configuration in code”. For example, you might want to try a different Google Auth in your Preview Branch than the one you use in Product. This would be a lot easier if the code was declarative, inside the [config.toml](/docs/guides/cli/config) file.","level":1,"lines":[114,115],"children":[{"type":"text","content":"We're still working on “configuration in code”. For example, you might want to try a different Google Auth in your Preview Branch than the one you use in Product. This would be a lot easier if the code was declarative, inside the ","level":0},{"type":"link_open","href":"/docs/guides/cli/config","title":"","level":0},{"type":"text","content":"config.toml","level":1},{"type":"link_close","level":0},{"type":"text","content":" file.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[116,117],"level":0},{"type":"inline","content":"[Automatic dashboard commits](#automatic-dashboard-commits)","level":1,"lines":[116,117],"children":[{"type":"text","content":"Automatic dashboard commits","level":0}],"lvl":3,"i":7,"seen":0,"slug":"automatic-dashboard-commits"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[118,119],"level":0},{"type":"inline","content":"In the current version, when you use the dashboard to create a change on a Preview Branch, you need to run `db diff` locally to pull that change into your Git repository. We plan to work on a feature to automatically capture your changes in a Git repo that you've connected.","level":1,"lines":[118,119],"children":[{"type":"text","content":"In the current version, when you use the dashboard to create a change on a Preview Branch, you need to run ","level":0},{"type":"code","content":"db diff","block":false,"level":0},{"type":"text","content":" locally to pull that change into your Git repository. We plan to work on a feature to automatically capture your changes in a Git repo that you've connected.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[120,121],"level":0},{"type":"inline","content":"[Extended seeding behavior](#extended-seeding-behavior)","level":1,"lines":[120,121],"children":[{"type":"text","content":"Extended seeding behavior","level":0}],"lvl":3,"i":8,"seen":0,"slug":"extended-seeding-behavior"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[122,123],"level":0},{"type":"inline","content":"There are a multitude of different strategies for populating seed data. We've dabbled with AI to generate seed data, which was fun. We also like the approach of [postgresql-anonymizer](https://postgresql-anonymizer.readthedocs.io/en/stable/) and [Snaplet](https://docs.snaplet.dev/recipes/supabase), which specialize in cloning production data while anonymizing the data for safe development.","level":1,"lines":[122,123],"children":[{"type":"text","content":"There are a multitude of different strategies for populating seed data. We've dabbled with AI to generate seed data, which was fun. We also like the approach of ","level":0},{"type":"link_open","href":"https://postgresql-anonymizer.readthedocs.io/en/stable/","title":"","level":0},{"type":"text","content":"postgresql-anonymizer","level":1},{"type":"link_close","level":0},{"type":"text","content":" and ","level":0},{"type":"link_open","href":"https://docs.snaplet.dev/recipes/supabase","title":"","level":0},{"type":"text","content":"Snaplet","level":1},{"type":"link_close","level":0},{"type":"text","content":", which specialize in cloning production data while anonymizing the data for safe development.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[124,125],"level":0},{"type":"inline","content":"[Copy-on-write](#copy-on-write)","level":1,"lines":[124,125],"children":[{"type":"text","content":"Copy-on-write","level":0}],"lvl":3,"i":9,"seen":0,"slug":"copy-on-write"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[126,127],"level":0},{"type":"inline","content":"We have something in development :). CoW means you can branch from database snapshot and then run tests on “production-like” workloads. This is the approach that [Postgres.ai](http://Postgres.ai) uses. As we mentioned above, we need to figure out an approach that also works with [File Storage](/storage).","level":1,"lines":[126,127],"children":[{"type":"text","content":"We have something in development :). CoW means you can branch from database snapshot and then run tests on “production-like” workloads. This is the approach that ","level":0},{"type":"link_open","href":"http://Postgres.ai","title":"","level":0},{"type":"text","content":"Postgres.ai","level":1},{"type":"link_close","level":0},{"type":"text","content":" uses. As we mentioned above, we need to figure out an approach that also works with ","level":0},{"type":"link_open","href":"/storage","title":"","level":0},{"type":"text","content":"File Storage","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[128,129],"level":0},{"type":"inline","content":"[Interested in using Branching?](#interested-in-using-branching)","level":1,"lines":[128,129],"children":[{"type":"text","content":"Interested in using Branching?","level":0}],"lvl":2,"i":10,"seen":0,"slug":"interested-in-using-branching"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[130,131],"level":0},{"type":"inline","content":"We'll be onboarding organizations in batches over the next few weeks, and working with these early users on Pricing.","level":1,"lines":[130,131],"children":[{"type":"text","content":"We'll be onboarding organizations in batches over the next few weeks, and working with these early users on Pricing.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[132,133],"level":0},{"type":"inline","content":"**Update 17th January 2024** - Early access for Branching is now closed for the foreseeable future. We are now working hard towards releasing a public beta.","level":1,"lines":[132,133],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Update 17th January 2024","level":1},{"type":"strong_close","level":0},{"type":"text","content":" - Early access for Branching is now closed for the foreseeable future. We are now working hard towards releasing a public beta.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[134,135],"level":0},{"type":"inline","content":"Check out the [Branching docs](/docs/guides/platform/branching) and also if you have any feedback, [you can join the discussion](https://github.com/orgs/supabase/discussions/18937).","level":1,"lines":[134,135],"children":[{"type":"text","content":"Check out the ","level":0},{"type":"link_open","href":"/docs/guides/platform/branching","title":"","level":0},{"type":"text","content":"Branching docs","level":1},{"type":"link_close","level":0},{"type":"text","content":" and also if you have any feedback, ","level":0},{"type":"link_open","href":"https://github.com/orgs/supabase/discussions/18937","title":"","level":0},{"type":"text","content":"you can join the discussion","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[136,145],"level":0},{"type":"inline","content":"\u003cImg\n  src={{\n    light: '/images/blog/lwx-supabase-branching/branching-ui--light.png',\n    dark: '/images/blog/lwx-supabase-branching/branching-ui--dark.png',\n  }}\n  wide={true}\n  caption=\"New Branching UI in the Supabase dashboard\"\n  captionAlign=\"left\"\n/\u003e","level":1,"lines":[136,145],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src={{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"light: '/images/blog/lwx-supabase-branching/branching-ui--light.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"dark: '/images/blog/lwx-supabase-branching/branching-ui--dark.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"}}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"wide={true}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"caption=\"New Branching UI in the Supabase dashboard\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"captionAlign=\"left\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0}],"content":"- [What's Branching?](#whats-branching)\n  * [Support for Vercel Previews](#support-for-vercel-previews)\n  * [Developing on the hosted Preview Branch](#developing-on-the-hosted-preview-branch)\n  * [Database migrations](#database-migrations)\n  * [Data seeding](#data-seeding)\n- [Future considerations](#future-considerations)\n  * [Declarative config](#declarative-config)\n  * [Automatic dashboard commits](#automatic-dashboard-commits)\n  * [Extended seeding behavior](#extended-seeding-behavior)\n  * [Copy-on-write](#copy-on-write)\n- [Interested in using Branching?](#interested-in-using-branching)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-branching"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>