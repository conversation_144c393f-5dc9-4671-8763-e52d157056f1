<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase Integrations Marketplace</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Become a Supabase Integrations Partner: Publish OAuth Apps and Build with Supabase." data-next-head=""/><meta property="og:title" content="Supabase Integrations Marketplace" data-next-head=""/><meta property="og:description" content="Become a Supabase Integrations Partner: Publish OAuth Apps and Build with Supabase." data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-integrations-marketplace" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2023-08-10" data-next-head=""/><meta property="article:author" content="https://twitter.com/thorwebdev" data-next-head=""/><meta property="article:tag" content="launch-week" data-next-head=""/><meta property="article:tag" content="integrations" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/launch-week-8/day-4/integration-marketplace-og.jpg" data-next-head=""/><meta property="og:image:alt" content="Supabase Integrations Marketplace thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Supabase Integrations Marketplace</h1><div class="text-light flex space-x-3 text-sm"><p>10 Aug 2023</p><p>•</p><p>5 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://twitter.com/thorwebdev"><div class="flex items-center gap-3"><div class="w-10"><img alt="Thor Schaeff avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fthorwebdev.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fthorwebdev.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fthorwebdev.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Thor Schaeff</span><span class="text-foreground-lighter mb-0 text-xs">DevRel &amp; DX</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Supabase Integrations Marketplace" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-4%2Fintegration-marketplace-thumb.jpg&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-4%2Fintegration-marketplace-thumb.jpg&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-4%2Fintegration-marketplace-thumb.jpg&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-4%2Fintegration-marketplace-thumb.jpg&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-4%2Fintegration-marketplace-thumb.jpg&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-4%2Fintegration-marketplace-thumb.jpg&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-4%2Fintegration-marketplace-thumb.jpg&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-4%2Fintegration-marketplace-thumb.jpg&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-4%2Fintegration-marketplace-thumb.jpg&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-4%2Fintegration-marketplace-thumb.jpg&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-4%2Fintegration-marketplace-thumb.jpg&amp;w=3840&amp;q=100"/></div><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p>We&#x27;ve been running our <a href="../partners.html">Integrations Marketplace</a> in “stealth mode” for about a year now. What started as a dog-fooding project has now transformed into a marketplace with <a href="../partners/integrations.html">over 60 integrations</a>. (It&#x27;s also an <a href="https://vercel.com/templates/next.js/supabase-partner-gallery">open source template</a> that you can use yourself).</p>
<p></p>
<p>Supabase Integrations allows Partners to extend the Supabase platform with useful tooling. Today we&#x27;re adding <a href="https://supabase.com/docs/guides/platform/oauth-apps/publish-an-oauth-app">OAuth2.0 Applications</a>. For Supabase users, this makes it even easier to connect their favorite tools to their Supabase projects. Within minutes you can:</p>
<ul>
<li>Add your favorite <a href="../partners/integrations.html#low-code">Low Code</a> tools on top of your Supabase database.</li>
<li>Integrate your favorite <a href="../partners/integrations.html#devtools">DevTools</a>: including secrets managers and database management tools.</li>
<li>Add <a href="../partners/integrations.html#caching%20/%20offline-first">caching</a> to your Supabase database.</li>
<li>Not a fan of the Supabase admin dashboard? Try <a href="../partners/integrations.html#data%20platform">one of these</a>.</li>
<li>Try out a different <a href="../partners/integrations.html#messaging">SMS and email provider</a>.</li>
</ul>
<h2 id="featured-partners" class="group scroll-mt-24">Featured Partners<a href="#featured-partners" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>For the initial launch we&#x27;ve started with a few partners to help us build and test the OAuth functionality.</p>
<h3 id="cloudflare" class="group scroll-mt-24">Cloudflare<a href="#cloudflare" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p></p>
<p>We worked with Cloudflare to build <a href="https://blog.cloudflare.com/announcing-database-integrations/">support for databases</a> inside Cloudflare Workers. The Cloudflare integration makes it incredibly easy to connect to your Supabase database directly from the Cloudflare Dashboard.</p>
<p>Check out the <a href="https://cloudflare.tv/event/using-supabase-with-cloudflare-workers/dgM90RgD">latest episode</a> on Cloudflare TV to see it in action.</p>
<h3 id="resend" class="group scroll-mt-24">Resend<a href="#resend" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p></p>
<p><a href="https://resend.com">Resend</a> (YC <a href="https://www.ycombinator.com/companies/resend">W23</a>) is building the modern email sending platform. If you&#x27;re using Supabase for Auth, then you&#x27;ll know already that we handle all your Auth emails. But did you know that the email configuration we provide you is only for testing purposes? When you&#x27;re <a href="https://supabase.com/docs/guides/platform/going-into-prod#restricted-access-levels-for-team-members">going into production</a>, you need to integrate your own email provider. That&#x27;s where Resend come in. They&#x27;ve built a one-click integration to add Resend as a custom SMTP provider for Supabase.</p>
<p>Read more on <a href="https://resend.com/blog/how-to-configure-supabase-to-send-emails-from-your-domain">Resend&#x27;s blog</a>.</p>
<h3 id="snaplet" class="group scroll-mt-24">Snaplet<a href="#snaplet" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p></p>
<p>Snaplet is a tool for Typescript developers to copy your database, transform sensitive data, and share it with your team without worrying about PII. If you followed our <a href="supabase-local-dev.html#database-seeding">Tuesday launch</a> you&#x27;ll be familiar with Snaplet - they are one of the best tools for <a href="https://supabase.com/docs/guides/cli/seeding-your-database#generating-seed-data">generating seed data</a> for your local development environment. Now they are making it even easier, with their official OAuth App, to spin up production-like development environments for your team.</p>
<p><a href="https://www.snaplet.dev/post/now-live-supabase-x-snaplet-integration">Learn more on snaplet.dev</a>.</p>
<h3 id="triggerdev" class="group scroll-mt-24">Trigger.dev<a href="#triggerdev" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p></p>
<p><a href="http://trigger.dev/">Trigger.dev</a> (YC <a href="https://www.ycombinator.com/companies/trigger-dev">W23</a>) is the open source Background Jobs framework for Next.js. You can create long-running Jobs directly in your codebase with features like API integrations, webhooks, scheduling and delays. And today you can use their one-click integration to <a href="https://trigger.dev/supabase">trigger anything from a database change</a> in Supabase.</p>
<p>Learn more about their integration at: <a href="http://trigger.dev/supabase">trigger.dev/supabase</a></p>
<h3 id="vercel" class="group scroll-mt-24">Vercel<a href="#vercel" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p></p>
<p>One that requires no introduction - since so many of you use Vercel, we&#x27;ve dedicated an entire blog post to the upgraded Vercel integration.</p>
<p>Learn more about the Vercel integration <a href="using-supabase-with-vercel.html">updates we&#x27;re launching</a> today.</p>
<h3 id="windmill" class="group scroll-mt-24">Windmill<a href="#windmill" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p></p>
<p><a href="https://windmill.dev">Windmill</a> (YC <a href="https://www.ycombinator.com/companies/windmill">S22</a>) is an open source alternative to Retool and a modern Airflow. They provide a developer platform to quickly build production-grade complex workflows and integrations from minimal Python and Typescript scripts. Their one-click integration with Supabase makes it simple to launch new databases, process large quantities of data (maybe even convert them into <a href="https://supabase.com/vector">embeddings</a>), and build internal dashboards.</p>
<p>Read the <a href="https://www.windmill.dev/blog/2023/08/10/supabase-partnership">official blog post on windmill.dev</a>.</p>
<h2 id="building-supabase-integrations" class="group scroll-mt-24">Building Supabase Integrations<a href="#building-supabase-integrations" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We&#x27;ve released full instructions in our <a href="https://supabase.com/docs/guides/platform/oauth-apps/build-a-supabase-integration">Build with Supabase</a> documentation so that you can build your own Supabase OAuth application for your users. Simply visit your <a href="https://supabase.com/dashboard/org/_/apps">Organization settings</a> and click “Add application” to get started:</p>
<div class="video-container"><iframe class="w-full" src="https://www.youtube-nocookie.com/embed/gtJo1lTxHfs" title="YouTube video player" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share"></iframe></div>
<p>The Integrations marketplace is open to everyone. After your submission is complete, you can share the integration with your own users - simply create a button to launch your new app. We&#x27;ve provided some <a href="../brand-assets.html">brand assets</a> so that developers can quickly identify the integration on your site.</p>
<h2 id="building-custom-integrations" class="group scroll-mt-24">Building custom integrations<a href="#building-custom-integrations" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>You don&#x27;t actually need to build an OAuth Application to build an integration with Supabase. If you&#x27;re building something for yourself or your team, the <a href="../docs/reference/api/introduction.html">Management API</a> is the way to go.</p>
<p>The <a href="https://trigger.dev">Trigger.dev</a> team deserve a special shout out. While developing their Integration they also developed <a href="https://github.com/supabase-community/supabase-management-js">supabase-management-js</a>, a Typescript library for the <a href="../docs/reference/api/introduction.html">Supabase Management API</a>. This makes it even easier to get started with the Supabase API.</p>
<p>It&#x27;s useful beyond just integrations. Want to programmatically spin up databases? Easy:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span>import { SupabaseManagementAPI } from &quot;supabase-management-js&quot;;</span></div></div><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span>const client = new SupabaseManagementAPI({</span></div></div><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span>	accessToken: &quot;&lt;access token&gt;&quot;</span></div></div><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span>})</span></div></div><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span>const newProject = await client.createProject({</span></div></div><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span>	  name: &#x27;staging&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span>		db_pass: &#x27;XXX&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span>    organization_id: &#x27;XXX&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span>		plan: &#x27;free&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span>    region: &#x27;us-east-1&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->13</span><div style="display:inline-block;margin-left:16px"><span>})</span></div></div><br/></code></div></div>
<h2 id="become-a-partner" class="group scroll-mt-24">Become a Partner<a href="#become-a-partner" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Supabase is a collaborative company. We love working with other communities (especially open source ones!), and we&#x27;d love to work with you. Get started today:</p>
<ul>
<li><a href="https://supabase.com/docs/guides/platform/oauth-apps/build-a-supabase-integration">Build an OAuth integration</a></li>
<li><a href="../docs/reference/api/introduction.html">Learn more about our Management API</a></li>
</ul>
<p></p>
<h2 id="more-launch-week-8" class="group scroll-mt-24">More Launch Week 8<a href="#more-launch-week-8" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ul>
<li><a href="supabase-local-dev.html">Supabase Local Dev: migrations, branching, and observability</a></li>
<li><a href="https://supabase.com/blog/hugging-face-supabase">Hugging Face is now supported in Supabase</a></li>
<li><a href="../launch-week.html">Launch Week 8</a></li>
<li><a href="https://supabase.com/blog/interactive-constellation-threejs-react-three-fiber">Coding the stars - an interactive constellation with Three.js and React Three Fiber</a></li>
<li><a href="https://supabase.com/blog/why-supabase-remote">Why we&#x27;ll stay remote</a></li>
<li><a href="https://github.com/supabase/postgres_lsp">Postgres Language Server</a></li>
</ul></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-integrations-marketplace&amp;text=Supabase%20Integrations%20Marketplace"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-integrations-marketplace&amp;text=Supabase%20Integrations%20Marketplace"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-integrations-marketplace&amp;t=Supabase%20Integrations%20Marketplace"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="supavisor-1-million.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Supavisor: Scaling Postgres to 1 Million Connections</h4><p class="small">11 August 2023</p></div></div></div></div></a></div><div><a href="using-supabase-with-vercel.html"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Vercel Integration and Next.js App Router Support</h4><p class="small">10 August 2023</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/launch-week"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">launch-week</div></a><a href="https://supabase.com/blog/tags/integrations"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">integrations</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#featured-partners">Featured Partners</a>
<ul>
<li><a href="#cloudflare">Cloudflare</a></li>
<li><a href="#resend">Resend</a></li>
<li><a href="#snaplet">Snaplet</a></li>
<li><a href="#triggerdev">Trigger.dev</a></li>
<li><a href="#vercel">Vercel</a></li>
<li><a href="#windmill">Windmill</a></li>
</ul>
</li>
<li><a href="#building-supabase-integrations">Building Supabase Integrations</a></li>
<li><a href="#building-custom-integrations">Building custom integrations</a></li>
<li><a href="#become-a-partner">Become a Partner</a></li>
<li><a href="#more-launch-week-8">More Launch Week 8</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-integrations-marketplace&amp;text=Supabase%20Integrations%20Marketplace"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-integrations-marketplace&amp;text=Supabase%20Integrations%20Marketplace"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-integrations-marketplace&amp;t=Supabase%20Integrations%20Marketplace"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"supavisor-1-million","title":"Supavisor: Scaling Postgres to 1 Million Connections","description":"Supavisor is a scalable, cloud-native Postgres connection pooler. We connected a million clients to it to see how it performs.","launchweek":"8","categories":["product"],"tags":["launch-week","supavisor","postgres"],"date":"2023-08-11","toc_depth":3,"author":"egor_romanov,chasers,stas","image":"launch-week-8/day-5/supavisor-og.jpg","thumb":"launch-week-8/day-5/supavisor-thumb.jpg","formattedDate":"11 August 2023","readingTime":"14 minute read","url":"/blog/supavisor-1-million","path":"/blog/supavisor-1-million"},"nextPost":{"slug":"using-supabase-with-vercel","title":"Vercel Integration and Next.js App Router Support","description":"Using Supabase with Vercel and Next.js is now a lot easier.","launchweek":"8","categories":["product"],"tags":["launch-week","integrations"],"date":"2023-08-10","toc_depth":3,"author":"jonny,jonmeyers_io","image":"launch-week-8/day-4/vercel-and-supabase-og.jpg","thumb":"launch-week-8/day-4/vercel-and-supabase-thumb.jpg","formattedDate":"10 August 2023","readingTime":"6 minute read","url":"/blog/using-supabase-with-vercel","path":"/blog/using-supabase-with-vercel"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-integrations-marketplace","source":"\nWe've been running our [Integrations Marketplace](/partners) in “stealth mode” for about a year now. What started as a dog-fooding project has now transformed into a marketplace with [over 60 integrations](/partners/integrations). (It's also an [open source template](https://vercel.com/templates/next.js/supabase-partner-gallery) that you can use yourself).\n\n![Featured](/images/blog/launch-week-8/day-4/featured-integrations.png)\n\nSupabase Integrations allows Partners to extend the Supabase platform with useful tooling. Today we're adding [OAuth2.0 Applications](https://supabase.com/docs/guides/platform/oauth-apps/publish-an-oauth-app). For Supabase users, this makes it even easier to connect their favorite tools to their Supabase projects. Within minutes you can:\n\n- Add your favorite [Low Code](https://supabase.com/partners/integrations#low-code) tools on top of your Supabase database.\n- Integrate your favorite [DevTools](https://supabase.com/partners/integrations#devtools): including secrets managers and database management tools.\n- Add [caching](https://supabase.com/partners/integrations#caching%20/%20offline-first) to your Supabase database.\n- Not a fan of the Supabase admin dashboard? Try [one of these](https://supabase.com/partners/integrations#data%20platform).\n- Try out a different [SMS and email provider](https://supabase.com/partners/integrations#messaging).\n\n## Featured Partners\n\nFor the initial launch we've started with a few partners to help us build and test the OAuth functionality.\n\n### Cloudflare\n\n![Cloudflare x Supabase](/images/blog/launch-week-8/day-4/marketplace-cloudflare.jpg)\n\nWe worked with Cloudflare to build [support for databases](https://blog.cloudflare.com/announcing-database-integrations/) inside Cloudflare Workers. The Cloudflare integration makes it incredibly easy to connect to your Supabase database directly from the Cloudflare Dashboard.\n\nCheck out the [latest episode](https://cloudflare.tv/event/using-supabase-with-cloudflare-workers/dgM90RgD) on Cloudflare TV to see it in action.\n\n### Resend\n\n![Resend x Supabase](/images/blog/launch-week-8/day-4/marketplace-resend.jpg)\n\n[Resend](https://resend.com) (YC [W23](https://www.ycombinator.com/companies/resend)) is building the modern email sending platform. If you're using Supabase for Auth, then you'll know already that we handle all your Auth emails. But did you know that the email configuration we provide you is only for testing purposes? When you're [going into production](https://supabase.com/docs/guides/platform/going-into-prod#restricted-access-levels-for-team-members), you need to integrate your own email provider. That's where Resend come in. They've built a one-click integration to add Resend as a custom SMTP provider for Supabase.\n\nRead more on [Resend's blog](https://resend.com/blog/how-to-configure-supabase-to-send-emails-from-your-domain).\n\n### Snaplet\n\n![Snaplet x Supabase](/images/blog/launch-week-8/day-4/marketplace-snaplet.jpg)\n\nSnaplet is a tool for Typescript developers to copy your database, transform sensitive data, and share it with your team without worrying about PII. If you followed our [Tuesday launch](https://supabase.com/blog/supabase-local-dev#database-seeding) you'll be familiar with Snaplet - they are one of the best tools for [generating seed data](https://supabase.com/docs/guides/cli/seeding-your-database#generating-seed-data) for your local development environment. Now they are making it even easier, with their official OAuth App, to spin up production-like development environments for your team.\n\n[Learn more on snaplet.dev](https://www.snaplet.dev/post/now-live-supabase-x-snaplet-integration).\n\n### Trigger.dev\n\n![Trigger x Supabase](/images/blog/launch-week-8/day-4/marketplace-triggerdev.jpg)\n\n[Trigger.dev](http://trigger.dev/) (YC [W23](https://www.ycombinator.com/companies/trigger-dev)) is the open source Background Jobs framework for Next.js. You can create long-running Jobs directly in your codebase with features like API integrations, webhooks, scheduling and delays. And today you can use their one-click integration to [trigger anything from a database change](https://trigger.dev/supabase) in Supabase.\n\nLearn more about their integration at: [trigger.dev/supabase](http://trigger.dev/supabase)\n\n### Vercel\n\n![Vercel x Supabase](/images/blog/launch-week-8/day-4/marketplace-vercel.jpg)\n\nOne that requires no introduction - since so many of you use Vercel, we've dedicated an entire blog post to the upgraded Vercel integration.\n\nLearn more about the Vercel integration [updates we're launching](/blog/using-supabase-with-vercel) today.\n\n### Windmill\n\n![Windmill x Supabase](/images/blog/launch-week-8/day-4/marketplace-windmill.jpg)\n\n[Windmill](https://windmill.dev) (YC [S22](https://www.ycombinator.com/companies/windmill)) is an open source alternative to Retool and a modern Airflow. They provide a developer platform to quickly build production-grade complex workflows and integrations from minimal Python and Typescript scripts. Their one-click integration with Supabase makes it simple to launch new databases, process large quantities of data (maybe even convert them into [embeddings](https://supabase.com/vector)), and build internal dashboards.\n\nRead the [official blog post on windmill.dev](https://www.windmill.dev/blog/2023/08/10/supabase-partnership).\n\n## Building Supabase Integrations\n\nWe've released full instructions in our [Build with Supabase](https://supabase.com/docs/guides/platform/oauth-apps/build-a-supabase-integration) documentation so that you can build your own Supabase OAuth application for your users. Simply visit your [Organization settings](https://supabase.com/dashboard/org/_/apps) and click “Add application” to get started:\n\n\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/gtJo1lTxHfs\"\n    title=\"YouTube video player\"\n    frameBorder=\"0\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"\n    allowfullscreen\n  \u003e\u003c/iframe\u003e\n\u003c/div\u003e\n\nThe Integrations marketplace is open to everyone. After your submission is complete, you can share the integration with your own users - simply create a button to launch your new app. We've provided some [brand assets](https://supabase.com/brand-assets) so that developers can quickly identify the integration on your site.\n\n## Building custom integrations\n\nYou don't actually need to build an OAuth Application to build an integration with Supabase. If you're building something for yourself or your team, the [Management API](https://supabase.com/docs/reference/api/introduction) is the way to go.\n\nThe [Trigger.dev](https://trigger.dev) team deserve a special shout out. While developing their Integration they also developed [supabase-management-js](https://github.com/supabase-community/supabase-management-js), a Typescript library for the [Supabase Management API](/docs/reference/api/introduction). This makes it even easier to get started with the Supabase API.\n\nIt's useful beyond just integrations. Want to programmatically spin up databases? Easy:\n\n```tsx\nimport { SupabaseManagementAPI } from \"supabase-management-js\";\n\nconst client = new SupabaseManagementAPI({\n\taccessToken: \"\u003caccess token\u003e\"\n})\n\nconst newProject = await client.createProject({\n\t  name: 'staging',\n\t\tdb_pass: 'XXX',\n    organization_id: 'XXX'\n\t\tplan: 'free',\n    region: 'us-east-1'\n})\n```\n\n## Become a Partner\n\nSupabase is a collaborative company. We love working with other communities (especially open source ones!), and we'd love to work with you. Get started today:\n\n- [Build an OAuth integration](/docs/guides/platform/oauth-apps/build-a-supabase-integration)\n- [Learn more about our Management API](/docs/reference/api/introduction)\n\n![Partner with Supabase](/images/blog/launch-week-8/day-4/partner-with-supabase.png)\n\n## More Launch Week 8\n\n- [Supabase Local Dev: migrations, branching, and observability](/blog/supabase-local-dev)\n- [Hugging Face is now supported in Supabase](/blog/hugging-face-supabase)\n- [Launch Week 8](/launch-week)\n- [Coding the stars - an interactive constellation with Three.js and React Three Fiber](/blog/interactive-constellation-threejs-react-three-fiber)\n- [Why we'll stay remote](/blog/why-supabase-remote)\n- [Postgres Language Server](https://github.com/supabase/postgres_lsp)\n","title":"Supabase Integrations Marketplace","description":"Become a Supabase Integrations Partner: Publish OAuth Apps and Build with Supabase.","launchweek":"8","categories":["product"],"tags":["launch-week","integrations"],"date":"2023-08-10","toc_depth":3,"author":"thor_schaeff","image":"launch-week-8/day-4/integration-marketplace-og.jpg","thumb":"launch-week-8/day-4/integration-marketplace-thumb.jpg","content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    a: \"a\",\n    img: \"img\",\n    ul: \"ul\",\n    li: \"li\",\n    h2: \"h2\",\n    h3: \"h3\"\n  }, _provideComponents(), props.components), {CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We've been running our \", _jsx(_components.a, {\n        href: \"/partners\",\n        children: \"Integrations Marketplace\"\n      }), \" in “stealth mode” for about a year now. What started as a dog-fooding project has now transformed into a marketplace with \", _jsx(_components.a, {\n        href: \"/partners/integrations\",\n        children: \"over 60 integrations\"\n      }), \". (It's also an \", _jsx(_components.a, {\n        href: \"https://vercel.com/templates/next.js/supabase-partner-gallery\",\n        children: \"open source template\"\n      }), \" that you can use yourself).\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-8/day-4/featured-integrations.png\",\n        alt: \"Featured\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Supabase Integrations allows Partners to extend the Supabase platform with useful tooling. Today we're adding \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/platform/oauth-apps/publish-an-oauth-app\",\n        children: \"OAuth2.0 Applications\"\n      }), \". For Supabase users, this makes it even easier to connect their favorite tools to their Supabase projects. Within minutes you can:\"]\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Add your favorite \", _jsx(_components.a, {\n          href: \"https://supabase.com/partners/integrations#low-code\",\n          children: \"Low Code\"\n        }), \" tools on top of your Supabase database.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Integrate your favorite \", _jsx(_components.a, {\n          href: \"https://supabase.com/partners/integrations#devtools\",\n          children: \"DevTools\"\n        }), \": including secrets managers and database management tools.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Add \", _jsx(_components.a, {\n          href: \"https://supabase.com/partners/integrations#caching%20/%20offline-first\",\n          children: \"caching\"\n        }), \" to your Supabase database.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Not a fan of the Supabase admin dashboard? Try \", _jsx(_components.a, {\n          href: \"https://supabase.com/partners/integrations#data%20platform\",\n          children: \"one of these\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Try out a different \", _jsx(_components.a, {\n          href: \"https://supabase.com/partners/integrations#messaging\",\n          children: \"SMS and email provider\"\n        }), \".\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"featured-partners\",\n      children: \"Featured Partners\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"For the initial launch we've started with a few partners to help us build and test the OAuth functionality.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"cloudflare\",\n      children: \"Cloudflare\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-8/day-4/marketplace-cloudflare.jpg\",\n        alt: \"Cloudflare x Supabase\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We worked with Cloudflare to build \", _jsx(_components.a, {\n        href: \"https://blog.cloudflare.com/announcing-database-integrations/\",\n        children: \"support for databases\"\n      }), \" inside Cloudflare Workers. The Cloudflare integration makes it incredibly easy to connect to your Supabase database directly from the Cloudflare Dashboard.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Check out the \", _jsx(_components.a, {\n        href: \"https://cloudflare.tv/event/using-supabase-with-cloudflare-workers/dgM90RgD\",\n        children: \"latest episode\"\n      }), \" on Cloudflare TV to see it in action.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"resend\",\n      children: \"Resend\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-8/day-4/marketplace-resend.jpg\",\n        alt: \"Resend x Supabase\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://resend.com\",\n        children: \"Resend\"\n      }), \" (YC \", _jsx(_components.a, {\n        href: \"https://www.ycombinator.com/companies/resend\",\n        children: \"W23\"\n      }), \") is building the modern email sending platform. If you're using Supabase for Auth, then you'll know already that we handle all your Auth emails. But did you know that the email configuration we provide you is only for testing purposes? When you're \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/platform/going-into-prod#restricted-access-levels-for-team-members\",\n        children: \"going into production\"\n      }), \", you need to integrate your own email provider. That's where Resend come in. They've built a one-click integration to add Resend as a custom SMTP provider for Supabase.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Read more on \", _jsx(_components.a, {\n        href: \"https://resend.com/blog/how-to-configure-supabase-to-send-emails-from-your-domain\",\n        children: \"Resend's blog\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"snaplet\",\n      children: \"Snaplet\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-8/day-4/marketplace-snaplet.jpg\",\n        alt: \"Snaplet x Supabase\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Snaplet is a tool for Typescript developers to copy your database, transform sensitive data, and share it with your team without worrying about PII. If you followed our \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/supabase-local-dev#database-seeding\",\n        children: \"Tuesday launch\"\n      }), \" you'll be familiar with Snaplet - they are one of the best tools for \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/cli/seeding-your-database#generating-seed-data\",\n        children: \"generating seed data\"\n      }), \" for your local development environment. Now they are making it even easier, with their official OAuth App, to spin up production-like development environments for your team.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://www.snaplet.dev/post/now-live-supabase-x-snaplet-integration\",\n        children: \"Learn more on snaplet.dev\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"triggerdev\",\n      children: \"Trigger.dev\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-8/day-4/marketplace-triggerdev.jpg\",\n        alt: \"Trigger x Supabase\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"http://trigger.dev/\",\n        children: \"Trigger.dev\"\n      }), \" (YC \", _jsx(_components.a, {\n        href: \"https://www.ycombinator.com/companies/trigger-dev\",\n        children: \"W23\"\n      }), \") is the open source Background Jobs framework for Next.js. You can create long-running Jobs directly in your codebase with features like API integrations, webhooks, scheduling and delays. And today you can use their one-click integration to \", _jsx(_components.a, {\n        href: \"https://trigger.dev/supabase\",\n        children: \"trigger anything from a database change\"\n      }), \" in Supabase.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Learn more about their integration at: \", _jsx(_components.a, {\n        href: \"http://trigger.dev/supabase\",\n        children: \"trigger.dev/supabase\"\n      })]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"vercel\",\n      children: \"Vercel\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-8/day-4/marketplace-vercel.jpg\",\n        alt: \"Vercel x Supabase\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"One that requires no introduction - since so many of you use Vercel, we've dedicated an entire blog post to the upgraded Vercel integration.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Learn more about the Vercel integration \", _jsx(_components.a, {\n        href: \"/blog/using-supabase-with-vercel\",\n        children: \"updates we're launching\"\n      }), \" today.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"windmill\",\n      children: \"Windmill\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-8/day-4/marketplace-windmill.jpg\",\n        alt: \"Windmill x Supabase\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://windmill.dev\",\n        children: \"Windmill\"\n      }), \" (YC \", _jsx(_components.a, {\n        href: \"https://www.ycombinator.com/companies/windmill\",\n        children: \"S22\"\n      }), \") is an open source alternative to Retool and a modern Airflow. They provide a developer platform to quickly build production-grade complex workflows and integrations from minimal Python and Typescript scripts. Their one-click integration with Supabase makes it simple to launch new databases, process large quantities of data (maybe even convert them into \", _jsx(_components.a, {\n        href: \"https://supabase.com/vector\",\n        children: \"embeddings\"\n      }), \"), and build internal dashboards.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Read the \", _jsx(_components.a, {\n        href: \"https://www.windmill.dev/blog/2023/08/10/supabase-partnership\",\n        children: \"official blog post on windmill.dev\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"building-supabase-integrations\",\n      children: \"Building Supabase Integrations\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We've released full instructions in our \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/platform/oauth-apps/build-a-supabase-integration\",\n        children: \"Build with Supabase\"\n      }), \" documentation so that you can build your own Supabase OAuth application for your users. Simply visit your \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard/org/_/apps\",\n        children: \"Organization settings\"\n      }), \" and click “Add application” to get started:\"]\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"video-container\",\n      children: _jsx(\"iframe\", {\n        className: \"w-full\",\n        src: \"https://www.youtube-nocookie.com/embed/gtJo1lTxHfs\",\n        title: \"YouTube video player\",\n        frameBorder: \"0\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\",\n        allowfullscreen: true\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The Integrations marketplace is open to everyone. After your submission is complete, you can share the integration with your own users - simply create a button to launch your new app. We've provided some \", _jsx(_components.a, {\n        href: \"https://supabase.com/brand-assets\",\n        children: \"brand assets\"\n      }), \" so that developers can quickly identify the integration on your site.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"building-custom-integrations\",\n      children: \"Building custom integrations\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You don't actually need to build an OAuth Application to build an integration with Supabase. If you're building something for yourself or your team, the \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/reference/api/introduction\",\n        children: \"Management API\"\n      }), \" is the way to go.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The \", _jsx(_components.a, {\n        href: \"https://trigger.dev\",\n        children: \"Trigger.dev\"\n      }), \" team deserve a special shout out. While developing their Integration they also developed \", _jsx(_components.a, {\n        href: \"https://github.com/supabase-community/supabase-management-js\",\n        children: \"supabase-management-js\"\n      }), \", a Typescript library for the \", _jsx(_components.a, {\n        href: \"/docs/reference/api/introduction\",\n        children: \"Supabase Management API\"\n      }), \". This makes it even easier to get started with the Supabase API.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"It's useful beyond just integrations. Want to programmatically spin up databases? Easy:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { SupabaseManagementAPI } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"supabase-management-js\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"client \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= new \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"SupabaseManagementAPI\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\taccessToken: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"\u003caccess token\u003e\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"})\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"newProject \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" client.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"createProject\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t  name: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'staging'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t\\tdb_pass: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'XXX'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    organization_id: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'XXX'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t\\tplan: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'free'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    region: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'us-east-1'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"})\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"tsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"become-a-partner\",\n      children: \"Become a Partner\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Supabase is a collaborative company. We love working with other communities (especially open source ones!), and we'd love to work with you. Get started today:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"/docs/guides/platform/oauth-apps/build-a-supabase-integration\",\n          children: \"Build an OAuth integration\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"/docs/reference/api/introduction\",\n          children: \"Learn more about our Management API\"\n        })\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/launch-week-8/day-4/partner-with-supabase.png\",\n        alt: \"Partner with Supabase\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"more-launch-week-8\",\n      children: \"More Launch Week 8\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"/blog/supabase-local-dev\",\n          children: \"Supabase Local Dev: migrations, branching, and observability\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"/blog/hugging-face-supabase\",\n          children: \"Hugging Face is now supported in Supabase\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"/launch-week\",\n          children: \"Launch Week 8\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"/blog/interactive-constellation-threejs-react-three-fiber\",\n          children: \"Coding the stars - an interactive constellation with Three.js and React Three Fiber\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"/blog/why-supabase-remote\",\n          children: \"Why we'll stay remote\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase/postgres_lsp\",\n          children: \"Postgres Language Server\"\n        })\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"Featured Partners","slug":"featured-partners","lvl":2,"i":0,"seen":0},{"content":"Cloudflare","slug":"cloudflare","lvl":3,"i":1,"seen":0},{"content":"Resend","slug":"resend","lvl":3,"i":2,"seen":0},{"content":"Snaplet","slug":"snaplet","lvl":3,"i":3,"seen":0},{"content":"Trigger.dev","slug":"triggerdev","lvl":3,"i":4,"seen":0},{"content":"Vercel","slug":"vercel","lvl":3,"i":5,"seen":0},{"content":"Windmill","slug":"windmill","lvl":3,"i":6,"seen":0},{"content":"Building Supabase Integrations","slug":"building-supabase-integrations","lvl":2,"i":7,"seen":0},{"content":"Building custom integrations","slug":"building-custom-integrations","lvl":2,"i":8,"seen":0},{"content":"Become a Partner","slug":"become-a-partner","lvl":2,"i":9,"seen":0},{"content":"More Launch Week 8","slug":"more-launch-week-8","lvl":2,"i":10,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,2],"level":0},{"type":"inline","content":"We've been running our [Integrations Marketplace](/partners) in “stealth mode” for about a year now. What started as a dog-fooding project has now transformed into a marketplace with [over 60 integrations](/partners/integrations). (It's also an [open source template](https://vercel.com/templates/next.js/supabase-partner-gallery) that you can use yourself).","level":1,"lines":[1,2],"children":[{"type":"text","content":"We've been running our ","level":0},{"type":"link_open","href":"/partners","title":"","level":0},{"type":"text","content":"Integrations Marketplace","level":1},{"type":"link_close","level":0},{"type":"text","content":" in “stealth mode” for about a year now. What started as a dog-fooding project has now transformed into a marketplace with ","level":0},{"type":"link_open","href":"/partners/integrations","title":"","level":0},{"type":"text","content":"over 60 integrations","level":1},{"type":"link_close","level":0},{"type":"text","content":". (It's also an ","level":0},{"type":"link_open","href":"https://vercel.com/templates/next.js/supabase-partner-gallery","title":"","level":0},{"type":"text","content":"open source template","level":1},{"type":"link_close","level":0},{"type":"text","content":" that you can use yourself).","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[3,4],"level":0},{"type":"inline","content":"![Featured](/images/blog/launch-week-8/day-4/featured-integrations.png)","level":1,"lines":[3,4],"children":[{"type":"image","src":"/images/blog/launch-week-8/day-4/featured-integrations.png","title":"","alt":"Featured","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[5,6],"level":0},{"type":"inline","content":"Supabase Integrations allows Partners to extend the Supabase platform with useful tooling. Today we're adding [OAuth2.0 Applications](https://supabase.com/docs/guides/platform/oauth-apps/publish-an-oauth-app). For Supabase users, this makes it even easier to connect their favorite tools to their Supabase projects. Within minutes you can:","level":1,"lines":[5,6],"children":[{"type":"text","content":"Supabase Integrations allows Partners to extend the Supabase platform with useful tooling. Today we're adding ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/platform/oauth-apps/publish-an-oauth-app","title":"","level":0},{"type":"text","content":"OAuth2.0 Applications","level":1},{"type":"link_close","level":0},{"type":"text","content":". For Supabase users, this makes it even easier to connect their favorite tools to their Supabase projects. Within minutes you can:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[7,13],"level":0},{"type":"list_item_open","lines":[7,8],"level":1},{"type":"paragraph_open","tight":true,"lines":[7,8],"level":2},{"type":"inline","content":"Add your favorite [Low Code](https://supabase.com/partners/integrations#low-code) tools on top of your Supabase database.","level":3,"lines":[7,8],"children":[{"type":"text","content":"Add your favorite ","level":0},{"type":"link_open","href":"https://supabase.com/partners/integrations#low-code","title":"","level":0},{"type":"text","content":"Low Code","level":1},{"type":"link_close","level":0},{"type":"text","content":" tools on top of your Supabase database.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[8,9],"level":1},{"type":"paragraph_open","tight":true,"lines":[8,9],"level":2},{"type":"inline","content":"Integrate your favorite [DevTools](https://supabase.com/partners/integrations#devtools): including secrets managers and database management tools.","level":3,"lines":[8,9],"children":[{"type":"text","content":"Integrate your favorite ","level":0},{"type":"link_open","href":"https://supabase.com/partners/integrations#devtools","title":"","level":0},{"type":"text","content":"DevTools","level":1},{"type":"link_close","level":0},{"type":"text","content":": including secrets managers and database management tools.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[9,10],"level":1},{"type":"paragraph_open","tight":true,"lines":[9,10],"level":2},{"type":"inline","content":"Add [caching](https://supabase.com/partners/integrations#caching%20/%20offline-first) to your Supabase database.","level":3,"lines":[9,10],"children":[{"type":"text","content":"Add ","level":0},{"type":"link_open","href":"https://supabase.com/partners/integrations#caching%20/%20offline-first","title":"","level":0},{"type":"text","content":"caching","level":1},{"type":"link_close","level":0},{"type":"text","content":" to your Supabase database.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[10,11],"level":1},{"type":"paragraph_open","tight":true,"lines":[10,11],"level":2},{"type":"inline","content":"Not a fan of the Supabase admin dashboard? Try [one of these](https://supabase.com/partners/integrations#data%20platform).","level":3,"lines":[10,11],"children":[{"type":"text","content":"Not a fan of the Supabase admin dashboard? Try ","level":0},{"type":"link_open","href":"https://supabase.com/partners/integrations#data%20platform","title":"","level":0},{"type":"text","content":"one of these","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[11,13],"level":1},{"type":"paragraph_open","tight":true,"lines":[11,12],"level":2},{"type":"inline","content":"Try out a different [SMS and email provider](https://supabase.com/partners/integrations#messaging).","level":3,"lines":[11,12],"children":[{"type":"text","content":"Try out a different ","level":0},{"type":"link_open","href":"https://supabase.com/partners/integrations#messaging","title":"","level":0},{"type":"text","content":"SMS and email provider","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":2,"lines":[13,14],"level":0},{"type":"inline","content":"[Featured Partners](#featured-partners)","level":1,"lines":[13,14],"children":[{"type":"text","content":"Featured Partners","level":0}],"lvl":2,"i":0,"seen":0,"slug":"featured-partners"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[15,16],"level":0},{"type":"inline","content":"For the initial launch we've started with a few partners to help us build and test the OAuth functionality.","level":1,"lines":[15,16],"children":[{"type":"text","content":"For the initial launch we've started with a few partners to help us build and test the OAuth functionality.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[17,18],"level":0},{"type":"inline","content":"[Cloudflare](#cloudflare)","level":1,"lines":[17,18],"children":[{"type":"text","content":"Cloudflare","level":0}],"lvl":3,"i":1,"seen":0,"slug":"cloudflare"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[19,20],"level":0},{"type":"inline","content":"![Cloudflare x Supabase](/images/blog/launch-week-8/day-4/marketplace-cloudflare.jpg)","level":1,"lines":[19,20],"children":[{"type":"image","src":"/images/blog/launch-week-8/day-4/marketplace-cloudflare.jpg","title":"","alt":"Cloudflare x Supabase","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[21,22],"level":0},{"type":"inline","content":"We worked with Cloudflare to build [support for databases](https://blog.cloudflare.com/announcing-database-integrations/) inside Cloudflare Workers. The Cloudflare integration makes it incredibly easy to connect to your Supabase database directly from the Cloudflare Dashboard.","level":1,"lines":[21,22],"children":[{"type":"text","content":"We worked with Cloudflare to build ","level":0},{"type":"link_open","href":"https://blog.cloudflare.com/announcing-database-integrations/","title":"","level":0},{"type":"text","content":"support for databases","level":1},{"type":"link_close","level":0},{"type":"text","content":" inside Cloudflare Workers. The Cloudflare integration makes it incredibly easy to connect to your Supabase database directly from the Cloudflare Dashboard.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[23,24],"level":0},{"type":"inline","content":"Check out the [latest episode](https://cloudflare.tv/event/using-supabase-with-cloudflare-workers/dgM90RgD) on Cloudflare TV to see it in action.","level":1,"lines":[23,24],"children":[{"type":"text","content":"Check out the ","level":0},{"type":"link_open","href":"https://cloudflare.tv/event/using-supabase-with-cloudflare-workers/dgM90RgD","title":"","level":0},{"type":"text","content":"latest episode","level":1},{"type":"link_close","level":0},{"type":"text","content":" on Cloudflare TV to see it in action.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[25,26],"level":0},{"type":"inline","content":"[Resend](#resend)","level":1,"lines":[25,26],"children":[{"type":"text","content":"Resend","level":0}],"lvl":3,"i":2,"seen":0,"slug":"resend"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[27,28],"level":0},{"type":"inline","content":"![Resend x Supabase](/images/blog/launch-week-8/day-4/marketplace-resend.jpg)","level":1,"lines":[27,28],"children":[{"type":"image","src":"/images/blog/launch-week-8/day-4/marketplace-resend.jpg","title":"","alt":"Resend x Supabase","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[29,30],"level":0},{"type":"inline","content":"[Resend](https://resend.com) (YC [W23](https://www.ycombinator.com/companies/resend)) is building the modern email sending platform. If you're using Supabase for Auth, then you'll know already that we handle all your Auth emails. But did you know that the email configuration we provide you is only for testing purposes? When you're [going into production](https://supabase.com/docs/guides/platform/going-into-prod#restricted-access-levels-for-team-members), you need to integrate your own email provider. That's where Resend come in. They've built a one-click integration to add Resend as a custom SMTP provider for Supabase.","level":1,"lines":[29,30],"children":[{"type":"link_open","href":"https://resend.com","title":"","level":0},{"type":"text","content":"Resend","level":1},{"type":"link_close","level":0},{"type":"text","content":" (YC ","level":0},{"type":"link_open","href":"https://www.ycombinator.com/companies/resend","title":"","level":0},{"type":"text","content":"W23","level":1},{"type":"link_close","level":0},{"type":"text","content":") is building the modern email sending platform. If you're using Supabase for Auth, then you'll know already that we handle all your Auth emails. But did you know that the email configuration we provide you is only for testing purposes? When you're ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/platform/going-into-prod#restricted-access-levels-for-team-members","title":"","level":0},{"type":"text","content":"going into production","level":1},{"type":"link_close","level":0},{"type":"text","content":", you need to integrate your own email provider. That's where Resend come in. They've built a one-click integration to add Resend as a custom SMTP provider for Supabase.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[31,32],"level":0},{"type":"inline","content":"Read more on [Resend's blog](https://resend.com/blog/how-to-configure-supabase-to-send-emails-from-your-domain).","level":1,"lines":[31,32],"children":[{"type":"text","content":"Read more on ","level":0},{"type":"link_open","href":"https://resend.com/blog/how-to-configure-supabase-to-send-emails-from-your-domain","title":"","level":0},{"type":"text","content":"Resend's blog","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[33,34],"level":0},{"type":"inline","content":"[Snaplet](#snaplet)","level":1,"lines":[33,34],"children":[{"type":"text","content":"Snaplet","level":0}],"lvl":3,"i":3,"seen":0,"slug":"snaplet"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[35,36],"level":0},{"type":"inline","content":"![Snaplet x Supabase](/images/blog/launch-week-8/day-4/marketplace-snaplet.jpg)","level":1,"lines":[35,36],"children":[{"type":"image","src":"/images/blog/launch-week-8/day-4/marketplace-snaplet.jpg","title":"","alt":"Snaplet x Supabase","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[37,38],"level":0},{"type":"inline","content":"Snaplet is a tool for Typescript developers to copy your database, transform sensitive data, and share it with your team without worrying about PII. If you followed our [Tuesday launch](https://supabase.com/blog/supabase-local-dev#database-seeding) you'll be familiar with Snaplet - they are one of the best tools for [generating seed data](https://supabase.com/docs/guides/cli/seeding-your-database#generating-seed-data) for your local development environment. Now they are making it even easier, with their official OAuth App, to spin up production-like development environments for your team.","level":1,"lines":[37,38],"children":[{"type":"text","content":"Snaplet is a tool for Typescript developers to copy your database, transform sensitive data, and share it with your team without worrying about PII. If you followed our ","level":0},{"type":"link_open","href":"https://supabase.com/blog/supabase-local-dev#database-seeding","title":"","level":0},{"type":"text","content":"Tuesday launch","level":1},{"type":"link_close","level":0},{"type":"text","content":" you'll be familiar with Snaplet - they are one of the best tools for ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/cli/seeding-your-database#generating-seed-data","title":"","level":0},{"type":"text","content":"generating seed data","level":1},{"type":"link_close","level":0},{"type":"text","content":" for your local development environment. Now they are making it even easier, with their official OAuth App, to spin up production-like development environments for your team.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[39,40],"level":0},{"type":"inline","content":"[Learn more on snaplet.dev](https://www.snaplet.dev/post/now-live-supabase-x-snaplet-integration).","level":1,"lines":[39,40],"children":[{"type":"link_open","href":"https://www.snaplet.dev/post/now-live-supabase-x-snaplet-integration","title":"","level":0},{"type":"text","content":"Learn more on snaplet.dev","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[41,42],"level":0},{"type":"inline","content":"[Trigger.dev](#triggerdev)","level":1,"lines":[41,42],"children":[{"type":"text","content":"Trigger.dev","level":0}],"lvl":3,"i":4,"seen":0,"slug":"triggerdev"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[43,44],"level":0},{"type":"inline","content":"![Trigger x Supabase](/images/blog/launch-week-8/day-4/marketplace-triggerdev.jpg)","level":1,"lines":[43,44],"children":[{"type":"image","src":"/images/blog/launch-week-8/day-4/marketplace-triggerdev.jpg","title":"","alt":"Trigger x Supabase","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[45,46],"level":0},{"type":"inline","content":"[Trigger.dev](http://trigger.dev/) (YC [W23](https://www.ycombinator.com/companies/trigger-dev)) is the open source Background Jobs framework for Next.js. You can create long-running Jobs directly in your codebase with features like API integrations, webhooks, scheduling and delays. And today you can use their one-click integration to [trigger anything from a database change](https://trigger.dev/supabase) in Supabase.","level":1,"lines":[45,46],"children":[{"type":"link_open","href":"http://trigger.dev/","title":"","level":0},{"type":"text","content":"Trigger.dev","level":1},{"type":"link_close","level":0},{"type":"text","content":" (YC ","level":0},{"type":"link_open","href":"https://www.ycombinator.com/companies/trigger-dev","title":"","level":0},{"type":"text","content":"W23","level":1},{"type":"link_close","level":0},{"type":"text","content":") is the open source Background Jobs framework for Next.js. You can create long-running Jobs directly in your codebase with features like API integrations, webhooks, scheduling and delays. And today you can use their one-click integration to ","level":0},{"type":"link_open","href":"https://trigger.dev/supabase","title":"","level":0},{"type":"text","content":"trigger anything from a database change","level":1},{"type":"link_close","level":0},{"type":"text","content":" in Supabase.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[47,48],"level":0},{"type":"inline","content":"Learn more about their integration at: [trigger.dev/supabase](http://trigger.dev/supabase)","level":1,"lines":[47,48],"children":[{"type":"text","content":"Learn more about their integration at: ","level":0},{"type":"link_open","href":"http://trigger.dev/supabase","title":"","level":0},{"type":"text","content":"trigger.dev/supabase","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[49,50],"level":0},{"type":"inline","content":"[Vercel](#vercel)","level":1,"lines":[49,50],"children":[{"type":"text","content":"Vercel","level":0}],"lvl":3,"i":5,"seen":0,"slug":"vercel"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[51,52],"level":0},{"type":"inline","content":"![Vercel x Supabase](/images/blog/launch-week-8/day-4/marketplace-vercel.jpg)","level":1,"lines":[51,52],"children":[{"type":"image","src":"/images/blog/launch-week-8/day-4/marketplace-vercel.jpg","title":"","alt":"Vercel x Supabase","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[53,54],"level":0},{"type":"inline","content":"One that requires no introduction - since so many of you use Vercel, we've dedicated an entire blog post to the upgraded Vercel integration.","level":1,"lines":[53,54],"children":[{"type":"text","content":"One that requires no introduction - since so many of you use Vercel, we've dedicated an entire blog post to the upgraded Vercel integration.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[55,56],"level":0},{"type":"inline","content":"Learn more about the Vercel integration [updates we're launching](/blog/using-supabase-with-vercel) today.","level":1,"lines":[55,56],"children":[{"type":"text","content":"Learn more about the Vercel integration ","level":0},{"type":"link_open","href":"/blog/using-supabase-with-vercel","title":"","level":0},{"type":"text","content":"updates we're launching","level":1},{"type":"link_close","level":0},{"type":"text","content":" today.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[57,58],"level":0},{"type":"inline","content":"[Windmill](#windmill)","level":1,"lines":[57,58],"children":[{"type":"text","content":"Windmill","level":0}],"lvl":3,"i":6,"seen":0,"slug":"windmill"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[59,60],"level":0},{"type":"inline","content":"![Windmill x Supabase](/images/blog/launch-week-8/day-4/marketplace-windmill.jpg)","level":1,"lines":[59,60],"children":[{"type":"image","src":"/images/blog/launch-week-8/day-4/marketplace-windmill.jpg","title":"","alt":"Windmill x Supabase","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[61,62],"level":0},{"type":"inline","content":"[Windmill](https://windmill.dev) (YC [S22](https://www.ycombinator.com/companies/windmill)) is an open source alternative to Retool and a modern Airflow. They provide a developer platform to quickly build production-grade complex workflows and integrations from minimal Python and Typescript scripts. Their one-click integration with Supabase makes it simple to launch new databases, process large quantities of data (maybe even convert them into [embeddings](https://supabase.com/vector)), and build internal dashboards.","level":1,"lines":[61,62],"children":[{"type":"link_open","href":"https://windmill.dev","title":"","level":0},{"type":"text","content":"Windmill","level":1},{"type":"link_close","level":0},{"type":"text","content":" (YC ","level":0},{"type":"link_open","href":"https://www.ycombinator.com/companies/windmill","title":"","level":0},{"type":"text","content":"S22","level":1},{"type":"link_close","level":0},{"type":"text","content":") is an open source alternative to Retool and a modern Airflow. They provide a developer platform to quickly build production-grade complex workflows and integrations from minimal Python and Typescript scripts. Their one-click integration with Supabase makes it simple to launch new databases, process large quantities of data (maybe even convert them into ","level":0},{"type":"link_open","href":"https://supabase.com/vector","title":"","level":0},{"type":"text","content":"embeddings","level":1},{"type":"link_close","level":0},{"type":"text","content":"), and build internal dashboards.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[63,64],"level":0},{"type":"inline","content":"Read the [official blog post on windmill.dev](https://www.windmill.dev/blog/2023/08/10/supabase-partnership).","level":1,"lines":[63,64],"children":[{"type":"text","content":"Read the ","level":0},{"type":"link_open","href":"https://www.windmill.dev/blog/2023/08/10/supabase-partnership","title":"","level":0},{"type":"text","content":"official blog post on windmill.dev","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[65,66],"level":0},{"type":"inline","content":"[Building Supabase Integrations](#building-supabase-integrations)","level":1,"lines":[65,66],"children":[{"type":"text","content":"Building Supabase Integrations","level":0}],"lvl":2,"i":7,"seen":0,"slug":"building-supabase-integrations"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[67,68],"level":0},{"type":"inline","content":"We've released full instructions in our [Build with Supabase](https://supabase.com/docs/guides/platform/oauth-apps/build-a-supabase-integration) documentation so that you can build your own Supabase OAuth application for your users. Simply visit your [Organization settings](https://supabase.com/dashboard/org/_/apps) and click “Add application” to get started:","level":1,"lines":[67,68],"children":[{"type":"text","content":"We've released full instructions in our ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/platform/oauth-apps/build-a-supabase-integration","title":"","level":0},{"type":"text","content":"Build with Supabase","level":1},{"type":"link_close","level":0},{"type":"text","content":" documentation so that you can build your own Supabase OAuth application for your users. Simply visit your ","level":0},{"type":"link_open","href":"https://supabase.com/dashboard/org/_/apps","title":"","level":0},{"type":"text","content":"Organization settings","level":1},{"type":"link_close","level":0},{"type":"text","content":" and click “Add application” to get started:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[69,77],"level":0},{"type":"inline","content":"\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/gtJo1lTxHfs\"\n    title=\"YouTube video player\"\n    frameBorder=\"0\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"\n    allowfullscreen","level":1,"lines":[69,77],"children":[{"type":"text","content":"\u003cdiv className=\"video-container\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ciframe","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"w-full\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://www.youtube-nocookie.com/embed/gtJo1lTxHfs\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"title=\"YouTube video player\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"frameBorder=\"0\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allowfullscreen","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"blockquote_open","lines":[77,79],"level":0},{"type":"paragraph_open","tight":false,"lines":[77,79],"level":1},{"type":"inline","content":"\u003c/iframe\u003e\n\u003c/div\u003e","level":2,"lines":[77,79],"children":[{"type":"text","content":"\u003c/iframe\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"paragraph_open","tight":false,"lines":[80,81],"level":0},{"type":"inline","content":"The Integrations marketplace is open to everyone. After your submission is complete, you can share the integration with your own users - simply create a button to launch your new app. We've provided some [brand assets](https://supabase.com/brand-assets) so that developers can quickly identify the integration on your site.","level":1,"lines":[80,81],"children":[{"type":"text","content":"The Integrations marketplace is open to everyone. After your submission is complete, you can share the integration with your own users - simply create a button to launch your new app. We've provided some ","level":0},{"type":"link_open","href":"https://supabase.com/brand-assets","title":"","level":0},{"type":"text","content":"brand assets","level":1},{"type":"link_close","level":0},{"type":"text","content":" so that developers can quickly identify the integration on your site.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[82,83],"level":0},{"type":"inline","content":"[Building custom integrations](#building-custom-integrations)","level":1,"lines":[82,83],"children":[{"type":"text","content":"Building custom integrations","level":0}],"lvl":2,"i":8,"seen":0,"slug":"building-custom-integrations"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[84,85],"level":0},{"type":"inline","content":"You don't actually need to build an OAuth Application to build an integration with Supabase. If you're building something for yourself or your team, the [Management API](https://supabase.com/docs/reference/api/introduction) is the way to go.","level":1,"lines":[84,85],"children":[{"type":"text","content":"You don't actually need to build an OAuth Application to build an integration with Supabase. If you're building something for yourself or your team, the ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/api/introduction","title":"","level":0},{"type":"text","content":"Management API","level":1},{"type":"link_close","level":0},{"type":"text","content":" is the way to go.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[86,87],"level":0},{"type":"inline","content":"The [Trigger.dev](https://trigger.dev) team deserve a special shout out. While developing their Integration they also developed [supabase-management-js](https://github.com/supabase-community/supabase-management-js), a Typescript library for the [Supabase Management API](/docs/reference/api/introduction). This makes it even easier to get started with the Supabase API.","level":1,"lines":[86,87],"children":[{"type":"text","content":"The ","level":0},{"type":"link_open","href":"https://trigger.dev","title":"","level":0},{"type":"text","content":"Trigger.dev","level":1},{"type":"link_close","level":0},{"type":"text","content":" team deserve a special shout out. While developing their Integration they also developed ","level":0},{"type":"link_open","href":"https://github.com/supabase-community/supabase-management-js","title":"","level":0},{"type":"text","content":"supabase-management-js","level":1},{"type":"link_close","level":0},{"type":"text","content":", a Typescript library for the ","level":0},{"type":"link_open","href":"/docs/reference/api/introduction","title":"","level":0},{"type":"text","content":"Supabase Management API","level":1},{"type":"link_close","level":0},{"type":"text","content":". This makes it even easier to get started with the Supabase API.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[88,89],"level":0},{"type":"inline","content":"It's useful beyond just integrations. Want to programmatically spin up databases? Easy:","level":1,"lines":[88,89],"children":[{"type":"text","content":"It's useful beyond just integrations. Want to programmatically spin up databases? Easy:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"tsx","content":"import { SupabaseManagementAPI } from \"supabase-management-js\";\n\nconst client = new SupabaseManagementAPI({\n    accessToken: \"\u003caccess token\u003e\"\n})\n\nconst newProject = await client.createProject({\n      name: 'staging',\n        db_pass: 'XXX',\n    organization_id: 'XXX'\n        plan: 'free',\n    region: 'us-east-1'\n})\n","lines":[90,105],"level":0},{"type":"heading_open","hLevel":2,"lines":[106,107],"level":0},{"type":"inline","content":"[Become a Partner](#become-a-partner)","level":1,"lines":[106,107],"children":[{"type":"text","content":"Become a Partner","level":0}],"lvl":2,"i":9,"seen":0,"slug":"become-a-partner"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[108,109],"level":0},{"type":"inline","content":"Supabase is a collaborative company. We love working with other communities (especially open source ones!), and we'd love to work with you. Get started today:","level":1,"lines":[108,109],"children":[{"type":"text","content":"Supabase is a collaborative company. We love working with other communities (especially open source ones!), and we'd love to work with you. Get started today:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[110,113],"level":0},{"type":"list_item_open","lines":[110,111],"level":1},{"type":"paragraph_open","tight":true,"lines":[110,111],"level":2},{"type":"inline","content":"[Build an OAuth integration](/docs/guides/platform/oauth-apps/build-a-supabase-integration)","level":3,"lines":[110,111],"children":[{"type":"link_open","href":"/docs/guides/platform/oauth-apps/build-a-supabase-integration","title":"","level":0},{"type":"text","content":"Build an OAuth integration","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[111,113],"level":1},{"type":"paragraph_open","tight":true,"lines":[111,112],"level":2},{"type":"inline","content":"[Learn more about our Management API](/docs/reference/api/introduction)","level":3,"lines":[111,112],"children":[{"type":"link_open","href":"/docs/reference/api/introduction","title":"","level":0},{"type":"text","content":"Learn more about our Management API","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[113,114],"level":0},{"type":"inline","content":"![Partner with Supabase](/images/blog/launch-week-8/day-4/partner-with-supabase.png)","level":1,"lines":[113,114],"children":[{"type":"image","src":"/images/blog/launch-week-8/day-4/partner-with-supabase.png","title":"","alt":"Partner with Supabase","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[115,116],"level":0},{"type":"inline","content":"[More Launch Week 8](#more-launch-week-8)","level":1,"lines":[115,116],"children":[{"type":"text","content":"More Launch Week 8","level":0}],"lvl":2,"i":10,"seen":0,"slug":"more-launch-week-8"},{"type":"heading_close","hLevel":2,"level":0},{"type":"bullet_list_open","lines":[117,123],"level":0},{"type":"list_item_open","lines":[117,118],"level":1},{"type":"paragraph_open","tight":true,"lines":[117,118],"level":2},{"type":"inline","content":"[Supabase Local Dev: migrations, branching, and observability](/blog/supabase-local-dev)","level":3,"lines":[117,118],"children":[{"type":"link_open","href":"/blog/supabase-local-dev","title":"","level":0},{"type":"text","content":"Supabase Local Dev: migrations, branching, and observability","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[118,119],"level":1},{"type":"paragraph_open","tight":true,"lines":[118,119],"level":2},{"type":"inline","content":"[Hugging Face is now supported in Supabase](/blog/hugging-face-supabase)","level":3,"lines":[118,119],"children":[{"type":"link_open","href":"/blog/hugging-face-supabase","title":"","level":0},{"type":"text","content":"Hugging Face is now supported in Supabase","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[119,120],"level":1},{"type":"paragraph_open","tight":true,"lines":[119,120],"level":2},{"type":"inline","content":"[Launch Week 8](/launch-week)","level":3,"lines":[119,120],"children":[{"type":"link_open","href":"/launch-week","title":"","level":0},{"type":"text","content":"Launch Week 8","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[120,121],"level":1},{"type":"paragraph_open","tight":true,"lines":[120,121],"level":2},{"type":"inline","content":"[Coding the stars - an interactive constellation with Three.js and React Three Fiber](/blog/interactive-constellation-threejs-react-three-fiber)","level":3,"lines":[120,121],"children":[{"type":"link_open","href":"/blog/interactive-constellation-threejs-react-three-fiber","title":"","level":0},{"type":"text","content":"Coding the stars - an interactive constellation with Three.js and React Three Fiber","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[121,122],"level":1},{"type":"paragraph_open","tight":true,"lines":[121,122],"level":2},{"type":"inline","content":"[Why we'll stay remote](/blog/why-supabase-remote)","level":3,"lines":[121,122],"children":[{"type":"link_open","href":"/blog/why-supabase-remote","title":"","level":0},{"type":"text","content":"Why we'll stay remote","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[122,123],"level":1},{"type":"paragraph_open","tight":true,"lines":[122,123],"level":2},{"type":"inline","content":"[Postgres Language Server](https://github.com/supabase/postgres_lsp)","level":3,"lines":[122,123],"children":[{"type":"link_open","href":"https://github.com/supabase/postgres_lsp","title":"","level":0},{"type":"text","content":"Postgres Language Server","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0}],"content":"- [Featured Partners](#featured-partners)\n  * [Cloudflare](#cloudflare)\n  * [Resend](#resend)\n  * [Snaplet](#snaplet)\n  * [Trigger.dev](#triggerdev)\n  * [Vercel](#vercel)\n  * [Windmill](#windmill)\n- [Building Supabase Integrations](#building-supabase-integrations)\n- [Building custom integrations](#building-custom-integrations)\n- [Become a Partner](#become-a-partner)\n- [More Launch Week 8](#more-launch-week-8)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-integrations-marketplace"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>