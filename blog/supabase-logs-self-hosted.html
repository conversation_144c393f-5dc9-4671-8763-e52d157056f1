<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase Logs: open source logging server</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="We&#x27;re releasing Supabase Logs for both self-hosted users and CLI development." data-next-head=""/><meta property="og:title" content="Supabase Logs: open source logging server" data-next-head=""/><meta property="og:description" content="We&#x27;re releasing Supabase Logs for both self-hosted users and CLI development." data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-logs-self-hosted" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2023-04-10" data-next-head=""/><meta property="article:author" content="https://github.com/chasers" data-next-head=""/><meta property="article:author" content="https://github.com/Ziinc" data-next-head=""/><meta property="article:tag" content="launch-week" data-next-head=""/><meta property="article:tag" content="o11y" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/launch-week-7/day-1-supabase-logs-self-hosted/day-1-supabase-logs-self-hosted-og.jpg" data-next-head=""/><meta property="og:image:alt" content="Supabase Logs: open source logging server thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Supabase Logs: open source logging server</h1><div class="text-light flex space-x-3 text-sm"><p>10 Apr 2023</p><p>•</p><p>8 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/chasers"><div class="flex items-center gap-3"><div class="w-10"><img alt="Chase Granberry avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fchasers.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fchasers.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fchasers.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Chase Granberry</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/Ziinc"><div class="flex items-center gap-3"><div class="w-10"><img alt="Lee TzeYiing avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2FZiinc.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2FZiinc.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2FZiinc.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Lee TzeYiing</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Supabase Logs: open source logging server" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-1-supabase-logs-self-hosted%2Fday-1-supabase-logs-self-hosted-thumb.jpg&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-1-supabase-logs-self-hosted%2Fday-1-supabase-logs-self-hosted-thumb.jpg&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-1-supabase-logs-self-hosted%2Fday-1-supabase-logs-self-hosted-thumb.jpg&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-1-supabase-logs-self-hosted%2Fday-1-supabase-logs-self-hosted-thumb.jpg&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-1-supabase-logs-self-hosted%2Fday-1-supabase-logs-self-hosted-thumb.jpg&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-1-supabase-logs-self-hosted%2Fday-1-supabase-logs-self-hosted-thumb.jpg&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-1-supabase-logs-self-hosted%2Fday-1-supabase-logs-self-hosted-thumb.jpg&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-1-supabase-logs-self-hosted%2Fday-1-supabase-logs-self-hosted-thumb.jpg&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-1-supabase-logs-self-hosted%2Fday-1-supabase-logs-self-hosted-thumb.jpg&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-1-supabase-logs-self-hosted%2Fday-1-supabase-logs-self-hosted-thumb.jpg&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-7%2Fday-1-supabase-logs-self-hosted%2Fday-1-supabase-logs-self-hosted-thumb.jpg&amp;w=3840&amp;q=100"/></div><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p>Today, we’re releasing Supabase Logs for both self-hosted users and CLI development.</p>
<h2 id="logflare-introduction" class="group scroll-mt-24">Logflare Introduction<a href="#logflare-introduction" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Since <a href="https://supabase.com/blog/supabase-acquires-logflare">Logflare joined Supabase</a> over a year ago it’s been quietly handling over 1 billion log events every day. These events come from various tools in the Supabase infrastructure - the API gateway, Postgres databases, Storage, Edge Functions, Auth, and Realtime.</p>
<p>Logflare is a multi-node, highly available Elixir cluster, ingesting the log events and storing them into BigQuery for Supabase and Logflare’s customers. On average, the cluster has 6 nodes handling every spike our customers throw at it.</p>
<p>To expose log data to customers, we leverage Logflare Endpoints. This provides an HTTP integration into Supabase Studio, powering the log query UIs and most time-series charts. These charts live across the studio, such as the project home page and the new API reports.</p>
<div class="video-container"><iframe class="w-full" src="https://www.youtube-nocookie.com/embed/Ai2BjHV36Ng" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share"></iframe></div>
<h2 id="self-hosting-logflare" class="group scroll-mt-24">Self-hosting Logflare<a href="#self-hosting-logflare" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Logflare was available under a BSL license prior to joining Supabase. We’ve since changed the license to <a href="https://github.com/Logflare/logflare/blob/staging/LICENSE.md">Apache 2.0</a>, aligning it with our open source philosophy.</p>
<p>In the past few months we’ve made Logflare more developer-friendly for local development and self-hosting. While you’re building a project, you can view and query your logs from any Supabase service, just as you would in our cloud platform.</p>
<p></p>
<div class="bg-gray-300 rounded-lg px-6 py-2 italic"><p>📢 Check out the <a href="../docs/reference/self-hosting-analytics/introduction.html">new self-hosting docs</a> to get Logflare up and running as your analytics server.</p></div>
<p>It currently supports a BigQuery backend, and we are actively working on supporting more.</p>
<h2 id="the-ingestion-pipeline" class="group scroll-mt-24">The Ingestion Pipeline<a href="#the-ingestion-pipeline" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Logflare receives Supabase log events via multiple methods. Services like Postgres use <a href="https://vector.dev/">Vector</a> to clean and forward log events to the Logflare ingest API. Other services such as Realtime and Storage utilize <a href="https://github.com/Logflare/logflare#integrations">native Logflare integrations</a> to send the log events directly. These then get processed and streamed into BigQuery.</p>
<h2 id="the-querying-pipeline" class="group scroll-mt-24">The Querying Pipeline<a href="#the-querying-pipeline" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>The hard part comes after ingesting the logs: searching, aggregating ,and analyzing them at scale. Crunching many terabytes of data on each query is expensive, and exposing the ingested data to Supabase customers in a naive manner would cause our costs to skyrocket.</p>
<p>To solve these issues, we built and refined Logflare Endpoints, the query engine that powers many of Supabase’s features, such as the logs views, Logs Explorer, and usage charts.</p>
<p>With Endpoints, you can create HTTP API endpoints from a SQL query, including parameterized queries. Endpoints are like PostgREST views but with some benefits:</p>
<ul>
<li><strong>Query parameters</strong>
<ul>
<li>You can provide string parameters to the SQL query via the HTTP endpoint.</li>
</ul>
</li>
<li><strong>Read-through caching</strong>
<ul>
<li>Results from the query are cached in memory for fast response times.</li>
<li>A read-through cache provides results if cached results do not exist.</li>
</ul>
</li>
<li><strong>Active cache warming</strong>
<ul>
<li>Query results are proactively warmed at a configurable interval for a combination of fast response times and as-realtime-as-needed data.</li>
</ul>
</li>
<li><strong>Query sandboxing</strong>
<ul>
<li>If an Endpoint query contains a CTE and the sandbox option is selected, the Endpoint will inject the query string of the <code class="short-inline-codeblock">sql</code> query parameter into the Endpoint SQL replacing the default query (the part of the SQL query after the CTE).</li>
<li>Endpoints parse SQL to allow <code class="short-inline-codeblock">select</code> queries only. No DML or DDL statements are permitted to run through Logflare Endpoints.</li>
</ul>
</li>
</ul>
<p>With this feature set, Supabase has been able to build any view we’ve needed on top of billions of daily log events.</p>
<h3 id="logflare-endpoint-example" class="group scroll-mt-24">Logflare Endpoint Example<a href="#logflare-endpoint-example" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Using webhooks, we can send all GitHub events in the Supabase organization to Logflare. The webhook sends structured events, and Logflare transforms the payload into metadata:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->17</span><div style="display:inline-block;margin-left:16px"><span>{</span></div></div><div><span class="ch-code-line-number">_<!-- -->17</span><div style="display:inline-block;margin-left:16px"><span>  &quot;event_message&quot;: &quot;supabase/supabase | JohannesBauer97 | created&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->17</span><div style="display:inline-block;margin-left:16px"><span>  &quot;id&quot;: &quot;0d48b71d-91c5-4356-82c7-fdb299b625d0&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->17</span><div style="display:inline-block;margin-left:16px"><span>  &quot;metadata&quot;: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->17</span><div style="display:inline-block;margin-left:16px"><span>    &quot;sender&quot;: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->17</span><div style="display:inline-block;margin-left:16px"><span>      &quot;id&quot;: 15695124,</span></div></div><div><span class="ch-code-line-number">_<!-- -->17</span><div style="display:inline-block;margin-left:16px"><span>      &quot;login&quot;: &quot;JohannesBauer97&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->17</span><div style="display:inline-block;margin-left:16px"><span>      &quot;node_id&quot;: &quot;MDQ6VXNlcjE1Njk1MTI0&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->17</span><div style="display:inline-block;margin-left:16px"><span>      &quot;site_admin&quot;: false,</span></div></div><div><span class="ch-code-line-number">_<!-- -->17</span><div style="display:inline-block;margin-left:16px"><span>      &quot;type&quot;: &quot;User&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->17</span><div style="display:inline-block;margin-left:16px"><span>      &quot;url&quot;: &quot;https://api.github.com/users/JohannesBauer97&quot;</span></div></div><div><span class="ch-code-line-number">_<!-- -->17</span><div style="display:inline-block;margin-left:16px"><span>    },</span></div></div><div><span class="ch-code-line-number">_<!-- -->17</span><div style="display:inline-block;margin-left:16px"><span>    &quot;starred_at&quot;: &quot;2023-03-30T20:33:55Z&quot;</span></div></div><div><span class="ch-code-line-number">_<!-- -->17</span><div style="display:inline-block;margin-left:16px"><span>    //...</span></div></div><div><span class="ch-code-line-number">_<!-- -->17</span><div style="display:inline-block;margin-left:16px"><span>  },</span></div></div><div><span class="ch-code-line-number">_<!-- -->17</span><div style="display:inline-block;margin-left:16px"><span>  &quot;timestamp&quot;: 1680208436849642</span></div></div><div><span class="ch-code-line-number">_<!-- -->17</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<p>We’re interested in the top contributors, which can be extracted with SQL (in BigQuery dialect):</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->15</span><div style="display:inline-block;margin-left:16px"><span>select</span></div></div><div><span class="ch-code-line-number">_<!-- -->15</span><div style="display:inline-block;margin-left:16px"><span>  count(t.timestamp) as count,</span></div></div><div><span class="ch-code-line-number">_<!-- -->15</span><div style="display:inline-block;margin-left:16px"><span>  s.login as gh_user</span></div></div><div><span class="ch-code-line-number">_<!-- -->15</span><div style="display:inline-block;margin-left:16px"><span>from</span></div></div><div><span class="ch-code-line-number">_<!-- -->15</span><div style="display:inline-block;margin-left:16px"><span>  `github.supabase.webhooks` as t</span></div></div><div><span class="ch-code-line-number">_<!-- -->15</span><div style="display:inline-block;margin-left:16px"><span>  cross join unnest(metadata) as m</span></div></div><div><span class="ch-code-line-number">_<!-- -->15</span><div style="display:inline-block;margin-left:16px"><span>  cross join unnest(m.sender) as s</span></div></div><div><span class="ch-code-line-number">_<!-- -->15</span><div style="display:inline-block;margin-left:16px"><span>where</span></div></div><div><span class="ch-code-line-number">_<!-- -->15</span><div style="display:inline-block;margin-left:16px"><span>  timestamp::date &gt; current_date() - @day::int</span></div></div><div><span class="ch-code-line-number">_<!-- -->15</span><div style="display:inline-block;margin-left:16px"><span>group by</span></div></div><div><span class="ch-code-line-number">_<!-- -->15</span><div style="display:inline-block;margin-left:16px"><span>  gh_user</span></div></div><div><span class="ch-code-line-number">_<!-- -->15</span><div style="display:inline-block;margin-left:16px"><span>order by</span></div></div><div><span class="ch-code-line-number">_<!-- -->15</span><div style="display:inline-block;margin-left:16px"><span>  count desc</span></div></div><div><span class="ch-code-line-number">_<!-- -->15</span><div style="display:inline-block;margin-left:16px"><span>limit</span></div></div><div><span class="ch-code-line-number">_<!-- -->15</span><div style="display:inline-block;margin-left:16px"><span>  25</span></div></div><br/></code></div></div>
<p>With this view in place, we can use Endpoints to provide an API that we can hit from our application:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>curl &quot;https://logflare.app/endpoints/query/69425db0-1cfb-48b4-84c7-2a872b6f0a61&quot; \</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span> -H &#x27;Content-Type: application/json; charset=utf-8&#x27; \</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span> -G -d &quot;day=30&quot;</span></div></div><br/></code></div></div>
<p>This returns a JSON response with the top org wide contributors for the last 30 days!</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->16</span><div style="display:inline-block;margin-left:16px"><span>{</span></div></div><div><span class="ch-code-line-number">_<!-- -->16</span><div style="display:inline-block;margin-left:16px"><span>  &quot;result&quot;: [</span></div></div><div><span class="ch-code-line-number">_<!-- -->16</span><div style="display:inline-block;margin-left:16px"><span>    { &quot;count&quot;: 23404, &quot;gh_user&quot;: &quot;vercel[bot]&quot; },</span></div></div><div><span class="ch-code-line-number">_<!-- -->16</span><div style="display:inline-block;margin-left:16px"><span>    { &quot;count&quot;: 10005, &quot;gh_user&quot;: &quot;joshenlim&quot; },</span></div></div><div><span class="ch-code-line-number">_<!-- -->16</span><div style="display:inline-block;margin-left:16px"><span>    { &quot;count&quot;: 7026, &quot;gh_user&quot;: &quot;MildTomato&quot; },</span></div></div><div><span class="ch-code-line-number">_<!-- -->16</span><div style="display:inline-block;margin-left:16px"><span>    { &quot;count&quot;: 6405, &quot;gh_user&quot;: &quot;fsansalvadore&quot; },</span></div></div><div><span class="ch-code-line-number">_<!-- -->16</span><div style="display:inline-block;margin-left:16px"><span>    { &quot;count&quot;: 5195, &quot;gh_user&quot;: &quot;saltcod&quot; },</span></div></div><div><span class="ch-code-line-number">_<!-- -->16</span><div style="display:inline-block;margin-left:16px"><span>    { &quot;count&quot;: 3454, &quot;gh_user&quot;: &quot;alaister&quot; },</span></div></div><div><span class="ch-code-line-number">_<!-- -->16</span><div style="display:inline-block;margin-left:16px"><span>    { &quot;count&quot;: 2691, &quot;gh_user&quot;: &quot;kevcodez&quot; },</span></div></div><div><span class="ch-code-line-number">_<!-- -->16</span><div style="display:inline-block;margin-left:16px"><span>    { &quot;count&quot;: 2117, &quot;gh_user&quot;: &quot;gregnr&quot; },</span></div></div><div><span class="ch-code-line-number">_<!-- -->16</span><div style="display:inline-block;margin-left:16px"><span>    { &quot;count&quot;: 1769, &quot;gh_user&quot;: &quot;Ziinc&quot; },</span></div></div><div><span class="ch-code-line-number">_<!-- -->16</span><div style="display:inline-block;margin-left:16px"><span>    { &quot;count&quot;: 1749, &quot;gh_user&quot;: &quot;chasers&quot; },</span></div></div><div><span class="ch-code-line-number">_<!-- -->16</span><div style="display:inline-block;margin-left:16px"><span>    { &quot;count&quot;: 1430, &quot;gh_user&quot;: &quot;Isaiah-Hamilton&quot; }</span></div></div><div><span class="ch-code-line-number">_<!-- -->16</span><div style="display:inline-block;margin-left:16px"><span>    //...</span></div></div><div><span class="ch-code-line-number">_<!-- -->16</span><div style="display:inline-block;margin-left:16px"><span>  ]</span></div></div><div><span class="ch-code-line-number">_<!-- -->16</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<p>We can configure this Endpoint to cache results for an interval of 10 minutes after the first API request, and proactively update those cached results every 2 minutes - 5 queries across the 10 minute interval. Even if we hit the Endpoint thousands of times, we only sustain the cost of 5 queries.</p>
<p>The initial request is fast because Logflare also performs setup (such as partitioning) on our BigQuery tables appropriately. Subsequent requests are <em>extremely fast</em> as they are cached in-memory.</p>
<p>The best part is that all these knobs can be tweaked for your use case. If we have a real-time requirement, we can completely disable caching or reduce the proactive caching to update on a per-second interval.</p>
<h2 id="the-self-hosted-challenge" class="group scroll-mt-24">The Self-hosted Challenge<a href="#the-self-hosted-challenge" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>To change the license, we needed to remove all closed-source dependencies. Previously, Logflare relied on the closed source <a href="https://www.sqlparser.com/">General SQL Parser</a> under a business licenses. This is incompatible with the Apache License.</p>
<p>We switched to an open source alternative, the rust-based <a href="https://github.com/sqlparser-rs/sqlparser-rs">sqlparser-rs</a> library, contributing a <a href="https://github.com/sqlparser-rs/sqlparser-rs/pulls?q=is%3Apr+is%3Amerged+author%3AZiinc">few updates</a> for the BigQuery dialect.</p>
<p>Along with the parser, we invested a lot of effort into transforming the multi-tenant architecture into something that was self-hosting friendly and easily configurable. We moved towards environment variable based configuration instead of compile-time configurations, exposing the Endpoints configurations necessary for Supabase Logs.</p>
<h2 id="whats-next" class="group scroll-mt-24">What’s Next?<a href="#whats-next" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>To further integrate Logflare into the Supabase platform, we are building out 2 main areas: Management API, Multiple Backends.</p>
<h3 id="management-api" class="group scroll-mt-24">Management API<a href="#management-api" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>The Management API allows users to interact programmatically with Logflare to manage their account and resources. This feature will be available for both Logflare customers and self-hosted users.</p>
<p>You can check out the preview of our OpenAPI spec here: <a href="https://logflare.app/swaggerui">https://logflare.app/swaggerui</a></p>
<p></p>
<p>Not only that, we intend to expose user account provisioning to select partners. Soon, you’ll be able to become a Logflare Partner to provision Logflare accounts through the Partner API. Perfect if you want to resell a log analytics service from your own platform.</p>
<p>Contact us at <a href="mailto:<EMAIL>"><EMAIL></a> to get in early on that waitlist.</p>
<h2 id="multiple-backends" class="group scroll-mt-24">Multiple Backends<a href="#multiple-backends" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Logflare currently supports a BigQuery backend. We plan to add support for other analytics-optimized databases, like Clickhouse. We will also support pushing data to other web services, making Logflare a good fit for any data pipeline.</p>
<p>This will benefit the Supabase CLI: once Postgres support is available, Logflare will be able to integrate seamlessly, without the BigQuery requirement.</p>
<h2 id="wrapping-up" class="group scroll-mt-24">Wrapping Up<a href="#wrapping-up" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Logflare has given Supabase the flexibility to quickly deploy features powered by an underlying structured event stream. Materializing metrics from an event stream is a powerful framework for delivering real-time views on analytics streams.</p>
<p>Logflare is the hub of analytics streams for Supabase. We look forward to giving Supabase customers the same superpower.</p></div></article><div class="flex flex-col gap-3 lg:gap-4 border-t border-muted py-4 lg:py-8 mt-4 lg:mt-8"><h3 class="text-foreground text-xl mb-4">More Launch Week 7</h3><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="designing-with-ai-midjourney.html"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Designing with AI</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fai-images%2F00-ai-images-thumb.png&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="https://github.com/supabase/supavisor"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Supavisor</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday0%2Fsupavisor%2Fsupavisor-thumb.png&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="supabase-logs-self-hosted.html"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Open Source Logging</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday1%2Fself-hosted-logs-thumb.jpg&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="edge-runtime-self-hosted-deno-functions.html"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Self-hosted Deno Edge Functions</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday2%2Fself-hosted-edge-functions-thumb.png&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="storage-v3-resumable-uploads.html"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg max-w-[240px]">Storage v3: Resumable Uploads with support for 50GB files</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday3%2Fstorage-v3-thumb.png&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="supabase-auth-sso-pkce.html"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg max-w-[240px]">Supabase Auth: SSO, Mobile, and Server-side support</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday4%2Fsso-support-thumb.jpg&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="launch-week-7-community-highlights.html"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Community Highlight</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fcommunity%2Fcommunity-thumb.jpg&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="supabase-studio-2.0.html"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Studio Updates</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fstudio%2Fstudio-thumb.jpg&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="dbdev.html"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">dbdev</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2Fdbdev-thumb.jpg&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div><div class="h-auto w-full flex flex-col lg:flex-row relative overflow-hidden"><a class="
flex flex-col flex-1 gap-3 items-start justify-center border rounded-xl h-full relative overflow-hidden
p-6 lg:p-10 text-2xl bg-[#1C1C1C]
before:absolute before:w-full before:h-full before:top-52 before:right-0 before:bottom-0 before:left-0
before:border-[#1f3536] before:-z-10
" href="pg-tle.html"><div class="relative z-10 flex items-center text-lg flex-col-reverse lg:flex-row lg:justify-start gap-2 text-foreground"><div class="text-transparent bg-clip-text bg-gradient-to-r text-base from-[#F4FFFA] to-[#B7B2C9] drop-shadow-lg ">Postgres TLE</div></div><div class="absolute inset-0 w-full h-full z-0" style="ease:0.5px;duration:0.2px;transform:none"><img alt="" loading="lazy" decoding="async" data-nimg="fill" class="
  absolute
  w-full h-full -z-10 transition-all duration-300
" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;object-fit:cover;object-position:100% 50%;color:transparent" sizes="100vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Flaunchweek%2Fseven%2Fday5%2Fone-more-thing%2FpgTLE-thumb.jpg&amp;w=3840&amp;q=75"/></div><div class="absolute opacity-10 w-full h-full -z-10 transition-all duration-300" style="background:radial-gradient(650px 150px at 50% 100%, #4635A730, transparent)"></div></a></div></div><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-logs-self-hosted&amp;text=Supabase%20Logs%3A%20open%20source%20logging%20server"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-logs-self-hosted&amp;text=Supabase%20Logs%3A%20open%20source%20logging%20server"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-logs-self-hosted&amp;t=Supabase%20Logs%3A%20open%20source%20logging%20server"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="edge-runtime-self-hosted-deno-functions.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Supabase Edge Runtime: Self-hosted Deno Functions</h4><p class="small">11 April 2023</p></div></div></div></div></a></div><div><a href="supabase-beta-update-march-2023.html"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Supabase Beta March 2023</h4><p class="small">8 April 2023</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/launch-week"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">launch-week</div></a><a href="https://supabase.com/blog/tags/o11y"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">o11y</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#logflare-introduction">Logflare Introduction</a></li>
<li><a href="#self-hosting-logflare">Self-hosting Logflare</a></li>
<li><a href="#the-ingestion-pipeline">The Ingestion Pipeline</a></li>
<li><a href="#the-querying-pipeline">The Querying Pipeline</a>
<ul>
<li><a href="#logflare-endpoint-example">Logflare Endpoint Example</a></li>
</ul>
</li>
<li><a href="#the-self-hosted-challenge">The Self-hosted Challenge</a></li>
<li><a href="#whats-next">What’s Next?</a>
<ul>
<li><a href="#management-api">Management API</a></li>
</ul>
</li>
<li><a href="#multiple-backends">Multiple Backends</a></li>
<li><a href="#wrapping-up">Wrapping Up</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-logs-self-hosted&amp;text=Supabase%20Logs%3A%20open%20source%20logging%20server"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-logs-self-hosted&amp;text=Supabase%20Logs%3A%20open%20source%20logging%20server"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-logs-self-hosted&amp;t=Supabase%20Logs%3A%20open%20source%20logging%20server"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"edge-runtime-self-hosted-deno-functions","title":"Supabase Edge Runtime: Self-hosted Deno Functions","description":"We are open-sourcing Supabase Edge Runtime allowing you to host your Edge Functions anywhere.","launchweek":"7","categories":["product"],"tags":["launch-week","functions"],"date":"2023-04-11","toc_depth":3,"author":"laktek,inian","image":"launch-week-7/day-2-edge-runtime/edge-runtime-og.jpg","thumb":"launch-week-7/day-2-edge-runtime/edge-runtime-thumb.jpeg","formattedDate":"11 April 2023","readingTime":"10 minute read","url":"/blog/edge-runtime-self-hosted-deno-functions","path":"/blog/edge-runtime-self-hosted-deno-functions"},"nextPost":{"slug":"supabase-beta-update-march-2023","title":"Supabase Beta March 2023","description":"We are in full shipping mode 🛥️… Launch Week 7 can’t come quickly enough!","author":"ant_wilson","image":"2023-04-08-supabase-beta-update-march/monthly-update-march-2023.jpg","thumb":"2023-04-08-supabase-beta-update-march/monthly-update-march-2023.jpg","categories":["product"],"tags":["release-notes"],"date":"2023-04-08","toc_depth":3,"formattedDate":"8 April 2023","readingTime":"4 minute read","url":"/blog/supabase-beta-update-march-2023","path":"/blog/supabase-beta-update-march-2023"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-logs-self-hosted","source":"\nToday, we’re releasing Supabase Logs for both self-hosted users and CLI development.\n\n## Logflare Introduction\n\nSince [Logflare joined Supabase](https://supabase.com/blog/supabase-acquires-logflare) over a year ago it’s been quietly handling over 1 billion log events every day. These events come from various tools in the Supabase infrastructure - the API gateway, Postgres databases, Storage, Edge Functions, Auth, and Realtime.\n\nLogflare is a multi-node, highly available Elixir cluster, ingesting the log events and storing them into BigQuery for Supabase and Logflare’s customers. On average, the cluster has 6 nodes handling every spike our customers throw at it.\n\nTo expose log data to customers, we leverage Logflare Endpoints. This provides an HTTP integration into Supabase Studio, powering the log query UIs and most time-series charts. These charts live across the studio, such as the project home page and the new API reports.\n\n\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/Ai2BjHV36Ng\"\n    title=\"YouTube video player\"\n    frameborder=\"0\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"\n    allowfullscreen\n  \u003e\u003c/iframe\u003e\n\u003c/div\u003e\n\n## Self-hosting Logflare\n\nLogflare was available under a BSL license prior to joining Supabase. We’ve since changed the license to [Apache 2.0](https://github.com/Logflare/logflare/blob/staging/LICENSE.md), aligning it with our open source philosophy.\n\nIn the past few months we’ve made Logflare more developer-friendly for local development and self-hosting. While you’re building a project, you can view and query your logs from any Supabase service, just as you would in our cloud platform.\n\n![Logs UI](/images/blog/lw7-o11y/logs-screenshot.png)\n\n\u003cdiv className=\"bg-gray-300 rounded-lg px-6 py-2 italic\"\u003e\n\n📢 Check out the [new self-hosting docs](https://supabase.com/docs/reference/self-hosting-analytics/introduction) to get Logflare up and running as your analytics server.\n\n\u003c/div\u003e\n\nIt currently supports a BigQuery backend, and we are actively working on supporting more.\n\n## The Ingestion Pipeline\n\nLogflare receives Supabase log events via multiple methods. Services like Postgres use [Vector](https://vector.dev/) to clean and forward log events to the Logflare ingest API. Other services such as Realtime and Storage utilize [native Logflare integrations](https://github.com/Logflare/logflare#integrations) to send the log events directly. These then get processed and streamed into BigQuery.\n\n## The Querying Pipeline\n\nThe hard part comes after ingesting the logs: searching, aggregating ,and analyzing them at scale. Crunching many terabytes of data on each query is expensive, and exposing the ingested data to Supabase customers in a naive manner would cause our costs to skyrocket.\n\nTo solve these issues, we built and refined Logflare Endpoints, the query engine that powers many of Supabase’s features, such as the logs views, Logs Explorer, and usage charts.\n\nWith Endpoints, you can create HTTP API endpoints from a SQL query, including parameterized queries. Endpoints are like PostgREST views but with some benefits:\n\n- **Query parameters**\n  - You can provide string parameters to the SQL query via the HTTP endpoint.\n- **Read-through caching**\n  - Results from the query are cached in memory for fast response times.\n  - A read-through cache provides results if cached results do not exist.\n- **Active cache warming**\n  - Query results are proactively warmed at a configurable interval for a combination of fast response times and as-realtime-as-needed data.\n- **Query sandboxing**\n  - If an Endpoint query contains a CTE and the sandbox option is selected, the Endpoint will inject the query string of the `sql` query parameter into the Endpoint SQL replacing the default query (the part of the SQL query after the CTE).\n  - Endpoints parse SQL to allow `select` queries only. No DML or DDL statements are permitted to run through Logflare Endpoints.\n\nWith this feature set, Supabase has been able to build any view we’ve needed on top of billions of daily log events.\n\n### Logflare Endpoint Example\n\nUsing webhooks, we can send all GitHub events in the Supabase organization to Logflare. The webhook sends structured events, and Logflare transforms the payload into metadata:\n\n```json\n{\n  \"event_message\": \"supabase/supabase | JohannesBauer97 | created\",\n  \"id\": \"0d48b71d-91c5-4356-82c7-fdb299b625d0\",\n  \"metadata\": {\n    \"sender\": {\n      \"id\": 15695124,\n      \"login\": \"JohannesBauer97\",\n      \"node_id\": \"MDQ6VXNlcjE1Njk1MTI0\",\n      \"site_admin\": false,\n      \"type\": \"User\",\n      \"url\": \"https://api.github.com/users/JohannesBauer97\"\n    },\n    \"starred_at\": \"2023-03-30T20:33:55Z\"\n    //...\n  },\n  \"timestamp\": 1680208436849642\n}\n```\n\nWe’re interested in the top contributors, which can be extracted with SQL (in BigQuery dialect):\n\n```sql\nselect\n  count(t.timestamp) as count,\n  s.login as gh_user\nfrom\n  `github.supabase.webhooks` as t\n  cross join unnest(metadata) as m\n  cross join unnest(m.sender) as s\nwhere\n  timestamp::date \u003e current_date() - @day::int\ngroup by\n  gh_user\norder by\n  count desc\nlimit\n  25\n```\n\nWith this view in place, we can use Endpoints to provide an API that we can hit from our application:\n\n```bash\ncurl \"https://logflare.app/endpoints/query/69425db0-1cfb-48b4-84c7-2a872b6f0a61\" \\\n -H 'Content-Type: application/json; charset=utf-8' \\\n -G -d \"day=30\"\n```\n\nThis returns a JSON response with the top org wide contributors for the last 30 days!\n\n```json\n{\n  \"result\": [\n    { \"count\": 23404, \"gh_user\": \"vercel[bot]\" },\n    { \"count\": 10005, \"gh_user\": \"joshenlim\" },\n    { \"count\": 7026, \"gh_user\": \"MildTomato\" },\n    { \"count\": 6405, \"gh_user\": \"fsansalvadore\" },\n    { \"count\": 5195, \"gh_user\": \"saltcod\" },\n    { \"count\": 3454, \"gh_user\": \"alaister\" },\n    { \"count\": 2691, \"gh_user\": \"kevcodez\" },\n    { \"count\": 2117, \"gh_user\": \"gregnr\" },\n    { \"count\": 1769, \"gh_user\": \"Ziinc\" },\n    { \"count\": 1749, \"gh_user\": \"chasers\" },\n    { \"count\": 1430, \"gh_user\": \"Isaiah-Hamilton\" }\n    //...\n  ]\n}\n```\n\nWe can configure this Endpoint to cache results for an interval of 10 minutes after the first API request, and proactively update those cached results every 2 minutes - 5 queries across the 10 minute interval. Even if we hit the Endpoint thousands of times, we only sustain the cost of 5 queries.\n\nThe initial request is fast because Logflare also performs setup (such as partitioning) on our BigQuery tables appropriately. Subsequent requests are _extremely fast_ as they are cached in-memory.\n\nThe best part is that all these knobs can be tweaked for your use case. If we have a real-time requirement, we can completely disable caching or reduce the proactive caching to update on a per-second interval.\n\n## The Self-hosted Challenge\n\nTo change the license, we needed to remove all closed-source dependencies. Previously, Logflare relied on the closed source [General SQL Parser](https://www.sqlparser.com/) under a business licenses. This is incompatible with the Apache License.\n\nWe switched to an open source alternative, the rust-based [sqlparser-rs](https://github.com/sqlparser-rs/sqlparser-rs) library, contributing a [few updates](https://github.com/sqlparser-rs/sqlparser-rs/pulls?q=is%3Apr+is%3Amerged+author%3AZiinc) for the BigQuery dialect.\n\nAlong with the parser, we invested a lot of effort into transforming the multi-tenant architecture into something that was self-hosting friendly and easily configurable. We moved towards environment variable based configuration instead of compile-time configurations, exposing the Endpoints configurations necessary for Supabase Logs.\n\n## What’s Next?\n\nTo further integrate Logflare into the Supabase platform, we are building out 2 main areas: Management API, Multiple Backends.\n\n### Management API\n\nThe Management API allows users to interact programmatically with Logflare to manage their account and resources. This feature will be available for both Logflare customers and self-hosted users.\n\nYou can check out the preview of our OpenAPI spec here: [https://logflare.app/swaggerui](https://logflare.app/swaggerui)\n\n![Swagger UI](/images/blog/lw7-o11y/swagger.png)\n\nNot only that, we intend to expose user account provisioning to select partners. Soon, you’ll be able to become a Logflare Partner to provision Logflare accounts through the Partner API. Perfect if you want to resell a log analytics service from your own platform.\n\nContact <NAME_EMAIL> to get in early on that waitlist.\n\n## Multiple Backends\n\nLogflare currently supports a BigQuery backend. We plan to add support for other analytics-optimized databases, like Clickhouse. We will also support pushing data to other web services, making Logflare a good fit for any data pipeline.\n\nThis will benefit the Supabase CLI: once Postgres support is available, Logflare will be able to integrate seamlessly, without the BigQuery requirement.\n\n## Wrapping Up\n\nLogflare has given Supabase the flexibility to quickly deploy features powered by an underlying structured event stream. Materializing metrics from an event stream is a powerful framework for delivering real-time views on analytics streams.\n\nLogflare is the hub of analytics streams for Supabase. We look forward to giving Supabase customers the same superpower.\n","title":"Supabase Logs: open source logging server","description":"We're releasing Supabase Logs for both self-hosted users and CLI development.","launchweek":"7","categories":["product"],"tags":["launch-week","o11y"],"date":"2023-04-10","toc_depth":3,"author":"chasers,ziinc","image":"launch-week-7/day-1-supabase-logs-self-hosted/day-1-supabase-logs-self-hosted-og.jpg","thumb":"launch-week-7/day-1-supabase-logs-self-hosted/day-1-supabase-logs-self-hosted-thumb.jpg","content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    h2: \"h2\",\n    a: \"a\",\n    img: \"img\",\n    ul: \"ul\",\n    li: \"li\",\n    strong: \"strong\",\n    code: \"code\",\n    h3: \"h3\",\n    em: \"em\"\n  }, _provideComponents(), props.components), {CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Today, we’re releasing Supabase Logs for both self-hosted users and CLI development.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"logflare-introduction\",\n      children: \"Logflare Introduction\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Since \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/supabase-acquires-logflare\",\n        children: \"Logflare joined Supabase\"\n      }), \" over a year ago it’s been quietly handling over 1 billion log events every day. These events come from various tools in the Supabase infrastructure - the API gateway, Postgres databases, Storage, Edge Functions, Auth, and Realtime.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Logflare is a multi-node, highly available Elixir cluster, ingesting the log events and storing them into BigQuery for Supabase and Logflare’s customers. On average, the cluster has 6 nodes handling every spike our customers throw at it.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To expose log data to customers, we leverage Logflare Endpoints. This provides an HTTP integration into Supabase Studio, powering the log query UIs and most time-series charts. These charts live across the studio, such as the project home page and the new API reports.\"\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"video-container\",\n      children: _jsx(\"iframe\", {\n        className: \"w-full\",\n        src: \"https://www.youtube-nocookie.com/embed/Ai2BjHV36Ng\",\n        title: \"YouTube video player\",\n        frameborder: \"0\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\",\n        allowfullscreen: true\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"self-hosting-logflare\",\n      children: \"Self-hosting Logflare\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Logflare was available under a BSL license prior to joining Supabase. We’ve since changed the license to \", _jsx(_components.a, {\n        href: \"https://github.com/Logflare/logflare/blob/staging/LICENSE.md\",\n        children: \"Apache 2.0\"\n      }), \", aligning it with our open source philosophy.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In the past few months we’ve made Logflare more developer-friendly for local development and self-hosting. While you’re building a project, you can view and query your logs from any Supabase service, just as you would in our cloud platform.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/lw7-o11y/logs-screenshot.png\",\n        alt: \"Logs UI\"\n      })\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"bg-gray-300 rounded-lg px-6 py-2 italic\",\n      children: _jsxs(_components.p, {\n        children: [\"📢 Check out the \", _jsx(_components.a, {\n          href: \"https://supabase.com/docs/reference/self-hosting-analytics/introduction\",\n          children: \"new self-hosting docs\"\n        }), \" to get Logflare up and running as your analytics server.\"]\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"It currently supports a BigQuery backend, and we are actively working on supporting more.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"the-ingestion-pipeline\",\n      children: \"The Ingestion Pipeline\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Logflare receives Supabase log events via multiple methods. Services like Postgres use \", _jsx(_components.a, {\n        href: \"https://vector.dev/\",\n        children: \"Vector\"\n      }), \" to clean and forward log events to the Logflare ingest API. Other services such as Realtime and Storage utilize \", _jsx(_components.a, {\n        href: \"https://github.com/Logflare/logflare#integrations\",\n        children: \"native Logflare integrations\"\n      }), \" to send the log events directly. These then get processed and streamed into BigQuery.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"the-querying-pipeline\",\n      children: \"The Querying Pipeline\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The hard part comes after ingesting the logs: searching, aggregating ,and analyzing them at scale. Crunching many terabytes of data on each query is expensive, and exposing the ingested data to Supabase customers in a naive manner would cause our costs to skyrocket.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To solve these issues, we built and refined Logflare Endpoints, the query engine that powers many of Supabase’s features, such as the logs views, Logs Explorer, and usage charts.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"With Endpoints, you can create HTTP API endpoints from a SQL query, including parameterized queries. Endpoints are like PostgREST views but with some benefits:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Query parameters\"\n        }), \"\\n\", _jsxs(_components.ul, {\n          children: [\"\\n\", _jsx(_components.li, {\n            children: \"You can provide string parameters to the SQL query via the HTTP endpoint.\"\n          }), \"\\n\"]\n        }), \"\\n\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Read-through caching\"\n        }), \"\\n\", _jsxs(_components.ul, {\n          children: [\"\\n\", _jsx(_components.li, {\n            children: \"Results from the query are cached in memory for fast response times.\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"A read-through cache provides results if cached results do not exist.\"\n          }), \"\\n\"]\n        }), \"\\n\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Active cache warming\"\n        }), \"\\n\", _jsxs(_components.ul, {\n          children: [\"\\n\", _jsx(_components.li, {\n            children: \"Query results are proactively warmed at a configurable interval for a combination of fast response times and as-realtime-as-needed data.\"\n          }), \"\\n\"]\n        }), \"\\n\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Query sandboxing\"\n        }), \"\\n\", _jsxs(_components.ul, {\n          children: [\"\\n\", _jsxs(_components.li, {\n            children: [\"If an Endpoint query contains a CTE and the sandbox option is selected, the Endpoint will inject the query string of the \", _jsx(_components.code, {\n              children: \"sql\"\n            }), \" query parameter into the Endpoint SQL replacing the default query (the part of the SQL query after the CTE).\"]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"Endpoints parse SQL to allow \", _jsx(_components.code, {\n              children: \"select\"\n            }), \" queries only. No DML or DDL statements are permitted to run through Logflare Endpoints.\"]\n          }), \"\\n\"]\n        }), \"\\n\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"With this feature set, Supabase has been able to build any view we’ve needed on top of billions of daily log events.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"logflare-endpoint-example\",\n      children: \"Logflare Endpoint Example\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Using webhooks, we can send all GitHub events in the Supabase organization to Logflare. The webhook sends structured events, and Logflare transforms the payload into metadata:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"event_message\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"supabase/supabase | JohannesBauer97 | created\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"id\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"0d48b71d-91c5-4356-82c7-fdb299b625d0\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"metadata\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \\\"sender\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"id\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"15695124\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"login\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"JohannesBauer97\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"node_id\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"MDQ6VXNlcjE1Njk1MTI0\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"site_admin\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"false\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"type\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"User\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"url\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"https://api.github.com/users/JohannesBauer97\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \\\"starred_at\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"2023-03-30T20:33:55Z\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    //...\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"timestamp\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"1680208436849642\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"json\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We’re interested in the top contributors, which can be extracted with SQL (in BigQuery dialect):\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"select\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  count\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"t\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"timestamp\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"as\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" count,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  s\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"login \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"as\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" gh_user\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"from\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  `github.supabase.webhooks` \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"as\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" t\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  cross join\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" unnest(metadata) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"as\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" m\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  cross join\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" unnest(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"m\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"sender\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"as\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" s\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"where\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  timestamp\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"::\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"date \u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" current_date() \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"-\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" @day::\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"int\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"group by\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  gh_user\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"order by\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  count \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"desc\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"limit\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  25\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"With this view in place, we can use Endpoints to provide an API that we can hit from our application:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"curl \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"https://logflare.app/endpoints/query/69425db0-1cfb-48b4-84c7-2a872b6f0a61\\\" \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\\\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \" -H \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"'Content-Type: application/json; charset=utf-8' \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\\\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \" -G -d \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"day=30\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This returns a JSON response with the top org wide contributors for the last 30 days!\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"result\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": [\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"count\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"23404\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"gh_user\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"vercel[bot]\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"count\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"10005\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"gh_user\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"joshenlim\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"count\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"7026\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"gh_user\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"MildTomato\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"count\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"6405\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"gh_user\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"fsansalvadore\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"count\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"5195\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"gh_user\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"saltcod\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"count\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"3454\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"gh_user\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"alaister\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"count\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"2691\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"gh_user\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"kevcodez\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"count\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"2117\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"gh_user\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"gregnr\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"count\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"1769\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"gh_user\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"Ziinc\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"count\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"1749\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"gh_user\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"chasers\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"count\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"1430\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"gh_user\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"Isaiah-Hamilton\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    //...\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  ]\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"json\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We can configure this Endpoint to cache results for an interval of 10 minutes after the first API request, and proactively update those cached results every 2 minutes - 5 queries across the 10 minute interval. Even if we hit the Endpoint thousands of times, we only sustain the cost of 5 queries.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The initial request is fast because Logflare also performs setup (such as partitioning) on our BigQuery tables appropriately. Subsequent requests are \", _jsx(_components.em, {\n        children: \"extremely fast\"\n      }), \" as they are cached in-memory.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The best part is that all these knobs can be tweaked for your use case. If we have a real-time requirement, we can completely disable caching or reduce the proactive caching to update on a per-second interval.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"the-self-hosted-challenge\",\n      children: \"The Self-hosted Challenge\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To change the license, we needed to remove all closed-source dependencies. Previously, Logflare relied on the closed source \", _jsx(_components.a, {\n        href: \"https://www.sqlparser.com/\",\n        children: \"General SQL Parser\"\n      }), \" under a business licenses. This is incompatible with the Apache License.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We switched to an open source alternative, the rust-based \", _jsx(_components.a, {\n        href: \"https://github.com/sqlparser-rs/sqlparser-rs\",\n        children: \"sqlparser-rs\"\n      }), \" library, contributing a \", _jsx(_components.a, {\n        href: \"https://github.com/sqlparser-rs/sqlparser-rs/pulls?q=is%3Apr+is%3Amerged+author%3AZiinc\",\n        children: \"few updates\"\n      }), \" for the BigQuery dialect.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Along with the parser, we invested a lot of effort into transforming the multi-tenant architecture into something that was self-hosting friendly and easily configurable. We moved towards environment variable based configuration instead of compile-time configurations, exposing the Endpoints configurations necessary for Supabase Logs.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"whats-next\",\n      children: \"What’s Next?\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To further integrate Logflare into the Supabase platform, we are building out 2 main areas: Management API, Multiple Backends.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"management-api\",\n      children: \"Management API\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The Management API allows users to interact programmatically with Logflare to manage their account and resources. This feature will be available for both Logflare customers and self-hosted users.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You can check out the preview of our OpenAPI spec here: \", _jsx(_components.a, {\n        href: \"https://logflare.app/swaggerui\",\n        children: \"https://logflare.app/swaggerui\"\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/lw7-o11y/swagger.png\",\n        alt: \"Swagger UI\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Not only that, we intend to expose user account provisioning to select partners. Soon, you’ll be able to become a Logflare Partner to provision Logflare accounts through the Partner API. Perfect if you want to resell a log analytics service from your own platform.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Contact us at \", _jsx(_components.a, {\n        href: \"mailto:<EMAIL>\",\n        children: \"<EMAIL>\"\n      }), \" to get in early on that waitlist.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"multiple-backends\",\n      children: \"Multiple Backends\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Logflare currently supports a BigQuery backend. We plan to add support for other analytics-optimized databases, like Clickhouse. We will also support pushing data to other web services, making Logflare a good fit for any data pipeline.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This will benefit the Supabase CLI: once Postgres support is available, Logflare will be able to integrate seamlessly, without the BigQuery requirement.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"wrapping-up\",\n      children: \"Wrapping Up\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Logflare has given Supabase the flexibility to quickly deploy features powered by an underlying structured event stream. Materializing metrics from an event stream is a powerful framework for delivering real-time views on analytics streams.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Logflare is the hub of analytics streams for Supabase. We look forward to giving Supabase customers the same superpower.\"\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"Logflare Introduction","slug":"logflare-introduction","lvl":2,"i":0,"seen":0},{"content":"Self-hosting Logflare","slug":"self-hosting-logflare","lvl":2,"i":1,"seen":0},{"content":"The Ingestion Pipeline","slug":"the-ingestion-pipeline","lvl":2,"i":2,"seen":0},{"content":"The Querying Pipeline","slug":"the-querying-pipeline","lvl":2,"i":3,"seen":0},{"content":"Logflare Endpoint Example","slug":"logflare-endpoint-example","lvl":3,"i":4,"seen":0},{"content":"The Self-hosted Challenge","slug":"the-self-hosted-challenge","lvl":2,"i":5,"seen":0},{"content":"What’s Next?","slug":"whats-next","lvl":2,"i":6,"seen":0},{"content":"Management API","slug":"management-api","lvl":3,"i":7,"seen":0},{"content":"Multiple Backends","slug":"multiple-backends","lvl":2,"i":8,"seen":0},{"content":"Wrapping Up","slug":"wrapping-up","lvl":2,"i":9,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,2],"level":0},{"type":"inline","content":"Today, we’re releasing Supabase Logs for both self-hosted users and CLI development.","level":1,"lines":[1,2],"children":[{"type":"text","content":"Today, we’re releasing Supabase Logs for both self-hosted users and CLI development.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[3,4],"level":0},{"type":"inline","content":"[Logflare Introduction](#logflare-introduction)","level":1,"lines":[3,4],"children":[{"type":"text","content":"Logflare Introduction","level":0}],"lvl":2,"i":0,"seen":0,"slug":"logflare-introduction"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[5,6],"level":0},{"type":"inline","content":"Since [Logflare joined Supabase](https://supabase.com/blog/supabase-acquires-logflare) over a year ago it’s been quietly handling over 1 billion log events every day. These events come from various tools in the Supabase infrastructure - the API gateway, Postgres databases, Storage, Edge Functions, Auth, and Realtime.","level":1,"lines":[5,6],"children":[{"type":"text","content":"Since ","level":0},{"type":"link_open","href":"https://supabase.com/blog/supabase-acquires-logflare","title":"","level":0},{"type":"text","content":"Logflare joined Supabase","level":1},{"type":"link_close","level":0},{"type":"text","content":" over a year ago it’s been quietly handling over 1 billion log events every day. These events come from various tools in the Supabase infrastructure - the API gateway, Postgres databases, Storage, Edge Functions, Auth, and Realtime.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[7,8],"level":0},{"type":"inline","content":"Logflare is a multi-node, highly available Elixir cluster, ingesting the log events and storing them into BigQuery for Supabase and Logflare’s customers. On average, the cluster has 6 nodes handling every spike our customers throw at it.","level":1,"lines":[7,8],"children":[{"type":"text","content":"Logflare is a multi-node, highly available Elixir cluster, ingesting the log events and storing them into BigQuery for Supabase and Logflare’s customers. On average, the cluster has 6 nodes handling every spike our customers throw at it.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[9,10],"level":0},{"type":"inline","content":"To expose log data to customers, we leverage Logflare Endpoints. This provides an HTTP integration into Supabase Studio, powering the log query UIs and most time-series charts. These charts live across the studio, such as the project home page and the new API reports.","level":1,"lines":[9,10],"children":[{"type":"text","content":"To expose log data to customers, we leverage Logflare Endpoints. This provides an HTTP integration into Supabase Studio, powering the log query UIs and most time-series charts. These charts live across the studio, such as the project home page and the new API reports.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[11,19],"level":0},{"type":"inline","content":"\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/Ai2BjHV36Ng\"\n    title=\"YouTube video player\"\n    frameborder=\"0\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"\n    allowfullscreen","level":1,"lines":[11,19],"children":[{"type":"text","content":"\u003cdiv className=\"video-container\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ciframe","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"w-full\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://www.youtube-nocookie.com/embed/Ai2BjHV36Ng\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"title=\"YouTube video player\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"frameborder=\"0\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allowfullscreen","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"blockquote_open","lines":[19,21],"level":0},{"type":"paragraph_open","tight":false,"lines":[19,21],"level":1},{"type":"inline","content":"\u003c/iframe\u003e\n\u003c/div\u003e","level":2,"lines":[19,21],"children":[{"type":"text","content":"\u003c/iframe\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"heading_open","hLevel":2,"lines":[22,23],"level":0},{"type":"inline","content":"[Self-hosting Logflare](#self-hosting-logflare)","level":1,"lines":[22,23],"children":[{"type":"text","content":"Self-hosting Logflare","level":0}],"lvl":2,"i":1,"seen":0,"slug":"self-hosting-logflare"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[24,25],"level":0},{"type":"inline","content":"Logflare was available under a BSL license prior to joining Supabase. We’ve since changed the license to [Apache 2.0](https://github.com/Logflare/logflare/blob/staging/LICENSE.md), aligning it with our open source philosophy.","level":1,"lines":[24,25],"children":[{"type":"text","content":"Logflare was available under a BSL license prior to joining Supabase. We’ve since changed the license to ","level":0},{"type":"link_open","href":"https://github.com/Logflare/logflare/blob/staging/LICENSE.md","title":"","level":0},{"type":"text","content":"Apache 2.0","level":1},{"type":"link_close","level":0},{"type":"text","content":", aligning it with our open source philosophy.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[26,27],"level":0},{"type":"inline","content":"In the past few months we’ve made Logflare more developer-friendly for local development and self-hosting. While you’re building a project, you can view and query your logs from any Supabase service, just as you would in our cloud platform.","level":1,"lines":[26,27],"children":[{"type":"text","content":"In the past few months we’ve made Logflare more developer-friendly for local development and self-hosting. While you’re building a project, you can view and query your logs from any Supabase service, just as you would in our cloud platform.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[28,29],"level":0},{"type":"inline","content":"![Logs UI](/images/blog/lw7-o11y/logs-screenshot.png)","level":1,"lines":[28,29],"children":[{"type":"image","src":"/images/blog/lw7-o11y/logs-screenshot.png","title":"","alt":"Logs UI","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[30,31],"level":0},{"type":"inline","content":"\u003cdiv className=\"bg-gray-300 rounded-lg px-6 py-2 italic\"\u003e","level":1,"lines":[30,31],"children":[{"type":"text","content":"\u003cdiv className=\"bg-gray-300 rounded-lg px-6 py-2 italic\"\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[32,33],"level":0},{"type":"inline","content":"📢 Check out the [new self-hosting docs](https://supabase.com/docs/reference/self-hosting-analytics/introduction) to get Logflare up and running as your analytics server.","level":1,"lines":[32,33],"children":[{"type":"text","content":"📢 Check out the ","level":0},{"type":"link_open","href":"https://supabase.com/docs/reference/self-hosting-analytics/introduction","title":"","level":0},{"type":"text","content":"new self-hosting docs","level":1},{"type":"link_close","level":0},{"type":"text","content":" to get Logflare up and running as your analytics server.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[34,35],"level":0},{"type":"inline","content":"\u003c/div\u003e","level":1,"lines":[34,35],"children":[{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[36,37],"level":0},{"type":"inline","content":"It currently supports a BigQuery backend, and we are actively working on supporting more.","level":1,"lines":[36,37],"children":[{"type":"text","content":"It currently supports a BigQuery backend, and we are actively working on supporting more.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[38,39],"level":0},{"type":"inline","content":"[The Ingestion Pipeline](#the-ingestion-pipeline)","level":1,"lines":[38,39],"children":[{"type":"text","content":"The Ingestion Pipeline","level":0}],"lvl":2,"i":2,"seen":0,"slug":"the-ingestion-pipeline"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[40,41],"level":0},{"type":"inline","content":"Logflare receives Supabase log events via multiple methods. Services like Postgres use [Vector](https://vector.dev/) to clean and forward log events to the Logflare ingest API. Other services such as Realtime and Storage utilize [native Logflare integrations](https://github.com/Logflare/logflare#integrations) to send the log events directly. These then get processed and streamed into BigQuery.","level":1,"lines":[40,41],"children":[{"type":"text","content":"Logflare receives Supabase log events via multiple methods. Services like Postgres use ","level":0},{"type":"link_open","href":"https://vector.dev/","title":"","level":0},{"type":"text","content":"Vector","level":1},{"type":"link_close","level":0},{"type":"text","content":" to clean and forward log events to the Logflare ingest API. Other services such as Realtime and Storage utilize ","level":0},{"type":"link_open","href":"https://github.com/Logflare/logflare#integrations","title":"","level":0},{"type":"text","content":"native Logflare integrations","level":1},{"type":"link_close","level":0},{"type":"text","content":" to send the log events directly. These then get processed and streamed into BigQuery.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[42,43],"level":0},{"type":"inline","content":"[The Querying Pipeline](#the-querying-pipeline)","level":1,"lines":[42,43],"children":[{"type":"text","content":"The Querying Pipeline","level":0}],"lvl":2,"i":3,"seen":0,"slug":"the-querying-pipeline"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[44,45],"level":0},{"type":"inline","content":"The hard part comes after ingesting the logs: searching, aggregating ,and analyzing them at scale. Crunching many terabytes of data on each query is expensive, and exposing the ingested data to Supabase customers in a naive manner would cause our costs to skyrocket.","level":1,"lines":[44,45],"children":[{"type":"text","content":"The hard part comes after ingesting the logs: searching, aggregating ,and analyzing them at scale. Crunching many terabytes of data on each query is expensive, and exposing the ingested data to Supabase customers in a naive manner would cause our costs to skyrocket.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[46,47],"level":0},{"type":"inline","content":"To solve these issues, we built and refined Logflare Endpoints, the query engine that powers many of Supabase’s features, such as the logs views, Logs Explorer, and usage charts.","level":1,"lines":[46,47],"children":[{"type":"text","content":"To solve these issues, we built and refined Logflare Endpoints, the query engine that powers many of Supabase’s features, such as the logs views, Logs Explorer, and usage charts.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[48,49],"level":0},{"type":"inline","content":"With Endpoints, you can create HTTP API endpoints from a SQL query, including parameterized queries. Endpoints are like PostgREST views but with some benefits:","level":1,"lines":[48,49],"children":[{"type":"text","content":"With Endpoints, you can create HTTP API endpoints from a SQL query, including parameterized queries. Endpoints are like PostgREST views but with some benefits:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[50,61],"level":0},{"type":"list_item_open","lines":[50,52],"level":1},{"type":"paragraph_open","tight":true,"lines":[50,51],"level":2},{"type":"inline","content":"**Query parameters**","level":3,"lines":[50,51],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Query parameters","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"bullet_list_open","lines":[51,52],"level":2},{"type":"list_item_open","lines":[51,52],"level":3},{"type":"paragraph_open","tight":true,"lines":[51,52],"level":4},{"type":"inline","content":"You can provide string parameters to the SQL query via the HTTP endpoint.","level":5,"lines":[51,52],"children":[{"type":"text","content":"You can provide string parameters to the SQL query via the HTTP endpoint.","level":0}]},{"type":"paragraph_close","tight":true,"level":4},{"type":"list_item_close","level":3},{"type":"bullet_list_close","level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[52,55],"level":1},{"type":"paragraph_open","tight":true,"lines":[52,53],"level":2},{"type":"inline","content":"**Read-through caching**","level":3,"lines":[52,53],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Read-through caching","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"bullet_list_open","lines":[53,55],"level":2},{"type":"list_item_open","lines":[53,54],"level":3},{"type":"paragraph_open","tight":true,"lines":[53,54],"level":4},{"type":"inline","content":"Results from the query are cached in memory for fast response times.","level":5,"lines":[53,54],"children":[{"type":"text","content":"Results from the query are cached in memory for fast response times.","level":0}]},{"type":"paragraph_close","tight":true,"level":4},{"type":"list_item_close","level":3},{"type":"list_item_open","lines":[54,55],"level":3},{"type":"paragraph_open","tight":true,"lines":[54,55],"level":4},{"type":"inline","content":"A read-through cache provides results if cached results do not exist.","level":5,"lines":[54,55],"children":[{"type":"text","content":"A read-through cache provides results if cached results do not exist.","level":0}]},{"type":"paragraph_close","tight":true,"level":4},{"type":"list_item_close","level":3},{"type":"bullet_list_close","level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[55,57],"level":1},{"type":"paragraph_open","tight":true,"lines":[55,56],"level":2},{"type":"inline","content":"**Active cache warming**","level":3,"lines":[55,56],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Active cache warming","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"bullet_list_open","lines":[56,57],"level":2},{"type":"list_item_open","lines":[56,57],"level":3},{"type":"paragraph_open","tight":true,"lines":[56,57],"level":4},{"type":"inline","content":"Query results are proactively warmed at a configurable interval for a combination of fast response times and as-realtime-as-needed data.","level":5,"lines":[56,57],"children":[{"type":"text","content":"Query results are proactively warmed at a configurable interval for a combination of fast response times and as-realtime-as-needed data.","level":0}]},{"type":"paragraph_close","tight":true,"level":4},{"type":"list_item_close","level":3},{"type":"bullet_list_close","level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[57,61],"level":1},{"type":"paragraph_open","tight":true,"lines":[57,58],"level":2},{"type":"inline","content":"**Query sandboxing**","level":3,"lines":[57,58],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Query sandboxing","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"bullet_list_open","lines":[58,61],"level":2},{"type":"list_item_open","lines":[58,59],"level":3},{"type":"paragraph_open","tight":true,"lines":[58,59],"level":4},{"type":"inline","content":"If an Endpoint query contains a CTE and the sandbox option is selected, the Endpoint will inject the query string of the `sql` query parameter into the Endpoint SQL replacing the default query (the part of the SQL query after the CTE).","level":5,"lines":[58,59],"children":[{"type":"text","content":"If an Endpoint query contains a CTE and the sandbox option is selected, the Endpoint will inject the query string of the ","level":0},{"type":"code","content":"sql","block":false,"level":0},{"type":"text","content":" query parameter into the Endpoint SQL replacing the default query (the part of the SQL query after the CTE).","level":0}]},{"type":"paragraph_close","tight":true,"level":4},{"type":"list_item_close","level":3},{"type":"list_item_open","lines":[59,61],"level":3},{"type":"paragraph_open","tight":true,"lines":[59,60],"level":4},{"type":"inline","content":"Endpoints parse SQL to allow `select` queries only. No DML or DDL statements are permitted to run through Logflare Endpoints.","level":5,"lines":[59,60],"children":[{"type":"text","content":"Endpoints parse SQL to allow ","level":0},{"type":"code","content":"select","block":false,"level":0},{"type":"text","content":" queries only. No DML or DDL statements are permitted to run through Logflare Endpoints.","level":0}]},{"type":"paragraph_close","tight":true,"level":4},{"type":"list_item_close","level":3},{"type":"bullet_list_close","level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[61,62],"level":0},{"type":"inline","content":"With this feature set, Supabase has been able to build any view we’ve needed on top of billions of daily log events.","level":1,"lines":[61,62],"children":[{"type":"text","content":"With this feature set, Supabase has been able to build any view we’ve needed on top of billions of daily log events.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[63,64],"level":0},{"type":"inline","content":"[Logflare Endpoint Example](#logflare-endpoint-example)","level":1,"lines":[63,64],"children":[{"type":"text","content":"Logflare Endpoint Example","level":0}],"lvl":3,"i":4,"seen":0,"slug":"logflare-endpoint-example"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[65,66],"level":0},{"type":"inline","content":"Using webhooks, we can send all GitHub events in the Supabase organization to Logflare. The webhook sends structured events, and Logflare transforms the payload into metadata:","level":1,"lines":[65,66],"children":[{"type":"text","content":"Using webhooks, we can send all GitHub events in the Supabase organization to Logflare. The webhook sends structured events, and Logflare transforms the payload into metadata:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"json","content":"{\n  \"event_message\": \"supabase/supabase | JohannesBauer97 | created\",\n  \"id\": \"0d48b71d-91c5-4356-82c7-fdb299b625d0\",\n  \"metadata\": {\n    \"sender\": {\n      \"id\": 15695124,\n      \"login\": \"JohannesBauer97\",\n      \"node_id\": \"MDQ6VXNlcjE1Njk1MTI0\",\n      \"site_admin\": false,\n      \"type\": \"User\",\n      \"url\": \"https://api.github.com/users/JohannesBauer97\"\n    },\n    \"starred_at\": \"2023-03-30T20:33:55Z\"\n    //...\n  },\n  \"timestamp\": 1680208436849642\n}\n","lines":[67,86],"level":0},{"type":"paragraph_open","tight":false,"lines":[87,88],"level":0},{"type":"inline","content":"We’re interested in the top contributors, which can be extracted with SQL (in BigQuery dialect):","level":1,"lines":[87,88],"children":[{"type":"text","content":"We’re interested in the top contributors, which can be extracted with SQL (in BigQuery dialect):","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql","content":"select\n  count(t.timestamp) as count,\n  s.login as gh_user\nfrom\n  `github.supabase.webhooks` as t\n  cross join unnest(metadata) as m\n  cross join unnest(m.sender) as s\nwhere\n  timestamp::date \u003e current_date() - @day::int\ngroup by\n  gh_user\norder by\n  count desc\nlimit\n  25\n","lines":[89,106],"level":0},{"type":"paragraph_open","tight":false,"lines":[107,108],"level":0},{"type":"inline","content":"With this view in place, we can use Endpoints to provide an API that we can hit from our application:","level":1,"lines":[107,108],"children":[{"type":"text","content":"With this view in place, we can use Endpoints to provide an API that we can hit from our application:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"bash","content":"curl \"https://logflare.app/endpoints/query/69425db0-1cfb-48b4-84c7-2a872b6f0a61\" \\\n -H 'Content-Type: application/json; charset=utf-8' \\\n -G -d \"day=30\"\n","lines":[109,114],"level":0},{"type":"paragraph_open","tight":false,"lines":[115,116],"level":0},{"type":"inline","content":"This returns a JSON response with the top org wide contributors for the last 30 days!","level":1,"lines":[115,116],"children":[{"type":"text","content":"This returns a JSON response with the top org wide contributors for the last 30 days!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"json","content":"{\n  \"result\": [\n    { \"count\": 23404, \"gh_user\": \"vercel[bot]\" },\n    { \"count\": 10005, \"gh_user\": \"joshenlim\" },\n    { \"count\": 7026, \"gh_user\": \"MildTomato\" },\n    { \"count\": 6405, \"gh_user\": \"fsansalvadore\" },\n    { \"count\": 5195, \"gh_user\": \"saltcod\" },\n    { \"count\": 3454, \"gh_user\": \"alaister\" },\n    { \"count\": 2691, \"gh_user\": \"kevcodez\" },\n    { \"count\": 2117, \"gh_user\": \"gregnr\" },\n    { \"count\": 1769, \"gh_user\": \"Ziinc\" },\n    { \"count\": 1749, \"gh_user\": \"chasers\" },\n    { \"count\": 1430, \"gh_user\": \"Isaiah-Hamilton\" }\n    //...\n  ]\n}\n","lines":[117,135],"level":0},{"type":"paragraph_open","tight":false,"lines":[136,137],"level":0},{"type":"inline","content":"We can configure this Endpoint to cache results for an interval of 10 minutes after the first API request, and proactively update those cached results every 2 minutes - 5 queries across the 10 minute interval. Even if we hit the Endpoint thousands of times, we only sustain the cost of 5 queries.","level":1,"lines":[136,137],"children":[{"type":"text","content":"We can configure this Endpoint to cache results for an interval of 10 minutes after the first API request, and proactively update those cached results every 2 minutes - 5 queries across the 10 minute interval. Even if we hit the Endpoint thousands of times, we only sustain the cost of 5 queries.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[138,139],"level":0},{"type":"inline","content":"The initial request is fast because Logflare also performs setup (such as partitioning) on our BigQuery tables appropriately. Subsequent requests are _extremely fast_ as they are cached in-memory.","level":1,"lines":[138,139],"children":[{"type":"text","content":"The initial request is fast because Logflare also performs setup (such as partitioning) on our BigQuery tables appropriately. Subsequent requests are ","level":0},{"type":"em_open","level":0},{"type":"text","content":"extremely fast","level":1},{"type":"em_close","level":0},{"type":"text","content":" as they are cached in-memory.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[140,141],"level":0},{"type":"inline","content":"The best part is that all these knobs can be tweaked for your use case. If we have a real-time requirement, we can completely disable caching or reduce the proactive caching to update on a per-second interval.","level":1,"lines":[140,141],"children":[{"type":"text","content":"The best part is that all these knobs can be tweaked for your use case. If we have a real-time requirement, we can completely disable caching or reduce the proactive caching to update on a per-second interval.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[142,143],"level":0},{"type":"inline","content":"[The Self-hosted Challenge](#the-self-hosted-challenge)","level":1,"lines":[142,143],"children":[{"type":"text","content":"The Self-hosted Challenge","level":0}],"lvl":2,"i":5,"seen":0,"slug":"the-self-hosted-challenge"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[144,145],"level":0},{"type":"inline","content":"To change the license, we needed to remove all closed-source dependencies. Previously, Logflare relied on the closed source [General SQL Parser](https://www.sqlparser.com/) under a business licenses. This is incompatible with the Apache License.","level":1,"lines":[144,145],"children":[{"type":"text","content":"To change the license, we needed to remove all closed-source dependencies. Previously, Logflare relied on the closed source ","level":0},{"type":"link_open","href":"https://www.sqlparser.com/","title":"","level":0},{"type":"text","content":"General SQL Parser","level":1},{"type":"link_close","level":0},{"type":"text","content":" under a business licenses. This is incompatible with the Apache License.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[146,147],"level":0},{"type":"inline","content":"We switched to an open source alternative, the rust-based [sqlparser-rs](https://github.com/sqlparser-rs/sqlparser-rs) library, contributing a [few updates](https://github.com/sqlparser-rs/sqlparser-rs/pulls?q=is%3Apr+is%3Amerged+author%3AZiinc) for the BigQuery dialect.","level":1,"lines":[146,147],"children":[{"type":"text","content":"We switched to an open source alternative, the rust-based ","level":0},{"type":"link_open","href":"https://github.com/sqlparser-rs/sqlparser-rs","title":"","level":0},{"type":"text","content":"sqlparser-rs","level":1},{"type":"link_close","level":0},{"type":"text","content":" library, contributing a ","level":0},{"type":"link_open","href":"https://github.com/sqlparser-rs/sqlparser-rs/pulls?q=is%3Apr+is%3Amerged+author%3AZiinc","title":"","level":0},{"type":"text","content":"few updates","level":1},{"type":"link_close","level":0},{"type":"text","content":" for the BigQuery dialect.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[148,149],"level":0},{"type":"inline","content":"Along with the parser, we invested a lot of effort into transforming the multi-tenant architecture into something that was self-hosting friendly and easily configurable. We moved towards environment variable based configuration instead of compile-time configurations, exposing the Endpoints configurations necessary for Supabase Logs.","level":1,"lines":[148,149],"children":[{"type":"text","content":"Along with the parser, we invested a lot of effort into transforming the multi-tenant architecture into something that was self-hosting friendly and easily configurable. We moved towards environment variable based configuration instead of compile-time configurations, exposing the Endpoints configurations necessary for Supabase Logs.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[150,151],"level":0},{"type":"inline","content":"[What’s Next?](#whats-next)","level":1,"lines":[150,151],"children":[{"type":"text","content":"What’s Next?","level":0}],"lvl":2,"i":6,"seen":0,"slug":"whats-next"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[152,153],"level":0},{"type":"inline","content":"To further integrate Logflare into the Supabase platform, we are building out 2 main areas: Management API, Multiple Backends.","level":1,"lines":[152,153],"children":[{"type":"text","content":"To further integrate Logflare into the Supabase platform, we are building out 2 main areas: Management API, Multiple Backends.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[154,155],"level":0},{"type":"inline","content":"[Management API](#management-api)","level":1,"lines":[154,155],"children":[{"type":"text","content":"Management API","level":0}],"lvl":3,"i":7,"seen":0,"slug":"management-api"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[156,157],"level":0},{"type":"inline","content":"The Management API allows users to interact programmatically with Logflare to manage their account and resources. This feature will be available for both Logflare customers and self-hosted users.","level":1,"lines":[156,157],"children":[{"type":"text","content":"The Management API allows users to interact programmatically with Logflare to manage their account and resources. This feature will be available for both Logflare customers and self-hosted users.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[158,159],"level":0},{"type":"inline","content":"You can check out the preview of our OpenAPI spec here: [https://logflare.app/swaggerui](https://logflare.app/swaggerui)","level":1,"lines":[158,159],"children":[{"type":"text","content":"You can check out the preview of our OpenAPI spec here: ","level":0},{"type":"link_open","href":"https://logflare.app/swaggerui","title":"","level":0},{"type":"text","content":"https://logflare.app/swaggerui","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[160,161],"level":0},{"type":"inline","content":"![Swagger UI](/images/blog/lw7-o11y/swagger.png)","level":1,"lines":[160,161],"children":[{"type":"image","src":"/images/blog/lw7-o11y/swagger.png","title":"","alt":"Swagger UI","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[162,163],"level":0},{"type":"inline","content":"Not only that, we intend to expose user account provisioning to select partners. Soon, you’ll be able to become a Logflare Partner to provision Logflare accounts through the Partner API. Perfect if you want to resell a log analytics service from your own platform.","level":1,"lines":[162,163],"children":[{"type":"text","content":"Not only that, we intend to expose user account provisioning to select partners. Soon, you’ll be able to become a Logflare Partner to provision Logflare accounts through the Partner API. Perfect if you want to resell a log analytics service from your own platform.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[164,165],"level":0},{"type":"inline","content":"Contact <NAME_EMAIL> to get in early on that waitlist.","level":1,"lines":[164,165],"children":[{"type":"text","content":"Contact <NAME_EMAIL> to get in early on that waitlist.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[166,167],"level":0},{"type":"inline","content":"[Multiple Backends](#multiple-backends)","level":1,"lines":[166,167],"children":[{"type":"text","content":"Multiple Backends","level":0}],"lvl":2,"i":8,"seen":0,"slug":"multiple-backends"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[168,169],"level":0},{"type":"inline","content":"Logflare currently supports a BigQuery backend. We plan to add support for other analytics-optimized databases, like Clickhouse. We will also support pushing data to other web services, making Logflare a good fit for any data pipeline.","level":1,"lines":[168,169],"children":[{"type":"text","content":"Logflare currently supports a BigQuery backend. We plan to add support for other analytics-optimized databases, like Clickhouse. We will also support pushing data to other web services, making Logflare a good fit for any data pipeline.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[170,171],"level":0},{"type":"inline","content":"This will benefit the Supabase CLI: once Postgres support is available, Logflare will be able to integrate seamlessly, without the BigQuery requirement.","level":1,"lines":[170,171],"children":[{"type":"text","content":"This will benefit the Supabase CLI: once Postgres support is available, Logflare will be able to integrate seamlessly, without the BigQuery requirement.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[172,173],"level":0},{"type":"inline","content":"[Wrapping Up](#wrapping-up)","level":1,"lines":[172,173],"children":[{"type":"text","content":"Wrapping Up","level":0}],"lvl":2,"i":9,"seen":0,"slug":"wrapping-up"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[174,175],"level":0},{"type":"inline","content":"Logflare has given Supabase the flexibility to quickly deploy features powered by an underlying structured event stream. Materializing metrics from an event stream is a powerful framework for delivering real-time views on analytics streams.","level":1,"lines":[174,175],"children":[{"type":"text","content":"Logflare has given Supabase the flexibility to quickly deploy features powered by an underlying structured event stream. Materializing metrics from an event stream is a powerful framework for delivering real-time views on analytics streams.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[176,177],"level":0},{"type":"inline","content":"Logflare is the hub of analytics streams for Supabase. We look forward to giving Supabase customers the same superpower.","level":1,"lines":[176,177],"children":[{"type":"text","content":"Logflare is the hub of analytics streams for Supabase. We look forward to giving Supabase customers the same superpower.","level":0}]},{"type":"paragraph_close","tight":false,"level":0}],"content":"- [Logflare Introduction](#logflare-introduction)\n- [Self-hosting Logflare](#self-hosting-logflare)\n- [The Ingestion Pipeline](#the-ingestion-pipeline)\n- [The Querying Pipeline](#the-querying-pipeline)\n  * [Logflare Endpoint Example](#logflare-endpoint-example)\n- [The Self-hosted Challenge](#the-self-hosted-challenge)\n- [What’s Next?](#whats-next)\n  * [Management API](#management-api)\n- [Multiple Backends](#multiple-backends)\n- [Wrapping Up](#wrapping-up)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-logs-self-hosted"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>