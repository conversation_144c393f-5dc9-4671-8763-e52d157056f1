<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase Hacktoberfest Hackathon 2021</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="We&#x27;re running another Supabase Hackathon during Hacktoberfest!" data-next-head=""/><meta property="og:title" content="Supabase Hacktoberfest Hackathon 2021" data-next-head=""/><meta property="og:description" content="We&#x27;re running another Supabase Hackathon during Hacktoberfest!" data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-hacktoberfest-hackathon-2021" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2021-09-28" data-next-head=""/><meta property="article:author" content="https://twitter.com/thorwebdev" data-next-head=""/><meta property="article:tag" content="hacktoberfest" data-next-head=""/><meta property="article:tag" content="hackathon" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/hacktoberfest-hackathon/hacktoberfest_banner.png" data-next-head=""/><meta property="og:image:alt" content="Supabase Hacktoberfest Hackathon 2021 thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Supabase Hacktoberfest Hackathon 2021</h1><div class="text-light flex space-x-3 text-sm"><p>28 Sep 2021</p><p>•</p><p>5 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://twitter.com/thorwebdev"><div class="flex items-center gap-3"><div class="w-10"><img alt="Thor Schaeff avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fthorwebdev.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fthorwebdev.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fthorwebdev.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Thor Schaeff</span><span class="text-foreground-lighter mb-0 text-xs">DevRel &amp; DX</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Supabase Hacktoberfest Hackathon 2021" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fhacktoberfest-hackathon%2Fhacktoberfest_banner.png&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fhacktoberfest-hackathon%2Fhacktoberfest_banner.png&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fhacktoberfest-hackathon%2Fhacktoberfest_banner.png&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fhacktoberfest-hackathon%2Fhacktoberfest_banner.png&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fhacktoberfest-hackathon%2Fhacktoberfest_banner.png&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fhacktoberfest-hackathon%2Fhacktoberfest_banner.png&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fhacktoberfest-hackathon%2Fhacktoberfest_banner.png&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fhacktoberfest-hackathon%2Fhacktoberfest_banner.png&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fhacktoberfest-hackathon%2Fhacktoberfest_banner.png&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fhacktoberfest-hackathon%2Fhacktoberfest_banner.png&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fhacktoberfest-hackathon%2Fhacktoberfest_banner.png&amp;w=3840&amp;q=100"/></div><h1 id="supabase-hacktoberfest-hackathon-2021" class="group scroll-mt-24">Supabase Hacktoberfest Hackathon 2021<a href="#supabase-hacktoberfest-hackathon-2021" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h1>
<p>We were absolutely blown away by <a href="https://supabase.com/blog/hackathon-winners">all the amazing projects</a> you built during our <a href="https://supabase.com/blog/1-the-supabase-hackathon">first Supabase Hackathon</a>, that we were looking for an excuse to run our next virtual open-source hackathon, and we found it in the upcoming <a href="https://hacktoberfest.digitalocean.com/">Hacktoberfest</a>.</p>
<h2 id="heres-the-plan" class="group scroll-mt-24">Here&#x27;s the plan<a href="#heres-the-plan" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ul>
<li>On <strong>Friday Oct 1st at 08:00am PT</strong> we&#x27;re kicking things off with our Hacktoberfest Discord Hangout. Join us in the #hackathon channel on our Discord server: <a href="https://discord.gg/bnncdqgBSS">https://discord.gg/bnncdqgBSS</a></li>
<li>Then you have 10 days to build a new <strong>open-source</strong> project with Supabase or contribute to one of our <a href="https://github.com/supabase-community?q=topic%3AHacktoberfest&amp;type=&amp;language=&amp;sort=">supabase-community projects</a> that have the <code class="short-inline-codeblock">hacktoberfest</code> topic.</li>
<li>It can be whatever you want - a project, mobile app, tool, library, anything</li>
<li>Enter as an individual, or as a team of up to 5 people</li>
<li>Submission deadline is <strong>Sunday Oct 10th at 11:59pm PT</strong></li>
<li>Besides <a href="https://hacktoberfest.digitalocean.com/resources/participation">earning your Hacktoberfest shirt</a>, you can win some extremely limited edition Supabase swag ✨ (see <a href="https://twitter.com/supabase/status/1440737587895799809?s=21">here</a> what folks won last time!)</li>
</ul>
<h2 id="details" class="group scroll-mt-24">Details<a href="#details" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<h3 id="timeline" class="group scroll-mt-24">Timeline<a href="#timeline" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<ul>
<li><strong>Friday Oct 1st at 08:00am PT:</strong> Opening Ceremony in the #hackathon channel <a href="https://discord.gg/bnncdqgBSS">on Discord</a>.</li>
<li>Build your project during the next 10 days and hang out with the community <a href="https://discord.gg/bnncdqgBSS">on Discord</a>.</li>
<li><strong>Sunday Oct 10th at 11:59pm PT:</strong> Submission deadline</li>
<li>Judges Deliberate (Monday)</li>
<li>We&#x27;ll be contacting and announcing the winners <a href="https://twitter.com/supabase">on Twitter</a> throughout the week after.</li>
</ul>
<blockquote class="twitter-tweet" data-dnt="true" data-theme="dark"><p lang="en" dir="ltr"><p>Who else deserves the Gold Supabase Tee? 👀
<a href="https://t.co/XPWw02kZad"><a href="https://t.co/XPWw02kZad">https://t.co/XPWw02kZad</a></a></p></p><p>— Supabase (@supabase) <a href="https://twitter.com/supabase/status/1440737587895799809?ref_src=twsrc%5Etfw">September 22, 2021</a></p></blockquote>
<script async="" src="https://platform.twitter.com/widgets.js" charSet="utf-8"></script>
<h3 id="prize-categories" class="group scroll-mt-24">Prize categories<a href="#prize-categories" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>There are 5 chances to win, there will be prizes for:</p>
<ul>
<li>Best Overall Project</li>
<li>Most Visually Pleasing</li>
<li>Most Technically Impressive</li>
<li>Best mobile project (can user Flutter, React Native, Ionic, etc.)</li>
<li>Most Spooky/Fun (Halloween is coming up!)</li>
</ul>
<p>There will be a winner and a runner-up prize for each category.</p>
<h3 id="submission" class="group scroll-mt-24">Submission<a href="#submission" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Submit your project via <a href="https://www.madewithsupabase.com/hacktoberfest">madewithsupabase.com/hacktoberfest</a></p>
<p>You will be asked to send a link to a GitHub (or similar) repo, in the README you should include:</p>
<ul>
<li>link to hosted demo (if applicable)</li>
<li>list of team members github handles (and twitter if they have one)</li>
<li>any demo videos, instructions, or memes</li>
<li>a brief description of how you used Supabase:<!-- -->
<ul>
<li>to store data?</li>
<li>realtime?</li>
<li>auth?</li>
<li>storage?</li>
</ul>
</li>
<li>any other info you want the judges to know (motivations/ideas/process)</li>
<li><em>optional</em> team photo</li>
</ul>
<h3 id="judging--announcement-of-winners" class="group scroll-mt-24">Judging &amp; announcement of winners<a href="#judging--announcement-of-winners" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>The Supabase team will excitedly review what you&#x27;ve built. They will be looking for a few things, including:</p>
<ul>
<li>creativity/inventiveness</li>
<li>functions correctly/smoothly</li>
<li>visually pleasing</li>
<li>technically impressive</li>
<li>use of Supabase features<!-- -->
<ul>
<li>deep usage of a single feature or</li>
<li>broad usage are both ok</li>
</ul>
</li>
<li>FUN! 😃</li>
</ul>
<p>We&#x27;ll be contacting and announcing winners <a href="https://twitter.com/supabase">on Twitter</a> throughout the week after submission closes.</p>
<blockquote class="twitter-tweet" data-dnt="true" data-theme="dark"><p lang="en" dir="ltr"><p>Absolutely buzzing to have won the
<a href="https://twitter.com/supabase?ref_src=twsrc%5Etfw">@supabase</a> hackathon! 🥳 <a href="https://t.co/rm5HBuju73"><a href="https://t.co/rm5HBuju73">https://t.co/rm5HBuju73</a></a></p></p><p>— Josh Cawthorne (@cawthornejosh) <a href="https://twitter.com/cawthornejosh/status/1424985377136390183?ref_src=twsrc%5Etfw">August 10, 2021</a></p></blockquote>
<script async="" src="https://platform.twitter.com/widgets.js" charset="utf-8"></script>
<h3 id="rules" class="group scroll-mt-24">Rules<a href="#rules" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<ul>
<li>Team size 1-5 (all team members on winning teams will receive a prize)</li>
<li>You cannot be in multiple teams</li>
<li>One submission per team</li>
<li>All design elements, code, etc. for your project/feature must be created <strong>during</strong> the event</li>
<li>All entries must be Open Source (link to source code required in entry)</li>
<li>Must use Supabase in some capacity</li>
<li>Can be any language or framework</li>
<li>You must submit before the deadline (no late entries)</li>
<li>You can continue to make updates to your project after the submission deadline, but there is no guarantee that the judges will see additions made after the submission time.</li>
</ul>
<h3 id="community--help" class="group scroll-mt-24">Community &amp; help<a href="#community--help" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Hang out with the Supabase team and community on Discord:</p>
<ul>
<li>Text channel: hackathon</li>
<li>Audio channel: hackathon</li>
</ul>
<p>If you need help or advice when building, find other people to join your team, or if you just want to chill and watch people build, come and join us!</p>
<p>Join our Discord: <a href="https://discord.gg/bnncdqgBSS">discord.gg/bnncdqgBSS</a></p>
<p></p>
<h3 id="resources--guides" class="group scroll-mt-24">Resources &amp; Guides<a href="#resources--guides" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Here&#x27;s a collection of resources that will help you get started building with Supabase:</p>
<ul>
<li>Need some inspiration? <a href="https://supabase.com/blog/hackathon-winners">See what folks built last time</a>!</li>
<li><a href="../docs/guides/getting-started.html">Examples and Resources</a></li>
<li><a href="https://www.youtube.com/watch?v=7uKQBl9uZ00">Supabase Crash Course</a> [video]</li>
<li><a href="https://supabase.com/docs/guides/with-flutter">Flutter Quickstart Guide</a></li>
<li><a href="https://supabase.com/docs/guides/with-nextjs">Nextjs Quickstart Guide</a></li>
<li><a href="https://supabase.com/blog/using-supabase-replit">Using Supabase inside Replit</a></li>
<li><a href="https://www.freecodecamp.org/news/the-complete-guide-to-full-stack-development-with-supabas/">Full Stack Development with Next.js and Supabase – The Complete Guide</a></li>
<li><a href="https://supabase.com/docs/learn/auth-deep-dive/auth-deep-dive-jwts">Auth Deep Dive - Learn everything there is to know about Supabase Auth</a> [videos]</li>
<li><a href="https://www.twilio.com/blog/send-sms-notifications-supabase-users-node-js-twilio-messaging">Send SMS notifications using Twilio</a></li>
<li><a href="https://www.youtube.com/watch?v=pl9XfIWutKE">How to Integrate Supabase in Your Ionic App</a> [video]</li>
<li><a href="https://www.youtube.com/watch?v=LUMxJ4w-MUU">Building a Slack clone with authentication and realtime data syncing using Supabase</a> [video]</li>
<li><a href="https://aboutmonica.com/blog/creating-protected-routes-in-next-js-with-supabase">Creating Protected Routes In Next.js With Supabase</a></li>
</ul>
<h3 id="additional-info" class="group scroll-mt-24">Additional Info<a href="#additional-info" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<ul>
<li>Any intellectual property developed during the hackathon will belong to the team that developed it. We expect that each team will have an agreement between themselves regarding the IP, but this is not required</li>
<li>By making a submission you grant Supabase permission to use screenshots, code-snippets and/or links to your project or content of your README on our twitter, blog, website, email updates, and in the Supabase discord server. Supabase does not make any claims over your IP.</li>
</ul></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-hacktoberfest-hackathon-2021&amp;text=Supabase%20Hacktoberfest%20Hackathon%202021"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-hacktoberfest-hackathon-2021&amp;text=Supabase%20Hacktoberfest%20Hackathon%202021"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-hacktoberfest-hackathon-2021&amp;t=Supabase%20Hacktoberfest%20Hackathon%202021"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="supabase-beta-sept-2021.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Supabase Beta Sept 2021</h4><p class="small">4 October 2021</p></div></div></div></div></a></div><div><a href="supabase-beta-august-2021.html"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Supabase Beta August 2021</h4><p class="small">10 September 2021</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/hacktoberfest"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">hacktoberfest</div></a><a href="https://supabase.com/blog/tags/hackathon"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">hackathon</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#supabase-hacktoberfest-hackathon-2021">Supabase Hacktoberfest Hackathon 2021</a>
<ul>
<li><a href="#heres-the-plan">Here&#x27;s the plan</a></li>
<li><a href="#details">Details</a>
<ul>
<li><a href="#timeline">Timeline</a></li>
<li><a href="#prize-categories">Prize categories</a></li>
<li><a href="#submission">Submission</a></li>
<li><a href="#judging--announcement-of-winners">Judging &amp; announcement of winners</a></li>
<li><a href="#rules">Rules</a></li>
<li><a href="#community--help">Community &amp; help</a></li>
<li><a href="#resources--guides">Resources &amp; Guides</a></li>
<li><a href="#additional-info">Additional Info</a></li>
</ul>
</li>
</ul>
</li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-hacktoberfest-hackathon-2021&amp;text=Supabase%20Hacktoberfest%20Hackathon%202021"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-hacktoberfest-hackathon-2021&amp;text=Supabase%20Hacktoberfest%20Hackathon%202021"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-hacktoberfest-hackathon-2021&amp;t=Supabase%20Hacktoberfest%20Hackathon%202021"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"supabase-beta-sept-2021","title":"Supabase Beta Sept 2021","description":"Hackathon, Aborting request, UI updates, and now Hiring.","author":"paul_copplestone","author_url":"https://github.com/kiwicopple","author_image_url":"https://github.com/kiwicopple.png","image":"2021-sept/release-sept-2021.jpg","thumb":"2021-sept/release-sept-2021-cover.jpg","categories":["product"],"tags":["release-notes"],"date":"2021-10-04","toc_depth":3,"video":"https://www.youtube.com/v/5fsKMTeBKKY","formattedDate":"4 October 2021","readingTime":"3 minute read","url":"/blog/supabase-beta-sept-2021","path":"/blog/supabase-beta-sept-2021"},"nextPost":{"slug":"supabase-beta-august-2021","title":"Supabase Beta August 2021","description":"Fundraising, Realtime Security, custom SMS templates, and deployments in South Korea.","author":"paul_copplestone","author_url":"https://github.com/kiwicopple","author_image_url":"https://github.com/kiwicopple.png","image":"2021-august/release-august-2021.jpg","thumb":"2021-august/release-august-2021-cover.jpg","categories":["product"],"tags":["release-notes"],"date":"2021-09-10","toc_depth":3,"video":"https://www.youtube.com/v/YYpTh2DAvho","formattedDate":"10 September 2021","readingTime":"5 minute read","url":"/blog/supabase-beta-august-2021","path":"/blog/supabase-beta-august-2021"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-hacktoberfest-hackathon-2021","source":"\n# Supabase Hacktoberfest Hackathon 2021\n\nWe were absolutely blown away by [all the amazing projects](/blog/hackathon-winners) you built during our [first Supabase Hackathon](/blog/1-the-supabase-hackathon), that we were looking for an excuse to run our next virtual open-source hackathon, and we found it in the upcoming [Hacktoberfest](https://hacktoberfest.digitalocean.com/).\n\n## Here's the plan\n\n- On **Friday Oct 1st at 08:00am PT** we're kicking things off with our Hacktoberfest Discord Hangout. Join us in the #hackathon channel on our Discord server: [https://discord.gg/bnncdqgBSS](https://discord.gg/bnncdqgBSS)\n- Then you have 10 days to build a new **open-source** project with Supabase or contribute to one of our [supabase-community projects](https://github.com/supabase-community?q=topic%3AHacktoberfest\u0026type=\u0026language=\u0026sort=) that have the `hacktoberfest` topic.\n- It can be whatever you want - a project, mobile app, tool, library, anything\n- Enter as an individual, or as a team of up to 5 people\n- Submission deadline is **Sunday Oct 10th at 11:59pm PT**\n- Besides [earning your Hacktoberfest shirt](https://hacktoberfest.digitalocean.com/resources/participation), you can win some extremely limited edition Supabase swag ✨ (see [here](https://twitter.com/supabase/status/1440737587895799809?s=21) what folks won last time!)\n\n## Details\n\n### Timeline\n\n- **Friday Oct 1st at 08:00am PT:** Opening Ceremony in the #hackathon channel [on Discord](https://discord.gg/bnncdqgBSS).\n- Build your project during the next 10 days and hang out with the community [on Discord](https://discord.gg/bnncdqgBSS).\n- **Sunday Oct 10th at 11:59pm PT:** Submission deadline\n- Judges Deliberate (Monday)\n- We'll be contacting and announcing the winners [on Twitter](https://twitter.com/supabase) throughout the week after.\n\n\u003cblockquote class=\"twitter-tweet\" data-dnt=\"true\" data-theme=\"dark\"\u003e\n  \u003cp lang=\"en\" dir=\"ltr\"\u003e\n    Who else deserves the Gold Supabase Tee? 👀\n    \u003ca href=\"https://t.co/XPWw02kZad\"\u003ehttps://t.co/XPWw02kZad\u003c/a\u003e\n  \u003c/p\u003e\n  \u0026mdash; Supabase (@supabase) \u003ca href=\"https://twitter.com/supabase/status/1440737587895799809?ref_src=twsrc%5Etfw\"\u003eSeptember 22, 2021\u003c/a\u003e\n\u003c/blockquote\u003e \u003cscript async src=\"https://platform.twitter.com/widgets.js\" charSet=\"utf-8\"\u003e\u003c/script\u003e\n\n### Prize categories\n\nThere are 5 chances to win, there will be prizes for:\n\n- Best Overall Project\n- Most Visually Pleasing\n- Most Technically Impressive\n- Best mobile project (can user Flutter, React Native, Ionic, etc.)\n- Most Spooky/Fun (Halloween is coming up!)\n\nThere will be a winner and a runner-up prize for each category.\n\n### Submission\n\nSubmit your project via [madewithsupabase.com/hacktoberfest](https://www.madewithsupabase.com/hacktoberfest)\n\nYou will be asked to send a link to a GitHub (or similar) repo, in the README you should include:\n\n- link to hosted demo (if applicable)\n- list of team members github handles (and twitter if they have one)\n- any demo videos, instructions, or memes\n- a brief description of how you used Supabase:\n  - to store data?\n  - realtime?\n  - auth?\n  - storage?\n- any other info you want the judges to know (motivations/ideas/process)\n- _optional_ team photo\n\n### Judging \u0026 announcement of winners\n\nThe Supabase team will excitedly review what you've built. They will be looking for a few things, including:\n\n- creativity/inventiveness\n- functions correctly/smoothly\n- visually pleasing\n- technically impressive\n- use of Supabase features\n  - deep usage of a single feature or\n  - broad usage are both ok\n- FUN! 😃\n\nWe'll be contacting and announcing winners [on Twitter](https://twitter.com/supabase) throughout the week after submission closes.\n\n\u003cblockquote class=\"twitter-tweet\" data-dnt=\"true\" data-theme=\"dark\"\u003e\n  \u003cp lang=\"en\" dir=\"ltr\"\u003e\n    Absolutely buzzing to have won the\n    \u003ca href=\"https://twitter.com/supabase?ref_src=twsrc%5Etfw\"\u003e@supabase\u003c/a\u003e hackathon! 🥳 \u003ca href=\"https://t.co/rm5HBuju73\"\u003ehttps://t.co/rm5HBuju73\u003c/a\u003e\n  \u003c/p\u003e\n  \u0026mdash; Josh Cawthorne (@cawthornejosh) \u003ca href=\"https://twitter.com/cawthornejosh/status/1424985377136390183?ref_src=twsrc%5Etfw\"\u003eAugust 10, 2021\u003c/a\u003e\n\u003c/blockquote\u003e \u003cscript async src=\"https://platform.twitter.com/widgets.js\" charset=\"utf-8\"\u003e\u003c/script\u003e\n\n### Rules\n\n- Team size 1-5 (all team members on winning teams will receive a prize)\n- You cannot be in multiple teams\n- One submission per team\n- All design elements, code, etc. for your project/feature must be created **during** the event\n- All entries must be Open Source (link to source code required in entry)\n- Must use Supabase in some capacity\n- Can be any language or framework\n- You must submit before the deadline (no late entries)\n- You can continue to make updates to your project after the submission deadline, but there is no guarantee that the judges will see additions made after the submission time.\n\n### Community \u0026 help\n\nHang out with the Supabase team and community on Discord:\n\n- Text channel: hackathon\n- Audio channel: hackathon\n\nIf you need help or advice when building, find other people to join your team, or if you just want to chill and watch people build, come and join us!\n\nJoin our Discord: [discord.gg/bnncdqgBSS](https://discord.gg/bnncdqgBSS)\n\n![Discord Hangout](/images/blog/hackathon/community.png)\n\n### Resources \u0026 Guides\n\nHere's a collection of resources that will help you get started building with Supabase:\n\n- Need some inspiration? [See what folks built last time](/blog/hackathon-winners)!\n- [Examples and Resources](/docs/guides/examples)\n- [Supabase Crash Course](https://www.youtube.com/watch?v=7uKQBl9uZ00) [video]\n- [Flutter Quickstart Guide](/docs/guides/with-flutter)\n- [Nextjs Quickstart Guide](/docs/guides/with-nextjs)\n- [Using Supabase inside Replit](/blog/using-supabase-replit)\n- [Full Stack Development with Next.js and Supabase – The Complete Guide](https://www.freecodecamp.org/news/the-complete-guide-to-full-stack-development-with-supabas/)\n- [Auth Deep Dive - Learn everything there is to know about Supabase Auth](/docs/learn/auth-deep-dive/auth-deep-dive-jwts) [videos]\n- [Send SMS notifications using Twilio](https://www.twilio.com/blog/send-sms-notifications-supabase-users-node-js-twilio-messaging)\n- [How to Integrate Supabase in Your Ionic App](https://www.youtube.com/watch?v=pl9XfIWutKE) [video]\n- [Building a Slack clone with authentication and realtime data syncing using Supabase](https://www.youtube.com/watch?v=LUMxJ4w-MUU) [video]\n- [Creating Protected Routes In Next.js With Supabase](https://aboutmonica.com/blog/creating-protected-routes-in-next-js-with-supabase)\n\n### Additional Info\n\n- Any intellectual property developed during the hackathon will belong to the team that developed it. We expect that each team will have an agreement between themselves regarding the IP, but this is not required\n- By making a submission you grant Supabase permission to use screenshots, code-snippets and/or links to your project or content of your README on our twitter, blog, website, email updates, and in the Supabase discord server. Supabase does not make any claims over your IP.\n","title":"Supabase Hacktoberfest Hackathon 2021","description":"We're running another Supabase Hackathon during Hacktoberfest!","author":"thor_schaeff","author_url":"https://github.com/thorwebdev","author_image_url":"https://github.com/thorwebdev.png","image":"hacktoberfest-hackathon/hacktoberfest_banner.png","thumb":"hacktoberfest-hackathon/hacktoberfest_banner.png","categories":["developers"],"tags":["hacktoberfest","hackathon"],"date":"2021-09-28","toc_depth":3,"content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    h1: \"h1\",\n    p: \"p\",\n    a: \"a\",\n    h2: \"h2\",\n    ul: \"ul\",\n    li: \"li\",\n    strong: \"strong\",\n    code: \"code\",\n    h3: \"h3\",\n    em: \"em\",\n    img: \"img\"\n  }, _provideComponents(), props.components);\n  return _jsxs(_Fragment, {\n    children: [_jsx(_components.h1, {\n      id: \"supabase-hacktoberfest-hackathon-2021\",\n      children: \"Supabase Hacktoberfest Hackathon 2021\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We were absolutely blown away by \", _jsx(_components.a, {\n        href: \"/blog/hackathon-winners\",\n        children: \"all the amazing projects\"\n      }), \" you built during our \", _jsx(_components.a, {\n        href: \"/blog/1-the-supabase-hackathon\",\n        children: \"first Supabase Hackathon\"\n      }), \", that we were looking for an excuse to run our next virtual open-source hackathon, and we found it in the upcoming \", _jsx(_components.a, {\n        href: \"https://hacktoberfest.digitalocean.com/\",\n        children: \"Hacktoberfest\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"heres-the-plan\",\n      children: \"Here's the plan\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"On \", _jsx(_components.strong, {\n          children: \"Friday Oct 1st at 08:00am PT\"\n        }), \" we're kicking things off with our Hacktoberfest Discord Hangout. Join us in the #hackathon channel on our Discord server: \", _jsx(_components.a, {\n          href: \"https://discord.gg/bnncdqgBSS\",\n          children: \"https://discord.gg/bnncdqgBSS\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Then you have 10 days to build a new \", _jsx(_components.strong, {\n          children: \"open-source\"\n        }), \" project with Supabase or contribute to one of our \", _jsx(_components.a, {\n          href: \"https://github.com/supabase-community?q=topic%3AHacktoberfest\u0026type=\u0026language=\u0026sort=\",\n          children: \"supabase-community projects\"\n        }), \" that have the \", _jsx(_components.code, {\n          children: \"hacktoberfest\"\n        }), \" topic.\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"It can be whatever you want - a project, mobile app, tool, library, anything\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Enter as an individual, or as a team of up to 5 people\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Submission deadline is \", _jsx(_components.strong, {\n          children: \"Sunday Oct 10th at 11:59pm PT\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Besides \", _jsx(_components.a, {\n          href: \"https://hacktoberfest.digitalocean.com/resources/participation\",\n          children: \"earning your Hacktoberfest shirt\"\n        }), \", you can win some extremely limited edition Supabase swag ✨ (see \", _jsx(_components.a, {\n          href: \"https://twitter.com/supabase/status/1440737587895799809?s=21\",\n          children: \"here\"\n        }), \" what folks won last time!)\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"details\",\n      children: \"Details\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"timeline\",\n      children: \"Timeline\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Friday Oct 1st at 08:00am PT:\"\n        }), \" Opening Ceremony in the #hackathon channel \", _jsx(_components.a, {\n          href: \"https://discord.gg/bnncdqgBSS\",\n          children: \"on Discord\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Build your project during the next 10 days and hang out with the community \", _jsx(_components.a, {\n          href: \"https://discord.gg/bnncdqgBSS\",\n          children: \"on Discord\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Sunday Oct 10th at 11:59pm PT:\"\n        }), \" Submission deadline\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Judges Deliberate (Monday)\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"We'll be contacting and announcing the winners \", _jsx(_components.a, {\n          href: \"https://twitter.com/supabase\",\n          children: \"on Twitter\"\n        }), \" throughout the week after.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(\"blockquote\", {\n      class: \"twitter-tweet\",\n      \"data-dnt\": \"true\",\n      \"data-theme\": \"dark\",\n      children: [_jsx(\"p\", {\n        lang: \"en\",\n        dir: \"ltr\",\n        children: _jsxs(_components.p, {\n          children: [\"Who else deserves the Gold Supabase Tee? 👀\\n\", _jsx(\"a\", {\n            href: \"https://t.co/XPWw02kZad\",\n            children: _jsx(_components.a, {\n              href: \"https://t.co/XPWw02kZad\",\n              children: \"https://t.co/XPWw02kZad\"\n            })\n          })]\n        })\n      }), _jsxs(_components.p, {\n        children: [\"— Supabase (@supabase) \", _jsx(\"a\", {\n          href: \"https://twitter.com/supabase/status/1440737587895799809?ref_src=twsrc%5Etfw\",\n          children: \"September 22, 2021\"\n        })]\n      })]\n    }), \"\\n\", _jsx(\"script\", {\n      async: true,\n      src: \"https://platform.twitter.com/widgets.js\",\n      charSet: \"utf-8\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"prize-categories\",\n      children: \"Prize categories\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"There are 5 chances to win, there will be prizes for:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Best Overall Project\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Most Visually Pleasing\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Most Technically Impressive\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Best mobile project (can user Flutter, React Native, Ionic, etc.)\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Most Spooky/Fun (Halloween is coming up!)\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"There will be a winner and a runner-up prize for each category.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"submission\",\n      children: \"Submission\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Submit your project via \", _jsx(_components.a, {\n        href: \"https://www.madewithsupabase.com/hacktoberfest\",\n        children: \"madewithsupabase.com/hacktoberfest\"\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You will be asked to send a link to a GitHub (or similar) repo, in the README you should include:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"link to hosted demo (if applicable)\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"list of team members github handles (and twitter if they have one)\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"any demo videos, instructions, or memes\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"a brief description of how you used Supabase:\", \"\\n\", _jsxs(_components.ul, {\n          children: [\"\\n\", _jsx(_components.li, {\n            children: \"to store data?\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"realtime?\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"auth?\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"storage?\"\n          }), \"\\n\"]\n        }), \"\\n\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"any other info you want the judges to know (motivations/ideas/process)\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.em, {\n          children: \"optional\"\n        }), \" team photo\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"judging--announcement-of-winners\",\n      children: \"Judging \u0026 announcement of winners\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The Supabase team will excitedly review what you've built. They will be looking for a few things, including:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"creativity/inventiveness\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"functions correctly/smoothly\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"visually pleasing\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"technically impressive\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"use of Supabase features\", \"\\n\", _jsxs(_components.ul, {\n          children: [\"\\n\", _jsx(_components.li, {\n            children: \"deep usage of a single feature or\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"broad usage are both ok\"\n          }), \"\\n\"]\n        }), \"\\n\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"FUN! 😃\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We'll be contacting and announcing winners \", _jsx(_components.a, {\n        href: \"https://twitter.com/supabase\",\n        children: \"on Twitter\"\n      }), \" throughout the week after submission closes.\"]\n    }), \"\\n\", _jsxs(\"blockquote\", {\n      class: \"twitter-tweet\",\n      \"data-dnt\": \"true\",\n      \"data-theme\": \"dark\",\n      children: [_jsx(\"p\", {\n        lang: \"en\",\n        dir: \"ltr\",\n        children: _jsxs(_components.p, {\n          children: [\"Absolutely buzzing to have won the\\n\", _jsx(\"a\", {\n            href: \"https://twitter.com/supabase?ref_src=twsrc%5Etfw\",\n            children: \"@supabase\"\n          }), \" hackathon! 🥳 \", _jsx(\"a\", {\n            href: \"https://t.co/rm5HBuju73\",\n            children: _jsx(_components.a, {\n              href: \"https://t.co/rm5HBuju73\",\n              children: \"https://t.co/rm5HBuju73\"\n            })\n          })]\n        })\n      }), _jsxs(_components.p, {\n        children: [\"— Josh Cawthorne (@cawthornejosh) \", _jsx(\"a\", {\n          href: \"https://twitter.com/cawthornejosh/status/1424985377136390183?ref_src=twsrc%5Etfw\",\n          children: \"August 10, 2021\"\n        })]\n      })]\n    }), \"\\n\", _jsx(\"script\", {\n      async: true,\n      src: \"https://platform.twitter.com/widgets.js\",\n      charset: \"utf-8\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"rules\",\n      children: \"Rules\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Team size 1-5 (all team members on winning teams will receive a prize)\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"You cannot be in multiple teams\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"One submission per team\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"All design elements, code, etc. for your project/feature must be created \", _jsx(_components.strong, {\n          children: \"during\"\n        }), \" the event\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"All entries must be Open Source (link to source code required in entry)\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Must use Supabase in some capacity\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Can be any language or framework\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"You must submit before the deadline (no late entries)\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"You can continue to make updates to your project after the submission deadline, but there is no guarantee that the judges will see additions made after the submission time.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"community--help\",\n      children: \"Community \u0026 help\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Hang out with the Supabase team and community on Discord:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Text channel: hackathon\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Audio channel: hackathon\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"If you need help or advice when building, find other people to join your team, or if you just want to chill and watch people build, come and join us!\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Join our Discord: \", _jsx(_components.a, {\n        href: \"https://discord.gg/bnncdqgBSS\",\n        children: \"discord.gg/bnncdqgBSS\"\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/hackathon/community.png\",\n        alt: \"Discord Hangout\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"resources--guides\",\n      children: \"Resources \u0026 Guides\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Here's a collection of resources that will help you get started building with Supabase:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Need some inspiration? \", _jsx(_components.a, {\n          href: \"/blog/hackathon-winners\",\n          children: \"See what folks built last time\"\n        }), \"!\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"/docs/guides/examples\",\n          children: \"Examples and Resources\"\n        })\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=7uKQBl9uZ00\",\n          children: \"Supabase Crash Course\"\n        }), \" [video]\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"/docs/guides/with-flutter\",\n          children: \"Flutter Quickstart Guide\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"/docs/guides/with-nextjs\",\n          children: \"Nextjs Quickstart Guide\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"/blog/using-supabase-replit\",\n          children: \"Using Supabase inside Replit\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://www.freecodecamp.org/news/the-complete-guide-to-full-stack-development-with-supabas/\",\n          children: \"Full Stack Development with Next.js and Supabase – The Complete Guide\"\n        })\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"/docs/learn/auth-deep-dive/auth-deep-dive-jwts\",\n          children: \"Auth Deep Dive - Learn everything there is to know about Supabase Auth\"\n        }), \" [videos]\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://www.twilio.com/blog/send-sms-notifications-supabase-users-node-js-twilio-messaging\",\n          children: \"Send SMS notifications using Twilio\"\n        })\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=pl9XfIWutKE\",\n          children: \"How to Integrate Supabase in Your Ionic App\"\n        }), \" [video]\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=LUMxJ4w-MUU\",\n          children: \"Building a Slack clone with authentication and realtime data syncing using Supabase\"\n        }), \" [video]\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://aboutmonica.com/blog/creating-protected-routes-in-next-js-with-supabase\",\n          children: \"Creating Protected Routes In Next.js With Supabase\"\n        })\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"additional-info\",\n      children: \"Additional Info\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Any intellectual property developed during the hackathon will belong to the team that developed it. We expect that each team will have an agreement between themselves regarding the IP, but this is not required\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"By making a submission you grant Supabase permission to use screenshots, code-snippets and/or links to your project or content of your README on our twitter, blog, website, email updates, and in the Supabase discord server. Supabase does not make any claims over your IP.\"\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"Supabase Hacktoberfest Hackathon 2021","slug":"supabase-hacktoberfest-hackathon-2021","lvl":1,"i":0,"seen":0},{"content":"Here's the plan","slug":"heres-the-plan","lvl":2,"i":1,"seen":0},{"content":"Details","slug":"details","lvl":2,"i":2,"seen":0},{"content":"Timeline","slug":"timeline","lvl":3,"i":3,"seen":0},{"content":"Prize categories","slug":"prize-categories","lvl":3,"i":4,"seen":0},{"content":"Submission","slug":"submission","lvl":3,"i":5,"seen":0},{"content":"Judging \u0026 announcement of winners","slug":"judging--announcement-of-winners","lvl":3,"i":6,"seen":0},{"content":"Rules","slug":"rules","lvl":3,"i":7,"seen":0},{"content":"Community \u0026 help","slug":"community--help","lvl":3,"i":8,"seen":0},{"content":"Resources \u0026 Guides","slug":"resources--guides","lvl":3,"i":9,"seen":0},{"content":"Additional Info","slug":"additional-info","lvl":3,"i":10,"seen":0}],"highest":1,"tokens":[{"type":"heading_open","hLevel":1,"lines":[1,2],"level":0},{"type":"inline","content":"[Supabase Hacktoberfest Hackathon 2021](#supabase-hacktoberfest-hackathon-2021)","level":1,"lines":[1,2],"children":[{"type":"text","content":"Supabase Hacktoberfest Hackathon 2021","level":0}],"lvl":1,"i":0,"seen":0,"slug":"supabase-hacktoberfest-hackathon-2021"},{"type":"heading_close","hLevel":1,"level":0},{"type":"paragraph_open","tight":false,"lines":[3,4],"level":0},{"type":"inline","content":"We were absolutely blown away by [all the amazing projects](/blog/hackathon-winners) you built during our [first Supabase Hackathon](/blog/1-the-supabase-hackathon), that we were looking for an excuse to run our next virtual open-source hackathon, and we found it in the upcoming [Hacktoberfest](https://hacktoberfest.digitalocean.com/).","level":1,"lines":[3,4],"children":[{"type":"text","content":"We were absolutely blown away by ","level":0},{"type":"link_open","href":"/blog/hackathon-winners","title":"","level":0},{"type":"text","content":"all the amazing projects","level":1},{"type":"link_close","level":0},{"type":"text","content":" you built during our ","level":0},{"type":"link_open","href":"/blog/1-the-supabase-hackathon","title":"","level":0},{"type":"text","content":"first Supabase Hackathon","level":1},{"type":"link_close","level":0},{"type":"text","content":", that we were looking for an excuse to run our next virtual open-source hackathon, and we found it in the upcoming ","level":0},{"type":"link_open","href":"https://hacktoberfest.digitalocean.com/","title":"","level":0},{"type":"text","content":"Hacktoberfest","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[5,6],"level":0},{"type":"inline","content":"[Here's the plan](#heres-the-plan)","level":1,"lines":[5,6],"children":[{"type":"text","content":"Here's the plan","level":0}],"lvl":2,"i":1,"seen":0,"slug":"heres-the-plan"},{"type":"heading_close","hLevel":2,"level":0},{"type":"bullet_list_open","lines":[7,14],"level":0},{"type":"list_item_open","lines":[7,8],"level":1},{"type":"paragraph_open","tight":true,"lines":[7,8],"level":2},{"type":"inline","content":"On **Friday Oct 1st at 08:00am PT** we're kicking things off with our Hacktoberfest Discord Hangout. Join us in the #hackathon channel on our Discord server: [https://discord.gg/bnncdqgBSS](https://discord.gg/bnncdqgBSS)","level":3,"lines":[7,8],"children":[{"type":"text","content":"On ","level":0},{"type":"strong_open","level":0},{"type":"text","content":"Friday Oct 1st at 08:00am PT","level":1},{"type":"strong_close","level":0},{"type":"text","content":" we're kicking things off with our Hacktoberfest Discord Hangout. Join us in the #hackathon channel on our Discord server: ","level":0},{"type":"link_open","href":"https://discord.gg/bnncdqgBSS","title":"","level":0},{"type":"text","content":"https://discord.gg/bnncdqgBSS","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[8,9],"level":1},{"type":"paragraph_open","tight":true,"lines":[8,9],"level":2},{"type":"inline","content":"Then you have 10 days to build a new **open-source** project with Supabase or contribute to one of our [supabase-community projects](https://github.com/supabase-community?q=topic%3AHacktoberfest\u0026type=\u0026language=\u0026sort=) that have the `hacktoberfest` topic.","level":3,"lines":[8,9],"children":[{"type":"text","content":"Then you have 10 days to build a new ","level":0},{"type":"strong_open","level":0},{"type":"text","content":"open-source","level":1},{"type":"strong_close","level":0},{"type":"text","content":" project with Supabase or contribute to one of our ","level":0},{"type":"link_open","href":"https://github.com/supabase-community?q=topic%3AHacktoberfest\u0026type=\u0026language=\u0026sort=","title":"","level":0},{"type":"text","content":"supabase-community projects","level":1},{"type":"link_close","level":0},{"type":"text","content":" that have the ","level":0},{"type":"code","content":"hacktoberfest","block":false,"level":0},{"type":"text","content":" topic.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[9,10],"level":1},{"type":"paragraph_open","tight":true,"lines":[9,10],"level":2},{"type":"inline","content":"It can be whatever you want - a project, mobile app, tool, library, anything","level":3,"lines":[9,10],"children":[{"type":"text","content":"It can be whatever you want - a project, mobile app, tool, library, anything","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[10,11],"level":1},{"type":"paragraph_open","tight":true,"lines":[10,11],"level":2},{"type":"inline","content":"Enter as an individual, or as a team of up to 5 people","level":3,"lines":[10,11],"children":[{"type":"text","content":"Enter as an individual, or as a team of up to 5 people","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[11,12],"level":1},{"type":"paragraph_open","tight":true,"lines":[11,12],"level":2},{"type":"inline","content":"Submission deadline is **Sunday Oct 10th at 11:59pm PT**","level":3,"lines":[11,12],"children":[{"type":"text","content":"Submission deadline is ","level":0},{"type":"strong_open","level":0},{"type":"text","content":"Sunday Oct 10th at 11:59pm PT","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[12,14],"level":1},{"type":"paragraph_open","tight":true,"lines":[12,13],"level":2},{"type":"inline","content":"Besides [earning your Hacktoberfest shirt](https://hacktoberfest.digitalocean.com/resources/participation), you can win some extremely limited edition Supabase swag ✨ (see [here](https://twitter.com/supabase/status/1440737587895799809?s=21) what folks won last time!)","level":3,"lines":[12,13],"children":[{"type":"text","content":"Besides ","level":0},{"type":"link_open","href":"https://hacktoberfest.digitalocean.com/resources/participation","title":"","level":0},{"type":"text","content":"earning your Hacktoberfest shirt","level":1},{"type":"link_close","level":0},{"type":"text","content":", you can win some extremely limited edition Supabase swag ✨ (see ","level":0},{"type":"link_open","href":"https://twitter.com/supabase/status/1440737587895799809?s=21","title":"","level":0},{"type":"text","content":"here","level":1},{"type":"link_close","level":0},{"type":"text","content":" what folks won last time!)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":2,"lines":[14,15],"level":0},{"type":"inline","content":"[Details](#details)","level":1,"lines":[14,15],"children":[{"type":"text","content":"Details","level":0}],"lvl":2,"i":2,"seen":0,"slug":"details"},{"type":"heading_close","hLevel":2,"level":0},{"type":"heading_open","hLevel":3,"lines":[16,17],"level":0},{"type":"inline","content":"[Timeline](#timeline)","level":1,"lines":[16,17],"children":[{"type":"text","content":"Timeline","level":0}],"lvl":3,"i":3,"seen":0,"slug":"timeline"},{"type":"heading_close","hLevel":3,"level":0},{"type":"bullet_list_open","lines":[18,24],"level":0},{"type":"list_item_open","lines":[18,19],"level":1},{"type":"paragraph_open","tight":true,"lines":[18,19],"level":2},{"type":"inline","content":"**Friday Oct 1st at 08:00am PT:** Opening Ceremony in the #hackathon channel [on Discord](https://discord.gg/bnncdqgBSS).","level":3,"lines":[18,19],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Friday Oct 1st at 08:00am PT:","level":1},{"type":"strong_close","level":0},{"type":"text","content":" Opening Ceremony in the #hackathon channel ","level":0},{"type":"link_open","href":"https://discord.gg/bnncdqgBSS","title":"","level":0},{"type":"text","content":"on Discord","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[19,20],"level":1},{"type":"paragraph_open","tight":true,"lines":[19,20],"level":2},{"type":"inline","content":"Build your project during the next 10 days and hang out with the community [on Discord](https://discord.gg/bnncdqgBSS).","level":3,"lines":[19,20],"children":[{"type":"text","content":"Build your project during the next 10 days and hang out with the community ","level":0},{"type":"link_open","href":"https://discord.gg/bnncdqgBSS","title":"","level":0},{"type":"text","content":"on Discord","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[20,21],"level":1},{"type":"paragraph_open","tight":true,"lines":[20,21],"level":2},{"type":"inline","content":"**Sunday Oct 10th at 11:59pm PT:** Submission deadline","level":3,"lines":[20,21],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Sunday Oct 10th at 11:59pm PT:","level":1},{"type":"strong_close","level":0},{"type":"text","content":" Submission deadline","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[21,22],"level":1},{"type":"paragraph_open","tight":true,"lines":[21,22],"level":2},{"type":"inline","content":"Judges Deliberate (Monday)","level":3,"lines":[21,22],"children":[{"type":"text","content":"Judges Deliberate (Monday)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[22,24],"level":1},{"type":"paragraph_open","tight":true,"lines":[22,23],"level":2},{"type":"inline","content":"We'll be contacting and announcing the winners [on Twitter](https://twitter.com/supabase) throughout the week after.","level":3,"lines":[22,23],"children":[{"type":"text","content":"We'll be contacting and announcing the winners ","level":0},{"type":"link_open","href":"https://twitter.com/supabase","title":"","level":0},{"type":"text","content":"on Twitter","level":1},{"type":"link_close","level":0},{"type":"text","content":" throughout the week after.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[24,31],"level":0},{"type":"inline","content":"\u003cblockquote class=\"twitter-tweet\" data-dnt=\"true\" data-theme=\"dark\"\u003e\n  \u003cp lang=\"en\" dir=\"ltr\"\u003e\n    Who else deserves the Gold Supabase Tee? 👀\n    \u003ca href=\"https://t.co/XPWw02kZad\"\u003ehttps://t.co/XPWw02kZad\u003c/a\u003e\n  \u003c/p\u003e\n  \u0026mdash; Supabase (@supabase) \u003ca href=\"https://twitter.com/supabase/status/1440737587895799809?ref_src=twsrc%5Etfw\"\u003eSeptember 22, 2021\u003c/a\u003e\n\u003c/blockquote\u003e \u003cscript async src=\"https://platform.twitter.com/widgets.js\" charSet=\"utf-8\"\u003e\u003c/script\u003e","level":1,"lines":[24,31],"children":[{"type":"text","content":"\u003cblockquote class=\"twitter-tweet\" data-dnt=\"true\" data-theme=\"dark\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cp lang=\"en\" dir=\"ltr\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Who else deserves the Gold Supabase Tee? 👀","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ca href=\"https://t.co/XPWw02kZad\"\u003ehttps://t.co/XPWw02kZad\u003c/a\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/p\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"— Supabase (@supabase) \u003ca href=\"https://twitter.com/supabase/status/1440737587895799809?ref_src=twsrc%5Etfw\"\u003eSeptember 22, 2021\u003c/a\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/blockquote\u003e \u003cscript async src=\"https://platform.twitter.com/widgets.js\" charSet=\"utf-8\"\u003e\u003c/script\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[32,33],"level":0},{"type":"inline","content":"[Prize categories](#prize-categories)","level":1,"lines":[32,33],"children":[{"type":"text","content":"Prize categories","level":0}],"lvl":3,"i":4,"seen":0,"slug":"prize-categories"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[34,35],"level":0},{"type":"inline","content":"There are 5 chances to win, there will be prizes for:","level":1,"lines":[34,35],"children":[{"type":"text","content":"There are 5 chances to win, there will be prizes for:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[36,42],"level":0},{"type":"list_item_open","lines":[36,37],"level":1},{"type":"paragraph_open","tight":true,"lines":[36,37],"level":2},{"type":"inline","content":"Best Overall Project","level":3,"lines":[36,37],"children":[{"type":"text","content":"Best Overall Project","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[37,38],"level":1},{"type":"paragraph_open","tight":true,"lines":[37,38],"level":2},{"type":"inline","content":"Most Visually Pleasing","level":3,"lines":[37,38],"children":[{"type":"text","content":"Most Visually Pleasing","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[38,39],"level":1},{"type":"paragraph_open","tight":true,"lines":[38,39],"level":2},{"type":"inline","content":"Most Technically Impressive","level":3,"lines":[38,39],"children":[{"type":"text","content":"Most Technically Impressive","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[39,40],"level":1},{"type":"paragraph_open","tight":true,"lines":[39,40],"level":2},{"type":"inline","content":"Best mobile project (can user Flutter, React Native, Ionic, etc.)","level":3,"lines":[39,40],"children":[{"type":"text","content":"Best mobile project (can user Flutter, React Native, Ionic, etc.)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[40,42],"level":1},{"type":"paragraph_open","tight":true,"lines":[40,41],"level":2},{"type":"inline","content":"Most Spooky/Fun (Halloween is coming up!)","level":3,"lines":[40,41],"children":[{"type":"text","content":"Most Spooky/Fun (Halloween is coming up!)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[42,43],"level":0},{"type":"inline","content":"There will be a winner and a runner-up prize for each category.","level":1,"lines":[42,43],"children":[{"type":"text","content":"There will be a winner and a runner-up prize for each category.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[44,45],"level":0},{"type":"inline","content":"[Submission](#submission)","level":1,"lines":[44,45],"children":[{"type":"text","content":"Submission","level":0}],"lvl":3,"i":5,"seen":0,"slug":"submission"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[46,47],"level":0},{"type":"inline","content":"Submit your project via [madewithsupabase.com/hacktoberfest](https://www.madewithsupabase.com/hacktoberfest)","level":1,"lines":[46,47],"children":[{"type":"text","content":"Submit your project via ","level":0},{"type":"link_open","href":"https://www.madewithsupabase.com/hacktoberfest","title":"","level":0},{"type":"text","content":"madewithsupabase.com/hacktoberfest","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[48,49],"level":0},{"type":"inline","content":"You will be asked to send a link to a GitHub (or similar) repo, in the README you should include:","level":1,"lines":[48,49],"children":[{"type":"text","content":"You will be asked to send a link to a GitHub (or similar) repo, in the README you should include:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[50,61],"level":0},{"type":"list_item_open","lines":[50,51],"level":1},{"type":"paragraph_open","tight":true,"lines":[50,51],"level":2},{"type":"inline","content":"link to hosted demo (if applicable)","level":3,"lines":[50,51],"children":[{"type":"text","content":"link to hosted demo (if applicable)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[51,52],"level":1},{"type":"paragraph_open","tight":true,"lines":[51,52],"level":2},{"type":"inline","content":"list of team members github handles (and twitter if they have one)","level":3,"lines":[51,52],"children":[{"type":"text","content":"list of team members github handles (and twitter if they have one)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[52,53],"level":1},{"type":"paragraph_open","tight":true,"lines":[52,53],"level":2},{"type":"inline","content":"any demo videos, instructions, or memes","level":3,"lines":[52,53],"children":[{"type":"text","content":"any demo videos, instructions, or memes","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[53,58],"level":1},{"type":"paragraph_open","tight":true,"lines":[53,54],"level":2},{"type":"inline","content":"a brief description of how you used Supabase:","level":3,"lines":[53,54],"children":[{"type":"text","content":"a brief description of how you used Supabase:","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"bullet_list_open","lines":[54,58],"level":2},{"type":"list_item_open","lines":[54,55],"level":3},{"type":"paragraph_open","tight":true,"lines":[54,55],"level":4},{"type":"inline","content":"to store data?","level":5,"lines":[54,55],"children":[{"type":"text","content":"to store data?","level":0}]},{"type":"paragraph_close","tight":true,"level":4},{"type":"list_item_close","level":3},{"type":"list_item_open","lines":[55,56],"level":3},{"type":"paragraph_open","tight":true,"lines":[55,56],"level":4},{"type":"inline","content":"realtime?","level":5,"lines":[55,56],"children":[{"type":"text","content":"realtime?","level":0}]},{"type":"paragraph_close","tight":true,"level":4},{"type":"list_item_close","level":3},{"type":"list_item_open","lines":[56,57],"level":3},{"type":"paragraph_open","tight":true,"lines":[56,57],"level":4},{"type":"inline","content":"auth?","level":5,"lines":[56,57],"children":[{"type":"text","content":"auth?","level":0}]},{"type":"paragraph_close","tight":true,"level":4},{"type":"list_item_close","level":3},{"type":"list_item_open","lines":[57,58],"level":3},{"type":"paragraph_open","tight":true,"lines":[57,58],"level":4},{"type":"inline","content":"storage?","level":5,"lines":[57,58],"children":[{"type":"text","content":"storage?","level":0}]},{"type":"paragraph_close","tight":true,"level":4},{"type":"list_item_close","level":3},{"type":"bullet_list_close","level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[58,59],"level":1},{"type":"paragraph_open","tight":true,"lines":[58,59],"level":2},{"type":"inline","content":"any other info you want the judges to know (motivations/ideas/process)","level":3,"lines":[58,59],"children":[{"type":"text","content":"any other info you want the judges to know (motivations/ideas/process)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[59,61],"level":1},{"type":"paragraph_open","tight":true,"lines":[59,60],"level":2},{"type":"inline","content":"_optional_ team photo","level":3,"lines":[59,60],"children":[{"type":"em_open","level":0},{"type":"text","content":"optional","level":1},{"type":"em_close","level":0},{"type":"text","content":" team photo","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":3,"lines":[61,62],"level":0},{"type":"inline","content":"[Judging \u0026 announcement of winners](#judging--announcement-of-winners)","level":1,"lines":[61,62],"children":[{"type":"text","content":"Judging \u0026 announcement of winners","level":0}],"lvl":3,"i":6,"seen":0,"slug":"judging--announcement-of-winners"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[63,64],"level":0},{"type":"inline","content":"The Supabase team will excitedly review what you've built. They will be looking for a few things, including:","level":1,"lines":[63,64],"children":[{"type":"text","content":"The Supabase team will excitedly review what you've built. They will be looking for a few things, including:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[65,74],"level":0},{"type":"list_item_open","lines":[65,66],"level":1},{"type":"paragraph_open","tight":true,"lines":[65,66],"level":2},{"type":"inline","content":"creativity/inventiveness","level":3,"lines":[65,66],"children":[{"type":"text","content":"creativity/inventiveness","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[66,67],"level":1},{"type":"paragraph_open","tight":true,"lines":[66,67],"level":2},{"type":"inline","content":"functions correctly/smoothly","level":3,"lines":[66,67],"children":[{"type":"text","content":"functions correctly/smoothly","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[67,68],"level":1},{"type":"paragraph_open","tight":true,"lines":[67,68],"level":2},{"type":"inline","content":"visually pleasing","level":3,"lines":[67,68],"children":[{"type":"text","content":"visually pleasing","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[68,69],"level":1},{"type":"paragraph_open","tight":true,"lines":[68,69],"level":2},{"type":"inline","content":"technically impressive","level":3,"lines":[68,69],"children":[{"type":"text","content":"technically impressive","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[69,72],"level":1},{"type":"paragraph_open","tight":true,"lines":[69,70],"level":2},{"type":"inline","content":"use of Supabase features","level":3,"lines":[69,70],"children":[{"type":"text","content":"use of Supabase features","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"bullet_list_open","lines":[70,72],"level":2},{"type":"list_item_open","lines":[70,71],"level":3},{"type":"paragraph_open","tight":true,"lines":[70,71],"level":4},{"type":"inline","content":"deep usage of a single feature or","level":5,"lines":[70,71],"children":[{"type":"text","content":"deep usage of a single feature or","level":0}]},{"type":"paragraph_close","tight":true,"level":4},{"type":"list_item_close","level":3},{"type":"list_item_open","lines":[71,72],"level":3},{"type":"paragraph_open","tight":true,"lines":[71,72],"level":4},{"type":"inline","content":"broad usage are both ok","level":5,"lines":[71,72],"children":[{"type":"text","content":"broad usage are both ok","level":0}]},{"type":"paragraph_close","tight":true,"level":4},{"type":"list_item_close","level":3},{"type":"bullet_list_close","level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[72,74],"level":1},{"type":"paragraph_open","tight":true,"lines":[72,73],"level":2},{"type":"inline","content":"FUN! 😃","level":3,"lines":[72,73],"children":[{"type":"text","content":"FUN! 😃","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[74,75],"level":0},{"type":"inline","content":"We'll be contacting and announcing winners [on Twitter](https://twitter.com/supabase) throughout the week after submission closes.","level":1,"lines":[74,75],"children":[{"type":"text","content":"We'll be contacting and announcing winners ","level":0},{"type":"link_open","href":"https://twitter.com/supabase","title":"","level":0},{"type":"text","content":"on Twitter","level":1},{"type":"link_close","level":0},{"type":"text","content":" throughout the week after submission closes.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[76,83],"level":0},{"type":"inline","content":"\u003cblockquote class=\"twitter-tweet\" data-dnt=\"true\" data-theme=\"dark\"\u003e\n  \u003cp lang=\"en\" dir=\"ltr\"\u003e\n    Absolutely buzzing to have won the\n    \u003ca href=\"https://twitter.com/supabase?ref_src=twsrc%5Etfw\"\u003e@supabase\u003c/a\u003e hackathon! 🥳 \u003ca href=\"https://t.co/rm5HBuju73\"\u003ehttps://t.co/rm5HBuju73\u003c/a\u003e\n  \u003c/p\u003e\n  \u0026mdash; Josh Cawthorne (@cawthornejosh) \u003ca href=\"https://twitter.com/cawthornejosh/status/1424985377136390183?ref_src=twsrc%5Etfw\"\u003eAugust 10, 2021\u003c/a\u003e\n\u003c/blockquote\u003e \u003cscript async src=\"https://platform.twitter.com/widgets.js\" charset=\"utf-8\"\u003e\u003c/script\u003e","level":1,"lines":[76,83],"children":[{"type":"text","content":"\u003cblockquote class=\"twitter-tweet\" data-dnt=\"true\" data-theme=\"dark\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cp lang=\"en\" dir=\"ltr\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Absolutely buzzing to have won the","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ca href=\"https://twitter.com/supabase?ref_src=twsrc%5Etfw\"\u003e@supabase\u003c/a\u003e hackathon! 🥳 \u003ca href=\"https://t.co/rm5HBuju73\"\u003ehttps://t.co/rm5HBuju73\u003c/a\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/p\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"— Josh Cawthorne (@cawthornejosh) \u003ca href=\"https://twitter.com/cawthornejosh/status/1424985377136390183?ref_src=twsrc%5Etfw\"\u003eAugust 10, 2021\u003c/a\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/blockquote\u003e \u003cscript async src=\"https://platform.twitter.com/widgets.js\" charset=\"utf-8\"\u003e\u003c/script\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[84,85],"level":0},{"type":"inline","content":"[Rules](#rules)","level":1,"lines":[84,85],"children":[{"type":"text","content":"Rules","level":0}],"lvl":3,"i":7,"seen":0,"slug":"rules"},{"type":"heading_close","hLevel":3,"level":0},{"type":"bullet_list_open","lines":[86,96],"level":0},{"type":"list_item_open","lines":[86,87],"level":1},{"type":"paragraph_open","tight":true,"lines":[86,87],"level":2},{"type":"inline","content":"Team size 1-5 (all team members on winning teams will receive a prize)","level":3,"lines":[86,87],"children":[{"type":"text","content":"Team size 1-5 (all team members on winning teams will receive a prize)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[87,88],"level":1},{"type":"paragraph_open","tight":true,"lines":[87,88],"level":2},{"type":"inline","content":"You cannot be in multiple teams","level":3,"lines":[87,88],"children":[{"type":"text","content":"You cannot be in multiple teams","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[88,89],"level":1},{"type":"paragraph_open","tight":true,"lines":[88,89],"level":2},{"type":"inline","content":"One submission per team","level":3,"lines":[88,89],"children":[{"type":"text","content":"One submission per team","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[89,90],"level":1},{"type":"paragraph_open","tight":true,"lines":[89,90],"level":2},{"type":"inline","content":"All design elements, code, etc. for your project/feature must be created **during** the event","level":3,"lines":[89,90],"children":[{"type":"text","content":"All design elements, code, etc. for your project/feature must be created ","level":0},{"type":"strong_open","level":0},{"type":"text","content":"during","level":1},{"type":"strong_close","level":0},{"type":"text","content":" the event","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[90,91],"level":1},{"type":"paragraph_open","tight":true,"lines":[90,91],"level":2},{"type":"inline","content":"All entries must be Open Source (link to source code required in entry)","level":3,"lines":[90,91],"children":[{"type":"text","content":"All entries must be Open Source (link to source code required in entry)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[91,92],"level":1},{"type":"paragraph_open","tight":true,"lines":[91,92],"level":2},{"type":"inline","content":"Must use Supabase in some capacity","level":3,"lines":[91,92],"children":[{"type":"text","content":"Must use Supabase in some capacity","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[92,93],"level":1},{"type":"paragraph_open","tight":true,"lines":[92,93],"level":2},{"type":"inline","content":"Can be any language or framework","level":3,"lines":[92,93],"children":[{"type":"text","content":"Can be any language or framework","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[93,94],"level":1},{"type":"paragraph_open","tight":true,"lines":[93,94],"level":2},{"type":"inline","content":"You must submit before the deadline (no late entries)","level":3,"lines":[93,94],"children":[{"type":"text","content":"You must submit before the deadline (no late entries)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[94,96],"level":1},{"type":"paragraph_open","tight":true,"lines":[94,95],"level":2},{"type":"inline","content":"You can continue to make updates to your project after the submission deadline, but there is no guarantee that the judges will see additions made after the submission time.","level":3,"lines":[94,95],"children":[{"type":"text","content":"You can continue to make updates to your project after the submission deadline, but there is no guarantee that the judges will see additions made after the submission time.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":3,"lines":[96,97],"level":0},{"type":"inline","content":"[Community \u0026 help](#community--help)","level":1,"lines":[96,97],"children":[{"type":"text","content":"Community \u0026 help","level":0}],"lvl":3,"i":8,"seen":0,"slug":"community--help"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[98,99],"level":0},{"type":"inline","content":"Hang out with the Supabase team and community on Discord:","level":1,"lines":[98,99],"children":[{"type":"text","content":"Hang out with the Supabase team and community on Discord:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[100,103],"level":0},{"type":"list_item_open","lines":[100,101],"level":1},{"type":"paragraph_open","tight":true,"lines":[100,101],"level":2},{"type":"inline","content":"Text channel: hackathon","level":3,"lines":[100,101],"children":[{"type":"text","content":"Text channel: hackathon","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[101,103],"level":1},{"type":"paragraph_open","tight":true,"lines":[101,102],"level":2},{"type":"inline","content":"Audio channel: hackathon","level":3,"lines":[101,102],"children":[{"type":"text","content":"Audio channel: hackathon","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[103,104],"level":0},{"type":"inline","content":"If you need help or advice when building, find other people to join your team, or if you just want to chill and watch people build, come and join us!","level":1,"lines":[103,104],"children":[{"type":"text","content":"If you need help or advice when building, find other people to join your team, or if you just want to chill and watch people build, come and join us!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[105,106],"level":0},{"type":"inline","content":"Join our Discord: [discord.gg/bnncdqgBSS](https://discord.gg/bnncdqgBSS)","level":1,"lines":[105,106],"children":[{"type":"text","content":"Join our Discord: ","level":0},{"type":"link_open","href":"https://discord.gg/bnncdqgBSS","title":"","level":0},{"type":"text","content":"discord.gg/bnncdqgBSS","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[107,108],"level":0},{"type":"inline","content":"![Discord Hangout](/images/blog/hackathon/community.png)","level":1,"lines":[107,108],"children":[{"type":"image","src":"/images/blog/hackathon/community.png","title":"","alt":"Discord Hangout","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[109,110],"level":0},{"type":"inline","content":"[Resources \u0026 Guides](#resources--guides)","level":1,"lines":[109,110],"children":[{"type":"text","content":"Resources \u0026 Guides","level":0}],"lvl":3,"i":9,"seen":0,"slug":"resources--guides"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[111,112],"level":0},{"type":"inline","content":"Here's a collection of resources that will help you get started building with Supabase:","level":1,"lines":[111,112],"children":[{"type":"text","content":"Here's a collection of resources that will help you get started building with Supabase:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[113,126],"level":0},{"type":"list_item_open","lines":[113,114],"level":1},{"type":"paragraph_open","tight":true,"lines":[113,114],"level":2},{"type":"inline","content":"Need some inspiration? [See what folks built last time](/blog/hackathon-winners)!","level":3,"lines":[113,114],"children":[{"type":"text","content":"Need some inspiration? ","level":0},{"type":"link_open","href":"/blog/hackathon-winners","title":"","level":0},{"type":"text","content":"See what folks built last time","level":1},{"type":"link_close","level":0},{"type":"text","content":"!","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[114,115],"level":1},{"type":"paragraph_open","tight":true,"lines":[114,115],"level":2},{"type":"inline","content":"[Examples and Resources](/docs/guides/examples)","level":3,"lines":[114,115],"children":[{"type":"link_open","href":"/docs/guides/examples","title":"","level":0},{"type":"text","content":"Examples and Resources","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[115,116],"level":1},{"type":"paragraph_open","tight":true,"lines":[115,116],"level":2},{"type":"inline","content":"[Supabase Crash Course](https://www.youtube.com/watch?v=7uKQBl9uZ00) [video]","level":3,"lines":[115,116],"children":[{"type":"link_open","href":"https://www.youtube.com/watch?v=7uKQBl9uZ00","title":"","level":0},{"type":"text","content":"Supabase Crash Course","level":1},{"type":"link_close","level":0},{"type":"text","content":" [video]","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[116,117],"level":1},{"type":"paragraph_open","tight":true,"lines":[116,117],"level":2},{"type":"inline","content":"[Flutter Quickstart Guide](/docs/guides/with-flutter)","level":3,"lines":[116,117],"children":[{"type":"link_open","href":"/docs/guides/with-flutter","title":"","level":0},{"type":"text","content":"Flutter Quickstart Guide","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[117,118],"level":1},{"type":"paragraph_open","tight":true,"lines":[117,118],"level":2},{"type":"inline","content":"[Nextjs Quickstart Guide](/docs/guides/with-nextjs)","level":3,"lines":[117,118],"children":[{"type":"link_open","href":"/docs/guides/with-nextjs","title":"","level":0},{"type":"text","content":"Nextjs Quickstart Guide","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[118,119],"level":1},{"type":"paragraph_open","tight":true,"lines":[118,119],"level":2},{"type":"inline","content":"[Using Supabase inside Replit](/blog/using-supabase-replit)","level":3,"lines":[118,119],"children":[{"type":"link_open","href":"/blog/using-supabase-replit","title":"","level":0},{"type":"text","content":"Using Supabase inside Replit","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[119,120],"level":1},{"type":"paragraph_open","tight":true,"lines":[119,120],"level":2},{"type":"inline","content":"[Full Stack Development with Next.js and Supabase – The Complete Guide](https://www.freecodecamp.org/news/the-complete-guide-to-full-stack-development-with-supabas/)","level":3,"lines":[119,120],"children":[{"type":"link_open","href":"https://www.freecodecamp.org/news/the-complete-guide-to-full-stack-development-with-supabas/","title":"","level":0},{"type":"text","content":"Full Stack Development with Next.js and Supabase – The Complete Guide","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[120,121],"level":1},{"type":"paragraph_open","tight":true,"lines":[120,121],"level":2},{"type":"inline","content":"[Auth Deep Dive - Learn everything there is to know about Supabase Auth](/docs/learn/auth-deep-dive/auth-deep-dive-jwts) [videos]","level":3,"lines":[120,121],"children":[{"type":"link_open","href":"/docs/learn/auth-deep-dive/auth-deep-dive-jwts","title":"","level":0},{"type":"text","content":"Auth Deep Dive - Learn everything there is to know about Supabase Auth","level":1},{"type":"link_close","level":0},{"type":"text","content":" [videos]","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[121,122],"level":1},{"type":"paragraph_open","tight":true,"lines":[121,122],"level":2},{"type":"inline","content":"[Send SMS notifications using Twilio](https://www.twilio.com/blog/send-sms-notifications-supabase-users-node-js-twilio-messaging)","level":3,"lines":[121,122],"children":[{"type":"link_open","href":"https://www.twilio.com/blog/send-sms-notifications-supabase-users-node-js-twilio-messaging","title":"","level":0},{"type":"text","content":"Send SMS notifications using Twilio","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[122,123],"level":1},{"type":"paragraph_open","tight":true,"lines":[122,123],"level":2},{"type":"inline","content":"[How to Integrate Supabase in Your Ionic App](https://www.youtube.com/watch?v=pl9XfIWutKE) [video]","level":3,"lines":[122,123],"children":[{"type":"link_open","href":"https://www.youtube.com/watch?v=pl9XfIWutKE","title":"","level":0},{"type":"text","content":"How to Integrate Supabase in Your Ionic App","level":1},{"type":"link_close","level":0},{"type":"text","content":" [video]","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[123,124],"level":1},{"type":"paragraph_open","tight":true,"lines":[123,124],"level":2},{"type":"inline","content":"[Building a Slack clone with authentication and realtime data syncing using Supabase](https://www.youtube.com/watch?v=LUMxJ4w-MUU) [video]","level":3,"lines":[123,124],"children":[{"type":"link_open","href":"https://www.youtube.com/watch?v=LUMxJ4w-MUU","title":"","level":0},{"type":"text","content":"Building a Slack clone with authentication and realtime data syncing using Supabase","level":1},{"type":"link_close","level":0},{"type":"text","content":" [video]","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[124,126],"level":1},{"type":"paragraph_open","tight":true,"lines":[124,125],"level":2},{"type":"inline","content":"[Creating Protected Routes In Next.js With Supabase](https://aboutmonica.com/blog/creating-protected-routes-in-next-js-with-supabase)","level":3,"lines":[124,125],"children":[{"type":"link_open","href":"https://aboutmonica.com/blog/creating-protected-routes-in-next-js-with-supabase","title":"","level":0},{"type":"text","content":"Creating Protected Routes In Next.js With Supabase","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":3,"lines":[126,127],"level":0},{"type":"inline","content":"[Additional Info](#additional-info)","level":1,"lines":[126,127],"children":[{"type":"text","content":"Additional Info","level":0}],"lvl":3,"i":10,"seen":0,"slug":"additional-info"},{"type":"heading_close","hLevel":3,"level":0},{"type":"bullet_list_open","lines":[128,130],"level":0},{"type":"list_item_open","lines":[128,129],"level":1},{"type":"paragraph_open","tight":true,"lines":[128,129],"level":2},{"type":"inline","content":"Any intellectual property developed during the hackathon will belong to the team that developed it. We expect that each team will have an agreement between themselves regarding the IP, but this is not required","level":3,"lines":[128,129],"children":[{"type":"text","content":"Any intellectual property developed during the hackathon will belong to the team that developed it. We expect that each team will have an agreement between themselves regarding the IP, but this is not required","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[129,130],"level":1},{"type":"paragraph_open","tight":true,"lines":[129,130],"level":2},{"type":"inline","content":"By making a submission you grant Supabase permission to use screenshots, code-snippets and/or links to your project or content of your README on our twitter, blog, website, email updates, and in the Supabase discord server. Supabase does not make any claims over your IP.","level":3,"lines":[129,130],"children":[{"type":"text","content":"By making a submission you grant Supabase permission to use screenshots, code-snippets and/or links to your project or content of your README on our twitter, blog, website, email updates, and in the Supabase discord server. Supabase does not make any claims over your IP.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0}],"content":"- [Supabase Hacktoberfest Hackathon 2021](#supabase-hacktoberfest-hackathon-2021)\n  * [Here's the plan](#heres-the-plan)\n  * [Details](#details)\n    + [Timeline](#timeline)\n    + [Prize categories](#prize-categories)\n    + [Submission](#submission)\n    + [Judging \u0026 announcement of winners](#judging--announcement-of-winners)\n    + [Rules](#rules)\n    + [Community \u0026 help](#community--help)\n    + [Resources \u0026 Guides](#resources--guides)\n    + [Additional Info](#additional-info)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-hacktoberfest-hackathon-2021"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>