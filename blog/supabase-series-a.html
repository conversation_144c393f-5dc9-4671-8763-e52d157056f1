<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase $30m Series A</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Supabase just raised $30M, bringing our total funding to $36M." data-next-head=""/><meta property="og:title" content="Supabase $30m Series A" data-next-head=""/><meta property="og:description" content="Supabase just raised $30M, bringing our total funding to $36M." data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-series-a" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2021-10-28" data-next-head=""/><meta property="article:author" content="https://github.com/kiwicopple" data-next-head=""/><meta property="article:tag" content="supabase" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/series-a/supabase-series-a.png" data-next-head=""/><meta property="og:image:alt" content="Supabase $30m Series A thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Supabase $30m Series A</h1><div class="text-light flex space-x-3 text-sm"><p>28 Oct 2021</p><p>•</p><p>10 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/kiwicopple"><div class="flex items-center gap-3"><div class="w-10"><img alt="Paul Copplestone avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fkiwicopple.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fkiwicopple.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fkiwicopple.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Paul Copplestone</span><span class="text-foreground-lighter mb-0 text-xs">CEO and Co-Founder</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Supabase $30m Series A" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fseries-a%2Fsupabase-series-a.png&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fseries-a%2Fsupabase-series-a.png&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fseries-a%2Fsupabase-series-a.png&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fseries-a%2Fsupabase-series-a.png&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fseries-a%2Fsupabase-series-a.png&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fseries-a%2Fsupabase-series-a.png&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fseries-a%2Fsupabase-series-a.png&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fseries-a%2Fsupabase-series-a.png&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fseries-a%2Fsupabase-series-a.png&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fseries-a%2Fsupabase-series-a.png&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fseries-a%2Fsupabase-series-a.png&amp;w=3840&amp;q=100"/></div><p>Supabase <a href="https://techcrunch.com/2021/09/09/supabase-raises-30m-for-its-open-source-insta-backend/">just raised $30M</a>,
bringing our total funding to $36M. How will we spend this? Read on to find out.</p>
<h2 id="were-growing-fast" class="group scroll-mt-24">We&#x27;re growing fast<a href="#were-growing-fast" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>From the outside, you can see that we&#x27;re growing fast.
To solidify some of the numbers with internal metrics:</p>
<h3 id="database-growth" class="group scroll-mt-24">Database Growth<a href="#database-growth" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>As of September, we&#x27;ve launched over 50,000 databases. This is <em>only</em> on our hosted platform - it doesn&#x27;t include our open source offering,
because we don&#x27;t add telemetry. Active databases are growing 35% per month.</p>
<p></p>
<h3 id="developers" class="group scroll-mt-24">Developers<a href="#developers" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>As of September, over 40,000 developers have signed up to Supabase from some of the world&#x27;s leading companies.
Because of the versatility of Postgres, they&#x27;re building everything from
<a href="https://supabase.com/blog/case-study-xendit">counter-fraud systems for Fintech</a> to
<a href="https://supabase.com/blog/mobbin-supabase-200000-users">digital platforms powering 200,000 users</a>.</p>
<div class="grid 
      gap-0.5 rounded-lg overflow-hidden
      items-center
    	grid-cols-3
      md:grid-cols-4
      lg:grid-cols-4
    "><div class="bg-surface-200 w-full col-span-1 flex items-center justify-center undefined p-8 undefined"><div class="relative h-8 w-full overflow-auto h-8 "><img alt="wells-fargo logo" loading="lazy" decoding="async" data-nimg="fill" class="object-scale-down object-center bg-no-repeat contrast-0 filter opacity-50" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fwells-fargo.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fwells-fargo.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fwells-fargo.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fwells-fargo.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fwells-fargo.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fwells-fargo.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fwells-fargo.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fwells-fargo.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fwells-fargo.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fwells-fargo.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fwells-fargo.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fwells-fargo.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fwells-fargo.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fwells-fargo.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fwells-fargo.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fwells-fargo.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fwells-fargo.png&amp;w=3840&amp;q=75"/></div></div><div class="bg-surface-200 w-full col-span-1 flex items-center justify-center undefined p-8 undefined"><div class="relative h-8 w-full overflow-auto h-8 "><img alt="under-armour logo" loading="lazy" decoding="async" data-nimg="fill" class="object-scale-down object-center bg-no-repeat contrast-0 filter opacity-50" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Funder-armour.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Funder-armour.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Funder-armour.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Funder-armour.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Funder-armour.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Funder-armour.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Funder-armour.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Funder-armour.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Funder-armour.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Funder-armour.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Funder-armour.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Funder-armour.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Funder-armour.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Funder-armour.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Funder-armour.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Funder-armour.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Funder-armour.png&amp;w=3840&amp;q=75"/></div></div><div class="bg-surface-200 w-full col-span-1 flex items-center justify-center undefined p-8 undefined"><div class="relative h-8 w-full overflow-auto h-8 "><img alt="audi-logo logo" loading="lazy" decoding="async" data-nimg="fill" class="object-scale-down object-center bg-no-repeat contrast-0 filter opacity-50" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Faudi-logo.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Faudi-logo.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Faudi-logo.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Faudi-logo.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Faudi-logo.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Faudi-logo.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Faudi-logo.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Faudi-logo.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Faudi-logo.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Faudi-logo.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Faudi-logo.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Faudi-logo.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Faudi-logo.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Faudi-logo.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Faudi-logo.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Faudi-logo.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Faudi-logo.png&amp;w=3840&amp;q=75"/></div></div><div class="bg-surface-200 w-full col-span-1 flex items-center justify-center undefined p-8 undefined"><div class="relative h-8 w-full overflow-auto h-8 "><img alt="capitalone logo" loading="lazy" decoding="async" data-nimg="fill" class="object-scale-down object-center bg-no-repeat contrast-0 filter opacity-50" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcapitalone.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcapitalone.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcapitalone.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcapitalone.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcapitalone.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcapitalone.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcapitalone.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcapitalone.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcapitalone.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcapitalone.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcapitalone.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcapitalone.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcapitalone.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcapitalone.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcapitalone.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcapitalone.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcapitalone.png&amp;w=3840&amp;q=75"/></div></div><div class="bg-surface-200 w-full col-span-1 flex items-center justify-center undefined p-8 undefined"><div class="relative h-8 w-full overflow-auto h-8 "><img alt="coinbase logo" loading="lazy" decoding="async" data-nimg="fill" class="object-scale-down object-center bg-no-repeat contrast-0 filter opacity-50" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcoinbase.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcoinbase.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcoinbase.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcoinbase.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcoinbase.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcoinbase.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcoinbase.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcoinbase.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcoinbase.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcoinbase.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcoinbase.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcoinbase.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcoinbase.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcoinbase.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcoinbase.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcoinbase.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fcoinbase.png&amp;w=3840&amp;q=75"/></div></div><div class="bg-surface-200 w-full col-span-1 flex items-center justify-center undefined p-8 undefined"><div class="relative h-8 w-full overflow-auto h-8 "><img alt="facebook logo" loading="lazy" decoding="async" data-nimg="fill" class="object-scale-down object-center bg-no-repeat contrast-0 filter opacity-50" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ffacebook.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ffacebook.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ffacebook.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ffacebook.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ffacebook.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ffacebook.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ffacebook.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ffacebook.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ffacebook.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ffacebook.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ffacebook.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ffacebook.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ffacebook.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ffacebook.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ffacebook.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ffacebook.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ffacebook.png&amp;w=3840&amp;q=75"/></div></div><div class="bg-surface-200 w-full col-span-1 flex items-center justify-center undefined p-8 undefined"><div class="relative h-8 w-full overflow-auto h-8 "><img alt="github logo" loading="lazy" decoding="async" data-nimg="fill" class="object-scale-down object-center bg-no-repeat contrast-0 filter opacity-50" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgithub.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgithub.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgithub.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgithub.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgithub.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgithub.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgithub.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgithub.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgithub.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgithub.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgithub.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgithub.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgithub.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgithub.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgithub.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgithub.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgithub.png&amp;w=3840&amp;q=75"/></div></div><div class="bg-surface-200 w-full col-span-1 flex items-center justify-center undefined p-8 undefined"><div class="relative h-8 w-full overflow-auto h-8 "><img alt="google logo" loading="lazy" decoding="async" data-nimg="fill" class="object-scale-down object-center bg-no-repeat contrast-0 filter opacity-50" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgoogle.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgoogle.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgoogle.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgoogle.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgoogle.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgoogle.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgoogle.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgoogle.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgoogle.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgoogle.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgoogle.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgoogle.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgoogle.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgoogle.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgoogle.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgoogle.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgoogle.png&amp;w=3840&amp;q=75"/></div></div><div class="bg-surface-200 w-full col-span-1 flex items-center justify-center undefined p-8 undefined"><div class="relative h-8 w-full overflow-auto h-8 "><img alt="gsk logo" loading="lazy" decoding="async" data-nimg="fill" class="object-scale-down object-center bg-no-repeat contrast-0 filter opacity-50" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgsk.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgsk.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgsk.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgsk.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgsk.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgsk.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgsk.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgsk.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgsk.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgsk.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgsk.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgsk.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgsk.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgsk.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgsk.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgsk.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fgsk.png&amp;w=3840&amp;q=75"/></div></div><div class="bg-surface-200 w-full col-span-1 flex items-center justify-center undefined p-8 undefined"><div class="relative h-8 w-full overflow-auto h-8 "><img alt="hewlett-packard logo" loading="lazy" decoding="async" data-nimg="fill" class="object-scale-down object-center bg-no-repeat contrast-0 filter opacity-50" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhewlett-packard.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhewlett-packard.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhewlett-packard.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhewlett-packard.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhewlett-packard.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhewlett-packard.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhewlett-packard.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhewlett-packard.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhewlett-packard.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhewlett-packard.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhewlett-packard.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhewlett-packard.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhewlett-packard.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhewlett-packard.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhewlett-packard.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhewlett-packard.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhewlett-packard.png&amp;w=3840&amp;q=75"/></div></div><div class="bg-surface-200 w-full col-span-1 flex items-center justify-center undefined p-8 undefined"><div class="relative h-8 w-full overflow-auto h-8 "><img alt="hubspot logo" loading="lazy" decoding="async" data-nimg="fill" class="object-scale-down object-center bg-no-repeat contrast-0 filter opacity-50" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhubspot.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhubspot.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhubspot.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhubspot.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhubspot.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhubspot.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhubspot.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhubspot.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhubspot.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhubspot.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhubspot.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhubspot.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhubspot.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhubspot.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhubspot.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhubspot.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fhubspot.png&amp;w=3840&amp;q=75"/></div></div><div class="bg-surface-200 w-full col-span-1 flex items-center justify-center undefined p-8 undefined"><div class="relative h-8 w-full overflow-auto h-8 "><img alt="ibm logo" loading="lazy" decoding="async" data-nimg="fill" class="object-scale-down object-center bg-no-repeat contrast-0 filter opacity-50" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fibm.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fibm.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fibm.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fibm.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fibm.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fibm.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fibm.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fibm.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fibm.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fibm.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fibm.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fibm.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fibm.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fibm.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fibm.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fibm.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fibm.png&amp;w=3840&amp;q=75"/></div></div><div class="bg-surface-200 w-full col-span-1 flex items-center justify-center undefined p-8 undefined"><div class="relative h-8 w-full overflow-auto h-8 "><img alt="instagram logo" loading="lazy" decoding="async" data-nimg="fill" class="object-scale-down object-center bg-no-repeat contrast-0 filter opacity-50" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Finstagram.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Finstagram.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Finstagram.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Finstagram.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Finstagram.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Finstagram.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Finstagram.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Finstagram.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Finstagram.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Finstagram.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Finstagram.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Finstagram.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Finstagram.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Finstagram.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Finstagram.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Finstagram.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Finstagram.png&amp;w=3840&amp;q=75"/></div></div><div class="bg-surface-200 w-full col-span-1 flex items-center justify-center undefined p-8 undefined"><div class="relative h-8 w-full overflow-auto h-8 "><img alt="linkedin logo" loading="lazy" decoding="async" data-nimg="fill" class="object-scale-down object-center bg-no-repeat contrast-0 filter opacity-50" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Flinkedin.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Flinkedin.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Flinkedin.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Flinkedin.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Flinkedin.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Flinkedin.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Flinkedin.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Flinkedin.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Flinkedin.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Flinkedin.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Flinkedin.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Flinkedin.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Flinkedin.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Flinkedin.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Flinkedin.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Flinkedin.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Flinkedin.png&amp;w=3840&amp;q=75"/></div></div><div class="bg-surface-200 w-full col-span-1 flex items-center justify-center undefined p-8 undefined"><div class="relative h-8 w-full overflow-auto h-8 "><img alt="microsoft logo" loading="lazy" decoding="async" data-nimg="fill" class="object-scale-down object-center bg-no-repeat contrast-0 filter opacity-50" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fmicrosoft.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fmicrosoft.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fmicrosoft.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fmicrosoft.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fmicrosoft.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fmicrosoft.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fmicrosoft.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fmicrosoft.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fmicrosoft.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fmicrosoft.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fmicrosoft.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fmicrosoft.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fmicrosoft.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fmicrosoft.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fmicrosoft.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fmicrosoft.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fmicrosoft.png&amp;w=3840&amp;q=75"/></div></div><div class="bg-surface-200 w-full col-span-1 flex items-center justify-center undefined p-8 undefined"><div class="relative h-8 w-full overflow-auto h-8 "><img alt="netflix logo" loading="lazy" decoding="async" data-nimg="fill" class="object-scale-down object-center bg-no-repeat contrast-0 filter opacity-50" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnetflix.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnetflix.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnetflix.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnetflix.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnetflix.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnetflix.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnetflix.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnetflix.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnetflix.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnetflix.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnetflix.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnetflix.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnetflix.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnetflix.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnetflix.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnetflix.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnetflix.png&amp;w=3840&amp;q=75"/></div></div><div class="bg-surface-200 w-full col-span-1 flex items-center justify-center undefined p-8 undefined"><div class="relative h-8 w-full overflow-auto h-8 "><img alt="notion logo" loading="lazy" decoding="async" data-nimg="fill" class="object-scale-down object-center bg-no-repeat contrast-0 filter opacity-50" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnotion.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnotion.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnotion.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnotion.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnotion.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnotion.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnotion.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnotion.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnotion.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnotion.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnotion.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnotion.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnotion.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnotion.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnotion.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnotion.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fnotion.png&amp;w=3840&amp;q=75"/></div></div><div class="bg-surface-200 w-full col-span-1 flex items-center justify-center undefined p-8 undefined"><div class="relative h-8 w-full overflow-auto h-8 "><img alt="red-hat logo" loading="lazy" decoding="async" data-nimg="fill" class="object-scale-down object-center bg-no-repeat contrast-0 filter opacity-50" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fred-hat.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fred-hat.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fred-hat.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fred-hat.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fred-hat.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fred-hat.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fred-hat.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fred-hat.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fred-hat.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fred-hat.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fred-hat.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fred-hat.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fred-hat.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fred-hat.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fred-hat.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fred-hat.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fred-hat.png&amp;w=3840&amp;q=75"/></div></div><div class="bg-surface-200 w-full col-span-1 flex items-center justify-center undefined p-8 undefined"><div class="relative h-8 w-full overflow-auto h-8 "><img alt="robinhood logo" loading="lazy" decoding="async" data-nimg="fill" class="object-scale-down object-center bg-no-repeat contrast-0 filter opacity-50" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Frobinhood.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Frobinhood.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Frobinhood.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Frobinhood.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Frobinhood.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Frobinhood.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Frobinhood.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Frobinhood.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Frobinhood.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Frobinhood.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Frobinhood.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Frobinhood.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Frobinhood.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Frobinhood.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Frobinhood.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Frobinhood.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Frobinhood.png&amp;w=3840&amp;q=75"/></div></div><div class="bg-surface-200 w-full col-span-1 flex items-center justify-center undefined p-8 undefined"><div class="relative h-8 w-full overflow-auto h-8 "><img alt="salesforce logo" loading="lazy" decoding="async" data-nimg="fill" class="object-scale-down object-center bg-no-repeat contrast-0 filter opacity-50" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsalesforce.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsalesforce.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsalesforce.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsalesforce.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsalesforce.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsalesforce.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsalesforce.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsalesforce.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsalesforce.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsalesforce.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsalesforce.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsalesforce.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsalesforce.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsalesforce.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsalesforce.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsalesforce.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsalesforce.png&amp;w=3840&amp;q=75"/></div></div><div class="bg-surface-200 w-full col-span-1 flex items-center justify-center undefined p-8 undefined"><div class="relative h-8 w-full overflow-auto h-8 "><img alt="santander logo" loading="lazy" decoding="async" data-nimg="fill" class="object-scale-down object-center bg-no-repeat contrast-0 filter opacity-50" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsantander.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsantander.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsantander.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsantander.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsantander.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsantander.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsantander.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsantander.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsantander.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsantander.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsantander.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsantander.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsantander.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsantander.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsantander.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsantander.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsantander.png&amp;w=3840&amp;q=75"/></div></div><div class="bg-surface-200 w-full col-span-1 flex items-center justify-center undefined p-8 undefined"><div class="relative h-8 w-full overflow-auto h-8 "><img alt="shopify logo" loading="lazy" decoding="async" data-nimg="fill" class="object-scale-down object-center bg-no-repeat contrast-0 filter opacity-50" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fshopify.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fshopify.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fshopify.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fshopify.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fshopify.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fshopify.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fshopify.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fshopify.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fshopify.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fshopify.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fshopify.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fshopify.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fshopify.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fshopify.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fshopify.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fshopify.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fshopify.png&amp;w=3840&amp;q=75"/></div></div><div class="bg-surface-200 w-full col-span-1 flex items-center justify-center undefined p-8 undefined"><div class="relative h-8 w-full overflow-auto h-8 "><img alt="squarespace logo" loading="lazy" decoding="async" data-nimg="fill" class="object-scale-down object-center bg-no-repeat contrast-0 filter opacity-50" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsquarespace.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsquarespace.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsquarespace.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsquarespace.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsquarespace.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsquarespace.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsquarespace.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsquarespace.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsquarespace.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsquarespace.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsquarespace.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsquarespace.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsquarespace.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsquarespace.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsquarespace.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsquarespace.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Fsquarespace.png&amp;w=3840&amp;q=75"/></div></div><div class="bg-surface-200 w-full col-span-1 flex items-center justify-center undefined p-8 undefined"><div class="relative h-8 w-full overflow-auto h-8 "><img alt="twitter logo" loading="lazy" decoding="async" data-nimg="fill" class="object-scale-down object-center bg-no-repeat contrast-0 filter opacity-50" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ftwitter.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ftwitter.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ftwitter.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ftwitter.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ftwitter.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ftwitter.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ftwitter.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ftwitter.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ftwitter.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ftwitter.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ftwitter.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ftwitter.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ftwitter.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ftwitter.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ftwitter.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ftwitter.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fcompany%2Fcompanies-using-supabase%2Ftwitter.png&amp;w=3840&amp;q=75"/></div></div></div>
<h3 id="community" class="group scroll-mt-24">Community<a href="#community" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Supabase has been in GitHub&#x27;s top-15 fastest growing open source startups for
<a href="https://twitter.com/kiwicopple/status/1451104569266671619">five consecutive quarters</a>,
and our <a href="https://discord.supabase.com">Discord</a> has grown to nearly 4000 members since launching just 3 months ago.
Our growth is all organic.</p>
<p></p>
<h2 id="about-the-round" class="group scroll-mt-24">About the round<a href="#about-the-round" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Supabase is an ambitious project. Firebase, to their credit, is a very well-developed platform which offers <em>a lot</em> of functionality.
Building an alternative is a bit like launching five different startups at once.</p>
<p>That said, our approach has been clear from the start: we don&#x27;t want to re-invent the wheel.
We want to leverage existing open source products wherever we can, improving them by up-streaming changes and employing maintainers
(<a href="https://supabase.com/blog/supabase-steve-chavez">PostgREST</a>,
<a href="https://www.postgresql.org/message-id/CABwTF4UZzYoHcaEGe2cESVn-e9dc3wzg%3Dmvx7Tz8qbKJ7p3UKA%40mail.gmail.com">PostgreSQL</a>).
This model is the real promise of open source, and we&#x27;ve been lucky to have investors who have backed our approach
since the start - Y Combinator, Mozilla, and our lead investor, Coatue.</p>
<p>Because of their conviction in Supabase, Coatue doubled down on their Seed investment to lead our Series A. We&#x27;re extremely excited to welcome
<a href="https://www.linkedin.com/in/caryn-marooney/">Caryn Marooney</a> to our Board.</p>
<blockquote class="text-foreground"><p>We are proud to lead a second consecutive round in Supabase and officially join the board.</p><p><p>We continue to be impressed by Paul and Ant and it has been a pleasure partnering with them. We
believe that the team has done an impressive job scaling their open-source community and think
that the strong traction and adoption speaks for itself.</p></p><div class="align-center m-0 flex h-8 items-center gap-3"><img alt="Caryn Marooney, Partner at Coatue. avatar" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="h-8 w-8 rounded-full object-cover text-center m-0" style="color:transparent" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Favatars%2Fcaryn-marooney.jpeg&amp;w=32&amp;q=75 1x, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Favatars%2Fcaryn-marooney.jpeg&amp;w=64&amp;q=75 2x" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Favatars%2Fcaryn-marooney.jpeg&amp;w=64&amp;q=75"/><figcaption style="margin-top:0" class="text-foreground-lighter font-normal not-italic not-prose"><p>Caryn Marooney, Partner at Coatue.</p></figcaption></div></blockquote>
<h3 id="joining-the-round" class="group scroll-mt-24">Joining the round<a href="#joining-the-round" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We&#x27;ve had a number of new investors join the round, this time a lot of operators and partners:</p>
<ul>
<li>Elad Gill</li>
<li>Tom Preston-Werner (GitHub cofounder)</li>
<li>Solomon Hykes (Docker cofounder)</li>
<li>Alex Solomon (PagerDuty cofounder)</li>
<li>Guillermo Rauch (Vercel founder)</li>
<li>Kurt Mackey (Fly cofounder)</li>
<li>Chris Nguyen (LogDNA cofounder)</li>
<li>Tod Sacerdoti (Pipedream Founder)</li>
<li>Alana Anderson (Base Case Capital)</li>
<li>Astasia Myers (Quiet Capital)</li>
<li>(and more)</li>
</ul>
<a href="../company.html#investors" target="_blank"><p>See all investors</p></a>
<h2 id="building-supabase" class="group scroll-mt-24">Building Supabase<a href="#building-supabase" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Our early positioning is an &quot;open source Firebase alternative&quot; but you might be surprised to learn that this is a small part of the Supabase vision.</p>
<p>Firebase is arguably one of the best tools in the world for building <em>new products</em>, but it has a flaw: scalability.</p>
<h3 id="phase-1-start-with-scalability" class="group scroll-mt-24">Phase 1: Start with scalability<a href="#phase-1-start-with-scalability" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>The term &quot;open source Firebase alternative&quot; is becoming popular now, and we are often asked: how are we different?</p>
<p>Simple. Supabase has smuggled scalability into the product, perhaps without you noticing.
Every Supabase project is a <em>full</em> Postgres database. It&#x27;s not &quot;Postgres compatible&quot;, it&#x27;s not &quot;built on top of&quot;,
and we don&#x27;t abstract it. If we did, your applications would face the same scalability issues as Firebase.</p>
<p>Supabase simply makes Postgres easy to use - hopefully easier than any other database platform you&#x27;ve ever used.</p>
<p>We plan to make Postgres the default database for every developer in the world. How do we do that? By making Postgres both easy
(Phase 2: tooling) and scalable (Phase 3: Cloud-Native Postgres).</p>
<h3 id="phase-2-postgres-tooling" class="group scroll-mt-24">Phase 2: Postgres Tooling<a href="#phase-2-postgres-tooling" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>User-facing applications usually require tools beyond just a database and frontend framework:</p>
<ul>
<li><strong>APIs:</strong> to provide access to your database from untrusted systems.</li>
<li><strong>Authentication:</strong> for users sign ups.</li>
<li><strong>File Storage:</strong> for large images and media.</li>
</ul>
<p>Supabase makes all of this easy. Beyond the database we offer <a href="../docs/guides/auth.html">Auth</a>,
RESTful and Realtime <a href="https://supabase.com/docs/guides/database/api">APIs</a>, a <a href="../docs/guides/storage.html">Storage</a>
system for large files, and a bunch of other tools - all wrapped up into a simple-to-use Dashboard.</p>
<p></p>
<p>And this is where Supabase really differentiates itself: by doing less. All of these features build on top of PostgreSQL&#x27;s
existing functionality.</p>
<ul>
<li><strong>Row Level Security:</strong> restrict user access to data and files using Postgres Policies.</li>
<li><strong>Realtime data streams:</strong> subscribe to database changes using the logical replication stream.</li>
<li><strong>Database Webhooks (previously called &quot;Function Hooks&quot;):</strong> trigger external systems using Postgres triggers and our <a href="https://github.com/supabase/pg_net/">async request extension</a>.</li>
<li><strong>RESTful APIs:</strong> query your database <a href="https://supabase.com/docs/reference/javascript/select">tables</a> and <a href="https://supabase.com/docs/reference/javascript/rpc">functions</a> over HTTP, using <a href="https://postgrest.org">PostgREST</a>.</li>
</ul>
<h3 id="phase-3-cloud-native-postgresql" class="group scroll-mt-24">Phase 3: Cloud-native PostgreSQL<a href="#phase-3-cloud-native-postgresql" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>PostgreSQL was created over 30 years ago, and it&#x27;s one of the safest and most reliable databases in the world.</p>
<p>But modern developers are becoming accustomed to cloud-native services with several characteristics:</p>
<ul>
<li>Automatic scaling.</li>
<li>Low latency anywhere in the world.</li>
<li>Billing based on usage.</li>
</ul>
<p>PostgreSQL is still catching up to the modern cloud environment in some areas. What does a Cloud-native PostgreSQL look like?</p>
<ul>
<li><strong>Branching:</strong> developers should be able to &quot;fork&quot; a database at any point. This is particularly important for Jamstack developers who run deployments for every Git branch.</li>
<li><strong>Scalable storage:</strong> storage should grow and shrink without the user needing to provision more space themselves.</li>
<li><strong>Distributed:</strong> An entire fleet of databases, distributed around the world, should feel like a single database (and even better if all nodes can accept read/write workloads).</li>
<li><strong>Ephemeral compute:</strong> developers don&#x27;t want to be charged for a database which isn&#x27;t doing anything. Likewise - if they are running huge computation then the database should scale up seamlessly, and scale down to zero when it&#x27;s unused.</li>
<li><strong>Snapshots and time-travel:</strong> developers should be able to roll back to any point in time, or take a copy of their database with just a click of a button.</li>
</ul>
<p>This is the future of Supabase - a platform to power your transactional workloads: no matter how big or small; no matter the use-case.
We&#x27;re building a cloud-native Postgres platform.</p>
<h2 id="join-us" class="group scroll-mt-24">Join us<a href="#join-us" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We have a track record of hiring open source maintainers, PostgREST is maintained by a
<a href="https://supabase.com/blog/supabase-steve-chavez">Supabase developer</a>, one of our first hires over a year ago.
We&#x27;ve recently added <a href="https://www.postgresql.org/message-id/CABwTF4UZzYoHcaEGe2cESVn-e9dc3wzg%3Dmvx7Tz8qbKJ7p3UKA%40mail.gmail.com">another developer</a>
with the explicit goal of working within the PostgreSQL community.</p>
<p>If you want to help us build the future of cloud-native Postgres, join us:
<a href="https://about.supabase.com/careers/postgres-experts">https://about.supabase.com/careers/postgres-experts</a></p>
<h2 id="get-started" class="group scroll-mt-24">Get started<a href="#get-started" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ul>
<li>Start using Supabase today: <strong><a href="https://supabase.com/dashboard/">supabase.com/dashboard</a></strong></li>
<li>Make sure to <strong><a href="https://github.com/supabase/supabase">star us on GitHub</a></strong></li>
<li>Follow us <strong><a href="https://twitter.com/supabase">on Twitter</a></strong></li>
<li>Subscribe to our <strong><a href="https://www.youtube.com/c/supabase">YouTube channel</a></strong></li>
<li>Become a <strong><a href="https://github.com/sponsors/supabase">sponsor</a></strong></li>
</ul></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-series-a&amp;text=Supabase%20%2430m%20Series%20A"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-series-a&amp;text=Supabase%20%2430m%20Series%20A"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-series-a&amp;t=Supabase%20%2430m%20Series%20A"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="supabase-beta-october-2021.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Supabase Beta October 2021</h4><p class="small">7 November 2021</p></div></div></div></div></a></div><div><a href="../customers/replenysh.html"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Replenysh uses Supabase to implement OTP in less than 24-hours</h4><p class="small">19 October 2021</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/supabase"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">supabase</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#were-growing-fast">We&#x27;re growing fast</a>
<ul>
<li><a href="#database-growth">Database Growth</a></li>
<li><a href="#developers">Developers</a></li>
<li><a href="#community">Community</a></li>
</ul>
</li>
<li><a href="#about-the-round">About the round</a>
<ul>
<li><a href="#joining-the-round">Joining the round</a></li>
</ul>
</li>
<li><a href="#building-supabase">Building Supabase</a>
<ul>
<li><a href="#phase-1-start-with-scalability">Phase 1: Start with scalability</a></li>
<li><a href="#phase-2-postgres-tooling">Phase 2: Postgres Tooling</a></li>
<li><a href="#phase-3-cloud-native-postgresql">Phase 3: Cloud-native PostgreSQL</a></li>
</ul>
</li>
<li><a href="#join-us">Join us</a></li>
<li><a href="#get-started">Get started</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-series-a&amp;text=Supabase%20%2430m%20Series%20A"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-series-a&amp;text=Supabase%20%2430m%20Series%20A"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-series-a&amp;t=Supabase%20%2430m%20Series%20A"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"supabase-beta-october-2021","title":"Supabase Beta October 2021","description":"Three new Auth providers, multi-schema support, and we're gearing up for another Launch Week.","author":"paul_copplestone","author_url":"https://github.com/kiwicopple","author_image_url":"https://github.com/kiwicopple.png","image":"2021-oct/release-oct-2021.jpg","thumb":"2021-oct/release-oct-2021-cover.jpg","categories":["product"],"tags":["release-notes"],"date":"2021-11-07","toc_depth":3,"video":"https://www.youtube.com/v/yL5WbAKAKjE","formattedDate":"7 November 2021","readingTime":"4 minute read","url":"/blog/supabase-beta-october-2021","path":"/blog/supabase-beta-october-2021"},"nextPost":{"slug":"replenysh-time-to-value-in-less-than-24-hours","title":"Replenysh uses Supabase to implement OTP in less than 24-hours","description":"Learn how Replenysh uses Supabase to power the circular economy, redefining how brands interact with their customers and products.","author":"rory_wilding","author_url":"https://github.com/roryw10","author_image_url":"https://github.com/roryw10.png","image":"replenysh/og-replenysh.png","thumb":"replenysh/thumb-replenysh.png","categories":["company"],"tags":["auth"],"date":"2021-10-19","toc_depth":2,"formattedDate":"19 October 2021","readingTime":"5 minute read","url":"/blog/replenysh-time-to-value-in-less-than-24-hours","path":"/blog/replenysh-time-to-value-in-less-than-24-hours"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-series-a","source":"\nSupabase [just raised $30M](https://techcrunch.com/2021/09/09/supabase-raises-30m-for-its-open-source-insta-backend/),\nbringing our total funding to $36M. How will we spend this? Read on to find out.\n\n## We're growing fast\n\nFrom the outside, you can see that we're growing fast.\nTo solidify some of the numbers with internal metrics:\n\n### Database Growth\n\nAs of September, we've launched over 50,000 databases. This is _only_ on our hosted platform - it doesn't include our open source offering,\nbecause we don't add telemetry. Active databases are growing 35% per month.\n\n![Supabase has launched more than 50000 databases](/images/blog/series-a/total-databases.png)\n\n### Developers\n\nAs of September, over 40,000 developers have signed up to Supabase from some of the world's leading companies.\nBecause of the versatility of Postgres, they're building everything from\n[counter-fraud systems for Fintech](/blog/case-study-xendit) to\n[digital platforms powering 200,000 users](/blog/mobbin-supabase-200000-users).\n\n\u003cImageGrid\n  smCols={3}\n  mdCols={4}\n  lgCols={4}\n  images={[\n    {\n      name: 'wells-fargo',\n      image: '/images/company/companies-using-supabase/wells-fargo.png',\n    },\n    {\n      name: 'under-armour',\n      image: '/images/company/companies-using-supabase/under-armour.png',\n    },\n    {\n      name: 'audi-logo',\n      image: '/images/company/companies-using-supabase/audi-logo.png',\n    },\n    {\n      name: 'capitalone',\n      image: '/images/company/companies-using-supabase/capitalone.png',\n    },\n    {\n      name: 'coinbase',\n      image: '/images/company/companies-using-supabase/coinbase.png',\n    },\n    {\n      name: 'facebook',\n      image: '/images/company/companies-using-supabase/facebook.png',\n    },\n    {\n      name: 'github',\n      image: '/images/company/companies-using-supabase/github.png',\n    },\n    {\n      name: 'google',\n      image: '/images/company/companies-using-supabase/google.png',\n    },\n    {\n      name: 'gsk',\n      image: '/images/company/companies-using-supabase/gsk.png',\n    },\n    {\n      name: 'hewlett-packard',\n      image: '/images/company/companies-using-supabase/hewlett-packard.png',\n    },\n    {\n      name: 'hubspot',\n      image: '/images/company/companies-using-supabase/hubspot.png',\n    },\n    {\n      name: 'ibm',\n      image: '/images/company/companies-using-supabase/ibm.png',\n    },\n    {\n      name: 'instagram',\n      image: '/images/company/companies-using-supabase/instagram.png',\n    },\n    {\n      name: 'linkedin',\n      image: '/images/company/companies-using-supabase/linkedin.png',\n    },\n    {\n      name: 'microsoft',\n      image: '/images/company/companies-using-supabase/microsoft.png',\n    },\n    {\n      name: 'netflix',\n      image: '/images/company/companies-using-supabase/netflix.png',\n    },\n    {\n      name: 'notion',\n      image: '/images/company/companies-using-supabase/notion.png',\n    },\n    {\n      name: 'red-hat',\n      image: '/images/company/companies-using-supabase/red-hat.png',\n    },\n    {\n      name: 'robinhood',\n      image: '/images/company/companies-using-supabase/robinhood.png',\n    },\n    {\n      name: 'salesforce',\n      image: '/images/company/companies-using-supabase/salesforce.png',\n    },\n    {\n      name: 'santander',\n      image: '/images/company/companies-using-supabase/santander.png',\n    },\n    {\n      name: 'shopify',\n      image: '/images/company/companies-using-supabase/shopify.png',\n    },\n    {\n      name: 'squarespace',\n      image: '/images/company/companies-using-supabase/squarespace.png',\n    },\n    {\n      name: 'twitter',\n      image: '/images/company/companies-using-supabase/twitter.png',\n    },\n  ]}\n/\u003e\n\n### Community\n\nSupabase has been in GitHub's top-15 fastest growing open source startups for\n[five consecutive quarters](https://twitter.com/kiwicopple/status/1451104569266671619),\nand our [Discord](https://discord.supabase.com) has grown to nearly 4000 members since launching just 3 months ago.\nOur growth is all organic.\n\n![Supabase Discord](/images/blog/series-a/discord-supabase-community.png)\n\n## About the round\n\nSupabase is an ambitious project. Firebase, to their credit, is a very well-developed platform which offers _a lot_ of functionality.\nBuilding an alternative is a bit like launching five different startups at once.\n\nThat said, our approach has been clear from the start: we don't want to re-invent the wheel.\nWe want to leverage existing open source products wherever we can, improving them by up-streaming changes and employing maintainers\n([PostgREST](/blog/supabase-steve-chavez),\n[PostgreSQL](https://www.postgresql.org/message-id/CABwTF4UZzYoHcaEGe2cESVn-e9dc3wzg%3Dmvx7Tz8qbKJ7p3UKA%40mail.gmail.com)).\nThis model is the real promise of open source, and we've been lucky to have investors who have backed our approach\nsince the start - Y Combinator, Mozilla, and our lead investor, Coatue.\n\nBecause of their conviction in Supabase, Coatue doubled down on their Seed investment to lead our Series A. We're extremely excited to welcome\n[Caryn Marooney](https://www.linkedin.com/in/caryn-marooney/) to our Board.\n\n\u003cQuote img=\"caryn-marooney.jpeg\" caption=\"Caryn Marooney, Partner at Coatue.\"\u003e\n  \u003cp\u003eWe are proud to lead a second consecutive round in Supabase and officially join the board.\u003c/p\u003e\n  \u003cp\u003e\n    We continue to be impressed by Paul and Ant and it has been a pleasure partnering with them. We\n    believe that the team has done an impressive job scaling their open-source community and think\n    that the strong traction and adoption speaks for itself.\n  \u003c/p\u003e\n\u003c/Quote\u003e\n\n### Joining the round\n\nWe've had a number of new investors join the round, this time a lot of operators and partners:\n\n- Elad Gill\n- Tom Preston-Werner (GitHub cofounder)\n- Solomon Hykes (Docker cofounder)\n- Alex Solomon (PagerDuty cofounder)\n- Guillermo Rauch (Vercel founder)\n- Kurt Mackey (Fly cofounder)\n- Chris Nguyen (LogDNA cofounder)\n- Tod Sacerdoti (Pipedream Founder)\n- Alana Anderson (Base Case Capital)\n- Astasia Myers (Quiet Capital)\n- (and more)\n\n\u003ca href=\"/company#investors\" target=\"_blank\"\u003e\n  See all investors\n\u003c/a\u003e\n\n## Building Supabase\n\nOur early positioning is an \"open source Firebase alternative\" but you might be surprised to learn that this is a small part of the Supabase vision.\n\nFirebase is arguably one of the best tools in the world for building _new products_, but it has a flaw: scalability.\n\n### Phase 1: Start with scalability\n\nThe term \"open source Firebase alternative\" is becoming popular now, and we are often asked: how are we different?\n\nSimple. Supabase has smuggled scalability into the product, perhaps without you noticing.\nEvery Supabase project is a _full_ Postgres database. It's not \"Postgres compatible\", it's not \"built on top of\",\nand we don't abstract it. If we did, your applications would face the same scalability issues as Firebase.\n\nSupabase simply makes Postgres easy to use - hopefully easier than any other database platform you've ever used.\n\nWe plan to make Postgres the default database for every developer in the world. How do we do that? By making Postgres both easy\n(Phase 2: tooling) and scalable (Phase 3: Cloud-Native Postgres).\n\n### Phase 2: Postgres Tooling\n\nUser-facing applications usually require tools beyond just a database and frontend framework:\n\n- **APIs:** to provide access to your database from untrusted systems.\n- **Authentication:** for users sign ups.\n- **File Storage:** for large images and media.\n\nSupabase makes all of this easy. Beyond the database we offer [Auth](/docs/guides/auth),\nRESTful and Realtime [APIs](/docs/guides/database/api), a [Storage](/docs/guides/storage)\nsystem for large files, and a bunch of other tools - all wrapped up into a simple-to-use Dashboard.\n\n![Supabase Dashboard](/images/blog/series-a/dashboard.png)\n\nAnd this is where Supabase really differentiates itself: by doing less. All of these features build on top of PostgreSQL's\nexisting functionality.\n\n- **Row Level Security:** restrict user access to data and files using Postgres Policies.\n- **Realtime data streams:** subscribe to database changes using the logical replication stream.\n- **Database Webhooks (previously called \"Function Hooks\"):** trigger external systems using Postgres triggers and our [async request extension](https://github.com/supabase/pg_net/).\n- **RESTful APIs:** query your database [tables](/docs/reference/javascript/select) and [functions](/docs/reference/javascript/rpc) over HTTP, using [PostgREST](https://postgrest.org).\n\n### Phase 3: Cloud-native PostgreSQL\n\nPostgreSQL was created over 30 years ago, and it's one of the safest and most reliable databases in the world.\n\nBut modern developers are becoming accustomed to cloud-native services with several characteristics:\n\n- Automatic scaling.\n- Low latency anywhere in the world.\n- Billing based on usage.\n\nPostgreSQL is still catching up to the modern cloud environment in some areas. What does a Cloud-native PostgreSQL look like?\n\n- **Branching:** developers should be able to \"fork\" a database at any point. This is particularly important for Jamstack developers who run deployments for every Git branch.\n- **Scalable storage:** storage should grow and shrink without the user needing to provision more space themselves.\n- **Distributed:** An entire fleet of databases, distributed around the world, should feel like a single database (and even better if all nodes can accept read/write workloads).\n- **Ephemeral compute:** developers don't want to be charged for a database which isn't doing anything. Likewise - if they are running huge computation then the database should scale up seamlessly, and scale down to zero when it's unused.\n- **Snapshots and time-travel:** developers should be able to roll back to any point in time, or take a copy of their database with just a click of a button.\n\nThis is the future of Supabase - a platform to power your transactional workloads: no matter how big or small; no matter the use-case.\nWe're building a cloud-native Postgres platform.\n\n## Join us\n\nWe have a track record of hiring open source maintainers, PostgREST is maintained by a\n[Supabase developer](/blog/supabase-steve-chavez), one of our first hires over a year ago.\nWe've recently added [another developer](https://www.postgresql.org/message-id/CABwTF4UZzYoHcaEGe2cESVn-e9dc3wzg%3Dmvx7Tz8qbKJ7p3UKA%40mail.gmail.com)\nwith the explicit goal of working within the PostgreSQL community.\n\nIf you want to help us build the future of cloud-native Postgres, join us:\n[https://about.supabase.com/careers/postgres-experts](https://about.supabase.com/careers/postgres-experts)\n\n## Get started\n\n- Start using Supabase today: **[supabase.com/dashboard](https://supabase.com/dashboard/)**\n- Make sure to **[star us on GitHub](https://github.com/supabase/supabase)**\n- Follow us **[on Twitter](https://twitter.com/supabase)**\n- Subscribe to our **[YouTube channel](https://www.youtube.com/c/supabase)**\n- Become a **[sponsor](https://github.com/sponsors/supabase)**\n","title":"Supabase $30m Series A","description":"Supabase just raised $30M, bringing our total funding to $36M.","author":"paul_copplestone","image":"series-a/supabase-series-a.png","thumb":"series-a/supabase-series-a.png","categories":["company"],"tags":["supabase"],"date":"2021-10-28","toc_depth":3,"content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    a: \"a\",\n    h2: \"h2\",\n    h3: \"h3\",\n    em: \"em\",\n    img: \"img\",\n    ul: \"ul\",\n    li: \"li\",\n    strong: \"strong\"\n  }, _provideComponents(), props.components), {ImageGrid, Quote} = _components;\n  if (!ImageGrid) _missingMdxReference(\"ImageGrid\", true);\n  if (!Quote) _missingMdxReference(\"Quote\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsxs(_components.p, {\n      children: [\"Supabase \", _jsx(_components.a, {\n        href: \"https://techcrunch.com/2021/09/09/supabase-raises-30m-for-its-open-source-insta-backend/\",\n        children: \"just raised $30M\"\n      }), \",\\nbringing our total funding to $36M. How will we spend this? Read on to find out.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"were-growing-fast\",\n      children: \"We're growing fast\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"From the outside, you can see that we're growing fast.\\nTo solidify some of the numbers with internal metrics:\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"database-growth\",\n      children: \"Database Growth\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"As of September, we've launched over 50,000 databases. This is \", _jsx(_components.em, {\n        children: \"only\"\n      }), \" on our hosted platform - it doesn't include our open source offering,\\nbecause we don't add telemetry. Active databases are growing 35% per month.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/series-a/total-databases.png\",\n        alt: \"Supabase has launched more than 50000 databases\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"developers\",\n      children: \"Developers\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"As of September, over 40,000 developers have signed up to Supabase from some of the world's leading companies.\\nBecause of the versatility of Postgres, they're building everything from\\n\", _jsx(_components.a, {\n        href: \"/blog/case-study-xendit\",\n        children: \"counter-fraud systems for Fintech\"\n      }), \" to\\n\", _jsx(_components.a, {\n        href: \"/blog/mobbin-supabase-200000-users\",\n        children: \"digital platforms powering 200,000 users\"\n      }), \".\"]\n    }), \"\\n\", _jsx(ImageGrid, {\n      smCols: 3,\n      mdCols: 4,\n      lgCols: 4,\n      images: [{\n        name: 'wells-fargo',\n        image: '/images/company/companies-using-supabase/wells-fargo.png'\n      }, {\n        name: 'under-armour',\n        image: '/images/company/companies-using-supabase/under-armour.png'\n      }, {\n        name: 'audi-logo',\n        image: '/images/company/companies-using-supabase/audi-logo.png'\n      }, {\n        name: 'capitalone',\n        image: '/images/company/companies-using-supabase/capitalone.png'\n      }, {\n        name: 'coinbase',\n        image: '/images/company/companies-using-supabase/coinbase.png'\n      }, {\n        name: 'facebook',\n        image: '/images/company/companies-using-supabase/facebook.png'\n      }, {\n        name: 'github',\n        image: '/images/company/companies-using-supabase/github.png'\n      }, {\n        name: 'google',\n        image: '/images/company/companies-using-supabase/google.png'\n      }, {\n        name: 'gsk',\n        image: '/images/company/companies-using-supabase/gsk.png'\n      }, {\n        name: 'hewlett-packard',\n        image: '/images/company/companies-using-supabase/hewlett-packard.png'\n      }, {\n        name: 'hubspot',\n        image: '/images/company/companies-using-supabase/hubspot.png'\n      }, {\n        name: 'ibm',\n        image: '/images/company/companies-using-supabase/ibm.png'\n      }, {\n        name: 'instagram',\n        image: '/images/company/companies-using-supabase/instagram.png'\n      }, {\n        name: 'linkedin',\n        image: '/images/company/companies-using-supabase/linkedin.png'\n      }, {\n        name: 'microsoft',\n        image: '/images/company/companies-using-supabase/microsoft.png'\n      }, {\n        name: 'netflix',\n        image: '/images/company/companies-using-supabase/netflix.png'\n      }, {\n        name: 'notion',\n        image: '/images/company/companies-using-supabase/notion.png'\n      }, {\n        name: 'red-hat',\n        image: '/images/company/companies-using-supabase/red-hat.png'\n      }, {\n        name: 'robinhood',\n        image: '/images/company/companies-using-supabase/robinhood.png'\n      }, {\n        name: 'salesforce',\n        image: '/images/company/companies-using-supabase/salesforce.png'\n      }, {\n        name: 'santander',\n        image: '/images/company/companies-using-supabase/santander.png'\n      }, {\n        name: 'shopify',\n        image: '/images/company/companies-using-supabase/shopify.png'\n      }, {\n        name: 'squarespace',\n        image: '/images/company/companies-using-supabase/squarespace.png'\n      }, {\n        name: 'twitter',\n        image: '/images/company/companies-using-supabase/twitter.png'\n      }]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"community\",\n      children: \"Community\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Supabase has been in GitHub's top-15 fastest growing open source startups for\\n\", _jsx(_components.a, {\n        href: \"https://twitter.com/kiwicopple/status/1451104569266671619\",\n        children: \"five consecutive quarters\"\n      }), \",\\nand our \", _jsx(_components.a, {\n        href: \"https://discord.supabase.com\",\n        children: \"Discord\"\n      }), \" has grown to nearly 4000 members since launching just 3 months ago.\\nOur growth is all organic.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/series-a/discord-supabase-community.png\",\n        alt: \"Supabase Discord\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"about-the-round\",\n      children: \"About the round\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Supabase is an ambitious project. Firebase, to their credit, is a very well-developed platform which offers \", _jsx(_components.em, {\n        children: \"a lot\"\n      }), \" of functionality.\\nBuilding an alternative is a bit like launching five different startups at once.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"That said, our approach has been clear from the start: we don't want to re-invent the wheel.\\nWe want to leverage existing open source products wherever we can, improving them by up-streaming changes and employing maintainers\\n(\", _jsx(_components.a, {\n        href: \"/blog/supabase-steve-chavez\",\n        children: \"PostgREST\"\n      }), \",\\n\", _jsx(_components.a, {\n        href: \"https://www.postgresql.org/message-id/CABwTF4UZzYoHcaEGe2cESVn-e9dc3wzg%3Dmvx7Tz8qbKJ7p3UKA%40mail.gmail.com\",\n        children: \"PostgreSQL\"\n      }), \").\\nThis model is the real promise of open source, and we've been lucky to have investors who have backed our approach\\nsince the start - Y Combinator, Mozilla, and our lead investor, Coatue.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Because of their conviction in Supabase, Coatue doubled down on their Seed investment to lead our Series A. We're extremely excited to welcome\\n\", _jsx(_components.a, {\n        href: \"https://www.linkedin.com/in/caryn-marooney/\",\n        children: \"Caryn Marooney\"\n      }), \" to our Board.\"]\n    }), \"\\n\", _jsxs(Quote, {\n      img: \"caryn-marooney.jpeg\",\n      caption: \"Caryn Marooney, Partner at Coatue.\",\n      children: [_jsx(\"p\", {\n        children: \"We are proud to lead a second consecutive round in Supabase and officially join the board.\"\n      }), _jsx(\"p\", {\n        children: _jsx(_components.p, {\n          children: \"We continue to be impressed by Paul and Ant and it has been a pleasure partnering with them. We\\nbelieve that the team has done an impressive job scaling their open-source community and think\\nthat the strong traction and adoption speaks for itself.\"\n        })\n      })]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"joining-the-round\",\n      children: \"Joining the round\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We've had a number of new investors join the round, this time a lot of operators and partners:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Elad Gill\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Tom Preston-Werner (GitHub cofounder)\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Solomon Hykes (Docker cofounder)\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Alex Solomon (PagerDuty cofounder)\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Guillermo Rauch (Vercel founder)\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Kurt Mackey (Fly cofounder)\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Chris Nguyen (LogDNA cofounder)\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Tod Sacerdoti (Pipedream Founder)\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Alana Anderson (Base Case Capital)\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Astasia Myers (Quiet Capital)\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"(and more)\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(\"a\", {\n      href: \"/company#investors\",\n      target: \"_blank\",\n      children: _jsx(_components.p, {\n        children: \"See all investors\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"building-supabase\",\n      children: \"Building Supabase\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Our early positioning is an \\\"open source Firebase alternative\\\" but you might be surprised to learn that this is a small part of the Supabase vision.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Firebase is arguably one of the best tools in the world for building \", _jsx(_components.em, {\n        children: \"new products\"\n      }), \", but it has a flaw: scalability.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"phase-1-start-with-scalability\",\n      children: \"Phase 1: Start with scalability\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The term \\\"open source Firebase alternative\\\" is becoming popular now, and we are often asked: how are we different?\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Simple. Supabase has smuggled scalability into the product, perhaps without you noticing.\\nEvery Supabase project is a \", _jsx(_components.em, {\n        children: \"full\"\n      }), \" Postgres database. It's not \\\"Postgres compatible\\\", it's not \\\"built on top of\\\",\\nand we don't abstract it. If we did, your applications would face the same scalability issues as Firebase.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Supabase simply makes Postgres easy to use - hopefully easier than any other database platform you've ever used.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We plan to make Postgres the default database for every developer in the world. How do we do that? By making Postgres both easy\\n(Phase 2: tooling) and scalable (Phase 3: Cloud-Native Postgres).\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"phase-2-postgres-tooling\",\n      children: \"Phase 2: Postgres Tooling\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"User-facing applications usually require tools beyond just a database and frontend framework:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"APIs:\"\n        }), \" to provide access to your database from untrusted systems.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Authentication:\"\n        }), \" for users sign ups.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"File Storage:\"\n        }), \" for large images and media.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Supabase makes all of this easy. Beyond the database we offer \", _jsx(_components.a, {\n        href: \"/docs/guides/auth\",\n        children: \"Auth\"\n      }), \",\\nRESTful and Realtime \", _jsx(_components.a, {\n        href: \"/docs/guides/database/api\",\n        children: \"APIs\"\n      }), \", a \", _jsx(_components.a, {\n        href: \"/docs/guides/storage\",\n        children: \"Storage\"\n      }), \"\\nsystem for large files, and a bunch of other tools - all wrapped up into a simple-to-use Dashboard.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/series-a/dashboard.png\",\n        alt: \"Supabase Dashboard\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"And this is where Supabase really differentiates itself: by doing less. All of these features build on top of PostgreSQL's\\nexisting functionality.\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Row Level Security:\"\n        }), \" restrict user access to data and files using Postgres Policies.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Realtime data streams:\"\n        }), \" subscribe to database changes using the logical replication stream.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Database Webhooks (previously called \\\"Function Hooks\\\"):\"\n        }), \" trigger external systems using Postgres triggers and our \", _jsx(_components.a, {\n          href: \"https://github.com/supabase/pg_net/\",\n          children: \"async request extension\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"RESTful APIs:\"\n        }), \" query your database \", _jsx(_components.a, {\n          href: \"/docs/reference/javascript/select\",\n          children: \"tables\"\n        }), \" and \", _jsx(_components.a, {\n          href: \"/docs/reference/javascript/rpc\",\n          children: \"functions\"\n        }), \" over HTTP, using \", _jsx(_components.a, {\n          href: \"https://postgrest.org\",\n          children: \"PostgREST\"\n        }), \".\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"phase-3-cloud-native-postgresql\",\n      children: \"Phase 3: Cloud-native PostgreSQL\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"PostgreSQL was created over 30 years ago, and it's one of the safest and most reliable databases in the world.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"But modern developers are becoming accustomed to cloud-native services with several characteristics:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Automatic scaling.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Low latency anywhere in the world.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Billing based on usage.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"PostgreSQL is still catching up to the modern cloud environment in some areas. What does a Cloud-native PostgreSQL look like?\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Branching:\"\n        }), \" developers should be able to \\\"fork\\\" a database at any point. This is particularly important for Jamstack developers who run deployments for every Git branch.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Scalable storage:\"\n        }), \" storage should grow and shrink without the user needing to provision more space themselves.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Distributed:\"\n        }), \" An entire fleet of databases, distributed around the world, should feel like a single database (and even better if all nodes can accept read/write workloads).\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Ephemeral compute:\"\n        }), \" developers don't want to be charged for a database which isn't doing anything. Likewise - if they are running huge computation then the database should scale up seamlessly, and scale down to zero when it's unused.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Snapshots and time-travel:\"\n        }), \" developers should be able to roll back to any point in time, or take a copy of their database with just a click of a button.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This is the future of Supabase - a platform to power your transactional workloads: no matter how big or small; no matter the use-case.\\nWe're building a cloud-native Postgres platform.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"join-us\",\n      children: \"Join us\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We have a track record of hiring open source maintainers, PostgREST is maintained by a\\n\", _jsx(_components.a, {\n        href: \"/blog/supabase-steve-chavez\",\n        children: \"Supabase developer\"\n      }), \", one of our first hires over a year ago.\\nWe've recently added \", _jsx(_components.a, {\n        href: \"https://www.postgresql.org/message-id/CABwTF4UZzYoHcaEGe2cESVn-e9dc3wzg%3Dmvx7Tz8qbKJ7p3UKA%40mail.gmail.com\",\n        children: \"another developer\"\n      }), \"\\nwith the explicit goal of working within the PostgreSQL community.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you want to help us build the future of cloud-native Postgres, join us:\\n\", _jsx(_components.a, {\n        href: \"https://about.supabase.com/careers/postgres-experts\",\n        children: \"https://about.supabase.com/careers/postgres-experts\"\n      })]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"get-started\",\n      children: \"Get started\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Start using Supabase today: \", _jsx(_components.strong, {\n          children: _jsx(_components.a, {\n            href: \"https://supabase.com/dashboard/\",\n            children: \"supabase.com/dashboard\"\n          })\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Make sure to \", _jsx(_components.strong, {\n          children: _jsx(_components.a, {\n            href: \"https://github.com/supabase/supabase\",\n            children: \"star us on GitHub\"\n          })\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Follow us \", _jsx(_components.strong, {\n          children: _jsx(_components.a, {\n            href: \"https://twitter.com/supabase\",\n            children: \"on Twitter\"\n          })\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Subscribe to our \", _jsx(_components.strong, {\n          children: _jsx(_components.a, {\n            href: \"https://www.youtube.com/c/supabase\",\n            children: \"YouTube channel\"\n          })\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Become a \", _jsx(_components.strong, {\n          children: _jsx(_components.a, {\n            href: \"https://github.com/sponsors/supabase\",\n            children: \"sponsor\"\n          })\n        })]\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"We're growing fast","slug":"were-growing-fast","lvl":2,"i":0,"seen":0},{"content":"Database Growth","slug":"database-growth","lvl":3,"i":1,"seen":0},{"content":"Developers","slug":"developers","lvl":3,"i":2,"seen":0},{"content":"Community","slug":"community","lvl":3,"i":3,"seen":0},{"content":"About the round","slug":"about-the-round","lvl":2,"i":4,"seen":0},{"content":"Joining the round","slug":"joining-the-round","lvl":3,"i":5,"seen":0},{"content":"Building Supabase","slug":"building-supabase","lvl":2,"i":6,"seen":0},{"content":"Phase 1: Start with scalability","slug":"phase-1-start-with-scalability","lvl":3,"i":7,"seen":0},{"content":"Phase 2: Postgres Tooling","slug":"phase-2-postgres-tooling","lvl":3,"i":8,"seen":0},{"content":"Phase 3: Cloud-native PostgreSQL","slug":"phase-3-cloud-native-postgresql","lvl":3,"i":9,"seen":0},{"content":"Join us","slug":"join-us","lvl":2,"i":10,"seen":0},{"content":"Get started","slug":"get-started","lvl":2,"i":11,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,3],"level":0},{"type":"inline","content":"Supabase [just raised $30M](https://techcrunch.com/2021/09/09/supabase-raises-30m-for-its-open-source-insta-backend/),\nbringing our total funding to $36M. How will we spend this? Read on to find out.","level":1,"lines":[1,3],"children":[{"type":"text","content":"Supabase ","level":0},{"type":"link_open","href":"https://techcrunch.com/2021/09/09/supabase-raises-30m-for-its-open-source-insta-backend/","title":"","level":0},{"type":"text","content":"just raised $30M","level":1},{"type":"link_close","level":0},{"type":"text","content":",","level":0},{"type":"softbreak","level":0},{"type":"text","content":"bringing our total funding to $36M. How will we spend this? Read on to find out.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[4,5],"level":0},{"type":"inline","content":"[We're growing fast](#were-growing-fast)","level":1,"lines":[4,5],"children":[{"type":"text","content":"We're growing fast","level":0}],"lvl":2,"i":0,"seen":0,"slug":"were-growing-fast"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[6,8],"level":0},{"type":"inline","content":"From the outside, you can see that we're growing fast.\nTo solidify some of the numbers with internal metrics:","level":1,"lines":[6,8],"children":[{"type":"text","content":"From the outside, you can see that we're growing fast.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"To solidify some of the numbers with internal metrics:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[9,10],"level":0},{"type":"inline","content":"[Database Growth](#database-growth)","level":1,"lines":[9,10],"children":[{"type":"text","content":"Database Growth","level":0}],"lvl":3,"i":1,"seen":0,"slug":"database-growth"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[11,13],"level":0},{"type":"inline","content":"As of September, we've launched over 50,000 databases. This is _only_ on our hosted platform - it doesn't include our open source offering,\nbecause we don't add telemetry. Active databases are growing 35% per month.","level":1,"lines":[11,13],"children":[{"type":"text","content":"As of September, we've launched over 50,000 databases. This is ","level":0},{"type":"em_open","level":0},{"type":"text","content":"only","level":1},{"type":"em_close","level":0},{"type":"text","content":" on our hosted platform - it doesn't include our open source offering,","level":0},{"type":"softbreak","level":0},{"type":"text","content":"because we don't add telemetry. Active databases are growing 35% per month.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[14,15],"level":0},{"type":"inline","content":"![Supabase has launched more than 50000 databases](/images/blog/series-a/total-databases.png)","level":1,"lines":[14,15],"children":[{"type":"image","src":"/images/blog/series-a/total-databases.png","title":"","alt":"Supabase has launched more than 50000 databases","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[16,17],"level":0},{"type":"inline","content":"[Developers](#developers)","level":1,"lines":[16,17],"children":[{"type":"text","content":"Developers","level":0}],"lvl":3,"i":2,"seen":0,"slug":"developers"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[18,22],"level":0},{"type":"inline","content":"As of September, over 40,000 developers have signed up to Supabase from some of the world's leading companies.\nBecause of the versatility of Postgres, they're building everything from\n[counter-fraud systems for Fintech](/blog/case-study-xendit) to\n[digital platforms powering 200,000 users](/blog/mobbin-supabase-200000-users).","level":1,"lines":[18,22],"children":[{"type":"text","content":"As of September, over 40,000 developers have signed up to Supabase from some of the world's leading companies.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Because of the versatility of Postgres, they're building everything from","level":0},{"type":"softbreak","level":0},{"type":"link_open","href":"/blog/case-study-xendit","title":"","level":0},{"type":"text","content":"counter-fraud systems for Fintech","level":1},{"type":"link_close","level":0},{"type":"text","content":" to","level":0},{"type":"softbreak","level":0},{"type":"link_open","href":"/blog/mobbin-supabase-200000-users","title":"","level":0},{"type":"text","content":"digital platforms powering 200,000 users","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[23,126],"level":0},{"type":"inline","content":"\u003cImageGrid\n  smCols={3}\n  mdCols={4}\n  lgCols={4}\n  images={[\n    {\n      name: 'wells-fargo',\n      image: '/images/company/companies-using-supabase/wells-fargo.png',\n    },\n    {\n      name: 'under-armour',\n      image: '/images/company/companies-using-supabase/under-armour.png',\n    },\n    {\n      name: 'audi-logo',\n      image: '/images/company/companies-using-supabase/audi-logo.png',\n    },\n    {\n      name: 'capitalone',\n      image: '/images/company/companies-using-supabase/capitalone.png',\n    },\n    {\n      name: 'coinbase',\n      image: '/images/company/companies-using-supabase/coinbase.png',\n    },\n    {\n      name: 'facebook',\n      image: '/images/company/companies-using-supabase/facebook.png',\n    },\n    {\n      name: 'github',\n      image: '/images/company/companies-using-supabase/github.png',\n    },\n    {\n      name: 'google',\n      image: '/images/company/companies-using-supabase/google.png',\n    },\n    {\n      name: 'gsk',\n      image: '/images/company/companies-using-supabase/gsk.png',\n    },\n    {\n      name: 'hewlett-packard',\n      image: '/images/company/companies-using-supabase/hewlett-packard.png',\n    },\n    {\n      name: 'hubspot',\n      image: '/images/company/companies-using-supabase/hubspot.png',\n    },\n    {\n      name: 'ibm',\n      image: '/images/company/companies-using-supabase/ibm.png',\n    },\n    {\n      name: 'instagram',\n      image: '/images/company/companies-using-supabase/instagram.png',\n    },\n    {\n      name: 'linkedin',\n      image: '/images/company/companies-using-supabase/linkedin.png',\n    },\n    {\n      name: 'microsoft',\n      image: '/images/company/companies-using-supabase/microsoft.png',\n    },\n    {\n      name: 'netflix',\n      image: '/images/company/companies-using-supabase/netflix.png',\n    },\n    {\n      name: 'notion',\n      image: '/images/company/companies-using-supabase/notion.png',\n    },\n    {\n      name: 'red-hat',\n      image: '/images/company/companies-using-supabase/red-hat.png',\n    },\n    {\n      name: 'robinhood',\n      image: '/images/company/companies-using-supabase/robinhood.png',\n    },\n    {\n      name: 'salesforce',\n      image: '/images/company/companies-using-supabase/salesforce.png',\n    },\n    {\n      name: 'santander',\n      image: '/images/company/companies-using-supabase/santander.png',\n    },\n    {\n      name: 'shopify',\n      image: '/images/company/companies-using-supabase/shopify.png',\n    },\n    {\n      name: 'squarespace',\n      image: '/images/company/companies-using-supabase/squarespace.png',\n    },\n    {\n      name: 'twitter',\n      image: '/images/company/companies-using-supabase/twitter.png',\n    },\n  ]}\n/\u003e","level":1,"lines":[23,126],"children":[{"type":"text","content":"\u003cImageGrid","level":0},{"type":"softbreak","level":0},{"type":"text","content":"smCols={3}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"mdCols={4}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"lgCols={4}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"images={[","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"name: 'wells-fargo',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"image: '/images/company/companies-using-supabase/wells-fargo.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"},","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"name: 'under-armour',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"image: '/images/company/companies-using-supabase/under-armour.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"},","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"name: 'audi-logo',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"image: '/images/company/companies-using-supabase/audi-logo.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"},","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"name: 'capitalone',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"image: '/images/company/companies-using-supabase/capitalone.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"},","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"name: 'coinbase',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"image: '/images/company/companies-using-supabase/coinbase.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"},","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"name: 'facebook',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"image: '/images/company/companies-using-supabase/facebook.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"},","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"name: 'github',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"image: '/images/company/companies-using-supabase/github.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"},","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"name: 'google',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"image: '/images/company/companies-using-supabase/google.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"},","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"name: 'gsk',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"image: '/images/company/companies-using-supabase/gsk.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"},","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"name: 'hewlett-packard',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"image: '/images/company/companies-using-supabase/hewlett-packard.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"},","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"name: 'hubspot',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"image: '/images/company/companies-using-supabase/hubspot.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"},","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"name: 'ibm',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"image: '/images/company/companies-using-supabase/ibm.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"},","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"name: 'instagram',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"image: '/images/company/companies-using-supabase/instagram.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"},","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"name: 'linkedin',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"image: '/images/company/companies-using-supabase/linkedin.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"},","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"name: 'microsoft',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"image: '/images/company/companies-using-supabase/microsoft.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"},","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"name: 'netflix',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"image: '/images/company/companies-using-supabase/netflix.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"},","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"name: 'notion',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"image: '/images/company/companies-using-supabase/notion.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"},","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"name: 'red-hat',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"image: '/images/company/companies-using-supabase/red-hat.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"},","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"name: 'robinhood',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"image: '/images/company/companies-using-supabase/robinhood.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"},","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"name: 'salesforce',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"image: '/images/company/companies-using-supabase/salesforce.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"},","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"name: 'santander',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"image: '/images/company/companies-using-supabase/santander.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"},","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"name: 'shopify',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"image: '/images/company/companies-using-supabase/shopify.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"},","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"name: 'squarespace',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"image: '/images/company/companies-using-supabase/squarespace.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"},","level":0},{"type":"softbreak","level":0},{"type":"text","content":"{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"name: 'twitter',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"image: '/images/company/companies-using-supabase/twitter.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"},","level":0},{"type":"softbreak","level":0},{"type":"text","content":"]}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[127,128],"level":0},{"type":"inline","content":"[Community](#community)","level":1,"lines":[127,128],"children":[{"type":"text","content":"Community","level":0}],"lvl":3,"i":3,"seen":0,"slug":"community"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[129,133],"level":0},{"type":"inline","content":"Supabase has been in GitHub's top-15 fastest growing open source startups for\n[five consecutive quarters](https://twitter.com/kiwicopple/status/1451104569266671619),\nand our [Discord](https://discord.supabase.com) has grown to nearly 4000 members since launching just 3 months ago.\nOur growth is all organic.","level":1,"lines":[129,133],"children":[{"type":"text","content":"Supabase has been in GitHub's top-15 fastest growing open source startups for","level":0},{"type":"softbreak","level":0},{"type":"link_open","href":"https://twitter.com/kiwicopple/status/1451104569266671619","title":"","level":0},{"type":"text","content":"five consecutive quarters","level":1},{"type":"link_close","level":0},{"type":"text","content":",","level":0},{"type":"softbreak","level":0},{"type":"text","content":"and our ","level":0},{"type":"link_open","href":"https://discord.supabase.com","title":"","level":0},{"type":"text","content":"Discord","level":1},{"type":"link_close","level":0},{"type":"text","content":" has grown to nearly 4000 members since launching just 3 months ago.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Our growth is all organic.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[134,135],"level":0},{"type":"inline","content":"![Supabase Discord](/images/blog/series-a/discord-supabase-community.png)","level":1,"lines":[134,135],"children":[{"type":"image","src":"/images/blog/series-a/discord-supabase-community.png","title":"","alt":"Supabase Discord","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[136,137],"level":0},{"type":"inline","content":"[About the round](#about-the-round)","level":1,"lines":[136,137],"children":[{"type":"text","content":"About the round","level":0}],"lvl":2,"i":4,"seen":0,"slug":"about-the-round"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[138,140],"level":0},{"type":"inline","content":"Supabase is an ambitious project. Firebase, to their credit, is a very well-developed platform which offers _a lot_ of functionality.\nBuilding an alternative is a bit like launching five different startups at once.","level":1,"lines":[138,140],"children":[{"type":"text","content":"Supabase is an ambitious project. Firebase, to their credit, is a very well-developed platform which offers ","level":0},{"type":"em_open","level":0},{"type":"text","content":"a lot","level":1},{"type":"em_close","level":0},{"type":"text","content":" of functionality.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Building an alternative is a bit like launching five different startups at once.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[141,147],"level":0},{"type":"inline","content":"That said, our approach has been clear from the start: we don't want to re-invent the wheel.\nWe want to leverage existing open source products wherever we can, improving them by up-streaming changes and employing maintainers\n([PostgREST](/blog/supabase-steve-chavez),\n[PostgreSQL](https://www.postgresql.org/message-id/CABwTF4UZzYoHcaEGe2cESVn-e9dc3wzg%3Dmvx7Tz8qbKJ7p3UKA%40mail.gmail.com)).\nThis model is the real promise of open source, and we've been lucky to have investors who have backed our approach\nsince the start - Y Combinator, Mozilla, and our lead investor, Coatue.","level":1,"lines":[141,147],"children":[{"type":"text","content":"That said, our approach has been clear from the start: we don't want to re-invent the wheel.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"We want to leverage existing open source products wherever we can, improving them by up-streaming changes and employing maintainers","level":0},{"type":"softbreak","level":0},{"type":"text","content":"(","level":0},{"type":"link_open","href":"/blog/supabase-steve-chavez","title":"","level":0},{"type":"text","content":"PostgREST","level":1},{"type":"link_close","level":0},{"type":"text","content":",","level":0},{"type":"softbreak","level":0},{"type":"link_open","href":"https://www.postgresql.org/message-id/CABwTF4UZzYoHcaEGe2cESVn-e9dc3wzg%3Dmvx7Tz8qbKJ7p3UKA%40mail.gmail.com","title":"","level":0},{"type":"text","content":"PostgreSQL","level":1},{"type":"link_close","level":0},{"type":"text","content":").","level":0},{"type":"softbreak","level":0},{"type":"text","content":"This model is the real promise of open source, and we've been lucky to have investors who have backed our approach","level":0},{"type":"softbreak","level":0},{"type":"text","content":"since the start - Y Combinator, Mozilla, and our lead investor, Coatue.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[148,150],"level":0},{"type":"inline","content":"Because of their conviction in Supabase, Coatue doubled down on their Seed investment to lead our Series A. We're extremely excited to welcome\n[Caryn Marooney](https://www.linkedin.com/in/caryn-marooney/) to our Board.","level":1,"lines":[148,150],"children":[{"type":"text","content":"Because of their conviction in Supabase, Coatue doubled down on their Seed investment to lead our Series A. We're extremely excited to welcome","level":0},{"type":"softbreak","level":0},{"type":"link_open","href":"https://www.linkedin.com/in/caryn-marooney/","title":"","level":0},{"type":"text","content":"Caryn Marooney","level":1},{"type":"link_close","level":0},{"type":"text","content":" to our Board.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[151,159],"level":0},{"type":"inline","content":"\u003cQuote img=\"caryn-marooney.jpeg\" caption=\"Caryn Marooney, Partner at Coatue.\"\u003e\n  \u003cp\u003eWe are proud to lead a second consecutive round in Supabase and officially join the board.\u003c/p\u003e\n  \u003cp\u003e\n    We continue to be impressed by Paul and Ant and it has been a pleasure partnering with them. We\n    believe that the team has done an impressive job scaling their open-source community and think\n    that the strong traction and adoption speaks for itself.\n  \u003c/p\u003e\n\u003c/Quote\u003e","level":1,"lines":[151,159],"children":[{"type":"text","content":"\u003cQuote img=\"caryn-marooney.jpeg\" caption=\"Caryn Marooney, Partner at Coatue.\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cp\u003eWe are proud to lead a second consecutive round in Supabase and officially join the board.\u003c/p\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cp\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"We continue to be impressed by Paul and Ant and it has been a pleasure partnering with them. We","level":0},{"type":"softbreak","level":0},{"type":"text","content":"believe that the team has done an impressive job scaling their open-source community and think","level":0},{"type":"softbreak","level":0},{"type":"text","content":"that the strong traction and adoption speaks for itself.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/p\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/Quote\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[160,161],"level":0},{"type":"inline","content":"[Joining the round](#joining-the-round)","level":1,"lines":[160,161],"children":[{"type":"text","content":"Joining the round","level":0}],"lvl":3,"i":5,"seen":0,"slug":"joining-the-round"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[162,163],"level":0},{"type":"inline","content":"We've had a number of new investors join the round, this time a lot of operators and partners:","level":1,"lines":[162,163],"children":[{"type":"text","content":"We've had a number of new investors join the round, this time a lot of operators and partners:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[164,176],"level":0},{"type":"list_item_open","lines":[164,165],"level":1},{"type":"paragraph_open","tight":true,"lines":[164,165],"level":2},{"type":"inline","content":"Elad Gill","level":3,"lines":[164,165],"children":[{"type":"text","content":"Elad Gill","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[165,166],"level":1},{"type":"paragraph_open","tight":true,"lines":[165,166],"level":2},{"type":"inline","content":"Tom Preston-Werner (GitHub cofounder)","level":3,"lines":[165,166],"children":[{"type":"text","content":"Tom Preston-Werner (GitHub cofounder)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[166,167],"level":1},{"type":"paragraph_open","tight":true,"lines":[166,167],"level":2},{"type":"inline","content":"Solomon Hykes (Docker cofounder)","level":3,"lines":[166,167],"children":[{"type":"text","content":"Solomon Hykes (Docker cofounder)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[167,168],"level":1},{"type":"paragraph_open","tight":true,"lines":[167,168],"level":2},{"type":"inline","content":"Alex Solomon (PagerDuty cofounder)","level":3,"lines":[167,168],"children":[{"type":"text","content":"Alex Solomon (PagerDuty cofounder)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[168,169],"level":1},{"type":"paragraph_open","tight":true,"lines":[168,169],"level":2},{"type":"inline","content":"Guillermo Rauch (Vercel founder)","level":3,"lines":[168,169],"children":[{"type":"text","content":"Guillermo Rauch (Vercel founder)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[169,170],"level":1},{"type":"paragraph_open","tight":true,"lines":[169,170],"level":2},{"type":"inline","content":"Kurt Mackey (Fly cofounder)","level":3,"lines":[169,170],"children":[{"type":"text","content":"Kurt Mackey (Fly cofounder)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[170,171],"level":1},{"type":"paragraph_open","tight":true,"lines":[170,171],"level":2},{"type":"inline","content":"Chris Nguyen (LogDNA cofounder)","level":3,"lines":[170,171],"children":[{"type":"text","content":"Chris Nguyen (LogDNA cofounder)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[171,172],"level":1},{"type":"paragraph_open","tight":true,"lines":[171,172],"level":2},{"type":"inline","content":"Tod Sacerdoti (Pipedream Founder)","level":3,"lines":[171,172],"children":[{"type":"text","content":"Tod Sacerdoti (Pipedream Founder)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[172,173],"level":1},{"type":"paragraph_open","tight":true,"lines":[172,173],"level":2},{"type":"inline","content":"Alana Anderson (Base Case Capital)","level":3,"lines":[172,173],"children":[{"type":"text","content":"Alana Anderson (Base Case Capital)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[173,174],"level":1},{"type":"paragraph_open","tight":true,"lines":[173,174],"level":2},{"type":"inline","content":"Astasia Myers (Quiet Capital)","level":3,"lines":[173,174],"children":[{"type":"text","content":"Astasia Myers (Quiet Capital)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[174,176],"level":1},{"type":"paragraph_open","tight":true,"lines":[174,175],"level":2},{"type":"inline","content":"(and more)","level":3,"lines":[174,175],"children":[{"type":"text","content":"(and more)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[176,179],"level":0},{"type":"inline","content":"\u003ca href=\"/company#investors\" target=\"_blank\"\u003e\n  See all investors\n\u003c/a\u003e","level":1,"lines":[176,179],"children":[{"type":"text","content":"\u003ca href=\"/company#investors\" target=\"_blank\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"See all investors","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/a\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[180,181],"level":0},{"type":"inline","content":"[Building Supabase](#building-supabase)","level":1,"lines":[180,181],"children":[{"type":"text","content":"Building Supabase","level":0}],"lvl":2,"i":6,"seen":0,"slug":"building-supabase"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[182,183],"level":0},{"type":"inline","content":"Our early positioning is an \"open source Firebase alternative\" but you might be surprised to learn that this is a small part of the Supabase vision.","level":1,"lines":[182,183],"children":[{"type":"text","content":"Our early positioning is an \"open source Firebase alternative\" but you might be surprised to learn that this is a small part of the Supabase vision.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[184,185],"level":0},{"type":"inline","content":"Firebase is arguably one of the best tools in the world for building _new products_, but it has a flaw: scalability.","level":1,"lines":[184,185],"children":[{"type":"text","content":"Firebase is arguably one of the best tools in the world for building ","level":0},{"type":"em_open","level":0},{"type":"text","content":"new products","level":1},{"type":"em_close","level":0},{"type":"text","content":", but it has a flaw: scalability.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[186,187],"level":0},{"type":"inline","content":"[Phase 1: Start with scalability](#phase-1-start-with-scalability)","level":1,"lines":[186,187],"children":[{"type":"text","content":"Phase 1: Start with scalability","level":0}],"lvl":3,"i":7,"seen":0,"slug":"phase-1-start-with-scalability"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[188,189],"level":0},{"type":"inline","content":"The term \"open source Firebase alternative\" is becoming popular now, and we are often asked: how are we different?","level":1,"lines":[188,189],"children":[{"type":"text","content":"The term \"open source Firebase alternative\" is becoming popular now, and we are often asked: how are we different?","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[190,193],"level":0},{"type":"inline","content":"Simple. Supabase has smuggled scalability into the product, perhaps without you noticing.\nEvery Supabase project is a _full_ Postgres database. It's not \"Postgres compatible\", it's not \"built on top of\",\nand we don't abstract it. If we did, your applications would face the same scalability issues as Firebase.","level":1,"lines":[190,193],"children":[{"type":"text","content":"Simple. Supabase has smuggled scalability into the product, perhaps without you noticing.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"Every Supabase project is a ","level":0},{"type":"em_open","level":0},{"type":"text","content":"full","level":1},{"type":"em_close","level":0},{"type":"text","content":" Postgres database. It's not \"Postgres compatible\", it's not \"built on top of\",","level":0},{"type":"softbreak","level":0},{"type":"text","content":"and we don't abstract it. If we did, your applications would face the same scalability issues as Firebase.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[194,195],"level":0},{"type":"inline","content":"Supabase simply makes Postgres easy to use - hopefully easier than any other database platform you've ever used.","level":1,"lines":[194,195],"children":[{"type":"text","content":"Supabase simply makes Postgres easy to use - hopefully easier than any other database platform you've ever used.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[196,198],"level":0},{"type":"inline","content":"We plan to make Postgres the default database for every developer in the world. How do we do that? By making Postgres both easy\n(Phase 2: tooling) and scalable (Phase 3: Cloud-Native Postgres).","level":1,"lines":[196,198],"children":[{"type":"text","content":"We plan to make Postgres the default database for every developer in the world. How do we do that? By making Postgres both easy","level":0},{"type":"softbreak","level":0},{"type":"text","content":"(Phase 2: tooling) and scalable (Phase 3: Cloud-Native Postgres).","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[199,200],"level":0},{"type":"inline","content":"[Phase 2: Postgres Tooling](#phase-2-postgres-tooling)","level":1,"lines":[199,200],"children":[{"type":"text","content":"Phase 2: Postgres Tooling","level":0}],"lvl":3,"i":8,"seen":0,"slug":"phase-2-postgres-tooling"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[201,202],"level":0},{"type":"inline","content":"User-facing applications usually require tools beyond just a database and frontend framework:","level":1,"lines":[201,202],"children":[{"type":"text","content":"User-facing applications usually require tools beyond just a database and frontend framework:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[203,207],"level":0},{"type":"list_item_open","lines":[203,204],"level":1},{"type":"paragraph_open","tight":true,"lines":[203,204],"level":2},{"type":"inline","content":"**APIs:** to provide access to your database from untrusted systems.","level":3,"lines":[203,204],"children":[{"type":"strong_open","level":0},{"type":"text","content":"APIs:","level":1},{"type":"strong_close","level":0},{"type":"text","content":" to provide access to your database from untrusted systems.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[204,205],"level":1},{"type":"paragraph_open","tight":true,"lines":[204,205],"level":2},{"type":"inline","content":"**Authentication:** for users sign ups.","level":3,"lines":[204,205],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Authentication:","level":1},{"type":"strong_close","level":0},{"type":"text","content":" for users sign ups.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[205,207],"level":1},{"type":"paragraph_open","tight":true,"lines":[205,206],"level":2},{"type":"inline","content":"**File Storage:** for large images and media.","level":3,"lines":[205,206],"children":[{"type":"strong_open","level":0},{"type":"text","content":"File Storage:","level":1},{"type":"strong_close","level":0},{"type":"text","content":" for large images and media.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[207,210],"level":0},{"type":"inline","content":"Supabase makes all of this easy. Beyond the database we offer [Auth](/docs/guides/auth),\nRESTful and Realtime [APIs](/docs/guides/database/api), a [Storage](/docs/guides/storage)\nsystem for large files, and a bunch of other tools - all wrapped up into a simple-to-use Dashboard.","level":1,"lines":[207,210],"children":[{"type":"text","content":"Supabase makes all of this easy. Beyond the database we offer ","level":0},{"type":"link_open","href":"/docs/guides/auth","title":"","level":0},{"type":"text","content":"Auth","level":1},{"type":"link_close","level":0},{"type":"text","content":",","level":0},{"type":"softbreak","level":0},{"type":"text","content":"RESTful and Realtime ","level":0},{"type":"link_open","href":"/docs/guides/database/api","title":"","level":0},{"type":"text","content":"APIs","level":1},{"type":"link_close","level":0},{"type":"text","content":", a ","level":0},{"type":"link_open","href":"/docs/guides/storage","title":"","level":0},{"type":"text","content":"Storage","level":1},{"type":"link_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"system for large files, and a bunch of other tools - all wrapped up into a simple-to-use Dashboard.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[211,212],"level":0},{"type":"inline","content":"![Supabase Dashboard](/images/blog/series-a/dashboard.png)","level":1,"lines":[211,212],"children":[{"type":"image","src":"/images/blog/series-a/dashboard.png","title":"","alt":"Supabase Dashboard","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[213,215],"level":0},{"type":"inline","content":"And this is where Supabase really differentiates itself: by doing less. All of these features build on top of PostgreSQL's\nexisting functionality.","level":1,"lines":[213,215],"children":[{"type":"text","content":"And this is where Supabase really differentiates itself: by doing less. All of these features build on top of PostgreSQL's","level":0},{"type":"softbreak","level":0},{"type":"text","content":"existing functionality.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[216,221],"level":0},{"type":"list_item_open","lines":[216,217],"level":1},{"type":"paragraph_open","tight":true,"lines":[216,217],"level":2},{"type":"inline","content":"**Row Level Security:** restrict user access to data and files using Postgres Policies.","level":3,"lines":[216,217],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Row Level Security:","level":1},{"type":"strong_close","level":0},{"type":"text","content":" restrict user access to data and files using Postgres Policies.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[217,218],"level":1},{"type":"paragraph_open","tight":true,"lines":[217,218],"level":2},{"type":"inline","content":"**Realtime data streams:** subscribe to database changes using the logical replication stream.","level":3,"lines":[217,218],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Realtime data streams:","level":1},{"type":"strong_close","level":0},{"type":"text","content":" subscribe to database changes using the logical replication stream.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[218,219],"level":1},{"type":"paragraph_open","tight":true,"lines":[218,219],"level":2},{"type":"inline","content":"**Database Webhooks (previously called \"Function Hooks\"):** trigger external systems using Postgres triggers and our [async request extension](https://github.com/supabase/pg_net/).","level":3,"lines":[218,219],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Database Webhooks (previously called \"Function Hooks\"):","level":1},{"type":"strong_close","level":0},{"type":"text","content":" trigger external systems using Postgres triggers and our ","level":0},{"type":"link_open","href":"https://github.com/supabase/pg_net/","title":"","level":0},{"type":"text","content":"async request extension","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[219,221],"level":1},{"type":"paragraph_open","tight":true,"lines":[219,220],"level":2},{"type":"inline","content":"**RESTful APIs:** query your database [tables](/docs/reference/javascript/select) and [functions](/docs/reference/javascript/rpc) over HTTP, using [PostgREST](https://postgrest.org).","level":3,"lines":[219,220],"children":[{"type":"strong_open","level":0},{"type":"text","content":"RESTful APIs:","level":1},{"type":"strong_close","level":0},{"type":"text","content":" query your database ","level":0},{"type":"link_open","href":"/docs/reference/javascript/select","title":"","level":0},{"type":"text","content":"tables","level":1},{"type":"link_close","level":0},{"type":"text","content":" and ","level":0},{"type":"link_open","href":"/docs/reference/javascript/rpc","title":"","level":0},{"type":"text","content":"functions","level":1},{"type":"link_close","level":0},{"type":"text","content":" over HTTP, using ","level":0},{"type":"link_open","href":"https://postgrest.org","title":"","level":0},{"type":"text","content":"PostgREST","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":3,"lines":[221,222],"level":0},{"type":"inline","content":"[Phase 3: Cloud-native PostgreSQL](#phase-3-cloud-native-postgresql)","level":1,"lines":[221,222],"children":[{"type":"text","content":"Phase 3: Cloud-native PostgreSQL","level":0}],"lvl":3,"i":9,"seen":0,"slug":"phase-3-cloud-native-postgresql"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[223,224],"level":0},{"type":"inline","content":"PostgreSQL was created over 30 years ago, and it's one of the safest and most reliable databases in the world.","level":1,"lines":[223,224],"children":[{"type":"text","content":"PostgreSQL was created over 30 years ago, and it's one of the safest and most reliable databases in the world.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[225,226],"level":0},{"type":"inline","content":"But modern developers are becoming accustomed to cloud-native services with several characteristics:","level":1,"lines":[225,226],"children":[{"type":"text","content":"But modern developers are becoming accustomed to cloud-native services with several characteristics:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[227,231],"level":0},{"type":"list_item_open","lines":[227,228],"level":1},{"type":"paragraph_open","tight":true,"lines":[227,228],"level":2},{"type":"inline","content":"Automatic scaling.","level":3,"lines":[227,228],"children":[{"type":"text","content":"Automatic scaling.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[228,229],"level":1},{"type":"paragraph_open","tight":true,"lines":[228,229],"level":2},{"type":"inline","content":"Low latency anywhere in the world.","level":3,"lines":[228,229],"children":[{"type":"text","content":"Low latency anywhere in the world.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[229,231],"level":1},{"type":"paragraph_open","tight":true,"lines":[229,230],"level":2},{"type":"inline","content":"Billing based on usage.","level":3,"lines":[229,230],"children":[{"type":"text","content":"Billing based on usage.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[231,232],"level":0},{"type":"inline","content":"PostgreSQL is still catching up to the modern cloud environment in some areas. What does a Cloud-native PostgreSQL look like?","level":1,"lines":[231,232],"children":[{"type":"text","content":"PostgreSQL is still catching up to the modern cloud environment in some areas. What does a Cloud-native PostgreSQL look like?","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[233,239],"level":0},{"type":"list_item_open","lines":[233,234],"level":1},{"type":"paragraph_open","tight":true,"lines":[233,234],"level":2},{"type":"inline","content":"**Branching:** developers should be able to \"fork\" a database at any point. This is particularly important for Jamstack developers who run deployments for every Git branch.","level":3,"lines":[233,234],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Branching:","level":1},{"type":"strong_close","level":0},{"type":"text","content":" developers should be able to \"fork\" a database at any point. This is particularly important for Jamstack developers who run deployments for every Git branch.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[234,235],"level":1},{"type":"paragraph_open","tight":true,"lines":[234,235],"level":2},{"type":"inline","content":"**Scalable storage:** storage should grow and shrink without the user needing to provision more space themselves.","level":3,"lines":[234,235],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Scalable storage:","level":1},{"type":"strong_close","level":0},{"type":"text","content":" storage should grow and shrink without the user needing to provision more space themselves.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[235,236],"level":1},{"type":"paragraph_open","tight":true,"lines":[235,236],"level":2},{"type":"inline","content":"**Distributed:** An entire fleet of databases, distributed around the world, should feel like a single database (and even better if all nodes can accept read/write workloads).","level":3,"lines":[235,236],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Distributed:","level":1},{"type":"strong_close","level":0},{"type":"text","content":" An entire fleet of databases, distributed around the world, should feel like a single database (and even better if all nodes can accept read/write workloads).","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[236,237],"level":1},{"type":"paragraph_open","tight":true,"lines":[236,237],"level":2},{"type":"inline","content":"**Ephemeral compute:** developers don't want to be charged for a database which isn't doing anything. Likewise - if they are running huge computation then the database should scale up seamlessly, and scale down to zero when it's unused.","level":3,"lines":[236,237],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Ephemeral compute:","level":1},{"type":"strong_close","level":0},{"type":"text","content":" developers don't want to be charged for a database which isn't doing anything. Likewise - if they are running huge computation then the database should scale up seamlessly, and scale down to zero when it's unused.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[237,239],"level":1},{"type":"paragraph_open","tight":true,"lines":[237,238],"level":2},{"type":"inline","content":"**Snapshots and time-travel:** developers should be able to roll back to any point in time, or take a copy of their database with just a click of a button.","level":3,"lines":[237,238],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Snapshots and time-travel:","level":1},{"type":"strong_close","level":0},{"type":"text","content":" developers should be able to roll back to any point in time, or take a copy of their database with just a click of a button.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[239,241],"level":0},{"type":"inline","content":"This is the future of Supabase - a platform to power your transactional workloads: no matter how big or small; no matter the use-case.\nWe're building a cloud-native Postgres platform.","level":1,"lines":[239,241],"children":[{"type":"text","content":"This is the future of Supabase - a platform to power your transactional workloads: no matter how big or small; no matter the use-case.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"We're building a cloud-native Postgres platform.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[242,243],"level":0},{"type":"inline","content":"[Join us](#join-us)","level":1,"lines":[242,243],"children":[{"type":"text","content":"Join us","level":0}],"lvl":2,"i":10,"seen":0,"slug":"join-us"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[244,248],"level":0},{"type":"inline","content":"We have a track record of hiring open source maintainers, PostgREST is maintained by a\n[Supabase developer](/blog/supabase-steve-chavez), one of our first hires over a year ago.\nWe've recently added [another developer](https://www.postgresql.org/message-id/CABwTF4UZzYoHcaEGe2cESVn-e9dc3wzg%3Dmvx7Tz8qbKJ7p3UKA%40mail.gmail.com)\nwith the explicit goal of working within the PostgreSQL community.","level":1,"lines":[244,248],"children":[{"type":"text","content":"We have a track record of hiring open source maintainers, PostgREST is maintained by a","level":0},{"type":"softbreak","level":0},{"type":"link_open","href":"/blog/supabase-steve-chavez","title":"","level":0},{"type":"text","content":"Supabase developer","level":1},{"type":"link_close","level":0},{"type":"text","content":", one of our first hires over a year ago.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"We've recently added ","level":0},{"type":"link_open","href":"https://www.postgresql.org/message-id/CABwTF4UZzYoHcaEGe2cESVn-e9dc3wzg%3Dmvx7Tz8qbKJ7p3UKA%40mail.gmail.com","title":"","level":0},{"type":"text","content":"another developer","level":1},{"type":"link_close","level":0},{"type":"softbreak","level":0},{"type":"text","content":"with the explicit goal of working within the PostgreSQL community.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[249,251],"level":0},{"type":"inline","content":"If you want to help us build the future of cloud-native Postgres, join us:\n[https://about.supabase.com/careers/postgres-experts](https://about.supabase.com/careers/postgres-experts)","level":1,"lines":[249,251],"children":[{"type":"text","content":"If you want to help us build the future of cloud-native Postgres, join us:","level":0},{"type":"softbreak","level":0},{"type":"link_open","href":"https://about.supabase.com/careers/postgres-experts","title":"","level":0},{"type":"text","content":"https://about.supabase.com/careers/postgres-experts","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[252,253],"level":0},{"type":"inline","content":"[Get started](#get-started)","level":1,"lines":[252,253],"children":[{"type":"text","content":"Get started","level":0}],"lvl":2,"i":11,"seen":0,"slug":"get-started"},{"type":"heading_close","hLevel":2,"level":0},{"type":"bullet_list_open","lines":[254,259],"level":0},{"type":"list_item_open","lines":[254,255],"level":1},{"type":"paragraph_open","tight":true,"lines":[254,255],"level":2},{"type":"inline","content":"Start using Supabase today: **[supabase.com/dashboard](https://supabase.com/dashboard/)**","level":3,"lines":[254,255],"children":[{"type":"text","content":"Start using Supabase today: ","level":0},{"type":"strong_open","level":0},{"type":"link_open","href":"https://supabase.com/dashboard/","title":"","level":1},{"type":"text","content":"supabase.com/dashboard","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[255,256],"level":1},{"type":"paragraph_open","tight":true,"lines":[255,256],"level":2},{"type":"inline","content":"Make sure to **[star us on GitHub](https://github.com/supabase/supabase)**","level":3,"lines":[255,256],"children":[{"type":"text","content":"Make sure to ","level":0},{"type":"strong_open","level":0},{"type":"link_open","href":"https://github.com/supabase/supabase","title":"","level":1},{"type":"text","content":"star us on GitHub","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[256,257],"level":1},{"type":"paragraph_open","tight":true,"lines":[256,257],"level":2},{"type":"inline","content":"Follow us **[on Twitter](https://twitter.com/supabase)**","level":3,"lines":[256,257],"children":[{"type":"text","content":"Follow us ","level":0},{"type":"strong_open","level":0},{"type":"link_open","href":"https://twitter.com/supabase","title":"","level":1},{"type":"text","content":"on Twitter","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[257,258],"level":1},{"type":"paragraph_open","tight":true,"lines":[257,258],"level":2},{"type":"inline","content":"Subscribe to our **[YouTube channel](https://www.youtube.com/c/supabase)**","level":3,"lines":[257,258],"children":[{"type":"text","content":"Subscribe to our ","level":0},{"type":"strong_open","level":0},{"type":"link_open","href":"https://www.youtube.com/c/supabase","title":"","level":1},{"type":"text","content":"YouTube channel","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[258,259],"level":1},{"type":"paragraph_open","tight":true,"lines":[258,259],"level":2},{"type":"inline","content":"Become a **[sponsor](https://github.com/sponsors/supabase)**","level":3,"lines":[258,259],"children":[{"type":"text","content":"Become a ","level":0},{"type":"strong_open","level":0},{"type":"link_open","href":"https://github.com/sponsors/supabase","title":"","level":1},{"type":"text","content":"sponsor","level":2},{"type":"link_close","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0}],"content":"- [We're growing fast](#were-growing-fast)\n  * [Database Growth](#database-growth)\n  * [Developers](#developers)\n  * [Community](#community)\n- [About the round](#about-the-round)\n  * [Joining the round](#joining-the-round)\n- [Building Supabase](#building-supabase)\n  * [Phase 1: Start with scalability](#phase-1-start-with-scalability)\n  * [Phase 2: Postgres Tooling](#phase-2-postgres-tooling)\n  * [Phase 3: Cloud-native PostgreSQL](#phase-3-cloud-native-postgresql)\n- [Join us](#join-us)\n- [Get started](#get-started)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-series-a"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>