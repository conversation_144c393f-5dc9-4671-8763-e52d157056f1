<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supavisor 1.0: a scalable connection pooler for Postgres</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Supavisor is now used across all projects, providing a scalable and cloud-native Postgres connection pooler that can handle millions of connections" data-next-head=""/><meta property="og:title" content="Supavisor 1.0: a scalable connection pooler for Postgres" data-next-head=""/><meta property="og:description" content="Supavisor is now used across all projects, providing a scalable and cloud-native Postgres connection pooler that can handle millions of connections" data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supavisor-postgres-connection-pooler" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2023-12-13" data-next-head=""/><meta property="article:author" content="https://github.com/abc3" data-next-head=""/><meta property="article:tag" content="launch-week" data-next-head=""/><meta property="article:tag" content="supavisor" data-next-head=""/><meta property="article:tag" content="postgres" data-next-head=""/><meta property="article:tag" content="planetpg" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/lwx-supavisor/supavisor-og.png" data-next-head=""/><meta property="og:image:alt" content="Supavisor 1.0: a scalable connection pooler for Postgres thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Supavisor 1.0: a scalable connection pooler for Postgres</h1><div class="text-light flex space-x-3 text-sm"><p>13 Dec 2023</p><p>•</p><p>7 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/abc3"><div class="flex items-center gap-3"><div class="w-10"><img alt="Stanislav Muzhyk avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fabc3.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fabc3.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fabc3.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Stanislav Muzhyk</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Supavisor 1.0: a scalable connection pooler for Postgres" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flwx-supavisor%2Fsupavisor-thumb.png&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flwx-supavisor%2Fsupavisor-thumb.png&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flwx-supavisor%2Fsupavisor-thumb.png&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flwx-supavisor%2Fsupavisor-thumb.png&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flwx-supavisor%2Fsupavisor-thumb.png&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flwx-supavisor%2Fsupavisor-thumb.png&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flwx-supavisor%2Fsupavisor-thumb.png&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flwx-supavisor%2Fsupavisor-thumb.png&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flwx-supavisor%2Fsupavisor-thumb.png&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flwx-supavisor%2Fsupavisor-thumb.png&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flwx-supavisor%2Fsupavisor-thumb.png&amp;w=3840&amp;q=100"/></div><p>After <a href="https://supabase.com/blog/supavisor-1-million">launching Supavisor in August</a>, we&#x27;ve successfully migrated all projects on the platform. Every new Supabase project launched now gets a Supavisor connection string to use for connection pooling.</p>
<p>Supavisor 1.0 symbolizes production readiness and comes with many bug fixes. It includes three important features:</p>
<ol>
<li>query load balancing</li>
<li>named prepared statement support</li>
<li>query cancelation</li>
</ol>
<h2 id="what-is-connection-pooling" class="group scroll-mt-24">What is connection pooling?<a href="#what-is-connection-pooling" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Supavisor is built with Elixir. Since the <a href="https://dashbit.co/">Dashbit</a> team have been helping with the development we invited Jose Valim, the creator of Elixir, to explain connection pooling, OTP, and why Elixir is a great fit for a connection pooler:</p>
<div class="video-container"><iframe class="w-full" src="https://www.youtube-nocookie.com/embed/ogYNmJOFEpk" title="YouTube video player" allow="accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share"></iframe></div>
<h2 id="sql-parsing-with-rust" class="group scroll-mt-24">SQL parsing with Rust<a href="#sql-parsing-with-rust" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>To implement the latest set of features, we now parse all SQL statements from connected clients.</p>
<p>Supavisor, developed in Elixir, supports high concurrency and rapid I/O. Elixir doesn&#x27;t have great performance for parsing, but it provides excellent interop with Rust via <a href="https://github.com/rusterlium/rustler">Rustler</a>. For efficient SQL parsing, we use <a href="https://github.com/pganalyze/pg_query.rs"><code class="short-inline-codeblock">pg_query.rs</code></a>.</p>
<h2 id="load-balancing" class="group scroll-mt-24">Load Balancing<a href="#load-balancing" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>When set up with a Postgres cluster, Supavisor load-balances read requests between the primary server and its replicas. It randomly distributes these read requests across the entire Postgres cluster.</p>
<p>Supavisor targets write operations to the primary automatically by probing read replicas until it hits the primary with a successful write, <a href="https://www.percona.com/blog/seamless-application-failover-using-libpq-features-in-postgresql/">similar to libpq</a>. The trade-off here is that writes may take a few milliseconds longer to complete in favor of zero additional client-side complexity. This write strategy also makes transparent primary failovers painless because detecting the primary for writes is automatic.</p>
<h3 id="read-after-writes" class="group scroll-mt-24">Read-after-writes<a href="#read-after-writes" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>With automatic primary detection, it&#x27;s easy to guarantee read-after-writes from the same client by wrapping the read and write in a transaction.</p>
<p>Future work is planned to allow custom server targeting with SQL statements such as <code class="short-inline-codeblock">SET SERVER &#x27;primary&#x27;</code> to let clients guarantee read-after-writes outside of transactions or across clients.</p>
<h2 id="named-prepared-statements" class="group scroll-mt-24">Named Prepared Statements<a href="#named-prepared-statements" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Many clients use named <a href="https://www.postgresql.org/docs/current/sql-prepare.html">prepared statements</a> when generating parameterized SQL. During statement preparation Postgres parses, plans, and optimizes queries.</p>
<p>If a client can create named prepared statements, then such a client can re-use these query plans and simply submit parameters for them.</p>
<p>The problem with named prepared statements and pooling in the transaction mode is that statements are not shared across Postgres backends (connections). Each client connection must issue prepared statements for each query they will run.</p>
<p>Supavisor now supports named prepared statements. Supavisor parses each query and identifies <code class="short-inline-codeblock">PREPARE</code> statements. When a <code class="short-inline-codeblock">PREPARE</code> statement is received on one connection, it is broadcast across all connections. This approach allows every client to access named prepared statements that have been issued by other connections. This adds a slight increase in memory overhead when duplicating query plans for each Postgres connection but should come with significant throughput gains.</p>
<!-- -->
<h2 id="query-cancelation" class="group scroll-mt-24">Query Cancelation<a href="#query-cancelation" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>With 1.0 we get query official cancelation as well. If you&#x27;re in <code class="short-inline-codeblock">psql</code> typing <code class="short-inline-codeblock">Ctrl+C</code> will actually cancel your query now.</p>
<p>Especially useful if you accidentally run a heavy query!</p>
<h2 id="platform-updates" class="group scroll-mt-24">Platform Updates<a href="#platform-updates" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>For the Supavisor rollout, we maintained consistent pooling settings between PgBouncer and Supavisor.</p>
<p>Now, we&#x27;re raising the client connection limit for smaller projects in Supavisor. Here are the updated default configurations:</p>
<table><thead><tr><th>Database Size</th><th>default_pool_size</th><th>max_connections</th><th>default_max_clients</th></tr></thead><tbody><tr><td>Micro</td><td>15</td><td>60</td><td>200</td></tr><tr><td>Small</td><td>15</td><td>90</td><td>400 (previously 200)</td></tr><tr><td>Medium</td><td>15</td><td>120</td><td>600 (previously 200)</td></tr><tr><td>Large</td><td>20</td><td>160</td><td>800 (previously 300)</td></tr><tr><td>XL</td><td>20</td><td>240</td><td>1,000 (previously 700)</td></tr><tr><td>2XL</td><td>25</td><td>380</td><td>1,500</td></tr><tr><td>4XL</td><td>32</td><td>480</td><td>3,000</td></tr><tr><td>8XL</td><td>64</td><td>490</td><td>6,000</td></tr><tr><td>12XL</td><td>96</td><td>500</td><td>9,000</td></tr><tr><td>16XL</td><td>128</td><td>500</td><td>12,000</td></tr></tbody></table>
<p>In this table:</p>
<ul>
<li><code class="short-inline-codeblock">default_pool_size</code>: the number of connections from Supavisor to your database (configurable)</li>
<li><code class="short-inline-codeblock">max_connections</code>: the max number of direct connections Postgres is configured to allow (configurable)</li>
<li><code class="short-inline-codeblock">default_max_clients</code> : the maximum number of clients allowed to connect to Supavisor (upgrade to increase)</li>
</ul>
<h3 id="ipv4-deprecation" class="group scroll-mt-24">IPv4 Deprecation<a href="#ipv4-deprecation" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Effective February 1, 2024 <a href="https://aws.amazon.com/blogs/aws/new-aws-public-ipv4-address-charge-public-ip-insights/">AWS is charging for all allocated IPV4 addresses</a>. Rather than passing that fee onto our customers Supavisor can mediate connections from IPv4 to IPv6.</p>
<p>If you&#x27;re using the PgBouncer connection string and haven&#x27;t migrated to the new Supavisor connection string make sure to do this before January 15th, 2024.</p>
<h2 id="getting-started" class="group scroll-mt-24">Getting started<a href="#getting-started" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>If you&#x27;re using the Supabase platform, you can already access the pooler URL in your <a href="https://supabase.com/dashboard/project/_?showConnect=true">project connect page</a>.</p>
<p>If you&#x27;re looking to self-host Supavisor, check out <a href="https://github.com/supabase/supavisor">GitHub repository</a> and <a href="https://supabase.github.io/supavisor/">documentation</a>.</p>
<p>You can expect Supavisor 1.0 to hit the platform next week along with the new pooling configuration changes. If you&#x27;ve set a custom pooler configuration, or we&#x27;ve set one for you, your settings won&#x27;t change.</p></div></article><div class="w-full border bg-alternative-200 flex flex-col rounded-lg text-foreground-lighter mt-12"><div class="w-full p-4 flex justify-between items-center"><a class="flex items-center gap-1.5 leading-none uppercase text-xs" href="../launch-week.html"><span class="text-foreground tracking-[1px]">Launch Week</span> <img alt="Supabase Launch Week X icon" loading="lazy" width="16" height="16" decoding="async" data-nimg="1" class="w-3 h-3" style="color:transparent" src="https://supabase.com/images/launchweek/lwx/logos/lwx_logo.svg"/></a><div class="font-mono uppercase tracking-wide text-xs">11-15 Dec</div></div><div class="pb-4 border-t p-4"><div class="font-mono uppercase text-xs text-foreground tracking-wide mb-3">Main Stage</div><ul class="flex flex-col gap-2"><ol><a class="group flex py-1 gap-2 hover:text-foreground" href="https://supabase.com/blog/studio-introducing-assistant"><span class="shrink-0 text-sm font-mono uppercase leading-6">Day <!-- -->1<!-- --> -</span><span class="leading-6">Supabase Studio: introducing an <strong>AI Assistant</strong>,<!-- --> <strong>Postgres roles</strong>, and <strong>user impersonation</strong></span></a></ol><ol><a class="group flex py-1 gap-2 hover:text-foreground" href="https://supabase.com/blog/edge-functions-node-npm"><span class="shrink-0 text-sm font-mono uppercase leading-6">Day <!-- -->2<!-- --> -</span><span class="leading-6">Edge Functions: <strong>Node</strong> and native <strong>npm</strong> compatibility</span></a></ol><ol><a class="group flex py-1 gap-2 hover:text-foreground" href="supabase-branching.html"><span class="shrink-0 text-sm font-mono uppercase leading-6">Day <!-- -->3<!-- --> -</span><span class="leading-6">Introducing Supabase <strong>Branching</strong>, a Postgres database for every pull request</span></a></ol><ol><a class="group flex py-1 gap-2 hover:text-foreground" href="supabase-auth-identity-linking-hooks.html"><span class="shrink-0 text-sm font-mono uppercase leading-6">Day <!-- -->4<!-- --> -</span><span class="leading-6">Supabase Auth: <strong>Identity Linking</strong>, <strong>Session Control</strong>,<!-- --> <strong>Password Protection</strong> and <strong>Hooks</strong></span></a></ol><ol><a class="group flex py-1 gap-2 hover:text-foreground" href="introducing-read-replicas.html"><span class="shrink-0 text-sm font-mono uppercase leading-6">Day <!-- -->5<!-- --> -</span><span class="leading-6">Introducing <strong>Read Replicas</strong> for low latency</span></a></ol></ul></div><div class="w-[calc(100%+2px)] bg-surface-100 flex flex-col gap-2 -m-px border rounded-lg"><div class="p-4"><div class="font-mono uppercase text-xs text-foreground tracking-wide">Build Stage</div><ul class="flex flex-col gap-2 mt-4"><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="https://supabase.productions/"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->1<!-- --> -</span>Supabase Album</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="https://supabase.com/blog/postgres-language-server-implementing-parser"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->2<!-- --> -</span>Postgres Language Server</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="https://supabase.com/blog/how-design-works-at-supabase"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->3<!-- --> -</span>Design at Supabase</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="https://github.com/supabase/supabase-grafana"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->4<!-- --> -</span>Supabase Grafana</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="https://supabase.com/blog/pg-graphql-postgres-functions"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->5<!-- --> -</span>pg_graphql: Postgres functions</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="postgrest-12.html"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->6<!-- --> -</span>PostgREST 12</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="supavisor-postgres-connection-pooler.html"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->7<!-- --> -</span>Supavisor 1.0</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="supabase-wrappers-v02.html"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->8<!-- --> -</span>Supabase Wrappers v0.2</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="client-libraries-v2.html"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->9<!-- --> -</span>Supabase Libraries V2</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="postgres-on-fly-by-supabase.html"><span class="relative"><span class="font-mono uppercase mr-2">10<!-- --> -</span>Supabase x Fly.io</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="launch-week-x-best-launches.html"><span class="relative"><span class="font-mono uppercase mr-2">11<!-- --> -</span>Top 10 Launches of LWX</span></a></ol><ol class="border-t pt-4 mt-2"><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="https://supabase.com/blog/supabase-hackathon-lwx">Supabase Launch Week X Hackathon</a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="https://supabase.com/blog/community-meetups-lwx">Supabase Launch Week X Community Meetups</a></ol></ul></div></div></div><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupavisor-postgres-connection-pooler&amp;text=Supavisor%201.0%3A%20a%20scalable%20connection%20pooler%20for%20Postgres"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupavisor-postgres-connection-pooler&amp;text=Supavisor%201.0%3A%20a%20scalable%20connection%20pooler%20for%20Postgres"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupavisor-postgres-connection-pooler&amp;t=Supavisor%201.0%3A%20a%20scalable%20connection%20pooler%20for%20Postgres"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="supabase-branching.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Supabase Branching</h4><p class="small">13 December 2023</p></div></div></div></div></a></div><div><a href="https://supabase.com/blog/edge-functions-node-npm"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Edge Functions: Node and native npm compatibility</h4><p class="small">12 December 2023</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/launch-week"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">launch-week</div></a><a href="https://supabase.com/blog/tags/supavisor"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">supavisor</div></a><a href="https://supabase.com/blog/tags/postgres"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">postgres</div></a><a href="https://supabase.com/blog/tags/planetpg"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">planetpg</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#what-is-connection-pooling">What is connection pooling?</a></li>
<li><a href="#sql-parsing-with-rust">SQL parsing with Rust</a></li>
<li><a href="#load-balancing">Load Balancing</a>
<ul>
<li><a href="#read-after-writes">Read-after-writes</a></li>
</ul>
</li>
<li><a href="#named-prepared-statements">Named Prepared Statements</a></li>
<li><a href="#query-cancelation">Query Cancelation</a></li>
<li><a href="#platform-updates">Platform Updates</a>
<ul>
<li><a href="#ipv4-deprecation">IPv4 Deprecation</a></li>
</ul>
</li>
<li><a href="#getting-started">Getting started</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupavisor-postgres-connection-pooler&amp;text=Supavisor%201.0%3A%20a%20scalable%20connection%20pooler%20for%20Postgres"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupavisor-postgres-connection-pooler&amp;text=Supavisor%201.0%3A%20a%20scalable%20connection%20pooler%20for%20Postgres"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupavisor-postgres-connection-pooler&amp;t=Supavisor%201.0%3A%20a%20scalable%20connection%20pooler%20for%20Postgres"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"supabase-branching","title":"Supabase Branching","description":"A Postgres database for every Pull Request.","launchweek":"X","categories":["product"],"tags":["launch-week","supavisor","postgres"],"date":"2023-12-13","toc_depth":3,"author":"qiao,jonny","image":"lwx-supabase-branching/branching-og.png","thumb":"lwx-supabase-branching/branching-thumb.png","formattedDate":"13 December 2023","readingTime":"8 minute read","url":"/blog/supabase-branching","path":"/blog/supabase-branching"},"nextPost":{"slug":"edge-functions-node-npm","title":"Edge Functions: Node and native npm compatibility","description":"We're adding Node and native npm compatibility for Edge Functions.","launchweek":"x","categories":["product"],"tags":["launch-week","edge-functions"],"date":"2023-12-12","toc_depth":3,"author":"laktek,andreespirela","image":"launch-week-x/day-2/og-edge-runtime.png","thumb":"launch-week-x/day-2/edge-runtime-thumb.png","formattedDate":"12 December 2023","readingTime":"6 minute read","url":"/blog/edge-functions-node-npm","path":"/blog/edge-functions-node-npm"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supavisor-postgres-connection-pooler","source":"\nAfter [launching Supavisor in August](https://supabase.com/blog/supavisor-1-million), we've successfully migrated all projects on the platform. Every new Supabase project launched now gets a Supavisor connection string to use for connection pooling.\n\nSupavisor 1.0 symbolizes production readiness and comes with many bug fixes. It includes three important features:\n\n1. query load balancing\n2. named prepared statement support\n3. query cancelation\n\n## What is connection pooling?\n\nSupavisor is built with Elixir. Since the [Dashbit](https://dashbit.co/) team have been helping with the development we invited Jose Valim, the creator of Elixir, to explain connection pooling, OTP, and why Elixir is a great fit for a connection pooler:\n\n\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/ogYNmJOFEpk\"\n    title=\"YouTube video player\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"\n    allowfullscreen\n  /\u003e\n\u003c/div\u003e\n\n## SQL parsing with Rust\n\nTo implement the latest set of features, we now parse all SQL statements from connected clients.\n\nSupavisor, developed in Elixir, supports high concurrency and rapid I/O. Elixir doesn't have great performance for parsing, but it provides excellent interop with Rust via [Rustler](https://github.com/rusterlium/rustler). For efficient SQL parsing, we use [`pg_query.rs`](https://github.com/pganalyze/pg_query.rs).\n\n## Load Balancing\n\nWhen set up with a Postgres cluster, Supavisor load-balances read requests between the primary server and its replicas. It randomly distributes these read requests across the entire Postgres cluster.\n\nSupavisor targets write operations to the primary automatically by probing read replicas until it hits the primary with a successful write, [similar to libpq](https://www.percona.com/blog/seamless-application-failover-using-libpq-features-in-postgresql/). The trade-off here is that writes may take a few milliseconds longer to complete in favor of zero additional client-side complexity. This write strategy also makes transparent primary failovers painless because detecting the primary for writes is automatic.\n\n### Read-after-writes\n\nWith automatic primary detection, it's easy to guarantee read-after-writes from the same client by wrapping the read and write in a transaction.\n\nFuture work is planned to allow custom server targeting with SQL statements such as `SET SERVER 'primary'` to let clients guarantee read-after-writes outside of transactions or across clients.\n\n## Named Prepared Statements\n\nMany clients use named [prepared statements](https://www.postgresql.org/docs/current/sql-prepare.html) when generating parameterized SQL. During statement preparation Postgres parses, plans, and optimizes queries.\n\nIf a client can create named prepared statements, then such a client can re-use these query plans and simply submit parameters for them.\n\nThe problem with named prepared statements and pooling in the transaction mode is that statements are not shared across Postgres backends (connections). Each client connection must issue prepared statements for each query they will run.\n\nSupavisor now supports named prepared statements. Supavisor parses each query and identifies `PREPARE` statements. When a `PREPARE` statement is received on one connection, it is broadcast across all connections. This approach allows every client to access named prepared statements that have been issued by other connections. This adds a slight increase in memory overhead when duplicating query plans for each Postgres connection but should come with significant throughput gains.\n\n\u003cImg\n  src={{\n    dark: '/images/blog/lwx-supavisor/prepared-statements-dark.png',\n    light: '/images/blog/lwx-supavisor/prepared-statements-light.png',\n  }}\n  alt=\"Diagram explaining how named prepared statements work in Supavisor\"\n/\u003e\n\n## Query Cancelation\n\nWith 1.0 we get query official cancelation as well. If you're in `psql` typing `Ctrl+C` will actually cancel your query now.\n\nEspecially useful if you accidentally run a heavy query!\n\n## Platform Updates\n\nFor the Supavisor rollout, we maintained consistent pooling settings between PgBouncer and Supavisor.\n\nNow, we're raising the client connection limit for smaller projects in Supavisor. Here are the updated default configurations:\n\n| Database Size | default_pool_size | max_connections | default_max_clients    |\n| ------------- | ----------------- | --------------- | ---------------------- |\n| Micro         | 15                | 60              | 200                    |\n| Small         | 15                | 90              | 400 (previously 200)   |\n| Medium        | 15                | 120             | 600 (previously 200)   |\n| Large         | 20                | 160             | 800 (previously 300)   |\n| XL            | 20                | 240             | 1,000 (previously 700) |\n| 2XL           | 25                | 380             | 1,500                  |\n| 4XL           | 32                | 480             | 3,000                  |\n| 8XL           | 64                | 490             | 6,000                  |\n| 12XL          | 96                | 500             | 9,000                  |\n| 16XL          | 128               | 500             | 12,000                 |\n\nIn this table:\n\n- `default_pool_size`: the number of connections from Supavisor to your database (configurable)\n- `max_connections`: the max number of direct connections Postgres is configured to allow (configurable)\n- `default_max_clients` : the maximum number of clients allowed to connect to Supavisor (upgrade to increase)\n\n### IPv4 Deprecation\n\nEffective February 1, 2024 [AWS is charging for all allocated IPV4 addresses](https://aws.amazon.com/blogs/aws/new-aws-public-ipv4-address-charge-public-ip-insights/). Rather than passing that fee onto our customers Supavisor can mediate connections from IPv4 to IPv6.\n\nIf you're using the PgBouncer connection string and haven't migrated to the new Supavisor connection string make sure to do this before January 15th, 2024.\n\n## Getting started\n\nIf you're using the Supabase platform, you can already access the pooler URL in your [project connect page](https://supabase.com/dashboard/project/_?showConnect=true).\n\nIf you're looking to self-host Supavisor, check out [GitHub repository](https://github.com/supabase/supavisor) and [documentation](https://supabase.github.io/supavisor/).\n\nYou can expect Supavisor 1.0 to hit the platform next week along with the new pooling configuration changes. If you've set a custom pooler configuration, or we've set one for you, your settings won't change.\n","title":"Supavisor 1.0: a scalable connection pooler for Postgres","description":"Supavisor is now used across all projects, providing a scalable and cloud-native Postgres connection pooler that can handle millions of connections","launchweek":"X","categories":["product"],"tags":["launch-week","supavisor","postgres","planetpg"],"date":"2023-12-13","toc_depth":3,"author":"stas","image":"lwx-supavisor/supavisor-og.png","thumb":"lwx-supavisor/supavisor-thumb.png","content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    a: \"a\",\n    ol: \"ol\",\n    li: \"li\",\n    h2: \"h2\",\n    code: \"code\",\n    h3: \"h3\",\n    table: \"table\",\n    thead: \"thead\",\n    tr: \"tr\",\n    th: \"th\",\n    tbody: \"tbody\",\n    td: \"td\",\n    ul: \"ul\"\n  }, _provideComponents(), props.components), {Img} = _components;\n  if (!Img) _missingMdxReference(\"Img\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsxs(_components.p, {\n      children: [\"After \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/supavisor-1-million\",\n        children: \"launching Supavisor in August\"\n      }), \", we've successfully migrated all projects on the platform. Every new Supabase project launched now gets a Supavisor connection string to use for connection pooling.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Supavisor 1.0 symbolizes production readiness and comes with many bug fixes. It includes three important features:\"\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"query load balancing\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"named prepared statement support\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"query cancelation\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"what-is-connection-pooling\",\n      children: \"What is connection pooling?\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Supavisor is built with Elixir. Since the \", _jsx(_components.a, {\n        href: \"https://dashbit.co/\",\n        children: \"Dashbit\"\n      }), \" team have been helping with the development we invited Jose Valim, the creator of Elixir, to explain connection pooling, OTP, and why Elixir is a great fit for a connection pooler:\"]\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"video-container\",\n      children: _jsx(\"iframe\", {\n        className: \"w-full\",\n        src: \"https://www.youtube-nocookie.com/embed/ogYNmJOFEpk\",\n        title: \"YouTube video player\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\",\n        allowfullscreen: true\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"sql-parsing-with-rust\",\n      children: \"SQL parsing with Rust\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To implement the latest set of features, we now parse all SQL statements from connected clients.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Supavisor, developed in Elixir, supports high concurrency and rapid I/O. Elixir doesn't have great performance for parsing, but it provides excellent interop with Rust via \", _jsx(_components.a, {\n        href: \"https://github.com/rusterlium/rustler\",\n        children: \"Rustler\"\n      }), \". For efficient SQL parsing, we use \", _jsx(_components.a, {\n        href: \"https://github.com/pganalyze/pg_query.rs\",\n        children: _jsx(_components.code, {\n          children: \"pg_query.rs\"\n        })\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"load-balancing\",\n      children: \"Load Balancing\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"When set up with a Postgres cluster, Supavisor load-balances read requests between the primary server and its replicas. It randomly distributes these read requests across the entire Postgres cluster.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Supavisor targets write operations to the primary automatically by probing read replicas until it hits the primary with a successful write, \", _jsx(_components.a, {\n        href: \"https://www.percona.com/blog/seamless-application-failover-using-libpq-features-in-postgresql/\",\n        children: \"similar to libpq\"\n      }), \". The trade-off here is that writes may take a few milliseconds longer to complete in favor of zero additional client-side complexity. This write strategy also makes transparent primary failovers painless because detecting the primary for writes is automatic.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"read-after-writes\",\n      children: \"Read-after-writes\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"With automatic primary detection, it's easy to guarantee read-after-writes from the same client by wrapping the read and write in a transaction.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Future work is planned to allow custom server targeting with SQL statements such as \", _jsx(_components.code, {\n        children: \"SET SERVER 'primary'\"\n      }), \" to let clients guarantee read-after-writes outside of transactions or across clients.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"named-prepared-statements\",\n      children: \"Named Prepared Statements\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Many clients use named \", _jsx(_components.a, {\n        href: \"https://www.postgresql.org/docs/current/sql-prepare.html\",\n        children: \"prepared statements\"\n      }), \" when generating parameterized SQL. During statement preparation Postgres parses, plans, and optimizes queries.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"If a client can create named prepared statements, then such a client can re-use these query plans and simply submit parameters for them.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The problem with named prepared statements and pooling in the transaction mode is that statements are not shared across Postgres backends (connections). Each client connection must issue prepared statements for each query they will run.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Supavisor now supports named prepared statements. Supavisor parses each query and identifies \", _jsx(_components.code, {\n        children: \"PREPARE\"\n      }), \" statements. When a \", _jsx(_components.code, {\n        children: \"PREPARE\"\n      }), \" statement is received on one connection, it is broadcast across all connections. This approach allows every client to access named prepared statements that have been issued by other connections. This adds a slight increase in memory overhead when duplicating query plans for each Postgres connection but should come with significant throughput gains.\"]\n    }), \"\\n\", _jsx(Img, {\n      src: {\n        dark: '/images/blog/lwx-supavisor/prepared-statements-dark.png',\n        light: '/images/blog/lwx-supavisor/prepared-statements-light.png'\n      },\n      alt: \"Diagram explaining how named prepared statements work in Supavisor\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"query-cancelation\",\n      children: \"Query Cancelation\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"With 1.0 we get query official cancelation as well. If you're in \", _jsx(_components.code, {\n        children: \"psql\"\n      }), \" typing \", _jsx(_components.code, {\n        children: \"Ctrl+C\"\n      }), \" will actually cancel your query now.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Especially useful if you accidentally run a heavy query!\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"platform-updates\",\n      children: \"Platform Updates\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"For the Supavisor rollout, we maintained consistent pooling settings between PgBouncer and Supavisor.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Now, we're raising the client connection limit for smaller projects in Supavisor. Here are the updated default configurations:\"\n    }), \"\\n\", _jsxs(_components.table, {\n      children: [_jsx(_components.thead, {\n        children: _jsxs(_components.tr, {\n          children: [_jsx(_components.th, {\n            children: \"Database Size\"\n          }), _jsx(_components.th, {\n            children: \"default_pool_size\"\n          }), _jsx(_components.th, {\n            children: \"max_connections\"\n          }), _jsx(_components.th, {\n            children: \"default_max_clients\"\n          })]\n        })\n      }), _jsxs(_components.tbody, {\n        children: [_jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"Micro\"\n          }), _jsx(_components.td, {\n            children: \"15\"\n          }), _jsx(_components.td, {\n            children: \"60\"\n          }), _jsx(_components.td, {\n            children: \"200\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"Small\"\n          }), _jsx(_components.td, {\n            children: \"15\"\n          }), _jsx(_components.td, {\n            children: \"90\"\n          }), _jsx(_components.td, {\n            children: \"400 (previously 200)\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"Medium\"\n          }), _jsx(_components.td, {\n            children: \"15\"\n          }), _jsx(_components.td, {\n            children: \"120\"\n          }), _jsx(_components.td, {\n            children: \"600 (previously 200)\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"Large\"\n          }), _jsx(_components.td, {\n            children: \"20\"\n          }), _jsx(_components.td, {\n            children: \"160\"\n          }), _jsx(_components.td, {\n            children: \"800 (previously 300)\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"XL\"\n          }), _jsx(_components.td, {\n            children: \"20\"\n          }), _jsx(_components.td, {\n            children: \"240\"\n          }), _jsx(_components.td, {\n            children: \"1,000 (previously 700)\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"2XL\"\n          }), _jsx(_components.td, {\n            children: \"25\"\n          }), _jsx(_components.td, {\n            children: \"380\"\n          }), _jsx(_components.td, {\n            children: \"1,500\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"4XL\"\n          }), _jsx(_components.td, {\n            children: \"32\"\n          }), _jsx(_components.td, {\n            children: \"480\"\n          }), _jsx(_components.td, {\n            children: \"3,000\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"8XL\"\n          }), _jsx(_components.td, {\n            children: \"64\"\n          }), _jsx(_components.td, {\n            children: \"490\"\n          }), _jsx(_components.td, {\n            children: \"6,000\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"12XL\"\n          }), _jsx(_components.td, {\n            children: \"96\"\n          }), _jsx(_components.td, {\n            children: \"500\"\n          }), _jsx(_components.td, {\n            children: \"9,000\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"16XL\"\n          }), _jsx(_components.td, {\n            children: \"128\"\n          }), _jsx(_components.td, {\n            children: \"500\"\n          }), _jsx(_components.td, {\n            children: \"12,000\"\n          })]\n        })]\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In this table:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.code, {\n          children: \"default_pool_size\"\n        }), \": the number of connections from Supavisor to your database (configurable)\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.code, {\n          children: \"max_connections\"\n        }), \": the max number of direct connections Postgres is configured to allow (configurable)\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.code, {\n          children: \"default_max_clients\"\n        }), \" : the maximum number of clients allowed to connect to Supavisor (upgrade to increase)\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"ipv4-deprecation\",\n      children: \"IPv4 Deprecation\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Effective February 1, 2024 \", _jsx(_components.a, {\n        href: \"https://aws.amazon.com/blogs/aws/new-aws-public-ipv4-address-charge-public-ip-insights/\",\n        children: \"AWS is charging for all allocated IPV4 addresses\"\n      }), \". Rather than passing that fee onto our customers Supavisor can mediate connections from IPv4 to IPv6.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"If you're using the PgBouncer connection string and haven't migrated to the new Supavisor connection string make sure to do this before January 15th, 2024.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"getting-started\",\n      children: \"Getting started\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you're using the Supabase platform, you can already access the pooler URL in your \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard/project/_?showConnect=true\",\n        children: \"project connect page\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you're looking to self-host Supavisor, check out \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/supavisor\",\n        children: \"GitHub repository\"\n      }), \" and \", _jsx(_components.a, {\n        href: \"https://supabase.github.io/supavisor/\",\n        children: \"documentation\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You can expect Supavisor 1.0 to hit the platform next week along with the new pooling configuration changes. If you've set a custom pooler configuration, or we've set one for you, your settings won't change.\"\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"What is connection pooling?","slug":"what-is-connection-pooling","lvl":2,"i":0,"seen":0},{"content":"SQL parsing with Rust","slug":"sql-parsing-with-rust","lvl":2,"i":1,"seen":0},{"content":"Load Balancing","slug":"load-balancing","lvl":2,"i":2,"seen":0},{"content":"Read-after-writes","slug":"read-after-writes","lvl":3,"i":3,"seen":0},{"content":"Named Prepared Statements","slug":"named-prepared-statements","lvl":2,"i":4,"seen":0},{"content":"Query Cancelation","slug":"query-cancelation","lvl":2,"i":5,"seen":0},{"content":"Platform Updates","slug":"platform-updates","lvl":2,"i":6,"seen":0},{"content":"IPv4 Deprecation","slug":"ipv4-deprecation","lvl":3,"i":7,"seen":0},{"content":"Getting started","slug":"getting-started","lvl":2,"i":8,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,2],"level":0},{"type":"inline","content":"After [launching Supavisor in August](https://supabase.com/blog/supavisor-1-million), we've successfully migrated all projects on the platform. Every new Supabase project launched now gets a Supavisor connection string to use for connection pooling.","level":1,"lines":[1,2],"children":[{"type":"text","content":"After ","level":0},{"type":"link_open","href":"https://supabase.com/blog/supavisor-1-million","title":"","level":0},{"type":"text","content":"launching Supavisor in August","level":1},{"type":"link_close","level":0},{"type":"text","content":", we've successfully migrated all projects on the platform. Every new Supabase project launched now gets a Supavisor connection string to use for connection pooling.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[3,4],"level":0},{"type":"inline","content":"Supavisor 1.0 symbolizes production readiness and comes with many bug fixes. It includes three important features:","level":1,"lines":[3,4],"children":[{"type":"text","content":"Supavisor 1.0 symbolizes production readiness and comes with many bug fixes. It includes three important features:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"ordered_list_open","order":1,"lines":[5,9],"level":0},{"type":"list_item_open","lines":[5,6],"level":1},{"type":"paragraph_open","tight":true,"lines":[5,6],"level":2},{"type":"inline","content":"query load balancing","level":3,"lines":[5,6],"children":[{"type":"text","content":"query load balancing","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[6,7],"level":1},{"type":"paragraph_open","tight":true,"lines":[6,7],"level":2},{"type":"inline","content":"named prepared statement support","level":3,"lines":[6,7],"children":[{"type":"text","content":"named prepared statement support","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[7,9],"level":1},{"type":"paragraph_open","tight":true,"lines":[7,8],"level":2},{"type":"inline","content":"query cancelation","level":3,"lines":[7,8],"children":[{"type":"text","content":"query cancelation","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"ordered_list_close","level":0},{"type":"heading_open","hLevel":2,"lines":[9,10],"level":0},{"type":"inline","content":"[What is connection pooling?](#what-is-connection-pooling)","level":1,"lines":[9,10],"children":[{"type":"text","content":"What is connection pooling?","level":0}],"lvl":2,"i":0,"seen":0,"slug":"what-is-connection-pooling"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[11,12],"level":0},{"type":"inline","content":"Supavisor is built with Elixir. Since the [Dashbit](https://dashbit.co/) team have been helping with the development we invited Jose Valim, the creator of Elixir, to explain connection pooling, OTP, and why Elixir is a great fit for a connection pooler:","level":1,"lines":[11,12],"children":[{"type":"text","content":"Supavisor is built with Elixir. Since the ","level":0},{"type":"link_open","href":"https://dashbit.co/","title":"","level":0},{"type":"text","content":"Dashbit","level":1},{"type":"link_close","level":0},{"type":"text","content":" team have been helping with the development we invited Jose Valim, the creator of Elixir, to explain connection pooling, OTP, and why Elixir is a great fit for a connection pooler:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[13,22],"level":0},{"type":"inline","content":"\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/ogYNmJOFEpk\"\n    title=\"YouTube video player\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"\n    allowfullscreen\n  /\u003e\n\u003c/div\u003e","level":1,"lines":[13,22],"children":[{"type":"text","content":"\u003cdiv className=\"video-container\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ciframe","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"w-full\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://www.youtube-nocookie.com/embed/ogYNmJOFEpk\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"title=\"YouTube video player\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allowfullscreen","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[23,24],"level":0},{"type":"inline","content":"[SQL parsing with Rust](#sql-parsing-with-rust)","level":1,"lines":[23,24],"children":[{"type":"text","content":"SQL parsing with Rust","level":0}],"lvl":2,"i":1,"seen":0,"slug":"sql-parsing-with-rust"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[25,26],"level":0},{"type":"inline","content":"To implement the latest set of features, we now parse all SQL statements from connected clients.","level":1,"lines":[25,26],"children":[{"type":"text","content":"To implement the latest set of features, we now parse all SQL statements from connected clients.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[27,28],"level":0},{"type":"inline","content":"Supavisor, developed in Elixir, supports high concurrency and rapid I/O. Elixir doesn't have great performance for parsing, but it provides excellent interop with Rust via [Rustler](https://github.com/rusterlium/rustler). For efficient SQL parsing, we use [`pg_query.rs`](https://github.com/pganalyze/pg_query.rs).","level":1,"lines":[27,28],"children":[{"type":"text","content":"Supavisor, developed in Elixir, supports high concurrency and rapid I/O. Elixir doesn't have great performance for parsing, but it provides excellent interop with Rust via ","level":0},{"type":"link_open","href":"https://github.com/rusterlium/rustler","title":"","level":0},{"type":"text","content":"Rustler","level":1},{"type":"link_close","level":0},{"type":"text","content":". For efficient SQL parsing, we use ","level":0},{"type":"link_open","href":"https://github.com/pganalyze/pg_query.rs","title":"","level":0},{"type":"code","content":"pg_query.rs","block":false,"level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[29,30],"level":0},{"type":"inline","content":"[Load Balancing](#load-balancing)","level":1,"lines":[29,30],"children":[{"type":"text","content":"Load Balancing","level":0}],"lvl":2,"i":2,"seen":0,"slug":"load-balancing"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[31,32],"level":0},{"type":"inline","content":"When set up with a Postgres cluster, Supavisor load-balances read requests between the primary server and its replicas. It randomly distributes these read requests across the entire Postgres cluster.","level":1,"lines":[31,32],"children":[{"type":"text","content":"When set up with a Postgres cluster, Supavisor load-balances read requests between the primary server and its replicas. It randomly distributes these read requests across the entire Postgres cluster.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[33,34],"level":0},{"type":"inline","content":"Supavisor targets write operations to the primary automatically by probing read replicas until it hits the primary with a successful write, [similar to libpq](https://www.percona.com/blog/seamless-application-failover-using-libpq-features-in-postgresql/). The trade-off here is that writes may take a few milliseconds longer to complete in favor of zero additional client-side complexity. This write strategy also makes transparent primary failovers painless because detecting the primary for writes is automatic.","level":1,"lines":[33,34],"children":[{"type":"text","content":"Supavisor targets write operations to the primary automatically by probing read replicas until it hits the primary with a successful write, ","level":0},{"type":"link_open","href":"https://www.percona.com/blog/seamless-application-failover-using-libpq-features-in-postgresql/","title":"","level":0},{"type":"text","content":"similar to libpq","level":1},{"type":"link_close","level":0},{"type":"text","content":". The trade-off here is that writes may take a few milliseconds longer to complete in favor of zero additional client-side complexity. This write strategy also makes transparent primary failovers painless because detecting the primary for writes is automatic.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[35,36],"level":0},{"type":"inline","content":"[Read-after-writes](#read-after-writes)","level":1,"lines":[35,36],"children":[{"type":"text","content":"Read-after-writes","level":0}],"lvl":3,"i":3,"seen":0,"slug":"read-after-writes"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[37,38],"level":0},{"type":"inline","content":"With automatic primary detection, it's easy to guarantee read-after-writes from the same client by wrapping the read and write in a transaction.","level":1,"lines":[37,38],"children":[{"type":"text","content":"With automatic primary detection, it's easy to guarantee read-after-writes from the same client by wrapping the read and write in a transaction.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[39,40],"level":0},{"type":"inline","content":"Future work is planned to allow custom server targeting with SQL statements such as `SET SERVER 'primary'` to let clients guarantee read-after-writes outside of transactions or across clients.","level":1,"lines":[39,40],"children":[{"type":"text","content":"Future work is planned to allow custom server targeting with SQL statements such as ","level":0},{"type":"code","content":"SET SERVER 'primary'","block":false,"level":0},{"type":"text","content":" to let clients guarantee read-after-writes outside of transactions or across clients.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[41,42],"level":0},{"type":"inline","content":"[Named Prepared Statements](#named-prepared-statements)","level":1,"lines":[41,42],"children":[{"type":"text","content":"Named Prepared Statements","level":0}],"lvl":2,"i":4,"seen":0,"slug":"named-prepared-statements"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[43,44],"level":0},{"type":"inline","content":"Many clients use named [prepared statements](https://www.postgresql.org/docs/current/sql-prepare.html) when generating parameterized SQL. During statement preparation Postgres parses, plans, and optimizes queries.","level":1,"lines":[43,44],"children":[{"type":"text","content":"Many clients use named ","level":0},{"type":"link_open","href":"https://www.postgresql.org/docs/current/sql-prepare.html","title":"","level":0},{"type":"text","content":"prepared statements","level":1},{"type":"link_close","level":0},{"type":"text","content":" when generating parameterized SQL. During statement preparation Postgres parses, plans, and optimizes queries.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[45,46],"level":0},{"type":"inline","content":"If a client can create named prepared statements, then such a client can re-use these query plans and simply submit parameters for them.","level":1,"lines":[45,46],"children":[{"type":"text","content":"If a client can create named prepared statements, then such a client can re-use these query plans and simply submit parameters for them.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[47,48],"level":0},{"type":"inline","content":"The problem with named prepared statements and pooling in the transaction mode is that statements are not shared across Postgres backends (connections). Each client connection must issue prepared statements for each query they will run.","level":1,"lines":[47,48],"children":[{"type":"text","content":"The problem with named prepared statements and pooling in the transaction mode is that statements are not shared across Postgres backends (connections). Each client connection must issue prepared statements for each query they will run.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[49,50],"level":0},{"type":"inline","content":"Supavisor now supports named prepared statements. Supavisor parses each query and identifies `PREPARE` statements. When a `PREPARE` statement is received on one connection, it is broadcast across all connections. This approach allows every client to access named prepared statements that have been issued by other connections. This adds a slight increase in memory overhead when duplicating query plans for each Postgres connection but should come with significant throughput gains.","level":1,"lines":[49,50],"children":[{"type":"text","content":"Supavisor now supports named prepared statements. Supavisor parses each query and identifies ","level":0},{"type":"code","content":"PREPARE","block":false,"level":0},{"type":"text","content":" statements. When a ","level":0},{"type":"code","content":"PREPARE","block":false,"level":0},{"type":"text","content":" statement is received on one connection, it is broadcast across all connections. This approach allows every client to access named prepared statements that have been issued by other connections. This adds a slight increase in memory overhead when duplicating query plans for each Postgres connection but should come with significant throughput gains.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[51,58],"level":0},{"type":"inline","content":"\u003cImg\n  src={{\n    dark: '/images/blog/lwx-supavisor/prepared-statements-dark.png',\n    light: '/images/blog/lwx-supavisor/prepared-statements-light.png',\n  }}\n  alt=\"Diagram explaining how named prepared statements work in Supavisor\"\n/\u003e","level":1,"lines":[51,58],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src={{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"dark: '/images/blog/lwx-supavisor/prepared-statements-dark.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"light: '/images/blog/lwx-supavisor/prepared-statements-light.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"}}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"Diagram explaining how named prepared statements work in Supavisor\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[59,60],"level":0},{"type":"inline","content":"[Query Cancelation](#query-cancelation)","level":1,"lines":[59,60],"children":[{"type":"text","content":"Query Cancelation","level":0}],"lvl":2,"i":5,"seen":0,"slug":"query-cancelation"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[61,62],"level":0},{"type":"inline","content":"With 1.0 we get query official cancelation as well. If you're in `psql` typing `Ctrl+C` will actually cancel your query now.","level":1,"lines":[61,62],"children":[{"type":"text","content":"With 1.0 we get query official cancelation as well. If you're in ","level":0},{"type":"code","content":"psql","block":false,"level":0},{"type":"text","content":" typing ","level":0},{"type":"code","content":"Ctrl+C","block":false,"level":0},{"type":"text","content":" will actually cancel your query now.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[63,64],"level":0},{"type":"inline","content":"Especially useful if you accidentally run a heavy query!","level":1,"lines":[63,64],"children":[{"type":"text","content":"Especially useful if you accidentally run a heavy query!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[65,66],"level":0},{"type":"inline","content":"[Platform Updates](#platform-updates)","level":1,"lines":[65,66],"children":[{"type":"text","content":"Platform Updates","level":0}],"lvl":2,"i":6,"seen":0,"slug":"platform-updates"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[67,68],"level":0},{"type":"inline","content":"For the Supavisor rollout, we maintained consistent pooling settings between PgBouncer and Supavisor.","level":1,"lines":[67,68],"children":[{"type":"text","content":"For the Supavisor rollout, we maintained consistent pooling settings between PgBouncer and Supavisor.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[69,70],"level":0},{"type":"inline","content":"Now, we're raising the client connection limit for smaller projects in Supavisor. Here are the updated default configurations:","level":1,"lines":[69,70],"children":[{"type":"text","content":"Now, we're raising the client connection limit for smaller projects in Supavisor. Here are the updated default configurations:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"table_open","lines":[71,83],"level":0},{"type":"thead_open","lines":[71,72],"level":1},{"type":"tr_open","lines":[71,72],"level":2},{"type":"th_open","align":"","lines":[71,72],"level":3},{"type":"inline","content":"Database Size","lines":[71,72],"level":4,"children":[{"type":"text","content":"Database Size","level":0}]},{"type":"th_close","level":3},{"type":"th_open","align":"","lines":[71,72],"level":3},{"type":"inline","content":"default_pool_size","lines":[71,72],"level":4,"children":[{"type":"text","content":"default_pool_size","level":0}]},{"type":"th_close","level":3},{"type":"th_open","align":"","lines":[71,72],"level":3},{"type":"inline","content":"max_connections","lines":[71,72],"level":4,"children":[{"type":"text","content":"max_connections","level":0}]},{"type":"th_close","level":3},{"type":"th_open","align":"","lines":[71,72],"level":3},{"type":"inline","content":"default_max_clients","lines":[71,72],"level":4,"children":[{"type":"text","content":"default_max_clients","level":0}]},{"type":"th_close","level":3},{"type":"tr_close","level":2},{"type":"thead_close","level":1},{"type":"tbody_open","lines":[73,83],"level":1},{"type":"tr_open","level":2},{"type":"td_open","align":"","level":3},{"type":"inline","content":"Micro","level":4,"children":[{"type":"text","content":"Micro","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"15","level":4,"children":[{"type":"text","content":"15","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"60","level":4,"children":[{"type":"text","content":"60","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"200","level":4,"children":[{"type":"text","content":"200","level":0}]},{"type":"td_close","level":3},{"type":"tr_close","level":2},{"type":"tr_open","level":2},{"type":"td_open","align":"","level":3},{"type":"inline","content":"Small","level":4,"children":[{"type":"text","content":"Small","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"15","level":4,"children":[{"type":"text","content":"15","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"90","level":4,"children":[{"type":"text","content":"90","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"400 (previously 200)","level":4,"children":[{"type":"text","content":"400 (previously 200)","level":0}]},{"type":"td_close","level":3},{"type":"tr_close","level":2},{"type":"tr_open","level":2},{"type":"td_open","align":"","level":3},{"type":"inline","content":"Medium","level":4,"children":[{"type":"text","content":"Medium","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"15","level":4,"children":[{"type":"text","content":"15","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"120","level":4,"children":[{"type":"text","content":"120","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"600 (previously 200)","level":4,"children":[{"type":"text","content":"600 (previously 200)","level":0}]},{"type":"td_close","level":3},{"type":"tr_close","level":2},{"type":"tr_open","level":2},{"type":"td_open","align":"","level":3},{"type":"inline","content":"Large","level":4,"children":[{"type":"text","content":"Large","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"20","level":4,"children":[{"type":"text","content":"20","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"160","level":4,"children":[{"type":"text","content":"160","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"800 (previously 300)","level":4,"children":[{"type":"text","content":"800 (previously 300)","level":0}]},{"type":"td_close","level":3},{"type":"tr_close","level":2},{"type":"tr_open","level":2},{"type":"td_open","align":"","level":3},{"type":"inline","content":"XL","level":4,"children":[{"type":"text","content":"XL","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"20","level":4,"children":[{"type":"text","content":"20","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"240","level":4,"children":[{"type":"text","content":"240","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"1,000 (previously 700)","level":4,"children":[{"type":"text","content":"1,000 (previously 700)","level":0}]},{"type":"td_close","level":3},{"type":"tr_close","level":2},{"type":"tr_open","level":2},{"type":"td_open","align":"","level":3},{"type":"inline","content":"2XL","level":4,"children":[{"type":"text","content":"2XL","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"25","level":4,"children":[{"type":"text","content":"25","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"380","level":4,"children":[{"type":"text","content":"380","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"1,500","level":4,"children":[{"type":"text","content":"1,500","level":0}]},{"type":"td_close","level":3},{"type":"tr_close","level":2},{"type":"tr_open","level":2},{"type":"td_open","align":"","level":3},{"type":"inline","content":"4XL","level":4,"children":[{"type":"text","content":"4XL","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"32","level":4,"children":[{"type":"text","content":"32","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"480","level":4,"children":[{"type":"text","content":"480","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"3,000","level":4,"children":[{"type":"text","content":"3,000","level":0}]},{"type":"td_close","level":3},{"type":"tr_close","level":2},{"type":"tr_open","level":2},{"type":"td_open","align":"","level":3},{"type":"inline","content":"8XL","level":4,"children":[{"type":"text","content":"8XL","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"64","level":4,"children":[{"type":"text","content":"64","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"490","level":4,"children":[{"type":"text","content":"490","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"6,000","level":4,"children":[{"type":"text","content":"6,000","level":0}]},{"type":"td_close","level":3},{"type":"tr_close","level":2},{"type":"tr_open","level":2},{"type":"td_open","align":"","level":3},{"type":"inline","content":"12XL","level":4,"children":[{"type":"text","content":"12XL","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"96","level":4,"children":[{"type":"text","content":"96","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"500","level":4,"children":[{"type":"text","content":"500","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"9,000","level":4,"children":[{"type":"text","content":"9,000","level":0}]},{"type":"td_close","level":3},{"type":"tr_close","level":2},{"type":"tr_open","level":2},{"type":"td_open","align":"","level":3},{"type":"inline","content":"16XL","level":4,"children":[{"type":"text","content":"16XL","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"128","level":4,"children":[{"type":"text","content":"128","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"500","level":4,"children":[{"type":"text","content":"500","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"12,000","level":4,"children":[{"type":"text","content":"12,000","level":0}]},{"type":"td_close","level":3},{"type":"tr_close","level":2},{"type":"tbody_close","level":1},{"type":"table_close","level":0},{"type":"paragraph_open","tight":false,"lines":[84,85],"level":0},{"type":"inline","content":"In this table:","level":1,"lines":[84,85],"children":[{"type":"text","content":"In this table:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[86,90],"level":0},{"type":"list_item_open","lines":[86,87],"level":1},{"type":"paragraph_open","tight":true,"lines":[86,87],"level":2},{"type":"inline","content":"`default_pool_size`: the number of connections from Supavisor to your database (configurable)","level":3,"lines":[86,87],"children":[{"type":"code","content":"default_pool_size","block":false,"level":0},{"type":"text","content":": the number of connections from Supavisor to your database (configurable)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[87,88],"level":1},{"type":"paragraph_open","tight":true,"lines":[87,88],"level":2},{"type":"inline","content":"`max_connections`: the max number of direct connections Postgres is configured to allow (configurable)","level":3,"lines":[87,88],"children":[{"type":"code","content":"max_connections","block":false,"level":0},{"type":"text","content":": the max number of direct connections Postgres is configured to allow (configurable)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[88,90],"level":1},{"type":"paragraph_open","tight":true,"lines":[88,89],"level":2},{"type":"inline","content":"`default_max_clients` : the maximum number of clients allowed to connect to Supavisor (upgrade to increase)","level":3,"lines":[88,89],"children":[{"type":"code","content":"default_max_clients","block":false,"level":0},{"type":"text","content":" : the maximum number of clients allowed to connect to Supavisor (upgrade to increase)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":3,"lines":[90,91],"level":0},{"type":"inline","content":"[IPv4 Deprecation](#ipv4-deprecation)","level":1,"lines":[90,91],"children":[{"type":"text","content":"IPv4 Deprecation","level":0}],"lvl":3,"i":7,"seen":0,"slug":"ipv4-deprecation"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[92,93],"level":0},{"type":"inline","content":"Effective February 1, 2024 [AWS is charging for all allocated IPV4 addresses](https://aws.amazon.com/blogs/aws/new-aws-public-ipv4-address-charge-public-ip-insights/). Rather than passing that fee onto our customers Supavisor can mediate connections from IPv4 to IPv6.","level":1,"lines":[92,93],"children":[{"type":"text","content":"Effective February 1, 2024 ","level":0},{"type":"link_open","href":"https://aws.amazon.com/blogs/aws/new-aws-public-ipv4-address-charge-public-ip-insights/","title":"","level":0},{"type":"text","content":"AWS is charging for all allocated IPV4 addresses","level":1},{"type":"link_close","level":0},{"type":"text","content":". Rather than passing that fee onto our customers Supavisor can mediate connections from IPv4 to IPv6.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[94,95],"level":0},{"type":"inline","content":"If you're using the PgBouncer connection string and haven't migrated to the new Supavisor connection string make sure to do this before January 15th, 2024.","level":1,"lines":[94,95],"children":[{"type":"text","content":"If you're using the PgBouncer connection string and haven't migrated to the new Supavisor connection string make sure to do this before January 15th, 2024.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[96,97],"level":0},{"type":"inline","content":"[Getting started](#getting-started)","level":1,"lines":[96,97],"children":[{"type":"text","content":"Getting started","level":0}],"lvl":2,"i":8,"seen":0,"slug":"getting-started"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[98,99],"level":0},{"type":"inline","content":"If you're using the Supabase platform, you can already access the pooler URL in your [project connect page](https://supabase.com/dashboard/project/_?showConnect=true).","level":1,"lines":[98,99],"children":[{"type":"text","content":"If you're using the Supabase platform, you can already access the pooler URL in your ","level":0},{"type":"link_open","href":"https://supabase.com/dashboard/project/_?showConnect=true","title":"","level":0},{"type":"text","content":"project connect page","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[100,101],"level":0},{"type":"inline","content":"If you're looking to self-host Supavisor, check out [GitHub repository](https://github.com/supabase/supavisor) and [documentation](https://supabase.github.io/supavisor/).","level":1,"lines":[100,101],"children":[{"type":"text","content":"If you're looking to self-host Supavisor, check out ","level":0},{"type":"link_open","href":"https://github.com/supabase/supavisor","title":"","level":0},{"type":"text","content":"GitHub repository","level":1},{"type":"link_close","level":0},{"type":"text","content":" and ","level":0},{"type":"link_open","href":"https://supabase.github.io/supavisor/","title":"","level":0},{"type":"text","content":"documentation","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[102,103],"level":0},{"type":"inline","content":"You can expect Supavisor 1.0 to hit the platform next week along with the new pooling configuration changes. If you've set a custom pooler configuration, or we've set one for you, your settings won't change.","level":1,"lines":[102,103],"children":[{"type":"text","content":"You can expect Supavisor 1.0 to hit the platform next week along with the new pooling configuration changes. If you've set a custom pooler configuration, or we've set one for you, your settings won't change.","level":0}]},{"type":"paragraph_close","tight":false,"level":0}],"content":"- [What is connection pooling?](#what-is-connection-pooling)\n- [SQL parsing with Rust](#sql-parsing-with-rust)\n- [Load Balancing](#load-balancing)\n  * [Read-after-writes](#read-after-writes)\n- [Named Prepared Statements](#named-prepared-statements)\n- [Query Cancelation](#query-cancelation)\n- [Platform Updates](#platform-updates)\n  * [IPv4 Deprecation](#ipv4-deprecation)\n- [Getting started](#getting-started)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supavisor-postgres-connection-pooler"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>