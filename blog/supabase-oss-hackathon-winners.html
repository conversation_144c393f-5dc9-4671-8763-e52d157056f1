<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Open Source Hackathon 2024 winners</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Announcing the winners of the Open Source Hackathon 2024!" data-next-head=""/><meta property="og:title" content="Open Source Hackathon 2024 winners" data-next-head=""/><meta property="og:description" content="Announcing the winners of the Open Source Hackathon 2024!" data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-oss-hackathon-winners" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2024-04-30" data-next-head=""/><meta property="article:author" content="https://twitter.com/dshukertjr" data-next-head=""/><meta property="article:tag" content="launch-week" data-next-head=""/><meta property="article:tag" content="hackathon" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/oss-hackathon-winners/thumbnail.png" data-next-head=""/><meta property="og:image:alt" content="Open Source Hackathon 2024 winners thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" href="https://supabase.com/images/launchweek/ga/ga-black.svg" as="image" data-next-head=""/><link rel="preload" href="https://supabase.com/images/launchweek/ga/ga-white.svg" as="image" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Open Source Hackathon 2024 winners</h1><div class="text-light flex space-x-3 text-sm"><p>30 Apr 2024</p><p>•</p><p>4 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://twitter.com/dshukertjr"><div class="flex items-center gap-3"><div class="w-10"><img alt="Tyler Shukert avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fdshukertjr.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fdshukertjr.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fdshukertjr.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Tyler Shukert</span><span class="text-foreground-lighter mb-0 text-xs">DevRel</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Open Source Hackathon 2024 winners" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Foss-hackathon-winners%2Fthumbnail.png&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Foss-hackathon-winners%2Fthumbnail.png&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Foss-hackathon-winners%2Fthumbnail.png&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Foss-hackathon-winners%2Fthumbnail.png&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Foss-hackathon-winners%2Fthumbnail.png&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Foss-hackathon-winners%2Fthumbnail.png&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Foss-hackathon-winners%2Fthumbnail.png&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Foss-hackathon-winners%2Fthumbnail.png&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Foss-hackathon-winners%2Fthumbnail.png&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Foss-hackathon-winners%2Fthumbnail.png&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Foss-hackathon-winners%2Fthumbnail.png&amp;w=3840&amp;q=100"/></div><p>The <a href="https://supabase.com/ga-week">Supabase GA week</a> brought us so many cool updates, but the fun isn’t over yet, because we are now announcing the winners of the <a href="https://supabase.com/blog/supabase-oss-hackathon">Supabase Open Source Hackathon 2024</a>!</p>
<p>We have enjoyed trying out every single submission we have received! You can find all of the submissions on <a href="https://www.madewithsupabase.com/">madewithsupabase.com</a>.</p>
<p>Now, without further ado, let us announce the winners of the hackathon!</p>
<h2 id="best-overall-project" class="group scroll-mt-24">Best overall project<a href="#best-overall-project" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<h3 id="winner" class="group scroll-mt-24">Winner<a href="#winner" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p><a href="https://github.com/xavimondev/vdbs">vdbs - vision database sql</a> - by <a href="https://twitter.com/xavimonp">@xavimonp</a></p>
<p>vdbs stands for &quot;vision database SQL&quot;. It allows you to convert your database diagrams into SQL Schema using the capabilities of Vision API. Once the SQL is ready, you can either copy and paste it right in your Supabase project or run the generated npm command to create the migration file.</p>
<p></p>
<h3 id="runner-up" class="group scroll-mt-24">Runner Up<a href="#runner-up" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p><a href="https://github.com/gSUz92nc/Complete-Components/">Complete Components</a> - by <a href="https://github.com/gSUz92nc">@gSUz92nc</a></p>
<p>Complete Components is an open-source project that uses AI to assist developers in quickly creating and integrating HTML components with Supabase as the backend and Tailwind CSS for styling. It aims to streamline the development process by leveraging AI to generate tailored HTML components and seamlessly integrate them with Supabase.</p>
<p></p>
<h2 id="best-use-of-ai" class="group scroll-mt-24">Best use of AI<a href="#best-use-of-ai" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<h3 id="winner-1" class="group scroll-mt-24">Winner<a href="#winner-1" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p><a href="https://github.com/ineffablesam/closet-ai#closet-ai--revamp-your-style-effortlessly">ClosetAi | Your virtual closet, endless possibilities.</a> - by <a href="https://twitter.com/samuelP09301972">@samuelP09301972</a>, <a href="https://twitter.com/teamnextvitap">@teamnextvitap</a>, and two others</p>
<p>Closet AI: the ultimate style companion. Upload images, choose your desired topwear or bottomwear, and watch as our cutting-edge AI replaces your outfit instantly! Revolutionize your wardrobe with the power of AI and Supabase!</p>
<p></p>
<h3 id="runner-up-1" class="group scroll-mt-24">Runner Up<a href="#runner-up-1" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p><a href="https://github.com/NeoFoxxo/plottwist">Plot Twist</a> - by <a href="https://twitter.com/NeoFoxMusic">@NeoFoxMusic</a>, <a href="https://twitter.com/bonndubz">@bonndubz</a>, <a href="https://twitter.com/Nidhish18224246">@Nidhish18224246</a> and <a href="https://twitter.com/SlaviKaraslavov">@SlaviKaraslavov</a></p>
<p>Generate interactive stories where your choices shape the narrative. Start with a prompt, and let our custom AI generate the story and choices you can make. Want to see what you would do if you were stranded on an island? Or if you were a cat who is a pro skateboarder? Provide the idea and you can experience these scenarios and affect the story!</p>
<p></p>
<h2 id="most-fun--best-easter-egg" class="group scroll-mt-24">Most fun / best easter egg<a href="#most-fun--best-easter-egg" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<h3 id="winner-2" class="group scroll-mt-24">Winner<a href="#winner-2" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p><a href="https://github.com/Git-Prakhar/npat-game">NamePLAT</a> - by <a href="https://twitter.com/Abhishek_cpp">@Abhishek_cpp</a> and <a href="https://twitter.com/BlackStarGames_">@BlackStarGames_</a></p>
<p>Name Place Animal Thing (NamePLAT) is a fun and challenging game where players compete to quickly identify a name (country) a place, an animal, and a thing that starts with a given letter, but the twist is you get to choose between 4 Images and the fastest finger gets most points. The game is designed to test players&#x27; knowledge, speed, and creativity.</p>
<p></p>
<h3 id="runner-up-2" class="group scroll-mt-24">Runner Up<a href="#runner-up-2" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p><a href="https://github.com/jensen/supapaused">Supapaused</a> - by <a href="https://twitter.com/karljensen">@karljensen</a></p>
<p>Supapaused is an app that allows Supabase users to track their paused Supabase instances. You can view when the projects were paused in a timeline.</p>
<p></p>
<h2 id="most-technically-impressive" class="group scroll-mt-24">Most technically impressive<a href="#most-technically-impressive" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<h3 id="winner-3" class="group scroll-mt-24">Winner<a href="#winner-3" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p><a href="https://github.com/Jcampillo1207/Retoritoughts">Retorithoughts</a> - by <a href="https://twitter.com/Chema12071">@Chema12071</a>, <a href="https://twitter.com/alexf505">@alexf505</a> and <a href="https://twitter.com/Patuqueson">@Patuqueson</a></p>
<p>Retorithoughts is a trivia game that challenges players to determine the chronological order of historical events. It&#x27;s fun and an educational tool that tests and expands your knowledge of history.</p>
<p></p>
<h3 id="runner-up-3" class="group scroll-mt-24">Runner Up<a href="#runner-up-3" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p><a href="https://github.com/sangonz193/data-loom">Data Loom</a> - by <a href="https://github.com/sangonz193">@sangonz193</a></p>
<p>Data Loom aims to provide a hassle-free and secure way to share files between devices. The platform leverages WebRTC technology, ensuring that your files are transferred directly and securely, with no intermediary server access.</p>
<p></p>
<h2 id="most-visually-pleasing" class="group scroll-mt-24">Most visually pleasing<a href="#most-visually-pleasing" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<h3 id="winner-4" class="group scroll-mt-24">Winner<a href="#winner-4" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p><a href="https://github.com/phibersoft/supawriter.git">SupaWriter</a> - by <a href="https://github.com/phibersoft">@phibersoft</a></p>
<p>SupaWriter is a ten-finger typing game that helps you improve your typing speed and accuracy. It also features a leaderboard to see how you are performing compared to others.</p>
<p></p>
<h3 id="runner-up-4" class="group scroll-mt-24">Runner Up<a href="#runner-up-4" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p><a href="https://github.com/laznic/echoes-of-creation">Echoes of Creation</a> - by <a href="https://twitter.com/laznic">@laznic</a> and <a href="https://twitter.com/janireijonen">@janireijonen</a></p>
<p>Echoes of Creation is a digital experience that portrays the excitement and struggles artists have with their creative processes.</p>
<p></p>
<h2 id="the-prizes" class="group scroll-mt-24">The Prizes<a href="#the-prizes" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p></p>
<p>The winner of the best overall project will receive an Apple AirPods, and the winners and the runner-ups in other categories will each receive a Supabase swag kit.</p>
<h2 id="getting-started-guides" class="group scroll-mt-24">Getting Started Guides<a href="#getting-started-guides" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>If you&#x27;re inspired to build, check out some of the latest resources:</p>
<ul>
<li><a href="../docs/guides/getting-started.html">Quick Start Guides</a></li>
<li><a href="../docs/guides/ai.html">AI &amp; Vectors Guide</a></li>
<li><a href="../docs/guides/functions.html">Edge Functions Guide</a></li>
</ul></div></article><div class="w-full border bg-alternative-200 flex flex-col rounded-lg text-foreground-lighter mt-12"><div class="w-full p-4 flex justify-between items-center"><a class="flex items-center gap-1.5 leading-none uppercase text-xs opacity-80 transition-opacity hover:opacity-100" href="https://supabase.com/ga-week"><img alt="GA logo" width="30" height="30" decoding="async" data-nimg="1" class="dark:hidden w-5 aspect-[104/57] h-auto" style="color:transparent" src="https://supabase.com/images/launchweek/ga/ga-black.svg"/><img alt="GA logo" width="30" height="30" decoding="async" data-nimg="1" class="hidden dark:block w-5 aspect-[104/57] h-auto" style="color:transparent" src="https://supabase.com/images/launchweek/ga/ga-white.svg"/><span class="text-foreground tracking-[1px] font-mono">Week</span></a><div class="font-mono uppercase tracking-wide text-xs">15-19 April</div></div><div class="pb-4 border-t p-4"><ul class="flex flex-col gap-2"><ol><a class="group flex py-1 gap-2 hover:text-foreground" href="../ga.html"><span class="shrink-0 text-sm font-mono uppercase leading-6">Day <!-- -->1<!-- --> -</span><span class="leading-6">Supabase is officially launching into General Availability</span></a></ol><ol><a class="group flex py-1 gap-2 hover:text-foreground" href="https://supabase.com/blog/ai-inference-now-available-in-supabase-edge-functions"><span class="shrink-0 text-sm font-mono uppercase leading-6">Day <!-- -->2<!-- --> -</span><span class="leading-6">Supabase Functions now supports AI models</span></a></ol><ol><a class="group flex py-1 gap-2 hover:text-foreground" href="https://supabase.com/blog/anonymous-sign-ins"><span class="shrink-0 text-sm font-mono uppercase leading-6">Day <!-- -->3<!-- --> -</span><span class="leading-6">Supabase Auth now supports Anonymous sign-ins</span></a></ol><ol><a class="group flex py-1 gap-2 hover:text-foreground" href="https://supabase.com/blog/s3-compatible-storage"><span class="shrink-0 text-sm font-mono uppercase leading-6">Day <!-- -->4<!-- --> -</span><span class="leading-6">Supabase Storage: now supports the S3 protocol</span></a></ol><ol><a class="group flex py-1 gap-2 hover:text-foreground" href="https://supabase.com/blog/security-performance-advisor"><span class="shrink-0 text-sm font-mono uppercase leading-6">Day <!-- -->5<!-- --> -</span><span class="leading-6">Supabase Security Advisor &amp; Performance Advisor</span></a></ol></ul></div><div class="w-[calc(100%+2px)] bg-surface-100 flex flex-col gap-2 -m-px border rounded-lg"><div class="p-4"><div class="font-mono uppercase text-xs text-foreground tracking-wide">Build Stage</div><ul class="flex flex-col gap-2 mt-4"><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="https://github.com/supabase/index_advisor"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->1<!-- --> -</span>PostgreSQL Index Advisor</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="https://supabase.com/blog/branching-publicly-available"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->2<!-- --> -</span>Branching now Publicly Available</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="https://supabase.com/blog/supabase-acquires-oriole"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->3<!-- --> -</span>Oriole joins Supabase</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="https://supabase.com/blog/supabase-aws-marketplace"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->4<!-- --> -</span>Supabase on AWS Marketplace</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="https://supabase.com/blog/supabase-bootstrap"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->5<!-- --> -</span>Supabase Bootstrap</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="https://supabase.com/blog/supabase-swift"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->6<!-- --> -</span>Supabase Swift</span></a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="https://supabase.com/blog/ga-week-summary"><span class="relative"><span class="font-mono uppercase mr-2">0<!-- -->7<!-- --> -</span>Top 10 Launches from Supabase GA Week</span></a></ol><ol class="border-t pt-4 mt-2"><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="https://supabase.com/blog/supabase-oss-hackathon">Open Source Hackathon 2024</a></ol><ol><a class="relative flex items-center justify-between group w-full py-1 hover:text-foreground" href="https://supabase.com/ga-week#meetups">Community Meetups</a></ol></ul></div></div></div><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-oss-hackathon-winners&amp;text=Open%20Source%20Hackathon%202024%20winners"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-oss-hackathon-winners&amp;text=Open%20Source%20Hackathon%202024%20winners"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-oss-hackathon-winners&amp;t=Open%20Source%20Hackathon%202024%20winners"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="pgvector-0-7-0.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">What&#x27;s new in pgvector v0.7.0</h4><p class="small">2 May 2024</p></div></div></div></div></a></div><div><a href="https://supabase.com/blog/postgres-bloat"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Postgres Bloat Minimization</h4><p class="small">26 April 2024</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/launch-week"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">launch-week</div></a><a href="https://supabase.com/blog/tags/hackathon"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">hackathon</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#best-overall-project">Best overall project</a></li>
<li><a href="#best-use-of-ai">Best use of AI</a></li>
<li><a href="#most-fun--best-easter-egg">Most fun / best easter egg</a></li>
<li><a href="#most-technically-impressive">Most technically impressive</a></li>
<li><a href="#most-visually-pleasing">Most visually pleasing</a></li>
<li><a href="#the-prizes">The Prizes</a></li>
<li><a href="#getting-started-guides">Getting Started Guides</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-oss-hackathon-winners&amp;text=Open%20Source%20Hackathon%202024%20winners"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-oss-hackathon-winners&amp;text=Open%20Source%20Hackathon%202024%20winners"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-oss-hackathon-winners&amp;t=Open%20Source%20Hackathon%202024%20winners"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"pgvector-0-7-0","title":"What's new in pgvector v0.7.0","description":"Exploring new features in pgvector v0.7.0","author":"pavel","image":"pgvector-0-7-0.png","thumb":"pgvector-0-7-0.png","categories":["engineering"],"tags":["supabase-engineering","planetpg"],"date":"2024-05-02","toc_depth":3,"formattedDate":"2 May 2024","readingTime":"8 minute read","url":"/blog/pgvector-0-7-0","path":"/blog/pgvector-0-7-0"},"nextPost":{"slug":"postgres-bloat","title":"Postgres Bloat Minimization","description":"Understanding and minimizing Postgres table bloat","author":"pavel","image":"postgres-bloat.png","thumb":"postgres-bloat.png","categories":["engineering"],"tags":["supabase-engineering","planetpg"],"date":"2024-04-26","toc_depth":3,"formattedDate":"26 April 2024","readingTime":"7 minute read","url":"/blog/postgres-bloat","path":"/blog/postgres-bloat"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-oss-hackathon-winners","source":"\nThe [Supabase GA week](https://supabase.com/ga-week) brought us so many cool updates, but the fun isn’t over yet, because we are now announcing the winners of the [Supabase Open Source Hackathon 2024](https://supabase.com/blog/supabase-oss-hackathon)!\n\nWe have enjoyed trying out every single submission we have received! You can find all of the submissions on [madewithsupabase.com](https://www.madewithsupabase.com/).\n\nNow, without further ado, let us announce the winners of the hackathon!\n\n## Best overall project\n\n### Winner\n\n[vdbs - vision database sql](https://github.com/xavimondev/vdbs) - by [@xavimonp](https://twitter.com/xavimonp)\n\nvdbs stands for \"vision database SQL\". It allows you to convert your database diagrams into SQL Schema using the capabilities of Vision API. Once the SQL is ready, you can either copy and paste it right in your Supabase project or run the generated npm command to create the migration file.\n\n![vdbs](/images/blog/oss-hackathon-winners/vdbs.png)\n\n### Runner Up\n\n[Complete Components](https://github.com/gSUz92nc/Complete-Components/) - by [@gSUz92nc](https://github.com/gSUz92nc)\n\nComplete Components is an open-source project that uses AI to assist developers in quickly creating and integrating HTML components with Supabase as the backend and Tailwind CSS for styling. It aims to streamline the development process by leveraging AI to generate tailored HTML components and seamlessly integrate them with Supabase.\n\n![Complete Components](/images/blog/oss-hackathon-winners/complete-components.png)\n\n## Best use of AI\n\n### Winner\n\n[ClosetAi | Your virtual closet, endless possibilities.](https://github.com/ineffablesam/closet-ai#closet-ai--revamp-your-style-effortlessly) - by [@samuelP09301972](https://twitter.com/samuelP09301972), [@teamnextvitap](https://twitter.com/teamnextvitap), and two others\n\nCloset AI: the ultimate style companion. Upload images, choose your desired topwear or bottomwear, and watch as our cutting-edge AI replaces your outfit instantly! Revolutionize your wardrobe with the power of AI and Supabase!\n\n![ClosetAi](/images/blog/oss-hackathon-winners/closetai.png)\n\n### Runner Up\n\n[Plot Twist](https://github.com/NeoFoxxo/plottwist) - by [@NeoFoxMusic](https://twitter.com/NeoFoxMusic), [@bonndubz](https://twitter.com/bonndubz), [@Nidhish18224246](https://twitter.com/Nidhish18224246) and [@SlaviKaraslavov](https://twitter.com/SlaviKaraslavov)\n\nGenerate interactive stories where your choices shape the narrative. Start with a prompt, and let our custom AI generate the story and choices you can make. Want to see what you would do if you were stranded on an island? Or if you were a cat who is a pro skateboarder? Provide the idea and you can experience these scenarios and affect the story!\n\n![Plot Twist](/images/blog/oss-hackathon-winners/plot-twist.png)\n\n## Most fun / best easter egg\n\n### Winner\n\n[NamePLAT](https://github.com/Git-Prakhar/npat-game) - by [@Abhishek_cpp](https://twitter.com/Abhishek_cpp) and [@BlackStarGames\\_](https://twitter.com/BlackStarGames_)\n\nName Place Animal Thing (NamePLAT) is a fun and challenging game where players compete to quickly identify a name (country) a place, an animal, and a thing that starts with a given letter, but the twist is you get to choose between 4 Images and the fastest finger gets most points. The game is designed to test players' knowledge, speed, and creativity.\n\n![NamePLAT](/images/blog/oss-hackathon-winners/nameplat.png)\n\n### Runner Up\n\n[Supapaused](https://github.com/jensen/supapaused) - by [@karljensen](https://twitter.com/karljensen)\n\nSupapaused is an app that allows Supabase users to track their paused Supabase instances. You can view when the projects were paused in a timeline.\n\n![Supapaused](/images/blog/oss-hackathon-winners/supapaused.png)\n\n## Most technically impressive\n\n### Winner\n\n[Retorithoughts](https://github.com/Jcampillo1207/Retoritoughts) - by [@Chema12071](https://twitter.com/Chema12071), [@alexf505](https://twitter.com/alexf505) and [@Patuqueson](https://twitter.com/Patuqueson)\n\nRetorithoughts is a trivia game that challenges players to determine the chronological order of historical events. It's fun and an educational tool that tests and expands your knowledge of history.\n\n![Retorithoughts](/images/blog/oss-hackathon-winners/retorithoughts.png)\n\n### Runner Up\n\n[Data Loom](https://github.com/sangonz193/data-loom) - by [@sangonz193](https://github.com/sangonz193)\n\nData Loom aims to provide a hassle-free and secure way to share files between devices. The platform leverages WebRTC technology, ensuring that your files are transferred directly and securely, with no intermediary server access.\n\n![Data Loom](/images/blog/oss-hackathon-winners/data-loom.png)\n\n## Most visually pleasing\n\n### Winner\n\n[SupaWriter](https://github.com/phibersoft/supawriter.git) - by [@phibersoft](https://github.com/phibersoft)\n\nSupaWriter is a ten-finger typing game that helps you improve your typing speed and accuracy. It also features a leaderboard to see how you are performing compared to others.\n\n![SupaWriter](/images/blog/oss-hackathon-winners/supawriter.png)\n\n### Runner Up\n\n[Echoes of Creation](https://github.com/laznic/echoes-of-creation) - by [@laznic](https://twitter.com/laznic) and [@janireijonen](https://twitter.com/janireijonen)\n\nEchoes of Creation is a digital experience that portrays the excitement and struggles artists have with their creative processes.\n\n![Echoes of Creation](/images/blog/oss-hackathon-winners/echos-of-creation.png)\n\n## The Prizes\n\n![The prize](/images/blog/oss-hackathon-winners/prize.png)\n\nThe winner of the best overall project will receive an Apple AirPods, and the winners and the runner-ups in other categories will each receive a Supabase swag kit.\n\n## Getting Started Guides\n\nIf you're inspired to build, check out some of the latest resources:\n\n- [Quick Start Guides](https://supabase.com/docs/guides/getting-started)\n- [AI \u0026 Vectors Guide](https://supabase.com/docs/guides/ai)\n- [Edge Functions Guide](https://supabase.com/docs/guides/functions)\n","title":"Open Source Hackathon 2024 winners","description":"Announcing the winners of the Open Source Hackathon 2024!","author":"tyler_shukert","image":"oss-hackathon-winners/thumbnail.png","thumb":"oss-hackathon-winners/thumbnail.png","launchweek":"11","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2024-04-30","toc_depth":2,"content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    a: \"a\",\n    h2: \"h2\",\n    h3: \"h3\",\n    img: \"img\",\n    ul: \"ul\",\n    li: \"li\"\n  }, _provideComponents(), props.components);\n  return _jsxs(_Fragment, {\n    children: [_jsxs(_components.p, {\n      children: [\"The \", _jsx(_components.a, {\n        href: \"https://supabase.com/ga-week\",\n        children: \"Supabase GA week\"\n      }), \" brought us so many cool updates, but the fun isn’t over yet, because we are now announcing the winners of the \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/supabase-oss-hackathon\",\n        children: \"Supabase Open Source Hackathon 2024\"\n      }), \"!\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We have enjoyed trying out every single submission we have received! You can find all of the submissions on \", _jsx(_components.a, {\n        href: \"https://www.madewithsupabase.com/\",\n        children: \"madewithsupabase.com\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Now, without further ado, let us announce the winners of the hackathon!\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"best-overall-project\",\n      children: \"Best overall project\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"winner\",\n      children: \"Winner\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://github.com/xavimondev/vdbs\",\n        children: \"vdbs - vision database sql\"\n      }), \" - by \", _jsx(_components.a, {\n        href: \"https://twitter.com/xavimonp\",\n        children: \"@xavimonp\"\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"vdbs stands for \\\"vision database SQL\\\". It allows you to convert your database diagrams into SQL Schema using the capabilities of Vision API. Once the SQL is ready, you can either copy and paste it right in your Supabase project or run the generated npm command to create the migration file.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/oss-hackathon-winners/vdbs.png\",\n        alt: \"vdbs\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"runner-up\",\n      children: \"Runner Up\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://github.com/gSUz92nc/Complete-Components/\",\n        children: \"Complete Components\"\n      }), \" - by \", _jsx(_components.a, {\n        href: \"https://github.com/gSUz92nc\",\n        children: \"@gSUz92nc\"\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Complete Components is an open-source project that uses AI to assist developers in quickly creating and integrating HTML components with Supabase as the backend and Tailwind CSS for styling. It aims to streamline the development process by leveraging AI to generate tailored HTML components and seamlessly integrate them with Supabase.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/oss-hackathon-winners/complete-components.png\",\n        alt: \"Complete Components\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"best-use-of-ai\",\n      children: \"Best use of AI\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"winner-1\",\n      children: \"Winner\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://github.com/ineffablesam/closet-ai#closet-ai--revamp-your-style-effortlessly\",\n        children: \"ClosetAi | Your virtual closet, endless possibilities.\"\n      }), \" - by \", _jsx(_components.a, {\n        href: \"https://twitter.com/samuelP09301972\",\n        children: \"@samuelP09301972\"\n      }), \", \", _jsx(_components.a, {\n        href: \"https://twitter.com/teamnextvitap\",\n        children: \"@teamnextvitap\"\n      }), \", and two others\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Closet AI: the ultimate style companion. Upload images, choose your desired topwear or bottomwear, and watch as our cutting-edge AI replaces your outfit instantly! Revolutionize your wardrobe with the power of AI and Supabase!\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/oss-hackathon-winners/closetai.png\",\n        alt: \"ClosetAi\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"runner-up-1\",\n      children: \"Runner Up\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://github.com/NeoFoxxo/plottwist\",\n        children: \"Plot Twist\"\n      }), \" - by \", _jsx(_components.a, {\n        href: \"https://twitter.com/NeoFoxMusic\",\n        children: \"@NeoFoxMusic\"\n      }), \", \", _jsx(_components.a, {\n        href: \"https://twitter.com/bonndubz\",\n        children: \"@bonndubz\"\n      }), \", \", _jsx(_components.a, {\n        href: \"https://twitter.com/Nidhish18224246\",\n        children: \"@Nidhish18224246\"\n      }), \" and \", _jsx(_components.a, {\n        href: \"https://twitter.com/SlaviKaraslavov\",\n        children: \"@SlaviKaraslavov\"\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Generate interactive stories where your choices shape the narrative. Start with a prompt, and let our custom AI generate the story and choices you can make. Want to see what you would do if you were stranded on an island? Or if you were a cat who is a pro skateboarder? Provide the idea and you can experience these scenarios and affect the story!\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/oss-hackathon-winners/plot-twist.png\",\n        alt: \"Plot Twist\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"most-fun--best-easter-egg\",\n      children: \"Most fun / best easter egg\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"winner-2\",\n      children: \"Winner\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://github.com/Git-Prakhar/npat-game\",\n        children: \"NamePLAT\"\n      }), \" - by \", _jsx(_components.a, {\n        href: \"https://twitter.com/Abhishek_cpp\",\n        children: \"@Abhishek_cpp\"\n      }), \" and \", _jsx(_components.a, {\n        href: \"https://twitter.com/BlackStarGames_\",\n        children: \"@BlackStarGames_\"\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Name Place Animal Thing (NamePLAT) is a fun and challenging game where players compete to quickly identify a name (country) a place, an animal, and a thing that starts with a given letter, but the twist is you get to choose between 4 Images and the fastest finger gets most points. The game is designed to test players' knowledge, speed, and creativity.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/oss-hackathon-winners/nameplat.png\",\n        alt: \"NamePLAT\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"runner-up-2\",\n      children: \"Runner Up\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://github.com/jensen/supapaused\",\n        children: \"Supapaused\"\n      }), \" - by \", _jsx(_components.a, {\n        href: \"https://twitter.com/karljensen\",\n        children: \"@karljensen\"\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Supapaused is an app that allows Supabase users to track their paused Supabase instances. You can view when the projects were paused in a timeline.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/oss-hackathon-winners/supapaused.png\",\n        alt: \"Supapaused\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"most-technically-impressive\",\n      children: \"Most technically impressive\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"winner-3\",\n      children: \"Winner\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://github.com/Jcampillo1207/Retoritoughts\",\n        children: \"Retorithoughts\"\n      }), \" - by \", _jsx(_components.a, {\n        href: \"https://twitter.com/Chema12071\",\n        children: \"@Chema12071\"\n      }), \", \", _jsx(_components.a, {\n        href: \"https://twitter.com/alexf505\",\n        children: \"@alexf505\"\n      }), \" and \", _jsx(_components.a, {\n        href: \"https://twitter.com/Patuqueson\",\n        children: \"@Patuqueson\"\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Retorithoughts is a trivia game that challenges players to determine the chronological order of historical events. It's fun and an educational tool that tests and expands your knowledge of history.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/oss-hackathon-winners/retorithoughts.png\",\n        alt: \"Retorithoughts\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"runner-up-3\",\n      children: \"Runner Up\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://github.com/sangonz193/data-loom\",\n        children: \"Data Loom\"\n      }), \" - by \", _jsx(_components.a, {\n        href: \"https://github.com/sangonz193\",\n        children: \"@sangonz193\"\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Data Loom aims to provide a hassle-free and secure way to share files between devices. The platform leverages WebRTC technology, ensuring that your files are transferred directly and securely, with no intermediary server access.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/oss-hackathon-winners/data-loom.png\",\n        alt: \"Data Loom\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"most-visually-pleasing\",\n      children: \"Most visually pleasing\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"winner-4\",\n      children: \"Winner\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://github.com/phibersoft/supawriter.git\",\n        children: \"SupaWriter\"\n      }), \" - by \", _jsx(_components.a, {\n        href: \"https://github.com/phibersoft\",\n        children: \"@phibersoft\"\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"SupaWriter is a ten-finger typing game that helps you improve your typing speed and accuracy. It also features a leaderboard to see how you are performing compared to others.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/oss-hackathon-winners/supawriter.png\",\n        alt: \"SupaWriter\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"runner-up-4\",\n      children: \"Runner Up\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://github.com/laznic/echoes-of-creation\",\n        children: \"Echoes of Creation\"\n      }), \" - by \", _jsx(_components.a, {\n        href: \"https://twitter.com/laznic\",\n        children: \"@laznic\"\n      }), \" and \", _jsx(_components.a, {\n        href: \"https://twitter.com/janireijonen\",\n        children: \"@janireijonen\"\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Echoes of Creation is a digital experience that portrays the excitement and struggles artists have with their creative processes.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/oss-hackathon-winners/echos-of-creation.png\",\n        alt: \"Echoes of Creation\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"the-prizes\",\n      children: \"The Prizes\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/oss-hackathon-winners/prize.png\",\n        alt: \"The prize\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The winner of the best overall project will receive an Apple AirPods, and the winners and the runner-ups in other categories will each receive a Supabase swag kit.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"getting-started-guides\",\n      children: \"Getting Started Guides\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"If you're inspired to build, check out some of the latest resources:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/docs/guides/getting-started\",\n          children: \"Quick Start Guides\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/docs/guides/ai\",\n          children: \"AI \u0026 Vectors Guide\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://supabase.com/docs/guides/functions\",\n          children: \"Edge Functions Guide\"\n        })\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"Best overall project","slug":"best-overall-project","lvl":2,"i":0,"seen":0},{"content":"Winner","slug":"winner","lvl":3,"i":1,"seen":0},{"content":"Runner Up","slug":"runner-up","lvl":3,"i":2,"seen":0},{"content":"Best use of AI","slug":"best-use-of-ai","lvl":2,"i":3,"seen":0},{"content":"Winner","slug":"winner-1","lvl":3,"i":4,"seen":1},{"content":"Runner Up","slug":"runner-up-1","lvl":3,"i":5,"seen":1},{"content":"Most fun / best easter egg","slug":"most-fun--best-easter-egg","lvl":2,"i":6,"seen":0},{"content":"Winner","slug":"winner-2","lvl":3,"i":7,"seen":2},{"content":"Runner Up","slug":"runner-up-2","lvl":3,"i":8,"seen":2},{"content":"Most technically impressive","slug":"most-technically-impressive","lvl":2,"i":9,"seen":0},{"content":"Winner","slug":"winner-3","lvl":3,"i":10,"seen":3},{"content":"Runner Up","slug":"runner-up-3","lvl":3,"i":11,"seen":3},{"content":"Most visually pleasing","slug":"most-visually-pleasing","lvl":2,"i":12,"seen":0},{"content":"Winner","slug":"winner-4","lvl":3,"i":13,"seen":4},{"content":"Runner Up","slug":"runner-up-4","lvl":3,"i":14,"seen":4},{"content":"The Prizes","slug":"the-prizes","lvl":2,"i":15,"seen":0},{"content":"Getting Started Guides","slug":"getting-started-guides","lvl":2,"i":16,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,2],"level":0},{"type":"inline","content":"The [Supabase GA week](https://supabase.com/ga-week) brought us so many cool updates, but the fun isn’t over yet, because we are now announcing the winners of the [Supabase Open Source Hackathon 2024](https://supabase.com/blog/supabase-oss-hackathon)!","level":1,"lines":[1,2],"children":[{"type":"text","content":"The ","level":0},{"type":"link_open","href":"https://supabase.com/ga-week","title":"","level":0},{"type":"text","content":"Supabase GA week","level":1},{"type":"link_close","level":0},{"type":"text","content":" brought us so many cool updates, but the fun isn’t over yet, because we are now announcing the winners of the ","level":0},{"type":"link_open","href":"https://supabase.com/blog/supabase-oss-hackathon","title":"","level":0},{"type":"text","content":"Supabase Open Source Hackathon 2024","level":1},{"type":"link_close","level":0},{"type":"text","content":"!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[3,4],"level":0},{"type":"inline","content":"We have enjoyed trying out every single submission we have received! You can find all of the submissions on [madewithsupabase.com](https://www.madewithsupabase.com/).","level":1,"lines":[3,4],"children":[{"type":"text","content":"We have enjoyed trying out every single submission we have received! You can find all of the submissions on ","level":0},{"type":"link_open","href":"https://www.madewithsupabase.com/","title":"","level":0},{"type":"text","content":"madewithsupabase.com","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[5,6],"level":0},{"type":"inline","content":"Now, without further ado, let us announce the winners of the hackathon!","level":1,"lines":[5,6],"children":[{"type":"text","content":"Now, without further ado, let us announce the winners of the hackathon!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[7,8],"level":0},{"type":"inline","content":"[Best overall project](#best-overall-project)","level":1,"lines":[7,8],"children":[{"type":"text","content":"Best overall project","level":0}],"lvl":2,"i":0,"seen":0,"slug":"best-overall-project"},{"type":"heading_close","hLevel":2,"level":0},{"type":"heading_open","hLevel":3,"lines":[9,10],"level":0},{"type":"inline","content":"[Winner](#winner)","level":1,"lines":[9,10],"children":[{"type":"text","content":"Winner","level":0}],"lvl":3,"i":1,"seen":0,"slug":"winner"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[11,12],"level":0},{"type":"inline","content":"[vdbs - vision database sql](https://github.com/xavimondev/vdbs) - by [@xavimonp](https://twitter.com/xavimonp)","level":1,"lines":[11,12],"children":[{"type":"link_open","href":"https://github.com/xavimondev/vdbs","title":"","level":0},{"type":"text","content":"vdbs - vision database sql","level":1},{"type":"link_close","level":0},{"type":"text","content":" - by ","level":0},{"type":"link_open","href":"https://twitter.com/xavimonp","title":"","level":0},{"type":"text","content":"@xavimonp","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[13,14],"level":0},{"type":"inline","content":"vdbs stands for \"vision database SQL\". It allows you to convert your database diagrams into SQL Schema using the capabilities of Vision API. Once the SQL is ready, you can either copy and paste it right in your Supabase project or run the generated npm command to create the migration file.","level":1,"lines":[13,14],"children":[{"type":"text","content":"vdbs stands for \"vision database SQL\". It allows you to convert your database diagrams into SQL Schema using the capabilities of Vision API. Once the SQL is ready, you can either copy and paste it right in your Supabase project or run the generated npm command to create the migration file.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[15,16],"level":0},{"type":"inline","content":"![vdbs](/images/blog/oss-hackathon-winners/vdbs.png)","level":1,"lines":[15,16],"children":[{"type":"image","src":"/images/blog/oss-hackathon-winners/vdbs.png","title":"","alt":"vdbs","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[17,18],"level":0},{"type":"inline","content":"[Runner Up](#runner-up)","level":1,"lines":[17,18],"children":[{"type":"text","content":"Runner Up","level":0}],"lvl":3,"i":2,"seen":0,"slug":"runner-up"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[19,20],"level":0},{"type":"inline","content":"[Complete Components](https://github.com/gSUz92nc/Complete-Components/) - by [@gSUz92nc](https://github.com/gSUz92nc)","level":1,"lines":[19,20],"children":[{"type":"link_open","href":"https://github.com/gSUz92nc/Complete-Components/","title":"","level":0},{"type":"text","content":"Complete Components","level":1},{"type":"link_close","level":0},{"type":"text","content":" - by ","level":0},{"type":"link_open","href":"https://github.com/gSUz92nc","title":"","level":0},{"type":"text","content":"@gSUz92nc","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[21,22],"level":0},{"type":"inline","content":"Complete Components is an open-source project that uses AI to assist developers in quickly creating and integrating HTML components with Supabase as the backend and Tailwind CSS for styling. It aims to streamline the development process by leveraging AI to generate tailored HTML components and seamlessly integrate them with Supabase.","level":1,"lines":[21,22],"children":[{"type":"text","content":"Complete Components is an open-source project that uses AI to assist developers in quickly creating and integrating HTML components with Supabase as the backend and Tailwind CSS for styling. It aims to streamline the development process by leveraging AI to generate tailored HTML components and seamlessly integrate them with Supabase.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[23,24],"level":0},{"type":"inline","content":"![Complete Components](/images/blog/oss-hackathon-winners/complete-components.png)","level":1,"lines":[23,24],"children":[{"type":"image","src":"/images/blog/oss-hackathon-winners/complete-components.png","title":"","alt":"Complete Components","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[25,26],"level":0},{"type":"inline","content":"[Best use of AI](#best-use-of-ai)","level":1,"lines":[25,26],"children":[{"type":"text","content":"Best use of AI","level":0}],"lvl":2,"i":3,"seen":0,"slug":"best-use-of-ai"},{"type":"heading_close","hLevel":2,"level":0},{"type":"heading_open","hLevel":3,"lines":[27,28],"level":0},{"type":"inline","content":"[Winner](#winner-1)","level":1,"lines":[27,28],"children":[{"type":"text","content":"Winner","level":0}],"lvl":3,"i":4,"seen":1,"slug":"winner-1"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[29,30],"level":0},{"type":"inline","content":"[ClosetAi | Your virtual closet, endless possibilities.](https://github.com/ineffablesam/closet-ai#closet-ai--revamp-your-style-effortlessly) - by [@samuelP09301972](https://twitter.com/samuelP09301972), [@teamnextvitap](https://twitter.com/teamnextvitap), and two others","level":1,"lines":[29,30],"children":[{"type":"link_open","href":"https://github.com/ineffablesam/closet-ai#closet-ai--revamp-your-style-effortlessly","title":"","level":0},{"type":"text","content":"ClosetAi | Your virtual closet, endless possibilities.","level":1},{"type":"link_close","level":0},{"type":"text","content":" - by ","level":0},{"type":"link_open","href":"https://twitter.com/samuelP09301972","title":"","level":0},{"type":"text","content":"@samuelP09301972","level":1},{"type":"link_close","level":0},{"type":"text","content":", ","level":0},{"type":"link_open","href":"https://twitter.com/teamnextvitap","title":"","level":0},{"type":"text","content":"@teamnextvitap","level":1},{"type":"link_close","level":0},{"type":"text","content":", and two others","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[31,32],"level":0},{"type":"inline","content":"Closet AI: the ultimate style companion. Upload images, choose your desired topwear or bottomwear, and watch as our cutting-edge AI replaces your outfit instantly! Revolutionize your wardrobe with the power of AI and Supabase!","level":1,"lines":[31,32],"children":[{"type":"text","content":"Closet AI: the ultimate style companion. Upload images, choose your desired topwear or bottomwear, and watch as our cutting-edge AI replaces your outfit instantly! Revolutionize your wardrobe with the power of AI and Supabase!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[33,34],"level":0},{"type":"inline","content":"![ClosetAi](/images/blog/oss-hackathon-winners/closetai.png)","level":1,"lines":[33,34],"children":[{"type":"image","src":"/images/blog/oss-hackathon-winners/closetai.png","title":"","alt":"ClosetAi","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[35,36],"level":0},{"type":"inline","content":"[Runner Up](#runner-up-1)","level":1,"lines":[35,36],"children":[{"type":"text","content":"Runner Up","level":0}],"lvl":3,"i":5,"seen":1,"slug":"runner-up-1"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[37,38],"level":0},{"type":"inline","content":"[Plot Twist](https://github.com/NeoFoxxo/plottwist) - by [@NeoFoxMusic](https://twitter.com/NeoFoxMusic), [@bonndubz](https://twitter.com/bonndubz), [@Nidhish18224246](https://twitter.com/Nidhish18224246) and [@SlaviKaraslavov](https://twitter.com/SlaviKaraslavov)","level":1,"lines":[37,38],"children":[{"type":"link_open","href":"https://github.com/NeoFoxxo/plottwist","title":"","level":0},{"type":"text","content":"Plot Twist","level":1},{"type":"link_close","level":0},{"type":"text","content":" - by ","level":0},{"type":"link_open","href":"https://twitter.com/NeoFoxMusic","title":"","level":0},{"type":"text","content":"@NeoFoxMusic","level":1},{"type":"link_close","level":0},{"type":"text","content":", ","level":0},{"type":"link_open","href":"https://twitter.com/bonndubz","title":"","level":0},{"type":"text","content":"@bonndubz","level":1},{"type":"link_close","level":0},{"type":"text","content":", ","level":0},{"type":"link_open","href":"https://twitter.com/Nidhish18224246","title":"","level":0},{"type":"text","content":"@Nidhish18224246","level":1},{"type":"link_close","level":0},{"type":"text","content":" and ","level":0},{"type":"link_open","href":"https://twitter.com/SlaviKaraslavov","title":"","level":0},{"type":"text","content":"@SlaviKaraslavov","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[39,40],"level":0},{"type":"inline","content":"Generate interactive stories where your choices shape the narrative. Start with a prompt, and let our custom AI generate the story and choices you can make. Want to see what you would do if you were stranded on an island? Or if you were a cat who is a pro skateboarder? Provide the idea and you can experience these scenarios and affect the story!","level":1,"lines":[39,40],"children":[{"type":"text","content":"Generate interactive stories where your choices shape the narrative. Start with a prompt, and let our custom AI generate the story and choices you can make. Want to see what you would do if you were stranded on an island? Or if you were a cat who is a pro skateboarder? Provide the idea and you can experience these scenarios and affect the story!","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[41,42],"level":0},{"type":"inline","content":"![Plot Twist](/images/blog/oss-hackathon-winners/plot-twist.png)","level":1,"lines":[41,42],"children":[{"type":"image","src":"/images/blog/oss-hackathon-winners/plot-twist.png","title":"","alt":"Plot Twist","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[43,44],"level":0},{"type":"inline","content":"[Most fun / best easter egg](#most-fun--best-easter-egg)","level":1,"lines":[43,44],"children":[{"type":"text","content":"Most fun / best easter egg","level":0}],"lvl":2,"i":6,"seen":0,"slug":"most-fun--best-easter-egg"},{"type":"heading_close","hLevel":2,"level":0},{"type":"heading_open","hLevel":3,"lines":[45,46],"level":0},{"type":"inline","content":"[Winner](#winner-2)","level":1,"lines":[45,46],"children":[{"type":"text","content":"Winner","level":0}],"lvl":3,"i":7,"seen":2,"slug":"winner-2"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[47,48],"level":0},{"type":"inline","content":"[NamePLAT](https://github.com/Git-Prakhar/npat-game) - by [@Abhishek_cpp](https://twitter.com/Abhishek_cpp) and [@BlackStarGames\\_](https://twitter.com/BlackStarGames_)","level":1,"lines":[47,48],"children":[{"type":"link_open","href":"https://github.com/Git-Prakhar/npat-game","title":"","level":0},{"type":"text","content":"NamePLAT","level":1},{"type":"link_close","level":0},{"type":"text","content":" - by ","level":0},{"type":"link_open","href":"https://twitter.com/Abhishek_cpp","title":"","level":0},{"type":"text","content":"@Abhishek_cpp","level":1},{"type":"link_close","level":0},{"type":"text","content":" and ","level":0},{"type":"link_open","href":"https://twitter.com/BlackStarGames_","title":"","level":0},{"type":"text","content":"@BlackStarGames_","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[49,50],"level":0},{"type":"inline","content":"Name Place Animal Thing (NamePLAT) is a fun and challenging game where players compete to quickly identify a name (country) a place, an animal, and a thing that starts with a given letter, but the twist is you get to choose between 4 Images and the fastest finger gets most points. The game is designed to test players' knowledge, speed, and creativity.","level":1,"lines":[49,50],"children":[{"type":"text","content":"Name Place Animal Thing (NamePLAT) is a fun and challenging game where players compete to quickly identify a name (country) a place, an animal, and a thing that starts with a given letter, but the twist is you get to choose between 4 Images and the fastest finger gets most points. The game is designed to test players' knowledge, speed, and creativity.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[51,52],"level":0},{"type":"inline","content":"![NamePLAT](/images/blog/oss-hackathon-winners/nameplat.png)","level":1,"lines":[51,52],"children":[{"type":"image","src":"/images/blog/oss-hackathon-winners/nameplat.png","title":"","alt":"NamePLAT","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[53,54],"level":0},{"type":"inline","content":"[Runner Up](#runner-up-2)","level":1,"lines":[53,54],"children":[{"type":"text","content":"Runner Up","level":0}],"lvl":3,"i":8,"seen":2,"slug":"runner-up-2"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[55,56],"level":0},{"type":"inline","content":"[Supapaused](https://github.com/jensen/supapaused) - by [@karljensen](https://twitter.com/karljensen)","level":1,"lines":[55,56],"children":[{"type":"link_open","href":"https://github.com/jensen/supapaused","title":"","level":0},{"type":"text","content":"Supapaused","level":1},{"type":"link_close","level":0},{"type":"text","content":" - by ","level":0},{"type":"link_open","href":"https://twitter.com/karljensen","title":"","level":0},{"type":"text","content":"@karljensen","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[57,58],"level":0},{"type":"inline","content":"Supapaused is an app that allows Supabase users to track their paused Supabase instances. You can view when the projects were paused in a timeline.","level":1,"lines":[57,58],"children":[{"type":"text","content":"Supapaused is an app that allows Supabase users to track their paused Supabase instances. You can view when the projects were paused in a timeline.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[59,60],"level":0},{"type":"inline","content":"![Supapaused](/images/blog/oss-hackathon-winners/supapaused.png)","level":1,"lines":[59,60],"children":[{"type":"image","src":"/images/blog/oss-hackathon-winners/supapaused.png","title":"","alt":"Supapaused","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[61,62],"level":0},{"type":"inline","content":"[Most technically impressive](#most-technically-impressive)","level":1,"lines":[61,62],"children":[{"type":"text","content":"Most technically impressive","level":0}],"lvl":2,"i":9,"seen":0,"slug":"most-technically-impressive"},{"type":"heading_close","hLevel":2,"level":0},{"type":"heading_open","hLevel":3,"lines":[63,64],"level":0},{"type":"inline","content":"[Winner](#winner-3)","level":1,"lines":[63,64],"children":[{"type":"text","content":"Winner","level":0}],"lvl":3,"i":10,"seen":3,"slug":"winner-3"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[65,66],"level":0},{"type":"inline","content":"[Retorithoughts](https://github.com/Jcampillo1207/Retoritoughts) - by [@Chema12071](https://twitter.com/Chema12071), [@alexf505](https://twitter.com/alexf505) and [@Patuqueson](https://twitter.com/Patuqueson)","level":1,"lines":[65,66],"children":[{"type":"link_open","href":"https://github.com/Jcampillo1207/Retoritoughts","title":"","level":0},{"type":"text","content":"Retorithoughts","level":1},{"type":"link_close","level":0},{"type":"text","content":" - by ","level":0},{"type":"link_open","href":"https://twitter.com/Chema12071","title":"","level":0},{"type":"text","content":"@Chema12071","level":1},{"type":"link_close","level":0},{"type":"text","content":", ","level":0},{"type":"link_open","href":"https://twitter.com/alexf505","title":"","level":0},{"type":"text","content":"@alexf505","level":1},{"type":"link_close","level":0},{"type":"text","content":" and ","level":0},{"type":"link_open","href":"https://twitter.com/Patuqueson","title":"","level":0},{"type":"text","content":"@Patuqueson","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[67,68],"level":0},{"type":"inline","content":"Retorithoughts is a trivia game that challenges players to determine the chronological order of historical events. It's fun and an educational tool that tests and expands your knowledge of history.","level":1,"lines":[67,68],"children":[{"type":"text","content":"Retorithoughts is a trivia game that challenges players to determine the chronological order of historical events. It's fun and an educational tool that tests and expands your knowledge of history.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[69,70],"level":0},{"type":"inline","content":"![Retorithoughts](/images/blog/oss-hackathon-winners/retorithoughts.png)","level":1,"lines":[69,70],"children":[{"type":"image","src":"/images/blog/oss-hackathon-winners/retorithoughts.png","title":"","alt":"Retorithoughts","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[71,72],"level":0},{"type":"inline","content":"[Runner Up](#runner-up-3)","level":1,"lines":[71,72],"children":[{"type":"text","content":"Runner Up","level":0}],"lvl":3,"i":11,"seen":3,"slug":"runner-up-3"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[73,74],"level":0},{"type":"inline","content":"[Data Loom](https://github.com/sangonz193/data-loom) - by [@sangonz193](https://github.com/sangonz193)","level":1,"lines":[73,74],"children":[{"type":"link_open","href":"https://github.com/sangonz193/data-loom","title":"","level":0},{"type":"text","content":"Data Loom","level":1},{"type":"link_close","level":0},{"type":"text","content":" - by ","level":0},{"type":"link_open","href":"https://github.com/sangonz193","title":"","level":0},{"type":"text","content":"@sangonz193","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[75,76],"level":0},{"type":"inline","content":"Data Loom aims to provide a hassle-free and secure way to share files between devices. The platform leverages WebRTC technology, ensuring that your files are transferred directly and securely, with no intermediary server access.","level":1,"lines":[75,76],"children":[{"type":"text","content":"Data Loom aims to provide a hassle-free and secure way to share files between devices. The platform leverages WebRTC technology, ensuring that your files are transferred directly and securely, with no intermediary server access.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[77,78],"level":0},{"type":"inline","content":"![Data Loom](/images/blog/oss-hackathon-winners/data-loom.png)","level":1,"lines":[77,78],"children":[{"type":"image","src":"/images/blog/oss-hackathon-winners/data-loom.png","title":"","alt":"Data Loom","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[79,80],"level":0},{"type":"inline","content":"[Most visually pleasing](#most-visually-pleasing)","level":1,"lines":[79,80],"children":[{"type":"text","content":"Most visually pleasing","level":0}],"lvl":2,"i":12,"seen":0,"slug":"most-visually-pleasing"},{"type":"heading_close","hLevel":2,"level":0},{"type":"heading_open","hLevel":3,"lines":[81,82],"level":0},{"type":"inline","content":"[Winner](#winner-4)","level":1,"lines":[81,82],"children":[{"type":"text","content":"Winner","level":0}],"lvl":3,"i":13,"seen":4,"slug":"winner-4"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[83,84],"level":0},{"type":"inline","content":"[SupaWriter](https://github.com/phibersoft/supawriter.git) - by [@phibersoft](https://github.com/phibersoft)","level":1,"lines":[83,84],"children":[{"type":"link_open","href":"https://github.com/phibersoft/supawriter.git","title":"","level":0},{"type":"text","content":"SupaWriter","level":1},{"type":"link_close","level":0},{"type":"text","content":" - by ","level":0},{"type":"link_open","href":"https://github.com/phibersoft","title":"","level":0},{"type":"text","content":"@phibersoft","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[85,86],"level":0},{"type":"inline","content":"SupaWriter is a ten-finger typing game that helps you improve your typing speed and accuracy. It also features a leaderboard to see how you are performing compared to others.","level":1,"lines":[85,86],"children":[{"type":"text","content":"SupaWriter is a ten-finger typing game that helps you improve your typing speed and accuracy. It also features a leaderboard to see how you are performing compared to others.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[87,88],"level":0},{"type":"inline","content":"![SupaWriter](/images/blog/oss-hackathon-winners/supawriter.png)","level":1,"lines":[87,88],"children":[{"type":"image","src":"/images/blog/oss-hackathon-winners/supawriter.png","title":"","alt":"SupaWriter","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[89,90],"level":0},{"type":"inline","content":"[Runner Up](#runner-up-4)","level":1,"lines":[89,90],"children":[{"type":"text","content":"Runner Up","level":0}],"lvl":3,"i":14,"seen":4,"slug":"runner-up-4"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[91,92],"level":0},{"type":"inline","content":"[Echoes of Creation](https://github.com/laznic/echoes-of-creation) - by [@laznic](https://twitter.com/laznic) and [@janireijonen](https://twitter.com/janireijonen)","level":1,"lines":[91,92],"children":[{"type":"link_open","href":"https://github.com/laznic/echoes-of-creation","title":"","level":0},{"type":"text","content":"Echoes of Creation","level":1},{"type":"link_close","level":0},{"type":"text","content":" - by ","level":0},{"type":"link_open","href":"https://twitter.com/laznic","title":"","level":0},{"type":"text","content":"@laznic","level":1},{"type":"link_close","level":0},{"type":"text","content":" and ","level":0},{"type":"link_open","href":"https://twitter.com/janireijonen","title":"","level":0},{"type":"text","content":"@janireijonen","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[93,94],"level":0},{"type":"inline","content":"Echoes of Creation is a digital experience that portrays the excitement and struggles artists have with their creative processes.","level":1,"lines":[93,94],"children":[{"type":"text","content":"Echoes of Creation is a digital experience that portrays the excitement and struggles artists have with their creative processes.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[95,96],"level":0},{"type":"inline","content":"![Echoes of Creation](/images/blog/oss-hackathon-winners/echos-of-creation.png)","level":1,"lines":[95,96],"children":[{"type":"image","src":"/images/blog/oss-hackathon-winners/echos-of-creation.png","title":"","alt":"Echoes of Creation","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[97,98],"level":0},{"type":"inline","content":"[The Prizes](#the-prizes)","level":1,"lines":[97,98],"children":[{"type":"text","content":"The Prizes","level":0}],"lvl":2,"i":15,"seen":0,"slug":"the-prizes"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[99,100],"level":0},{"type":"inline","content":"![The prize](/images/blog/oss-hackathon-winners/prize.png)","level":1,"lines":[99,100],"children":[{"type":"image","src":"/images/blog/oss-hackathon-winners/prize.png","title":"","alt":"The prize","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[101,102],"level":0},{"type":"inline","content":"The winner of the best overall project will receive an Apple AirPods, and the winners and the runner-ups in other categories will each receive a Supabase swag kit.","level":1,"lines":[101,102],"children":[{"type":"text","content":"The winner of the best overall project will receive an Apple AirPods, and the winners and the runner-ups in other categories will each receive a Supabase swag kit.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[103,104],"level":0},{"type":"inline","content":"[Getting Started Guides](#getting-started-guides)","level":1,"lines":[103,104],"children":[{"type":"text","content":"Getting Started Guides","level":0}],"lvl":2,"i":16,"seen":0,"slug":"getting-started-guides"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[105,106],"level":0},{"type":"inline","content":"If you're inspired to build, check out some of the latest resources:","level":1,"lines":[105,106],"children":[{"type":"text","content":"If you're inspired to build, check out some of the latest resources:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[107,110],"level":0},{"type":"list_item_open","lines":[107,108],"level":1},{"type":"paragraph_open","tight":true,"lines":[107,108],"level":2},{"type":"inline","content":"[Quick Start Guides](https://supabase.com/docs/guides/getting-started)","level":3,"lines":[107,108],"children":[{"type":"link_open","href":"https://supabase.com/docs/guides/getting-started","title":"","level":0},{"type":"text","content":"Quick Start Guides","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[108,109],"level":1},{"type":"paragraph_open","tight":true,"lines":[108,109],"level":2},{"type":"inline","content":"[AI \u0026 Vectors Guide](https://supabase.com/docs/guides/ai)","level":3,"lines":[108,109],"children":[{"type":"link_open","href":"https://supabase.com/docs/guides/ai","title":"","level":0},{"type":"text","content":"AI \u0026 Vectors Guide","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[109,110],"level":1},{"type":"paragraph_open","tight":true,"lines":[109,110],"level":2},{"type":"inline","content":"[Edge Functions Guide](https://supabase.com/docs/guides/functions)","level":3,"lines":[109,110],"children":[{"type":"link_open","href":"https://supabase.com/docs/guides/functions","title":"","level":0},{"type":"text","content":"Edge Functions Guide","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0}],"content":"- [Best overall project](#best-overall-project)\n- [Best use of AI](#best-use-of-ai)\n- [Most fun / best easter egg](#most-fun--best-easter-egg)\n- [Most technically impressive](#most-technically-impressive)\n- [Most visually pleasing](#most-visually-pleasing)\n- [The Prizes](#the-prizes)\n- [Getting Started Guides](#getting-started-guides)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-oss-hackathon-winners"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>