<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Visualizing Supabase Data using Metabase</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="How to create different kinds of charts out of data stored in Supabase using Metabase." data-next-head=""/><meta property="og:title" content="Visualizing Supabase Data using Metabase" data-next-head=""/><meta property="og:description" content="How to create different kinds of charts out of data stored in Supabase using Metabase." data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/visualizing-supabase-data-using-metabase" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2022-06-29" data-next-head=""/><meta property="article:author" content="https://github.com/awalias" data-next-head=""/><meta property="article:tag" content="python" data-next-head=""/><meta property="article:tag" content="open-source" data-next-head=""/><meta property="article:tag" content="community" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/python-1/supabase-python-metabase.jpg" data-next-head=""/><meta property="og:image:alt" content="Visualizing Supabase Data using Metabase thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Visualizing Supabase Data using Metabase</h1><div class="text-light flex space-x-3 text-sm"><p>29 Jun 2022</p><p>•</p><p>5 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/awalias"><div class="flex items-center gap-3"><div class="w-10"><img alt="Ant Wilson avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fawalias.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fawalias.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fawalias.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Ant Wilson</span><span class="text-foreground-lighter mb-0 text-xs">CTO and Co-Founder</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Visualizing Supabase Data using Metabase" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fpython-1%2Fsupabase-python-metabase.jpg&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fpython-1%2Fsupabase-python-metabase.jpg&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fpython-1%2Fsupabase-python-metabase.jpg&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fpython-1%2Fsupabase-python-metabase.jpg&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fpython-1%2Fsupabase-python-metabase.jpg&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fpython-1%2Fsupabase-python-metabase.jpg&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fpython-1%2Fsupabase-python-metabase.jpg&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fpython-1%2Fsupabase-python-metabase.jpg&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fpython-1%2Fsupabase-python-metabase.jpg&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fpython-1%2Fsupabase-python-metabase.jpg&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Fpython-1%2Fsupabase-python-metabase.jpg&amp;w=3840&amp;q=100"/></div><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p>Data helps organizations make better decisions. With a programming language like Python to analyze your data and transform data into visual representations, you can effortlessly tell the story of your business. One way to create customized visuals from your data would be to use data visualization libraries in Python like <a href="https://matplotlib.org/">Matplotlib</a>, <a href="https://seaborn.pydata.org/">Seaborn</a>, <a href="https://ggplot2.tidyverse.org/index.html">Ggplot2</a>, <a href="https://plotly.com/">Plotly</a>, or <a href="https://pandas.pydata.org/">Pandas</a>. When you want to accomplish this task with little or no code (not even SQL), you might consider using tools like <a href="https://www.metabase.com/">Metabase</a>.</p>
<p>With Metabase, a powerful visualization tool, you can quickly turn your data into easy-to-understand visuals like graphs, pie charts, flow diagrams, and much more. Then, using Metabase’s intuitive interface, you can cut through the data noise and focus on what’s essential for your business.</p>
<p>In the previous blog of this series, we explained <a href="https://supabase.com/blog/loading-data-supabase-python">how to use Python to load data into Supabase</a>. In this blog, we will create different kinds of charts out of the data stored in Supabase using Metabase.</p>
<h2 id="prerequisites" class="group scroll-mt-24">Prerequisites<a href="#prerequisites" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Before we dive in, let’s look at some prerequisites that you will need:</p>
<ul>
<li><strong>Supabase project with data</strong></li>
</ul>
<p>Based on our <a href="https://supabase.com/blog/loading-data-supabase-python">previous article</a>, we assume we already have a Supabase project setup and have data loaded into it.</p>
<ul>
<li><strong>Metabase Docker Container</strong></li>
</ul>
<p>To take advantage of the open-source version of Metabase, you can use the Metabase docker container <a href="https://hub.docker.com/r/metabase/metabase">here</a>.</p>
<h2 id="visualizing-data-in-supabase-with-metabase" class="group scroll-mt-24">Visualizing data in Supabase with Metabase<a href="#visualizing-data-in-supabase-with-metabase" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<h3 id="launching-metabase" class="group scroll-mt-24">Launching Metabase<a href="#launching-metabase" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>To launch Metabase, simply go to <a href="http://localhost:3000/setup/">http://localhost:3000/setup/</a> which is the default port that the Metabase server will be listening to.</p>
<p>After Metabase is launched, select your preferred language and add your contact information. In the <em>Add your data</em> markdown, you will need to choose PostgreSQL.</p>
<p></p>
<p>You will be prompted to add the necessary connection information to your Supabase project. Go to your <a href="https://supabase.com/dashboard/">Supabase project</a> and hit <em>Settings &gt; Database to get the database info</em>.</p>
<p></p>
<p>Enter the necessary information on Metabase and hit next. Finally, select your data preference, after which you will land on the Metabase homepage.</p>
<h3 id="view-database-and-tables" class="group scroll-mt-24">View Database and Tables<a href="#view-database-and-tables" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We can now see the &quot;Supabase DB&quot; Supabase project under &quot;Our data&quot;.</p>
<p></p>
<p>To view the tables, go to <em>SupabaseDB &gt; public</em></p>
<p></p>
<h3 id="view-table-data-insights" class="group scroll-mt-24">View Table Data Insights<a href="#view-table-data-insights" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Go back to the home page and select public schema under &quot;Try these x-rays based on your data&quot;</p>
<p></p>
<p>Here is the output of the product table.</p>
<p></p>
<p>As you can see, we can get some handy information from this like:</p>
<ul>
<li>How many products are present with a given range of inventory count.</li>
<li>How many products are present for a given range of price.</li>
<li>The ratio between the number of employees to the number of products.</li>
<li>How many products each vendor has created.</li>
</ul>
<p>If you have column-specific views, you can select the <em>zoom-in</em> option under <em>More x-rays</em>.</p>
<p></p>
<p>For example, let&#x27;s select the total employees field.</p>
<p></p>
<p>With information like this, you will be able to answer some key questions like</p>
<ul>
<li>What are some common statistics for company employees like average, minimum, maximum, and standard deviation?</li>
<li>What is the distribution of the employees across different geo locations?</li>
<li>What is the distribution of the employees across different vendors?</li>
</ul>
<h2 id="using-custom-sql-queries" class="group scroll-mt-24">Using custom SQL queries<a href="#using-custom-sql-queries" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We can also use custom queries to set up our dashboards. To do this, go to <em>New &gt; SQL query.</em></p>
<p></p>
<p>Next, under the database, select &quot;SupabaseDB&quot;.</p>
<p></p>
<p>We will be using the following SQL query:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>select &quot;Vendor&quot;.vendor_name, product_name, &quot;Vendor&quot;.total_employees</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>from</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  &quot;Product&quot;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  left join &quot;Vendor&quot; on &quot;Product&quot;.vendor_id = &quot;Vendor&quot;.vendor_id</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>where &quot;Vendor&quot;.total_employees;</span></div></div><br/></code></div></div>
<p>This query should fetch us the vendor name and the product where the number of employees for a given vendor is less than 110.</p>
<p>To run the SQL query, hit the play button.</p>
<p></p>
<p>This will be shown below in the output window. To visualize the data, hit the visualization button.</p>
<p></p>
<p>Next, select the type of visualizer you want. Let us choose <em>Bar</em>.</p>
<p></p>
<p>Choose the appropriate x-axis and y-axis fields, and you will be able to view the data in bar format.</p>
<p></p>
<h2 id="conclusion" class="group scroll-mt-24">Conclusion<a href="#conclusion" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Data visualization empowers organizations to turn unused data into actionable insights, leading to faster and better decision-making. Why wait?</p>
<p></p>
<p>With our <a href="https://supabase.com/dashboard/">Free Plan Supabase account</a>, you can start a new project today and use Metabase to visualize your app data.</p>
<p>If you have any questions please reach out via <a href="https://twitter.com/supabase">Twitter</a> or join our <a href="https://discord.supabase.com">Discord</a>.</p>
<h2 id="more-python-and-supabase-resources" class="group scroll-mt-24">More Python and Supabase resources<a href="#more-python-and-supabase-resources" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<ul>
<li><a href="https://github.com/supabase-community/supabase-py">supabase-py</a></li>
<li><a href="slack-consolidate-slackbot-to-consolidate-messages.html">Slack Consolidate: a slackbot built with Python and Supabase</a></li>
<li><a href="https://replit.com/@Supabase/Supabase-py-Database?v=1">Supabase-py (Database) on Replit</a></li>
</ul></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fvisualizing-supabase-data-using-metabase&amp;text=Visualizing%20Supabase%20Data%20using%20Metabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fvisualizing-supabase-data-using-metabase&amp;text=Visualizing%20Supabase%20Data%20using%20Metabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fvisualizing-supabase-data-using-metabase&amp;t=Visualizing%20Supabase%20Data%20using%20Metabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="flutter-tutorial-building-a-chat-app.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Flutter Tutorial: building a Flutter chat app</h4><p class="small">30 June 2022</p></div></div></div></div></a></div><div><a href="https://supabase.com/blog/partial-postgresql-data-dumps-with-rls"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Partial data dumps using Postgres Row Level Security</h4><p class="small">28 June 2022</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/python"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">python</div></a><a href="https://supabase.com/blog/tags/open-source"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">open-source</div></a><a href="https://supabase.com/blog/tags/community"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">community</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#prerequisites">Prerequisites</a></li>
<li><a href="#visualizing-data-in-supabase-with-metabase">Visualizing data in Supabase with Metabase</a>
<ul>
<li><a href="#launching-metabase">Launching Metabase</a></li>
<li><a href="#view-database-and-tables">View Database and Tables</a></li>
<li><a href="#view-table-data-insights">View Table Data Insights</a></li>
</ul>
</li>
<li><a href="#using-custom-sql-queries">Using custom SQL queries</a></li>
<li><a href="#conclusion">Conclusion</a></li>
<li><a href="#more-python-and-supabase-resources">More Python and Supabase resources</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fvisualizing-supabase-data-using-metabase&amp;text=Visualizing%20Supabase%20Data%20using%20Metabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fvisualizing-supabase-data-using-metabase&amp;text=Visualizing%20Supabase%20Data%20using%20Metabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fvisualizing-supabase-data-using-metabase&amp;t=Visualizing%20Supabase%20Data%20using%20Metabase"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"flutter-tutorial-building-a-chat-app","title":"Flutter Tutorial: building a Flutter chat app","description":"Learn how to build a Flutter chat app with open source and scalable backend (inc. auth, realtime, database, and more).","author":"tyler_shukert","image":"flutter-chat/flutter-chat-app.jpg","thumb":"flutter-chat/flutter-chat-app.jpg","categories":["engineering"],"tags":["flutter","realtime","mobile"],"date":"2022-06-30","toc_depth":3,"formattedDate":"30 June 2022","readingTime":"39 minute read","url":"/blog/flutter-tutorial-building-a-chat-app","path":"/blog/flutter-tutorial-building-a-chat-app"},"nextPost":{"slug":"partial-postgresql-data-dumps-with-rls","title":"Partial data dumps using Postgres Row Level Security","description":"Using RLS to create seed files for local PostgreSQL testing.","author":"paul_copplestone","image":"partial-dumps/og-partial-dumps-with-rls.png","thumb":"partial-dumps/og-partial-dumps-with-rls.png","categories":["postgres"],"tags":["postgresql","data","planetpg"],"date":"2022-06-28","toc_depth":3,"formattedDate":"28 June 2022","readingTime":"6 minute read","url":"/blog/partial-postgresql-data-dumps-with-rls","path":"/blog/partial-postgresql-data-dumps-with-rls"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"visualizing-supabase-data-using-metabase","source":"\nData helps organizations make better decisions. With a programming language like Python to analyze your data and transform data into visual representations, you can effortlessly tell the story of your business. One way to create customized visuals from your data would be to use data visualization libraries in Python like [Matplotlib](https://matplotlib.org/), [Seaborn](https://seaborn.pydata.org/), [Ggplot2](https://ggplot2.tidyverse.org/index.html), [Plotly](https://plotly.com/), or [Pandas](https://pandas.pydata.org/). When you want to accomplish this task with little or no code (not even SQL), you might consider using tools like [Metabase](https://www.metabase.com/).\n\nWith Metabase, a powerful visualization tool, you can quickly turn your data into easy-to-understand visuals like graphs, pie charts, flow diagrams, and much more. Then, using Metabase’s intuitive interface, you can cut through the data noise and focus on what’s essential for your business.\n\nIn the previous blog of this series, we explained [how to use Python to load data into Supabase](https://supabase.com/blog/loading-data-supabase-python). In this blog, we will create different kinds of charts out of the data stored in Supabase using Metabase.\n\n## Prerequisites\n\nBefore we dive in, let’s look at some prerequisites that you will need:\n\n- **Supabase project with data**\n\nBased on our [previous article](https://supabase.com/blog/loading-data-supabase-python), we assume we already have a Supabase project setup and have data loaded into it.\n\n- **Metabase Docker Container**\n\nTo take advantage of the open-source version of Metabase, you can use the Metabase docker container [here](https://hub.docker.com/r/metabase/metabase).\n\n## Visualizing data in Supabase with Metabase\n\n### Launching Metabase\n\nTo launch Metabase, simply go to [http://localhost:3000/setup/](http://localhost:3000/setup/) which is the default port that the Metabase server will be listening to.\n\nAfter Metabase is launched, select your preferred language and add your contact information. In the _Add your data_ markdown, you will need to choose PostgreSQL.\n\n![screen shot of adding postgres data to metabase](/images/blog/python-1/adding-postgresql-data-metabase.png)\n\nYou will be prompted to add the necessary connection information to your Supabase project. Go to your [Supabase project](https://supabase.com/dashboard/) and hit _Settings \u003e Database to get the database info_.\n\n![screen shot of supabase dashboard database connection information](/images/blog/python-1/supabase-dashboard-connect-database-info.png)\n\nEnter the necessary information on Metabase and hit next. Finally, select your data preference, after which you will land on the Metabase homepage.\n\n### View Database and Tables\n\nWe can now see the \"Supabase DB\" Supabase project under \"Our data\".\n\n![screen shot of metabase dashboard](/images/blog/python-1/metabase-dashboard-with-supabase-db.png)\n\nTo view the tables, go to _SupabaseDB \u003e public_\n\n![screen shot of metabase dashboard table view](/images/blog/python-1/metabase-dashboard-02.png)\n\n### View Table Data Insights\n\nGo back to the home page and select public schema under \"Try these x-rays based on your data\"\n\n![screen shot of metabase dashboard schema view](/images/blog/python-1/metabase-dashboard-03.png)\n\nHere is the output of the product table.\n\n![screen shot of metabase data visualization](/images/blog/python-1/metabase-visualization-supabase-db.png)\n\nAs you can see, we can get some handy information from this like:\n\n- How many products are present with a given range of inventory count.\n- How many products are present for a given range of price.\n- The ratio between the number of employees to the number of products.\n- How many products each vendor has created.\n\nIf you have column-specific views, you can select the _zoom-in_ option under _More x-rays_.\n\n![screen shot of metabase data visualization](/images/blog/python-1/metabase-visualization-supabase-db-02.png)\n\nFor example, let's select the total employees field.\n\n![screen shot of metabase data visualization](/images/blog/python-1/metabase-visualization-supabase-data.png)\n\nWith information like this, you will be able to answer some key questions like\n\n- What are some common statistics for company employees like average, minimum, maximum, and standard deviation?\n- What is the distribution of the employees across different geo locations?\n- What is the distribution of the employees across different vendors?\n\n## Using custom SQL queries\n\nWe can also use custom queries to set up our dashboards. To do this, go to _New \u003e SQL query._\n\n![screen shot of metabase custom queries dashboard](/images/blog/python-1/metabase-dashboard-04.png)\n\nNext, under the database, select \"SupabaseDB\".\n\n![screen shot of metabase custom queries dashboard](/images/blog/python-1/metabase-dashboard-05.png)\n\nWe will be using the following SQL query:\n\n```sql\nselect \"Vendor\".vendor_name, product_name, \"Vendor\".total_employees\nfrom\n  \"Product\"\n  left join \"Vendor\" on \"Product\".vendor_id = \"Vendor\".vendor_id\nwhere \"Vendor\".total_employees;\n```\n\nThis query should fetch us the vendor name and the product where the number of employees for a given vendor is less than 110.\n\nTo run the SQL query, hit the play button.\n\n![screen shot of metabase data visualization](/images/blog/python-1/metabase-dashboard-06.png)\n\nThis will be shown below in the output window. To visualize the data, hit the visualization button.\n\n![screen shot of metabase data visualization](/images/blog/python-1/metabase-dashboard-07.png)\n\nNext, select the type of visualizer you want. Let us choose _Bar_.\n\n![screen shot of metabase data visualization](/images/blog/python-1/metabase-dashboard-08.png)\n\nChoose the appropriate x-axis and y-axis fields, and you will be able to view the data in bar format.\n\n![screen shot of metabase data visualization](/images/blog/python-1/metabase-report.png)\n\n## Conclusion\n\nData visualization empowers organizations to turn unused data into actionable insights, leading to faster and better decision-making. Why wait?\n\n![screen shot of a meme saying visualization works every time](/images/blog/python-1/final-meme.png)\n\nWith our [Free Plan Supabase account](https://supabase.com/dashboard/), you can start a new project today and use Metabase to visualize your app data.\n\nIf you have any questions please reach out via [Twitter](https://twitter.com/supabase) or join our [Discord](https://discord.supabase.com).\n\n## More Python and Supabase resources\n\n- [supabase-py](https://github.com/supabase-community/supabase-py)\n- [Slack Consolidate: a slackbot built with Python and Supabase](slack-consolidate-slackbot-to-consolidate-messages)\n- [Supabase-py (Database) on Replit](https://replit.com/@Supabase/Supabase-py-Database?v=1)\n","title":"Visualizing Supabase Data using Metabase","description":"How to create different kinds of charts out of data stored in Supabase using Metabase.","author":"ant_wilson","image":"python-1/supabase-python-metabase.jpg","thumb":"python-1/supabase-python-metabase.jpg","categories":["engineering"],"tags":["python","open-source","community"],"date":"2022-06-29","toc_depth":3,"content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    a: \"a\",\n    h2: \"h2\",\n    ul: \"ul\",\n    li: \"li\",\n    strong: \"strong\",\n    h3: \"h3\",\n    em: \"em\",\n    img: \"img\"\n  }, _provideComponents(), props.components), {CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Data helps organizations make better decisions. With a programming language like Python to analyze your data and transform data into visual representations, you can effortlessly tell the story of your business. One way to create customized visuals from your data would be to use data visualization libraries in Python like \", _jsx(_components.a, {\n        href: \"https://matplotlib.org/\",\n        children: \"Matplotlib\"\n      }), \", \", _jsx(_components.a, {\n        href: \"https://seaborn.pydata.org/\",\n        children: \"Seaborn\"\n      }), \", \", _jsx(_components.a, {\n        href: \"https://ggplot2.tidyverse.org/index.html\",\n        children: \"Ggplot2\"\n      }), \", \", _jsx(_components.a, {\n        href: \"https://plotly.com/\",\n        children: \"Plotly\"\n      }), \", or \", _jsx(_components.a, {\n        href: \"https://pandas.pydata.org/\",\n        children: \"Pandas\"\n      }), \". When you want to accomplish this task with little or no code (not even SQL), you might consider using tools like \", _jsx(_components.a, {\n        href: \"https://www.metabase.com/\",\n        children: \"Metabase\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"With Metabase, a powerful visualization tool, you can quickly turn your data into easy-to-understand visuals like graphs, pie charts, flow diagrams, and much more. Then, using Metabase’s intuitive interface, you can cut through the data noise and focus on what’s essential for your business.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In the previous blog of this series, we explained \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/loading-data-supabase-python\",\n        children: \"how to use Python to load data into Supabase\"\n      }), \". In this blog, we will create different kinds of charts out of the data stored in Supabase using Metabase.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"prerequisites\",\n      children: \"Prerequisites\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Before we dive in, let’s look at some prerequisites that you will need:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.strong, {\n          children: \"Supabase project with data\"\n        })\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Based on our \", _jsx(_components.a, {\n        href: \"https://supabase.com/blog/loading-data-supabase-python\",\n        children: \"previous article\"\n      }), \", we assume we already have a Supabase project setup and have data loaded into it.\"]\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.strong, {\n          children: \"Metabase Docker Container\"\n        })\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To take advantage of the open-source version of Metabase, you can use the Metabase docker container \", _jsx(_components.a, {\n        href: \"https://hub.docker.com/r/metabase/metabase\",\n        children: \"here\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"visualizing-data-in-supabase-with-metabase\",\n      children: \"Visualizing data in Supabase with Metabase\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"launching-metabase\",\n      children: \"Launching Metabase\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To launch Metabase, simply go to \", _jsx(_components.a, {\n        href: \"http://localhost:3000/setup/\",\n        children: \"http://localhost:3000/setup/\"\n      }), \" which is the default port that the Metabase server will be listening to.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"After Metabase is launched, select your preferred language and add your contact information. In the \", _jsx(_components.em, {\n        children: \"Add your data\"\n      }), \" markdown, you will need to choose PostgreSQL.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/python-1/adding-postgresql-data-metabase.png\",\n        alt: \"screen shot of adding postgres data to metabase\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You will be prompted to add the necessary connection information to your Supabase project. Go to your \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard/\",\n        children: \"Supabase project\"\n      }), \" and hit \", _jsx(_components.em, {\n        children: \"Settings \u003e Database to get the database info\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/python-1/supabase-dashboard-connect-database-info.png\",\n        alt: \"screen shot of supabase dashboard database connection information\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Enter the necessary information on Metabase and hit next. Finally, select your data preference, after which you will land on the Metabase homepage.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"view-database-and-tables\",\n      children: \"View Database and Tables\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We can now see the \\\"Supabase DB\\\" Supabase project under \\\"Our data\\\".\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/python-1/metabase-dashboard-with-supabase-db.png\",\n        alt: \"screen shot of metabase dashboard\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To view the tables, go to \", _jsx(_components.em, {\n        children: \"SupabaseDB \u003e public\"\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/python-1/metabase-dashboard-02.png\",\n        alt: \"screen shot of metabase dashboard table view\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"view-table-data-insights\",\n      children: \"View Table Data Insights\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Go back to the home page and select public schema under \\\"Try these x-rays based on your data\\\"\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/python-1/metabase-dashboard-03.png\",\n        alt: \"screen shot of metabase dashboard schema view\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Here is the output of the product table.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/python-1/metabase-visualization-supabase-db.png\",\n        alt: \"screen shot of metabase data visualization\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"As you can see, we can get some handy information from this like:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"How many products are present with a given range of inventory count.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"How many products are present for a given range of price.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"The ratio between the number of employees to the number of products.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"How many products each vendor has created.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you have column-specific views, you can select the \", _jsx(_components.em, {\n        children: \"zoom-in\"\n      }), \" option under \", _jsx(_components.em, {\n        children: \"More x-rays\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/python-1/metabase-visualization-supabase-db-02.png\",\n        alt: \"screen shot of metabase data visualization\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"For example, let's select the total employees field.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/python-1/metabase-visualization-supabase-data.png\",\n        alt: \"screen shot of metabase data visualization\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"With information like this, you will be able to answer some key questions like\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"What are some common statistics for company employees like average, minimum, maximum, and standard deviation?\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"What is the distribution of the employees across different geo locations?\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"What is the distribution of the employees across different vendors?\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"using-custom-sql-queries\",\n      children: \"Using custom SQL queries\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We can also use custom queries to set up our dashboards. To do this, go to \", _jsx(_components.em, {\n        children: \"New \u003e SQL query.\"\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/python-1/metabase-dashboard-04.png\",\n        alt: \"screen shot of metabase custom queries dashboard\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Next, under the database, select \\\"SupabaseDB\\\".\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/python-1/metabase-dashboard-05.png\",\n        alt: \"screen shot of metabase custom queries dashboard\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We will be using the following SQL query:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"select \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"Vendor\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \".vendor_name, product_name, \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"Vendor\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \".total_employees\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"from\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"Product\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  left join \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"Vendor\\\" \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"on \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"Product\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \".vendor_id \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"Vendor\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \".vendor_id\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"where \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"Vendor\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \".total_employees;\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This query should fetch us the vendor name and the product where the number of employees for a given vendor is less than 110.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To run the SQL query, hit the play button.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/python-1/metabase-dashboard-06.png\",\n        alt: \"screen shot of metabase data visualization\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This will be shown below in the output window. To visualize the data, hit the visualization button.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/python-1/metabase-dashboard-07.png\",\n        alt: \"screen shot of metabase data visualization\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Next, select the type of visualizer you want. Let us choose \", _jsx(_components.em, {\n        children: \"Bar\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/python-1/metabase-dashboard-08.png\",\n        alt: \"screen shot of metabase data visualization\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Choose the appropriate x-axis and y-axis fields, and you will be able to view the data in bar format.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/python-1/metabase-report.png\",\n        alt: \"screen shot of metabase data visualization\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"conclusion\",\n      children: \"Conclusion\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Data visualization empowers organizations to turn unused data into actionable insights, leading to faster and better decision-making. Why wait?\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/python-1/final-meme.png\",\n        alt: \"screen shot of a meme saying visualization works every time\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"With our \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard/\",\n        children: \"Free Plan Supabase account\"\n      }), \", you can start a new project today and use Metabase to visualize your app data.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you have any questions please reach out via \", _jsx(_components.a, {\n        href: \"https://twitter.com/supabase\",\n        children: \"Twitter\"\n      }), \" or join our \", _jsx(_components.a, {\n        href: \"https://discord.supabase.com\",\n        children: \"Discord\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"more-python-and-supabase-resources\",\n      children: \"More Python and Supabase resources\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://github.com/supabase-community/supabase-py\",\n          children: \"supabase-py\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"slack-consolidate-slackbot-to-consolidate-messages\",\n          children: \"Slack Consolidate: a slackbot built with Python and Supabase\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://replit.com/@Supabase/Supabase-py-Database?v=1\",\n          children: \"Supabase-py (Database) on Replit\"\n        })\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"Prerequisites","slug":"prerequisites","lvl":2,"i":0,"seen":0},{"content":"Visualizing data in Supabase with Metabase","slug":"visualizing-data-in-supabase-with-metabase","lvl":2,"i":1,"seen":0},{"content":"Launching Metabase","slug":"launching-metabase","lvl":3,"i":2,"seen":0},{"content":"View Database and Tables","slug":"view-database-and-tables","lvl":3,"i":3,"seen":0},{"content":"View Table Data Insights","slug":"view-table-data-insights","lvl":3,"i":4,"seen":0},{"content":"Using custom SQL queries","slug":"using-custom-sql-queries","lvl":2,"i":5,"seen":0},{"content":"Conclusion","slug":"conclusion","lvl":2,"i":6,"seen":0},{"content":"More Python and Supabase resources","slug":"more-python-and-supabase-resources","lvl":2,"i":7,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,2],"level":0},{"type":"inline","content":"Data helps organizations make better decisions. With a programming language like Python to analyze your data and transform data into visual representations, you can effortlessly tell the story of your business. One way to create customized visuals from your data would be to use data visualization libraries in Python like [Matplotlib](https://matplotlib.org/), [Seaborn](https://seaborn.pydata.org/), [Ggplot2](https://ggplot2.tidyverse.org/index.html), [Plotly](https://plotly.com/), or [Pandas](https://pandas.pydata.org/). When you want to accomplish this task with little or no code (not even SQL), you might consider using tools like [Metabase](https://www.metabase.com/).","level":1,"lines":[1,2],"children":[{"type":"text","content":"Data helps organizations make better decisions. With a programming language like Python to analyze your data and transform data into visual representations, you can effortlessly tell the story of your business. One way to create customized visuals from your data would be to use data visualization libraries in Python like ","level":0},{"type":"link_open","href":"https://matplotlib.org/","title":"","level":0},{"type":"text","content":"Matplotlib","level":1},{"type":"link_close","level":0},{"type":"text","content":", ","level":0},{"type":"link_open","href":"https://seaborn.pydata.org/","title":"","level":0},{"type":"text","content":"Seaborn","level":1},{"type":"link_close","level":0},{"type":"text","content":", ","level":0},{"type":"link_open","href":"https://ggplot2.tidyverse.org/index.html","title":"","level":0},{"type":"text","content":"Ggplot2","level":1},{"type":"link_close","level":0},{"type":"text","content":", ","level":0},{"type":"link_open","href":"https://plotly.com/","title":"","level":0},{"type":"text","content":"Plotly","level":1},{"type":"link_close","level":0},{"type":"text","content":", or ","level":0},{"type":"link_open","href":"https://pandas.pydata.org/","title":"","level":0},{"type":"text","content":"Pandas","level":1},{"type":"link_close","level":0},{"type":"text","content":". When you want to accomplish this task with little or no code (not even SQL), you might consider using tools like ","level":0},{"type":"link_open","href":"https://www.metabase.com/","title":"","level":0},{"type":"text","content":"Metabase","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[3,4],"level":0},{"type":"inline","content":"With Metabase, a powerful visualization tool, you can quickly turn your data into easy-to-understand visuals like graphs, pie charts, flow diagrams, and much more. Then, using Metabase’s intuitive interface, you can cut through the data noise and focus on what’s essential for your business.","level":1,"lines":[3,4],"children":[{"type":"text","content":"With Metabase, a powerful visualization tool, you can quickly turn your data into easy-to-understand visuals like graphs, pie charts, flow diagrams, and much more. Then, using Metabase’s intuitive interface, you can cut through the data noise and focus on what’s essential for your business.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[5,6],"level":0},{"type":"inline","content":"In the previous blog of this series, we explained [how to use Python to load data into Supabase](https://supabase.com/blog/loading-data-supabase-python). In this blog, we will create different kinds of charts out of the data stored in Supabase using Metabase.","level":1,"lines":[5,6],"children":[{"type":"text","content":"In the previous blog of this series, we explained ","level":0},{"type":"link_open","href":"https://supabase.com/blog/loading-data-supabase-python","title":"","level":0},{"type":"text","content":"how to use Python to load data into Supabase","level":1},{"type":"link_close","level":0},{"type":"text","content":". In this blog, we will create different kinds of charts out of the data stored in Supabase using Metabase.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[7,8],"level":0},{"type":"inline","content":"[Prerequisites](#prerequisites)","level":1,"lines":[7,8],"children":[{"type":"text","content":"Prerequisites","level":0}],"lvl":2,"i":0,"seen":0,"slug":"prerequisites"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[9,10],"level":0},{"type":"inline","content":"Before we dive in, let’s look at some prerequisites that you will need:","level":1,"lines":[9,10],"children":[{"type":"text","content":"Before we dive in, let’s look at some prerequisites that you will need:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[11,13],"level":0},{"type":"list_item_open","lines":[11,13],"level":1},{"type":"paragraph_open","tight":true,"lines":[11,12],"level":2},{"type":"inline","content":"**Supabase project with data**","level":3,"lines":[11,12],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Supabase project with data","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[13,14],"level":0},{"type":"inline","content":"Based on our [previous article](https://supabase.com/blog/loading-data-supabase-python), we assume we already have a Supabase project setup and have data loaded into it.","level":1,"lines":[13,14],"children":[{"type":"text","content":"Based on our ","level":0},{"type":"link_open","href":"https://supabase.com/blog/loading-data-supabase-python","title":"","level":0},{"type":"text","content":"previous article","level":1},{"type":"link_close","level":0},{"type":"text","content":", we assume we already have a Supabase project setup and have data loaded into it.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[15,17],"level":0},{"type":"list_item_open","lines":[15,17],"level":1},{"type":"paragraph_open","tight":true,"lines":[15,16],"level":2},{"type":"inline","content":"**Metabase Docker Container**","level":3,"lines":[15,16],"children":[{"type":"strong_open","level":0},{"type":"text","content":"Metabase Docker Container","level":1},{"type":"strong_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[17,18],"level":0},{"type":"inline","content":"To take advantage of the open-source version of Metabase, you can use the Metabase docker container [here](https://hub.docker.com/r/metabase/metabase).","level":1,"lines":[17,18],"children":[{"type":"text","content":"To take advantage of the open-source version of Metabase, you can use the Metabase docker container ","level":0},{"type":"link_open","href":"https://hub.docker.com/r/metabase/metabase","title":"","level":0},{"type":"text","content":"here","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[19,20],"level":0},{"type":"inline","content":"[Visualizing data in Supabase with Metabase](#visualizing-data-in-supabase-with-metabase)","level":1,"lines":[19,20],"children":[{"type":"text","content":"Visualizing data in Supabase with Metabase","level":0}],"lvl":2,"i":1,"seen":0,"slug":"visualizing-data-in-supabase-with-metabase"},{"type":"heading_close","hLevel":2,"level":0},{"type":"heading_open","hLevel":3,"lines":[21,22],"level":0},{"type":"inline","content":"[Launching Metabase](#launching-metabase)","level":1,"lines":[21,22],"children":[{"type":"text","content":"Launching Metabase","level":0}],"lvl":3,"i":2,"seen":0,"slug":"launching-metabase"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[23,24],"level":0},{"type":"inline","content":"To launch Metabase, simply go to [http://localhost:3000/setup/](http://localhost:3000/setup/) which is the default port that the Metabase server will be listening to.","level":1,"lines":[23,24],"children":[{"type":"text","content":"To launch Metabase, simply go to ","level":0},{"type":"link_open","href":"http://localhost:3000/setup/","title":"","level":0},{"type":"text","content":"http://localhost:3000/setup/","level":1},{"type":"link_close","level":0},{"type":"text","content":" which is the default port that the Metabase server will be listening to.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[25,26],"level":0},{"type":"inline","content":"After Metabase is launched, select your preferred language and add your contact information. In the _Add your data_ markdown, you will need to choose PostgreSQL.","level":1,"lines":[25,26],"children":[{"type":"text","content":"After Metabase is launched, select your preferred language and add your contact information. In the ","level":0},{"type":"em_open","level":0},{"type":"text","content":"Add your data","level":1},{"type":"em_close","level":0},{"type":"text","content":" markdown, you will need to choose PostgreSQL.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[27,28],"level":0},{"type":"inline","content":"![screen shot of adding postgres data to metabase](/images/blog/python-1/adding-postgresql-data-metabase.png)","level":1,"lines":[27,28],"children":[{"type":"image","src":"/images/blog/python-1/adding-postgresql-data-metabase.png","title":"","alt":"screen shot of adding postgres data to metabase","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[29,30],"level":0},{"type":"inline","content":"You will be prompted to add the necessary connection information to your Supabase project. Go to your [Supabase project](https://supabase.com/dashboard/) and hit _Settings \u003e Database to get the database info_.","level":1,"lines":[29,30],"children":[{"type":"text","content":"You will be prompted to add the necessary connection information to your Supabase project. Go to your ","level":0},{"type":"link_open","href":"https://supabase.com/dashboard/","title":"","level":0},{"type":"text","content":"Supabase project","level":1},{"type":"link_close","level":0},{"type":"text","content":" and hit ","level":0},{"type":"em_open","level":0},{"type":"text","content":"Settings \u003e Database to get the database info","level":1},{"type":"em_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[31,32],"level":0},{"type":"inline","content":"![screen shot of supabase dashboard database connection information](/images/blog/python-1/supabase-dashboard-connect-database-info.png)","level":1,"lines":[31,32],"children":[{"type":"image","src":"/images/blog/python-1/supabase-dashboard-connect-database-info.png","title":"","alt":"screen shot of supabase dashboard database connection information","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[33,34],"level":0},{"type":"inline","content":"Enter the necessary information on Metabase and hit next. Finally, select your data preference, after which you will land on the Metabase homepage.","level":1,"lines":[33,34],"children":[{"type":"text","content":"Enter the necessary information on Metabase and hit next. Finally, select your data preference, after which you will land on the Metabase homepage.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[35,36],"level":0},{"type":"inline","content":"[View Database and Tables](#view-database-and-tables)","level":1,"lines":[35,36],"children":[{"type":"text","content":"View Database and Tables","level":0}],"lvl":3,"i":3,"seen":0,"slug":"view-database-and-tables"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[37,38],"level":0},{"type":"inline","content":"We can now see the \"Supabase DB\" Supabase project under \"Our data\".","level":1,"lines":[37,38],"children":[{"type":"text","content":"We can now see the \"Supabase DB\" Supabase project under \"Our data\".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[39,40],"level":0},{"type":"inline","content":"![screen shot of metabase dashboard](/images/blog/python-1/metabase-dashboard-with-supabase-db.png)","level":1,"lines":[39,40],"children":[{"type":"image","src":"/images/blog/python-1/metabase-dashboard-with-supabase-db.png","title":"","alt":"screen shot of metabase dashboard","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[41,42],"level":0},{"type":"inline","content":"To view the tables, go to _SupabaseDB \u003e public_","level":1,"lines":[41,42],"children":[{"type":"text","content":"To view the tables, go to ","level":0},{"type":"em_open","level":0},{"type":"text","content":"SupabaseDB \u003e public","level":1},{"type":"em_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[43,44],"level":0},{"type":"inline","content":"![screen shot of metabase dashboard table view](/images/blog/python-1/metabase-dashboard-02.png)","level":1,"lines":[43,44],"children":[{"type":"image","src":"/images/blog/python-1/metabase-dashboard-02.png","title":"","alt":"screen shot of metabase dashboard table view","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[45,46],"level":0},{"type":"inline","content":"[View Table Data Insights](#view-table-data-insights)","level":1,"lines":[45,46],"children":[{"type":"text","content":"View Table Data Insights","level":0}],"lvl":3,"i":4,"seen":0,"slug":"view-table-data-insights"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[47,48],"level":0},{"type":"inline","content":"Go back to the home page and select public schema under \"Try these x-rays based on your data\"","level":1,"lines":[47,48],"children":[{"type":"text","content":"Go back to the home page and select public schema under \"Try these x-rays based on your data\"","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[49,50],"level":0},{"type":"inline","content":"![screen shot of metabase dashboard schema view](/images/blog/python-1/metabase-dashboard-03.png)","level":1,"lines":[49,50],"children":[{"type":"image","src":"/images/blog/python-1/metabase-dashboard-03.png","title":"","alt":"screen shot of metabase dashboard schema view","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[51,52],"level":0},{"type":"inline","content":"Here is the output of the product table.","level":1,"lines":[51,52],"children":[{"type":"text","content":"Here is the output of the product table.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[53,54],"level":0},{"type":"inline","content":"![screen shot of metabase data visualization](/images/blog/python-1/metabase-visualization-supabase-db.png)","level":1,"lines":[53,54],"children":[{"type":"image","src":"/images/blog/python-1/metabase-visualization-supabase-db.png","title":"","alt":"screen shot of metabase data visualization","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[55,56],"level":0},{"type":"inline","content":"As you can see, we can get some handy information from this like:","level":1,"lines":[55,56],"children":[{"type":"text","content":"As you can see, we can get some handy information from this like:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[57,62],"level":0},{"type":"list_item_open","lines":[57,58],"level":1},{"type":"paragraph_open","tight":true,"lines":[57,58],"level":2},{"type":"inline","content":"How many products are present with a given range of inventory count.","level":3,"lines":[57,58],"children":[{"type":"text","content":"How many products are present with a given range of inventory count.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[58,59],"level":1},{"type":"paragraph_open","tight":true,"lines":[58,59],"level":2},{"type":"inline","content":"How many products are present for a given range of price.","level":3,"lines":[58,59],"children":[{"type":"text","content":"How many products are present for a given range of price.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[59,60],"level":1},{"type":"paragraph_open","tight":true,"lines":[59,60],"level":2},{"type":"inline","content":"The ratio between the number of employees to the number of products.","level":3,"lines":[59,60],"children":[{"type":"text","content":"The ratio between the number of employees to the number of products.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[60,62],"level":1},{"type":"paragraph_open","tight":true,"lines":[60,61],"level":2},{"type":"inline","content":"How many products each vendor has created.","level":3,"lines":[60,61],"children":[{"type":"text","content":"How many products each vendor has created.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[62,63],"level":0},{"type":"inline","content":"If you have column-specific views, you can select the _zoom-in_ option under _More x-rays_.","level":1,"lines":[62,63],"children":[{"type":"text","content":"If you have column-specific views, you can select the ","level":0},{"type":"em_open","level":0},{"type":"text","content":"zoom-in","level":1},{"type":"em_close","level":0},{"type":"text","content":" option under ","level":0},{"type":"em_open","level":0},{"type":"text","content":"More x-rays","level":1},{"type":"em_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[64,65],"level":0},{"type":"inline","content":"![screen shot of metabase data visualization](/images/blog/python-1/metabase-visualization-supabase-db-02.png)","level":1,"lines":[64,65],"children":[{"type":"image","src":"/images/blog/python-1/metabase-visualization-supabase-db-02.png","title":"","alt":"screen shot of metabase data visualization","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[66,67],"level":0},{"type":"inline","content":"For example, let's select the total employees field.","level":1,"lines":[66,67],"children":[{"type":"text","content":"For example, let's select the total employees field.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[68,69],"level":0},{"type":"inline","content":"![screen shot of metabase data visualization](/images/blog/python-1/metabase-visualization-supabase-data.png)","level":1,"lines":[68,69],"children":[{"type":"image","src":"/images/blog/python-1/metabase-visualization-supabase-data.png","title":"","alt":"screen shot of metabase data visualization","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[70,71],"level":0},{"type":"inline","content":"With information like this, you will be able to answer some key questions like","level":1,"lines":[70,71],"children":[{"type":"text","content":"With information like this, you will be able to answer some key questions like","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[72,76],"level":0},{"type":"list_item_open","lines":[72,73],"level":1},{"type":"paragraph_open","tight":true,"lines":[72,73],"level":2},{"type":"inline","content":"What are some common statistics for company employees like average, minimum, maximum, and standard deviation?","level":3,"lines":[72,73],"children":[{"type":"text","content":"What are some common statistics for company employees like average, minimum, maximum, and standard deviation?","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[73,74],"level":1},{"type":"paragraph_open","tight":true,"lines":[73,74],"level":2},{"type":"inline","content":"What is the distribution of the employees across different geo locations?","level":3,"lines":[73,74],"children":[{"type":"text","content":"What is the distribution of the employees across different geo locations?","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[74,76],"level":1},{"type":"paragraph_open","tight":true,"lines":[74,75],"level":2},{"type":"inline","content":"What is the distribution of the employees across different vendors?","level":3,"lines":[74,75],"children":[{"type":"text","content":"What is the distribution of the employees across different vendors?","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":2,"lines":[76,77],"level":0},{"type":"inline","content":"[Using custom SQL queries](#using-custom-sql-queries)","level":1,"lines":[76,77],"children":[{"type":"text","content":"Using custom SQL queries","level":0}],"lvl":2,"i":5,"seen":0,"slug":"using-custom-sql-queries"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[78,79],"level":0},{"type":"inline","content":"We can also use custom queries to set up our dashboards. To do this, go to _New \u003e SQL query._","level":1,"lines":[78,79],"children":[{"type":"text","content":"We can also use custom queries to set up our dashboards. To do this, go to ","level":0},{"type":"em_open","level":0},{"type":"text","content":"New \u003e SQL query.","level":1},{"type":"em_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[80,81],"level":0},{"type":"inline","content":"![screen shot of metabase custom queries dashboard](/images/blog/python-1/metabase-dashboard-04.png)","level":1,"lines":[80,81],"children":[{"type":"image","src":"/images/blog/python-1/metabase-dashboard-04.png","title":"","alt":"screen shot of metabase custom queries dashboard","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[82,83],"level":0},{"type":"inline","content":"Next, under the database, select \"SupabaseDB\".","level":1,"lines":[82,83],"children":[{"type":"text","content":"Next, under the database, select \"SupabaseDB\".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[84,85],"level":0},{"type":"inline","content":"![screen shot of metabase custom queries dashboard](/images/blog/python-1/metabase-dashboard-05.png)","level":1,"lines":[84,85],"children":[{"type":"image","src":"/images/blog/python-1/metabase-dashboard-05.png","title":"","alt":"screen shot of metabase custom queries dashboard","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[86,87],"level":0},{"type":"inline","content":"We will be using the following SQL query:","level":1,"lines":[86,87],"children":[{"type":"text","content":"We will be using the following SQL query:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql","content":"select \"Vendor\".vendor_name, product_name, \"Vendor\".total_employees\nfrom\n  \"Product\"\n  left join \"Vendor\" on \"Product\".vendor_id = \"Vendor\".vendor_id\nwhere \"Vendor\".total_employees;\n","lines":[88,95],"level":0},{"type":"paragraph_open","tight":false,"lines":[96,97],"level":0},{"type":"inline","content":"This query should fetch us the vendor name and the product where the number of employees for a given vendor is less than 110.","level":1,"lines":[96,97],"children":[{"type":"text","content":"This query should fetch us the vendor name and the product where the number of employees for a given vendor is less than 110.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[98,99],"level":0},{"type":"inline","content":"To run the SQL query, hit the play button.","level":1,"lines":[98,99],"children":[{"type":"text","content":"To run the SQL query, hit the play button.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[100,101],"level":0},{"type":"inline","content":"![screen shot of metabase data visualization](/images/blog/python-1/metabase-dashboard-06.png)","level":1,"lines":[100,101],"children":[{"type":"image","src":"/images/blog/python-1/metabase-dashboard-06.png","title":"","alt":"screen shot of metabase data visualization","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[102,103],"level":0},{"type":"inline","content":"This will be shown below in the output window. To visualize the data, hit the visualization button.","level":1,"lines":[102,103],"children":[{"type":"text","content":"This will be shown below in the output window. To visualize the data, hit the visualization button.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[104,105],"level":0},{"type":"inline","content":"![screen shot of metabase data visualization](/images/blog/python-1/metabase-dashboard-07.png)","level":1,"lines":[104,105],"children":[{"type":"image","src":"/images/blog/python-1/metabase-dashboard-07.png","title":"","alt":"screen shot of metabase data visualization","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[106,107],"level":0},{"type":"inline","content":"Next, select the type of visualizer you want. Let us choose _Bar_.","level":1,"lines":[106,107],"children":[{"type":"text","content":"Next, select the type of visualizer you want. Let us choose ","level":0},{"type":"em_open","level":0},{"type":"text","content":"Bar","level":1},{"type":"em_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[108,109],"level":0},{"type":"inline","content":"![screen shot of metabase data visualization](/images/blog/python-1/metabase-dashboard-08.png)","level":1,"lines":[108,109],"children":[{"type":"image","src":"/images/blog/python-1/metabase-dashboard-08.png","title":"","alt":"screen shot of metabase data visualization","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[110,111],"level":0},{"type":"inline","content":"Choose the appropriate x-axis and y-axis fields, and you will be able to view the data in bar format.","level":1,"lines":[110,111],"children":[{"type":"text","content":"Choose the appropriate x-axis and y-axis fields, and you will be able to view the data in bar format.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[112,113],"level":0},{"type":"inline","content":"![screen shot of metabase data visualization](/images/blog/python-1/metabase-report.png)","level":1,"lines":[112,113],"children":[{"type":"image","src":"/images/blog/python-1/metabase-report.png","title":"","alt":"screen shot of metabase data visualization","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[114,115],"level":0},{"type":"inline","content":"[Conclusion](#conclusion)","level":1,"lines":[114,115],"children":[{"type":"text","content":"Conclusion","level":0}],"lvl":2,"i":6,"seen":0,"slug":"conclusion"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[116,117],"level":0},{"type":"inline","content":"Data visualization empowers organizations to turn unused data into actionable insights, leading to faster and better decision-making. Why wait?","level":1,"lines":[116,117],"children":[{"type":"text","content":"Data visualization empowers organizations to turn unused data into actionable insights, leading to faster and better decision-making. Why wait?","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[118,119],"level":0},{"type":"inline","content":"![screen shot of a meme saying visualization works every time](/images/blog/python-1/final-meme.png)","level":1,"lines":[118,119],"children":[{"type":"image","src":"/images/blog/python-1/final-meme.png","title":"","alt":"screen shot of a meme saying visualization works every time","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[120,121],"level":0},{"type":"inline","content":"With our [Free Plan Supabase account](https://supabase.com/dashboard/), you can start a new project today and use Metabase to visualize your app data.","level":1,"lines":[120,121],"children":[{"type":"text","content":"With our ","level":0},{"type":"link_open","href":"https://supabase.com/dashboard/","title":"","level":0},{"type":"text","content":"Free Plan Supabase account","level":1},{"type":"link_close","level":0},{"type":"text","content":", you can start a new project today and use Metabase to visualize your app data.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[122,123],"level":0},{"type":"inline","content":"If you have any questions please reach out via [Twitter](https://twitter.com/supabase) or join our [Discord](https://discord.supabase.com).","level":1,"lines":[122,123],"children":[{"type":"text","content":"If you have any questions please reach out via ","level":0},{"type":"link_open","href":"https://twitter.com/supabase","title":"","level":0},{"type":"text","content":"Twitter","level":1},{"type":"link_close","level":0},{"type":"text","content":" or join our ","level":0},{"type":"link_open","href":"https://discord.supabase.com","title":"","level":0},{"type":"text","content":"Discord","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[124,125],"level":0},{"type":"inline","content":"[More Python and Supabase resources](#more-python-and-supabase-resources)","level":1,"lines":[124,125],"children":[{"type":"text","content":"More Python and Supabase resources","level":0}],"lvl":2,"i":7,"seen":0,"slug":"more-python-and-supabase-resources"},{"type":"heading_close","hLevel":2,"level":0},{"type":"bullet_list_open","lines":[126,129],"level":0},{"type":"list_item_open","lines":[126,127],"level":1},{"type":"paragraph_open","tight":true,"lines":[126,127],"level":2},{"type":"inline","content":"[supabase-py](https://github.com/supabase-community/supabase-py)","level":3,"lines":[126,127],"children":[{"type":"link_open","href":"https://github.com/supabase-community/supabase-py","title":"","level":0},{"type":"text","content":"supabase-py","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[127,128],"level":1},{"type":"paragraph_open","tight":true,"lines":[127,128],"level":2},{"type":"inline","content":"[Slack Consolidate: a slackbot built with Python and Supabase](slack-consolidate-slackbot-to-consolidate-messages)","level":3,"lines":[127,128],"children":[{"type":"link_open","href":"slack-consolidate-slackbot-to-consolidate-messages","title":"","level":0},{"type":"text","content":"Slack Consolidate: a slackbot built with Python and Supabase","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[128,129],"level":1},{"type":"paragraph_open","tight":true,"lines":[128,129],"level":2},{"type":"inline","content":"[Supabase-py (Database) on Replit](https://replit.com/@Supabase/Supabase-py-Database?v=1)","level":3,"lines":[128,129],"children":[{"type":"link_open","href":"https://replit.com/@Supabase/Supabase-py-Database?v=1","title":"","level":0},{"type":"text","content":"Supabase-py (Database) on Replit","level":1},{"type":"link_close","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0}],"content":"- [Prerequisites](#prerequisites)\n- [Visualizing data in Supabase with Metabase](#visualizing-data-in-supabase-with-metabase)\n  * [Launching Metabase](#launching-metabase)\n  * [View Database and Tables](#view-database-and-tables)\n  * [View Table Data Insights](#view-table-data-insights)\n- [Using custom SQL queries](#using-custom-sql-queries)\n- [Conclusion](#conclusion)\n- [More Python and Supabase resources](#more-python-and-supabase-resources)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"visualizing-supabase-data-using-metabase"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>