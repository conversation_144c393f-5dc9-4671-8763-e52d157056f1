<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Making the Supabase Dashboard Supa-fast</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Improving the performance of the Supabase dashboard" data-next-head=""/><meta property="og:title" content="Making the Supabase Dashboard Supa-fast" data-next-head=""/><meta property="og:description" content="Improving the performance of the Supabase dashboard" data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supabase-dashboard-performance" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="12-13-2020" data-next-head=""/><meta property="article:author" content="https://twitter.com/everConfusedGuy" data-next-head=""/><meta property="article:tag" content="supabase" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/undefined" data-next-head=""/><meta property="og:image:alt" content="Making the Supabase Dashboard Supa-fast thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Making the Supabase Dashboard Supa-fast</h1><div class="text-light flex space-x-3 text-sm"><p>13 Dec 2020</p><p>•</p><p>8 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://twitter.com/everConfusedGuy"><div class="flex items-center gap-3"><div class="w-10"><img alt="Inian Parameshwaran avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Favatars.githubusercontent.com%2Fu%2F2155545%3Fv%3D4&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Favatars.githubusercontent.com%2Fu%2F2155545%3Fv%3D4&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Favatars.githubusercontent.com%2Fu%2F2155545%3Fv%3D4&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Inian Parameshwaran</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p>The Supabase dashboard has become more feature-rich in the last month. We have a powerful SQL editor backed by <a href="https://microsoft.github.io/monaco-editor/">Monaco</a>. We built an Airtable-like view of your database, making editing a breeze.</p>
<blockquote>
<p>Features, performance, DX - choose three</p>
</blockquote>
<p>Performance can quickly regress when adding new features, especially in a Single Page Application. Here are the steps we took to guarantee a good baseline performance within our application, without compromising on the developer experience (DX).</p>
<h2 id="establishing-a-baseline-and-setting-targets" class="group scroll-mt-24">Establishing a baseline and setting targets<a href="#establishing-a-baseline-and-setting-targets" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<blockquote>
<p>You can&#x27;t fix what you can&#x27;t measure</p>
</blockquote>
<p>There was some low-hanging fruit to improve performance, but we had one important thing to do before that - establish a baseline.</p>
<p>Our dashboard is JavaScript heavy, so we started by setting up analytics to track our bundle sizes. <a href="https://www.npmjs.com/package/@next/bundle-analyzer">Next-bundle-analyzer</a> (or <a href="https://www.npmjs.com/package/webpack-bundle-analyzer">webpack-bundle-analyzer</a>) provides an interactive treemap of your generated JavaScript bundles. This is our treemap when we started. It gave us a clear indication what changes we needed to achieve the most impact.</p>
<p></p>
<p>There are some great tools when it comes to Real User Monitoring (RUM). We chose the newly-launched <a href="https://sentry.io/for/performance/">Sentry performance monitoring</a> product since we already use Sentry for error tracking and we wanted to minimize new tools in our stack. It also supports reporting <a href="https://web.dev/vitals/">Core Web Vitals</a>, the performance metrics created by Google to track initial loading performance, responsiveness and visual stability. Core Web Vitals come with recommended target values, giving us clear goals to hit.</p>
<p></p>
<h2 id="improving-our-javascript-bundle-size" class="group scroll-mt-24">Improving our JavaScript bundle size<a href="#improving-our-javascript-bundle-size" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<blockquote>
<p>How to not load the entire npm registry into our user&#x27;s browsers</p>
</blockquote>
<h3 id="choosing-smaller-modules" class="group scroll-mt-24">Choosing smaller modules<a href="#choosing-smaller-modules" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We used <a href="https://bundlephobia.com/">Bundlephobia</a> on our largest modules. This is a great website to have in your JS-performance arsenal. It gives the size of npm modules across different versions and recommends alternate modules with similar functionality which are smaller.</p>
<p><code class="short-inline-codeblock">Moment.js</code> is notorious for its large bundle size and we don&#x27;t need complex date processing for our dashboard. It was straightforward to switch to <a href="https://day.js.org/">day-js</a> which is largely API-compatible with <code class="short-inline-codeblock">Moment.js</code>. This change reduced our gzipped bundle size by 68 KB.</p>
<p>We migrated from <code class="short-inline-codeblock">Joi</code> to <code class="short-inline-codeblock">ajv</code> for our schema validation which was 32% smaller. <code class="short-inline-codeblock">ajv</code> was already bundled as a transitive dependency of other modules, making it a no-brainer.</p>
<p></p>
<p>We reverted our <a href="https://github.com/brix/crypto-js">crypto-js</a> module from version 4.0 to 3.3.0. Version 4.0 <a href="https://github.com/brix/crypto-js/issues/276">injects more than 400kb code</a> when used in a browser context. The newer version replaces <code class="short-inline-codeblock">Math.random</code> with node&#x27;s implementation, injecting the entire node crypto module into the browser context. We use <code class="short-inline-codeblock">crypto-js</code> for decrypting user&#x27;s API keys and so we&#x27;re not reliant on the randomness of the PRNG. We might move to a dedicated module like <a href="https://www.npmjs.com/package/aes-js">aes-js</a> in the future since it has a much smaller surface area than <code class="short-inline-codeblock">crypto-js</code> (in terms of security and performance).</p>
<h3 id="using-partial-imports" class="group scroll-mt-24">Using partial imports<a href="#using-partial-imports" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>By selectively importing functions from modules like <code class="short-inline-codeblock">lodash</code>, we cut the gzipped size by another 40kb across all our bundles.</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>// before</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>import _ from &#x27;lodash&#x27;\n</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>// maunally cherry picking modules</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>import find from &#x27;lodash/find&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>import debounce from &#x27;lodash/debounce&#x27;\n</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>// using babel-plugin-lodash</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>import { find, debounce } from &#x27;lodash&#x27;</span></div></div><br/></code></div></div>
<p>In the above example, we added <a href="https://github.com/lodash/babel-plugin-lodash">babel-plugin-lodash</a> to our babel configuration which cherry picks the exact <code class="short-inline-codeblock">lodash</code> functions we import. This makes it easier to import from <code class="short-inline-codeblock">lodash</code> without cluttering the code with selective import statements.</p>
<h3 id="moving-complex-logic-to-the-server" class="group scroll-mt-24">Moving complex logic to the server<a href="#moving-complex-logic-to-the-server" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Thanks to some skilled haxors (well, weak passwords mainly) we had crypto miners running on some of our customer&#x27;s databases. To prevent this, we enforce password strength with the <a href="https://github.com/dropbox/zxcvbn">zxcvbn</a> module. Though it improved our overall security, the module is <a href="https://bundlephobia.com/result?p=zxcvbn@4.4.2">pretty big</a>, weighing in at 388kb gzipped. To get around this, we moved the password-strength checking logic to an API. Now, the frontend queries a server with a user-supplied password and the server computes its strength. This eliminates the module from the frontend.</p>
<h3 id="lazy-loading-code" class="group scroll-mt-24">Lazy loading code<a href="#lazy-loading-code" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p><a href="https://github.com/SheetJS/sheetjs">xlsx</a> is another complex and large module, which is used to import spreadsheets into tables. We contemplated moving this logic to the backend, but we found another solution: lazy loading it.</p>
<p>The spreadsheet import is triggered when the user is creating a new table. However the code was previously loaded every time the page was visited - even when a new table was not being created. This made it a good candidate for lazy loading. Using <a href="https://nextjs.org/docs/advanced-features/dynamic-import">Next.js dynamic imports</a> we are able to load this component (313 kb brotlied) dynamically, whenever the user clicks the &quot;Add content&quot; button.</p>
<video width="99%" muted="" playsinline="" controls=""><source src="https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/lazy_loading.mov" type="video/mp4"/></video>
<p>We use the same technique to lazy load some Lottie animations which are relatively large.</p>
<h3 id="using-native-browser-apis" class="group scroll-mt-24">Using native browser APIs<a href="#using-native-browser-apis" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We decided against supporting IE11, opening up more avenues for optimization. Using native browser APIs enabled us to drop even more dependencies. For example, since the <a href="https://caniuse.com/fetch">fetch API is available</a> in all the browsers we care about, we removed <a href="https://github.com/axios/axios">axios</a> and built a simple wrapper using the native fetch API.</p>
<h2 id="improving-vercels-default-caching" class="group scroll-mt-24">Improving Vercel&#x27;s default caching<a href="#improving-vercels-default-caching" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<blockquote>
<p>The fastest request is the request not made</p>
</blockquote>
<p>We noticed that Vercel was sending a <code class="short-inline-codeblock">Cache-Control</code> header of <code class="short-inline-codeblock">public, max-age=0, must-revalidate</code> , preventing some of our SVG, CSS and font files from being cached in the browser.</p>
<p>We updated our <code class="short-inline-codeblock">next.config.js</code> , adding a long <code class="short-inline-codeblock">max-age</code> to the caching header that Vercel sends. Our assets are versioned properly, so we were able to safely do this.</p>
<h2 id="enabling-nextjs-automatic-static-optimization" class="group scroll-mt-24">Enabling Next.js Automatic Static Optimization<a href="#enabling-nextjs-automatic-static-optimization" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Next.js is able to automatically pre-render a page to HTML, whenever a page meets some pre-conditions. This mode is called <a href="https://nextjs.org/docs/advanced-features/automatic-static-optimization">Automatic Static Optimization</a>. Pre-rendered pages can be cached on a CDN for extremely fast page loads. We removed calls to <code class="short-inline-codeblock">getServerSideProps</code> and <code class="short-inline-codeblock">getInitialProps</code> to take advantage of this mode.</p>
<h2 id="developing-a-performance-culture" class="group scroll-mt-24">Developing a performance culture<a href="#developing-a-performance-culture" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<blockquote>
<p>Always in sight, always in mind</p>
</blockquote>
<p>Our performance optimization journey will never be complete. It requires constant vigilance to maintain a baseline across our users. To instill this within our team, we took a few actions.</p>
<p>We developed a Slack bot which sends our Sentry performance dashboard every week, containing our slowest transactions and our Core Web Vitals summary. This shows which pages need improvement and where our <a href="https://docs.sentry.io/product/performance/metrics/#user-misery">users are the most miserable</a>.</p>
<p>During our transition from Alpha to Beta, performance was one of the important features, along with stability and security. We considered performance implications while choosing libraries and tools. Having a &quot;seat at the table&quot; in these discussions ensures that performance is not considered as an after-thought.</p>
<h2 id="results" class="group scroll-mt-24">Results<a href="#results" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>With these changes, we have a respectable Core Web Vitals score. This is a snapshot from Sentry with RUM data from the last week. We are within the recommended threshold for all the 3 Web Vitals.</p>
<p></p>
<p>Our Next.js build output also shows that users download &lt; 200 kb of JavaScript between any two page transitions. We&#x27;re still improving too - even though we provide a lot of functionality in our dashboard, we will continue to reduce our bundle sizes.</p>
<h2 id="things-that-did-not-work" class="group scroll-mt-24">Things that did not work<a href="#things-that-did-not-work" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<blockquote>
<p>You win some, you lose some</p>
</blockquote>
<p>We tried a VSCode plugin called <a href="https://marketplace.visualstudio.com/items?itemName=wix.vscode-import-cost">Import cost</a> which shows the size of JavaScript modules when you import it in your editor. However, the plugin did not work on our codebase since it doesn&#x27;t support some JavaScript features, like optional chaining.</p>
<p>We also passed on using <a href="https://www.npmjs.com/package/lodash-webpack-plugin">lodash-webpack-plugin</a> even though it had the potential to reduce our JavaScript sizes, because it could potentially break our code if not used carefully. This plugin would require our frontend team to understand the Webpack configuration, updating it whenever they use a new lodash feature set.</p>
<h2 id="the-road-ahead" class="group scroll-mt-24">The road ahead<a href="#the-road-ahead" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Our broad goal is to implement best practices for frontend performance, and make it exciting to all of our team. These are some ideas we have on our roadmap -</p>
<ul>
<li>Set up <a href="https://developers.google.com/web/tools/lighthouse">Lighthouse</a> in a GitHub Action to catch performance regression earlier in the development life cycle.</li>
<li>Continue reducing our initial JavaScript payload size, to improve our LCP time</li>
<li>Explore <code class="short-inline-codeblock">cloud-mode</code> in <a href="https://segment.com/docs/connections/destinations/">Segment</a> which makes API calls from the server instead of loading third-party library on the browser.</li>
</ul>
<p>Reach out to us on <a href="https://twitter.com/supabase">Twitter</a> if you have more ideas to speed up our website ⚡</p></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-dashboard-performance&amp;text=Making%20the%20Supabase%20Dashboard%20Supa-fast"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-dashboard-performance&amp;text=Making%20the%20Supabase%20Dashboard%20Supa-fast"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-dashboard-performance&amp;t=Making%20the%20Supabase%20Dashboard%20Supa-fast"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="supabase-beta-december-2020.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Supabase Beta December 2020</h4><p class="small">2 January 2021</p></div></div></div></div></a></div><div><a href="../customers.html"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Monitoro Built a Web Crawler Handling Millions of API Requests</h4><p class="small">2 December 2020</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/supabase"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">supabase</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#establishing-a-baseline-and-setting-targets">Establishing a baseline and setting targets</a></li>
<li><a href="#improving-our-javascript-bundle-size">Improving our JavaScript bundle size</a></li>
<li><a href="#improving-vercels-default-caching">Improving Vercel&#x27;s default caching</a></li>
<li><a href="#enabling-nextjs-automatic-static-optimization">Enabling Next.js Automatic Static Optimization</a></li>
<li><a href="#developing-a-performance-culture">Developing a performance culture</a></li>
<li><a href="#results">Results</a></li>
<li><a href="#things-that-did-not-work">Things that did not work</a></li>
<li><a href="#the-road-ahead">The road ahead</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-dashboard-performance&amp;text=Making%20the%20Supabase%20Dashboard%20Supa-fast"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-dashboard-performance&amp;text=Making%20the%20Supabase%20Dashboard%20Supa-fast"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupabase-dashboard-performance&amp;t=Making%20the%20Supabase%20Dashboard%20Supa-fast"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"supabase-beta-december-2020","title":"Supabase Beta December 2020","description":"Ten months of building.","author":"paul_copplestone","author_title":"Supabase","author_url":"https://github.com/kiwicopple","author_image_url":"https://github.com/kiwicopple.png","image":"supabase-december-2020.png","thumb":"supabase-december-2020.png","categories":["product"],"tags":["supabase"],"date":"01-02-2021","video":"https://www.youtube.com/v/ofSm4BJkZ1g","formattedDate":"2 January 2021","readingTime":"3 minute read","url":"/blog/supabase-beta-december-2020","path":"/blog/supabase-beta-december-2020"},"nextPost":{"slug":"case-study-monitoro","title":"Monitoro Built a Web Crawler Handling Millions of API Requests","description":"See how Monitoro built an automated scraping platform using Supabase.","author":"rory_wilding","author_title":"Supabase","author_url":"https://github.com/roryw10","author_image_url":"https://github.com/roryw10.png","image":"/images/blog/supabase-monitoro.png","categories":["company"],"tags":["no-code"],"date":"12-02-2020","video":"https://www.youtube.com/v/8A6_pg41M2s","formattedDate":"2 December 2020","readingTime":"3 minute read","url":"/blog/case-study-monitoro","path":"/blog/case-study-monitoro"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supabase-dashboard-performance","source":"\nThe Supabase dashboard has become more feature-rich in the last month. We have a powerful SQL editor backed by [Monaco](https://microsoft.github.io/monaco-editor/). We built an Airtable-like view of your database, making editing a breeze.\n\n\u003e Features, performance, DX - choose three\n\nPerformance can quickly regress when adding new features, especially in a Single Page Application. Here are the steps we took to guarantee a good baseline performance within our application, without compromising on the developer experience (DX).\n\n## Establishing a baseline and setting targets\n\n\u003e You can't fix what you can't measure\n\nThere was some low-hanging fruit to improve performance, but we had one important thing to do before that - establish a baseline.\n\nOur dashboard is JavaScript heavy, so we started by setting up analytics to track our bundle sizes. [Next-bundle-analyzer](https://www.npmjs.com/package/@next/bundle-analyzer) (or [webpack-bundle-analyzer](https://www.npmjs.com/package/webpack-bundle-analyzer)) provides an interactive treemap of your generated JavaScript bundles. This is our treemap when we started. It gave us a clear indication what changes we needed to achieve the most impact.\n\n![Nextjs tree analyzer](/images/blog/blog/nextjs-tree-analyzer.png)\n\nThere are some great tools when it comes to Real User Monitoring (RUM). We chose the newly-launched [Sentry performance monitoring](https://sentry.io/for/performance/) product since we already use Sentry for error tracking and we wanted to minimize new tools in our stack. It also supports reporting [Core Web Vitals](https://web.dev/vitals/), the performance metrics created by Google to track initial loading performance, responsiveness and visual stability. Core Web Vitals come with recommended target values, giving us clear goals to hit.\n\n![Core Web Vitals](/images/blog/blog/core-web-vitals.png)\n\n## Improving our JavaScript bundle size\n\n\u003e How to not load the entire npm registry into our user's browsers\n\n### Choosing smaller modules\n\nWe used [Bundlephobia](https://bundlephobia.com/) on our largest modules. This is a great website to have in your JS-performance arsenal. It gives the size of npm modules across different versions and recommends alternate modules with similar functionality which are smaller.\n\n`Moment.js` is notorious for its large bundle size and we don't need complex date processing for our dashboard. It was straightforward to switch to [day-js](https://day.js.org/) which is largely API-compatible with `Moment.js`. This change reduced our gzipped bundle size by 68 KB.\n\nWe migrated from `Joi` to `ajv` for our schema validation which was 32% smaller. `ajv` was already bundled as a transitive dependency of other modules, making it a no-brainer.\n\n![NPM dependencies](/images/blog/blog/npm-dependencies.png)\n\nWe reverted our [crypto-js](https://github.com/brix/crypto-js) module from version 4.0 to 3.3.0. Version 4.0 [injects more than 400kb code](https://github.com/brix/crypto-js/issues/276) when used in a browser context. The newer version replaces `Math.random` with node's implementation, injecting the entire node crypto module into the browser context. We use `crypto-js` for decrypting user's API keys and so we're not reliant on the randomness of the PRNG. We might move to a dedicated module like [aes-js](https://www.npmjs.com/package/aes-js) in the future since it has a much smaller surface area than `crypto-js` (in terms of security and performance).\n\n### Using partial imports\n\nBy selectively importing functions from modules like `lodash`, we cut the gzipped size by another 40kb across all our bundles.\n\n```js\n// before\nimport _ from 'lodash'\\n\n// maunally cherry picking modules\nimport find from 'lodash/find'\nimport debounce from 'lodash/debounce'\\n\n// using babel-plugin-lodash\nimport { find, debounce } from 'lodash'\n```\n\nIn the above example, we added [babel-plugin-lodash](https://github.com/lodash/babel-plugin-lodash) to our babel configuration which cherry picks the exact `lodash` functions we import. This makes it easier to import from `lodash` without cluttering the code with selective import statements.\n\n### Moving complex logic to the server\n\nThanks to some skilled haxors (well, weak passwords mainly) we had crypto miners running on some of our customer's databases. To prevent this, we enforce password strength with the [zxcvbn](https://github.com/dropbox/zxcvbn) module. Though it improved our overall security, the module is [pretty big](https://bundlephobia.com/result?p=zxcvbn@4.4.2), weighing in at 388kb gzipped. To get around this, we moved the password-strength checking logic to an API. Now, the frontend queries a server with a user-supplied password and the server computes its strength. This eliminates the module from the frontend.\n\n### Lazy loading code\n\n[xlsx](https://github.com/SheetJS/sheetjs) is another complex and large module, which is used to import spreadsheets into tables. We contemplated moving this logic to the backend, but we found another solution: lazy loading it.\n\nThe spreadsheet import is triggered when the user is creating a new table. However the code was previously loaded every time the page was visited - even when a new table was not being created. This made it a good candidate for lazy loading. Using [Next.js dynamic imports](https://nextjs.org/docs/advanced-features/dynamic-import) we are able to load this component (313 kb brotlied) dynamically, whenever the user clicks the \"Add content\" button.\n\n\u003cvideo width=\"99%\" muted playsInline controls={true}\u003e\n  \u003csource\n    src=\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/lazy_loading.mov\"\n    type=\"video/mp4\"\n  /\u003e\n\u003c/video\u003e\n\nWe use the same technique to lazy load some Lottie animations which are relatively large.\n\n### Using native browser APIs\n\nWe decided against supporting IE11, opening up more avenues for optimization. Using native browser APIs enabled us to drop even more dependencies. For example, since the [fetch API is available](https://caniuse.com/fetch) in all the browsers we care about, we removed [axios](https://github.com/axios/axios) and built a simple wrapper using the native fetch API.\n\n## Improving Vercel's default caching\n\n\u003e The fastest request is the request not made\n\nWe noticed that Vercel was sending a `Cache-Control` header of `public, max-age=0, must-revalidate` , preventing some of our SVG, CSS and font files from being cached in the browser.\n\nWe updated our `next.config.js` , adding a long `max-age` to the caching header that Vercel sends. Our assets are versioned properly, so we were able to safely do this.\n\n## Enabling Next.js Automatic Static Optimization\n\nNext.js is able to automatically pre-render a page to HTML, whenever a page meets some pre-conditions. This mode is called [Automatic Static Optimization](https://nextjs.org/docs/advanced-features/automatic-static-optimization). Pre-rendered pages can be cached on a CDN for extremely fast page loads. We removed calls to `getServerSideProps` and `getInitialProps` to take advantage of this mode.\n\n## Developing a performance culture\n\n\u003e Always in sight, always in mind\n\nOur performance optimization journey will never be complete. It requires constant vigilance to maintain a baseline across our users. To instill this within our team, we took a few actions.\n\nWe developed a Slack bot which sends our Sentry performance dashboard every week, containing our slowest transactions and our Core Web Vitals summary. This shows which pages need improvement and where our [users are the most miserable](https://docs.sentry.io/product/performance/metrics/#user-misery).\n\nDuring our transition from Alpha to Beta, performance was one of the important features, along with stability and security. We considered performance implications while choosing libraries and tools. Having a \"seat at the table\" in these discussions ensures that performance is not considered as an after-thought.\n\n## Results\n\nWith these changes, we have a respectable Core Web Vitals score. This is a snapshot from Sentry with RUM data from the last week. We are within the recommended threshold for all the 3 Web Vitals.\n\n![Sentry results](/images/blog/blog/sentry-results.png)\n\nOur Next.js build output also shows that users download \u003c 200 kb of JavaScript between any two page transitions. We're still improving too - even though we provide a lot of functionality in our dashboard, we will continue to reduce our bundle sizes.\n\n## Things that did not work\n\n\u003e You win some, you lose some\n\nWe tried a VSCode plugin called [Import cost](https://marketplace.visualstudio.com/items?itemName=wix.vscode-import-cost) which shows the size of JavaScript modules when you import it in your editor. However, the plugin did not work on our codebase since it doesn't support some JavaScript features, like optional chaining.\n\nWe also passed on using [lodash-webpack-plugin](https://www.npmjs.com/package/lodash-webpack-plugin) even though it had the potential to reduce our JavaScript sizes, because it could potentially break our code if not used carefully. This plugin would require our frontend team to understand the Webpack configuration, updating it whenever they use a new lodash feature set.\n\n## The road ahead\n\nOur broad goal is to implement best practices for frontend performance, and make it exciting to all of our team. These are some ideas we have on our roadmap -\n\n- Set up [Lighthouse](https://developers.google.com/web/tools/lighthouse) in a GitHub Action to catch performance regression earlier in the development life cycle.\n- Continue reducing our initial JavaScript payload size, to improve our LCP time\n- Explore `cloud-mode` in [Segment](https://segment.com/docs/connections/destinations/) which makes API calls from the server instead of loading third-party library on the browser.\n\nReach out to us on [Twitter](https://twitter.com/supabase) if you have more ideas to speed up our website ⚡\n","title":"Making the Supabase Dashboard Supa-fast","description":"Improving the performance of the Supabase dashboard","author":"inian","author_title":"Supabase","author_url":"https://github.com/inian","author_image_url":"https://github.com/inian.png","categories":["product"],"tags":["supabase"],"date":"12-13-2020","content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    a: \"a\",\n    blockquote: \"blockquote\",\n    h2: \"h2\",\n    img: \"img\",\n    h3: \"h3\",\n    code: \"code\",\n    ul: \"ul\",\n    li: \"li\"\n  }, _provideComponents(), props.components), {CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The Supabase dashboard has become more feature-rich in the last month. We have a powerful SQL editor backed by \", _jsx(_components.a, {\n        href: \"https://microsoft.github.io/monaco-editor/\",\n        children: \"Monaco\"\n      }), \". We built an Airtable-like view of your database, making editing a breeze.\"]\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsx(_components.p, {\n        children: \"Features, performance, DX - choose three\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Performance can quickly regress when adding new features, especially in a Single Page Application. Here are the steps we took to guarantee a good baseline performance within our application, without compromising on the developer experience (DX).\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"establishing-a-baseline-and-setting-targets\",\n      children: \"Establishing a baseline and setting targets\"\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsx(_components.p, {\n        children: \"You can't fix what you can't measure\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"There was some low-hanging fruit to improve performance, but we had one important thing to do before that - establish a baseline.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Our dashboard is JavaScript heavy, so we started by setting up analytics to track our bundle sizes. \", _jsx(_components.a, {\n        href: \"https://www.npmjs.com/package/@next/bundle-analyzer\",\n        children: \"Next-bundle-analyzer\"\n      }), \" (or \", _jsx(_components.a, {\n        href: \"https://www.npmjs.com/package/webpack-bundle-analyzer\",\n        children: \"webpack-bundle-analyzer\"\n      }), \") provides an interactive treemap of your generated JavaScript bundles. This is our treemap when we started. It gave us a clear indication what changes we needed to achieve the most impact.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/blog/nextjs-tree-analyzer.png\",\n        alt: \"Nextjs tree analyzer\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"There are some great tools when it comes to Real User Monitoring (RUM). We chose the newly-launched \", _jsx(_components.a, {\n        href: \"https://sentry.io/for/performance/\",\n        children: \"Sentry performance monitoring\"\n      }), \" product since we already use Sentry for error tracking and we wanted to minimize new tools in our stack. It also supports reporting \", _jsx(_components.a, {\n        href: \"https://web.dev/vitals/\",\n        children: \"Core Web Vitals\"\n      }), \", the performance metrics created by Google to track initial loading performance, responsiveness and visual stability. Core Web Vitals come with recommended target values, giving us clear goals to hit.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/blog/core-web-vitals.png\",\n        alt: \"Core Web Vitals\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"improving-our-javascript-bundle-size\",\n      children: \"Improving our JavaScript bundle size\"\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsx(_components.p, {\n        children: \"How to not load the entire npm registry into our user's browsers\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"choosing-smaller-modules\",\n      children: \"Choosing smaller modules\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We used \", _jsx(_components.a, {\n        href: \"https://bundlephobia.com/\",\n        children: \"Bundlephobia\"\n      }), \" on our largest modules. This is a great website to have in your JS-performance arsenal. It gives the size of npm modules across different versions and recommends alternate modules with similar functionality which are smaller.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.code, {\n        children: \"Moment.js\"\n      }), \" is notorious for its large bundle size and we don't need complex date processing for our dashboard. It was straightforward to switch to \", _jsx(_components.a, {\n        href: \"https://day.js.org/\",\n        children: \"day-js\"\n      }), \" which is largely API-compatible with \", _jsx(_components.code, {\n        children: \"Moment.js\"\n      }), \". This change reduced our gzipped bundle size by 68 KB.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We migrated from \", _jsx(_components.code, {\n        children: \"Joi\"\n      }), \" to \", _jsx(_components.code, {\n        children: \"ajv\"\n      }), \" for our schema validation which was 32% smaller. \", _jsx(_components.code, {\n        children: \"ajv\"\n      }), \" was already bundled as a transitive dependency of other modules, making it a no-brainer.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/blog/npm-dependencies.png\",\n        alt: \"NPM dependencies\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We reverted our \", _jsx(_components.a, {\n        href: \"https://github.com/brix/crypto-js\",\n        children: \"crypto-js\"\n      }), \" module from version 4.0 to 3.3.0. Version 4.0 \", _jsx(_components.a, {\n        href: \"https://github.com/brix/crypto-js/issues/276\",\n        children: \"injects more than 400kb code\"\n      }), \" when used in a browser context. The newer version replaces \", _jsx(_components.code, {\n        children: \"Math.random\"\n      }), \" with node's implementation, injecting the entire node crypto module into the browser context. We use \", _jsx(_components.code, {\n        children: \"crypto-js\"\n      }), \" for decrypting user's API keys and so we're not reliant on the randomness of the PRNG. We might move to a dedicated module like \", _jsx(_components.a, {\n        href: \"https://www.npmjs.com/package/aes-js\",\n        children: \"aes-js\"\n      }), \" in the future since it has a much smaller surface area than \", _jsx(_components.code, {\n        children: \"crypto-js\"\n      }), \" (in terms of security and performance).\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"using-partial-imports\",\n      children: \"Using partial imports\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"By selectively importing functions from modules like \", _jsx(_components.code, {\n        children: \"lodash\"\n      }), \", we cut the gzipped size by another 40kb across all our bundles.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// before\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" _ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'lodash'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\\n\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"// maunally cherry picking modules\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" find \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'lodash/find'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" debounce \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'lodash/debounce'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\\n\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"// using babel-plugin-lodash\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { find, debounce } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'lodash'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"js\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In the above example, we added \", _jsx(_components.a, {\n        href: \"https://github.com/lodash/babel-plugin-lodash\",\n        children: \"babel-plugin-lodash\"\n      }), \" to our babel configuration which cherry picks the exact \", _jsx(_components.code, {\n        children: \"lodash\"\n      }), \" functions we import. This makes it easier to import from \", _jsx(_components.code, {\n        children: \"lodash\"\n      }), \" without cluttering the code with selective import statements.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"moving-complex-logic-to-the-server\",\n      children: \"Moving complex logic to the server\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Thanks to some skilled haxors (well, weak passwords mainly) we had crypto miners running on some of our customer's databases. To prevent this, we enforce password strength with the \", _jsx(_components.a, {\n        href: \"https://github.com/dropbox/zxcvbn\",\n        children: \"zxcvbn\"\n      }), \" module. Though it improved our overall security, the module is \", _jsx(_components.a, {\n        href: \"https://bundlephobia.com/result?p=zxcvbn@4.4.2\",\n        children: \"pretty big\"\n      }), \", weighing in at 388kb gzipped. To get around this, we moved the password-strength checking logic to an API. Now, the frontend queries a server with a user-supplied password and the server computes its strength. This eliminates the module from the frontend.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"lazy-loading-code\",\n      children: \"Lazy loading code\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://github.com/SheetJS/sheetjs\",\n        children: \"xlsx\"\n      }), \" is another complex and large module, which is used to import spreadsheets into tables. We contemplated moving this logic to the backend, but we found another solution: lazy loading it.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The spreadsheet import is triggered when the user is creating a new table. However the code was previously loaded every time the page was visited - even when a new table was not being created. This made it a good candidate for lazy loading. Using \", _jsx(_components.a, {\n        href: \"https://nextjs.org/docs/advanced-features/dynamic-import\",\n        children: \"Next.js dynamic imports\"\n      }), \" we are able to load this component (313 kb brotlied) dynamically, whenever the user clicks the \\\"Add content\\\" button.\"]\n    }), \"\\n\", _jsx(\"video\", {\n      width: \"99%\",\n      muted: true,\n      playsInline: true,\n      controls: true,\n      children: _jsx(\"source\", {\n        src: \"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/lazy_loading.mov\",\n        type: \"video/mp4\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We use the same technique to lazy load some Lottie animations which are relatively large.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"using-native-browser-apis\",\n      children: \"Using native browser APIs\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We decided against supporting IE11, opening up more avenues for optimization. Using native browser APIs enabled us to drop even more dependencies. For example, since the \", _jsx(_components.a, {\n        href: \"https://caniuse.com/fetch\",\n        children: \"fetch API is available\"\n      }), \" in all the browsers we care about, we removed \", _jsx(_components.a, {\n        href: \"https://github.com/axios/axios\",\n        children: \"axios\"\n      }), \" and built a simple wrapper using the native fetch API.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"improving-vercels-default-caching\",\n      children: \"Improving Vercel's default caching\"\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsx(_components.p, {\n        children: \"The fastest request is the request not made\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We noticed that Vercel was sending a \", _jsx(_components.code, {\n        children: \"Cache-Control\"\n      }), \" header of \", _jsx(_components.code, {\n        children: \"public, max-age=0, must-revalidate\"\n      }), \" , preventing some of our SVG, CSS and font files from being cached in the browser.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We updated our \", _jsx(_components.code, {\n        children: \"next.config.js\"\n      }), \" , adding a long \", _jsx(_components.code, {\n        children: \"max-age\"\n      }), \" to the caching header that Vercel sends. Our assets are versioned properly, so we were able to safely do this.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"enabling-nextjs-automatic-static-optimization\",\n      children: \"Enabling Next.js Automatic Static Optimization\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Next.js is able to automatically pre-render a page to HTML, whenever a page meets some pre-conditions. This mode is called \", _jsx(_components.a, {\n        href: \"https://nextjs.org/docs/advanced-features/automatic-static-optimization\",\n        children: \"Automatic Static Optimization\"\n      }), \". Pre-rendered pages can be cached on a CDN for extremely fast page loads. We removed calls to \", _jsx(_components.code, {\n        children: \"getServerSideProps\"\n      }), \" and \", _jsx(_components.code, {\n        children: \"getInitialProps\"\n      }), \" to take advantage of this mode.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"developing-a-performance-culture\",\n      children: \"Developing a performance culture\"\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsx(_components.p, {\n        children: \"Always in sight, always in mind\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Our performance optimization journey will never be complete. It requires constant vigilance to maintain a baseline across our users. To instill this within our team, we took a few actions.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We developed a Slack bot which sends our Sentry performance dashboard every week, containing our slowest transactions and our Core Web Vitals summary. This shows which pages need improvement and where our \", _jsx(_components.a, {\n        href: \"https://docs.sentry.io/product/performance/metrics/#user-misery\",\n        children: \"users are the most miserable\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"During our transition from Alpha to Beta, performance was one of the important features, along with stability and security. We considered performance implications while choosing libraries and tools. Having a \\\"seat at the table\\\" in these discussions ensures that performance is not considered as an after-thought.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"results\",\n      children: \"Results\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"With these changes, we have a respectable Core Web Vitals score. This is a snapshot from Sentry with RUM data from the last week. We are within the recommended threshold for all the 3 Web Vitals.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"/images/blog/blog/sentry-results.png\",\n        alt: \"Sentry results\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Our Next.js build output also shows that users download \u003c 200 kb of JavaScript between any two page transitions. We're still improving too - even though we provide a lot of functionality in our dashboard, we will continue to reduce our bundle sizes.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"things-that-did-not-work\",\n      children: \"Things that did not work\"\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsx(_components.p, {\n        children: \"You win some, you lose some\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We tried a VSCode plugin called \", _jsx(_components.a, {\n        href: \"https://marketplace.visualstudio.com/items?itemName=wix.vscode-import-cost\",\n        children: \"Import cost\"\n      }), \" which shows the size of JavaScript modules when you import it in your editor. However, the plugin did not work on our codebase since it doesn't support some JavaScript features, like optional chaining.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We also passed on using \", _jsx(_components.a, {\n        href: \"https://www.npmjs.com/package/lodash-webpack-plugin\",\n        children: \"lodash-webpack-plugin\"\n      }), \" even though it had the potential to reduce our JavaScript sizes, because it could potentially break our code if not used carefully. This plugin would require our frontend team to understand the Webpack configuration, updating it whenever they use a new lodash feature set.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"the-road-ahead\",\n      children: \"The road ahead\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Our broad goal is to implement best practices for frontend performance, and make it exciting to all of our team. These are some ideas we have on our roadmap -\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Set up \", _jsx(_components.a, {\n          href: \"https://developers.google.com/web/tools/lighthouse\",\n          children: \"Lighthouse\"\n        }), \" in a GitHub Action to catch performance regression earlier in the development life cycle.\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Continue reducing our initial JavaScript payload size, to improve our LCP time\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Explore \", _jsx(_components.code, {\n          children: \"cloud-mode\"\n        }), \" in \", _jsx(_components.a, {\n          href: \"https://segment.com/docs/connections/destinations/\",\n          children: \"Segment\"\n        }), \" which makes API calls from the server instead of loading third-party library on the browser.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Reach out to us on \", _jsx(_components.a, {\n        href: \"https://twitter.com/supabase\",\n        children: \"Twitter\"\n      }), \" if you have more ideas to speed up our website ⚡\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"Establishing a baseline and setting targets","slug":"establishing-a-baseline-and-setting-targets","lvl":2,"i":0,"seen":0},{"content":"Improving our JavaScript bundle size","slug":"improving-our-javascript-bundle-size","lvl":2,"i":1,"seen":0},{"content":"Choosing smaller modules","slug":"choosing-smaller-modules","lvl":3,"i":2,"seen":0},{"content":"Using partial imports","slug":"using-partial-imports","lvl":3,"i":3,"seen":0},{"content":"Moving complex logic to the server","slug":"moving-complex-logic-to-the-server","lvl":3,"i":4,"seen":0},{"content":"Lazy loading code","slug":"lazy-loading-code","lvl":3,"i":5,"seen":0},{"content":"Using native browser APIs","slug":"using-native-browser-apis","lvl":3,"i":6,"seen":0},{"content":"Improving Vercel's default caching","slug":"improving-vercels-default-caching","lvl":2,"i":7,"seen":0},{"content":"Enabling Next.js Automatic Static Optimization","slug":"enabling-nextjs-automatic-static-optimization","lvl":2,"i":8,"seen":0},{"content":"Developing a performance culture","slug":"developing-a-performance-culture","lvl":2,"i":9,"seen":0},{"content":"Results","slug":"results","lvl":2,"i":10,"seen":0},{"content":"Things that did not work","slug":"things-that-did-not-work","lvl":2,"i":11,"seen":0},{"content":"The road ahead","slug":"the-road-ahead","lvl":2,"i":12,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,2],"level":0},{"type":"inline","content":"The Supabase dashboard has become more feature-rich in the last month. We have a powerful SQL editor backed by [Monaco](https://microsoft.github.io/monaco-editor/). We built an Airtable-like view of your database, making editing a breeze.","level":1,"lines":[1,2],"children":[{"type":"text","content":"The Supabase dashboard has become more feature-rich in the last month. We have a powerful SQL editor backed by ","level":0},{"type":"link_open","href":"https://microsoft.github.io/monaco-editor/","title":"","level":0},{"type":"text","content":"Monaco","level":1},{"type":"link_close","level":0},{"type":"text","content":". We built an Airtable-like view of your database, making editing a breeze.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"blockquote_open","lines":[3,4],"level":0},{"type":"paragraph_open","tight":false,"lines":[3,4],"level":1},{"type":"inline","content":"Features, performance, DX - choose three","level":2,"lines":[3,4],"children":[{"type":"text","content":"Features, performance, DX - choose three","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"paragraph_open","tight":false,"lines":[5,6],"level":0},{"type":"inline","content":"Performance can quickly regress when adding new features, especially in a Single Page Application. Here are the steps we took to guarantee a good baseline performance within our application, without compromising on the developer experience (DX).","level":1,"lines":[5,6],"children":[{"type":"text","content":"Performance can quickly regress when adding new features, especially in a Single Page Application. Here are the steps we took to guarantee a good baseline performance within our application, without compromising on the developer experience (DX).","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[7,8],"level":0},{"type":"inline","content":"[Establishing a baseline and setting targets](#establishing-a-baseline-and-setting-targets)","level":1,"lines":[7,8],"children":[{"type":"text","content":"Establishing a baseline and setting targets","level":0}],"lvl":2,"i":0,"seen":0,"slug":"establishing-a-baseline-and-setting-targets"},{"type":"heading_close","hLevel":2,"level":0},{"type":"blockquote_open","lines":[9,10],"level":0},{"type":"paragraph_open","tight":false,"lines":[9,10],"level":1},{"type":"inline","content":"You can't fix what you can't measure","level":2,"lines":[9,10],"children":[{"type":"text","content":"You can't fix what you can't measure","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"paragraph_open","tight":false,"lines":[11,12],"level":0},{"type":"inline","content":"There was some low-hanging fruit to improve performance, but we had one important thing to do before that - establish a baseline.","level":1,"lines":[11,12],"children":[{"type":"text","content":"There was some low-hanging fruit to improve performance, but we had one important thing to do before that - establish a baseline.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[13,14],"level":0},{"type":"inline","content":"Our dashboard is JavaScript heavy, so we started by setting up analytics to track our bundle sizes. [Next-bundle-analyzer](https://www.npmjs.com/package/@next/bundle-analyzer) (or [webpack-bundle-analyzer](https://www.npmjs.com/package/webpack-bundle-analyzer)) provides an interactive treemap of your generated JavaScript bundles. This is our treemap when we started. It gave us a clear indication what changes we needed to achieve the most impact.","level":1,"lines":[13,14],"children":[{"type":"text","content":"Our dashboard is JavaScript heavy, so we started by setting up analytics to track our bundle sizes. ","level":0},{"type":"link_open","href":"https://www.npmjs.com/package/@next/bundle-analyzer","title":"","level":0},{"type":"text","content":"Next-bundle-analyzer","level":1},{"type":"link_close","level":0},{"type":"text","content":" (or ","level":0},{"type":"link_open","href":"https://www.npmjs.com/package/webpack-bundle-analyzer","title":"","level":0},{"type":"text","content":"webpack-bundle-analyzer","level":1},{"type":"link_close","level":0},{"type":"text","content":") provides an interactive treemap of your generated JavaScript bundles. This is our treemap when we started. It gave us a clear indication what changes we needed to achieve the most impact.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[15,16],"level":0},{"type":"inline","content":"![Nextjs tree analyzer](/images/blog/blog/nextjs-tree-analyzer.png)","level":1,"lines":[15,16],"children":[{"type":"image","src":"/images/blog/blog/nextjs-tree-analyzer.png","title":"","alt":"Nextjs tree analyzer","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[17,18],"level":0},{"type":"inline","content":"There are some great tools when it comes to Real User Monitoring (RUM). We chose the newly-launched [Sentry performance monitoring](https://sentry.io/for/performance/) product since we already use Sentry for error tracking and we wanted to minimize new tools in our stack. It also supports reporting [Core Web Vitals](https://web.dev/vitals/), the performance metrics created by Google to track initial loading performance, responsiveness and visual stability. Core Web Vitals come with recommended target values, giving us clear goals to hit.","level":1,"lines":[17,18],"children":[{"type":"text","content":"There are some great tools when it comes to Real User Monitoring (RUM). We chose the newly-launched ","level":0},{"type":"link_open","href":"https://sentry.io/for/performance/","title":"","level":0},{"type":"text","content":"Sentry performance monitoring","level":1},{"type":"link_close","level":0},{"type":"text","content":" product since we already use Sentry for error tracking and we wanted to minimize new tools in our stack. It also supports reporting ","level":0},{"type":"link_open","href":"https://web.dev/vitals/","title":"","level":0},{"type":"text","content":"Core Web Vitals","level":1},{"type":"link_close","level":0},{"type":"text","content":", the performance metrics created by Google to track initial loading performance, responsiveness and visual stability. Core Web Vitals come with recommended target values, giving us clear goals to hit.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[19,20],"level":0},{"type":"inline","content":"![Core Web Vitals](/images/blog/blog/core-web-vitals.png)","level":1,"lines":[19,20],"children":[{"type":"image","src":"/images/blog/blog/core-web-vitals.png","title":"","alt":"Core Web Vitals","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[21,22],"level":0},{"type":"inline","content":"[Improving our JavaScript bundle size](#improving-our-javascript-bundle-size)","level":1,"lines":[21,22],"children":[{"type":"text","content":"Improving our JavaScript bundle size","level":0}],"lvl":2,"i":1,"seen":0,"slug":"improving-our-javascript-bundle-size"},{"type":"heading_close","hLevel":2,"level":0},{"type":"blockquote_open","lines":[23,24],"level":0},{"type":"paragraph_open","tight":false,"lines":[23,24],"level":1},{"type":"inline","content":"How to not load the entire npm registry into our user's browsers","level":2,"lines":[23,24],"children":[{"type":"text","content":"How to not load the entire npm registry into our user's browsers","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"heading_open","hLevel":3,"lines":[25,26],"level":0},{"type":"inline","content":"[Choosing smaller modules](#choosing-smaller-modules)","level":1,"lines":[25,26],"children":[{"type":"text","content":"Choosing smaller modules","level":0}],"lvl":3,"i":2,"seen":0,"slug":"choosing-smaller-modules"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[27,28],"level":0},{"type":"inline","content":"We used [Bundlephobia](https://bundlephobia.com/) on our largest modules. This is a great website to have in your JS-performance arsenal. It gives the size of npm modules across different versions and recommends alternate modules with similar functionality which are smaller.","level":1,"lines":[27,28],"children":[{"type":"text","content":"We used ","level":0},{"type":"link_open","href":"https://bundlephobia.com/","title":"","level":0},{"type":"text","content":"Bundlephobia","level":1},{"type":"link_close","level":0},{"type":"text","content":" on our largest modules. This is a great website to have in your JS-performance arsenal. It gives the size of npm modules across different versions and recommends alternate modules with similar functionality which are smaller.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[29,30],"level":0},{"type":"inline","content":"`Moment.js` is notorious for its large bundle size and we don't need complex date processing for our dashboard. It was straightforward to switch to [day-js](https://day.js.org/) which is largely API-compatible with `Moment.js`. This change reduced our gzipped bundle size by 68 KB.","level":1,"lines":[29,30],"children":[{"type":"code","content":"Moment.js","block":false,"level":0},{"type":"text","content":" is notorious for its large bundle size and we don't need complex date processing for our dashboard. It was straightforward to switch to ","level":0},{"type":"link_open","href":"https://day.js.org/","title":"","level":0},{"type":"text","content":"day-js","level":1},{"type":"link_close","level":0},{"type":"text","content":" which is largely API-compatible with ","level":0},{"type":"code","content":"Moment.js","block":false,"level":0},{"type":"text","content":". This change reduced our gzipped bundle size by 68 KB.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[31,32],"level":0},{"type":"inline","content":"We migrated from `Joi` to `ajv` for our schema validation which was 32% smaller. `ajv` was already bundled as a transitive dependency of other modules, making it a no-brainer.","level":1,"lines":[31,32],"children":[{"type":"text","content":"We migrated from ","level":0},{"type":"code","content":"Joi","block":false,"level":0},{"type":"text","content":" to ","level":0},{"type":"code","content":"ajv","block":false,"level":0},{"type":"text","content":" for our schema validation which was 32% smaller. ","level":0},{"type":"code","content":"ajv","block":false,"level":0},{"type":"text","content":" was already bundled as a transitive dependency of other modules, making it a no-brainer.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[33,34],"level":0},{"type":"inline","content":"![NPM dependencies](/images/blog/blog/npm-dependencies.png)","level":1,"lines":[33,34],"children":[{"type":"image","src":"/images/blog/blog/npm-dependencies.png","title":"","alt":"NPM dependencies","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[35,36],"level":0},{"type":"inline","content":"We reverted our [crypto-js](https://github.com/brix/crypto-js) module from version 4.0 to 3.3.0. Version 4.0 [injects more than 400kb code](https://github.com/brix/crypto-js/issues/276) when used in a browser context. The newer version replaces `Math.random` with node's implementation, injecting the entire node crypto module into the browser context. We use `crypto-js` for decrypting user's API keys and so we're not reliant on the randomness of the PRNG. We might move to a dedicated module like [aes-js](https://www.npmjs.com/package/aes-js) in the future since it has a much smaller surface area than `crypto-js` (in terms of security and performance).","level":1,"lines":[35,36],"children":[{"type":"text","content":"We reverted our ","level":0},{"type":"link_open","href":"https://github.com/brix/crypto-js","title":"","level":0},{"type":"text","content":"crypto-js","level":1},{"type":"link_close","level":0},{"type":"text","content":" module from version 4.0 to 3.3.0. Version 4.0 ","level":0},{"type":"link_open","href":"https://github.com/brix/crypto-js/issues/276","title":"","level":0},{"type":"text","content":"injects more than 400kb code","level":1},{"type":"link_close","level":0},{"type":"text","content":" when used in a browser context. The newer version replaces ","level":0},{"type":"code","content":"Math.random","block":false,"level":0},{"type":"text","content":" with node's implementation, injecting the entire node crypto module into the browser context. We use ","level":0},{"type":"code","content":"crypto-js","block":false,"level":0},{"type":"text","content":" for decrypting user's API keys and so we're not reliant on the randomness of the PRNG. We might move to a dedicated module like ","level":0},{"type":"link_open","href":"https://www.npmjs.com/package/aes-js","title":"","level":0},{"type":"text","content":"aes-js","level":1},{"type":"link_close","level":0},{"type":"text","content":" in the future since it has a much smaller surface area than ","level":0},{"type":"code","content":"crypto-js","block":false,"level":0},{"type":"text","content":" (in terms of security and performance).","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[37,38],"level":0},{"type":"inline","content":"[Using partial imports](#using-partial-imports)","level":1,"lines":[37,38],"children":[{"type":"text","content":"Using partial imports","level":0}],"lvl":3,"i":3,"seen":0,"slug":"using-partial-imports"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[39,40],"level":0},{"type":"inline","content":"By selectively importing functions from modules like `lodash`, we cut the gzipped size by another 40kb across all our bundles.","level":1,"lines":[39,40],"children":[{"type":"text","content":"By selectively importing functions from modules like ","level":0},{"type":"code","content":"lodash","block":false,"level":0},{"type":"text","content":", we cut the gzipped size by another 40kb across all our bundles.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"js","content":"// before\nimport _ from 'lodash'\\n\n// maunally cherry picking modules\nimport find from 'lodash/find'\nimport debounce from 'lodash/debounce'\\n\n// using babel-plugin-lodash\nimport { find, debounce } from 'lodash'\n","lines":[41,50],"level":0},{"type":"paragraph_open","tight":false,"lines":[51,52],"level":0},{"type":"inline","content":"In the above example, we added [babel-plugin-lodash](https://github.com/lodash/babel-plugin-lodash) to our babel configuration which cherry picks the exact `lodash` functions we import. This makes it easier to import from `lodash` without cluttering the code with selective import statements.","level":1,"lines":[51,52],"children":[{"type":"text","content":"In the above example, we added ","level":0},{"type":"link_open","href":"https://github.com/lodash/babel-plugin-lodash","title":"","level":0},{"type":"text","content":"babel-plugin-lodash","level":1},{"type":"link_close","level":0},{"type":"text","content":" to our babel configuration which cherry picks the exact ","level":0},{"type":"code","content":"lodash","block":false,"level":0},{"type":"text","content":" functions we import. This makes it easier to import from ","level":0},{"type":"code","content":"lodash","block":false,"level":0},{"type":"text","content":" without cluttering the code with selective import statements.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[53,54],"level":0},{"type":"inline","content":"[Moving complex logic to the server](#moving-complex-logic-to-the-server)","level":1,"lines":[53,54],"children":[{"type":"text","content":"Moving complex logic to the server","level":0}],"lvl":3,"i":4,"seen":0,"slug":"moving-complex-logic-to-the-server"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[55,56],"level":0},{"type":"inline","content":"Thanks to some skilled haxors (well, weak passwords mainly) we had crypto miners running on some of our customer's databases. To prevent this, we enforce password strength with the [zxcvbn](https://github.com/dropbox/zxcvbn) module. Though it improved our overall security, the module is [pretty big](https://bundlephobia.com/result?p=zxcvbn@4.4.2), weighing in at 388kb gzipped. To get around this, we moved the password-strength checking logic to an API. Now, the frontend queries a server with a user-supplied password and the server computes its strength. This eliminates the module from the frontend.","level":1,"lines":[55,56],"children":[{"type":"text","content":"Thanks to some skilled haxors (well, weak passwords mainly) we had crypto miners running on some of our customer's databases. To prevent this, we enforce password strength with the ","level":0},{"type":"link_open","href":"https://github.com/dropbox/zxcvbn","title":"","level":0},{"type":"text","content":"zxcvbn","level":1},{"type":"link_close","level":0},{"type":"text","content":" module. Though it improved our overall security, the module is ","level":0},{"type":"link_open","href":"https://bundlephobia.com/result?p=zxcvbn@4.4.2","title":"","level":0},{"type":"text","content":"pretty big","level":1},{"type":"link_close","level":0},{"type":"text","content":", weighing in at 388kb gzipped. To get around this, we moved the password-strength checking logic to an API. Now, the frontend queries a server with a user-supplied password and the server computes its strength. This eliminates the module from the frontend.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[57,58],"level":0},{"type":"inline","content":"[Lazy loading code](#lazy-loading-code)","level":1,"lines":[57,58],"children":[{"type":"text","content":"Lazy loading code","level":0}],"lvl":3,"i":5,"seen":0,"slug":"lazy-loading-code"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[59,60],"level":0},{"type":"inline","content":"[xlsx](https://github.com/SheetJS/sheetjs) is another complex and large module, which is used to import spreadsheets into tables. We contemplated moving this logic to the backend, but we found another solution: lazy loading it.","level":1,"lines":[59,60],"children":[{"type":"link_open","href":"https://github.com/SheetJS/sheetjs","title":"","level":0},{"type":"text","content":"xlsx","level":1},{"type":"link_close","level":0},{"type":"text","content":" is another complex and large module, which is used to import spreadsheets into tables. We contemplated moving this logic to the backend, but we found another solution: lazy loading it.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[61,62],"level":0},{"type":"inline","content":"The spreadsheet import is triggered when the user is creating a new table. However the code was previously loaded every time the page was visited - even when a new table was not being created. This made it a good candidate for lazy loading. Using [Next.js dynamic imports](https://nextjs.org/docs/advanced-features/dynamic-import) we are able to load this component (313 kb brotlied) dynamically, whenever the user clicks the \"Add content\" button.","level":1,"lines":[61,62],"children":[{"type":"text","content":"The spreadsheet import is triggered when the user is creating a new table. However the code was previously loaded every time the page was visited - even when a new table was not being created. This made it a good candidate for lazy loading. Using ","level":0},{"type":"link_open","href":"https://nextjs.org/docs/advanced-features/dynamic-import","title":"","level":0},{"type":"text","content":"Next.js dynamic imports","level":1},{"type":"link_close","level":0},{"type":"text","content":" we are able to load this component (313 kb brotlied) dynamically, whenever the user clicks the \"Add content\" button.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[63,69],"level":0},{"type":"inline","content":"\u003cvideo width=\"99%\" muted playsInline controls={true}\u003e\n  \u003csource\n    src=\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/lazy_loading.mov\"\n    type=\"video/mp4\"\n  /\u003e\n\u003c/video\u003e","level":1,"lines":[63,69],"children":[{"type":"text","content":"\u003cvideo width=\"99%\" muted playsInline controls={true}\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003csource","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://xguihxuzqibwxjnimxev.supabase.co/storage/v1/object/public/videos/docs/lazy_loading.mov\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"type=\"video/mp4\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/video\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[70,71],"level":0},{"type":"inline","content":"We use the same technique to lazy load some Lottie animations which are relatively large.","level":1,"lines":[70,71],"children":[{"type":"text","content":"We use the same technique to lazy load some Lottie animations which are relatively large.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[72,73],"level":0},{"type":"inline","content":"[Using native browser APIs](#using-native-browser-apis)","level":1,"lines":[72,73],"children":[{"type":"text","content":"Using native browser APIs","level":0}],"lvl":3,"i":6,"seen":0,"slug":"using-native-browser-apis"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[74,75],"level":0},{"type":"inline","content":"We decided against supporting IE11, opening up more avenues for optimization. Using native browser APIs enabled us to drop even more dependencies. For example, since the [fetch API is available](https://caniuse.com/fetch) in all the browsers we care about, we removed [axios](https://github.com/axios/axios) and built a simple wrapper using the native fetch API.","level":1,"lines":[74,75],"children":[{"type":"text","content":"We decided against supporting IE11, opening up more avenues for optimization. Using native browser APIs enabled us to drop even more dependencies. For example, since the ","level":0},{"type":"link_open","href":"https://caniuse.com/fetch","title":"","level":0},{"type":"text","content":"fetch API is available","level":1},{"type":"link_close","level":0},{"type":"text","content":" in all the browsers we care about, we removed ","level":0},{"type":"link_open","href":"https://github.com/axios/axios","title":"","level":0},{"type":"text","content":"axios","level":1},{"type":"link_close","level":0},{"type":"text","content":" and built a simple wrapper using the native fetch API.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[76,77],"level":0},{"type":"inline","content":"[Improving Vercel's default caching](#improving-vercels-default-caching)","level":1,"lines":[76,77],"children":[{"type":"text","content":"Improving Vercel's default caching","level":0}],"lvl":2,"i":7,"seen":0,"slug":"improving-vercels-default-caching"},{"type":"heading_close","hLevel":2,"level":0},{"type":"blockquote_open","lines":[78,79],"level":0},{"type":"paragraph_open","tight":false,"lines":[78,79],"level":1},{"type":"inline","content":"The fastest request is the request not made","level":2,"lines":[78,79],"children":[{"type":"text","content":"The fastest request is the request not made","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"paragraph_open","tight":false,"lines":[80,81],"level":0},{"type":"inline","content":"We noticed that Vercel was sending a `Cache-Control` header of `public, max-age=0, must-revalidate` , preventing some of our SVG, CSS and font files from being cached in the browser.","level":1,"lines":[80,81],"children":[{"type":"text","content":"We noticed that Vercel was sending a ","level":0},{"type":"code","content":"Cache-Control","block":false,"level":0},{"type":"text","content":" header of ","level":0},{"type":"code","content":"public, max-age=0, must-revalidate","block":false,"level":0},{"type":"text","content":" , preventing some of our SVG, CSS and font files from being cached in the browser.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[82,83],"level":0},{"type":"inline","content":"We updated our `next.config.js` , adding a long `max-age` to the caching header that Vercel sends. Our assets are versioned properly, so we were able to safely do this.","level":1,"lines":[82,83],"children":[{"type":"text","content":"We updated our ","level":0},{"type":"code","content":"next.config.js","block":false,"level":0},{"type":"text","content":" , adding a long ","level":0},{"type":"code","content":"max-age","block":false,"level":0},{"type":"text","content":" to the caching header that Vercel sends. Our assets are versioned properly, so we were able to safely do this.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[84,85],"level":0},{"type":"inline","content":"[Enabling Next.js Automatic Static Optimization](#enabling-nextjs-automatic-static-optimization)","level":1,"lines":[84,85],"children":[{"type":"text","content":"Enabling Next.js Automatic Static Optimization","level":0}],"lvl":2,"i":8,"seen":0,"slug":"enabling-nextjs-automatic-static-optimization"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[86,87],"level":0},{"type":"inline","content":"Next.js is able to automatically pre-render a page to HTML, whenever a page meets some pre-conditions. This mode is called [Automatic Static Optimization](https://nextjs.org/docs/advanced-features/automatic-static-optimization). Pre-rendered pages can be cached on a CDN for extremely fast page loads. We removed calls to `getServerSideProps` and `getInitialProps` to take advantage of this mode.","level":1,"lines":[86,87],"children":[{"type":"text","content":"Next.js is able to automatically pre-render a page to HTML, whenever a page meets some pre-conditions. This mode is called ","level":0},{"type":"link_open","href":"https://nextjs.org/docs/advanced-features/automatic-static-optimization","title":"","level":0},{"type":"text","content":"Automatic Static Optimization","level":1},{"type":"link_close","level":0},{"type":"text","content":". Pre-rendered pages can be cached on a CDN for extremely fast page loads. We removed calls to ","level":0},{"type":"code","content":"getServerSideProps","block":false,"level":0},{"type":"text","content":" and ","level":0},{"type":"code","content":"getInitialProps","block":false,"level":0},{"type":"text","content":" to take advantage of this mode.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[88,89],"level":0},{"type":"inline","content":"[Developing a performance culture](#developing-a-performance-culture)","level":1,"lines":[88,89],"children":[{"type":"text","content":"Developing a performance culture","level":0}],"lvl":2,"i":9,"seen":0,"slug":"developing-a-performance-culture"},{"type":"heading_close","hLevel":2,"level":0},{"type":"blockquote_open","lines":[90,91],"level":0},{"type":"paragraph_open","tight":false,"lines":[90,91],"level":1},{"type":"inline","content":"Always in sight, always in mind","level":2,"lines":[90,91],"children":[{"type":"text","content":"Always in sight, always in mind","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"paragraph_open","tight":false,"lines":[92,93],"level":0},{"type":"inline","content":"Our performance optimization journey will never be complete. It requires constant vigilance to maintain a baseline across our users. To instill this within our team, we took a few actions.","level":1,"lines":[92,93],"children":[{"type":"text","content":"Our performance optimization journey will never be complete. It requires constant vigilance to maintain a baseline across our users. To instill this within our team, we took a few actions.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[94,95],"level":0},{"type":"inline","content":"We developed a Slack bot which sends our Sentry performance dashboard every week, containing our slowest transactions and our Core Web Vitals summary. This shows which pages need improvement and where our [users are the most miserable](https://docs.sentry.io/product/performance/metrics/#user-misery).","level":1,"lines":[94,95],"children":[{"type":"text","content":"We developed a Slack bot which sends our Sentry performance dashboard every week, containing our slowest transactions and our Core Web Vitals summary. This shows which pages need improvement and where our ","level":0},{"type":"link_open","href":"https://docs.sentry.io/product/performance/metrics/#user-misery","title":"","level":0},{"type":"text","content":"users are the most miserable","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[96,97],"level":0},{"type":"inline","content":"During our transition from Alpha to Beta, performance was one of the important features, along with stability and security. We considered performance implications while choosing libraries and tools. Having a \"seat at the table\" in these discussions ensures that performance is not considered as an after-thought.","level":1,"lines":[96,97],"children":[{"type":"text","content":"During our transition from Alpha to Beta, performance was one of the important features, along with stability and security. We considered performance implications while choosing libraries and tools. Having a \"seat at the table\" in these discussions ensures that performance is not considered as an after-thought.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[98,99],"level":0},{"type":"inline","content":"[Results](#results)","level":1,"lines":[98,99],"children":[{"type":"text","content":"Results","level":0}],"lvl":2,"i":10,"seen":0,"slug":"results"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[100,101],"level":0},{"type":"inline","content":"With these changes, we have a respectable Core Web Vitals score. This is a snapshot from Sentry with RUM data from the last week. We are within the recommended threshold for all the 3 Web Vitals.","level":1,"lines":[100,101],"children":[{"type":"text","content":"With these changes, we have a respectable Core Web Vitals score. This is a snapshot from Sentry with RUM data from the last week. We are within the recommended threshold for all the 3 Web Vitals.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[102,103],"level":0},{"type":"inline","content":"![Sentry results](/images/blog/blog/sentry-results.png)","level":1,"lines":[102,103],"children":[{"type":"image","src":"/images/blog/blog/sentry-results.png","title":"","alt":"Sentry results","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[104,105],"level":0},{"type":"inline","content":"Our Next.js build output also shows that users download \u003c 200 kb of JavaScript between any two page transitions. We're still improving too - even though we provide a lot of functionality in our dashboard, we will continue to reduce our bundle sizes.","level":1,"lines":[104,105],"children":[{"type":"text","content":"Our Next.js build output also shows that users download \u003c 200 kb of JavaScript between any two page transitions. We're still improving too - even though we provide a lot of functionality in our dashboard, we will continue to reduce our bundle sizes.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[106,107],"level":0},{"type":"inline","content":"[Things that did not work](#things-that-did-not-work)","level":1,"lines":[106,107],"children":[{"type":"text","content":"Things that did not work","level":0}],"lvl":2,"i":11,"seen":0,"slug":"things-that-did-not-work"},{"type":"heading_close","hLevel":2,"level":0},{"type":"blockquote_open","lines":[108,109],"level":0},{"type":"paragraph_open","tight":false,"lines":[108,109],"level":1},{"type":"inline","content":"You win some, you lose some","level":2,"lines":[108,109],"children":[{"type":"text","content":"You win some, you lose some","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0},{"type":"paragraph_open","tight":false,"lines":[110,111],"level":0},{"type":"inline","content":"We tried a VSCode plugin called [Import cost](https://marketplace.visualstudio.com/items?itemName=wix.vscode-import-cost) which shows the size of JavaScript modules when you import it in your editor. However, the plugin did not work on our codebase since it doesn't support some JavaScript features, like optional chaining.","level":1,"lines":[110,111],"children":[{"type":"text","content":"We tried a VSCode plugin called ","level":0},{"type":"link_open","href":"https://marketplace.visualstudio.com/items?itemName=wix.vscode-import-cost","title":"","level":0},{"type":"text","content":"Import cost","level":1},{"type":"link_close","level":0},{"type":"text","content":" which shows the size of JavaScript modules when you import it in your editor. However, the plugin did not work on our codebase since it doesn't support some JavaScript features, like optional chaining.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[112,113],"level":0},{"type":"inline","content":"We also passed on using [lodash-webpack-plugin](https://www.npmjs.com/package/lodash-webpack-plugin) even though it had the potential to reduce our JavaScript sizes, because it could potentially break our code if not used carefully. This plugin would require our frontend team to understand the Webpack configuration, updating it whenever they use a new lodash feature set.","level":1,"lines":[112,113],"children":[{"type":"text","content":"We also passed on using ","level":0},{"type":"link_open","href":"https://www.npmjs.com/package/lodash-webpack-plugin","title":"","level":0},{"type":"text","content":"lodash-webpack-plugin","level":1},{"type":"link_close","level":0},{"type":"text","content":" even though it had the potential to reduce our JavaScript sizes, because it could potentially break our code if not used carefully. This plugin would require our frontend team to understand the Webpack configuration, updating it whenever they use a new lodash feature set.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[114,115],"level":0},{"type":"inline","content":"[The road ahead](#the-road-ahead)","level":1,"lines":[114,115],"children":[{"type":"text","content":"The road ahead","level":0}],"lvl":2,"i":12,"seen":0,"slug":"the-road-ahead"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[116,117],"level":0},{"type":"inline","content":"Our broad goal is to implement best practices for frontend performance, and make it exciting to all of our team. These are some ideas we have on our roadmap -","level":1,"lines":[116,117],"children":[{"type":"text","content":"Our broad goal is to implement best practices for frontend performance, and make it exciting to all of our team. These are some ideas we have on our roadmap -","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[118,122],"level":0},{"type":"list_item_open","lines":[118,119],"level":1},{"type":"paragraph_open","tight":true,"lines":[118,119],"level":2},{"type":"inline","content":"Set up [Lighthouse](https://developers.google.com/web/tools/lighthouse) in a GitHub Action to catch performance regression earlier in the development life cycle.","level":3,"lines":[118,119],"children":[{"type":"text","content":"Set up ","level":0},{"type":"link_open","href":"https://developers.google.com/web/tools/lighthouse","title":"","level":0},{"type":"text","content":"Lighthouse","level":1},{"type":"link_close","level":0},{"type":"text","content":" in a GitHub Action to catch performance regression earlier in the development life cycle.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[119,120],"level":1},{"type":"paragraph_open","tight":true,"lines":[119,120],"level":2},{"type":"inline","content":"Continue reducing our initial JavaScript payload size, to improve our LCP time","level":3,"lines":[119,120],"children":[{"type":"text","content":"Continue reducing our initial JavaScript payload size, to improve our LCP time","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[120,122],"level":1},{"type":"paragraph_open","tight":true,"lines":[120,121],"level":2},{"type":"inline","content":"Explore `cloud-mode` in [Segment](https://segment.com/docs/connections/destinations/) which makes API calls from the server instead of loading third-party library on the browser.","level":3,"lines":[120,121],"children":[{"type":"text","content":"Explore ","level":0},{"type":"code","content":"cloud-mode","block":false,"level":0},{"type":"text","content":" in ","level":0},{"type":"link_open","href":"https://segment.com/docs/connections/destinations/","title":"","level":0},{"type":"text","content":"Segment","level":1},{"type":"link_close","level":0},{"type":"text","content":" which makes API calls from the server instead of loading third-party library on the browser.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[122,123],"level":0},{"type":"inline","content":"Reach out to us on [Twitter](https://twitter.com/supabase) if you have more ideas to speed up our website ⚡","level":1,"lines":[122,123],"children":[{"type":"text","content":"Reach out to us on ","level":0},{"type":"link_open","href":"https://twitter.com/supabase","title":"","level":0},{"type":"text","content":"Twitter","level":1},{"type":"link_close","level":0},{"type":"text","content":" if you have more ideas to speed up our website ⚡","level":0}]},{"type":"paragraph_close","tight":false,"level":0}],"content":"- [Establishing a baseline and setting targets](#establishing-a-baseline-and-setting-targets)\n- [Improving our JavaScript bundle size](#improving-our-javascript-bundle-size)\n- [Improving Vercel's default caching](#improving-vercels-default-caching)\n- [Enabling Next.js Automatic Static Optimization](#enabling-nextjs-automatic-static-optimization)\n- [Developing a performance culture](#developing-a-performance-culture)\n- [Results](#results)\n- [Things that did not work](#things-that-did-not-work)\n- [The road ahead](#the-road-ahead)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supabase-dashboard-performance"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>