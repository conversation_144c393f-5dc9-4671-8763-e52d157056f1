<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../rss.xml" data-next-head=""/><link rel="manifest" href="../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supavisor: Scaling Postgres to 1 Million Connections</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Supavisor is a scalable, cloud-native Postgres connection pooler. We connected a million clients to it to see how it performs." data-next-head=""/><meta property="og:title" content="Supavisor: Scaling Postgres to 1 Million Connections" data-next-head=""/><meta property="og:description" content="Supavisor is a scalable, cloud-native Postgres connection pooler. We connected a million clients to it to see how it performs." data-next-head=""/><meta property="og:url" content="https://supabase.com/blog/supavisor-1-million" data-next-head=""/><meta property="og:type" content="article" data-next-head=""/><meta property="article:published_time" content="2023-08-11" data-next-head=""/><meta property="article:author" content="https://github.com/egor-romanov" data-next-head=""/><meta property="article:author" content="https://github.com/chasers" data-next-head=""/><meta property="article:author" content="https://github.com/abc3" data-next-head=""/><meta property="article:tag" content="launch-week" data-next-head=""/><meta property="article:tag" content="supavisor" data-next-head=""/><meta property="article:tag" content="postgres" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/blog/launch-week-8/day-5/supavisor-og.jpg" data-next-head=""/><meta property="og:image:alt" content="Supavisor: Scaling Postgres to 1 Million Connections thumbnail" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8544-7a6c5c8f7951e22e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/9660-834135bd441aa061.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/7675-fc7f7e1f41645bf5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/236-173d2434a4fd252f.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8499-ba333c3fc48b68a9.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/blog/%5Bslug%5D-b3f09ca1c1858229.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav><div class="h-[2px] w-full flex justify-start relative"><div class="h-full top-0 bottom-0 right-0 absolute w-screen bg-brand will-change-transform transition-opacity" style="display:absolute;transform:translate3d(-100%,0,0);opacity:1"></div></div></div><main class="relative min-h-screen overflow-x-hidden"><div class=" container mx-auto px-4 py-4 md:py-8 xl:py-10 sm:px-16 xl:px-20 "><div class="grid grid-cols-12 gap-4"><div class="hidden col-span-12 xl:block lg:col-span-2"><a class="text-foreground-lighter hover:text-foreground flex cursor-pointer items-center text-sm transition" href="../blog.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left" style="padding:0"><path d="m15 18-6-6 6-6"></path></svg>Back</a></div><div class="col-span-12 lg:col-span-12 xl:col-span-10"><div class="mb-6 lg:mb-10 max-w-5xl space-y-8"><div class="space-y-4"><a class="text-brand hidden lg:inline" href="../blog.html">Blog</a><h1 class="text-2xl sm:text-4xl">Supavisor: Scaling Postgres to 1 Million Connections</h1><div class="text-light flex space-x-3 text-sm"><p>11 Aug 2023</p><p>•</p><p>14 minute read</p></div><div class="hidden lg:flex justify-between"><div class="flex-1 flex flex-col gap-3 pt-2 md:flex-row md:gap-0 lg:gap-3"><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/egor-romanov"><div class="flex items-center gap-3"><div class="w-10"><img alt="Egor Romanov avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fegor-romanov.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fegor-romanov.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fegor-romanov.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Egor Romanov</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/chasers"><div class="flex items-center gap-3"><div class="w-10"><img alt="Chase Granberry avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fchasers.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fchasers.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fchasers.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Chase Granberry</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div><div class="mr-4 w-max"><a target="_blank" class="cursor-pointer" href="https://github.com/abc3"><div class="flex items-center gap-3"><div class="w-10"><img alt="Stanislav Muzhyk avatar" loading="lazy" width="40" height="40" decoding="async" data-nimg="1" class="border-default rounded-full border w-full aspect-square object-cover" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fabc3.png&amp;w=48&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fabc3.png&amp;w=96&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fgithub.com%2Fabc3.png&amp;w=96&amp;q=75"/></div><div class="flex flex-col"><span class="text-foreground mb-0 text-sm">Stanislav Muzhyk</span><span class="text-foreground-lighter mb-0 text-xs">Engineering</span></div></div></a></div></div></div></div></div><div class="grid grid-cols-12 lg:gap-16 xl:gap-8"><div class="col-span-12 lg:col-span-7 xl:col-span-7"><article><div class="prose prose-docs"><div class="hidden md:block relative mb-8 w-full aspect-[3/2] overflow-auto rounded-lg border"><img alt="Supavisor: Scaling Postgres to 1 Million Connections" loading="lazy" decoding="async" data-nimg="fill" class="object-cover m-0" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-5%2Fsupavisor-thumb.jpg&amp;w=256&amp;q=100 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-5%2Fsupavisor-thumb.jpg&amp;w=384&amp;q=100 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-5%2Fsupavisor-thumb.jpg&amp;w=640&amp;q=100 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-5%2Fsupavisor-thumb.jpg&amp;w=750&amp;q=100 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-5%2Fsupavisor-thumb.jpg&amp;w=828&amp;q=100 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-5%2Fsupavisor-thumb.jpg&amp;w=1080&amp;q=100 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-5%2Fsupavisor-thumb.jpg&amp;w=1200&amp;q=100 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-5%2Fsupavisor-thumb.jpg&amp;w=1920&amp;q=100 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-5%2Fsupavisor-thumb.jpg&amp;w=2048&amp;q=100 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-5%2Fsupavisor-thumb.jpg&amp;w=3840&amp;q=100 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Flaunch-week-8%2Fday-5%2Fsupavisor-thumb.jpg&amp;w=3840&amp;q=100"/></div><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p>One of the most <a href="https://news.ycombinator.com/item?id=24735012">widely-discussed shortcomings</a> of Postgres is it&#x27;s connection system. Every Postgres connection has a reasonably high memory footprint, and determining the maximum number of connections your database can handle is a <a href="https://momjian.us/main/blogs/pgblog/2020.html#April_22_2020">bit of an art</a>.</p>
<p>A common solution is <a href="https://supabase.com/docs/guides/database/connecting-to-postgres#how-connection-pooling-works">connection pooling</a>. Supabase currently offers <a href="http://www.pgbouncer.org/">pgbouncer</a> which is single-threaded, making it difficult to scale. We&#x27;ve seen some <a href="https://twitter.com/viggy28/status/1677674197664038912?s=12&amp;t=_WCn3v_QJ7tkQLvOvkZkqg">novel ways</a> to scale pgbouncer, but we have a <a href="https://github.com/supabase/supavisor#motivation">few other goals</a> in mind for our platform.</p>
<p>And so we&#x27;ve built <a href="https://github.com/supabase/supavisor">Supavisor</a>, a Postgres connection pooler that can handle millions of connections.</p>
<h2 id="what-is-supavisor" class="group scroll-mt-24">What is Supavisor?<a href="#what-is-supavisor" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Supavisor is a scalable, cloud-native Postgres connection pooler. It has been developed with multi-tenancy in mind, handling millions of connections without significant overhead or latency. Supavisor is built in Elixir, in partnership with <a href="https://twitter.com/josevalim">José Valim</a> (the creator of Elixir) and the <a href="https://dashbit.co/">Dashbit</a> team.</p>
<!-- -->
<p>Supavisor will enable us to build some exciting new features for your Postgres cluster:</p>
<ul>
<li>query caching</li>
<li>automatic read-replica load balancing</li>
<li>query blocking</li>
<li>and much more</li>
</ul>
<h2 id="benchmarking-1-million-connections" class="group scroll-mt-24">Benchmarking 1 million connections<a href="#benchmarking-1-million-connections" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>We&#x27;ve benchmarked the characteristics Supavisor exhibits under load before rolling it out to our entire Postgres fleet. We tested how we can scale the cluster vertically and horizontally. These results have given us confidence that Supavisor is ready.</p>
<h3 id="setup" class="group scroll-mt-24">Setup<a href="#setup" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We use a <a href="https://github.com/supabase/benchmarks">custom load-testing application</a> to test the features of the Supabase platform. It consists of:</p>
<ol>
<li>Terraform scripts for creating a testing environment on AWS.</li>
<li>k6 as the load generator. We used the k6 guides for <a href="https://k6.io/docs/testing-guides/running-large-tests/">running large-scale tests</a> and <a href="https://k6.io/docs/misc/fine-tuning-os/">fine-tuning OS</a> to tweak the config for AWS instances.</li>
<li>Grafana + Prometheus for monitoring.</li>
</ol>
<p>To simulate 1,000,000 concurrent active connections, we used 20 AWS EC2 instances with 16 cores and 32GB of RAM. We ran the tests for up to 2 hours to ensure that the Supavisor can handle load over long periods.</p>
<h3 id="establishing-a-baseline" class="group scroll-mt-24">Establishing a baseline<a href="#establishing-a-baseline" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>In the first test, we set up a single ARM 16-core Supavisor instance on Ubuntu 22.04.2 aarch64 connected to one database instance.</p>
<!-- -->
<p>We wanted to assess the capacity of a single Supavisor instance. We achieved:</p>
<ul>
<li>250,000 concurrent connections to Supavisor</li>
<li>Supavisor was running with a pool of 400 direct connections to the database</li>
<li>The system was processing 20,000 queries per second (QPS)</li>
</ul>
<!-- -->
<p>With this setup the database is the bottleneck - 20,000 QPS was the maximum this instance could handle. Increasing QPS would have been possible with a larger instance or read-replicas, but we wanted to focus on the scalability of Supavisor&#x27;s connection limit (not Postgres&#x27;s). Since Supavisor is built with multi-tenancy, addition of read-replicas is as easy as sending a single post request.</p>
<div></div>
<h3 id="supavisors-scaling-capabilities" class="group scroll-mt-24">Supavisor&#x27;s scaling capabilities<a href="#supavisors-scaling-capabilities" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>In the next step, we focused on Supavisor&#x27;s vertical scaling capabilities by connecting <strong>500,000 concurrent users</strong> with a 64-core Supavisor instance while the single database instance configuration remained the same.</p>
<!-- -->
<p>The system showed no signs of instability or performance degradation. QPS remained constant at 20,000, proving that an increased number of connections doesn&#x27;t negatively affect Supavisor&#x27;s overall performance (this is generally expected from a <a href="https://stressgrid.com/blog/webserver_benchmark/">BEAM-based language</a> like Elixir).</p>
<div></div>
<p>We also monitored how the load was distributed over Supavisor instance&#x27;s cores:</p>
<div></div>
<p>The load is spread evenly between all cores, which is great. CPU usage is high, signaling that the current setup has reached its capacity: a single Supavisor instance with 64 core handles around 500,000 connections. With this reference number, we moved on to horizontal scaling tests.</p>
<h3 id="scaling-to-1000000-connections" class="group scroll-mt-24">Scaling to 1,000,000 connections<a href="#scaling-to-1000000-connections" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>To examine horizontal scalability, we deployed two 64-core Supavisor instances, with the first instance connected directly to the database and the other relaying queries through the first.</p>
<!-- -->
<p>In the Supavisor architecture, only a single node holds direct connections to each database instance. When you add more Postgres databases or read-replicas, Supavisor spreads the connections to the replicas. Every Supavisor instance can accept incoming connections and either execute queries themselves (if they directly connected) or relay to another node (if not).</p>
<p>This setup successfully handled:</p>
<ul>
<li><strong>1,003,200 simultaneous connections</strong> to the Supavisor instances.</li>
<li><strong>20,000 QPS</strong> or <strong>1.2 million queries per minute.</strong> Each connection executed a <code class="short-inline-codeblock">select</code> query once every 50 seconds.</li>
</ul>
<div></div>
<p>Within the cluster:</p>
<ul>
<li>The directly connected instance was under almost the same load as when handling 500,000 concurrent clients in a single-node mode.</li>
<li>The relaying instance was extremely over-resourced. Most cores had little-to-no workload because relayed connections are more lightweight.</li>
</ul>
<div></div>
<p>In a multi-tenant setup (or when using Read-Replicas), the load is much more evenly spread because all Supavisor instances connect to comparable numbers of databases and have both direct and relayed connections evenly distributed between each other.</p>
<!-- -->
<h3 id="supavisors-impact-on-query-duration" class="group scroll-mt-24">Supavisor&#x27;s impact on query duration<a href="#supavisors-impact-on-query-duration" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>To measure the impact on query duration, we started with 5,000 queries per second. This allows us to exclude side effects from the database side (long query execution times).</p>
<p>The query used in the experiment was the following:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>select *</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>from (</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    values</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    (1, &#x27;one&#x27;),</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    (2, &#x27;two&#x27;),</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    (3, &#x27;three&#x27;)</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>) as t (num, letter);</span></div></div><br/></code></div></div>
<p>We found with Supavisor median query duration was less than 2ms. And this includes not only time from client to Supavisor but the whole roundtrip: from Client to Supavisor ➡️ from Supavisor to Postgres ➡️ then query execution time on Postgres ➡️ and back to Supavisor ➡️ and to the Client.</p>
<table><thead><tr><th></th><th>Query Duration</th></tr></thead><tbody><tr><td>Median</td><td>2ms</td></tr><tr><td>p95</td><td>3ms</td></tr><tr><td>p99</td><td>23ms</td></tr></tbody></table>
<div></div>
<p>We can see that 95% of queries were completed in less than 3 milliseconds. A slightly higher query duration at the beginning of the test can be explained by the dynamic nature of Supavisor-to-Database connection pool. It is being scaled up to the hard limit when more clients establish connections to Supavisor itself and scaled back down when users leave.</p>
<p>We continued to scale up to 20,000QPS to assess the impact on query duration and measured a median of 18.4ms:</p>
<table><thead><tr><th></th><th>Query Duration</th></tr></thead><tbody><tr><td>Median</td><td>18.4ms</td></tr><tr><td>p95</td><td>46.9ms</td></tr><tr><td>p99</td><td>68ms</td></tr></tbody></table>
<div></div>
<p>Database experiences much more load and more concurrent queries, which leads to higher execution times on the database side. And here are some metrics from the database side:</p>
<!-- -->
<p>The scalability can be further enhanced by adding more databases (or read-replicas if you want to scale a single app) to increase QPS or deploying additional Supavisor instances to accommodate tens of millions of concurrent connections.</p>
<h3 id="supavisor-on-supabase-platform" class="group scroll-mt-24">Supavisor on Supabase Platform<a href="#supavisor-on-supabase-platform" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>We compared our current PgBouncer setup with the new Supavisor setup to assess any impact on query duration.</p>
<p><em>Current architecture</em></p>
<p>Currently, every Supabase project comes with its own PgBouncer server running on the same instance as the Postgres database to ensure that the latency is as low as possible. But this setup comes with a trade-off: it uses the same compute resources as your database.</p>
<!-- -->
<p><em>Supavisor architecture</em></p>
<p>In the future, you connect to a distinct multi-tenant Supavisor cluster through a load-balancer. The Supavisor cluster maintains a connection pool to your database. In this case the pooler doesn&#x27;t consume additional CPU and RAM resources on the database server, but it does involve extra network latency.</p>
<!-- -->
<p>We ran 5,000 queries per second for each configuration, this time experimenting with <code class="short-inline-codeblock">insert</code> query. To make the experiment more realistic, we enabled the PostGIS extension to store coordinates:</p>
<div class="ch-codeblock not-prose "><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>insert into positions (</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>    stud_id,</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>    first_name,</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>    last_name,</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>    title,</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>    reports_to,</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>    timestamp,</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>    location,</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>    email</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>)</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>values (</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>    ${name},</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>    &#x27;Virtual ${name}&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>    &#x27;User ${name}&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>    &#x27;Load Tester&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>    1,</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>    ${Date.now()},</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>    st_point(-73.946${x}, 40.807${y}),</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>    &#x27;vu${name}@acme.corp&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->20</span><div style="display:inline-block;margin-left:16px"><span>);</span></div></div><br/></code></div></div>
<p>We observed an additional 2ms required each query to be executed on the Supavisor architecture compared to PgBouncer architecture.</p>
<table><thead><tr><th></th><th>Query Duration with Supavisor</th><th>Query Duration with PgBouncer</th></tr></thead><tbody><tr><td>Median</td><td>4ms</td><td>1ms</td></tr><tr><td>p95</td><td>4ms</td><td>2ms</td></tr><tr><td>p99</td><td>5ms</td><td>3ms</td></tr></tbody></table>
<div><figcaption>Fig.1 - Query Duration with Supavisor</figcaption></div>
<div><figcaption>Fig.2 - Query Duration with PgBouncer</figcaption></div>
<h3 id="getting-started" class="group scroll-mt-24">Getting started<a href="#getting-started" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3>
<p>Supavisor has been rolled out to all Supabase projects in all regions.</p>
<p>Contact <a href="https://supabase.com/dashboard/support/new">support</a> to start using it today, and we&#x27;ll provide connection details. We will be exposing a new connection string in project dashboards over the next few weeks.</p>
<p>You&#x27;ll be able to use both PgBouncer and Supavisor for a few months in parallel. Nothing will change with your PgBouncer setup if you need to switch back.</p>
<p>Supavisor will be added to the self-hosted stack as soon as we have tested it across our database fleet. That said - we&#x27;re confident that it&#x27;s ready for use if you want to try it with your own Postgres database. <a href="https://sequin.io/">Sequin</a>, one of our partners, has been using Supavisor for several months:</p>
<blockquote class="text-foreground"><p><p>With Supavisor, we&#x27;ve been able to ship incredible features that would have been very hard to
build otherwise. For example, our customers can now read from and write to Salesforce and
HubSpot via Postgres. We achieve this by intercepting queries that route through Supavisor.</p></p><p><p>We chose Supavisor because it&#x27;s scalable, multi-tenant, and written in Elixir. We were able to
integrate it easily with our own Elixir infrastructure. As partners, we look forward to helping
shape the future of Postgres connection pooling with Supabase.</p></p><div class="align-center m-0 flex h-8 items-center gap-3"><img alt="Anthony Accomazzo, Co-founder of Sequin. avatar" loading="lazy" width="32" height="32" decoding="async" data-nimg="1" class="h-8 w-8 rounded-full object-cover text-center m-0" style="color:transparent" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Favatars%2Fanthony-accomazzo.jpeg&amp;w=32&amp;q=75 1x, https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Favatars%2Fanthony-accomazzo.jpeg&amp;w=64&amp;q=75 2x" src="https://supabase.com/_next/image?url=%2Fimages%2Fblog%2Favatars%2Fanthony-accomazzo.jpeg&amp;w=64&amp;q=75"/><figcaption style="margin-top:0" class="text-foreground-lighter font-normal not-italic not-prose"><p>Anthony Accomazzo, Co-founder of Sequin.</p></figcaption></div></blockquote>
<h2 id="conclusion" class="group scroll-mt-24">Conclusion<a href="#conclusion" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2>
<p>Supavisor&#x27;s vertical and horizontal scaling ability make it the optimal solution for developers who aim to create applications that can effortlessly scale, even under extreme workloads, avoiding issues such as &quot;too many connections&quot; and enabling the full power of edge functions and serverless.</p>
<p>If you are interested in exploring Supavisor&#x27;s potential or want to implement its scalability in your upcoming project, check out <a href="https://github.com/supabase/supavisor">the GitHub repository</a> to know more.</p>
<div class="video-container"><iframe class="w-full" src="https://www.youtube-nocookie.com/embed/qzxzLSAJDfE" title="YouTube video player" frameBorder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share"></iframe></div></div></article><div class="block lg:hidden py-8"><div class="text-foreground-lighter text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupavisor-1-million&amp;text=Supavisor%3A%20Scaling%20Postgres%20to%201%20Million%20Connections"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupavisor-1-million&amp;text=Supavisor%3A%20Scaling%20Postgres%20to%201%20Million%20Connections"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupavisor-1-million&amp;t=Supavisor%3A%20Scaling%20Postgres%20to%201%20Million%20Connections"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div><div class="grid gap-8 py-8 lg:grid-cols-1"><div><a href="supabase-soc2-hipaa.html"><div><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Last post</p></div><div><h4 class="text-foreground text-lg">Supabase is now HIPAA and SOC2 Type 2 compliant</h4><p class="small">11 August 2023</p></div></div></div></div></a></div><div><a href="https://supabase.com/blog/supabase-integrations-marketplace"><div class="text-right"><div class="hover:bg-control cursor-pointer rounded border p-6 transition"><div class="space-y-4"><div><p class="text-foreground-lighter text-sm">Next post</p></div><div><h4 class="text-foreground text-lg">Supabase Integrations Marketplace</h4><p class="small">10 August 2023</p></div></div></div></div></a></div></div></div><div class="relative col-span-12 space-y-8 lg:col-span-5 xl:col-span-3 xl:col-start-9"><div class="space-y-6"><div class="hidden lg:block"><div class="space-y-8 py-8 lg:py-0"><div><div class="flex flex-wrap gap-2"><a href="https://supabase.com/blog/tags/launch-week"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">launch-week</div></a><a href="https://supabase.com/blog/tags/supavisor"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">supavisor</div></a><a href="https://supabase.com/blog/tags/postgres"><div class="inline-flex items-center rounded-full bg-opacity-10 bg-surface-200 text-foreground-light border border-strong px-2.5 py-0.5 text-xs">postgres</div></a></div></div><div><div><p class="text-foreground mb-4">On this page</p><div class="prose-toc"><ul>
<li><a href="#what-is-supavisor">What is Supavisor?</a></li>
<li><a href="#benchmarking-1-million-connections">Benchmarking 1 million connections</a>
<ul>
<li><a href="#setup">Setup</a></li>
<li><a href="#establishing-a-baseline">Establishing a baseline</a></li>
<li><a href="#supavisors-scaling-capabilities">Supavisor&#x27;s scaling capabilities</a></li>
<li><a href="#scaling-to-1000000-connections">Scaling to 1,000,000 connections</a></li>
<li><a href="#supavisors-impact-on-query-duration">Supavisor&#x27;s impact on query duration</a></li>
<li><a href="#supavisor-on-supabase-platform">Supavisor on Supabase Platform</a></li>
<li><a href="#getting-started">Getting started</a></li>
</ul>
</li>
<li><a href="#conclusion">Conclusion</a></li>
</ul></div></div></div></div></div><div class="hidden lg:block"><div class="text-foreground text-sm">Share this article</div><div class="mt-4 flex items-center gap-4"><a aria-label="Share on X" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://twitter.com/intent/tweet?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupavisor-1-million&amp;text=Supavisor%3A%20Scaling%20Postgres%20to%201%20Million%20Connections"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a aria-label="Share on Linkedin" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://www.linkedin.com/shareArticle?url=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupavisor-1-million&amp;text=Supavisor%3A%20Scaling%20Postgres%20to%201%20Million%20Connections"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M0 1.85859C0 1.31985 0.180185 0.87541 0.540541 0.525253C0.900896 0.175079 1.36937 0 1.94595 0C2.51223 0 2.97039 0.17238 3.32046 0.517172C3.68082 0.872727 3.861 1.33602 3.861 1.90707C3.861 2.42424 3.68598 2.85521 3.33591 3.2C2.97555 3.55556 2.50193 3.73333 1.91506 3.73333H1.89961C1.33333 3.73333 0.875166 3.55556 0.525097 3.2C0.175027 2.84444 0 2.3973 0 1.85859ZM0.200772 16V5.20404H3.62934V16H0.200772ZM5.52896 16H8.95753V9.97172C8.95753 9.5946 8.99872 9.30369 9.08108 9.09899C9.22522 8.73265 9.44402 8.42289 9.73745 8.1697C10.0309 7.91649 10.399 7.7899 10.8417 7.7899C11.9949 7.7899 12.5714 8.60336 12.5714 10.2303V16H16V9.8101C16 8.21548 15.6396 7.00606 14.9189 6.18182C14.1982 5.35758 13.2458 4.94545 12.0618 4.94545C10.7336 4.94545 9.69884 5.54343 8.95753 6.73939V6.77172H8.94208L8.95753 6.73939V5.20404H5.52896C5.54954 5.54882 5.55985 6.62086 5.55985 8.4202C5.55985 10.2195 5.54954 12.7461 5.52896 16Z"></path></svg></div></a><a aria-label="Share on Hacker News" target="_blank" class="text-foreground-lighter hover:text-foreground" href="https://news.ycombinator.com/submitlink?u=https%3A%2F%2Fsupabase.com%2Fblog%2Fsupavisor-1-million&amp;t=Supavisor%3A%20Scaling%20Postgres%20to%201%20Million%20Connections"><div class="relative" style="width:20px;height:20px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0ZM4.69024 3.8925L7.46363 9.0659V12.4258H8.53025V9.11913L11.3036 3.8925H10.1302L8.53025 7.14582C8.42355 7.30582 8.31694 7.46582 8.26363 7.62582C8.23286 7.74859 8.1845 7.83601 8.13883 7.91862C8.10515 7.97957 8.07286 8.0379 8.05024 8.10582C8.02355 8.07913 8.01017 8.03913 7.99686 7.99906C7.98355 7.95913 7.97024 7.91913 7.94355 7.89244C7.91686 7.83913 7.90348 7.79906 7.89017 7.75899C7.87686 7.71906 7.86355 7.67906 7.83694 7.62582V7.57251L7.78355 7.51913C7.76319 7.47841 7.75061 7.44546 7.73984 7.41731C7.72246 7.37171 7.70988 7.33877 7.67694 7.30582C7.62355 7.25251 7.57024 7.19913 7.57024 7.14582L5.97024 3.8925H4.69024Z"></path></svg></div></a></div></div></div></div></div></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="../dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="../contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--light.png 1x, ../_next/supabase-logo-wordmark--light.png 2x" src="../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../_next/supabase-logo-wordmark--dark.png 1x, ../_next/supabase-logo-wordmark--dark.png 2x" src="../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"prevPost":{"slug":"supabase-soc2-hipaa","title":"Supabase is now HIPAA and SOC2 Type 2 compliant","description":"This documents our journey from SOC2 Type 1 to SOC2 Type2 and HIPAA compliance. You can start building healthcare apps on Supabase today.","launchweek":"8","categories":["company"],"tags":["launch-week","security"],"date":"2023-08-11","toc_depth":3,"author":"inian","image":"launch-week-8/day-5/OG-day5-compliance.jpg","thumb":"launch-week-8/day-5/thumb-day5-compliance.jpg","formattedDate":"11 August 2023","readingTime":"5 minute read","url":"/blog/supabase-soc2-hipaa","path":"/blog/supabase-soc2-hipaa"},"nextPost":{"slug":"supabase-integrations-marketplace","title":"Supabase Integrations Marketplace","description":"Become a Supabase Integrations Partner: Publish OAuth Apps and Build with Supabase.","launchweek":"8","categories":["product"],"tags":["launch-week","integrations"],"date":"2023-08-10","toc_depth":3,"author":"thor_schaeff","image":"launch-week-8/day-4/integration-marketplace-og.jpg","thumb":"launch-week-8/day-4/integration-marketplace-thumb.jpg","formattedDate":"10 August 2023","readingTime":"5 minute read","url":"/blog/supabase-integrations-marketplace","path":"/blog/supabase-integrations-marketplace"},"relatedPosts":[{"slug":"launch-week-15-top-10","title":"Top 10 Launches of Launch Week 15","description":"Highlights from Launch Week 15","author":"wenbo","image":"launch-week-15/wrap-up/og.png","thumb":"launch-week-15/wrap-up/thumb.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week"],"date":"2025-07-18T15:00:00","toc_depth":3,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/launch-week-15-top-10","path":"/blog/launch-week-15-top-10"},{"slug":"lw15-hackathon","title":"Supabase Launch Week 15 Hackathon","description":"Build an Open Source Project over 10 days. 5 prize categories.","author":"tyler_shukert","image":"launch-week-15/hackathon/lw15-hackathon.png","thumb":"launch-week-15/hackathon/lw15-hackathon.png","launchweek":"15","categories":["launch-week"],"tags":["launch-week","hackathon"],"date":"2025-07-18:11:00","toc_depth":2,"formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/lw15-hackathon","path":"/blog/lw15-hackathon"},{"slug":"storage-500gb-uploads-cheaper-egress-pricing","title":"Storage: 10x Larger Uploads, 3x Cheaper Cached Egress, and 2x Egress Quota","description":"Upload files up to 500 GB with significant egress cost reductions.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-18:10:00","toc_depth":2,"author":"inian","image":"launch-week-15/day-5-storage-cheaper-egress/og.png","thumb":"launch-week-15/day-5-storage-cheaper-egress/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"4 minute read","url":"/blog/storage-500gb-uploads-cheaper-egress-pricing","path":"/blog/storage-500gb-uploads-cheaper-egress-pricing"},{"slug":"persistent-storage-for-faster-edge-functions","title":"Persistent Storage and 97% Faster Cold Starts for Edge Functions","description":"Mount S3-compatible buckets as persistent file storage in Edge Functions with up to 97% faster cold start times.","categories":["product","launch-week","edge-functions"],"tags":["launch-week","edge-functions","storage"],"date":"2025-07-18:00:00","toc_depth":3,"author":"laktek,nyannyacha","image":"launch-week-15/day-5-persistent-storage-for-functions/og.jpg","thumb":"launch-week-15/day-5-persistent-storage-for-functions/thumb.png","launchweek":"15","formattedDate":"18 July 2025","readingTime":"6 minute read","url":"/blog/persistent-storage-for-faster-edge-functions","path":"/blog/persistent-storage-for-faster-edge-functions"},{"slug":"algolia-connector-for-supabase","title":"Algolia Connector for Supabase","description":"Bring lightning-fast search to your Supabase apps, with no code required.","categories":["launch-week"],"tags":["launch-week","algolia"],"date":"2025-07-17:00:00","toc_depth":2,"author":"prashant","image":"launch-week-15/day-4-algolia-connector/og.png","thumb":"launch-week-15/day-4-algolia-connector/thumb.png","launchweek":"15","formattedDate":"17 July 2025","readingTime":"5 minute read","url":"/blog/algolia-connector-for-supabase","path":"/blog/algolia-connector-for-supabase"}],"blog":{"slug":"supavisor-1-million","source":"\nOne of the most [widely-discussed shortcomings](https://news.ycombinator.com/item?id=24735012) of Postgres is it's connection system. Every Postgres connection has a reasonably high memory footprint, and determining the maximum number of connections your database can handle is a [bit of an art](https://momjian.us/main/blogs/pgblog/2020.html#April_22_2020).\n\nA common solution is [connection pooling](https://supabase.com/docs/guides/database/connecting-to-postgres#how-connection-pooling-works). Supabase currently offers [pgbouncer](http://www.pgbouncer.org/) which is single-threaded, making it difficult to scale. We've seen some [novel ways](https://twitter.com/viggy28/status/1677674197664038912?s=12\u0026t=_WCn3v_QJ7tkQLvOvkZkqg) to scale pgbouncer, but we have a [few other goals](https://github.com/supabase/supavisor#motivation) in mind for our platform.\n\nAnd so we've built [Supavisor](https://github.com/supabase/supavisor), a Postgres connection pooler that can handle millions of connections.\n\n## What is Supavisor?\n\nSupavisor is a scalable, cloud-native Postgres connection pooler. It has been developed with multi-tenancy in mind, handling millions of connections without significant overhead or latency. Supavisor is built in Elixir, in partnership with [José Valim](https://twitter.com/josevalim) (the creator of Elixir) and the [Dashbit](https://dashbit.co/) team.\n\n\u003cImg\n  alt=\"diagram of supavisor architecture\"\n  src=\"/images/blog/2023-08-11-supavisor-1-million/pooler-diagram-github--dark.png\"\n/\u003e\n\nSupavisor will enable us to build some exciting new features for your Postgres cluster:\n\n- query caching\n- automatic read-replica load balancing\n- query blocking\n- and much more\n\n## Benchmarking 1 million connections\n\nWe've benchmarked the characteristics Supavisor exhibits under load before rolling it out to our entire Postgres fleet. We tested how we can scale the cluster vertically and horizontally. These results have given us confidence that Supavisor is ready.\n\n### Setup\n\nWe use a [custom load-testing application](https://github.com/supabase/benchmarks) to test the features of the Supabase platform. It consists of:\n\n1. Terraform scripts for creating a testing environment on AWS.\n2. k6 as the load generator. We used the k6 guides for [running large-scale tests](https://k6.io/docs/testing-guides/running-large-tests/) and [fine-tuning OS](https://k6.io/docs/misc/fine-tuning-os/) to tweak the config for AWS instances.\n3. Grafana + Prometheus for monitoring.\n\nTo simulate 1,000,000 concurrent active connections, we used 20 AWS EC2 instances with 16 cores and 32GB of RAM. We ran the tests for up to 2 hours to ensure that the Supavisor can handle load over long periods.\n\n### Establishing a baseline\n\nIn the first test, we set up a single ARM 16-core Supavisor instance on Ubuntu 22.04.2 aarch64 connected to one database instance.\n\n\u003cImg\n  alt=\"diagram supavisor 1 instance dark\"\n  src=\"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--baseline.png\"\n/\u003e\n\nWe wanted to assess the capacity of a single Supavisor instance. We achieved:\n\n- 250,000 concurrent connections to Supavisor\n- Supavisor was running with a pool of 400 direct connections to the database\n- The system was processing 20,000 queries per second (QPS)\n\n\u003cImg\n  alt=\"diagram supavisor 1 instance dark\"\n  src=\"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--250k-test.png\"\n/\u003e\n\nWith this setup the database is the bottleneck - 20,000 QPS was the maximum this instance could handle. Increasing QPS would have been possible with a larger instance or read-replicas, but we wanted to focus on the scalability of Supavisor's connection limit (not Postgres's). Since Supavisor is built with multi-tenancy, addition of read-replicas is as easy as sending a single post request.\n\n\u003cdiv\u003e\n  \u003cImg\n    alt=\"chart connections baseline light\"\n    src={{\n      light: '/images/blog/2023-08-11-supavisor-1-million/chart-baseline-light.png',\n      dark: '/images/blog/2023-08-11-supavisor-1-million/chart-baseline-dark.png',\n    }}\n  /\u003e\n\u003c/div\u003e\n\n### Supavisor's scaling capabilities\n\nIn the next step, we focused on Supavisor's vertical scaling capabilities by connecting **500,000 concurrent users** with a 64-core Supavisor instance while the single database instance configuration remained the same.\n\n\u003cImg\n  alt=\"diagram supavisor 1 instance dark\"\n  src=\"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--500k-test.png\"\n/\u003e\n\nThe system showed no signs of instability or performance degradation. QPS remained constant at 20,000, proving that an increased number of connections doesn't negatively affect Supavisor's overall performance (this is generally expected from a [BEAM-based language](https://stressgrid.com/blog/webserver_benchmark/) like Elixir).\n\n\u003cdiv\u003e\n  \u003cImg\n    alt=\"chart connections vertical scaling light\"\n    src={{\n      light: '/images/blog/2023-08-11-supavisor-1-million/chart-vertical-light.png',\n      dark: '/images/blog/2023-08-11-supavisor-1-million/chart-vertical-dark.png',\n    }}\n  /\u003e\n\u003c/div\u003e\n\nWe also monitored how the load was distributed over Supavisor instance's cores:\n\n\u003cdiv\u003e\n  \u003cImg\n    alt=\"cpu load vertical scaling light\"\n    src={{\n      light: '/images/blog/2023-08-11-supavisor-1-million/cpu-vertical-light.png',\n      dark: '/images/blog/2023-08-11-supavisor-1-million/cpu-vertical-dark.png',\n    }}\n  /\u003e\n\u003c/div\u003e\n\nThe load is spread evenly between all cores, which is great. CPU usage is high, signaling that the current setup has reached its capacity: a single Supavisor instance with 64 core handles around 500,000 connections. With this reference number, we moved on to horizontal scaling tests.\n\n### Scaling to 1,000,000 connections\n\nTo examine horizontal scalability, we deployed two 64-core Supavisor instances, with the first instance connected directly to the database and the other relaying queries through the first.\n\n\u003cImg\n  alt=\"diagram supavisor 1 instance dark\"\n  src=\"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--scaling-to-1million.png\"\n/\u003e\n\nIn the Supavisor architecture, only a single node holds direct connections to each database instance. When you add more Postgres databases or read-replicas, Supavisor spreads the connections to the replicas. Every Supavisor instance can accept incoming connections and either execute queries themselves (if they directly connected) or relay to another node (if not).\n\nThis setup successfully handled:\n\n- **1,003,200 simultaneous connections** to the Supavisor instances.\n- **20,000 QPS** or **1.2 million queries per minute.** Each connection executed a `select` query once every 50 seconds.\n\n\u003cdiv\u003e\n  \u003cImg\n    alt=\"chart connections 1 million light\"\n    src={{\n      light: '/images/blog/2023-08-11-supavisor-1-million/chart-1m-light.png',\n      dark: '/images/blog/2023-08-11-supavisor-1-million/chart-1m-dark.png',\n    }}\n  /\u003e\n\u003c/div\u003e\n\nWithin the cluster:\n\n- The directly connected instance was under almost the same load as when handling 500,000 concurrent clients in a single-node mode.\n- The relaying instance was extremely over-resourced. Most cores had little-to-no workload because relayed connections are more lightweight.\n\n\u003cdiv\u003e\n  \u003cImg\n    alt=\"cpu load 1 million light\"\n    src={{\n      light: '/images/blog/2023-08-11-supavisor-1-million/cpu-1m-light.png',\n      dark: '/images/blog/2023-08-11-supavisor-1-million/cpu-1m-dark.png',\n    }}\n  /\u003e\n\u003c/div\u003e\n\nIn a multi-tenant setup (or when using Read-Replicas), the load is much more evenly spread because all Supavisor instances connect to comparable numbers of databases and have both direct and relayed connections evenly distributed between each other.\n\n\u003cImg\n  alt=\"diagram supavisor 1 instance dark\"\n  src=\"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--multi-tenant-concept.png\"\n/\u003e\n\n### Supavisor's impact on query duration\n\nTo measure the impact on query duration, we started with 5,000 queries per second. This allows us to exclude side effects from the database side (long query execution times).\n\nThe query used in the experiment was the following:\n\n```sql\nselect *\nfrom (\n    values\n    (1, 'one'),\n    (2, 'two'),\n    (3, 'three')\n) as t (num, letter);\n```\n\nWe found with Supavisor median query duration was less than 2ms. And this includes not only time from client to Supavisor but the whole roundtrip: from Client to Supavisor ➡️ from Supavisor to Postgres ➡️ then query execution time on Postgres ➡️ and back to Supavisor ➡️ and to the Client.\n\n|        | Query Duration |\n| ------ | -------------- |\n| Median | 2ms            |\n| p95    | 3ms            |\n| p99    | 23ms           |\n\n\u003cdiv\u003e\n  \u003cImg\n    alt=\"chart query duration for 5k qps light\"\n    src={{\n      light: '/images/blog/2023-08-11-supavisor-1-million/chart-5k-light.png',\n      dark: '/images/blog/2023-08-11-supavisor-1-million/chart-5k-dark.png',\n    }}\n  /\u003e\n\u003c/div\u003e\n\nWe can see that 95% of queries were completed in less than 3 milliseconds. A slightly higher query duration at the beginning of the test can be explained by the dynamic nature of Supavisor-to-Database connection pool. It is being scaled up to the hard limit when more clients establish connections to Supavisor itself and scaled back down when users leave.\n\nWe continued to scale up to 20,000QPS to assess the impact on query duration and measured a median of 18.4ms:\n\n|        | Query Duration |\n| ------ | -------------- |\n| Median | 18.4ms         |\n| p95    | 46.9ms         |\n| p99    | 68ms           |\n\n\u003cdiv\u003e\n  \u003cImg\n    alt=\"chart query duration for 20k qps light\"\n    src={{\n      light: '/images/blog/2023-08-11-supavisor-1-million/chart-20k-light.png',\n      dark: '/images/blog/2023-08-11-supavisor-1-million/chart-20k-dark.png',\n    }}\n  /\u003e\n\u003c/div\u003e\n\nDatabase experiences much more load and more concurrent queries, which leads to higher execution times on the database side. And here are some metrics from the database side:\n\n\u003cImg\n  src=\"/images/blog/2023-08-11-supavisor-1-million/postgres-metrics.png\"\n  alt=\"Postgres metrics\"\n/\u003e\n\nThe scalability can be further enhanced by adding more databases (or read-replicas if you want to scale a single app) to increase QPS or deploying additional Supavisor instances to accommodate tens of millions of concurrent connections.\n\n### Supavisor on Supabase Platform\n\nWe compared our current PgBouncer setup with the new Supavisor setup to assess any impact on query duration.\n\n_Current architecture_\n\nCurrently, every Supabase project comes with its own PgBouncer server running on the same instance as the Postgres database to ensure that the latency is as low as possible. But this setup comes with a trade-off: it uses the same compute resources as your database.\n\n\u003cImg\n  alt=\"diagram supavisor 1 instance dark\"\n  src=\"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--pgbouncer.png\"\n/\u003e\n\n_Supavisor architecture_\n\nIn the future, you connect to a distinct multi-tenant Supavisor cluster through a load-balancer. The Supavisor cluster maintains a connection pool to your database. In this case the pooler doesn't consume additional CPU and RAM resources on the database server, but it does involve extra network latency.\n\n\u003cImg\n  alt=\"diagram supavisor 1 instance dark\"\n  src=\"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--supavisor-and-supabase.png\"\n/\u003e\n\nWe ran 5,000 queries per second for each configuration, this time experimenting with `insert` query. To make the experiment more realistic, we enabled the PostGIS extension to store coordinates:\n\n```sql\ninsert into positions (\n    stud_id,\n    first_name,\n    last_name,\n    title,\n    reports_to,\n    timestamp,\n    location,\n    email\n)\nvalues (\n    ${name},\n    'Virtual ${name}',\n    'User ${name}',\n    'Load Tester',\n    1,\n    ${Date.now()},\n    st_point(-73.946${x}, 40.807${y}),\n    'vu${name}@acme.corp'\n);\n```\n\nWe observed an additional 2ms required each query to be executed on the Supavisor architecture compared to PgBouncer architecture.\n\n|        | Query Duration with Supavisor | Query Duration with PgBouncer |\n| ------ | ----------------------------- | ----------------------------- |\n| Median | 4ms                           | 1ms                           |\n| p95    | 4ms                           | 2ms                           |\n| p99    | 5ms                           | 3ms                           |\n\n\u003cdiv\u003e\n  \u003cImg\n    style={{ marginBottom: '0em' }}\n    alt=\"chart query duration for 5k qps inserts with supavisor light\"\n    src={{\n      light: '/images/blog/2023-08-11-supavisor-1-million/chart-supavisor-duration-light.png',\n      dark: '/images/blog/2023-08-11-supavisor-1-million/chart-supavisor-duration-dark.png',\n    }}\n  /\u003e\n  \u003cfigcaption\u003eFig.1 - Query Duration with Supavisor\u003c/figcaption\u003e\n\u003c/div\u003e\n\n\u003cdiv\u003e\n  \u003cImg\n    style={{ marginBottom: '0em' }}\n    alt=\"chart query duration for 5k qps inserts with pgbouncer light\"\n    src={{\n      light: '/images/blog/2023-08-11-supavisor-1-million/chart-pgbouncer-duration-light.png',\n      dark: '/images/blog/2023-08-11-supavisor-1-million/chart-pgbouncer-duration-dark.png',\n    }}\n  /\u003e\n  \u003cfigcaption\u003eFig.2 - Query Duration with PgBouncer\u003c/figcaption\u003e\n\u003c/div\u003e\n\n### Getting started\n\nSupavisor has been rolled out to all Supabase projects in all regions.\n\nContact [support](https://supabase.com/dashboard/support/new) to start using it today, and we'll provide connection details. We will be exposing a new connection string in project dashboards over the next few weeks.\n\nYou'll be able to use both PgBouncer and Supavisor for a few months in parallel. Nothing will change with your PgBouncer setup if you need to switch back.\n\nSupavisor will be added to the self-hosted stack as soon as we have tested it across our database fleet. That said - we're confident that it's ready for use if you want to try it with your own Postgres database. [Sequin](https://sequin.io/), one of our partners, has been using Supavisor for several months:\n\n\u003cQuote img=\"anthony-accomazzo.jpeg\" caption=\"Anthony Accomazzo, Co-founder of Sequin.\"\u003e\n  \u003cp\u003e\n    With Supavisor, we've been able to ship incredible features that would have been very hard to\n    build otherwise. For example, our customers can now read from and write to Salesforce and\n    HubSpot via Postgres. We achieve this by intercepting queries that route through Supavisor.\n  \u003c/p\u003e\n  \u003cp\u003e\n    We chose Supavisor because it's scalable, multi-tenant, and written in Elixir. We were able to\n    integrate it easily with our own Elixir infrastructure. As partners, we look forward to helping\n    shape the future of Postgres connection pooling with Supabase.\n  \u003c/p\u003e\n\u003c/Quote\u003e\n\n## Conclusion\n\nSupavisor's vertical and horizontal scaling ability make it the optimal solution for developers who aim to create applications that can effortlessly scale, even under extreme workloads, avoiding issues such as \"too many connections\" and enabling the full power of edge functions and serverless.\n\nIf you are interested in exploring Supavisor's potential or want to implement its scalability in your upcoming project, check out [the GitHub repository](https://github.com/supabase/supavisor) to know more.\n\n\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/qzxzLSAJDfE\"\n    title=\"YouTube video player\"\n    frameBorder=\"0\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"\n    allowfullscreen\n  \u003e\u003c/iframe\u003e\n\u003c/div\u003e\n","title":"Supavisor: Scaling Postgres to 1 Million Connections","description":"Supavisor is a scalable, cloud-native Postgres connection pooler. We connected a million clients to it to see how it performs.","launchweek":"8","categories":["product"],"tags":["launch-week","supavisor","postgres"],"date":"2023-08-11","toc_depth":3,"author":"egor_romanov,chasers,stas","image":"launch-week-8/day-5/supavisor-og.jpg","thumb":"launch-week-8/day-5/supavisor-thumb.jpg","content":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    a: \"a\",\n    h2: \"h2\",\n    ul: \"ul\",\n    li: \"li\",\n    h3: \"h3\",\n    ol: \"ol\",\n    strong: \"strong\",\n    code: \"code\",\n    table: \"table\",\n    thead: \"thead\",\n    tr: \"tr\",\n    th: \"th\",\n    tbody: \"tbody\",\n    td: \"td\",\n    em: \"em\"\n  }, _provideComponents(), props.components), {Img, Quote, CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  if (!Img) _missingMdxReference(\"Img\", true);\n  if (!Quote) _missingMdxReference(\"Quote\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"One of the most \", _jsx(_components.a, {\n        href: \"https://news.ycombinator.com/item?id=24735012\",\n        children: \"widely-discussed shortcomings\"\n      }), \" of Postgres is it's connection system. Every Postgres connection has a reasonably high memory footprint, and determining the maximum number of connections your database can handle is a \", _jsx(_components.a, {\n        href: \"https://momjian.us/main/blogs/pgblog/2020.html#April_22_2020\",\n        children: \"bit of an art\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"A common solution is \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/database/connecting-to-postgres#how-connection-pooling-works\",\n        children: \"connection pooling\"\n      }), \". Supabase currently offers \", _jsx(_components.a, {\n        href: \"http://www.pgbouncer.org/\",\n        children: \"pgbouncer\"\n      }), \" which is single-threaded, making it difficult to scale. We've seen some \", _jsx(_components.a, {\n        href: \"https://twitter.com/viggy28/status/1677674197664038912?s=12\u0026t=_WCn3v_QJ7tkQLvOvkZkqg\",\n        children: \"novel ways\"\n      }), \" to scale pgbouncer, but we have a \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/supavisor#motivation\",\n        children: \"few other goals\"\n      }), \" in mind for our platform.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"And so we've built \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/supavisor\",\n        children: \"Supavisor\"\n      }), \", a Postgres connection pooler that can handle millions of connections.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"what-is-supavisor\",\n      children: \"What is Supavisor?\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Supavisor is a scalable, cloud-native Postgres connection pooler. It has been developed with multi-tenancy in mind, handling millions of connections without significant overhead or latency. Supavisor is built in Elixir, in partnership with \", _jsx(_components.a, {\n        href: \"https://twitter.com/josevalim\",\n        children: \"José Valim\"\n      }), \" (the creator of Elixir) and the \", _jsx(_components.a, {\n        href: \"https://dashbit.co/\",\n        children: \"Dashbit\"\n      }), \" team.\"]\n    }), \"\\n\", _jsx(Img, {\n      alt: \"diagram of supavisor architecture\",\n      src: \"/images/blog/2023-08-11-supavisor-1-million/pooler-diagram-github--dark.png\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Supavisor will enable us to build some exciting new features for your Postgres cluster:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"query caching\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"automatic read-replica load balancing\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"query blocking\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"and much more\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"benchmarking-1-million-connections\",\n      children: \"Benchmarking 1 million connections\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We've benchmarked the characteristics Supavisor exhibits under load before rolling it out to our entire Postgres fleet. We tested how we can scale the cluster vertically and horizontally. These results have given us confidence that Supavisor is ready.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"setup\",\n      children: \"Setup\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We use a \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/benchmarks\",\n        children: \"custom load-testing application\"\n      }), \" to test the features of the Supabase platform. It consists of:\"]\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Terraform scripts for creating a testing environment on AWS.\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"k6 as the load generator. We used the k6 guides for \", _jsx(_components.a, {\n          href: \"https://k6.io/docs/testing-guides/running-large-tests/\",\n          children: \"running large-scale tests\"\n        }), \" and \", _jsx(_components.a, {\n          href: \"https://k6.io/docs/misc/fine-tuning-os/\",\n          children: \"fine-tuning OS\"\n        }), \" to tweak the config for AWS instances.\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Grafana + Prometheus for monitoring.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To simulate 1,000,000 concurrent active connections, we used 20 AWS EC2 instances with 16 cores and 32GB of RAM. We ran the tests for up to 2 hours to ensure that the Supavisor can handle load over long periods.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"establishing-a-baseline\",\n      children: \"Establishing a baseline\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In the first test, we set up a single ARM 16-core Supavisor instance on Ubuntu 22.04.2 aarch64 connected to one database instance.\"\n    }), \"\\n\", _jsx(Img, {\n      alt: \"diagram supavisor 1 instance dark\",\n      src: \"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--baseline.png\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We wanted to assess the capacity of a single Supavisor instance. We achieved:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"250,000 concurrent connections to Supavisor\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Supavisor was running with a pool of 400 direct connections to the database\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"The system was processing 20,000 queries per second (QPS)\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(Img, {\n      alt: \"diagram supavisor 1 instance dark\",\n      src: \"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--250k-test.png\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"With this setup the database is the bottleneck - 20,000 QPS was the maximum this instance could handle. Increasing QPS would have been possible with a larger instance or read-replicas, but we wanted to focus on the scalability of Supavisor's connection limit (not Postgres's). Since Supavisor is built with multi-tenancy, addition of read-replicas is as easy as sending a single post request.\"\n    }), \"\\n\", _jsx(\"div\", {\n      children: _jsx(Img, {\n        alt: \"chart connections baseline light\",\n        src: {\n          light: '/images/blog/2023-08-11-supavisor-1-million/chart-baseline-light.png',\n          dark: '/images/blog/2023-08-11-supavisor-1-million/chart-baseline-dark.png'\n        }\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"supavisors-scaling-capabilities\",\n      children: \"Supavisor's scaling capabilities\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In the next step, we focused on Supavisor's vertical scaling capabilities by connecting \", _jsx(_components.strong, {\n        children: \"500,000 concurrent users\"\n      }), \" with a 64-core Supavisor instance while the single database instance configuration remained the same.\"]\n    }), \"\\n\", _jsx(Img, {\n      alt: \"diagram supavisor 1 instance dark\",\n      src: \"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--500k-test.png\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The system showed no signs of instability or performance degradation. QPS remained constant at 20,000, proving that an increased number of connections doesn't negatively affect Supavisor's overall performance (this is generally expected from a \", _jsx(_components.a, {\n        href: \"https://stressgrid.com/blog/webserver_benchmark/\",\n        children: \"BEAM-based language\"\n      }), \" like Elixir).\"]\n    }), \"\\n\", _jsx(\"div\", {\n      children: _jsx(Img, {\n        alt: \"chart connections vertical scaling light\",\n        src: {\n          light: '/images/blog/2023-08-11-supavisor-1-million/chart-vertical-light.png',\n          dark: '/images/blog/2023-08-11-supavisor-1-million/chart-vertical-dark.png'\n        }\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We also monitored how the load was distributed over Supavisor instance's cores:\"\n    }), \"\\n\", _jsx(\"div\", {\n      children: _jsx(Img, {\n        alt: \"cpu load vertical scaling light\",\n        src: {\n          light: '/images/blog/2023-08-11-supavisor-1-million/cpu-vertical-light.png',\n          dark: '/images/blog/2023-08-11-supavisor-1-million/cpu-vertical-dark.png'\n        }\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The load is spread evenly between all cores, which is great. CPU usage is high, signaling that the current setup has reached its capacity: a single Supavisor instance with 64 core handles around 500,000 connections. With this reference number, we moved on to horizontal scaling tests.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"scaling-to-1000000-connections\",\n      children: \"Scaling to 1,000,000 connections\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To examine horizontal scalability, we deployed two 64-core Supavisor instances, with the first instance connected directly to the database and the other relaying queries through the first.\"\n    }), \"\\n\", _jsx(Img, {\n      alt: \"diagram supavisor 1 instance dark\",\n      src: \"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--scaling-to-1million.png\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In the Supavisor architecture, only a single node holds direct connections to each database instance. When you add more Postgres databases or read-replicas, Supavisor spreads the connections to the replicas. Every Supavisor instance can accept incoming connections and either execute queries themselves (if they directly connected) or relay to another node (if not).\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This setup successfully handled:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"1,003,200 simultaneous connections\"\n        }), \" to the Supavisor instances.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"20,000 QPS\"\n        }), \" or \", _jsx(_components.strong, {\n          children: \"1.2 million queries per minute.\"\n        }), \" Each connection executed a \", _jsx(_components.code, {\n          children: \"select\"\n        }), \" query once every 50 seconds.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(\"div\", {\n      children: _jsx(Img, {\n        alt: \"chart connections 1 million light\",\n        src: {\n          light: '/images/blog/2023-08-11-supavisor-1-million/chart-1m-light.png',\n          dark: '/images/blog/2023-08-11-supavisor-1-million/chart-1m-dark.png'\n        }\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Within the cluster:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"The directly connected instance was under almost the same load as when handling 500,000 concurrent clients in a single-node mode.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"The relaying instance was extremely over-resourced. Most cores had little-to-no workload because relayed connections are more lightweight.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(\"div\", {\n      children: _jsx(Img, {\n        alt: \"cpu load 1 million light\",\n        src: {\n          light: '/images/blog/2023-08-11-supavisor-1-million/cpu-1m-light.png',\n          dark: '/images/blog/2023-08-11-supavisor-1-million/cpu-1m-dark.png'\n        }\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In a multi-tenant setup (or when using Read-Replicas), the load is much more evenly spread because all Supavisor instances connect to comparable numbers of databases and have both direct and relayed connections evenly distributed between each other.\"\n    }), \"\\n\", _jsx(Img, {\n      alt: \"diagram supavisor 1 instance dark\",\n      src: \"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--multi-tenant-concept.png\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"supavisors-impact-on-query-duration\",\n      children: \"Supavisor's impact on query duration\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To measure the impact on query duration, we started with 5,000 queries per second. This allows us to exclude side effects from the database side (long query execution times).\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The query used in the experiment was the following:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"select *\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"from\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    values\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"1\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'one'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"),\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"2\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'two'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"),\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"3\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'three'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"as\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" t (num, letter);\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We found with Supavisor median query duration was less than 2ms. And this includes not only time from client to Supavisor but the whole roundtrip: from Client to Supavisor ➡️ from Supavisor to Postgres ➡️ then query execution time on Postgres ➡️ and back to Supavisor ➡️ and to the Client.\"\n    }), \"\\n\", _jsxs(_components.table, {\n      children: [_jsx(_components.thead, {\n        children: _jsxs(_components.tr, {\n          children: [_jsx(_components.th, {}), _jsx(_components.th, {\n            children: \"Query Duration\"\n          })]\n        })\n      }), _jsxs(_components.tbody, {\n        children: [_jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"Median\"\n          }), _jsx(_components.td, {\n            children: \"2ms\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"p95\"\n          }), _jsx(_components.td, {\n            children: \"3ms\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"p99\"\n          }), _jsx(_components.td, {\n            children: \"23ms\"\n          })]\n        })]\n      })]\n    }), \"\\n\", _jsx(\"div\", {\n      children: _jsx(Img, {\n        alt: \"chart query duration for 5k qps light\",\n        src: {\n          light: '/images/blog/2023-08-11-supavisor-1-million/chart-5k-light.png',\n          dark: '/images/blog/2023-08-11-supavisor-1-million/chart-5k-dark.png'\n        }\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We can see that 95% of queries were completed in less than 3 milliseconds. A slightly higher query duration at the beginning of the test can be explained by the dynamic nature of Supavisor-to-Database connection pool. It is being scaled up to the hard limit when more clients establish connections to Supavisor itself and scaled back down when users leave.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We continued to scale up to 20,000QPS to assess the impact on query duration and measured a median of 18.4ms:\"\n    }), \"\\n\", _jsxs(_components.table, {\n      children: [_jsx(_components.thead, {\n        children: _jsxs(_components.tr, {\n          children: [_jsx(_components.th, {}), _jsx(_components.th, {\n            children: \"Query Duration\"\n          })]\n        })\n      }), _jsxs(_components.tbody, {\n        children: [_jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"Median\"\n          }), _jsx(_components.td, {\n            children: \"18.4ms\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"p95\"\n          }), _jsx(_components.td, {\n            children: \"46.9ms\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"p99\"\n          }), _jsx(_components.td, {\n            children: \"68ms\"\n          })]\n        })]\n      })]\n    }), \"\\n\", _jsx(\"div\", {\n      children: _jsx(Img, {\n        alt: \"chart query duration for 20k qps light\",\n        src: {\n          light: '/images/blog/2023-08-11-supavisor-1-million/chart-20k-light.png',\n          dark: '/images/blog/2023-08-11-supavisor-1-million/chart-20k-dark.png'\n        }\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Database experiences much more load and more concurrent queries, which leads to higher execution times on the database side. And here are some metrics from the database side:\"\n    }), \"\\n\", _jsx(Img, {\n      src: \"/images/blog/2023-08-11-supavisor-1-million/postgres-metrics.png\",\n      alt: \"Postgres metrics\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The scalability can be further enhanced by adding more databases (or read-replicas if you want to scale a single app) to increase QPS or deploying additional Supavisor instances to accommodate tens of millions of concurrent connections.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"supavisor-on-supabase-platform\",\n      children: \"Supavisor on Supabase Platform\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We compared our current PgBouncer setup with the new Supavisor setup to assess any impact on query duration.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.em, {\n        children: \"Current architecture\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Currently, every Supabase project comes with its own PgBouncer server running on the same instance as the Postgres database to ensure that the latency is as low as possible. But this setup comes with a trade-off: it uses the same compute resources as your database.\"\n    }), \"\\n\", _jsx(Img, {\n      alt: \"diagram supavisor 1 instance dark\",\n      src: \"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--pgbouncer.png\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.em, {\n        children: \"Supavisor architecture\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In the future, you connect to a distinct multi-tenant Supavisor cluster through a load-balancer. The Supavisor cluster maintains a connection pool to your database. In this case the pooler doesn't consume additional CPU and RAM resources on the database server, but it does involve extra network latency.\"\n    }), \"\\n\", _jsx(Img, {\n      alt: \"diagram supavisor 1 instance dark\",\n      src: \"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--supavisor-and-supabase.png\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We ran 5,000 queries per second for each configuration, this time experimenting with \", _jsx(_components.code, {\n        children: \"insert\"\n      }), \" query. To make the experiment more realistic, we enabled the PostGIS extension to store coordinates:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"insert into\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" positions (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    stud_id,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    first_name,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    last_name,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    title,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    reports_to,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    timestamp\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    location\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    email\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"values\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    ${\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"name\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"},\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    'Virtual ${name}'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    'User ${name}'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    'Load Tester'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    1\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    ${\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Date\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"now\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"()},\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    st_point(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"-\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"73\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"946\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"${x}, \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"40\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"807\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"${y}),\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    'vu${name}@acme.corp'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \");\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We observed an additional 2ms required each query to be executed on the Supavisor architecture compared to PgBouncer architecture.\"\n    }), \"\\n\", _jsxs(_components.table, {\n      children: [_jsx(_components.thead, {\n        children: _jsxs(_components.tr, {\n          children: [_jsx(_components.th, {}), _jsx(_components.th, {\n            children: \"Query Duration with Supavisor\"\n          }), _jsx(_components.th, {\n            children: \"Query Duration with PgBouncer\"\n          })]\n        })\n      }), _jsxs(_components.tbody, {\n        children: [_jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"Median\"\n          }), _jsx(_components.td, {\n            children: \"4ms\"\n          }), _jsx(_components.td, {\n            children: \"1ms\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"p95\"\n          }), _jsx(_components.td, {\n            children: \"4ms\"\n          }), _jsx(_components.td, {\n            children: \"2ms\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"p99\"\n          }), _jsx(_components.td, {\n            children: \"5ms\"\n          }), _jsx(_components.td, {\n            children: \"3ms\"\n          })]\n        })]\n      })]\n    }), \"\\n\", _jsxs(\"div\", {\n      children: [_jsx(Img, {\n        style: {\n          marginBottom: '0em'\n        },\n        alt: \"chart query duration for 5k qps inserts with supavisor light\",\n        src: {\n          light: '/images/blog/2023-08-11-supavisor-1-million/chart-supavisor-duration-light.png',\n          dark: '/images/blog/2023-08-11-supavisor-1-million/chart-supavisor-duration-dark.png'\n        }\n      }), _jsx(\"figcaption\", {\n        children: \"Fig.1 - Query Duration with Supavisor\"\n      })]\n    }), \"\\n\", _jsxs(\"div\", {\n      children: [_jsx(Img, {\n        style: {\n          marginBottom: '0em'\n        },\n        alt: \"chart query duration for 5k qps inserts with pgbouncer light\",\n        src: {\n          light: '/images/blog/2023-08-11-supavisor-1-million/chart-pgbouncer-duration-light.png',\n          dark: '/images/blog/2023-08-11-supavisor-1-million/chart-pgbouncer-duration-dark.png'\n        }\n      }), _jsx(\"figcaption\", {\n        children: \"Fig.2 - Query Duration with PgBouncer\"\n      })]\n    }), \"\\n\", _jsx(_components.h3, {\n      id: \"getting-started\",\n      children: \"Getting started\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Supavisor has been rolled out to all Supabase projects in all regions.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Contact \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard/support/new\",\n        children: \"support\"\n      }), \" to start using it today, and we'll provide connection details. We will be exposing a new connection string in project dashboards over the next few weeks.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You'll be able to use both PgBouncer and Supavisor for a few months in parallel. Nothing will change with your PgBouncer setup if you need to switch back.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Supavisor will be added to the self-hosted stack as soon as we have tested it across our database fleet. That said - we're confident that it's ready for use if you want to try it with your own Postgres database. \", _jsx(_components.a, {\n        href: \"https://sequin.io/\",\n        children: \"Sequin\"\n      }), \", one of our partners, has been using Supavisor for several months:\"]\n    }), \"\\n\", _jsxs(Quote, {\n      img: \"anthony-accomazzo.jpeg\",\n      caption: \"Anthony Accomazzo, Co-founder of Sequin.\",\n      children: [_jsx(\"p\", {\n        children: _jsx(_components.p, {\n          children: \"With Supavisor, we've been able to ship incredible features that would have been very hard to\\nbuild otherwise. For example, our customers can now read from and write to Salesforce and\\nHubSpot via Postgres. We achieve this by intercepting queries that route through Supavisor.\"\n        })\n      }), _jsx(\"p\", {\n        children: _jsx(_components.p, {\n          children: \"We chose Supavisor because it's scalable, multi-tenant, and written in Elixir. We were able to\\nintegrate it easily with our own Elixir infrastructure. As partners, we look forward to helping\\nshape the future of Postgres connection pooling with Supabase.\"\n        })\n      })]\n    }), \"\\n\", _jsx(_components.h2, {\n      id: \"conclusion\",\n      children: \"Conclusion\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Supavisor's vertical and horizontal scaling ability make it the optimal solution for developers who aim to create applications that can effortlessly scale, even under extreme workloads, avoiding issues such as \\\"too many connections\\\" and enabling the full power of edge functions and serverless.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you are interested in exploring Supavisor's potential or want to implement its scalability in your upcoming project, check out \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/supavisor\",\n        children: \"the GitHub repository\"\n      }), \" to know more.\"]\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"video-container\",\n      children: _jsx(\"iframe\", {\n        className: \"w-full\",\n        src: \"https://www.youtube-nocookie.com/embed/qzxzLSAJDfE\",\n        title: \"YouTube video player\",\n        frameBorder: \"0\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\",\n        allowfullscreen: true\n      })\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{"chCodeConfig":{"theme":{"name":"supabase","type":"from-css","tokenColors":[{"scope":["comment","punctuation.definition.comment","string.comment"],"settings":{"foreground":"var(--ch-1)"}},{"scope":["constant","entity.name.constant","variable.other.constant","variable.other.enummember","variable.language","entity"],"settings":{"foreground":"var(--ch-2)"}},{"scope":["entity.name","meta.export.default","meta.definition.variable"],"settings":{"foreground":"var(--ch-3)"}},{"scope":["variable.parameter.function","meta.jsx.children","meta.block","meta.tag.attributes","entity.name.constant","meta.object.member","meta.embedded.expression"],"settings":{"foreground":"var(--ch-4)"}},{"scope":"entity.name.function","settings":{"foreground":"var(--ch-5)"}},{"scope":["entity.name.tag","support.class.component"],"settings":{"foreground":"var(--ch-6)"}},{"scope":"keyword","settings":{"foreground":"var(--ch-7)"}},{"scope":["storage","storage.type"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["storage.modifier.package","storage.modifier.import","storage.type.java"],"settings":{"foreground":"var(--ch-4)"}},{"scope":["string","string punctuation.section.embedded source"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"support","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.property-name","settings":{"foreground":"var(--ch-2)"}},{"scope":"variable","settings":{"foreground":"var(--ch-3)"}},{"scope":"variable.other","settings":{"foreground":"var(--ch-4)"}},{"scope":"invalid.broken","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.deprecated","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.illegal","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"invalid.unimplemented","settings":{"foreground":"var(--ch-9)","fontStyle":"italic"}},{"scope":"carriage-return","settings":{"background":"var(--ch-7)","foreground":"var(--ch-10)","fontStyle":"italic underline"}},{"scope":"message.error","settings":{"foreground":"var(--ch-9)"}},{"scope":"string variable","settings":{"foreground":"var(--ch-2)"}},{"scope":["source.regexp","string.regexp"],"settings":{"foreground":"var(--ch-8)"}},{"scope":["string.regexp.character-class","string.regexp constant.character.escape","string.regexp source.ruby.embedded","string.regexp string.regexp.arbitrary-repitition"],"settings":{"foreground":"var(--ch-8)"}},{"scope":"string.regexp constant.character.escape","settings":{"foreground":"var(--ch-6)","fontStyle":"bold"}},{"scope":"support.constant","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.variable","settings":{"foreground":"var(--ch-2)"}},{"scope":"support.type.property-name.json","settings":{"foreground":"var(--ch-6)"}},{"scope":"meta.module-reference","settings":{"foreground":"var(--ch-2)"}},{"scope":"punctuation.definition.list.begin.markdown","settings":{"foreground":"var(--ch-3)"}},{"scope":["markup.heading","markup.heading entity.name"],"settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"markup.quote","settings":{"foreground":"var(--ch-6)"}},{"scope":"markup.italic","settings":{"foreground":"var(--ch-4)","fontStyle":"italic"}},{"scope":"markup.bold","settings":{"foreground":"var(--ch-4)","fontStyle":"bold"}},{"scope":["markup.underline"],"settings":{"fontStyle":"underline"}},{"scope":["markup.strikethrough"],"settings":{"fontStyle":"strikethrough"}},{"scope":"markup.inline.raw","settings":{"foreground":"var(--ch-2)"}},{"scope":["markup.deleted","meta.diff.header.from-file","punctuation.definition.deleted"],"settings":{"background":"var(--ch-11)","foreground":"var(--ch-9)"}},{"scope":["punctuation.section.embedded"],"settings":{"foreground":"var(--ch-7)"}},{"scope":["markup.inserted","meta.diff.header.to-file","punctuation.definition.inserted"],"settings":{"background":"var(--ch-12)","foreground":"var(--ch-6)"}},{"scope":["markup.changed","punctuation.definition.changed"],"settings":{"background":"var(--ch-13)","foreground":"var(--ch-3)"}},{"scope":["markup.ignored","markup.untracked"],"settings":{"background":"var(--ch-2)","foreground":"var(--ch-14)"}},{"scope":"meta.diff.range","settings":{"foreground":"var(--ch-5)","fontStyle":"bold"}},{"scope":"meta.diff.header","settings":{"foreground":"var(--ch-2)"}},{"scope":"meta.separator","settings":{"foreground":"var(--ch-2)","fontStyle":"bold"}},{"scope":"meta.output","settings":{"foreground":"var(--ch-2)"}},{"scope":["brackethighlighter.tag","brackethighlighter.curly","brackethighlighter.round","brackethighlighter.square","brackethighlighter.angle","brackethighlighter.quote"],"settings":{"foreground":"var(--ch-15)"}},{"scope":"brackethighlighter.unmatched","settings":{"foreground":"var(--ch-9)"}},{"scope":["constant.other.reference.link","string.other.link"],"settings":{"foreground":"var(--ch-8)","fontStyle":"underline"}}],"colors":{"editor.background":"var(--ch-16)","editor.foreground":"var(--ch-4)","editor.selectionBackground":"var(--ch-17)","editor.infoForeground":"var(--ch-18)","editor.rangeHighlightBackground":"var(--ch-19)","editorLineNumber.foreground":"var(--ch-20)","tab.activeBackground":"var(--ch-16)","tab.inactiveBackground":"var(--ch-21)","tab.activeForeground":"var(--ch-4)","tab.inactiveForeground":"var(--ch-15)","tab.border":"var(--ch-22)","tab.activeBorder":"var(--ch-16)","tab.activeBorderTop":"var(--ch-23)","tab.hoverBackground":"var(--ch-16)","tab.hoverForeground":"var(--ch-15)","editorGroupHeader.tabsBorder":"var(--ch-22)","editorGroupHeader.tabsBackground":"var(--ch-21)","list.inactiveSelectionBackground":"var(--ch-24)","list.inactiveSelectionForeground":"var(--ch-4)","list.hoverBackground":"var(--ch-25)","list.hoverForeground":"var(--ch-4)"}},"lineNumbers":true,"showCopyButton":true,"skipLanguages":[],"autoImport":false}}},"toc":{"json":[{"content":"What is Supavisor?","slug":"what-is-supavisor","lvl":2,"i":0,"seen":0},{"content":"Benchmarking 1 million connections","slug":"benchmarking-1-million-connections","lvl":2,"i":1,"seen":0},{"content":"Setup","slug":"setup","lvl":3,"i":2,"seen":0},{"content":"Establishing a baseline","slug":"establishing-a-baseline","lvl":3,"i":3,"seen":0},{"content":"Supavisor's scaling capabilities","slug":"supavisors-scaling-capabilities","lvl":3,"i":4,"seen":0},{"content":"Scaling to 1,000,000 connections","slug":"scaling-to-1000000-connections","lvl":3,"i":5,"seen":0},{"content":"Supavisor's impact on query duration","slug":"supavisors-impact-on-query-duration","lvl":3,"i":6,"seen":0},{"content":"Supavisor on Supabase Platform","slug":"supavisor-on-supabase-platform","lvl":3,"i":7,"seen":0},{"content":"Getting started","slug":"getting-started","lvl":3,"i":8,"seen":0},{"content":"Conclusion","slug":"conclusion","lvl":2,"i":9,"seen":0}],"highest":2,"tokens":[{"type":"paragraph_open","tight":false,"lines":[1,2],"level":0},{"type":"inline","content":"One of the most [widely-discussed shortcomings](https://news.ycombinator.com/item?id=24735012) of Postgres is it's connection system. Every Postgres connection has a reasonably high memory footprint, and determining the maximum number of connections your database can handle is a [bit of an art](https://momjian.us/main/blogs/pgblog/2020.html#April_22_2020).","level":1,"lines":[1,2],"children":[{"type":"text","content":"One of the most ","level":0},{"type":"link_open","href":"https://news.ycombinator.com/item?id=24735012","title":"","level":0},{"type":"text","content":"widely-discussed shortcomings","level":1},{"type":"link_close","level":0},{"type":"text","content":" of Postgres is it's connection system. Every Postgres connection has a reasonably high memory footprint, and determining the maximum number of connections your database can handle is a ","level":0},{"type":"link_open","href":"https://momjian.us/main/blogs/pgblog/2020.html#April_22_2020","title":"","level":0},{"type":"text","content":"bit of an art","level":1},{"type":"link_close","level":0},{"type":"text","content":".","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[3,4],"level":0},{"type":"inline","content":"A common solution is [connection pooling](https://supabase.com/docs/guides/database/connecting-to-postgres#how-connection-pooling-works). Supabase currently offers [pgbouncer](http://www.pgbouncer.org/) which is single-threaded, making it difficult to scale. We've seen some [novel ways](https://twitter.com/viggy28/status/1677674197664038912?s=12\u0026t=_WCn3v_QJ7tkQLvOvkZkqg) to scale pgbouncer, but we have a [few other goals](https://github.com/supabase/supavisor#motivation) in mind for our platform.","level":1,"lines":[3,4],"children":[{"type":"text","content":"A common solution is ","level":0},{"type":"link_open","href":"https://supabase.com/docs/guides/database/connecting-to-postgres#how-connection-pooling-works","title":"","level":0},{"type":"text","content":"connection pooling","level":1},{"type":"link_close","level":0},{"type":"text","content":". Supabase currently offers ","level":0},{"type":"link_open","href":"http://www.pgbouncer.org/","title":"","level":0},{"type":"text","content":"pgbouncer","level":1},{"type":"link_close","level":0},{"type":"text","content":" which is single-threaded, making it difficult to scale. We've seen some ","level":0},{"type":"link_open","href":"https://twitter.com/viggy28/status/1677674197664038912?s=12\u0026t=_WCn3v_QJ7tkQLvOvkZkqg","title":"","level":0},{"type":"text","content":"novel ways","level":1},{"type":"link_close","level":0},{"type":"text","content":" to scale pgbouncer, but we have a ","level":0},{"type":"link_open","href":"https://github.com/supabase/supavisor#motivation","title":"","level":0},{"type":"text","content":"few other goals","level":1},{"type":"link_close","level":0},{"type":"text","content":" in mind for our platform.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[5,6],"level":0},{"type":"inline","content":"And so we've built [Supavisor](https://github.com/supabase/supavisor), a Postgres connection pooler that can handle millions of connections.","level":1,"lines":[5,6],"children":[{"type":"text","content":"And so we've built ","level":0},{"type":"link_open","href":"https://github.com/supabase/supavisor","title":"","level":0},{"type":"text","content":"Supavisor","level":1},{"type":"link_close","level":0},{"type":"text","content":", a Postgres connection pooler that can handle millions of connections.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[7,8],"level":0},{"type":"inline","content":"[What is Supavisor?](#what-is-supavisor)","level":1,"lines":[7,8],"children":[{"type":"text","content":"What is Supavisor?","level":0}],"lvl":2,"i":0,"seen":0,"slug":"what-is-supavisor"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[9,10],"level":0},{"type":"inline","content":"Supavisor is a scalable, cloud-native Postgres connection pooler. It has been developed with multi-tenancy in mind, handling millions of connections without significant overhead or latency. Supavisor is built in Elixir, in partnership with [José Valim](https://twitter.com/josevalim) (the creator of Elixir) and the [Dashbit](https://dashbit.co/) team.","level":1,"lines":[9,10],"children":[{"type":"text","content":"Supavisor is a scalable, cloud-native Postgres connection pooler. It has been developed with multi-tenancy in mind, handling millions of connections without significant overhead or latency. Supavisor is built in Elixir, in partnership with ","level":0},{"type":"link_open","href":"https://twitter.com/josevalim","title":"","level":0},{"type":"text","content":"José Valim","level":1},{"type":"link_close","level":0},{"type":"text","content":" (the creator of Elixir) and the ","level":0},{"type":"link_open","href":"https://dashbit.co/","title":"","level":0},{"type":"text","content":"Dashbit","level":1},{"type":"link_close","level":0},{"type":"text","content":" team.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[11,15],"level":0},{"type":"inline","content":"\u003cImg\n  alt=\"diagram of supavisor architecture\"\n  src=\"/images/blog/2023-08-11-supavisor-1-million/pooler-diagram-github--dark.png\"\n/\u003e","level":1,"lines":[11,15],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"diagram of supavisor architecture\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"/images/blog/2023-08-11-supavisor-1-million/pooler-diagram-github--dark.png\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[16,17],"level":0},{"type":"inline","content":"Supavisor will enable us to build some exciting new features for your Postgres cluster:","level":1,"lines":[16,17],"children":[{"type":"text","content":"Supavisor will enable us to build some exciting new features for your Postgres cluster:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[18,23],"level":0},{"type":"list_item_open","lines":[18,19],"level":1},{"type":"paragraph_open","tight":true,"lines":[18,19],"level":2},{"type":"inline","content":"query caching","level":3,"lines":[18,19],"children":[{"type":"text","content":"query caching","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[19,20],"level":1},{"type":"paragraph_open","tight":true,"lines":[19,20],"level":2},{"type":"inline","content":"automatic read-replica load balancing","level":3,"lines":[19,20],"children":[{"type":"text","content":"automatic read-replica load balancing","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[20,21],"level":1},{"type":"paragraph_open","tight":true,"lines":[20,21],"level":2},{"type":"inline","content":"query blocking","level":3,"lines":[20,21],"children":[{"type":"text","content":"query blocking","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[21,23],"level":1},{"type":"paragraph_open","tight":true,"lines":[21,22],"level":2},{"type":"inline","content":"and much more","level":3,"lines":[21,22],"children":[{"type":"text","content":"and much more","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"heading_open","hLevel":2,"lines":[23,24],"level":0},{"type":"inline","content":"[Benchmarking 1 million connections](#benchmarking-1-million-connections)","level":1,"lines":[23,24],"children":[{"type":"text","content":"Benchmarking 1 million connections","level":0}],"lvl":2,"i":1,"seen":0,"slug":"benchmarking-1-million-connections"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[25,26],"level":0},{"type":"inline","content":"We've benchmarked the characteristics Supavisor exhibits under load before rolling it out to our entire Postgres fleet. We tested how we can scale the cluster vertically and horizontally. These results have given us confidence that Supavisor is ready.","level":1,"lines":[25,26],"children":[{"type":"text","content":"We've benchmarked the characteristics Supavisor exhibits under load before rolling it out to our entire Postgres fleet. We tested how we can scale the cluster vertically and horizontally. These results have given us confidence that Supavisor is ready.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[27,28],"level":0},{"type":"inline","content":"[Setup](#setup)","level":1,"lines":[27,28],"children":[{"type":"text","content":"Setup","level":0}],"lvl":3,"i":2,"seen":0,"slug":"setup"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[29,30],"level":0},{"type":"inline","content":"We use a [custom load-testing application](https://github.com/supabase/benchmarks) to test the features of the Supabase platform. It consists of:","level":1,"lines":[29,30],"children":[{"type":"text","content":"We use a ","level":0},{"type":"link_open","href":"https://github.com/supabase/benchmarks","title":"","level":0},{"type":"text","content":"custom load-testing application","level":1},{"type":"link_close","level":0},{"type":"text","content":" to test the features of the Supabase platform. It consists of:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"ordered_list_open","order":1,"lines":[31,35],"level":0},{"type":"list_item_open","lines":[31,32],"level":1},{"type":"paragraph_open","tight":true,"lines":[31,32],"level":2},{"type":"inline","content":"Terraform scripts for creating a testing environment on AWS.","level":3,"lines":[31,32],"children":[{"type":"text","content":"Terraform scripts for creating a testing environment on AWS.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[32,33],"level":1},{"type":"paragraph_open","tight":true,"lines":[32,33],"level":2},{"type":"inline","content":"k6 as the load generator. We used the k6 guides for [running large-scale tests](https://k6.io/docs/testing-guides/running-large-tests/) and [fine-tuning OS](https://k6.io/docs/misc/fine-tuning-os/) to tweak the config for AWS instances.","level":3,"lines":[32,33],"children":[{"type":"text","content":"k6 as the load generator. We used the k6 guides for ","level":0},{"type":"link_open","href":"https://k6.io/docs/testing-guides/running-large-tests/","title":"","level":0},{"type":"text","content":"running large-scale tests","level":1},{"type":"link_close","level":0},{"type":"text","content":" and ","level":0},{"type":"link_open","href":"https://k6.io/docs/misc/fine-tuning-os/","title":"","level":0},{"type":"text","content":"fine-tuning OS","level":1},{"type":"link_close","level":0},{"type":"text","content":" to tweak the config for AWS instances.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[33,35],"level":1},{"type":"paragraph_open","tight":true,"lines":[33,34],"level":2},{"type":"inline","content":"Grafana + Prometheus for monitoring.","level":3,"lines":[33,34],"children":[{"type":"text","content":"Grafana + Prometheus for monitoring.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"ordered_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[35,36],"level":0},{"type":"inline","content":"To simulate 1,000,000 concurrent active connections, we used 20 AWS EC2 instances with 16 cores and 32GB of RAM. We ran the tests for up to 2 hours to ensure that the Supavisor can handle load over long periods.","level":1,"lines":[35,36],"children":[{"type":"text","content":"To simulate 1,000,000 concurrent active connections, we used 20 AWS EC2 instances with 16 cores and 32GB of RAM. We ran the tests for up to 2 hours to ensure that the Supavisor can handle load over long periods.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[37,38],"level":0},{"type":"inline","content":"[Establishing a baseline](#establishing-a-baseline)","level":1,"lines":[37,38],"children":[{"type":"text","content":"Establishing a baseline","level":0}],"lvl":3,"i":3,"seen":0,"slug":"establishing-a-baseline"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[39,40],"level":0},{"type":"inline","content":"In the first test, we set up a single ARM 16-core Supavisor instance on Ubuntu 22.04.2 aarch64 connected to one database instance.","level":1,"lines":[39,40],"children":[{"type":"text","content":"In the first test, we set up a single ARM 16-core Supavisor instance on Ubuntu 22.04.2 aarch64 connected to one database instance.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[41,45],"level":0},{"type":"inline","content":"\u003cImg\n  alt=\"diagram supavisor 1 instance dark\"\n  src=\"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--baseline.png\"\n/\u003e","level":1,"lines":[41,45],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"diagram supavisor 1 instance dark\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--baseline.png\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[46,47],"level":0},{"type":"inline","content":"We wanted to assess the capacity of a single Supavisor instance. We achieved:","level":1,"lines":[46,47],"children":[{"type":"text","content":"We wanted to assess the capacity of a single Supavisor instance. We achieved:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[48,52],"level":0},{"type":"list_item_open","lines":[48,49],"level":1},{"type":"paragraph_open","tight":true,"lines":[48,49],"level":2},{"type":"inline","content":"250,000 concurrent connections to Supavisor","level":3,"lines":[48,49],"children":[{"type":"text","content":"250,000 concurrent connections to Supavisor","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[49,50],"level":1},{"type":"paragraph_open","tight":true,"lines":[49,50],"level":2},{"type":"inline","content":"Supavisor was running with a pool of 400 direct connections to the database","level":3,"lines":[49,50],"children":[{"type":"text","content":"Supavisor was running with a pool of 400 direct connections to the database","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[50,52],"level":1},{"type":"paragraph_open","tight":true,"lines":[50,51],"level":2},{"type":"inline","content":"The system was processing 20,000 queries per second (QPS)","level":3,"lines":[50,51],"children":[{"type":"text","content":"The system was processing 20,000 queries per second (QPS)","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[52,56],"level":0},{"type":"inline","content":"\u003cImg\n  alt=\"diagram supavisor 1 instance dark\"\n  src=\"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--250k-test.png\"\n/\u003e","level":1,"lines":[52,56],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"diagram supavisor 1 instance dark\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--250k-test.png\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[57,58],"level":0},{"type":"inline","content":"With this setup the database is the bottleneck - 20,000 QPS was the maximum this instance could handle. Increasing QPS would have been possible with a larger instance or read-replicas, but we wanted to focus on the scalability of Supavisor's connection limit (not Postgres's). Since Supavisor is built with multi-tenancy, addition of read-replicas is as easy as sending a single post request.","level":1,"lines":[57,58],"children":[{"type":"text","content":"With this setup the database is the bottleneck - 20,000 QPS was the maximum this instance could handle. Increasing QPS would have been possible with a larger instance or read-replicas, but we wanted to focus on the scalability of Supavisor's connection limit (not Postgres's). Since Supavisor is built with multi-tenancy, addition of read-replicas is as easy as sending a single post request.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[59,68],"level":0},{"type":"inline","content":"\u003cdiv\u003e\n  \u003cImg\n    alt=\"chart connections baseline light\"\n    src={{\n      light: '/images/blog/2023-08-11-supavisor-1-million/chart-baseline-light.png',\n      dark: '/images/blog/2023-08-11-supavisor-1-million/chart-baseline-dark.png',\n    }}\n  /\u003e\n\u003c/div\u003e","level":1,"lines":[59,68],"children":[{"type":"text","content":"\u003cdiv\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"chart connections baseline light\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src={{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"light: '/images/blog/2023-08-11-supavisor-1-million/chart-baseline-light.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"dark: '/images/blog/2023-08-11-supavisor-1-million/chart-baseline-dark.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"}}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[69,70],"level":0},{"type":"inline","content":"[Supavisor's scaling capabilities](#supavisors-scaling-capabilities)","level":1,"lines":[69,70],"children":[{"type":"text","content":"Supavisor's scaling capabilities","level":0}],"lvl":3,"i":4,"seen":0,"slug":"supavisors-scaling-capabilities"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[71,72],"level":0},{"type":"inline","content":"In the next step, we focused on Supavisor's vertical scaling capabilities by connecting **500,000 concurrent users** with a 64-core Supavisor instance while the single database instance configuration remained the same.","level":1,"lines":[71,72],"children":[{"type":"text","content":"In the next step, we focused on Supavisor's vertical scaling capabilities by connecting ","level":0},{"type":"strong_open","level":0},{"type":"text","content":"500,000 concurrent users","level":1},{"type":"strong_close","level":0},{"type":"text","content":" with a 64-core Supavisor instance while the single database instance configuration remained the same.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[73,77],"level":0},{"type":"inline","content":"\u003cImg\n  alt=\"diagram supavisor 1 instance dark\"\n  src=\"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--500k-test.png\"\n/\u003e","level":1,"lines":[73,77],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"diagram supavisor 1 instance dark\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--500k-test.png\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[78,79],"level":0},{"type":"inline","content":"The system showed no signs of instability or performance degradation. QPS remained constant at 20,000, proving that an increased number of connections doesn't negatively affect Supavisor's overall performance (this is generally expected from a [BEAM-based language](https://stressgrid.com/blog/webserver_benchmark/) like Elixir).","level":1,"lines":[78,79],"children":[{"type":"text","content":"The system showed no signs of instability or performance degradation. QPS remained constant at 20,000, proving that an increased number of connections doesn't negatively affect Supavisor's overall performance (this is generally expected from a ","level":0},{"type":"link_open","href":"https://stressgrid.com/blog/webserver_benchmark/","title":"","level":0},{"type":"text","content":"BEAM-based language","level":1},{"type":"link_close","level":0},{"type":"text","content":" like Elixir).","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[80,89],"level":0},{"type":"inline","content":"\u003cdiv\u003e\n  \u003cImg\n    alt=\"chart connections vertical scaling light\"\n    src={{\n      light: '/images/blog/2023-08-11-supavisor-1-million/chart-vertical-light.png',\n      dark: '/images/blog/2023-08-11-supavisor-1-million/chart-vertical-dark.png',\n    }}\n  /\u003e\n\u003c/div\u003e","level":1,"lines":[80,89],"children":[{"type":"text","content":"\u003cdiv\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"chart connections vertical scaling light\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src={{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"light: '/images/blog/2023-08-11-supavisor-1-million/chart-vertical-light.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"dark: '/images/blog/2023-08-11-supavisor-1-million/chart-vertical-dark.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"}}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[90,91],"level":0},{"type":"inline","content":"We also monitored how the load was distributed over Supavisor instance's cores:","level":1,"lines":[90,91],"children":[{"type":"text","content":"We also monitored how the load was distributed over Supavisor instance's cores:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[92,101],"level":0},{"type":"inline","content":"\u003cdiv\u003e\n  \u003cImg\n    alt=\"cpu load vertical scaling light\"\n    src={{\n      light: '/images/blog/2023-08-11-supavisor-1-million/cpu-vertical-light.png',\n      dark: '/images/blog/2023-08-11-supavisor-1-million/cpu-vertical-dark.png',\n    }}\n  /\u003e\n\u003c/div\u003e","level":1,"lines":[92,101],"children":[{"type":"text","content":"\u003cdiv\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"cpu load vertical scaling light\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src={{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"light: '/images/blog/2023-08-11-supavisor-1-million/cpu-vertical-light.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"dark: '/images/blog/2023-08-11-supavisor-1-million/cpu-vertical-dark.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"}}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[102,103],"level":0},{"type":"inline","content":"The load is spread evenly between all cores, which is great. CPU usage is high, signaling that the current setup has reached its capacity: a single Supavisor instance with 64 core handles around 500,000 connections. With this reference number, we moved on to horizontal scaling tests.","level":1,"lines":[102,103],"children":[{"type":"text","content":"The load is spread evenly between all cores, which is great. CPU usage is high, signaling that the current setup has reached its capacity: a single Supavisor instance with 64 core handles around 500,000 connections. With this reference number, we moved on to horizontal scaling tests.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[104,105],"level":0},{"type":"inline","content":"[Scaling to 1,000,000 connections](#scaling-to-1000000-connections)","level":1,"lines":[104,105],"children":[{"type":"text","content":"Scaling to 1,000,000 connections","level":0}],"lvl":3,"i":5,"seen":0,"slug":"scaling-to-1000000-connections"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[106,107],"level":0},{"type":"inline","content":"To examine horizontal scalability, we deployed two 64-core Supavisor instances, with the first instance connected directly to the database and the other relaying queries through the first.","level":1,"lines":[106,107],"children":[{"type":"text","content":"To examine horizontal scalability, we deployed two 64-core Supavisor instances, with the first instance connected directly to the database and the other relaying queries through the first.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[108,112],"level":0},{"type":"inline","content":"\u003cImg\n  alt=\"diagram supavisor 1 instance dark\"\n  src=\"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--scaling-to-1million.png\"\n/\u003e","level":1,"lines":[108,112],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"diagram supavisor 1 instance dark\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--scaling-to-1million.png\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[113,114],"level":0},{"type":"inline","content":"In the Supavisor architecture, only a single node holds direct connections to each database instance. When you add more Postgres databases or read-replicas, Supavisor spreads the connections to the replicas. Every Supavisor instance can accept incoming connections and either execute queries themselves (if they directly connected) or relay to another node (if not).","level":1,"lines":[113,114],"children":[{"type":"text","content":"In the Supavisor architecture, only a single node holds direct connections to each database instance. When you add more Postgres databases or read-replicas, Supavisor spreads the connections to the replicas. Every Supavisor instance can accept incoming connections and either execute queries themselves (if they directly connected) or relay to another node (if not).","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[115,116],"level":0},{"type":"inline","content":"This setup successfully handled:","level":1,"lines":[115,116],"children":[{"type":"text","content":"This setup successfully handled:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[117,120],"level":0},{"type":"list_item_open","lines":[117,118],"level":1},{"type":"paragraph_open","tight":true,"lines":[117,118],"level":2},{"type":"inline","content":"**1,003,200 simultaneous connections** to the Supavisor instances.","level":3,"lines":[117,118],"children":[{"type":"strong_open","level":0},{"type":"text","content":"1,003,200 simultaneous connections","level":1},{"type":"strong_close","level":0},{"type":"text","content":" to the Supavisor instances.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[118,120],"level":1},{"type":"paragraph_open","tight":true,"lines":[118,119],"level":2},{"type":"inline","content":"**20,000 QPS** or **1.2 million queries per minute.** Each connection executed a `select` query once every 50 seconds.","level":3,"lines":[118,119],"children":[{"type":"strong_open","level":0},{"type":"text","content":"20,000 QPS","level":1},{"type":"strong_close","level":0},{"type":"text","content":" or ","level":0},{"type":"strong_open","level":0},{"type":"text","content":"1.2 million queries per minute.","level":1},{"type":"strong_close","level":0},{"type":"text","content":" Each connection executed a ","level":0},{"type":"code","content":"select","block":false,"level":0},{"type":"text","content":" query once every 50 seconds.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[120,129],"level":0},{"type":"inline","content":"\u003cdiv\u003e\n  \u003cImg\n    alt=\"chart connections 1 million light\"\n    src={{\n      light: '/images/blog/2023-08-11-supavisor-1-million/chart-1m-light.png',\n      dark: '/images/blog/2023-08-11-supavisor-1-million/chart-1m-dark.png',\n    }}\n  /\u003e\n\u003c/div\u003e","level":1,"lines":[120,129],"children":[{"type":"text","content":"\u003cdiv\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"chart connections 1 million light\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src={{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"light: '/images/blog/2023-08-11-supavisor-1-million/chart-1m-light.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"dark: '/images/blog/2023-08-11-supavisor-1-million/chart-1m-dark.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"}}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[130,131],"level":0},{"type":"inline","content":"Within the cluster:","level":1,"lines":[130,131],"children":[{"type":"text","content":"Within the cluster:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"bullet_list_open","lines":[132,135],"level":0},{"type":"list_item_open","lines":[132,133],"level":1},{"type":"paragraph_open","tight":true,"lines":[132,133],"level":2},{"type":"inline","content":"The directly connected instance was under almost the same load as when handling 500,000 concurrent clients in a single-node mode.","level":3,"lines":[132,133],"children":[{"type":"text","content":"The directly connected instance was under almost the same load as when handling 500,000 concurrent clients in a single-node mode.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"list_item_open","lines":[133,135],"level":1},{"type":"paragraph_open","tight":true,"lines":[133,134],"level":2},{"type":"inline","content":"The relaying instance was extremely over-resourced. Most cores had little-to-no workload because relayed connections are more lightweight.","level":3,"lines":[133,134],"children":[{"type":"text","content":"The relaying instance was extremely over-resourced. Most cores had little-to-no workload because relayed connections are more lightweight.","level":0}]},{"type":"paragraph_close","tight":true,"level":2},{"type":"list_item_close","level":1},{"type":"bullet_list_close","level":0},{"type":"paragraph_open","tight":false,"lines":[135,144],"level":0},{"type":"inline","content":"\u003cdiv\u003e\n  \u003cImg\n    alt=\"cpu load 1 million light\"\n    src={{\n      light: '/images/blog/2023-08-11-supavisor-1-million/cpu-1m-light.png',\n      dark: '/images/blog/2023-08-11-supavisor-1-million/cpu-1m-dark.png',\n    }}\n  /\u003e\n\u003c/div\u003e","level":1,"lines":[135,144],"children":[{"type":"text","content":"\u003cdiv\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"cpu load 1 million light\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src={{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"light: '/images/blog/2023-08-11-supavisor-1-million/cpu-1m-light.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"dark: '/images/blog/2023-08-11-supavisor-1-million/cpu-1m-dark.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"}}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[145,146],"level":0},{"type":"inline","content":"In a multi-tenant setup (or when using Read-Replicas), the load is much more evenly spread because all Supavisor instances connect to comparable numbers of databases and have both direct and relayed connections evenly distributed between each other.","level":1,"lines":[145,146],"children":[{"type":"text","content":"In a multi-tenant setup (or when using Read-Replicas), the load is much more evenly spread because all Supavisor instances connect to comparable numbers of databases and have both direct and relayed connections evenly distributed between each other.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[147,151],"level":0},{"type":"inline","content":"\u003cImg\n  alt=\"diagram supavisor 1 instance dark\"\n  src=\"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--multi-tenant-concept.png\"\n/\u003e","level":1,"lines":[147,151],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"diagram supavisor 1 instance dark\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--multi-tenant-concept.png\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[152,153],"level":0},{"type":"inline","content":"[Supavisor's impact on query duration](#supavisors-impact-on-query-duration)","level":1,"lines":[152,153],"children":[{"type":"text","content":"Supavisor's impact on query duration","level":0}],"lvl":3,"i":6,"seen":0,"slug":"supavisors-impact-on-query-duration"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[154,155],"level":0},{"type":"inline","content":"To measure the impact on query duration, we started with 5,000 queries per second. This allows us to exclude side effects from the database side (long query execution times).","level":1,"lines":[154,155],"children":[{"type":"text","content":"To measure the impact on query duration, we started with 5,000 queries per second. This allows us to exclude side effects from the database side (long query execution times).","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[156,157],"level":0},{"type":"inline","content":"The query used in the experiment was the following:","level":1,"lines":[156,157],"children":[{"type":"text","content":"The query used in the experiment was the following:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql","content":"select *\nfrom (\n    values\n    (1, 'one'),\n    (2, 'two'),\n    (3, 'three')\n) as t (num, letter);\n","lines":[158,167],"level":0},{"type":"paragraph_open","tight":false,"lines":[168,169],"level":0},{"type":"inline","content":"We found with Supavisor median query duration was less than 2ms. And this includes not only time from client to Supavisor but the whole roundtrip: from Client to Supavisor ➡️ from Supavisor to Postgres ➡️ then query execution time on Postgres ➡️ and back to Supavisor ➡️ and to the Client.","level":1,"lines":[168,169],"children":[{"type":"text","content":"We found with Supavisor median query duration was less than 2ms. And this includes not only time from client to Supavisor but the whole roundtrip: from Client to Supavisor ➡️ from Supavisor to Postgres ➡️ then query execution time on Postgres ➡️ and back to Supavisor ➡️ and to the Client.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"table_open","lines":[170,175],"level":0},{"type":"thead_open","lines":[170,171],"level":1},{"type":"tr_open","lines":[170,171],"level":2},{"type":"th_open","align":"","lines":[170,171],"level":3},{"type":"inline","content":"","lines":[170,171],"level":4,"children":[]},{"type":"th_close","level":3},{"type":"th_open","align":"","lines":[170,171],"level":3},{"type":"inline","content":"Query Duration","lines":[170,171],"level":4,"children":[{"type":"text","content":"Query Duration","level":0}]},{"type":"th_close","level":3},{"type":"tr_close","level":2},{"type":"thead_close","level":1},{"type":"tbody_open","lines":[172,175],"level":1},{"type":"tr_open","level":2},{"type":"td_open","align":"","level":3},{"type":"inline","content":"Median","level":4,"children":[{"type":"text","content":"Median","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"2ms","level":4,"children":[{"type":"text","content":"2ms","level":0}]},{"type":"td_close","level":3},{"type":"tr_close","level":2},{"type":"tr_open","level":2},{"type":"td_open","align":"","level":3},{"type":"inline","content":"p95","level":4,"children":[{"type":"text","content":"p95","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"3ms","level":4,"children":[{"type":"text","content":"3ms","level":0}]},{"type":"td_close","level":3},{"type":"tr_close","level":2},{"type":"tr_open","level":2},{"type":"td_open","align":"","level":3},{"type":"inline","content":"p99","level":4,"children":[{"type":"text","content":"p99","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"23ms","level":4,"children":[{"type":"text","content":"23ms","level":0}]},{"type":"td_close","level":3},{"type":"tr_close","level":2},{"type":"tbody_close","level":1},{"type":"table_close","level":0},{"type":"paragraph_open","tight":false,"lines":[176,185],"level":0},{"type":"inline","content":"\u003cdiv\u003e\n  \u003cImg\n    alt=\"chart query duration for 5k qps light\"\n    src={{\n      light: '/images/blog/2023-08-11-supavisor-1-million/chart-5k-light.png',\n      dark: '/images/blog/2023-08-11-supavisor-1-million/chart-5k-dark.png',\n    }}\n  /\u003e\n\u003c/div\u003e","level":1,"lines":[176,185],"children":[{"type":"text","content":"\u003cdiv\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"chart query duration for 5k qps light\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src={{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"light: '/images/blog/2023-08-11-supavisor-1-million/chart-5k-light.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"dark: '/images/blog/2023-08-11-supavisor-1-million/chart-5k-dark.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"}}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[186,187],"level":0},{"type":"inline","content":"We can see that 95% of queries were completed in less than 3 milliseconds. A slightly higher query duration at the beginning of the test can be explained by the dynamic nature of Supavisor-to-Database connection pool. It is being scaled up to the hard limit when more clients establish connections to Supavisor itself and scaled back down when users leave.","level":1,"lines":[186,187],"children":[{"type":"text","content":"We can see that 95% of queries were completed in less than 3 milliseconds. A slightly higher query duration at the beginning of the test can be explained by the dynamic nature of Supavisor-to-Database connection pool. It is being scaled up to the hard limit when more clients establish connections to Supavisor itself and scaled back down when users leave.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[188,189],"level":0},{"type":"inline","content":"We continued to scale up to 20,000QPS to assess the impact on query duration and measured a median of 18.4ms:","level":1,"lines":[188,189],"children":[{"type":"text","content":"We continued to scale up to 20,000QPS to assess the impact on query duration and measured a median of 18.4ms:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"table_open","lines":[190,195],"level":0},{"type":"thead_open","lines":[190,191],"level":1},{"type":"tr_open","lines":[190,191],"level":2},{"type":"th_open","align":"","lines":[190,191],"level":3},{"type":"inline","content":"","lines":[190,191],"level":4,"children":[]},{"type":"th_close","level":3},{"type":"th_open","align":"","lines":[190,191],"level":3},{"type":"inline","content":"Query Duration","lines":[190,191],"level":4,"children":[{"type":"text","content":"Query Duration","level":0}]},{"type":"th_close","level":3},{"type":"tr_close","level":2},{"type":"thead_close","level":1},{"type":"tbody_open","lines":[192,195],"level":1},{"type":"tr_open","level":2},{"type":"td_open","align":"","level":3},{"type":"inline","content":"Median","level":4,"children":[{"type":"text","content":"Median","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"18.4ms","level":4,"children":[{"type":"text","content":"18.4ms","level":0}]},{"type":"td_close","level":3},{"type":"tr_close","level":2},{"type":"tr_open","level":2},{"type":"td_open","align":"","level":3},{"type":"inline","content":"p95","level":4,"children":[{"type":"text","content":"p95","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"46.9ms","level":4,"children":[{"type":"text","content":"46.9ms","level":0}]},{"type":"td_close","level":3},{"type":"tr_close","level":2},{"type":"tr_open","level":2},{"type":"td_open","align":"","level":3},{"type":"inline","content":"p99","level":4,"children":[{"type":"text","content":"p99","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"68ms","level":4,"children":[{"type":"text","content":"68ms","level":0}]},{"type":"td_close","level":3},{"type":"tr_close","level":2},{"type":"tbody_close","level":1},{"type":"table_close","level":0},{"type":"paragraph_open","tight":false,"lines":[196,205],"level":0},{"type":"inline","content":"\u003cdiv\u003e\n  \u003cImg\n    alt=\"chart query duration for 20k qps light\"\n    src={{\n      light: '/images/blog/2023-08-11-supavisor-1-million/chart-20k-light.png',\n      dark: '/images/blog/2023-08-11-supavisor-1-million/chart-20k-dark.png',\n    }}\n  /\u003e\n\u003c/div\u003e","level":1,"lines":[196,205],"children":[{"type":"text","content":"\u003cdiv\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"chart query duration for 20k qps light\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src={{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"light: '/images/blog/2023-08-11-supavisor-1-million/chart-20k-light.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"dark: '/images/blog/2023-08-11-supavisor-1-million/chart-20k-dark.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"}}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[206,207],"level":0},{"type":"inline","content":"Database experiences much more load and more concurrent queries, which leads to higher execution times on the database side. And here are some metrics from the database side:","level":1,"lines":[206,207],"children":[{"type":"text","content":"Database experiences much more load and more concurrent queries, which leads to higher execution times on the database side. And here are some metrics from the database side:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[208,212],"level":0},{"type":"inline","content":"\u003cImg\n  src=\"/images/blog/2023-08-11-supavisor-1-million/postgres-metrics.png\"\n  alt=\"Postgres metrics\"\n/\u003e","level":1,"lines":[208,212],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"/images/blog/2023-08-11-supavisor-1-million/postgres-metrics.png\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"Postgres metrics\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[213,214],"level":0},{"type":"inline","content":"The scalability can be further enhanced by adding more databases (or read-replicas if you want to scale a single app) to increase QPS or deploying additional Supavisor instances to accommodate tens of millions of concurrent connections.","level":1,"lines":[213,214],"children":[{"type":"text","content":"The scalability can be further enhanced by adding more databases (or read-replicas if you want to scale a single app) to increase QPS or deploying additional Supavisor instances to accommodate tens of millions of concurrent connections.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[215,216],"level":0},{"type":"inline","content":"[Supavisor on Supabase Platform](#supavisor-on-supabase-platform)","level":1,"lines":[215,216],"children":[{"type":"text","content":"Supavisor on Supabase Platform","level":0}],"lvl":3,"i":7,"seen":0,"slug":"supavisor-on-supabase-platform"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[217,218],"level":0},{"type":"inline","content":"We compared our current PgBouncer setup with the new Supavisor setup to assess any impact on query duration.","level":1,"lines":[217,218],"children":[{"type":"text","content":"We compared our current PgBouncer setup with the new Supavisor setup to assess any impact on query duration.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[219,220],"level":0},{"type":"inline","content":"_Current architecture_","level":1,"lines":[219,220],"children":[{"type":"em_open","level":0},{"type":"text","content":"Current architecture","level":1},{"type":"em_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[221,222],"level":0},{"type":"inline","content":"Currently, every Supabase project comes with its own PgBouncer server running on the same instance as the Postgres database to ensure that the latency is as low as possible. But this setup comes with a trade-off: it uses the same compute resources as your database.","level":1,"lines":[221,222],"children":[{"type":"text","content":"Currently, every Supabase project comes with its own PgBouncer server running on the same instance as the Postgres database to ensure that the latency is as low as possible. But this setup comes with a trade-off: it uses the same compute resources as your database.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[223,227],"level":0},{"type":"inline","content":"\u003cImg\n  alt=\"diagram supavisor 1 instance dark\"\n  src=\"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--pgbouncer.png\"\n/\u003e","level":1,"lines":[223,227],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"diagram supavisor 1 instance dark\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--pgbouncer.png\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[228,229],"level":0},{"type":"inline","content":"_Supavisor architecture_","level":1,"lines":[228,229],"children":[{"type":"em_open","level":0},{"type":"text","content":"Supavisor architecture","level":1},{"type":"em_close","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[230,231],"level":0},{"type":"inline","content":"In the future, you connect to a distinct multi-tenant Supavisor cluster through a load-balancer. The Supavisor cluster maintains a connection pool to your database. In this case the pooler doesn't consume additional CPU and RAM resources on the database server, but it does involve extra network latency.","level":1,"lines":[230,231],"children":[{"type":"text","content":"In the future, you connect to a distinct multi-tenant Supavisor cluster through a load-balancer. The Supavisor cluster maintains a connection pool to your database. In this case the pooler doesn't consume additional CPU and RAM resources on the database server, but it does involve extra network latency.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[232,236],"level":0},{"type":"inline","content":"\u003cImg\n  alt=\"diagram supavisor 1 instance dark\"\n  src=\"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--supavisor-and-supabase.png\"\n/\u003e","level":1,"lines":[232,236],"children":[{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"diagram supavisor 1 instance dark\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"/images/blog/2023-08-11-supavisor-1-million/supavisor-tests--supavisor-and-supabase.png\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[237,238],"level":0},{"type":"inline","content":"We ran 5,000 queries per second for each configuration, this time experimenting with `insert` query. To make the experiment more realistic, we enabled the PostGIS extension to store coordinates:","level":1,"lines":[237,238],"children":[{"type":"text","content":"We ran 5,000 queries per second for each configuration, this time experimenting with ","level":0},{"type":"code","content":"insert","block":false,"level":0},{"type":"text","content":" query. To make the experiment more realistic, we enabled the PostGIS extension to store coordinates:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"fence","params":"sql","content":"insert into positions (\n    stud_id,\n    first_name,\n    last_name,\n    title,\n    reports_to,\n    timestamp,\n    location,\n    email\n)\nvalues (\n    ${name},\n    'Virtual ${name}',\n    'User ${name}',\n    'Load Tester',\n    1,\n    ${Date.now()},\n    st_point(-73.946${x}, 40.807${y}),\n    'vu${name}@acme.corp'\n);\n","lines":[239,261],"level":0},{"type":"paragraph_open","tight":false,"lines":[262,263],"level":0},{"type":"inline","content":"We observed an additional 2ms required each query to be executed on the Supavisor architecture compared to PgBouncer architecture.","level":1,"lines":[262,263],"children":[{"type":"text","content":"We observed an additional 2ms required each query to be executed on the Supavisor architecture compared to PgBouncer architecture.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"table_open","lines":[264,269],"level":0},{"type":"thead_open","lines":[264,265],"level":1},{"type":"tr_open","lines":[264,265],"level":2},{"type":"th_open","align":"","lines":[264,265],"level":3},{"type":"inline","content":"","lines":[264,265],"level":4,"children":[]},{"type":"th_close","level":3},{"type":"th_open","align":"","lines":[264,265],"level":3},{"type":"inline","content":"Query Duration with Supavisor","lines":[264,265],"level":4,"children":[{"type":"text","content":"Query Duration with Supavisor","level":0}]},{"type":"th_close","level":3},{"type":"th_open","align":"","lines":[264,265],"level":3},{"type":"inline","content":"Query Duration with PgBouncer","lines":[264,265],"level":4,"children":[{"type":"text","content":"Query Duration with PgBouncer","level":0}]},{"type":"th_close","level":3},{"type":"tr_close","level":2},{"type":"thead_close","level":1},{"type":"tbody_open","lines":[266,269],"level":1},{"type":"tr_open","level":2},{"type":"td_open","align":"","level":3},{"type":"inline","content":"Median","level":4,"children":[{"type":"text","content":"Median","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"4ms","level":4,"children":[{"type":"text","content":"4ms","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"1ms","level":4,"children":[{"type":"text","content":"1ms","level":0}]},{"type":"td_close","level":3},{"type":"tr_close","level":2},{"type":"tr_open","level":2},{"type":"td_open","align":"","level":3},{"type":"inline","content":"p95","level":4,"children":[{"type":"text","content":"p95","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"4ms","level":4,"children":[{"type":"text","content":"4ms","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"2ms","level":4,"children":[{"type":"text","content":"2ms","level":0}]},{"type":"td_close","level":3},{"type":"tr_close","level":2},{"type":"tr_open","level":2},{"type":"td_open","align":"","level":3},{"type":"inline","content":"p99","level":4,"children":[{"type":"text","content":"p99","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"5ms","level":4,"children":[{"type":"text","content":"5ms","level":0}]},{"type":"td_close","level":3},{"type":"td_open","align":"","level":3},{"type":"inline","content":"3ms","level":4,"children":[{"type":"text","content":"3ms","level":0}]},{"type":"td_close","level":3},{"type":"tr_close","level":2},{"type":"tbody_close","level":1},{"type":"table_close","level":0},{"type":"paragraph_open","tight":false,"lines":[270,281],"level":0},{"type":"inline","content":"\u003cdiv\u003e\n  \u003cImg\n    style={{ marginBottom: '0em' }}\n    alt=\"chart query duration for 5k qps inserts with supavisor light\"\n    src={{\n      light: '/images/blog/2023-08-11-supavisor-1-million/chart-supavisor-duration-light.png',\n      dark: '/images/blog/2023-08-11-supavisor-1-million/chart-supavisor-duration-dark.png',\n    }}\n  /\u003e\n  \u003cfigcaption\u003eFig.1 - Query Duration with Supavisor\u003c/figcaption\u003e\n\u003c/div\u003e","level":1,"lines":[270,281],"children":[{"type":"text","content":"\u003cdiv\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"style={{ marginBottom: '0em' }}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"chart query duration for 5k qps inserts with supavisor light\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src={{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"light: '/images/blog/2023-08-11-supavisor-1-million/chart-supavisor-duration-light.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"dark: '/images/blog/2023-08-11-supavisor-1-million/chart-supavisor-duration-dark.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"}}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cfigcaption\u003eFig.1 - Query Duration with Supavisor\u003c/figcaption\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[282,293],"level":0},{"type":"inline","content":"\u003cdiv\u003e\n  \u003cImg\n    style={{ marginBottom: '0em' }}\n    alt=\"chart query duration for 5k qps inserts with pgbouncer light\"\n    src={{\n      light: '/images/blog/2023-08-11-supavisor-1-million/chart-pgbouncer-duration-light.png',\n      dark: '/images/blog/2023-08-11-supavisor-1-million/chart-pgbouncer-duration-dark.png',\n    }}\n  /\u003e\n  \u003cfigcaption\u003eFig.2 - Query Duration with PgBouncer\u003c/figcaption\u003e\n\u003c/div\u003e","level":1,"lines":[282,293],"children":[{"type":"text","content":"\u003cdiv\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cImg","level":0},{"type":"softbreak","level":0},{"type":"text","content":"style={{ marginBottom: '0em' }}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"alt=\"chart query duration for 5k qps inserts with pgbouncer light\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src={{","level":0},{"type":"softbreak","level":0},{"type":"text","content":"light: '/images/blog/2023-08-11-supavisor-1-million/chart-pgbouncer-duration-light.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"dark: '/images/blog/2023-08-11-supavisor-1-million/chart-pgbouncer-duration-dark.png',","level":0},{"type":"softbreak","level":0},{"type":"text","content":"}}","level":0},{"type":"softbreak","level":0},{"type":"text","content":"/\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cfigcaption\u003eFig.2 - Query Duration with PgBouncer\u003c/figcaption\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":3,"lines":[294,295],"level":0},{"type":"inline","content":"[Getting started](#getting-started)","level":1,"lines":[294,295],"children":[{"type":"text","content":"Getting started","level":0}],"lvl":3,"i":8,"seen":0,"slug":"getting-started"},{"type":"heading_close","hLevel":3,"level":0},{"type":"paragraph_open","tight":false,"lines":[296,297],"level":0},{"type":"inline","content":"Supavisor has been rolled out to all Supabase projects in all regions.","level":1,"lines":[296,297],"children":[{"type":"text","content":"Supavisor has been rolled out to all Supabase projects in all regions.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[298,299],"level":0},{"type":"inline","content":"Contact [support](https://supabase.com/dashboard/support/new) to start using it today, and we'll provide connection details. We will be exposing a new connection string in project dashboards over the next few weeks.","level":1,"lines":[298,299],"children":[{"type":"text","content":"Contact ","level":0},{"type":"link_open","href":"https://supabase.com/dashboard/support/new","title":"","level":0},{"type":"text","content":"support","level":1},{"type":"link_close","level":0},{"type":"text","content":" to start using it today, and we'll provide connection details. We will be exposing a new connection string in project dashboards over the next few weeks.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[300,301],"level":0},{"type":"inline","content":"You'll be able to use both PgBouncer and Supavisor for a few months in parallel. Nothing will change with your PgBouncer setup if you need to switch back.","level":1,"lines":[300,301],"children":[{"type":"text","content":"You'll be able to use both PgBouncer and Supavisor for a few months in parallel. Nothing will change with your PgBouncer setup if you need to switch back.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[302,303],"level":0},{"type":"inline","content":"Supavisor will be added to the self-hosted stack as soon as we have tested it across our database fleet. That said - we're confident that it's ready for use if you want to try it with your own Postgres database. [Sequin](https://sequin.io/), one of our partners, has been using Supavisor for several months:","level":1,"lines":[302,303],"children":[{"type":"text","content":"Supavisor will be added to the self-hosted stack as soon as we have tested it across our database fleet. That said - we're confident that it's ready for use if you want to try it with your own Postgres database. ","level":0},{"type":"link_open","href":"https://sequin.io/","title":"","level":0},{"type":"text","content":"Sequin","level":1},{"type":"link_close","level":0},{"type":"text","content":", one of our partners, has been using Supavisor for several months:","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[304,316],"level":0},{"type":"inline","content":"\u003cQuote img=\"anthony-accomazzo.jpeg\" caption=\"Anthony Accomazzo, Co-founder of Sequin.\"\u003e\n  \u003cp\u003e\n    With Supavisor, we've been able to ship incredible features that would have been very hard to\n    build otherwise. For example, our customers can now read from and write to Salesforce and\n    HubSpot via Postgres. We achieve this by intercepting queries that route through Supavisor.\n  \u003c/p\u003e\n  \u003cp\u003e\n    We chose Supavisor because it's scalable, multi-tenant, and written in Elixir. We were able to\n    integrate it easily with our own Elixir infrastructure. As partners, we look forward to helping\n    shape the future of Postgres connection pooling with Supabase.\n  \u003c/p\u003e\n\u003c/Quote\u003e","level":1,"lines":[304,316],"children":[{"type":"text","content":"\u003cQuote img=\"anthony-accomazzo.jpeg\" caption=\"Anthony Accomazzo, Co-founder of Sequin.\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cp\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"With Supavisor, we've been able to ship incredible features that would have been very hard to","level":0},{"type":"softbreak","level":0},{"type":"text","content":"build otherwise. For example, our customers can now read from and write to Salesforce and","level":0},{"type":"softbreak","level":0},{"type":"text","content":"HubSpot via Postgres. We achieve this by intercepting queries that route through Supavisor.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/p\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003cp\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"We chose Supavisor because it's scalable, multi-tenant, and written in Elixir. We were able to","level":0},{"type":"softbreak","level":0},{"type":"text","content":"integrate it easily with our own Elixir infrastructure. As partners, we look forward to helping","level":0},{"type":"softbreak","level":0},{"type":"text","content":"shape the future of Postgres connection pooling with Supabase.","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/p\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/Quote\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"heading_open","hLevel":2,"lines":[317,318],"level":0},{"type":"inline","content":"[Conclusion](#conclusion)","level":1,"lines":[317,318],"children":[{"type":"text","content":"Conclusion","level":0}],"lvl":2,"i":9,"seen":0,"slug":"conclusion"},{"type":"heading_close","hLevel":2,"level":0},{"type":"paragraph_open","tight":false,"lines":[319,320],"level":0},{"type":"inline","content":"Supavisor's vertical and horizontal scaling ability make it the optimal solution for developers who aim to create applications that can effortlessly scale, even under extreme workloads, avoiding issues such as \"too many connections\" and enabling the full power of edge functions and serverless.","level":1,"lines":[319,320],"children":[{"type":"text","content":"Supavisor's vertical and horizontal scaling ability make it the optimal solution for developers who aim to create applications that can effortlessly scale, even under extreme workloads, avoiding issues such as \"too many connections\" and enabling the full power of edge functions and serverless.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[321,322],"level":0},{"type":"inline","content":"If you are interested in exploring Supavisor's potential or want to implement its scalability in your upcoming project, check out [the GitHub repository](https://github.com/supabase/supavisor) to know more.","level":1,"lines":[321,322],"children":[{"type":"text","content":"If you are interested in exploring Supavisor's potential or want to implement its scalability in your upcoming project, check out ","level":0},{"type":"link_open","href":"https://github.com/supabase/supavisor","title":"","level":0},{"type":"text","content":"the GitHub repository","level":1},{"type":"link_close","level":0},{"type":"text","content":" to know more.","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"paragraph_open","tight":false,"lines":[323,331],"level":0},{"type":"inline","content":"\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    className=\"w-full\"\n    src=\"https://www.youtube-nocookie.com/embed/qzxzLSAJDfE\"\n    title=\"YouTube video player\"\n    frameBorder=\"0\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"\n    allowfullscreen","level":1,"lines":[323,331],"children":[{"type":"text","content":"\u003cdiv className=\"video-container\"\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003ciframe","level":0},{"type":"softbreak","level":0},{"type":"text","content":"className=\"w-full\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"src=\"https://www.youtube-nocookie.com/embed/qzxzLSAJDfE\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"title=\"YouTube video player\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"frameBorder=\"0\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; fullscreen; gyroscope; picture-in-picture; web-share\"","level":0},{"type":"softbreak","level":0},{"type":"text","content":"allowfullscreen","level":0}]},{"type":"paragraph_close","tight":false,"level":0},{"type":"blockquote_open","lines":[331,333],"level":0},{"type":"paragraph_open","tight":false,"lines":[331,333],"level":1},{"type":"inline","content":"\u003c/iframe\u003e\n\u003c/div\u003e","level":2,"lines":[331,333],"children":[{"type":"text","content":"\u003c/iframe\u003e","level":0},{"type":"softbreak","level":0},{"type":"text","content":"\u003c/div\u003e","level":0}]},{"type":"paragraph_close","tight":false,"level":1},{"type":"blockquote_close","level":0}],"content":"- [What is Supavisor?](#what-is-supavisor)\n- [Benchmarking 1 million connections](#benchmarking-1-million-connections)\n  * [Setup](#setup)\n  * [Establishing a baseline](#establishing-a-baseline)\n  * [Supavisor's scaling capabilities](#supavisors-scaling-capabilities)\n  * [Scaling to 1,000,000 connections](#scaling-to-1000000-connections)\n  * [Supavisor's impact on query duration](#supavisors-impact-on-query-duration)\n  * [Supavisor on Supabase Platform](#supavisor-on-supabase-platform)\n  * [Getting started](#getting-started)\n- [Conclusion](#conclusion)"}}},"__N_SSG":true},"page":"/blog/[slug]","query":{"slug":"supavisor-1-million"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>