<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="rss.xml" data-next-head=""/><link rel="manifest" href="favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:url" content="https://supabase.com/" data-next-head=""/><meta property="og:type" content="website" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/og/supabase-og.png" data-next-head=""/><meta property="og:image:alt" content="Supabase Og Image" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><title data-next-head="">Service Level Agreement | Supabase</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Supabase Service Level Agreement" data-next-head=""/><meta property="og:title" content="Service Level Agreement | Supabase" data-next-head=""/><meta property="og:description" content="Supabase Service Level Agreement" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/c912681474966412.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6151-30d4d60b3cdda3e3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4276-684705cd6a7088f5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2113-a2d1d7666c8141d8.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3969-0619f90fcae38481.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/5209-8452647d2f8ab7fa.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3642-2271933ec388713a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6775-68f86226495a6581.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/4313-cf0e62df457f85d5.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/sla-9ebd93776ec8d050.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:Ramila6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="_next/supabase-logo-wordmark--light.png 1x, _next/supabase-logo-wordmark--light.png 2x" src="_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="_next/supabase-logo-wordmark--dark.png 1x, _next/supabase-logo-wordmark--dark.png 2x" src="_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:Rimila6:-trigger-radix-:Rbimila6:" data-state="closed" aria-expanded="false" aria-controls="radix-:Rimila6:-content-radix-:Rbimila6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:Rimila6:-trigger-radix-:Rjimila6:" data-state="closed" aria-expanded="false" aria-controls="radix-:Rimila6:-content-radix-:Rjimila6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:Rimila6:-trigger-radix-:Rrimila6:" data-state="closed" aria-expanded="false" aria-controls="radix-:Rimila6:-content-radix-:Rrimila6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav></div><main class="relative min-h-screen"><div class="prose max-w-none"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20"><h1 id="service-level-agreements" class="group scroll-mt-24">Service Level Agreements<a href="#service-level-agreements" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h1><h2 id="enterprise-platform-uptime-sla" class="group scroll-mt-24">Enterprise Platform Uptime SLA<a href="#enterprise-platform-uptime-sla" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2><p>The following Service Level Agreement, which is incorporated into and forms part of the Subscription Agreement between Supabase, Inc. (&quot;Supabase&quot;) and Customer (the &quot;Agreement&quot;), will apply to the Services for Enterprise Customers specified in an Order Form during the applicable Subscription Term.</p><hr/><h2 id="1-definitions" class="group scroll-mt-24">1. Definitions<a href="#1-definitions" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2><p>All capitalized terms used but not defined in this SLA have the meaning set forth in the Agreement.</p><p><strong>Availability Metrics:</strong></p><p>Defines the measurements used to calculate service uptime under this SLA.</p><ul>
<li>
<p><strong>Scheduled Availability:</strong></p>
<p>The total time (in minutes) that the applicable service is generally accessible and available to permitted users.</p>
</li>
<li>
<p><strong>Scheduled Downtime/Maintenance Windows:</strong></p>
<p>Periods of time that Supabase has communicated in advance, during which the service may be temporarily unavailable due to planned maintenance, upgrades, or other scheduled activities. These windows are not counted as Unscheduled Downtime for SLA purposes.</p>
</li>
<li>
<p><strong>Unscheduled Downtime:</strong></p>
<p>The total time (in minutes) that the service is not accessible or available, excluding periods attributable to any causes listed under SLA Exclusions.</p>
</li>
<li>
<p><strong>Actual Availability:</strong></p>
<p>The result of subtracting Unscheduled Downtime from Scheduled Availability.</p>
</li>
</ul><p><strong>Release Maturity Levels:</strong> Indicates the stage of release for a given product or feature and whether it is covered by the SLA.</p><ul>
<li><strong>GA (General Availability):</strong> The product is fully released and covered by the SLA.</li>
<li><strong>Beta:</strong> The product or feature is in limited release. Beta products are not covered by the SLA.</li>
<li><strong>Alpha:</strong> The product or feature is in early release. Alpha products are not covered by the SLA.</li>
</ul><p>For the current release stage of each Supabase product and feature see <a href="features.html">Supabase features</a>.</p><p><strong>SLA Scope:</strong> The level at which service availability is measured and the impact required to constitute an SLA breach.</p><ul>
<li><strong>Global:</strong> The service is deployed from a centralized global infrastructure; SLA is breached if more than 1% of projects worldwide are affected during a downtime event.</li>
<li><strong>Regional:</strong> The service is deployed within individual geographic regions; SLA is breached if more than 1% of projects in a single region are affected.</li>
<li><strong>Project:</strong> The service is deployed on a dedicated, per-project basis; SLA applies individually to each project.</li>
</ul><hr/><h2 id="2-uptime-commitment" class="group scroll-mt-24">2. Uptime Commitment<a href="#2-uptime-commitment" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2><p>Supabase will provide Actual Availability for at least ninety-nine and nine tenths percent (99.9%) of the total time in each calendar month during the Subscription Term, as measured by Supabase (the &quot;Uptime Commitment&quot;). Each product is individually covered by a 99.9% uptime commitment for customers with an Enterprise tier subscription.</p><hr/><h2 id="3-sla-definition--exclusions" class="group scroll-mt-24">3. SLA Definition &amp; Exclusions<a href="#3-sla-definition--exclusions" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2><p>This Service Level Agreement applies only to the services and products specifically listed as covered, and is subject to the definitions, scopes, and exclusions outlined in this document.</p><p>Supabase is not responsible for outages or service interruptions caused by factors outside of its reasonable control. The following categories of events are excluded from this SLA:</p><ul>
<li>
<p><strong>Third-Party Vendors:</strong></p>
<p>Issues attributable to external vendors or cloud providers, including AWS, Cloudflare, GCP, Azure, GitHub, or other similar providers.</p>
</li>
<li>
<p><strong>Integration Partners:</strong></p>
<p>Failures or downtime related to third-party integration partners, such as Resend for email delivery, or other external service failures.</p>
</li>
<li>
<p><strong>General Factors Outside Our Control:</strong></p>
<p>Events such as force majeure, internet service provider (ISP) outages, or other issues outside Supabase&#x27;s reasonable control.</p>
</li>
<li>
<p><strong>Customer Actions or Inactions:</strong></p>
<p>Resource limitations, misconfigurations, or failures to follow operational guidelines provided in Supabase documentation; delays in recovery due to insufficient I/O capacity; issues caused by customer&#x27;s equipment or software; or account suspension or termination in accordance with Supabase Terms.</p>
</li>
</ul><p>Product-specific exclusions and further detail are provided in the following sections.</p><hr/><h3 id="product-specific" class="group scroll-mt-24">Product-Specific<a href="#product-specific" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3><h3 id="postgres" class="group scroll-mt-24">Postgres<a href="#postgres" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3><ul>
<li><strong>SLA Scope:</strong> Project</li>
<li><strong>Dependencies:</strong> None</li>
</ul><p><strong>Downtime Definition:</strong></p><p>Any period during which the managed Postgres database for a given project is not generally accessible for permitted users to perform read or write operations.</p><p><strong>Exclusions:</strong></p><ul>
<li>Use of user-defined, unofficial, or unsupported Postgres extensions.</li>
<li>Use of Postgres versions older than the two most recent major releases officially supported by Supabase.</li>
<li>Use of outdated database extension versions; customers must be running the most recent version of database extensions for those to be included in SLA coverage.</li>
<li>Customer&#x27;s failure to provision sufficient CPU, memory, or storage resources for expected workloads.</li>
<li>Excessively large numbers of tables or objects that significantly impact recovery times.</li>
<li>Insufficient I/O capacity for the database workload as provisioned by the customer.</li>
<li>Outages caused by customer-initiated schema changes or migrations that impact database integrity or operability.</li>
<li>Issues caused by customer&#x27;s equipment, networks, or software.</li>
<li>Downtime related to suspension or termination of account per Supabase Terms.</li>
</ul><hr/><h3 id="auth" class="group scroll-mt-24">Auth<a href="#auth" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3><ul>
<li><strong>SLA Scope:</strong> Project</li>
<li><strong>Dependencies:</strong> Postgres</li>
</ul><p><strong>Downtime Definition:</strong></p><p>Any period during which the Auth service is unavailable for performing authentication or authorization operations for permitted users of a production system.</p><p><strong>Exclusions:</strong></p><ul>
<li>Unavailability caused by upstream service outages listed in Dependencies.</li>
<li>Inappropriately provisioned compute resources for anticipated auth workloads.</li>
<li>Customer-initiated modifications to database objects, roles, or relationships in the <code class="short-inline-codeblock">auth</code> schema.</li>
<li>Outages resulting from integration with third-party providers (OAuth, OpenID, email, SMS, CAPTCHA, password strength checking, geolocation, etc.).</li>
<li>Outages due to overly permissive rate-limiting configurations set by the customer.</li>
<li>Email sending issues when using the default (provisional) configuration <a href="docs/guides/auth/auth-smtp.html">not intended for production use</a>.</li>
<li>Issues caused by using retracted or unofficial Supabase libraries, frameworks, or proxies.</li>
<li>Issues that would have been resolved by upgrading to a newer minor or patch version of official Supabase libraries or tools.</li>
</ul><hr/><h3 id="data-apis-postgrest" class="group scroll-mt-24">Data APIs (PostgREST)<a href="#data-apis-postgrest" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3><ul>
<li><strong>SLA Scope:</strong> Project</li>
<li><strong>Dependencies:</strong> Postgres, Auth</li>
</ul><p><strong>Downtime Definition:</strong></p><p>Any period during which the Data APIs (including PostgREST endpoints) are unavailable for permitted users to perform API calls against the database.</p><p><strong>Exclusions:</strong></p><ul>
<li>Unavailability caused by upstream service outages listed in Dependencies.</li>
<li>Customer misconfiguration of API permissions, security policies, or database schema.</li>
<li>Use of unofficial or unsupported client libraries, API versions, or modifications.</li>
<li>Failures resulting from customer&#x27;s network, application, or API client errors.</li>
<li>Outages that could have been resolved by upgrading to the latest supported version of Supabase Data API components.</li>
</ul><hr/><h3 id="storage" class="group scroll-mt-24">Storage<a href="#storage" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3><ul>
<li><strong>SLA Scope:</strong> Regional</li>
<li><strong>Dependencies:</strong> Postgres, Pooler</li>
</ul><p><strong>Downtime Definition:</strong></p><p>Any period during which the Storage service is unavailable for permitted users to upload, download, or manage files and buckets in a region.</p><p><strong>Exclusions:</strong></p><ul>
<li>Unavailability caused by upstream service outages listed in Dependencies.</li>
<li>Customer misconfiguration of storage settings or connection pools (e.g., low <code class="short-inline-codeblock">max_clients</code> or <code class="short-inline-codeblock">pool_size</code>).</li>
<li>Use of unofficial or unsupported client libraries or modifications.</li>
<li>Customer-initiated schema changes in the storage schema or cross-schema relationships impacting availability.</li>
<li>Deletion of objects or buckets by the customer via the Storage API.</li>
<li>Outages that could have been resolved by upgrading to the latest supported version of Supabase Storage components.</li>
</ul><hr/><h3 id="pooler-pgbouncer--supavisor" class="group scroll-mt-24">Pooler (PgBouncer &amp; Supavisor)<a href="#pooler-pgbouncer--supavisor" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3><ul>
<li><strong>SLA Scope:</strong> Regional</li>
<li><strong>Dependencies:</strong> Postgres</li>
</ul><p><strong>Downtime Definition:</strong></p><p>Any period during which the database connection pooling layer is unavailable for permitted users, resulting in inability to connect to Postgres.</p><p><strong>Exclusions:</strong></p><ul>
<li>Unavailability caused by upstream service outages listed in Dependencies.</li>
<li>Customer&#x27;s failure to provision sufficient pooler capacity (e.g., <code class="short-inline-codeblock">max_clients</code>, <code class="short-inline-codeblock">pool_size</code>) for actual workload.</li>
<li>Custom changes to connection pooling settings outside recommended operational guidelines.</li>
<li>Issues arising from customer&#x27;s network or database client configuration.</li>
<li>Failures resolvable by updating to a supported version of official Supabase pooling components.</li>
</ul><hr/><h3 id="management-api" class="group scroll-mt-24">Management API<a href="#management-api" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3><ul>
<li><strong>SLA Scope:</strong> Regional</li>
<li><strong>Dependencies:</strong> None</li>
</ul><p><strong>Downtime Definition:</strong></p><p>Any period during which the Supabase Management API is unavailable for permitted users to perform management, provisioning, or configuration actions in a region.</p><p><strong>Exclusions:</strong></p><ul>
<li>Customer loss or compromise of personal access tokens or confidential information.</li>
<li>Use of the Management API in violation of Supabase fair-use policy.</li>
<li>Failures that could have been resolved by upgrading to a newer version of official Supabase management tooling.</li>
</ul><hr/><h3 id="branching" class="group scroll-mt-24">Branching<a href="#branching" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3><ul>
<li><strong>SLA Scope:</strong> Regional</li>
<li><strong>Dependencies:</strong> Postgres, Management API</li>
</ul><p><strong>Downtime Definition:</strong></p><p>Any period during which the branching functionality (including creation, deletion, or promotion of branches) is unavailable for permitted users within a given region.</p><p><strong>Exclusions:</strong></p><ul>
<li>Unavailability caused by upstream service outages listed under Dependencies.</li>
<li>Failures due to unsupported schema or configurations within branches.</li>
<li>Customer misuse or unsupported use of branching features, including but not limited to version pinning, manual overrides, or undocumented patterns.</li>
<li>Issues resulting from user-initiated migrations that introduce data loss or instability, including those merged into production environments.</li>
<li>Failures in applying configuration or updates older than 90 days.</li>
<li>Failures in applying configuration or service updates (e.g., Auth settings) to branches with stale or diverged states.</li>
</ul><hr/><h3 id="realtime" class="group scroll-mt-24">Realtime<a href="#realtime" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3><ul>
<li><strong>SLA Scope:</strong> Regional</li>
<li><strong>Dependencies:</strong> Postgres, Auth</li>
</ul><p><strong>Downtime Definition:</strong></p><p>Any period during which the Realtime service is unavailable for permitted users to send and receive event notifications or subscribe to database changes in a region.</p><p><strong>Exclusions:</strong></p><ul>
<li>Unavailability caused by upstream service outages listed in Dependencies.</li>
<li>Customer&#x27;s failure to provision sufficient compute resources for Realtime workloads.</li>
<li>The Realtime service does not guarantee delivery of messages (at-least-once, exactly-once, or at-most-once delivery).</li>
<li>Issues resulting from the use of unofficial, outdated, or unsupported client libraries or event-handling frameworks.</li>
</ul><hr/><h3 id="functions" class="group scroll-mt-24">Functions<a href="#functions" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3><ul>
<li><strong>SLA Scope:</strong> Regional</li>
<li><strong>Dependencies:</strong> None</li>
</ul><p><strong>Downtime Definition:</strong></p><p>Any period during which Supabase Edge Functions are unavailable to be executed, created, updated, or deleted by permitted users in a region.</p><p><strong>Exclusions:</strong></p><ul>
<li>Outages caused by user code errors, infinite loops, or unsupported packages.</li>
<li>Failures due to integration with external dependencies or services.</li>
<li>Failures resulting from downstream dependencies explicitly invoked by the user within their function logic (e.g., Postgres queries, HTTP requests to PostgREST, Auth, Storage, or third-party services). These are considered outside the scope of the Functions SLA.</li>
</ul><hr/><h3 id="studio" class="group scroll-mt-24">Studio<a href="#studio" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3><ul>
<li><strong>SLA Scope:</strong> Global</li>
<li><strong>Dependencies:</strong> Postgres, Management API, Logging, Auth, Realtime, Functions, Storage</li>
</ul><p><strong>Downtime Definition:</strong></p><p>Any period during which Supabase Studio is unavailable for permitted users to manage projects, view logs, or interact with platform resources globally.</p><p><strong>Exclusions:</strong></p><ul>
<li>Unavailability caused by upstream service outages listed in Dependencies.</li>
<li>Failures caused by unsupported browser versions or extensions.</li>
</ul><hr/><h3 id="logging" class="group scroll-mt-24">Logging<a href="#logging" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3><ul>
<li><strong>SLA Scope:</strong> Global</li>
<li><strong>Dependencies:</strong> None</li>
</ul><p><strong>Downtime Definition:</strong></p><p>Any period during which the Logging service is unavailable for permitted users to collect, query, or retrieve log data globally.</p><p><strong>Exclusions:</strong></p><ul>
<li>Integration failures with external log ingestion partners or third-party tools.</li>
<li>Outages resulting from customer misconfiguration of log collection or retention policies.</li>
</ul><hr/><h2 id="4-service-credits" class="group scroll-mt-24">4. Service Credits<a href="#4-service-credits" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2><p>If the Uptime Commitment is not met during any particular calendar month, Customer is eligible for a service credit (&quot;Service Credit&quot;) upon request. The amount will be:</p><p><code class="short-inline-codeblock">&lt;Total Monthly Fees for Affected Service&gt; * &lt;Credit Percentage&gt;</code></p><p>where Credit Percentage is derived from the table below:</p><table><thead><tr><th>Actual Availability</th><th>Credit Percentage</th></tr></thead><tbody><tr><td>Less than 99.9% but greater than or equal to 99.0%</td><td>10%</td></tr><tr><td>Less than 99.0% but greater than or equal to 98.0%</td><td>15%</td></tr><tr><td>Less than 98.0% but greater than or equal to 96.0%</td><td>20%</td></tr><tr><td>Less than 96.0%</td><td>30%</td></tr></tbody></table><hr/><h2 id="5-credit-requests-and-payment" class="group scroll-mt-24">5. Credit Requests and Payment<a href="#5-credit-requests-and-payment" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2><p>To request a Service Credit, Customer must send an email to Supabase at <a href="mailto:<EMAIL>"><EMAIL></a> within thirty (30) days of the end of the month in which the Uptime Commitment was not met. The request must include:</p><p>(a) the affected organization, service(s), region(s), and project(s);</p><p>(b) the specific dates and times (in 5-minute intervals) during which the service was unavailable; and</p><p>(c) supporting logs or monitoring data showing failed requests or clear unavailability.</p><p>Supabase reserves the right to validate any claim using its own internal monitoring systems and may deny claims that are unsupported, inaccurate, or inconsistent with internal metrics.</p><p>If Supabase confirms that Customer is eligible for a Service Credit, Supabase will issue a credit to Customer&#x27;s account within thirty (30) days. Service Credits are not refunds, cannot be exchanged into cash, and may only be applied to future billing charges. Except as set forth in Section 6 below, the Service Credits constitute Customer&#x27;s sole and exclusive remedy, and Supabase&#x27;s sole and exclusive liability, for any failure to meet the Uptime Commitment.</p><p>Notwithstanding anything to the contrary in this SLA, the total amount of Service Credits issued to Customer under this SLA shall not exceed twenty percent (20%) of the total fees paid by Customer for the affected services under the applicable Order Form during the preceding twelve (12) month period. Any Service Credits calculated in excess of this cap will be forfeited and shall have no cash or credit value.</p><hr/><h2 id="7-support" class="group scroll-mt-24">7. Support<a href="#7-support" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h2><p>Supabase provides Support Service Level Agreements for Team and Enterprise customers.</p><h3 id="urgent" class="group scroll-mt-24">Urgent<a href="#urgent" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3><p><strong>Critical Issue</strong></p><p>Defect resulting in full or partial system outage or a condition that makes Supabase unusable or unavailable in production for all of Customer&#x27;s Users.</p><h3 id="high" class="group scroll-mt-24">High<a href="#high" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3><p><strong>Significant Business Disruption</strong></p><p>Issue resulting in a situation meaning major functionality is impacted and significant performance degradation is experienced. Issue impacts significant proportion of user base and / or major Supabase functionality.</p><h3 id="normal" class="group scroll-mt-24">Normal<a href="#normal" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3><p><strong>Minor Feature or Functional Issue / General Question</strong></p><p>Issue results in a component of Supabase not performing as expected or documented. An inquiry by a Customer representative regarding a general technical issue or general question.</p><h3 id="low" class="group scroll-mt-24">Low<a href="#low" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3><p><strong>Minor Issue / Feature Request</strong></p><p>An Information request about Supabase or feature request.</p><h3 id="severity-and-target-initial-response-times" class="group scroll-mt-24">Severity and Target Initial Response Times<a href="#severity-and-target-initial-response-times" aria-hidden="true" class="ml-2 opacity-0 group-hover:opacity-100 transition"><span aria-hidden="true">#</span></a></h3><table><thead><tr><th>Severity Level</th><th>Team</th><th>Enterprise Standard</th><th>Priority Plus</th></tr></thead><tbody><tr><td>1. Urgent</td><td>24 hours<br/>24/7 × 365</td><td>1 hour<br/>24/7 × 365</td><td>1 hour<br/>24/7 × 365</td></tr><tr><td>2. High</td><td>1 business day<br/>Monday - Friday</td><td>2 business hours<br/>Monday - Friday</td><td>2 hours<br/>24/7 × 365</td></tr><tr><td>3. Normal</td><td>1 business day<br/>Monday - Friday</td><td>1 business day<br/>Monday - Friday</td><td>12 hours<br/>24/7 x 365</td></tr><tr><td>4. Low</td><td>2 business days<br/>Monday - Friday</td><td>2 business days<br/>Monday - Friday</td><td>24 hours<br/>24/7 x 365</td></tr></tbody></table><p>Business hours are 6am to 6pm local time unless stated otherwise.</p></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="_next/supabase-logo-wordmark--light.png 1x, _next/supabase-logo-wordmark--light.png 2x" src="_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="_next/supabase-logo-wordmark--dark.png 1x, _next/supabase-logo-wordmark--dark.png 2x" src="_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{}},"page":"/sla","query":{},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","nextExport":true,"autoExport":true,"isFallback":false,"scriptLoader":[]}</script></body></html>