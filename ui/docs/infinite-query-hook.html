<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="https://supabase.com/ui/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" type="font/woff2"/><link rel="stylesheet" href="../_next/static/css/03f63ad6e897ae58.css" data-precedence="next"/><link rel="stylesheet" href="../_next/static/css/954cd54edf7b3efb.css" data-precedence="next"/><link rel="stylesheet" href="../_next/static/css/be16f9d4f36b2c7a.css" data-precedence="next"/><link rel="stylesheet" href="../_next/static/css/99f16e312f80aa1b.css" data-precedence="next"/><link rel="stylesheet" href="https://supabase.com/ui/_next/static/css/f12065469b9a7bd1.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="../_next/static/chunks/webpack-e33ce3369a09e8d8.js"/><script src="../_next/static/chunks/1c2ff98c-7d3bc7c78beee601.js" async=""></script><script src="../_next/static/chunks/2053-b9dd66de32bef85e.js" async=""></script><script src="../_next/static/chunks/main-app-acf3b350459af11a.js" async=""></script><script src="../_next/static/chunks/6985-d9c94a68747ee58b.js" async=""></script><script src="../_next/static/chunks/2105-5c6fb0a066c1340f.js" async=""></script><script src="../_next/static/chunks/3502-8ffc67f87c6f59da.js" async=""></script><script src="../_next/static/chunks/6276-e32f7d13c5c52f3b.js" async=""></script><script src="../_next/static/chunks/6701-6b908838eebce949.js" async=""></script><script src="../_next/static/chunks/6602-18415a95b5bdd8fe.js" async=""></script><script src="../_next/static/chunks/5710-2cb44b983b4cfc0c.js" async=""></script><script src="../_next/static/chunks/8443-3b56b7405ddfa048.js" async=""></script><script src="../_next/static/chunks/494-582b02a90a51684d.js" async=""></script><script src="../_next/static/chunks/app/(app)/layout-68e9ee4f0ef3c93b.js" async=""></script><script src="../_next/static/chunks/3276-a6118a55186eb94a.js" async=""></script><script src="../_next/static/chunks/898-7244b379490d4af4.js" async=""></script><script src="https://supabase.com/ui/_next/static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3" async=""></script><script src="../_next/static/chunks/8815-1dd29c457db8514c.js" async=""></script><script src="https://supabase.com/ui/_next/static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3" async=""></script><meta name="next-size-adjust" content=""/><title>Infinite Query Hook</title><meta name="description" content="React hook for infinite lists, fetching data from Supabase."/><meta name="application-name" content="Supabase UI Library"/><meta property="og:title" content="Infinite Query Hook"/><meta property="og:description" content="React hook for infinite lists, fetching data from Supabase."/><meta property="og:url" content="https://supabase.com/undefined/docs/infinite-query-hook"/><meta property="og:image" content="https://supabase.com/ui/img/supabase-og-image.png"/><meta property="og:type" content="article"/><meta property="article:published_time" content="2025-07-30T14:07:09.974Z"/><meta property="article:modified_time" content="2025-07-30T14:07:09.974Z"/><meta property="article:author" content="Supabase"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:site" content="@supabase"/><meta name="twitter:creator" content="@supabase"/><meta name="twitter:title" content="Infinite Query Hook"/><meta name="twitter:description" content="React hook for infinite lists, fetching data from Supabase."/><meta name="twitter:image" content="https://supabase.com/ui/img/supabase-og-image.png"/><link rel="shortcut icon" href="../favicon/favicon.ico"/><link rel="icon" href="../favicon/favicon.ico" type="image/x-icon"/><link rel="apple-touch-icon" href="../favicon/favicon.ico"/><link rel="apple-touch-icon-precomposed" href="../favicon/apple-icon-57x57.png" sizes="57x57"/><link rel="apple-touch-icon-precomposed" href="../favicon/apple-icon-60x60.png" sizes="60x60"/><link rel="apple-touch-icon-precomposed" href="../favicon/apple-icon-72x72.png" sizes="72x72"/><link rel="apple-touch-icon-precomposed" href="../favicon/apple-icon-76x76.png" sizes="76x76"/><link rel="apple-touch-icon-precomposed" href="../favicon/apple-icon-114x114.png" sizes="114x114"/><link rel="apple-touch-icon-precomposed" href="../favicon/apple-icon-120x120.png" sizes="120x120"/><link rel="apple-touch-icon-precomposed" href="../favicon/apple-icon-144x144.png" sizes="144x144"/><link rel="apple-touch-icon-precomposed" href="../favicon/apple-icon-152x152.png" sizes="152x152"/><link rel="icon" href="../favicon/favicon-16x16.png" type="image/png" sizes="16x16"/><link rel="icon" href="../favicon/favicon-32x32.png" type="image/png" sizes="32x32"/><link rel="icon" href="../favicon/favicon-48x48.png" type="image/png" sizes="48x48"/><link rel="icon" href="../favicon/favicon-96x96.png" type="image/png" sizes="96x96"/><link rel="icon" href="https://supabase.com/ui/favicon/favicon-128x128.png" type="image/png" sizes="128x128"/><link rel="icon" href="../favicon/favicon-180x180.png" type="image/png" sizes="180x180"/><link rel="icon" href="../favicon/favicon-196x196.png" type="image/png" sizes="196x196"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="../_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_cbad29 antialiased"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&true)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}if(e==='light'||e==='dark')d.style.colorScheme=e}catch(e){}}()</script><div class="pt-10 md:pt-0"><main class="flex-1 max-w-site mx-auto w-full p-0"><div class="border-b"><div class="flex-1 items-start md:grid md:grid-cols-[220px_minmax(0,1fr)] lg:grid-cols-[240px_minmax(0,1fr)]"><div class="md:hidden fixed top-0 left-0 right-0 z-50 bg-background justify-between flex items-center px-8 py-3 border-b"><button data-size="tiny" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-«R2lmqlb»" data-state="closed" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-transparent border-strong hover:border-foreground-muted focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-xs px-2.5 py-1 h-[26px]"><div class="[&amp;_svg]:h-[14px] [&amp;_svg]:w-[14px] text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></div> <!-- --> </button></div><aside class="fixed z-30 top-0 hidden h-screen w-full shrink-0 md:sticky md:block bg-200 border-r border-muted/50"><div dir="ltr" class="relative overflow-hidden h-full" style="position:relative;--radix-scroll-area-corner-width:0px;--radix-scroll-area-corner-height:0px"><style>[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}</style><div data-radix-scroll-area-viewport="" class="h-full w-full rounded-[inherit]" style="overflow-x:hidden;overflow-y:hidden"><div style="min-width:100%;display:table"><nav class="flex flex-col h-full min-w-[220px]"><div class="p-6"><div class="flex items-start justify-between mb-4"><a href="../../ui.html"><svg xmlns="http://www.w3.org/2000/svg" width="109" height="113" viewBox="0 0 109 113" fill="none" class="w-6 h-6"><path d="M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0625L99.1935 40.0625C107.384 40.0625 111.952 49.5226 106.859 55.9372L63.7076 110.284Z" fill="url(#paint0_linear)"></path><path d="M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0625L99.1935 40.0625C107.384 40.0625 111.952 49.5226 106.859 55.9372L63.7076 110.284Z" fill="url(#paint1_linear)" fill-opacity="0.2"></path><path d="M45.317 2.07103C48.1765 -1.53037 53.9745 0.442937 54.0434 5.041L54.4849 72.2922H9.83113C1.64038 72.2922 -2.92775 62.8321 2.1655 56.4175L45.317 2.07103Z" fill="#3ECF8E"></path><defs><linearGradient id="paint0_linear" x1="53.9738" y1="54.9738" x2="94.1635" y2="71.8293" gradientUnits="userSpaceOnUse"><stop stop-color="#249361"></stop><stop offset="1" stop-color="#3ECF8E"></stop></linearGradient><linearGradient id="paint1_linear" x1="36.1558" y1="30.5779" x2="54.4844" y2="65.0804" gradientUnits="userSpaceOnUse"><stop></stop><stop offset="1" stop-opacity="0"></stop></linearGradient></defs></svg></a></div><a class="mb-4 block" href="../../ui.html"><h1>Supabase UI Library</h1></a><button data-size="tiny" type="button" class="cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border border-strong focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong px-2.5 py-1 relative h-8 w-full justify-start rounded-[0.5rem] bg-background text-sm font-normal text-foreground-muted shadow-none sm:pr-12 hover:border-foreground-muted hover:bg-surface-100 hover:text-foreground-lighter"> <span class="truncate"><span class="hidden lg:inline-flex">Search UI Library...</span><span class="inline-flex lg:hidden">Search...</span><kbd class="pointer-events-none absolute right-[0.3rem] top-[0.3rem] hidden h-5 select-none items-center gap-1 rounded border bg-surface-200 px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex text-foreground-light"><span class="text-sm">⌘</span>K</kbd></span> </button></div><div class="pb-6 space-y-0.5"><div class="font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest">Getting Started</div><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="getting-started/introduction.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Introduction</a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="getting-started/quickstart.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Quick Start</a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="getting-started/faq.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>FAQ</a></div><div class="pb-6"><div class="font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest">Blocks</div><div class="space-y-0.5"><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="nextjs/client.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Client</a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="nextjs/password-based-auth.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Password-Based Auth</a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="nextjs/social-auth.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Social Auth<div class="inline-flex items-center rounded-full bg-opacity-10 bg-brand text-brand-600 border border-brand-500 px-2.5 py-0.5 text-xs capitalize">NEW</div></a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="nextjs/dropzone.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Dropzone</a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="nextjs/realtime-cursor.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Realtime Cursor</a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="nextjs/current-user-avatar.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Current User Avatar</a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="nextjs/realtime-avatar-stack.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Realtime Avatar Stack</a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="nextjs/realtime-chat.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Realtime Chat</a><a class="relative flex items-center justify-between h-6 text-sm px-6 bg-surface-200 text-foreground transition-all" href="infinite-query-hook.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-100"></div>Infinite Query Hook<div class="inline-flex items-center rounded-full bg-opacity-10 bg-brand text-brand-600 border border-brand-500 px-2.5 py-0.5 text-xs capitalize">NEW</div></a></div></div><div class="pb-6 flex-1"><div class="font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest">AI Editors Rules</div><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="https://supabase.com/ui/docs/ai-editors-rules/prompts"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Prompts</a></div><div class="pb-6"><div class="font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest">Platform</div><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="https://supabase.com/ui/docs/platform/platform-kit"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Platform Kit</a></div></nav></div></div></div></aside><div vaul-drawer-wrapper=""><div class="relative flex min-h-screen flex-col bg-background"><main class="relative lg:gap-10 xl:grid xl:grid-cols-[1fr_200px] px-8 md:px-16 py-20"><div class="mx-auto w-full min-w-0 max-w-4xl"><div class="mb-4 flex items-center space-x-1 text-sm text-foreground-muted"><div class="overflow-hidden text-ellipsis whitespace-nowrap">Docs</div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right h-4 w-4 text-foreground-muted"><path d="m9 18 6-6-6-6"></path></svg><div class="text-foreground-lighter">Infinite Query Hook</div></div><div class="flex flex-col lg:flex-row lg:items-end justify-between mb-5"><div class="space-y-2"><h1 class="scroll-m-20 text-2xl lg:text-4xl tracking-tight">Infinite Query Hook</h1><p class="text-base lg:text-lg text-foreground-light"><span data-br="«R154utpmqlb»" data-brr="1" style="display:inline-block;vertical-align:top;text-decoration:inherit;text-wrap:balance">React hook for infinite lists, fetching data from Supabase.</span><script>self.__wrap_n=self.__wrap_n||(self.CSS&&CSS.supports("text-wrap","balance")?1:2);self.__wrap_b=(e,n,t)=>{let o=(t=t||document.querySelector(`[data-br="${e}"]`)).parentElement,r=e=>t.style.maxWidth=e+"px";t.style.maxWidth="";let s=o.clientWidth,a=o.clientHeight,c=s/2-.25,l=s+.5,i;if(s){for(r(c),c=Math.max(t.scrollWidth,c);c+1<l;)r(i=Math.round((c+l)/2)),o.clientHeight===a?l=i:c=i;r(l*n+s*(1-n))}t.__wrap_o||"undefined"!=typeof ResizeObserver&&(t.__wrap_o=new ResizeObserver(()=>{self.__wrap_b(0,+t.dataset.brr,t)})).observe(o)};self.__wrap_n!=1&&self.__wrap_b("«R154utpmqlb»",1)</script></p></div></div><div class="flex flex-col -space-y-px"></div><div class="pb-12"><div class="mdx"><div class="mt-4 w-full"><div class="relative border rounded-lg overflow-hidden bg-muted min-h-[150px] h-[600px]"><div class="w-full h-full flex items-center justify-center"><!--$--><iframe src="https://supabase.com/ui/example/infinite-list-demo" style="border:none;width:100%;height:100%;display:block" name="preview-frame"></iframe><!--/$--></div></div></div>
<h2 class="font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0" id="installation"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#installation"><span class="icon icon-link"></span></a>Installation</h2>
<div class="mt-4"><!--$!--><template data-dgst="BAILOUT_TO_CLIENT_SIDE_RENDERING"></template><!--/$--><a href="https://v0.dev/chat/api/open?url=https://supabase.com/ui/r/infinite-query-hook.json" target="_blank" rel="noreferrer" class="inline-flex items-center justify-center font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-foreground-muted focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 py-2 h-7 gap-1 rounded-lg shadow-none bg-black px-3 text-xs text-white hover:bg-black hover:text-white dark:bg-white dark:text-black w-fit shrink-0 mt-4" aria-label="Open in v0">Open in<!-- --> <svg viewBox="0 0 40 20" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-current"><path d="M23.3919 0H32.9188C36.7819 0 39.9136 3.13165 39.9136 6.99475V16.0805H36.0006V6.99475C36.0006 6.90167 35.9969 6.80925 35.9898 6.71766L26.4628 16.079C26.4949 16.08 26.5272 16.0805 26.5595 16.0805H36.0006V19.7762H26.5595C22.6964 19.7762 19.4788 16.6139 19.4788 12.7508V3.68923H23.3919V12.7508C23.3919 12.9253 23.4054 13.0977 23.4316 13.2668L33.1682 3.6995C33.0861 3.6927 33.003 3.68923 32.9188 3.68923H23.3919V0Z" fill="currentColor"></path><path d="M13.7688 19.0956L0 3.68759H5.53933L13.6231 12.7337V3.68759H17.7535V17.5746C17.7535 19.6705 15.1654 20.6584 13.7688 19.0956Z" fill="currentColor"></path></svg></a></div>
<h2 class="font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0" id="folder-structure"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#folder-structure"><span class="icon icon-link"></span></a>Folder structure</h2>
<div class="flex mt-4 border rounded-lg overflow-hidden h-[652px] not-prose"><div class="w-64 grow-0 shrink-0 flex-0 py-2 border-r bg-muted/30 overflow-y-auto"><ul class="tree w-full" role="tree" aria-multiselectable="false" aria-label="file browser"><li role="treeitem" aria-expanded="true" aria-setsize="1" aria-posinset="1" aria-level="1" aria-disabled="false" tabindex="0" class="tree-branch-wrapper"><div class="group relative transition-colors h-[28px] flex items-center text-sm cursor-pointer select-none text-foreground-light hover:bg-control aria-expanded:bg-transparent data-[state=open]:bg-transparent gap-1.5" aria-selected="false" aria-expanded="true" style="padding-left:16px" data-treeview-is-branch="true" data-treeview-level="1"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right text-foreground-muted group-aria-selected:text-foreground-light group-aria-expanded:text-foreground-light transition-transform duration-200 group-aria-expanded:rotate-90"><path d="m9 18 6-6-6-6"></path></svg><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-folder-open transition-colors text-foreground-muted group-aria-selected:text-foreground-light group-aria-expanded:text-foreground-light"><path d="m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2"></path></svg><span class="truncate text-sm" title="hooks">hooks</span><form class="hidden"><input autofocus="" class="rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive block w-full text-sm px-2 py-1 h-7" value="hooks"/></form></div><ul role="group" class="tree-node-group tree-node-group--expanded"><li role="none" class="tree-leaf-list-item tree-leaf-list-item--selected"><div role="treeitem" tabindex="-1" class="group relative transition-colors h-[28px] flex items-center text-sm cursor-pointer select-none hover:bg-control aria-expanded:bg-transparent data-[state=open]:bg-transparent text-foreground !bg-selection gap-1.5" aria-setsize="1" aria-posinset="1" aria-level="2" aria-disabled="false" aria-selected="true" aria-expanded="false" style="padding-left:35px" data-treeview-is-branch="false" data-treeview-level="2"><div style="left:23px" class="absolute h-full w-px bg-border-strong"></div><div class="absolute left-0 h-full w-0.5 bg-foreground"></div><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file shrink-0"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path></svg><span class="truncate text-sm" title="use-infinite-query.ts">use-infinite-query.ts</span><form class="hidden"><input autofocus="" class="rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive block w-full text-sm px-2 py-1 h-7" value="use-infinite-query.ts"/></form></div></li></ul></li></ul></div><div class="group relative max-w-[90vw] md:max-w-none overflow-auto w-full"><pre class="code-block border border-surface p-4 w-full !my-0 !bg-surface-100 outline-none focus:border-foreground-lighter/50 h-full max-w-none !w-full flex-1 font-mono text-xs rounded-none border-none" contentEditable="true" style="display:block;overflow-x:auto;color:#888;font-size:13px;line-height:1.4"><code class="language-ts" style="white-space:pre"><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">1</span><span style="color:#3ECF8E">&#x27;use client&#x27;</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">2</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">3</span><span></span><span style="color:#569cd6;font-weight:normal">import</span><span> { createClient } </span><span style="color:#569cd6;font-weight:normal">from</span><span> </span><span style="color:#3ECF8E">&#x27;@/lib/supabase/client&#x27;</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">4</span><span></span><span style="color:#569cd6;font-weight:normal">import</span><span> { PostgrestQueryBuilder } </span><span style="color:#569cd6;font-weight:normal">from</span><span> </span><span style="color:#3ECF8E">&#x27;@supabase/postgrest-js&#x27;</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">5</span><span></span><span style="color:#569cd6;font-weight:normal">import</span><span> { SupabaseClient } </span><span style="color:#569cd6;font-weight:normal">from</span><span> </span><span style="color:#3ECF8E">&#x27;@supabase/supabase-js&#x27;</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">6</span><span></span><span style="color:#569cd6;font-weight:normal">import</span><span> { useEffect, useRef, useSyncExternalStore } </span><span style="color:#569cd6;font-weight:normal">from</span><span> </span><span style="color:#3ECF8E">&#x27;react&#x27;</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">7</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">8</span><span></span><span style="color:#569cd6;font-weight:normal">const</span><span> supabase = createClient()
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">9</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">10</span><span></span><span style="color:#888">// The following types are used to make the hook type-safe. It extracts the database type from the supabase client.</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">11</span><span></span><span style="color:#569cd6;font-weight:normal">type</span><span> SupabaseClientType = </span><span style="color:#569cd6;font-weight:normal">typeof</span><span> supabase
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">12</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">13</span><span></span><span style="color:#888">// Utility type to check if the type is any</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">14</span><span></span><span style="color:#569cd6;font-weight:normal">type</span><span> IfAny&lt;T, Y, N&gt; = </span><span class="hljs-number">0</span><span> </span><span style="color:#569cd6;font-weight:normal">extends</span><span> </span><span class="hljs-number">1</span><span> &amp; T ? Y : N
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">15</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">16</span><span></span><span style="color:#888">// Extracts the database type from the supabase client. If the supabase client doesn&#x27;t have a type, it will fallback properly.</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">17</span><span></span><span style="color:#569cd6;font-weight:normal">type</span><span> Database =
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">18</span><span>  SupabaseClientType </span><span style="color:#569cd6;font-weight:normal">extends</span><span> SupabaseClient&lt;infer U&gt;
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">19</span>    ? IfAny&lt;
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">20</span>        U,
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">21</span>        {
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">22</span><span>          </span><span class="hljs-attr">public</span><span>: {
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">23</span><span>            </span><span class="hljs-attr">Tables</span><span>: Record&lt;</span><span style="color:#3ECF8E">string</span><span>, </span><span style="color:#3ECF8E">any</span><span>&gt;
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">24</span><span>            Views: Record&lt;</span><span style="color:#3ECF8E">string</span><span>, </span><span style="color:#3ECF8E">any</span><span>&gt;
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">25</span><span>            Functions: Record&lt;</span><span style="color:#3ECF8E">string</span><span>, </span><span style="color:#3ECF8E">any</span><span>&gt;
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">26</span>          }
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">27</span>        },
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">28</span>        U
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">29</span>      &gt;
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">30</span><span>    : </span><span style="color:#3ECF8E">never</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">31</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">32</span><span></span><span style="color:#888">// Change this to the database schema you want to use</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">33</span><span></span><span style="color:#569cd6;font-weight:normal">type</span><span> DatabaseSchema = Database[</span><span style="color:#3ECF8E">&#x27;public&#x27;</span><span>]
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">34</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">35</span><span></span><span style="color:#888">// Extracts the table names from the database type</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">36</span><span></span><span style="color:#569cd6;font-weight:normal">type</span><span> SupabaseTableName = keyof DatabaseSchema[</span><span style="color:#3ECF8E">&#x27;Tables&#x27;</span><span>]
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">37</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">38</span><span></span><span style="color:#888">// Extracts the table definition from the database type</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">39</span><span></span><span style="color:#569cd6;font-weight:normal">type</span><span> SupabaseTableData&lt;T </span><span style="color:#569cd6;font-weight:normal">extends</span><span> SupabaseTableName&gt; = DatabaseSchema[</span><span style="color:#3ECF8E">&#x27;Tables&#x27;</span><span>][T][</span><span style="color:#3ECF8E">&#x27;Row&#x27;</span><span>]
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">40</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">41</span><span></span><span style="color:#569cd6;font-weight:normal">type</span><span> SupabaseSelectBuilder&lt;T </span><span style="color:#569cd6;font-weight:normal">extends</span><span> SupabaseTableName&gt; = ReturnType&lt;
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">42</span><span>  PostgrestQueryBuilder&lt;DatabaseSchema, DatabaseSchema[</span><span style="color:#3ECF8E">&#x27;Tables&#x27;</span><span>][T], T&gt;[</span><span style="color:#3ECF8E">&#x27;select&#x27;</span><span>]
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">43</span>&gt;
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">44</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">45</span><span></span><span style="color:#888">// A function that modifies the query. Can be used to sort, filter, etc. If .range is used, it will be overwritten.</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">46</span><span></span><span style="color:#569cd6;font-weight:normal">type</span><span> SupabaseQueryHandler&lt;T </span><span style="color:#569cd6;font-weight:normal">extends</span><span> SupabaseTableName&gt; = </span><span class="hljs-function">(</span><span class="hljs-function hljs-params">
</span></span><span class="hljs-function hljs-params"><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">47</span>  query: SupabaseSelectBuilder&lt;T&gt;
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">48</span><span class="hljs-function hljs-params"></span><span class="hljs-function">) =&gt;</span><span> SupabaseSelectBuilder&lt;T&gt;
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">49</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">50</span><span></span><span style="color:#569cd6;font-weight:normal">interface</span><span> UseInfiniteQueryProps&lt;T </span><span style="color:#569cd6;font-weight:normal">extends</span><span> SupabaseTableName, Query </span><span style="color:#569cd6;font-weight:normal">extends</span><span> string = &#x27;*&#x27;&gt; {
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">51</span><span>  </span><span style="color:#888">// The table name to query</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">52</span><span>  </span><span class="hljs-attr">tableName</span><span>: T
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">53</span><span>  </span><span style="color:#888">// The columns to select, defaults to `*`</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">54</span><span>  columns?: </span><span style="color:#3ECF8E">string</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">55</span><span>  </span><span style="color:#888">// The number of items to fetch per page, defaults to `20`</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">56</span><span>  pageSize?: </span><span style="color:#3ECF8E">number</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">57</span><span>  </span><span style="color:#888">// A function that modifies the query. Can be used to sort, filter, etc. If .range is used, it will be overwritten.</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">58</span>  trailingQuery?: SupabaseQueryHandler&lt;T&gt;
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">59</span>}
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">60</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">61</span><span></span><span style="color:#569cd6;font-weight:normal">interface</span><span> StoreState&lt;TData&gt; {
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">62</span><span>  </span><span class="hljs-attr">data</span><span>: TData[]
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">63</span><span>  </span><span class="hljs-attr">count</span><span>: </span><span style="color:#3ECF8E">number</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">64</span><span>  </span><span class="hljs-attr">isSuccess</span><span>: </span><span style="color:#3ECF8E">boolean</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">65</span><span>  </span><span class="hljs-attr">isLoading</span><span>: </span><span style="color:#3ECF8E">boolean</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">66</span><span>  </span><span class="hljs-attr">isFetching</span><span>: </span><span style="color:#3ECF8E">boolean</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">67</span><span>  </span><span class="hljs-attr">error</span><span>: </span><span style="color:#3ECF8E">Error</span><span> | </span><span style="color:#569cd6;font-weight:normal">null</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">68</span><span>  </span><span class="hljs-attr">hasInitialFetch</span><span>: </span><span style="color:#3ECF8E">boolean</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">69</span>}
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">70</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">71</span><span></span><span style="color:#569cd6;font-weight:normal">type</span><span> Listener = </span><span class="hljs-function">() =&gt;</span><span> </span><span style="color:#3ECF8E">void</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">72</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">73</span><span></span><span class="hljs-function" style="color:#569cd6;font-weight:normal">function</span><span class="hljs-function"> </span><span class="hljs-function" style="color:#3ECF8E;font-weight:normal">createStore</span><span class="hljs-function">&lt;</span><span class="hljs-function" style="color:#3ECF8E;font-weight:normal">TData</span><span class="hljs-function"> </span><span class="hljs-function" style="color:#3ECF8E;font-weight:normal">extends</span><span class="hljs-function"> </span><span class="hljs-function" style="color:#3ECF8E;font-weight:normal">SupabaseTableData</span><span class="hljs-function">&lt;</span><span class="hljs-function" style="color:#3ECF8E;font-weight:normal">T</span><span class="hljs-function">&gt;, </span><span class="hljs-function" style="color:#3ECF8E;font-weight:normal">T</span><span class="hljs-function"> </span><span class="hljs-function" style="color:#3ECF8E;font-weight:normal">extends</span><span class="hljs-function"> </span><span class="hljs-function" style="color:#3ECF8E;font-weight:normal">SupabaseTableName</span><span class="hljs-function">&gt;(</span><span class="hljs-function hljs-params">
</span></span><span class="hljs-function hljs-params"><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">74</span>  props: UseInfiniteQueryProps&lt;T&gt;
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">75</span><span class="hljs-function hljs-params"></span><span class="hljs-function">) </span><span>{
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">76</span><span>  </span><span style="color:#569cd6;font-weight:normal">const</span><span> { tableName, columns = </span><span style="color:#3ECF8E">&#x27;*&#x27;</span><span>, pageSize = </span><span class="hljs-number">20</span><span>, trailingQuery } = props
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">77</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">78</span><span>  </span><span style="color:#569cd6;font-weight:normal">let</span><span> state: StoreState&lt;TData&gt; = {
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">79</span><span>    </span><span class="hljs-attr">data</span><span>: [],
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">80</span><span>    </span><span class="hljs-attr">count</span><span>: </span><span class="hljs-number">0</span><span>,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">81</span><span>    </span><span class="hljs-attr">isSuccess</span><span>: </span><span style="color:#569cd6;font-weight:normal">false</span><span>,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">82</span><span>    </span><span class="hljs-attr">isLoading</span><span>: </span><span style="color:#569cd6;font-weight:normal">false</span><span>,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">83</span><span>    </span><span class="hljs-attr">isFetching</span><span>: </span><span style="color:#569cd6;font-weight:normal">false</span><span>,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">84</span><span>    </span><span class="hljs-attr">error</span><span>: </span><span style="color:#569cd6;font-weight:normal">null</span><span>,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">85</span><span>    </span><span class="hljs-attr">hasInitialFetch</span><span>: </span><span style="color:#569cd6;font-weight:normal">false</span><span>,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">86</span>  }
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">87</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">88</span><span>  </span><span style="color:#569cd6;font-weight:normal">const</span><span> listeners = </span><span style="color:#569cd6;font-weight:normal">new</span><span> </span><span style="color:#3ECF8E">Set</span><span>&lt;Listener&gt;()
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">89</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">90</span><span>  </span><span style="color:#569cd6;font-weight:normal">const</span><span> notify = </span><span class="hljs-function">() =&gt;</span><span> {
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">91</span><span>    listeners.forEach(</span><span class="hljs-function">(</span><span class="hljs-function hljs-params">listener</span><span class="hljs-function">) =&gt;</span><span> listener())
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">92</span>  }
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">93</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">94</span><span>  </span><span style="color:#569cd6;font-weight:normal">const</span><span> setState = </span><span class="hljs-function">(</span><span class="hljs-function hljs-params">newState: Partial&lt;StoreState&lt;TData&gt;&gt;</span><span class="hljs-function">) =&gt;</span><span> {
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">95</span>    state = { ...state, ...newState }
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">96</span>    notify()
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">97</span>  }
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">98</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">99</span><span>  </span><span style="color:#569cd6;font-weight:normal">const</span><span> fetchPage = </span><span style="color:#569cd6;font-weight:normal">async</span><span> (skip: </span><span style="color:#3ECF8E">number</span><span>) =&gt; {
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">100</span><span>    </span><span style="color:#569cd6;font-weight:normal">if</span><span> (state.hasInitialFetch &amp;&amp; (state.isFetching || state.count &lt;= state.data.length)) </span><span style="color:#569cd6;font-weight:normal">return</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">101</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">102</span><span>    setState({ </span><span class="hljs-attr">isFetching</span><span>: </span><span style="color:#569cd6;font-weight:normal">true</span><span> })
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">103</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">104</span><span>    </span><span style="color:#569cd6;font-weight:normal">let</span><span> query = supabase
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">105</span>      .from(tableName)
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">106</span><span>      .select(columns, { </span><span class="hljs-attr">count</span><span>: </span><span style="color:#3ECF8E">&#x27;exact&#x27;</span><span> }) </span><span style="color:#569cd6;font-weight:normal">as</span><span> unknown </span><span style="color:#569cd6;font-weight:normal">as</span><span> SupabaseSelectBuilder&lt;T&gt;
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">107</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">108</span><span>    </span><span style="color:#569cd6;font-weight:normal">if</span><span> (trailingQuery) {
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">109</span>      query = trailingQuery(query)
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">110</span>    }
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">111</span><span>    </span><span style="color:#569cd6;font-weight:normal">const</span><span> { </span><span class="hljs-attr">data</span><span>: newData, count, error } = </span><span style="color:#569cd6;font-weight:normal">await</span><span> query.range(skip, skip + pageSize - </span><span class="hljs-number">1</span><span>)
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">112</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">113</span><span>    </span><span style="color:#569cd6;font-weight:normal">if</span><span> (error) {
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">114</span><span>      </span><span style="color:#3ECF8E">console</span><span>.error(</span><span style="color:#3ECF8E">&#x27;An unexpected error occurred:&#x27;</span><span>, error)
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">115</span>      setState({ error })
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">116</span><span>    } </span><span style="color:#569cd6;font-weight:normal">else</span><span> {
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">117</span><span>      </span><span style="color:#569cd6;font-weight:normal">const</span><span> deduplicatedData = ((newData || []) </span><span style="color:#569cd6;font-weight:normal">as</span><span> TData[]).filter(
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">118</span><span>        </span><span class="hljs-function">(</span><span class="hljs-function hljs-params">item</span><span class="hljs-function">) =&gt;</span><span> !state.data.find(</span><span class="hljs-function">(</span><span class="hljs-function hljs-params">old</span><span class="hljs-function">) =&gt;</span><span> old.id === item.id)
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">119</span>      )
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">120</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">121</span>      setState({
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">122</span><span>        </span><span class="hljs-attr">data</span><span>: [...state.data, ...deduplicatedData],
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">123</span><span>        </span><span class="hljs-attr">count</span><span>: count || </span><span class="hljs-number">0</span><span>,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">124</span><span>        </span><span class="hljs-attr">isSuccess</span><span>: </span><span style="color:#569cd6;font-weight:normal">true</span><span>,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">125</span><span>        </span><span class="hljs-attr">error</span><span>: </span><span style="color:#569cd6;font-weight:normal">null</span><span>,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">126</span>      })
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">127</span>    }
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">128</span><span>    setState({ </span><span class="hljs-attr">isFetching</span><span>: </span><span style="color:#569cd6;font-weight:normal">false</span><span> })
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">129</span>  }
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">130</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">131</span><span>  </span><span style="color:#569cd6;font-weight:normal">const</span><span> fetchNextPage = </span><span style="color:#569cd6;font-weight:normal">async</span><span> () =&gt; {
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">132</span><span>    </span><span style="color:#569cd6;font-weight:normal">if</span><span> (state.isFetching) </span><span style="color:#569cd6;font-weight:normal">return</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">133</span><span>    </span><span style="color:#569cd6;font-weight:normal">await</span><span> fetchPage(state.data.length)
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">134</span>  }
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">135</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">136</span><span>  </span><span style="color:#569cd6;font-weight:normal">const</span><span> initialize = </span><span style="color:#569cd6;font-weight:normal">async</span><span> () =&gt; {
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">137</span><span>    setState({ </span><span class="hljs-attr">isLoading</span><span>: </span><span style="color:#569cd6;font-weight:normal">true</span><span>, </span><span class="hljs-attr">isSuccess</span><span>: </span><span style="color:#569cd6;font-weight:normal">false</span><span>, </span><span class="hljs-attr">data</span><span>: [] })
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">138</span><span>    </span><span style="color:#569cd6;font-weight:normal">await</span><span> fetchNextPage()
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">139</span><span>    setState({ </span><span class="hljs-attr">isLoading</span><span>: </span><span style="color:#569cd6;font-weight:normal">false</span><span>, </span><span class="hljs-attr">hasInitialFetch</span><span>: </span><span style="color:#569cd6;font-weight:normal">true</span><span> })
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">140</span>  }
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">141</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">142</span><span>  </span><span style="color:#569cd6;font-weight:normal">return</span><span> {
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">143</span><span>    </span><span class="hljs-attr">getState</span><span>: </span><span class="hljs-function">() =&gt;</span><span> state,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">144</span><span>    </span><span class="hljs-attr">subscribe</span><span>: </span><span class="hljs-function">(</span><span class="hljs-function hljs-params">listener: Listener</span><span class="hljs-function">) =&gt;</span><span> {
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">145</span>      listeners.add(listener)
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">146</span><span>      </span><span style="color:#569cd6;font-weight:normal">return</span><span> </span><span class="hljs-function">() =&gt;</span><span> listeners.delete(listener)
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">147</span>    },
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">148</span>    fetchNextPage,
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">149</span>    initialize,
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">150</span>  }
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">151</span>}
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">152</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">153</span><span></span><span style="color:#888">// Empty initial state to avoid hydration errors.</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">154</span><span></span><span style="color:#569cd6;font-weight:normal">const</span><span> initialState: </span><span style="color:#3ECF8E">any</span><span> = {
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">155</span><span>  </span><span class="hljs-attr">data</span><span>: [],
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">156</span><span>  </span><span class="hljs-attr">count</span><span>: </span><span class="hljs-number">0</span><span>,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">157</span><span>  </span><span class="hljs-attr">isSuccess</span><span>: </span><span style="color:#569cd6;font-weight:normal">false</span><span>,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">158</span><span>  </span><span class="hljs-attr">isLoading</span><span>: </span><span style="color:#569cd6;font-weight:normal">false</span><span>,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">159</span><span>  </span><span class="hljs-attr">isFetching</span><span>: </span><span style="color:#569cd6;font-weight:normal">false</span><span>,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">160</span><span>  </span><span class="hljs-attr">error</span><span>: </span><span style="color:#569cd6;font-weight:normal">null</span><span>,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">161</span><span>  </span><span class="hljs-attr">hasInitialFetch</span><span>: </span><span style="color:#569cd6;font-weight:normal">false</span><span>,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">162</span>}
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">163</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">164</span><span></span><span class="hljs-function" style="color:#569cd6;font-weight:normal">function</span><span class="hljs-function"> </span><span class="hljs-function" style="color:#3ECF8E;font-weight:normal">useInfiniteQuery</span><span class="hljs-function">&lt;
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">165</span><span class="hljs-function">  </span><span class="hljs-function" style="color:#3ECF8E;font-weight:normal">TData</span><span class="hljs-function"> </span><span class="hljs-function" style="color:#3ECF8E;font-weight:normal">extends</span><span class="hljs-function"> </span><span class="hljs-function" style="color:#3ECF8E;font-weight:normal">SupabaseTableData</span><span class="hljs-function">&lt;</span><span class="hljs-function" style="color:#3ECF8E;font-weight:normal">T</span><span class="hljs-function">&gt;,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">166</span><span class="hljs-function">  </span><span class="hljs-function" style="color:#3ECF8E;font-weight:normal">T</span><span class="hljs-function"> </span><span class="hljs-function" style="color:#3ECF8E;font-weight:normal">extends</span><span class="hljs-function"> </span><span class="hljs-function" style="color:#3ECF8E;font-weight:normal">SupabaseTableName</span><span class="hljs-function"> = </span><span class="hljs-function" style="color:#3ECF8E;font-weight:normal">SupabaseTableName</span><span class="hljs-function">,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">167</span><span class="hljs-function">&gt;(</span><span class="hljs-function hljs-params">props: UseInfiniteQueryProps&lt;T&gt;</span><span class="hljs-function">) </span><span>{
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">168</span><span>  </span><span style="color:#569cd6;font-weight:normal">const</span><span> storeRef = useRef(createStore&lt;TData, T&gt;(props))
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">169</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">170</span><span>  </span><span style="color:#569cd6;font-weight:normal">const</span><span> state = useSyncExternalStore(
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">171</span>    storeRef.current.subscribe,
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">172</span><span>    </span><span class="hljs-function">() =&gt;</span><span> storeRef.current.getState(),
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">173</span><span>    </span><span class="hljs-function">() =&gt;</span><span> initialState </span><span style="color:#569cd6;font-weight:normal">as</span><span> StoreState&lt;TData&gt;
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">174</span>  )
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">175</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">176</span><span>  useEffect(</span><span class="hljs-function">() =&gt;</span><span> {
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">177</span><span>    </span><span style="color:#888">// Recreate store if props change</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">178</span><span>    </span><span style="color:#569cd6;font-weight:normal">if</span><span> (
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">179</span>      storeRef.current.getState().hasInitialFetch &amp;&amp;
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">180</span>      (props.tableName !== props.tableName ||
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">181</span>        props.columns !== props.columns ||
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">182</span>        props.pageSize !== props.pageSize)
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">183</span>    ) {
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">184</span>      storeRef.current = createStore&lt;TData, T&gt;(props)
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">185</span>    }
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">186</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">187</span><span>    </span><span style="color:#569cd6;font-weight:normal">if</span><span> (!state.hasInitialFetch &amp;&amp; </span><span style="color:#569cd6;font-weight:normal">typeof</span><span> </span><span style="color:#3ECF8E">window</span><span> !== </span><span style="color:#3ECF8E">&#x27;undefined&#x27;</span><span>) {
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">188</span>      storeRef.current.initialize()
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">189</span>    }
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">190</span>  }, [props.tableName, props.columns, props.pageSize, state.hasInitialFetch])
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">191</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">192</span><span>  </span><span style="color:#569cd6;font-weight:normal">return</span><span> {
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">193</span><span>    </span><span class="hljs-attr">data</span><span>: state.data,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">194</span><span>    </span><span class="hljs-attr">count</span><span>: state.count,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">195</span><span>    </span><span class="hljs-attr">isSuccess</span><span>: state.isSuccess,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">196</span><span>    </span><span class="hljs-attr">isLoading</span><span>: state.isLoading,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">197</span><span>    </span><span class="hljs-attr">isFetching</span><span>: state.isFetching,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">198</span><span>    </span><span class="hljs-attr">error</span><span>: state.error,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">199</span><span>    </span><span class="hljs-attr">hasMore</span><span>: state.count &gt; state.data.length,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">200</span><span>    </span><span class="hljs-attr">fetchNextPage</span><span>: storeRef.current.fetchNextPage,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">201</span>  }
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">202</span>}
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">203</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">204</span><span></span><span style="color:#569cd6;font-weight:normal">export</span><span> {
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">205</span>  useInfiniteQuery,
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">206</span><span>  </span><span style="color:#569cd6;font-weight:normal">type</span><span> SupabaseQueryHandler,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">207</span><span>  </span><span style="color:#569cd6;font-weight:normal">type</span><span> SupabaseTableData,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">208</span><span>  </span><span style="color:#569cd6;font-weight:normal">type</span><span> SupabaseTableName,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">209</span><span>  </span><span style="color:#569cd6;font-weight:normal">type</span><span> UseInfiniteQueryProps,
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">210</span>}</span></code></pre><div class="absolute right-2 top-2 opacity-0 group-hover:opacity-100 transition "><button data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs py-1 h-[26px] px-1.5"><div class="[&amp;_svg]:h-[14px] [&amp;_svg]:w-[14px] text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></div> <!-- --> </button></div></div></div>
<h2 class="font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0" id="introduction"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#introduction"><span class="icon icon-link"></span></a>Introduction</h2>
<p class="leading-7 [&amp;:not(:first-child)]:mt-6 text-foreground-light">The Infinite Query Hook provides a single React hook which will make it easier to load data progressively from your Supabase database. It handles data fetching and pagination state, It is meant to be used with infinite lists or tables.
The hook is fully typed, provided you have generated and setup your database types.</p>
<h2 class="font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0" id="adding-types"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#adding-types"><span class="icon icon-link"></span></a>Adding types</h2>
<p class="leading-7 [&amp;:not(:first-child)]:mt-6 text-foreground-light">Before using this hook, we <strong>highly</strong> recommend you setup database types in your project. This will make the hook fully-typesafe. More info about generating Typescript types from database schema <a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2" href="../../docs/guides/api/rest/generating-types.html">here</a></p>
<h2 class="font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0" id="props"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#props"><span class="icon icon-link"></span></a>Props</h2>
<div class="my-6 w-full overflow-y-auto"><table class="w-full"><thead><tr class="m-0 border-t p-0 even:bg-surface-75/75"><th class="border px-4 py-2 text-left font-normal [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right">Prop</th><th class="border px-4 py-2 text-left font-normal [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right">Type</th><th class="border px-4 py-2 text-left font-normal [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right">Description</th></tr></thead><tbody><tr class="m-0 border-t p-0 even:bg-surface-75/75"><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">tableName</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">string</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><strong>Required.</strong> The name of the Supabase table to fetch data from.</td></tr><tr class="m-0 border-t p-0 even:bg-surface-75/75"><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">columns</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">string</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right">Columns to select from the table. Defaults to <code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">&#x27;*&#x27;</code>.</td></tr><tr class="m-0 border-t p-0 even:bg-surface-75/75"><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">pageSize</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">number</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right">Number of items to fetch per page. Defaults to <code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">20</code>.</td></tr><tr class="m-0 border-t p-0 even:bg-surface-75/75"><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">trailingQuery</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">(query: SupabaseSelectBuilder) =&gt; SupabaseSelectBuilder</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right">Function to apply filters or sorting to the Supabase query.</td></tr></tbody></table></div>
<h2 class="font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0" id="return-type"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#return-type"><span class="icon icon-link"></span></a>Return type</h2>
<p class="leading-7 [&amp;:not(:first-child)]:mt-6 text-foreground-light">data, count, isSuccess, isLoading, isFetching, error, hasMore, fetchNextPage</p>
<div class="my-6 w-full overflow-y-auto"><table class="w-full"><thead><tr class="m-0 border-t p-0 even:bg-surface-75/75"><th class="border px-4 py-2 text-left font-normal [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right">Prop</th><th class="border px-4 py-2 text-left font-normal [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right">Type</th><th class="border px-4 py-2 text-left font-normal [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right">Description</th></tr></thead><tbody><tr class="m-0 border-t p-0 even:bg-surface-75/75"><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">data</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">TableData[]</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right">An array of fetched items.</td></tr><tr class="m-0 border-t p-0 even:bg-surface-75/75"><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">count</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">number</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right">Number of total items in the database. It takes <code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">trailingQuery</code> into consideration.</td></tr><tr class="m-0 border-t p-0 even:bg-surface-75/75"><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">isSuccess</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">boolean</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right">It&#x27;s true if the last API call succeeded.</td></tr><tr class="m-0 border-t p-0 even:bg-surface-75/75"><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">isLoading</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">boolean</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right">It&#x27;s true only for the initial fetch.</td></tr><tr class="m-0 border-t p-0 even:bg-surface-75/75"><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">isFetching</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">boolean</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right">It&#x27;s true for the initial and all incremental fetches.</td></tr><tr class="m-0 border-t p-0 even:bg-surface-75/75"><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">error</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">any</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right">The error from the last fetch.</td></tr><tr class="m-0 border-t p-0 even:bg-surface-75/75"><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">hasMore</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">boolean</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right">Whether the query has finished fetching all items from the database</td></tr><tr class="m-0 border-t p-0 even:bg-surface-75/75"><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">fetchNextPage</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">() =&gt; void</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right">Sends a new request for the next items</td></tr></tbody></table></div>
<h2 class="font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0" id="type-safety"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#type-safety"><span class="icon icon-link"></span></a>Type safety</h2>
<p class="leading-7 [&amp;:not(:first-child)]:mt-6 text-foreground-light">The hook will use the typed defined on your Supabase client if they&#x27;re setup (<a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2" href="https://supabase.com/docs/reference/javascript/typescript-support">more info</a>).</p>
<p class="leading-7 [&amp;:not(:first-child)]:mt-6 text-foreground-light">The hook also supports an custom defined result type by using <code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">useInfiniteQuery&lt;T&gt;</code>. For example, if you have a custom type for <code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">Product</code>, you can use it like this <code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">useInfiniteQuery&lt;Product&gt;</code>.</p>
<h2 class="font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0" id="usage"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#usage"><span class="icon icon-link"></span></a>Usage</h2>
<h3 class="font-heading mt-8 scroll-m-20 text-xl tracking-tight" id="with-sorting"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#with-sorting"><span class="icon icon-link"></span></a>With sorting</h3>
<div data-rehype-pretty-code-fragment=""><pre class="mb-4 mt-6 max-h-[650px] overflow-x-auto rounded-lg border bg-surface-75/75 py-4 text-foreground-light" data-language="tsx" data-theme="default"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm" data-language="tsx" data-theme="default"><span class="line"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-punctuation)"> {</span><span style="color:var(--code-token-constant)"> data</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-constant)"> fetchNextPage</span><span style="color:var(--code-token-punctuation)"> }</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-function)"> useInfiniteQuery</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">{</span></span>
<span class="line"><span style="color:var(--code-token-property)">  tableName</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-punctuation)"> &#x27;</span><span style="color:var(--code-token-string-expression)">products</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span></span>
<span class="line"><span style="color:var(--code-token-property)">  columns</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-punctuation)"> &#x27;</span><span style="color:var(--code-token-string-expression)">*</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span></span>
<span class="line"><span style="color:var(--code-token-property)">  pageSize</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-constant)"> 10</span><span style="color:var(--code-token-punctuation)">,</span></span>
<span class="line"><span style="color:var(--code-token-function)">  trailingQuery</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-punctuation)"> (</span><span style="color:var(--code-token-parameter)">query</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-token-keyword)"> =&gt;</span><span style="color:var(--code-token-parameter)"> query</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">order</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">created_at</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-punctuation)"> {</span><span style="color:var(--code-token-property)"> ascending</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-constant)"> false</span><span style="color:var(--code-token-punctuation)"> }</span><span style="color:var(--code-foreground)">)</span><span style="color:var(--code-token-punctuation)">,</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-keyword)">return</span><span style="color:var(--code-foreground)"> (</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">  &lt;</span><span style="color:var(--code-token-function)">div</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">    {</span><span style="color:var(--code-token-parameter)">data</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">map</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-parameter)">item</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-token-keyword)"> =&gt;</span><span style="color:var(--code-foreground)"> (</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">      &lt;</span><span style="color:var(--code-token-function)">ProductCard </span><span style="color:var(--code-token-property)">key</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-token-parameter)">item</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">id</span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-property)"> product</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-token-parameter)">item</span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-punctuation)"> /&gt;</span></span>
<span class="line"><span style="color:var(--code-foreground)">    ))</span><span style="color:var(--code-token-punctuation)">}</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">    &lt;</span><span style="color:var(--code-token-function)">Button </span><span style="color:var(--code-token-property)">onClick</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-token-parameter)">fetchNextPage</span><span style="color:var(--code-token-punctuation)">}&gt;</span><span style="color:var(--code-token-function)">Load more products</span><span style="color:var(--code-token-punctuation)">&lt;/</span><span style="color:var(--code-token-function)">Button</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">  &lt;/</span><span style="color:var(--code-token-function)">div</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-foreground)">)</span></span></code></pre><button data-size="small" type="button" class="justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong hover:border-foreground-muted focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-sm leading-4 z-10 h-6 w-6 text-foreground-muted hover:bg-surface-100 hover:text-foreground p-0 absolute right-4 top-4"> <span class="truncate"><span class="sr-only">Copy</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy h-3 w-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></span> </button></div>
<h3 class="font-heading mt-8 scroll-m-20 text-xl tracking-tight" id="with-filtering-on-search-params"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#with-filtering-on-search-params"><span class="icon icon-link"></span></a>With filtering on search params</h3>
<p class="leading-7 [&amp;:not(:first-child)]:mt-6 text-foreground-light">This example will filter based on a search param like <code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">example.com/?q=hello</code>.</p>
<div data-rehype-pretty-code-fragment=""><pre class="mb-4 mt-6 max-h-[650px] overflow-x-auto rounded-lg border bg-surface-75/75 py-4 text-foreground-light" data-language="tsx" data-theme="default"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm" data-language="tsx" data-theme="default"><span class="line"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-constant)"> params</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-function)"> useSearchParams</span><span style="color:var(--code-foreground)">()</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-constant)"> searchQuery</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-parameter)"> params</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">get</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">q</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-foreground)">)</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-punctuation)"> {</span><span style="color:var(--code-token-constant)"> data</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-constant)"> isLoading</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-constant)"> isFetching</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-constant)"> fetchNextPage</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-constant)"> count</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-constant)"> isSuccess</span><span style="color:var(--code-token-punctuation)"> }</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-function)"> useInfiniteQuery</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">{</span></span>
<span class="line"><span style="color:var(--code-token-property)">  tableName</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-punctuation)"> &#x27;</span><span style="color:var(--code-token-string-expression)">products</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span></span>
<span class="line"><span style="color:var(--code-token-property)">  columns</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-punctuation)"> &#x27;</span><span style="color:var(--code-token-string-expression)">*</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span></span>
<span class="line"><span style="color:var(--code-token-property)">  pageSize</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-constant)"> 10</span><span style="color:var(--code-token-punctuation)">,</span></span>
<span class="line"><span style="color:var(--code-token-function)">  trailingQuery</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-punctuation)"> (</span><span style="color:var(--code-token-parameter)">query</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-token-keyword)"> =&gt;</span><span style="color:var(--code-token-punctuation)"> {</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">    if</span><span style="color:var(--code-foreground)"> (</span><span style="color:var(--code-token-parameter)">searchQuery</span><span style="color:var(--code-token-keyword)"> &amp;&amp;</span><span style="color:var(--code-token-parameter)"> searchQuery</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">length</span><span style="color:var(--code-token-keyword)"> &gt;</span><span style="color:var(--code-token-constant)"> 0</span><span style="color:var(--code-foreground)">) </span><span style="color:var(--code-token-punctuation)">{</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">      query</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-parameter)"> query</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">ilike</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">name</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-punctuation)"> `</span><span style="color:var(--code-token-string)">%</span><span style="color:var(--code-token-punctuation)">${</span><span style="color:var(--code-token-parameter)">searchQuery</span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-string)">%</span><span style="color:var(--code-token-punctuation)">`</span><span style="color:var(--code-foreground)">)</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">    }</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">    return</span><span style="color:var(--code-token-parameter)"> query</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">  },</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-foreground)">)</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-keyword)">return</span><span style="color:var(--code-foreground)"> (</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">  &lt;</span><span style="color:var(--code-token-function)">div</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">    {</span><span style="color:var(--code-token-parameter)">data</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">map</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-parameter)">item</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-token-keyword)"> =&gt;</span><span style="color:var(--code-foreground)"> (</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">      &lt;</span><span style="color:var(--code-token-function)">ProductCard </span><span style="color:var(--code-token-property)">key</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-token-parameter)">item</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">id</span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-property)"> product</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-token-parameter)">item</span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-punctuation)"> /&gt;</span></span>
<span class="line"><span style="color:var(--code-foreground)">    ))</span><span style="color:var(--code-token-punctuation)">}</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">    &lt;</span><span style="color:var(--code-token-function)">Button </span><span style="color:var(--code-token-property)">onClick</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-token-parameter)">fetchNextPage</span><span style="color:var(--code-token-punctuation)">}&gt;</span><span style="color:var(--code-token-function)">Load more products</span><span style="color:var(--code-token-punctuation)">&lt;/</span><span style="color:var(--code-token-function)">Button</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">  &lt;/</span><span style="color:var(--code-token-function)">div</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-foreground)">)</span></span></code></pre><button data-size="small" type="button" class="justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong hover:border-foreground-muted focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-sm leading-4 z-10 h-6 w-6 text-foreground-muted hover:bg-surface-100 hover:text-foreground p-0 absolute right-4 top-4"> <span class="truncate"><span class="sr-only">Copy</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy h-3 w-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></span> </button></div>
<h2 class="font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0" id="reusable-components"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#reusable-components"><span class="icon icon-link"></span></a>Reusable components</h2>
<h3 class="font-heading mt-8 scroll-m-20 text-xl tracking-tight" id="infinite-list-fetches-as-you-scroll"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#infinite-list-fetches-as-you-scroll"><span class="icon icon-link"></span></a>Infinite list (fetches as you scroll)</h3>
<p class="leading-7 [&amp;:not(:first-child)]:mt-6 text-foreground-light">The following component abstracts the hook into a component. It includes few utility components for no results and end of the list.</p>
<div data-rehype-pretty-code-fragment=""><pre class="mb-4 mt-6 max-h-[650px] overflow-x-auto rounded-lg border bg-surface-75/75 py-4 text-foreground-light" data-language="tsx" data-theme="default"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm" data-language="tsx" data-theme="default"><span class="line"><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">use client</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-punctuation)"> {</span><span style="color:var(--code-token-parameter)"> cn</span><span style="color:var(--code-token-punctuation)"> }</span><span style="color:var(--code-token-keyword)"> from</span><span style="color:var(--code-token-punctuation)"> &#x27;</span><span style="color:var(--code-token-string-expression)">@/lib/utils</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-punctuation)"> {</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">  SupabaseQueryHandler</span><span style="color:var(--code-token-punctuation)">,</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">  SupabaseTableData</span><span style="color:var(--code-token-punctuation)">,</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">  SupabaseTableName</span><span style="color:var(--code-token-punctuation)">,</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">  useInfiniteQuery</span><span style="color:var(--code-token-punctuation)">,</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-keyword)"> from</span><span style="color:var(--code-token-punctuation)"> &#x27;</span><span style="color:var(--code-token-string-expression)">@/hooks/use-infinite-query</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-constant)"> *</span><span style="color:var(--code-token-keyword)"> as</span><span style="color:var(--code-token-parameter)"> React</span><span style="color:var(--code-token-keyword)"> from</span><span style="color:var(--code-token-punctuation)"> &#x27;</span><span style="color:var(--code-token-string-expression)">react</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-keyword)">interface</span><span style="color:var(--code-token-function)"> InfiniteListProps</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">TableName</span><span style="color:var(--code-token-keyword)"> extends</span><span style="color:var(--code-token-function)"> SupabaseTableName</span><span style="color:var(--code-token-punctuation)">&gt;</span><span style="color:var(--code-token-punctuation)"> {</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">  tableName</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-token-function)"> TableName</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">  columns</span><span style="color:var(--code-token-keyword)">?:</span><span style="color:var(--code-token-function)"> string</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">  pageSize</span><span style="color:var(--code-token-keyword)">?:</span><span style="color:var(--code-token-function)"> number</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">  trailingQuery</span><span style="color:var(--code-token-keyword)">?:</span><span style="color:var(--code-token-function)"> SupabaseQueryHandler</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">TableName</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-token-function)">  renderItem</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-token-punctuation)"> (</span><span style="color:var(--code-token-parameter)">item</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-token-function)"> SupabaseTableData</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">TableName</span><span style="color:var(--code-token-punctuation)">&gt;,</span><span style="color:var(--code-token-parameter)"> index</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-token-function)"> number</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-token-keyword)"> =&gt;</span><span style="color:var(--code-token-function)"> React</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">ReactNode</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">  className</span><span style="color:var(--code-token-keyword)">?:</span><span style="color:var(--code-token-function)"> string</span></span>
<span class="line"><span style="color:var(--code-token-function)">  renderNoResults</span><span style="color:var(--code-token-keyword)">?:</span><span style="color:var(--code-token-punctuation)"> ()</span><span style="color:var(--code-token-keyword)"> =&gt;</span><span style="color:var(--code-token-function)"> React</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">ReactNode</span></span>
<span class="line"><span style="color:var(--code-token-function)">  renderEndMessage</span><span style="color:var(--code-token-keyword)">?:</span><span style="color:var(--code-token-punctuation)"> ()</span><span style="color:var(--code-token-keyword)"> =&gt;</span><span style="color:var(--code-token-function)"> React</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">ReactNode</span></span>
<span class="line"><span style="color:var(--code-token-function)">  renderSkeleton</span><span style="color:var(--code-token-keyword)">?:</span><span style="color:var(--code-token-punctuation)"> (</span><span style="color:var(--code-token-parameter)">count</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-token-function)"> number</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-token-keyword)"> =&gt;</span><span style="color:var(--code-token-function)"> React</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">ReactNode</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">}</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-function)"> DefaultNoResults</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-punctuation)"> ()</span><span style="color:var(--code-token-keyword)"> =&gt;</span><span style="color:var(--code-foreground)"> (</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">  &lt;</span><span style="color:var(--code-token-function)">div </span><span style="color:var(--code-token-property)">className</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)">text-center text-muted-foreground py-10</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">&gt;</span><span style="color:var(--code-token-function)">No results.</span><span style="color:var(--code-token-punctuation)">&lt;/</span><span style="color:var(--code-token-function)">div</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-foreground)">)</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-function)"> DefaultEndMessage</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-punctuation)"> ()</span><span style="color:var(--code-token-keyword)"> =&gt;</span><span style="color:var(--code-foreground)"> (</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">  &lt;</span><span style="color:var(--code-token-function)">div </span><span style="color:var(--code-token-property)">className</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)">text-center text-muted-foreground py-4 text-sm</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">&gt;</span><span style="color:var(--code-token-function)">You</span><span style="color:var(--code-token-punctuation)">&amp;</span><span style="color:var(--code-token-constant)">apos</span><span style="color:var(--code-token-punctuation)">;</span><span style="color:var(--code-token-function)">ve reached the end.</span><span style="color:var(--code-token-punctuation)">&lt;/</span><span style="color:var(--code-token-function)">div</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-foreground)">)</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-function)"> defaultSkeleton</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-punctuation)"> (</span><span style="color:var(--code-token-parameter)">count</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-token-function)"> number</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-token-keyword)"> =&gt;</span><span style="color:var(--code-foreground)"> (</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">  &lt;</span><span style="color:var(--code-token-function)">div </span><span style="color:var(--code-token-property)">className</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)">flex flex-col gap-2 px-4</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">    {</span><span style="color:var(--code-token-parameter)">Array</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">from</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-token-property)"> length</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-parameter)"> count</span><span style="color:var(--code-token-punctuation)"> }</span><span style="color:var(--code-foreground)">)</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">map</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-parameter)">_</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-parameter)"> index</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-token-keyword)"> =&gt;</span><span style="color:var(--code-foreground)"> (</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">      &lt;</span><span style="color:var(--code-token-function)">div </span><span style="color:var(--code-token-property)">key</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-token-parameter)">index</span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-property)"> className</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)">h-4 w-full bg-muted animate-pulse</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)"> /&gt;</span></span>
<span class="line"><span style="color:var(--code-foreground)">    ))</span><span style="color:var(--code-token-punctuation)">}</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">  &lt;/</span><span style="color:var(--code-token-function)">div</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-foreground)">)</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-keyword)">export</span><span style="color:var(--code-token-keyword)"> function</span><span style="color:var(--code-token-function)"> InfiniteList</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">TableName</span><span style="color:var(--code-token-keyword)"> extends</span><span style="color:var(--code-token-function)"> SupabaseTableName</span><span style="color:var(--code-token-punctuation)">&gt;({</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">  tableName</span><span style="color:var(--code-token-punctuation)">,</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">  columns</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-punctuation)"> &#x27;</span><span style="color:var(--code-token-string-expression)">*</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">  pageSize</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-constant)"> 20</span><span style="color:var(--code-token-punctuation)">,</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">  trailingQuery</span><span style="color:var(--code-token-punctuation)">,</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">  renderItem</span><span style="color:var(--code-token-punctuation)">,</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">  className</span><span style="color:var(--code-token-punctuation)">,</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">  renderNoResults</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-parameter)"> DefaultNoResults</span><span style="color:var(--code-token-punctuation)">,</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">  renderEndMessage</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-parameter)"> DefaultEndMessage</span><span style="color:var(--code-token-punctuation)">,</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">  renderSkeleton</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-parameter)"> defaultSkeleton</span><span style="color:var(--code-token-punctuation)">,</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-token-function)"> InfiniteListProps</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">TableName</span><span style="color:var(--code-token-punctuation)">&gt;)</span><span style="color:var(--code-token-punctuation)"> {</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">  const</span><span style="color:var(--code-token-punctuation)"> {</span><span style="color:var(--code-token-constant)"> data</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-constant)"> isFetching</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-constant)"> hasMore</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-constant)"> fetchNextPage</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-constant)"> isSuccess</span><span style="color:var(--code-token-punctuation)"> }</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-function)"> useInfiniteQuery</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">{</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">    tableName</span><span style="color:var(--code-token-punctuation)">,</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">    columns</span><span style="color:var(--code-token-punctuation)">,</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">    pageSize</span><span style="color:var(--code-token-punctuation)">,</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">    trailingQuery</span><span style="color:var(--code-token-punctuation)">,</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">  }</span><span style="color:var(--code-foreground)">)</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-comment);font-style:italic">  // Ref for the scrolling container</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">  const</span><span style="color:var(--code-token-constant)"> scrollContainerRef</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-parameter)"> React</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">useRef</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">HTMLDivElement</span><span style="color:var(--code-token-punctuation)">&gt;</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-constant)">null</span><span style="color:var(--code-foreground)">)</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-comment);font-style:italic">  // Intersection observer logic - target the last rendered *item* or a dedicated sentinel</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">  const</span><span style="color:var(--code-token-constant)"> loadMoreSentinelRef</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-parameter)"> React</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">useRef</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">HTMLDivElement</span><span style="color:var(--code-token-punctuation)">&gt;</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-constant)">null</span><span style="color:var(--code-foreground)">)</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">  const</span><span style="color:var(--code-token-constant)"> observer</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-parameter)"> React</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">useRef</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-function)">IntersectionObserver</span><span style="color:var(--code-token-keyword)"> |</span><span style="color:var(--code-token-function)"> null</span><span style="color:var(--code-token-punctuation)">&gt;</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-constant)">null</span><span style="color:var(--code-foreground)">)</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-parameter)">  React</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">useEffect</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-token-keyword)"> =&gt;</span><span style="color:var(--code-token-punctuation)"> {</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">    if</span><span style="color:var(--code-foreground)"> (</span><span style="color:var(--code-token-parameter)">observer</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">current</span><span style="color:var(--code-foreground)">) </span><span style="color:var(--code-token-parameter)">observer</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">current</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">disconnect</span><span style="color:var(--code-foreground)">()</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-parameter)">    observer</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">current</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-keyword)"> new</span><span style="color:var(--code-token-function)"> IntersectionObserver</span><span style="color:var(--code-foreground)">(</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">      (</span><span style="color:var(--code-token-parameter)">entries</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-token-keyword)"> =&gt;</span><span style="color:var(--code-token-punctuation)"> {</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">        if</span><span style="color:var(--code-foreground)"> (</span><span style="color:var(--code-token-parameter)">entries</span><span style="color:var(--code-token-punctuation)">[</span><span style="color:var(--code-token-constant)">0</span><span style="color:var(--code-token-punctuation)">].</span><span style="color:var(--code-token-parameter)">isIntersecting</span><span style="color:var(--code-token-keyword)"> &amp;&amp;</span><span style="color:var(--code-token-parameter)"> hasMore</span><span style="color:var(--code-token-keyword)"> &amp;&amp;</span><span style="color:var(--code-token-keyword)"> !</span><span style="color:var(--code-token-parameter)">isFetching</span><span style="color:var(--code-foreground)">) </span><span style="color:var(--code-token-punctuation)">{</span></span>
<span class="line"><span style="color:var(--code-token-function)">          fetchNextPage</span><span style="color:var(--code-foreground)">()</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">        }</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">      },</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">      {</span></span>
<span class="line"><span style="color:var(--code-token-property)">        root</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-parameter)"> scrollContainerRef</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">current</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-comment);font-style:italic"> // Use the scroll container for scroll detection</span></span>
<span class="line"><span style="color:var(--code-token-property)">        threshold</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-constant)"> 0.1</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-comment);font-style:italic"> // Trigger when 10% of the target is visible</span></span>
<span class="line"><span style="color:var(--code-token-property)">        rootMargin</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-punctuation)"> &#x27;</span><span style="color:var(--code-token-string-expression)">0px 0px 100px 0px</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-comment);font-style:italic"> // Trigger loading a bit before reaching the end</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">      }</span></span>
<span class="line"><span style="color:var(--code-foreground)">    )</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-keyword)">    if</span><span style="color:var(--code-foreground)"> (</span><span style="color:var(--code-token-parameter)">loadMoreSentinelRef</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">current</span><span style="color:var(--code-foreground)">) </span><span style="color:var(--code-token-punctuation)">{</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">      observer</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">current</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">observe</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">loadMoreSentinelRef</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">current</span><span style="color:var(--code-foreground)">)</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">    }</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-keyword)">    return</span><span style="color:var(--code-token-punctuation)"> ()</span><span style="color:var(--code-token-keyword)"> =&gt;</span><span style="color:var(--code-token-punctuation)"> {</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">      if</span><span style="color:var(--code-foreground)"> (</span><span style="color:var(--code-token-parameter)">observer</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">current</span><span style="color:var(--code-foreground)">) </span><span style="color:var(--code-token-parameter)">observer</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">current</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">disconnect</span><span style="color:var(--code-foreground)">()</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">    }</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">  },</span><span style="color:var(--code-token-punctuation)"> [</span><span style="color:var(--code-token-parameter)">isFetching</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-parameter)"> hasMore</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-parameter)"> fetchNextPage</span><span style="color:var(--code-token-punctuation)">]</span><span style="color:var(--code-foreground)">)</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-keyword)">  return</span><span style="color:var(--code-foreground)"> (</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">    &lt;</span><span style="color:var(--code-token-function)">div </span><span style="color:var(--code-token-property)">ref</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-token-parameter)">scrollContainerRef</span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-property)"> className</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-token-function)">cn</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">relative h-full overflow-auto</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-parameter)"> className</span><span style="color:var(--code-foreground)">)</span><span style="color:var(--code-token-punctuation)">}&gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">      &lt;</span><span style="color:var(--code-token-function)">div</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">        {</span><span style="color:var(--code-token-parameter)">isSuccess</span><span style="color:var(--code-token-keyword)"> &amp;&amp;</span><span style="color:var(--code-token-parameter)"> data</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">length</span><span style="color:var(--code-token-keyword)"> ===</span><span style="color:var(--code-token-constant)"> 0</span><span style="color:var(--code-token-keyword)"> &amp;&amp;</span><span style="color:var(--code-token-function)"> renderNoResults</span><span style="color:var(--code-foreground)">()</span><span style="color:var(--code-token-punctuation)">}</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-punctuation)">        {</span><span style="color:var(--code-token-parameter)">data</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">map</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">(</span><span style="color:var(--code-token-parameter)">item</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-parameter)"> index</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-token-keyword)"> =&gt;</span><span style="color:var(--code-token-function)"> renderItem</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">item</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-parameter)"> index</span><span style="color:var(--code-foreground)">))</span><span style="color:var(--code-token-punctuation)">}</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-punctuation)">        {</span><span style="color:var(--code-token-parameter)">isFetching</span><span style="color:var(--code-token-keyword)"> &amp;&amp;</span><span style="color:var(--code-token-parameter)"> renderSkeleton</span><span style="color:var(--code-token-keyword)"> &amp;&amp;</span><span style="color:var(--code-token-function)"> renderSkeleton</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">pageSize</span><span style="color:var(--code-foreground)">)</span><span style="color:var(--code-token-punctuation)">}</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-punctuation)">        &lt;</span><span style="color:var(--code-token-function)">div </span><span style="color:var(--code-token-property)">ref</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-token-parameter)">loadMoreSentinelRef</span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-property)"> style</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">{{</span><span style="color:var(--code-token-property)"> height</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-punctuation)"> &#x27;</span><span style="color:var(--code-token-string-expression)">1px</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)"> }}</span><span style="color:var(--code-token-punctuation)"> /&gt;</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-punctuation)">        {</span><span style="color:var(--code-token-keyword)">!</span><span style="color:var(--code-token-parameter)">hasMore</span><span style="color:var(--code-token-keyword)"> &amp;&amp;</span><span style="color:var(--code-token-parameter)"> data</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">length</span><span style="color:var(--code-token-keyword)"> &gt;</span><span style="color:var(--code-token-constant)"> 0</span><span style="color:var(--code-token-keyword)"> &amp;&amp;</span><span style="color:var(--code-token-function)"> renderEndMessage</span><span style="color:var(--code-foreground)">()</span><span style="color:var(--code-token-punctuation)">}</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">      &lt;/</span><span style="color:var(--code-token-function)">div</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">    &lt;/</span><span style="color:var(--code-token-function)">div</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-foreground)">  )</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">}</span></span></code></pre><button data-size="small" type="button" class="justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong hover:border-foreground-muted focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-sm leading-4 z-10 h-6 w-6 text-foreground-muted hover:bg-surface-100 hover:text-foreground p-0 absolute right-4 top-4"> <span class="truncate"><span class="sr-only">Copy</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy h-3 w-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></span> </button></div>
<p class="leading-7 [&amp;:not(:first-child)]:mt-6 text-foreground-light">Use the <code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">InfiniteList</code> component with the <a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2" href="https://supabase.com/dashboard/project/_/sql/quickstarts">Todo List</a> quickstart.</p>
<p class="leading-7 [&amp;:not(:first-child)]:mt-6 text-foreground-light">Add <code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">&lt;InfiniteListDemo /&gt;</code> to a page to see it in action.
Ensure the <a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2" href="https://ui.shadcn.com/docs/components/checkbox">Checkbox</a> component from shadcn/ui is installed, and <a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2" href="../../docs/guides/api/rest/generating-types.html">regenerate/download</a> types after running the quickstart.</p>
<div data-rehype-pretty-code-fragment=""><pre class="mb-4 mt-6 max-h-[650px] overflow-x-auto rounded-lg border bg-surface-75/75 py-4 text-foreground-light" data-language="tsx" data-theme="default"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm" data-language="tsx" data-theme="default"><span class="line"><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">use client</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-punctuation)"> {</span><span style="color:var(--code-token-parameter)"> Checkbox</span><span style="color:var(--code-token-punctuation)"> }</span><span style="color:var(--code-token-keyword)"> from</span><span style="color:var(--code-token-punctuation)"> &#x27;</span><span style="color:var(--code-token-string-expression)">@/components/ui/checkbox</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-punctuation)"> {</span><span style="color:var(--code-token-parameter)"> InfiniteList</span><span style="color:var(--code-token-punctuation)"> }</span><span style="color:var(--code-token-keyword)"> from</span><span style="color:var(--code-token-punctuation)"> &#x27;</span><span style="color:var(--code-token-string-expression)">./infinite-component</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-punctuation)"> {</span><span style="color:var(--code-token-parameter)"> SupabaseQueryHandler</span><span style="color:var(--code-token-punctuation)"> }</span><span style="color:var(--code-token-keyword)"> from</span><span style="color:var(--code-token-punctuation)"> &#x27;</span><span style="color:var(--code-token-string-expression)">@/hooks/use-infinite-query</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-punctuation)"> {</span><span style="color:var(--code-token-parameter)"> Database</span><span style="color:var(--code-token-punctuation)"> }</span><span style="color:var(--code-token-keyword)"> from</span><span style="color:var(--code-token-punctuation)"> &#x27;</span><span style="color:var(--code-token-string-expression)">@/lib/supabase.types</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-keyword)">type</span><span style="color:var(--code-token-function)"> TodoTask</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-function)"> Database</span><span style="color:var(--code-token-punctuation)">[</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">public</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">][</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">Tables</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">][</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">todos</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">][</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">Row</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">]</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-comment);font-style:italic">// Define how each item should be rendered</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-function)"> renderTodoItem</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-punctuation)"> (</span><span style="color:var(--code-token-parameter)">todo</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-token-function)"> TodoTask</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-token-keyword)"> =&gt;</span><span style="color:var(--code-token-punctuation)"> {</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">  return</span><span style="color:var(--code-foreground)"> (</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">    &lt;</span><span style="color:var(--code-token-function)">div</span></span>
<span class="line"><span style="color:var(--code-token-property)">      key</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-token-parameter)">todo</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">id</span><span style="color:var(--code-token-punctuation)">}</span></span>
<span class="line"><span style="color:var(--code-token-property)">      className</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)">border-b py-3 px-4 hover:bg-muted flex items-center justify-between</span><span style="color:var(--code-token-punctuation)">&quot;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">    &gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">      &lt;</span><span style="color:var(--code-token-function)">div </span><span style="color:var(--code-token-property)">className</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)">flex items-center gap-3</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">        &lt;</span><span style="color:var(--code-token-function)">Checkbox </span><span style="color:var(--code-token-property)">defaultChecked</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-token-parameter)">todo</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">is_complete</span><span style="color:var(--code-token-keyword)"> ??</span><span style="color:var(--code-token-constant)"> false</span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-punctuation)"> /&gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">        &lt;</span><span style="color:var(--code-token-function)">div</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">          &lt;</span><span style="color:var(--code-token-function)">span </span><span style="color:var(--code-token-property)">className</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)">font-medium text-sm text-foreground</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">&gt;{</span><span style="color:var(--code-token-parameter)">todo</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">task</span><span style="color:var(--code-token-punctuation)">}&lt;/</span><span style="color:var(--code-token-function)">span</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">          &lt;</span><span style="color:var(--code-token-function)">div </span><span style="color:var(--code-token-property)">className</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)">text-sm text-muted-foreground</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">            {</span><span style="color:var(--code-token-keyword)">new</span><span style="color:var(--code-token-function)"> Date</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">todo</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-parameter)">inserted_at</span><span style="color:var(--code-foreground)">)</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">toLocaleDateString</span><span style="color:var(--code-foreground)">()</span><span style="color:var(--code-token-punctuation)">}</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">          &lt;/</span><span style="color:var(--code-token-function)">div</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">        &lt;/</span><span style="color:var(--code-token-function)">div</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">      &lt;/</span><span style="color:var(--code-token-function)">div</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">    &lt;/</span><span style="color:var(--code-token-function)">div</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-foreground)">  )</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">}</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-function)"> orderByInsertedAt</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-token-function)"> SupabaseQueryHandler</span><span style="color:var(--code-token-punctuation)">&lt;</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">todos</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">&gt;</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-punctuation)"> (</span><span style="color:var(--code-token-parameter)">query</span><span style="color:var(--code-token-punctuation)">)</span><span style="color:var(--code-token-keyword)"> =&gt;</span><span style="color:var(--code-token-punctuation)"> {</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">  return</span><span style="color:var(--code-token-parameter)"> query</span><span style="color:var(--code-token-punctuation)">.</span><span style="color:var(--code-token-function)">order</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">inserted_at</span><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-punctuation)">,</span><span style="color:var(--code-token-punctuation)"> {</span><span style="color:var(--code-token-property)"> ascending</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-constant)"> false</span><span style="color:var(--code-token-punctuation)"> }</span><span style="color:var(--code-foreground)">)</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">}</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-keyword)">export</span><span style="color:var(--code-token-keyword)"> const</span><span style="color:var(--code-token-function)"> InfiniteListDemo</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-punctuation)"> ()</span><span style="color:var(--code-token-keyword)"> =&gt;</span><span style="color:var(--code-token-punctuation)"> {</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">  return</span><span style="color:var(--code-foreground)"> (</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">    &lt;</span><span style="color:var(--code-token-function)">div </span><span style="color:var(--code-token-property)">className</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)">bg-background h-[600px]</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">      &lt;</span><span style="color:var(--code-token-function)">InfiniteList</span></span>
<span class="line"><span style="color:var(--code-token-property)">        tableName</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)">todos</span><span style="color:var(--code-token-punctuation)">&quot;</span></span>
<span class="line"><span style="color:var(--code-token-property)">        renderItem</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-token-parameter)">renderTodoItem</span><span style="color:var(--code-token-punctuation)">}</span></span>
<span class="line"><span style="color:var(--code-token-property)">        pageSize</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-token-constant)">3</span><span style="color:var(--code-token-punctuation)">}</span></span>
<span class="line"><span style="color:var(--code-token-property)">        trailingQuery</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-token-parameter)">orderByInsertedAt</span><span style="color:var(--code-token-punctuation)">}</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">      /&gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">    &lt;/</span><span style="color:var(--code-token-function)">div</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-foreground)">  )</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">}</span></span></code></pre><button data-size="small" type="button" class="justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong hover:border-foreground-muted focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-sm leading-4 z-10 h-6 w-6 text-foreground-muted hover:bg-surface-100 hover:text-foreground p-0 absolute right-4 top-4"> <span class="truncate"><span class="sr-only">Copy</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy h-3 w-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></span> </button></div>
<div role="alert" class="relative w-full text-sm rounded-lg p-4 [&amp;&gt;svg~*]:pl-10 [&amp;&gt;svg+div]:translate-y-[-3px] [&amp;&gt;svg]:absolute [&amp;&gt;svg]:left-4 [&amp;&gt;svg]:top-4 [&amp;&gt;svg]:w-[23px] [&amp;&gt;svg]:h-[23px] [&amp;&gt;svg]:p-1 [&amp;&gt;svg]:flex [&amp;&gt;svg]:rounded text-foreground bg-alternative border [&amp;&gt;svg]:text-background [&amp;&gt;svg]:bg-foreground"><div class="text-sm [&amp;_p]:leading-relaxed text-foreground-light font-normal"><p class="leading-7 [&amp;:not(:first-child)]:mt-6 text-foreground-light">The Todo List table has Row Level Security (RLS) enabled by default. Feel free disable it
temporarily while testing. With RLS enabled, you will get an <a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2" href="https://supabase.com/docs/guides/troubleshooting/why-is-my-select-returning-an-empty-data-array-and-i-have-data-in-the-table-xvOPgx">empty
array</a>
of results by default. <a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2" href="../../docs/guides/database/postgres/row-level-security.html">Read
more</a> about RLS.</p></div></div>
<h2 class="font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0" id="further-reading"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#further-reading"><span class="icon icon-link"></span></a>Further reading</h2>
<ul class="my-6 ml-6 list-disc text-foreground-light">
<li class="mt-2"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2" href="https://supabase.com/docs/reference/javascript/typescript-support">Generating Typescript types from the database</a></li>
<li class="mt-2"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2" href="https://supabase.com/docs/reference/javascript/select">Supabase Database API</a></li>
<li class="mt-2"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2" href="https://supabase.com/docs/reference/javascript/select#pagination">Supabase Pagination</a></li>
<li class="mt-2"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2" href="https://developer.mozilla.org/en-US/docs/Web/API/Intersection_Observer_API">Intersection Observer API</a></li>
</ul></div></div></div><div class="hidden text-sm xl:block"><div class="sticky top-16 -mt-10 pt-4"><div dir="ltr" class="relative overflow-hidden pb-10" style="position:relative;--radix-scroll-area-corner-width:0px;--radix-scroll-area-corner-height:0px"><style>[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}</style><div data-radix-scroll-area-viewport="" class="h-full w-full rounded-[inherit]" style="overflow-x:hidden;overflow-y:hidden"><div style="min-width:100%;display:table"><div class="sticky top-16 -mt-10 h-[calc(100vh-3.5rem)] py-12"></div></div></div></div></div></div></main><!--$--><!--/$--><!--$--><!--/$--></div></div></div></div></main></div><footer class="py-6 px-4 md:px-8 md:py-0 mx-auto w-full max-w-site"><div class="flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row"><p class="text-balance text-center text-sm leading-loose text-foreground-muted md:text-left">Built by<!-- --> <a href="https://twitter.com/supabase" target="_blank" rel="noreferrer" class="font-medium underline underline-offset-4 hover:text-foreground-lighter">Supabase</a>. The source code is available on<!-- --> <a href="https://github.com/supabase/supabase/tree/master/apps/ui-library" target="_blank" rel="noreferrer" class="font-medium underline underline-offset-4 hover:text-foreground-lighter">GitHub</a>.</p><p class="text-balance text-center text-sm leading-loose text-foreground-muted">Site inspired by<!-- --> <a href="https://www.radix-ui.com/themes/docs/overview/getting-started" target="_blank" rel="noreferrer" class="font-medium underline underline-offset-4 hover:text-foreground-lighter">Radix</a>,<!-- --> <a href="https://ui.shadcn.com" target="_blank" rel="noreferrer" class="font-medium underline underline-offset-4 hover:text-foreground-lighter">shadcn/ui</a>.</p></div></footer><script src="../_next/static/chunks/webpack-e33ce3369a09e8d8.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n3:I[52595,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"4944\",\"static/chunks/app/(app)/layout-68e9ee4f0ef3c93b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"MobileMenuSheet\"]\n4:I[86084,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app"])</script><script>self.__next_f.push([1,"/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"SheetTrigger\"]\n5:I[35621,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"Button\"]\n6:I[86084,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513"])</script><script>self.__next_f.push([1,"\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"SheetContent\"]\n7:I[28694,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"ScrollArea\"]\n8:I[15531,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4"])</script><script>self.__next_f.push([1,"WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"\"]\n9:I[47642,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"4944\",\"static/chunks/app/(app)/layout-68e9ee4f0ef3c93b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"ThemeSwitcherDropdown\"]\na:I[28044,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4"])</script><script>self.__next_f.push([1,"WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"4944\",\"static/chunks/app/(app)/layout-68e9ee4f0ef3c93b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"CommandMenu\"]\nb:I[22792,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"4944\",\"static/chunks/app/(app)/layout-68e9ee4f0ef3c93b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"default\"]\nc:I[93926,[],\"\"]\nd:I[6252,[],\"\"]\ne:I[63645,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv"])</script><script>self.__next_f.push([1,"1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"4944\",\"static/chunks/app/(app)/layout-68e9ee4f0ef3c93b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"TelemetryWrapper\"]\n10:I[18206,[],\"MetadataBoundary\"]\n12:I[18206,[],\"OutletBoundary\"]\n15:I[38670,[],\"AsyncMetadataOutlet\"]\n17:I[18206,[],\"ViewportBoundary\"]\n19:I[47249,[],\"\"]\n:HL[\"/ui/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/ui/_next/static/css/03f63ad6e897ae58.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"style\"]\n:HL[\"/ui/_next/static/css/954cd54edf7b3efb.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"style\"]\n:HL[\"/ui/_next/static/css/be16f9d4f36b2c7a.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"style\"]\n:HL[\"/ui/_next/static/css/99f16e312f80aa1b.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"style\"]\n:HL[\"/ui/_next/static/css/f12065469b9a7bd1.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"InVsSGMudyjao1kx6wnTY\",\"p\":\"/ui\",\"c\":[\"\",\"docs\",\"infinite-query-hook\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"(app)\",{\"children\":[\"docs\",{\"children\":[[\"slug\",\"infinite-query-hook\",\"oc\"],{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/ui/_next/static/css/03f63ad6e897ae58.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/ui/_next/static/css/954cd54edf7b3efb.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"2\",{\"rel\":\"stylesheet\",\"href\":\"/ui/_next/static/css/be16f9d4f36b2c7a.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],\"$L2\"]}],{\"children\":[\"(app)\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/ui/_next/static/css/99f16e312f80aa1b.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[[\"$\",\"div\",null,{\"className\":\"pt-10 md:pt-0\",\"children\":[\"$\",\"main\",null,{\"className\":\"flex-1 max-w-site mx-auto w-full p-0\",\"children\":[\"$\",\"div\",null,{\"className\":\"border-b\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex-1 items-start md:grid md:grid-cols-[220px_minmax(0,1fr)] lg:grid-cols-[240px_minmax(0,1fr)]\",\"children\":[[[\"$\",\"div\",null,{\"className\":\"md:hidden fixed top-0 left-0 right-0 z-50 bg-background justify-between flex items-center px-8 py-3 border-b\",\"children\":[[\"$\",\"$L3\",null,{\"children\":[[\"$\",\"$L4\",null,{\"asChild\":true,\"children\":[\"$\",\"$L5\",null,{\"type\":\"outline\",\"icon\":[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-menu\",\"children\":[[\"$\",\"line\",\"1e0a9i\",{\"x1\":\"4\",\"x2\":\"20\",\"y1\":\"12\",\"y2\":\"12\"}],[\"$\",\"line\",\"1owob3\",{\"x1\":\"4\",\"x2\":\"20\",\"y1\":\"6\",\"y2\":\"6\"}],[\"$\",\"line\",\"yk5zj1\",{\"x1\":\"4\",\"x2\":\"20\",\"y1\":\"18\",\"y2\":\"18\"}],\"$undefined\"]}]}]}],[\"$\",\"$L6\",null,{\"side\":\"left\",\"className\":\"p-0 w-80\",\"showClose\":false,\"children\":[\"$\",\"$L7\",null,{\"className\":\"h-full\",\"children\":[\"$\",\"nav\",null,{\"className\":\"flex flex-col h-full min-w-[220px]\",\"children\":[[\"$\",\"div\",null,{\"className\":\"p-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-start justify-between mb-4\",\"children\":[[\"$\",\"$L8\",null,{\"href\":\"/\",\"children\":[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":\"109\",\"height\":\"113\",\"viewBox\":\"0 0 109 113\",\"fill\":\"none\",\"className\":\"w-6 h-6\",\"children\":[[\"$\",\"path\",null,{\"d\":\"M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0625L99.1935 40.0625C107.384 40.0625 111.952 49.5226 106.859 55.9372L63.7076 110.284Z\",\"fill\":\"url(#paint0_linear)\"}],[\"$\",\"path\",null,{\"d\":\"M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0625L99.1935 40.0625C107.384 40.0625 111.952 49.5226 106.859 55.9372L63.7076 110.284Z\",\"fill\":\"url(#paint1_linear)\",\"fillOpacity\":\"0.2\"}],[\"$\",\"path\",null,{\"d\":\"M45.317 2.07103C48.1765 -1.53037 53.9745 0.442937 54.0434 5.041L54.4849 72.2922H9.83113C1.64038 72.2922 -2.92775 62.8321 2.1655 56.4175L45.317 2.07103Z\",\"fill\":\"#3ECF8E\"}],[\"$\",\"defs\",null,{\"children\":[[\"$\",\"linearGradient\",null,{\"id\":\"paint0_linear\",\"x1\":\"53.9738\",\"y1\":\"54.9738\",\"x2\":\"94.1635\",\"y2\":\"71.8293\",\"gradientUnits\":\"userSpaceOnUse\",\"children\":[[\"$\",\"stop\",null,{\"stopColor\":\"#249361\"}],[\"$\",\"stop\",null,{\"offset\":\"1\",\"stopColor\":\"#3ECF8E\"}]]}],[\"$\",\"linearGradient\",null,{\"id\":\"paint1_linear\",\"x1\":\"36.1558\",\"y1\":\"30.5779\",\"x2\":\"54.4844\",\"y2\":\"65.0804\",\"gradientUnits\":\"userSpaceOnUse\",\"children\":[[\"$\",\"stop\",null,{}],[\"$\",\"stop\",null,{\"offset\":\"1\",\"stopOpacity\":\"0\"}]]}]]}]]}]}],[\"$\",\"$L9\",null,{}]]}],[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"mb-4 block\",\"children\":[\"$\",\"h1\",null,{\"children\":\"Supabase UI Library\"}]}],[\"$\",\"$La\",null,{}]]}],[\"$\",\"div\",null,{\"className\":\"pb-6 space-y-0.5\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest\",\"children\":\"Getting Started\"}],[[\"$\",\"$Lb\",\"/docs/getting-started/introduction-0\",{\"item\":{\"title\":\"Introduction\",\"href\":\"/docs/getting-started/introduction\",\"items\":[],\"commandItemLabel\":\"Introduction\"}}],[\"$\",\"$Lb\",\"/docs/getting-started/quickstart-1\",{\"item\":{\"title\":\"Quick Start\",\"href\":\"/docs/getting-started/quickstart\",\"items\":[],\"commandItemLabel\":\"Quick Start\"}}],[\"$\",\"$Lb\",\"/docs/getting-started/faq-2\",{\"item\":{\"title\":\"FAQ\",\"href\":\"/docs/getting-started/faq\",\"items\":[],\"commandItemLabel\":\"FAQ\"}}]]]}],[\"$\",\"div\",null,{\"className\":\"pb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest\",\"children\":\"Blocks\"}],[\"$\",\"div\",null,{\"className\":\"space-y-0.5\",\"children\":[[\"$\",\"$Lb\",\"/docs/nextjs/client-0\",{\"item\":{\"title\":\"Client\",\"supportedFrameworks\":[\"nextjs\",\"react-router\",\"tanstack\",\"react\"],\"href\":\"/docs/nextjs/client\",\"items\":[],\"commandItemLabel\":\"Supabase Client\"}}],[\"$\",\"$Lb\",\"/docs/nextjs/password-based-auth-1\",{\"item\":{\"title\":\"Password-Based Auth\",\"supportedFrameworks\":[\"nextjs\",\"react-router\",\"tanstack\",\"react\"],\"href\":\"/docs/nextjs/password-based-auth\",\"items\":[],\"commandItemLabel\":\"Password-Based Auth\"}}],[\"$\",\"$Lb\",\"/docs/nextjs/social-auth-2\",{\"item\":{\"title\":\"Social Auth\",\"supportedFrameworks\":[\"nextjs\",\"react-router\",\"tanstack\",\"react\"],\"href\":\"/docs/nextjs/social-auth\",\"items\":[],\"new\":true,\"commandItemLabel\":\"Social Auth\"}}],[\"$\",\"$Lb\",\"/docs/nextjs/dropzone-3\",{\"item\":{\"title\":\"Dropzone\",\"supportedFrameworks\":[\"nextjs\",\"react-router\",\"tanstack\",\"react\"],\"href\":\"/docs/nextjs/dropzone\",\"items\":[],\"commandItemLabel\":\"Dropzone (File Upload)\"}}],[\"$\",\"$Lb\",\"/docs/nextjs/realtime-cursor-4\",{\"item\":{\"title\":\"Realtime Cursor\",\"supportedFrameworks\":[\"nextjs\",\"react-router\",\"tanstack\",\"react\"],\"href\":\"/docs/nextjs/realtime-cursor\",\"items\":[],\"commandItemLabel\":\"Realtime Cursor\"}}],[\"$\",\"$Lb\",\"/docs/nextjs/current-user-avatar-5\",{\"item\":{\"title\":\"Current User Avatar\",\"supportedFrameworks\":[\"nextjs\",\"react-router\",\"tanstack\",\"react\"],\"href\":\"/docs/nextjs/current-user-avatar\",\"items\":[],\"commandItemLabel\":\"Current User Avatar\"}}],[\"$\",\"$Lb\",\"/docs/nextjs/realtime-avatar-stack-6\",{\"item\":{\"title\":\"Realtime Avatar Stack\",\"supportedFrameworks\":[\"nextjs\",\"react-router\",\"tanstack\",\"react\"],\"href\":\"/docs/nextjs/realtime-avatar-stack\",\"items\":[],\"commandItemLabel\":\"Realtime Avatar Stack\"}}],[\"$\",\"$Lb\",\"/docs/nextjs/realtime-chat-7\",{\"item\":{\"title\":\"Realtime Chat\",\"supportedFrameworks\":[\"nextjs\",\"react-router\",\"tanstack\",\"react\"],\"href\":\"/docs/nextjs/realtime-chat\",\"items\":[],\"commandItemLabel\":\"Realtime Chat\"}}],[\"$\",\"$Lb\",\"/docs/infinite-query-hook-8\",{\"item\":{\"title\":\"Infinite Query Hook\",\"supportedFrameworks\":[],\"href\":\"/docs/infinite-query-hook\",\"new\":true,\"items\":[],\"commandItemLabel\":\"Infinite Query Hook\"}}]]}]]}],[\"$\",\"div\",null,{\"className\":\"pb-6 flex-1\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest\",\"children\":\"AI Editors Rules\"}],[[\"$\",\"$Lb\",\"/docs/ai-editors-rules/prompts-0\",{\"item\":{\"title\":\"Prompts\",\"href\":\"/docs/ai-editors-rules/prompts\",\"items\":[],\"commandItemLabel\":\"AI Editors Rules\"}}]]]}],[\"$\",\"div\",null,{\"className\":\"pb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest\",\"children\":\"Platform\"}],[[\"$\",\"$Lb\",\"/docs/platform/platform-kit-0\",{\"item\":{\"title\":\"Platform Kit\",\"href\":\"/docs/platform/platform-kit\",\"items\":[],\"commandItemLabel\":\"Platform Kit\"}}]]]}]]}]}]}]]}],[\"$\",\"$L9\",null,{}]]}],[\"$\",\"aside\",null,{\"className\":\"fixed z-30 top-0 hidden h-screen w-full shrink-0 md:sticky md:block bg-200 border-r border-muted/50\",\"children\":[\"$\",\"$L7\",null,{\"className\":\"h-full\",\"children\":[\"$\",\"nav\",null,{\"className\":\"flex flex-col h-full min-w-[220px]\",\"children\":[[\"$\",\"div\",null,{\"className\":\"p-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-start justify-between mb-4\",\"children\":[[\"$\",\"$L8\",null,{\"href\":\"/\",\"children\":[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":\"109\",\"height\":\"113\",\"viewBox\":\"0 0 109 113\",\"fill\":\"none\",\"className\":\"w-6 h-6\",\"children\":[[\"$\",\"path\",null,{\"d\":\"M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0625L99.1935 40.0625C107.384 40.0625 111.952 49.5226 106.859 55.9372L63.7076 110.284Z\",\"fill\":\"url(#paint0_linear)\"}],[\"$\",\"path\",null,{\"d\":\"M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0625L99.1935 40.0625C107.384 40.0625 111.952 49.5226 106.859 55.9372L63.7076 110.284Z\",\"fill\":\"url(#paint1_linear)\",\"fillOpacity\":\"0.2\"}],[\"$\",\"path\",null,{\"d\":\"M45.317 2.07103C48.1765 -1.53037 53.9745 0.442937 54.0434 5.041L54.4849 72.2922H9.83113C1.64038 72.2922 -2.92775 62.8321 2.1655 56.4175L45.317 2.07103Z\",\"fill\":\"#3ECF8E\"}],[\"$\",\"defs\",null,{\"children\":[[\"$\",\"linearGradient\",null,{\"id\":\"paint0_linear\",\"x1\":\"53.9738\",\"y1\":\"54.9738\",\"x2\":\"94.1635\",\"y2\":\"71.8293\",\"gradientUnits\":\"userSpaceOnUse\",\"children\":[[\"$\",\"stop\",null,{\"stopColor\":\"#249361\"}],[\"$\",\"stop\",null,{\"offset\":\"1\",\"stopColor\":\"#3ECF8E\"}]]}],[\"$\",\"linearGradient\",null,{\"id\":\"paint1_linear\",\"x1\":\"36.1558\",\"y1\":\"30.5779\",\"x2\":\"54.4844\",\"y2\":\"65.0804\",\"gradientUnits\":\"userSpaceOnUse\",\"children\":[[\"$\",\"stop\",null,{}],[\"$\",\"stop\",null,{\"offset\":\"1\",\"stopOpacity\":\"0\"}]]}]]}]]}]}],[\"$\",\"$L9\",null,{}]]}],[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"mb-4 block\",\"children\":[\"$\",\"h1\",null,{\"children\":\"Supabase UI Library\"}]}],[\"$\",\"$La\",null,{}]]}],[\"$\",\"div\",null,{\"className\":\"pb-6 space-y-0.5\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest\",\"children\":\"Getting Started\"}],[[\"$\",\"$Lb\",\"/docs/getting-started/introduction-0\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:1:props:children:1:0:props:item\"}],[\"$\",\"$Lb\",\"/docs/getting-started/quickstart-1\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:1:props:children:1:1:props:item\"}],[\"$\",\"$Lb\",\"/docs/getting-started/faq-2\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:1:props:children:1:2:props:item\"}]]]}],[\"$\",\"div\",null,{\"className\":\"pb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest\",\"children\":\"Blocks\"}],[\"$\",\"div\",null,{\"className\":\"space-y-0.5\",\"children\":[[\"$\",\"$Lb\",\"/docs/nextjs/client-0\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:0:props:item\"}],[\"$\",\"$Lb\",\"/docs/nextjs/password-based-auth-1\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:1:props:item\"}],[\"$\",\"$Lb\",\"/docs/nextjs/social-auth-2\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:2:props:item\"}],[\"$\",\"$Lb\",\"/docs/nextjs/dropzone-3\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:3:props:item\"}],[\"$\",\"$Lb\",\"/docs/nextjs/realtime-cursor-4\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:4:props:item\"}],[\"$\",\"$Lb\",\"/docs/nextjs/current-user-avatar-5\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:5:props:item\"}],[\"$\",\"$Lb\",\"/docs/nextjs/realtime-avatar-stack-6\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:6:props:item\"}],[\"$\",\"$Lb\",\"/docs/nextjs/realtime-chat-7\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:7:props:item\"}],[\"$\",\"$Lb\",\"/docs/infinite-query-hook-8\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:8:props:item\"}]]}]]}],[\"$\",\"div\",null,{\"className\":\"pb-6 flex-1\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest\",\"children\":\"AI Editors Rules\"}],[[\"$\",\"$Lb\",\"/docs/ai-editors-rules/prompts-0\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:3:props:children:1:0:props:item\"}]]]}],[\"$\",\"div\",null,{\"className\":\"pb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest\",\"children\":\"Platform\"}],[[\"$\",\"$Lb\",\"/docs/platform/platform-kit-0\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:4:props:children:1:0:props:item\"}]]]}]]}]}]}]],[\"$\",\"div\",null,{\"vaul-drawer-wrapper\":\"\",\"children\":[\"$\",\"div\",null,{\"className\":\"relative flex min-h-screen flex-col bg-background\",\"children\":[\"$\",\"$Lc\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Ld\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}]}]}]}],[\"$\",\"footer\",null,{\"className\":\"py-6 px-4 md:px-8 md:py-0 mx-auto w-full max-w-site\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row\",\"children\":[[\"$\",\"p\",null,{\"className\":\"text-balance text-center text-sm leading-loose text-foreground-muted md:text-left\",\"children\":[\"Built by\",\" \",[\"$\",\"a\",null,{\"href\":\"https://twitter.com/supabase\",\"target\":\"_blank\",\"rel\":\"noreferrer\",\"className\":\"font-medium underline underline-offset-4 hover:text-foreground-lighter\",\"children\":\"Supabase\"}],\". The source code is available on\",\" \",[\"$\",\"a\",null,{\"href\":\"https://github.com/supabase/supabase/tree/master/apps/ui-library\",\"target\":\"_blank\",\"rel\":\"noreferrer\",\"className\":\"font-medium underline underline-offset-4 hover:text-foreground-lighter\",\"children\":\"GitHub\"}],\".\"]}],[\"$\",\"p\",null,{\"className\":\"text-balance text-center text-sm leading-loose text-foreground-muted\",\"children\":[\"Site inspired by\",\" \",[\"$\",\"a\",null,{\"href\":\"https://www.radix-ui.com/themes/docs/overview/getting-started\",\"target\":\"_blank\",\"rel\":\"noreferrer\",\"className\":\"font-medium underline underline-offset-4 hover:text-foreground-lighter\",\"children\":\"Radix\"}],\",\",\" \",[\"$\",\"a\",null,{\"href\":\"https://ui.shadcn.com\",\"target\":\"_blank\",\"rel\":\"noreferrer\",\"className\":\"font-medium underline underline-offset-4 hover:text-foreground-lighter\",\"children\":\"shadcn/ui\"}],\".\"]}]]}]}],[\"$\",\"$Le\",null,{}]]]}],{\"children\":[\"docs\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Lc\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Ld\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[[\"slug\",\"infinite-query-hook\",\"oc\"],[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Lc\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Ld\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[\"$Lf\",[\"$\",\"$L10\",null,{\"children\":\"$L11\"}],[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/ui/_next/static/css/f12065469b9a7bd1.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"$L12\",null,{\"children\":[\"$L13\",\"$L14\",[\"$\",\"$L15\",null,{\"promise\":\"$@16\"}]]}]]}],{},null,false]},null,false]},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"GhLdAE-Bf14b4GMTlNGxI\",{\"children\":[[\"$\",\"$L17\",null,{\"children\":\"$L18\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$19\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"1a:I[39346,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"TelemetryTagManager\"]\n1b:I[12287,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/ch"])</script><script>self.__next_f.push([1,"unks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"FeatureFlagProvider\"]\n1c:I[22104,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"ThemeProvider\"]\n1d:I[26854,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4"])</script><script>self.__next_f.push([1,"WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"SonnerToaster\"]\n1e:\"$Sreact.suspense\"\n1f:I[38670,[],\"AsyncMetadata\"]\n"])</script><script>self.__next_f.push([1,"2:[\"$\",\"html\",null,{\"lang\":\"en\",\"suppressHydrationWarning\":true,\"children\":[[\"$\",\"head\",null,{}],[\"$\",\"body\",null,{\"className\":\"__className_cbad29 antialiased\",\"children\":[[\"$\",\"$L1a\",null,{}],[\"$\",\"$L1b\",null,{\"API_URL\":\"https://api.supabase.com/platform\",\"children\":[\"$\",\"$L1c\",null,{\"themes\":[\"dark\",\"light\",\"classic-dark\"],\"defaultTheme\":\"system\",\"enableSystem\":true,\"children\":[[\"$\",\"$Lc\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Ld\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:1:props:children:props:children:props:notFound:0:1:props:style\",\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:1:props:children:props:children:props:notFound:0:1:props:children:props:children:1:props:style\",\"children\":404}],[\"$\",\"div\",null,{\"style\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:1:props:children:props:children:props:notFound:0:1:props:children:props:children:2:props:style\",\"children\":[\"$\",\"h2\",null,{\"style\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:1:props:children:props:children:props:notFound:0:1:props:children:props:children:2:props:children:props:style\",\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}],[\"$\",\"$L1d\",null,{}]]}]}]]}]]}]\n"])</script><script>self.__next_f.push([1,"11:[\"$\",\"$1e\",null,{\"fallback\":null,\"children\":[\"$\",\"$L1f\",null,{\"promise\":\"$@20\"}]}]\n"])</script><script>self.__next_f.push([1,"14:null\n"])</script><script>self.__next_f.push([1,"21:I[79904,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"default\"]\n22:I[50619,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2c"])</script><script>self.__next_f.push([1,"b44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"FrameworkSelector\"]\n23:I[16107,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"BlockPreview\"]\n24:I[42988,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL"])</script><script>self.__next_f.push([1,"3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"BlockItem\"]\n26:I[8186,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B"])</script><script>self.__next_f.push([1,"%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"StyleWrapper\"]\n27:I[45719,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"CopyButton\"]\n2a:I[22090,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static"])</script><script>self.__next_f.push([1,"/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"DashboardTableOfContents\"]\n28:Tc4f,"])</script><script>self.__next_f.push([1,"'use client'\n\nimport { cn } from '@/lib/utils'\nimport {\n  SupabaseQueryHandler,\n  SupabaseTableData,\n  SupabaseTableName,\n  useInfiniteQuery,\n} from '@/hooks/use-infinite-query'\nimport * as React from 'react'\n\ninterface InfiniteListProps\u003cTableName extends SupabaseTableName\u003e {\n  tableName: TableName\n  columns?: string\n  pageSize?: number\n  trailingQuery?: SupabaseQueryHandler\u003cTableName\u003e\n  renderItem: (item: SupabaseTableData\u003cTableName\u003e, index: number) =\u003e React.ReactNode\n  className?: string\n  renderNoResults?: () =\u003e React.ReactNode\n  renderEndMessage?: () =\u003e React.ReactNode\n  renderSkeleton?: (count: number) =\u003e React.ReactNode\n}\n\nconst DefaultNoResults = () =\u003e (\n  \u003cdiv className=\"text-center text-muted-foreground py-10\"\u003eNo results.\u003c/div\u003e\n)\n\nconst DefaultEndMessage = () =\u003e (\n  \u003cdiv className=\"text-center text-muted-foreground py-4 text-sm\"\u003eYou\u0026apos;ve reached the end.\u003c/div\u003e\n)\n\nconst defaultSkeleton = (count: number) =\u003e (\n  \u003cdiv className=\"flex flex-col gap-2 px-4\"\u003e\n    {Array.from({ length: count }).map((_, index) =\u003e (\n      \u003cdiv key={index} className=\"h-4 w-full bg-muted animate-pulse\" /\u003e\n    ))}\n  \u003c/div\u003e\n)\n\nexport function InfiniteList\u003cTableName extends SupabaseTableName\u003e({\n  tableName,\n  columns = '*',\n  pageSize = 20,\n  trailingQuery,\n  renderItem,\n  className,\n  renderNoResults = DefaultNoResults,\n  renderEndMessage = DefaultEndMessage,\n  renderSkeleton = defaultSkeleton,\n}: InfiniteListProps\u003cTableName\u003e) {\n  const { data, isFetching, hasMore, fetchNextPage, isSuccess } = useInfiniteQuery({\n    tableName,\n    columns,\n    pageSize,\n    trailingQuery,\n  })\n\n  // Ref for the scrolling container\n  const scrollContainerRef = React.useRef\u003cHTMLDivElement\u003e(null)\n\n  // Intersection observer logic - target the last rendered *item* or a dedicated sentinel\n  const loadMoreSentinelRef = React.useRef\u003cHTMLDivElement\u003e(null)\n  const observer = React.useRef\u003cIntersectionObserver | null\u003e(null)\n\n  React.useEffect(() =\u003e {\n    if (observer.current) observer.current.disconnect()\n\n    observer.current = new IntersectionObserver(\n      (entries) =\u003e {\n        if (entries[0].isIntersecting \u0026\u0026 hasMore \u0026\u0026 !isFetching) {\n          fetchNextPage()\n        }\n      },\n      {\n        root: scrollContainerRef.current, // Use the scroll container for scroll detection\n        threshold: 0.1, // Trigger when 10% of the target is visible\n        rootMargin: '0px 0px 100px 0px', // Trigger loading a bit before reaching the end\n      }\n    )\n\n    if (loadMoreSentinelRef.current) {\n      observer.current.observe(loadMoreSentinelRef.current)\n    }\n\n    return () =\u003e {\n      if (observer.current) observer.current.disconnect()\n    }\n  }, [isFetching, hasMore, fetchNextPage])\n\n  return (\n    \u003cdiv ref={scrollContainerRef} className={cn('relative h-full overflow-auto', className)}\u003e\n      \u003cdiv\u003e\n        {isSuccess \u0026\u0026 data.length === 0 \u0026\u0026 renderNoResults()}\n\n        {data.map((item, index) =\u003e renderItem(item, index))}\n\n        {isFetching \u0026\u0026 renderSkeleton \u0026\u0026 renderSkeleton(pageSize)}\n\n        \u003cdiv ref={loadMoreSentinelRef} style={{ height: '1px' }} /\u003e\n\n        {!hasMore \u0026\u0026 data.length \u003e 0 \u0026\u0026 renderEndMessage()}\n      \u003c/div\u003e\n    \u003c/div\u003e\n  )\n}\n"])</script><script>self.__next_f.push([1,"29:T51a,'use client'\n\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { InfiniteList } from './infinite-component'\nimport { SupabaseQueryHandler } from '@/hooks/use-infinite-query'\nimport { Database } from '@/lib/supabase.types'\n\ntype TodoTask = Database['public']['Tables']['todos']['Row']\n\n// Define how each item should be rendered\nconst renderTodoItem = (todo: TodoTask) =\u003e {\n  return (\n    \u003cdiv\n      key={todo.id}\n      className=\"border-b py-3 px-4 hover:bg-muted flex items-center justify-between\"\n    \u003e\n      \u003cdiv className=\"flex items-center gap-3\"\u003e\n        \u003cCheckbox defaultChecked={todo.is_complete ?? false} /\u003e\n        \u003cdiv\u003e\n          \u003cspan className=\"font-medium text-sm text-foreground\"\u003e{todo.task}\u003c/span\u003e\n          \u003cdiv className=\"text-sm text-muted-foreground\"\u003e\n            {new Date(todo.inserted_at).toLocaleDateString()}\n          \u003c/div\u003e\n        \u003c/div\u003e\n      \u003c/div\u003e\n    \u003c/div\u003e\n  )\n}\n\nconst orderByInsertedAt: SupabaseQueryHandler\u003c'todos'\u003e = (query) =\u003e {\n  return query.order('inserted_at', { ascending: false })\n}\n\nexport const InfiniteListDemo = () =\u003e {\n  return (\n    \u003cdiv className=\"bg-background h-[600px]\"\u003e\n      \u003cInfiniteList\n        tableName=\"todos\"\n        renderItem={renderTodoItem}\n        pageSize={3}\n        trailingQuery={orderByInsertedAt}\n      /\u003e\n    \u003c/div\u003e\n  )\n}\n"])</script><script>self.__next_f.push([1,"f:[\"$\",\"main\",null,{\"className\":\"relative lg:gap-10 xl:grid xl:grid-cols-[1fr_200px] px-8 md:px-16 py-20\",\"children\":[[\"$\",\"div\",null,{\"className\":\"mx-auto w-full min-w-0 max-w-4xl\",\"children\":[[\"$\",\"div\",null,{\"className\":\"mb-4 flex items-center space-x-1 text-sm text-foreground-muted\",\"children\":[[\"$\",\"div\",null,{\"className\":\"overflow-hidden text-ellipsis whitespace-nowrap\",\"children\":\"Docs\"}],[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-chevron-right h-4 w-4 text-foreground-muted\",\"children\":[[\"$\",\"path\",\"mthhwq\",{\"d\":\"m9 18 6-6-6-6\"}],\"$undefined\"]}],[\"$\",\"div\",null,{\"className\":\"text-foreground-lighter\",\"children\":\"Infinite Query Hook\"}]]}],[\"$\",\"div\",null,{\"className\":\"flex flex-col lg:flex-row lg:items-end justify-between mb-5\",\"children\":[[\"$\",\"div\",null,{\"className\":\"space-y-2\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"scroll-m-20 text-2xl lg:text-4xl tracking-tight\",\"children\":\"Infinite Query Hook\"}],[\"$\",\"p\",null,{\"className\":\"text-base lg:text-lg text-foreground-light\",\"children\":[\"$\",\"$L21\",null,{\"children\":\"React hook for infinite lists, fetching data from Supabase.\"}]}]]}],[\"$\",\"$L22\",null,{}]]}],[\"$\",\"div\",null,{\"className\":\"flex flex-col -space-y-px\",\"children\":[\"$undefined\",\"$undefined\",\"$undefined\",\"$undefined\"]}],[\"$\",\"div\",null,{\"className\":\"pb-12\",\"children\":[\"$\",\"div\",null,{\"className\":\"mdx\",\"children\":[[\"$\",\"$L23\",null,{\"name\":\"infinite-list-demo\"}],\"\\n\",[\"$\",\"h2\",null,{\"className\":\"font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0\",\"id\":\"installation\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#installation\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Installation\"]}],\"\\n\",[\"$\",\"$L24\",null,{\"name\":\"infinite-query-hook\",\"description\":\"Installs the Infinite List component and necessary Supabase client setup.\"}],\"\\n\",[\"$\",\"h2\",null,{\"className\":\"font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0\",\"id\":\"folder-structure\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#folder-structure\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Folder structure\"]}],\"\\n\",\"$L25\",\"\\n\",[\"$\",\"h2\",null,{\"className\":\"font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0\",\"id\":\"introduction\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#introduction\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Introduction\"]}],\"\\n\",[\"$\",\"p\",null,{\"className\":\"leading-7 [\u0026:not(:first-child)]:mt-6 text-foreground-light\",\"children\":\"The Infinite Query Hook provides a single React hook which will make it easier to load data progressively from your Supabase database. It handles data fetching and pagination state, It is meant to be used with infinite lists or tables.\\nThe hook is fully typed, provided you have generated and setup your database types.\"}],\"\\n\",[\"$\",\"h2\",null,{\"className\":\"font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0\",\"id\":\"adding-types\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#adding-types\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Adding types\"]}],\"\\n\",[\"$\",\"p\",null,{\"className\":\"leading-7 [\u0026:not(:first-child)]:mt-6 text-foreground-light\",\"children\":[\"Before using this hook, we \",[\"$\",\"strong\",null,{\"children\":\"highly\"}],\" recommend you setup database types in your project. This will make the hook fully-typesafe. More info about generating Typescript types from database schema \",[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2\",\"href\":\"https://supabase.com/docs/guides/api/rest/generating-types\",\"children\":\"here\"}]]}],\"\\n\",[\"$\",\"h2\",null,{\"className\":\"font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0\",\"id\":\"props\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#props\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Props\"]}],\"\\n\",[\"$\",\"div\",null,{\"className\":\"my-6 w-full overflow-y-auto\",\"children\":[\"$\",\"table\",null,{\"className\":\"w-full\",\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"className\":\"m-0 border-t p-0 even:bg-surface-75/75\",\"children\":[[\"$\",\"th\",null,{\"className\":\"border px-4 py-2 text-left font-normal [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":\"Prop\"}],[\"$\",\"th\",null,{\"className\":\"border px-4 py-2 text-left font-normal [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":\"Type\"}],[\"$\",\"th\",null,{\"className\":\"border px-4 py-2 text-left font-normal [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":\"Description\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[[\"$\",\"tr\",null,{\"className\":\"m-0 border-t p-0 even:bg-surface-75/75\",\"children\":[[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"tableName\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"string\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[[\"$\",\"strong\",null,{\"children\":\"Required.\"}],\" The name of the Supabase table to fetch data from.\"]}]]}],[\"$\",\"tr\",null,{\"className\":\"m-0 border-t p-0 even:bg-surface-75/75\",\"children\":[[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"columns\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"string\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"Columns to select from the table. Defaults to \",[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"'*'\"}],\".\"]}]]}],[\"$\",\"tr\",null,{\"className\":\"m-0 border-t p-0 even:bg-surface-75/75\",\"children\":[[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"pageSize\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"number\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"Number of items to fetch per page. Defaults to \",[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"20\"}],\".\"]}]]}],[\"$\",\"tr\",null,{\"className\":\"m-0 border-t p-0 even:bg-surface-75/75\",\"children\":[[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"trailingQuery\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"(query: SupabaseSelectBuilder) =\u003e SupabaseSelectBuilder\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":\"Function to apply filters or sorting to the Supabase query.\"}]]}]]}]]}]}],\"\\n\",[\"$\",\"h2\",null,{\"className\":\"font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0\",\"id\":\"return-type\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#return-type\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Return type\"]}],\"\\n\",[\"$\",\"p\",null,{\"className\":\"leading-7 [\u0026:not(:first-child)]:mt-6 text-foreground-light\",\"children\":\"data, count, isSuccess, isLoading, isFetching, error, hasMore, fetchNextPage\"}],\"\\n\",[\"$\",\"div\",null,{\"className\":\"my-6 w-full overflow-y-auto\",\"children\":[\"$\",\"table\",null,{\"className\":\"w-full\",\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"className\":\"m-0 border-t p-0 even:bg-surface-75/75\",\"children\":[[\"$\",\"th\",null,{\"className\":\"border px-4 py-2 text-left font-normal [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":\"Prop\"}],[\"$\",\"th\",null,{\"className\":\"border px-4 py-2 text-left font-normal [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":\"Type\"}],[\"$\",\"th\",null,{\"className\":\"border px-4 py-2 text-left font-normal [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":\"Description\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[[\"$\",\"tr\",null,{\"className\":\"m-0 border-t p-0 even:bg-surface-75/75\",\"children\":[[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"data\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"TableData[]\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":\"An array of fetched items.\"}]]}],[\"$\",\"tr\",null,{\"className\":\"m-0 border-t p-0 even:bg-surface-75/75\",\"children\":[[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"count\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"number\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"Number of total items in the database. It takes \",[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"trailingQuery\"}],\" into consideration.\"]}]]}],[\"$\",\"tr\",null,{\"className\":\"m-0 border-t p-0 even:bg-surface-75/75\",\"children\":[[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"isSuccess\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"boolean\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":\"It's true if the last API call succeeded.\"}]]}],[\"$\",\"tr\",null,{\"className\":\"m-0 border-t p-0 even:bg-surface-75/75\",\"children\":[[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"isLoading\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"boolean\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":\"It's true only for the initial fetch.\"}]]}],[\"$\",\"tr\",null,{\"className\":\"m-0 border-t p-0 even:bg-surface-75/75\",\"children\":[[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"isFetching\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"boolean\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":\"It's true for the initial and all incremental fetches.\"}]]}],[\"$\",\"tr\",null,{\"className\":\"m-0 border-t p-0 even:bg-surface-75/75\",\"children\":[[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"error\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"any\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":\"The error from the last fetch.\"}]]}],[\"$\",\"tr\",null,{\"className\":\"m-0 border-t p-0 even:bg-surface-75/75\",\"children\":[[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"hasMore\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"boolean\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":\"Whether the query has finished fetching all items from the database\"}]]}],[\"$\",\"tr\",null,{\"className\":\"m-0 border-t p-0 even:bg-surface-75/75\",\"children\":[[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"fetchNextPage\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"() =\u003e void\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":\"Sends a new request for the next items\"}]]}]]}]]}]}],\"\\n\",[\"$\",\"h2\",null,{\"className\":\"font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0\",\"id\":\"type-safety\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#type-safety\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Type safety\"]}],\"\\n\",[\"$\",\"p\",null,{\"className\":\"leading-7 [\u0026:not(:first-child)]:mt-6 text-foreground-light\",\"children\":[\"The hook will use the typed defined on your Supabase client if they're setup (\",[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2\",\"href\":\"https://supabase.com/docs/reference/javascript/typescript-support\",\"children\":\"more info\"}],\").\"]}],\"\\n\",[\"$\",\"p\",null,{\"className\":\"leading-7 [\u0026:not(:first-child)]:mt-6 text-foreground-light\",\"children\":[\"The hook also supports an custom defined result type by using \",[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"useInfiniteQuery\u003cT\u003e\"}],\". For example, if you have a custom type for \",[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"Product\"}],\", you can use it like this \",[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"useInfiniteQuery\u003cProduct\u003e\"}],\".\"]}],\"\\n\",[\"$\",\"h2\",null,{\"className\":\"font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0\",\"id\":\"usage\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#usage\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Usage\"]}],\"\\n\",[\"$\",\"h3\",null,{\"className\":\"font-heading mt-8 scroll-m-20 text-xl tracking-tight\",\"id\":\"with-sorting\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#with-sorting\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"With sorting\"]}],\"\\n\",[\"$\",\"div\",null,{\"data-rehype-pretty-code-fragment\":\"\",\"children\":[\"$\",\"$L26\",null,{\"styleName\":\"$undefined\",\"children\":[[\"$\",\"pre\",null,{\"className\":\"mb-4 mt-6 max-h-[650px] overflow-x-auto rounded-lg border bg-surface-75/75 py-4 text-foreground-light\",\"data-language\":\"tsx\",\"data-theme\":\"default\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"data-language\":\"tsx\",\"data-theme\":\"default\",\"children\":[[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" data\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" fetchNextPage\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" }\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" useInfiniteQuery\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"  tableName\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"products\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"  columns\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"*\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"  pageSize\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" 10\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"  trailingQuery\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" (\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"query\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" query\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"order\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"created_at\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\" ascending\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" false\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" }\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"return\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"  \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"data\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"map\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"item\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"      \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"ProductCard \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"key\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"item\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"id\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\" product\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"item\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" /\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    ))\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Button \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"onClick\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"fetchNextPage\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Load more products\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c/\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Button\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"  \u003c/\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]}]]}]}],[\"$\",\"$L27\",null,{\"value\":\"const { data, fetchNextPage } = useInfiniteQuery({\\n  tableName: 'products',\\n  columns: '*',\\n  pageSize: 10,\\n  trailingQuery: (query) =\u003e query.order('created_at', { ascending: false }),\\n})\\n\\nreturn (\\n  \u003cdiv\u003e\\n    {data.map((item) =\u003e (\\n      \u003cProductCard key={item.id} product={item} /\u003e\\n    ))}\\n    \u003cButton onClick={fetchNextPage}\u003eLoad more products\u003c/Button\u003e\\n  \u003c/div\u003e\\n)\\n\",\"src\":\"$undefined\",\"className\":\"absolute right-4 top-4\"}]]}]}],\"\\n\",[\"$\",\"h3\",null,{\"className\":\"font-heading mt-8 scroll-m-20 text-xl tracking-tight\",\"id\":\"with-filtering-on-search-params\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#with-filtering-on-search-params\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"With filtering on search params\"]}],\"\\n\",[\"$\",\"p\",null,{\"className\":\"leading-7 [\u0026:not(:first-child)]:mt-6 text-foreground-light\",\"children\":[\"This example will filter based on a search param like \",[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"example.com/?q=hello\"}],\".\"]}],\"\\n\",[\"$\",\"div\",null,{\"data-rehype-pretty-code-fragment\":\"\",\"children\":[\"$\",\"$L26\",null,{\"styleName\":\"$undefined\",\"children\":[[\"$\",\"pre\",null,{\"className\":\"mb-4 mt-6 max-h-[650px] overflow-x-auto rounded-lg border bg-surface-75/75 py-4 text-foreground-light\",\"data-language\":\"tsx\",\"data-theme\":\"default\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"data-language\":\"tsx\",\"data-theme\":\"default\",\"children\":[[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" params\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" useSearchParams\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" searchQuery\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" params\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"get\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"q\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" data\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" isLoading\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" isFetching\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" fetchNextPage\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" count\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" isSuccess\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" }\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" useInfiniteQuery\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"  tableName\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"products\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"  columns\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"*\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"  pageSize\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" 10\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"  trailingQuery\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" (\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"query\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"    if\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"searchQuery\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" \u0026\u0026\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" searchQuery\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"length\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" \u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" 0\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\") \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"      query\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" query\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"ilike\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"name\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" `\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"%\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"$${\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"searchQuery\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string)\"},\"children\":\"%\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"`\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    }\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"    return\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" query\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"  },\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"return\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"  \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"data\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"map\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"item\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"      \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"ProductCard \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"key\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"item\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"id\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\" product\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"item\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" /\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    ))\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Button \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"onClick\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"fetchNextPage\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Load more products\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c/\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Button\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"  \u003c/\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]}]]}]}],[\"$\",\"$L27\",null,{\"value\":\"const params = useSearchParams()\\nconst searchQuery = params.get('q')\\n\\nconst { data, isLoading, isFetching, fetchNextPage, count, isSuccess } = useInfiniteQuery({\\n  tableName: 'products',\\n  columns: '*',\\n  pageSize: 10,\\n  trailingQuery: (query) =\u003e {\\n    if (searchQuery \u0026\u0026 searchQuery.length \u003e 0) {\\n      query = query.ilike('name', `%${searchQuery}%`)\\n    }\\n    return query\\n  },\\n})\\n\\nreturn (\\n  \u003cdiv\u003e\\n    {data.map((item) =\u003e (\\n      \u003cProductCard key={item.id} product={item} /\u003e\\n    ))}\\n    \u003cButton onClick={fetchNextPage}\u003eLoad more products\u003c/Button\u003e\\n  \u003c/div\u003e\\n)\\n\",\"src\":\"$undefined\",\"className\":\"absolute right-4 top-4\"}]]}]}],\"\\n\",[\"$\",\"h2\",null,{\"className\":\"font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0\",\"id\":\"reusable-components\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#reusable-components\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Reusable components\"]}],\"\\n\",[\"$\",\"h3\",null,{\"className\":\"font-heading mt-8 scroll-m-20 text-xl tracking-tight\",\"id\":\"infinite-list-fetches-as-you-scroll\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#infinite-list-fetches-as-you-scroll\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Infinite list (fetches as you scroll)\"]}],\"\\n\",[\"$\",\"p\",null,{\"className\":\"leading-7 [\u0026:not(:first-child)]:mt-6 text-foreground-light\",\"children\":\"The following component abstracts the hook into a component. It includes few utility components for no results and end of the list.\"}],\"\\n\",[\"$\",\"div\",null,{\"data-rehype-pretty-code-fragment\":\"\",\"children\":[\"$\",\"$L26\",null,{\"styleName\":\"$undefined\",\"children\":[[\"$\",\"pre\",null,{\"className\":\"mb-4 mt-6 max-h-[650px] overflow-x-auto rounded-lg border bg-surface-75/75 py-4 text-foreground-light\",\"data-language\":\"tsx\",\"data-theme\":\"default\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"data-language\":\"tsx\",\"data-theme\":\"default\",\"children\":[[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"use client\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" cn\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" }\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" from\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"@/lib/utils\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"  SupabaseQueryHandler\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"  SupabaseTableData\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"  SupabaseTableName\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"  useInfiniteQuery\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" from\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"@/hooks/use-infinite-query\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" *\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" as\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" React\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" from\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"react\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"interface\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" InfiniteListProps\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"TableName\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" extends\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" SupabaseTableName\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"  tableName\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" TableName\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"  columns\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"?:\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" string\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"  pageSize\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"?:\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" number\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"  trailingQuery\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"?:\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" SupabaseQueryHandler\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"TableName\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"  renderItem\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" (\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"item\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" SupabaseTableData\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"TableName\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e,\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" index\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" number\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" React\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"ReactNode\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"  className\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"?:\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" string\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"  renderNoResults\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"?:\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" ()\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" React\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"ReactNode\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"  renderEndMessage\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"?:\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" ()\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" React\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"ReactNode\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"  renderSkeleton\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"?:\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" (\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"count\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" number\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" React\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"ReactNode\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" DefaultNoResults\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" ()\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"  \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"className\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"text-center text-muted-foreground py-10\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"No results.\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c/\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" DefaultEndMessage\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" ()\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"  \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"className\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"text-center text-muted-foreground py-4 text-sm\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"You\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u0026\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"apos\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\";\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"ve reached the end.\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c/\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" defaultSkeleton\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" (\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"count\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" number\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"  \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"className\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"flex flex-col gap-2 px-4\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"Array\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"from\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\" length\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" count\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" }\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"map\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"_\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" index\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"      \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"key\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"index\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\" className\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"h-4 w-full bg-muted animate-pulse\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" /\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    ))\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"  \u003c/\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"export\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" function\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" InfiniteList\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"TableName\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" extends\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" SupabaseTableName\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e({\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"  tableName\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"  columns\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"*\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"  pageSize\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" 20\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"  trailingQuery\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"  renderItem\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"  className\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"  renderNoResults\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" DefaultNoResults\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"  renderEndMessage\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" DefaultEndMessage\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"  renderSkeleton\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" defaultSkeleton\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" InfiniteListProps\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"TableName\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e)\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"  const\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" data\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" isFetching\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" hasMore\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" fetchNextPage\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" isSuccess\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" }\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" useInfiniteQuery\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"    tableName\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"    columns\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"    pageSize\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"    trailingQuery\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"  }\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"  // Ref for the scrolling container\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"  const\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" scrollContainerRef\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" React\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"useRef\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"HTMLDivElement\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"null\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"  // Intersection observer logic - target the last rendered *item* or a dedicated sentinel\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"  const\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" loadMoreSentinelRef\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" React\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"useRef\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"HTMLDivElement\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"null\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"  const\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" observer\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" React\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"useRef\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"IntersectionObserver\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" |\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" null\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"null\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"  React\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"useEffect\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"    if\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"observer\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"current\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\") \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"observer\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"current\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"disconnect\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"    observer\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"current\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" new\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" IntersectionObserver\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"      (\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"entries\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"        if\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"entries\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"[\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"0\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"].\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"isIntersecting\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" \u0026\u0026\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" hasMore\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" \u0026\u0026\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" !\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"isFetching\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\") \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"          fetchNextPage\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"        }\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"      },\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"      {\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"        root\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" scrollContainerRef\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"current\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\" // Use the scroll container for scroll detection\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"        threshold\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" 0.1\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\" // Trigger when 10% of the target is visible\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"        rootMargin\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"0px 0px 100px 0px\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\" // Trigger loading a bit before reaching the end\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"      }\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"    )\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"    if\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"loadMoreSentinelRef\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"current\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\") \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"      observer\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"current\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"observe\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"loadMoreSentinelRef\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"current\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    }\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"    return\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" ()\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"      if\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"observer\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"current\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\") \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"observer\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"current\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"disconnect\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    }\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"  },\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" [\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"isFetching\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" hasMore\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" fetchNextPage\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"]\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"  return\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"ref\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"scrollContainerRef\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\" className\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"cn\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"relative h-full overflow-auto\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" className\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"      \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"        {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"isSuccess\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" \u0026\u0026\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" data\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"length\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" ===\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" 0\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" \u0026\u0026\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" renderNoResults\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"        {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"data\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"map\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"item\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" index\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" renderItem\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"item\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" index\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"))\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"        {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"isFetching\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" \u0026\u0026\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" renderSkeleton\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" \u0026\u0026\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" renderSkeleton\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"pageSize\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"        \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"ref\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"loadMoreSentinelRef\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\" style\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\" height\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"1px\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" }}\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" /\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"        {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"!\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"hasMore\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" \u0026\u0026\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" data\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"length\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" \u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" 0\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" \u0026\u0026\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" renderEndMessage\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"      \u003c/\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    \u003c/\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  )\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]}]]}]}],[\"$\",\"$L27\",null,{\"value\":\"$28\",\"src\":\"$undefined\",\"className\":\"absolute right-4 top-4\"}]]}]}],\"\\n\",[\"$\",\"p\",null,{\"className\":\"leading-7 [\u0026:not(:first-child)]:mt-6 text-foreground-light\",\"children\":[\"Use the \",[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"InfiniteList\"}],\" component with the \",[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2\",\"href\":\"https://supabase.com/dashboard/project/_/sql/quickstarts\",\"children\":\"Todo List\"}],\" quickstart.\"]}],\"\\n\",[\"$\",\"p\",null,{\"className\":\"leading-7 [\u0026:not(:first-child)]:mt-6 text-foreground-light\",\"children\":[\"Add \",[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"\u003cInfiniteListDemo /\u003e\"}],\" to a page to see it in action.\\nEnsure the \",[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2\",\"href\":\"https://ui.shadcn.com/docs/components/checkbox\",\"children\":\"Checkbox\"}],\" component from shadcn/ui is installed, and \",[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2\",\"href\":\"https://supabase.com/docs/guides/api/rest/generating-types\",\"children\":\"regenerate/download\"}],\" types after running the quickstart.\"]}],\"\\n\",[\"$\",\"div\",null,{\"data-rehype-pretty-code-fragment\":\"\",\"children\":[\"$\",\"$L26\",null,{\"styleName\":\"$undefined\",\"children\":[[\"$\",\"pre\",null,{\"className\":\"mb-4 mt-6 max-h-[650px] overflow-x-auto rounded-lg border bg-surface-75/75 py-4 text-foreground-light\",\"data-language\":\"tsx\",\"data-theme\":\"default\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"data-language\":\"tsx\",\"data-theme\":\"default\",\"children\":[[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"use client\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" Checkbox\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" }\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" from\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"@/components/ui/checkbox\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" InfiniteList\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" }\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" from\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"./infinite-component\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" SupabaseQueryHandler\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" }\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" from\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"@/hooks/use-infinite-query\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" Database\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" }\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" from\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"@/lib/supabase.types\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"type\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" TodoTask\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" Database\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"[\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"public\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"][\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"Tables\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"][\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"todos\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"][\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"Row\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"]\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"// Define how each item should be rendered\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" renderTodoItem\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" (\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"todo\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" TodoTask\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"  return\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"      key\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"todo\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"id\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"      className\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"border-b py-3 px-4 hover:bg-muted flex items-center justify-between\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    \u003e\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"      \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"className\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"flex items-center gap-3\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"        \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Checkbox \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"defaultChecked\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"todo\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"is_complete\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" ??\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" false\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" /\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"        \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"          \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"span \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"className\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"font-medium text-sm text-foreground\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"todo\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"task\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\u003c/\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"span\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"          \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"className\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"text-sm text-muted-foreground\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"            {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"new\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" Date\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"todo\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"inserted_at\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"toLocaleDateString\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"          \u003c/\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"        \u003c/\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"      \u003c/\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    \u003c/\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  )\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" orderByInsertedAt\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" SupabaseQueryHandler\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"todos\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" (\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"query\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"  return\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" query\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"order\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"inserted_at\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\" ascending\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" false\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" }\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"export\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" const\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" InfiniteListDemo\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" ()\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"  return\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"className\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"bg-background h-[600px]\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"      \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"InfiniteList\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"        tableName\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"todos\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"        renderItem\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"renderTodoItem\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"        pageSize\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"3\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"        trailingQuery\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"orderByInsertedAt\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"      /\u003e\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    \u003c/\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  )\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]}]]}]}],[\"$\",\"$L27\",null,{\"value\":\"$29\",\"src\":\"$undefined\",\"className\":\"absolute right-4 top-4\"}]]}]}],\"\\n\",[\"$\",\"div\",null,{\"ref\":\"$undefined\",\"role\":\"alert\",\"className\":\"relative w-full text-sm rounded-lg p-4 [\u0026\u003esvg~*]:pl-10 [\u0026\u003esvg+div]:translate-y-[-3px] [\u0026\u003esvg]:absolute [\u0026\u003esvg]:left-4 [\u0026\u003esvg]:top-4 [\u0026\u003esvg]:w-[23px] [\u0026\u003esvg]:h-[23px] [\u0026\u003esvg]:p-1 [\u0026\u003esvg]:flex [\u0026\u003esvg]:rounded text-foreground bg-alternative border [\u0026\u003esvg]:text-background [\u0026\u003esvg]:bg-foreground\",\"children\":[\"$undefined\",\"$undefined\",[\"$\",\"div\",null,{\"ref\":\"$undefined\",\"className\":\"text-sm [\u0026_p]:leading-relaxed text-foreground-light font-normal\",\"children\":[\"$\",\"p\",null,{\"className\":\"leading-7 [\u0026:not(:first-child)]:mt-6 text-foreground-light\",\"children\":[\"The Todo List table has Row Level Security (RLS) enabled by default. Feel free disable it\\ntemporarily while testing. With RLS enabled, you will get an \",[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2\",\"href\":\"https://supabase.com/docs/guides/troubleshooting/why-is-my-select-returning-an-empty-data-array-and-i-have-data-in-the-table-xvOPgx\",\"children\":\"empty\\narray\"}],\"\\nof results by default. \",[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2\",\"href\":\"https://supabase.com/docs/guides/database/postgres/row-level-security\",\"children\":\"Read\\nmore\"}],\" about RLS.\"]}]}]]}],\"\\n\",[\"$\",\"h2\",null,{\"className\":\"font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0\",\"id\":\"further-reading\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#further-reading\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Further reading\"]}],\"\\n\",[\"$\",\"ul\",null,{\"className\":\"my-6 ml-6 list-disc text-foreground-light\",\"children\":[\"\\n\",[\"$\",\"li\",null,{\"className\":\"mt-2\",\"children\":[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2\",\"href\":\"https://supabase.com/docs/reference/javascript/typescript-support\",\"children\":\"Generating Typescript types from the database\"}]}],\"\\n\",[\"$\",\"li\",null,{\"className\":\"mt-2\",\"children\":[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2\",\"href\":\"https://supabase.com/docs/reference/javascript/select\",\"children\":\"Supabase Database API\"}]}],\"\\n\",[\"$\",\"li\",null,{\"className\":\"mt-2\",\"children\":[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2\",\"href\":\"https://supabase.com/docs/reference/javascript/select#pagination\",\"children\":\"Supabase Pagination\"}]}],\"\\n\",[\"$\",\"li\",null,{\"className\":\"mt-2\",\"children\":[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2\",\"href\":\"https://developer.mozilla.org/en-US/docs/Web/API/Intersection_Observer_API\",\"children\":\"Intersection Observer API\"}]}],\"\\n\"]}]]}]}]]}],[\"$\",\"div\",null,{\"className\":\"hidden text-sm xl:block\",\"children\":[\"$\",\"div\",null,{\"className\":\"sticky top-16 -mt-10 pt-4\",\"children\":[\"$\",\"$L7\",null,{\"className\":\"pb-10\",\"children\":[\"$\",\"div\",null,{\"className\":\"sticky top-16 -mt-10 h-[calc(100vh-3.5rem)] py-12\",\"children\":[\"$\",\"$L2a\",null,{\"toc\":{\"items\":[{\"url\":\"#installation\",\"title\":\"Installation\"},{\"url\":\"#folder-structure\",\"title\":\"Folder structure\"},{\"url\":\"#introduction\",\"title\":\"Introduction\"},{\"url\":\"#adding-types\",\"title\":\"Adding types\"},{\"url\":\"#props\",\"title\":\"Props\"},{\"url\":\"#return-type\",\"title\":\"Return type\"},{\"url\":\"#type-safety\",\"title\":\"Type safety\"},{\"url\":\"#usage\",\"title\":\"Usage\",\"items\":[{\"url\":\"#with-sorting\",\"title\":\"With sorting\"},{\"url\":\"#with-filtering-on-search-params\",\"title\":\"With filtering on search params\"}]},{\"url\":\"#reusable-components\",\"title\":\"Reusable components\",\"items\":[{\"url\":\"#infinite-list-fetches-as-you-scroll\",\"title\":\"Infinite list (fetches as you scroll)\"}]},{\"url\":\"#further-reading\",\"title\":\"Further reading\"}]}}]}]}]}]}]]}]\n"])</script><script>self.__next_f.push([1,"2b:I[38866,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"BlockItemCode\"]\n2c:T1712,"])</script><script>self.__next_f.push([1,"'use client'\n\nimport { createClient } from '@/lib/supabase/client'\nimport { PostgrestQueryBuilder } from '@supabase/postgrest-js'\nimport { SupabaseClient } from '@supabase/supabase-js'\nimport { useEffect, useRef, useSyncExternalStore } from 'react'\n\nconst supabase = createClient()\n\n// The following types are used to make the hook type-safe. It extracts the database type from the supabase client.\ntype SupabaseClientType = typeof supabase\n\n// Utility type to check if the type is any\ntype IfAny\u003cT, Y, N\u003e = 0 extends 1 \u0026 T ? Y : N\n\n// Extracts the database type from the supabase client. If the supabase client doesn't have a type, it will fallback properly.\ntype Database =\n  SupabaseClientType extends SupabaseClient\u003cinfer U\u003e\n    ? IfAny\u003c\n        U,\n        {\n          public: {\n            Tables: Record\u003cstring, any\u003e\n            Views: Record\u003cstring, any\u003e\n            Functions: Record\u003cstring, any\u003e\n          }\n        },\n        U\n      \u003e\n    : never\n\n// Change this to the database schema you want to use\ntype DatabaseSchema = Database['public']\n\n// Extracts the table names from the database type\ntype SupabaseTableName = keyof DatabaseSchema['Tables']\n\n// Extracts the table definition from the database type\ntype SupabaseTableData\u003cT extends SupabaseTableName\u003e = DatabaseSchema['Tables'][T]['Row']\n\ntype SupabaseSelectBuilder\u003cT extends SupabaseTableName\u003e = ReturnType\u003c\n  PostgrestQueryBuilder\u003cDatabaseSchema, DatabaseSchema['Tables'][T], T\u003e['select']\n\u003e\n\n// A function that modifies the query. Can be used to sort, filter, etc. If .range is used, it will be overwritten.\ntype SupabaseQueryHandler\u003cT extends SupabaseTableName\u003e = (\n  query: SupabaseSelectBuilder\u003cT\u003e\n) =\u003e SupabaseSelectBuilder\u003cT\u003e\n\ninterface UseInfiniteQueryProps\u003cT extends SupabaseTableName, Query extends string = '*'\u003e {\n  // The table name to query\n  tableName: T\n  // The columns to select, defaults to `*`\n  columns?: string\n  // The number of items to fetch per page, defaults to `20`\n  pageSize?: number\n  // A function that modifies the query. Can be used to sort, filter, etc. If .range is used, it will be overwritten.\n  trailingQuery?: SupabaseQueryHandler\u003cT\u003e\n}\n\ninterface StoreState\u003cTData\u003e {\n  data: TData[]\n  count: number\n  isSuccess: boolean\n  isLoading: boolean\n  isFetching: boolean\n  error: Error | null\n  hasInitialFetch: boolean\n}\n\ntype Listener = () =\u003e void\n\nfunction createStore\u003cTData extends SupabaseTableData\u003cT\u003e, T extends SupabaseTableName\u003e(\n  props: UseInfiniteQueryProps\u003cT\u003e\n) {\n  const { tableName, columns = '*', pageSize = 20, trailingQuery } = props\n\n  let state: StoreState\u003cTData\u003e = {\n    data: [],\n    count: 0,\n    isSuccess: false,\n    isLoading: false,\n    isFetching: false,\n    error: null,\n    hasInitialFetch: false,\n  }\n\n  const listeners = new Set\u003cListener\u003e()\n\n  const notify = () =\u003e {\n    listeners.forEach((listener) =\u003e listener())\n  }\n\n  const setState = (newState: Partial\u003cStoreState\u003cTData\u003e\u003e) =\u003e {\n    state = { ...state, ...newState }\n    notify()\n  }\n\n  const fetchPage = async (skip: number) =\u003e {\n    if (state.hasInitialFetch \u0026\u0026 (state.isFetching || state.count \u003c= state.data.length)) return\n\n    setState({ isFetching: true })\n\n    let query = supabase\n      .from(tableName)\n      .select(columns, { count: 'exact' }) as unknown as SupabaseSelectBuilder\u003cT\u003e\n\n    if (trailingQuery) {\n      query = trailingQuery(query)\n    }\n    const { data: newData, count, error } = await query.range(skip, skip + pageSize - 1)\n\n    if (error) {\n      console.error('An unexpected error occurred:', error)\n      setState({ error })\n    } else {\n      const deduplicatedData = ((newData || []) as TData[]).filter(\n        (item) =\u003e !state.data.find((old) =\u003e old.id === item.id)\n      )\n\n      setState({\n        data: [...state.data, ...deduplicatedData],\n        count: count || 0,\n        isSuccess: true,\n        error: null,\n      })\n    }\n    setState({ isFetching: false })\n  }\n\n  const fetchNextPage = async () =\u003e {\n    if (state.isFetching) return\n    await fetchPage(state.data.length)\n  }\n\n  const initialize = async () =\u003e {\n    setState({ isLoading: true, isSuccess: false, data: [] })\n    await fetchNextPage()\n    setState({ isLoading: false, hasInitialFetch: true })\n  }\n\n  return {\n    getState: () =\u003e state,\n    subscribe: (listener: Listener) =\u003e {\n      listeners.add(listener)\n      return () =\u003e listeners.delete(listener)\n    },\n    fetchNextPage,\n    initialize,\n  }\n}\n\n// Empty initial state to avoid hydration errors.\nconst initialState: any = {\n  data: [],\n  count: 0,\n  isSuccess: false,\n  isLoading: false,\n  isFetching: false,\n  error: null,\n  hasInitialFetch: false,\n}\n\nfunction useInfiniteQuery\u003c\n  TData extends SupabaseTableData\u003cT\u003e,\n  T extends SupabaseTableName = SupabaseTableName,\n\u003e(props: UseInfiniteQueryProps\u003cT\u003e) {\n  const storeRef = useRef(createStore\u003cTData, T\u003e(props))\n\n  const state = useSyncExternalStore(\n    storeRef.current.subscribe,\n    () =\u003e storeRef.current.getState(),\n    () =\u003e initialState as StoreState\u003cTData\u003e\n  )\n\n  useEffect(() =\u003e {\n    // Recreate store if props change\n    if (\n      storeRef.current.getState().hasInitialFetch \u0026\u0026\n      (props.tableName !== props.tableName ||\n        props.columns !== props.columns ||\n        props.pageSize !== props.pageSize)\n    ) {\n      storeRef.current = createStore\u003cTData, T\u003e(props)\n    }\n\n    if (!state.hasInitialFetch \u0026\u0026 typeof window !== 'undefined') {\n      storeRef.current.initialize()\n    }\n  }, [props.tableName, props.columns, props.pageSize, state.hasInitialFetch])\n\n  return {\n    data: state.data,\n    count: state.count,\n    isSuccess: state.isSuccess,\n    isLoading: state.isLoading,\n    isFetching: state.isFetching,\n    error: state.error,\n    hasMore: state.count \u003e state.data.length,\n    fetchNextPage: storeRef.current.fetchNextPage,\n  }\n}\n\nexport {\n  useInfiniteQuery,\n  type SupabaseQueryHandler,\n  type SupabaseTableData,\n  type SupabaseTableName,\n  type UseInfiniteQueryProps,\n}\n"])</script><script>self.__next_f.push([1,"25:[\"$\",\"$L2b\",null,{\"files\":[{\"name\":\"hooks\",\"path\":\"/hooks\",\"originalPath\":\"registry/default/blocks/infinite-query-hook/hooks/use-infinite-query.ts\",\"type\":\"directory\",\"children\":[{\"name\":\"use-infinite-query.ts\",\"path\":\"/hooks/use-infinite-query.ts\",\"originalPath\":\"registry/default/blocks/infinite-query-hook/hooks/use-infinite-query.ts\",\"type\":\"file\",\"content\":\"$2c\"}]}]}]\n"])</script><script>self.__next_f.push([1,"18:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n13:null\n"])</script><script>self.__next_f.push([1,"20:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Infinite Query Hook\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"React hook for infinite lists, fetching data from Supabase.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"application-name\",\"content\":\"Supabase UI Library\"}],[\"$\",\"meta\",\"3\",{\"property\":\"og:title\",\"content\":\"Infinite Query Hook\"}],[\"$\",\"meta\",\"4\",{\"property\":\"og:description\",\"content\":\"React hook for infinite lists, fetching data from Supabase.\"}],[\"$\",\"meta\",\"5\",{\"property\":\"og:url\",\"content\":\"https://supabase.com/undefined/docs/infinite-query-hook\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:image\",\"content\":\"https://supabase.com/ui/img/supabase-og-image.png\"}],[\"$\",\"meta\",\"7\",{\"property\":\"og:type\",\"content\":\"article\"}],[\"$\",\"meta\",\"8\",{\"property\":\"article:published_time\",\"content\":\"2025-07-30T14:07:09.974Z\"}],[\"$\",\"meta\",\"9\",{\"property\":\"article:modified_time\",\"content\":\"2025-07-30T14:07:09.974Z\"}],[\"$\",\"meta\",\"10\",{\"property\":\"article:author\",\"content\":\"Supabase\"}],[\"$\",\"meta\",\"11\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"12\",{\"name\":\"twitter:site\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"13\",{\"name\":\"twitter:creator\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"14\",{\"name\":\"twitter:title\",\"content\":\"Infinite Query Hook\"}],[\"$\",\"meta\",\"15\",{\"name\":\"twitter:description\",\"content\":\"React hook for infinite lists, fetching data from Supabase.\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:image\",\"content\":\"https://supabase.com/ui/img/supabase-og-image.png\"}],[\"$\",\"link\",\"17\",{\"rel\":\"shortcut icon\",\"href\":\"/ui/favicon/favicon.ico\"}],[\"$\",\"link\",\"18\",{\"rel\":\"icon\",\"href\":\"/ui/favicon/favicon.ico\",\"type\":\"image/x-icon\"}],[\"$\",\"link\",\"19\",{\"rel\":\"apple-touch-icon\",\"href\":\"/ui/favicon/favicon.ico\"}],[\"$\",\"link\",\"20\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/ui/favicon/apple-icon-57x57.png\",\"sizes\":\"57x57\"}],[\"$\",\"link\",\"21\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/ui/favicon/apple-icon-60x60.png\",\"sizes\":\"60x60\"}],[\"$\",\"link\",\"22\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/ui/favicon/apple-icon-72x72.png\",\"sizes\":\"72x72\"}],[\"$\",\"link\",\"23\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/ui/favicon/apple-icon-76x76.png\",\"sizes\":\"76x76\"}],[\"$\",\"link\",\"24\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/ui/favicon/apple-icon-114x114.png\",\"sizes\":\"114x114\"}],[\"$\",\"link\",\"25\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/ui/favicon/apple-icon-120x120.png\",\"sizes\":\"120x120\"}],[\"$\",\"link\",\"26\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/ui/favicon/apple-icon-144x144.png\",\"sizes\":\"144x144\"}],[\"$\",\"link\",\"27\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/ui/favicon/apple-icon-152x152.png\",\"sizes\":\"152x152\"}],[\"$\",\"link\",\"28\",{\"rel\":\"icon\",\"href\":\"/ui/favicon/favicon-16x16.png\",\"type\":\"image/png\",\"sizes\":\"16x16\"}],[\"$\",\"link\",\"29\",{\"rel\":\"icon\",\"href\":\"/ui/favicon/favicon-32x32.png\",\"type\":\"image/png\",\"sizes\":\"32x32\"}],[\"$\",\"link\",\"30\",{\"rel\":\"icon\",\"href\":\"/ui/favicon/favicon-48x48.png\",\"type\":\"image/png\",\"sizes\":\"48x48\"}],[\"$\",\"link\",\"31\",{\"rel\":\"icon\",\"href\":\"/ui/favicon/favicon-96x96.png\",\"type\":\"image/png\",\"sizes\":\"96x96\"}],[\"$\",\"link\",\"32\",{\"rel\":\"icon\",\"href\":\"/ui/favicon/favicon-128x128.png\",\"type\":\"image/png\",\"sizes\":\"128x128\"}],[\"$\",\"link\",\"33\",{\"rel\":\"icon\",\"href\":\"/ui/favicon/favicon-180x180.png\",\"type\":\"image/png\",\"sizes\":\"180x180\"}],[\"$\",\"link\",\"34\",{\"rel\":\"icon\",\"href\":\"/ui/favicon/favicon-196x196.png\",\"type\":\"image/png\",\"sizes\":\"196x196\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"16:{\"metadata\":\"$20:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>