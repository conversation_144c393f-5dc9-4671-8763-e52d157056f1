<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="https://supabase.com/ui/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" type="font/woff2"/><link rel="stylesheet" href="../../_next/static/css/03f63ad6e897ae58.css" data-precedence="next"/><link rel="stylesheet" href="../../_next/static/css/954cd54edf7b3efb.css" data-precedence="next"/><link rel="stylesheet" href="../../_next/static/css/be16f9d4f36b2c7a.css" data-precedence="next"/><link rel="stylesheet" href="../../_next/static/css/99f16e312f80aa1b.css" data-precedence="next"/><link rel="stylesheet" href="https://supabase.com/ui/_next/static/css/f12065469b9a7bd1.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="../../_next/static/chunks/webpack-e33ce3369a09e8d8.js"/><script src="../../_next/static/chunks/1c2ff98c-7d3bc7c78beee601.js" async=""></script><script src="../../_next/static/chunks/2053-b9dd66de32bef85e.js" async=""></script><script src="../../_next/static/chunks/main-app-acf3b350459af11a.js" async=""></script><script src="../../_next/static/chunks/6985-d9c94a68747ee58b.js" async=""></script><script src="../../_next/static/chunks/2105-5c6fb0a066c1340f.js" async=""></script><script src="../../_next/static/chunks/3502-8ffc67f87c6f59da.js" async=""></script><script src="../../_next/static/chunks/6276-e32f7d13c5c52f3b.js" async=""></script><script src="../../_next/static/chunks/6701-6b908838eebce949.js" async=""></script><script src="../../_next/static/chunks/6602-18415a95b5bdd8fe.js" async=""></script><script src="../../_next/static/chunks/5710-2cb44b983b4cfc0c.js" async=""></script><script src="../../_next/static/chunks/8443-3b56b7405ddfa048.js" async=""></script><script src="../../_next/static/chunks/494-582b02a90a51684d.js" async=""></script><script src="../../_next/static/chunks/app/(app)/layout-68e9ee4f0ef3c93b.js" async=""></script><script src="../../_next/static/chunks/3276-a6118a55186eb94a.js" async=""></script><script src="../../_next/static/chunks/898-7244b379490d4af4.js" async=""></script><script src="https://supabase.com/ui/_next/static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3" async=""></script><script src="../../_next/static/chunks/8815-1dd29c457db8514c.js" async=""></script><script src="https://supabase.com/ui/_next/static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3" async=""></script><meta name="next-size-adjust" content=""/><title>Current User Avatar</title><meta name="description" content="Supabase Auth-aware avatar"/><meta name="application-name" content="Supabase UI Library"/><meta property="og:title" content="Current User Avatar"/><meta property="og:description" content="Supabase Auth-aware avatar"/><meta property="og:url" content="https://supabase.com/undefined/docs/nextjs/current-user-avatar"/><meta property="og:image" content="https://supabase.com/ui/img/supabase-og-image.png"/><meta property="og:type" content="article"/><meta property="article:published_time" content="2025-07-30T14:07:10.034Z"/><meta property="article:modified_time" content="2025-07-30T14:07:10.034Z"/><meta property="article:author" content="Supabase"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:site" content="@supabase"/><meta name="twitter:creator" content="@supabase"/><meta name="twitter:title" content="Current User Avatar"/><meta name="twitter:description" content="Supabase Auth-aware avatar"/><meta name="twitter:image" content="https://supabase.com/ui/img/supabase-og-image.png"/><link rel="shortcut icon" href="../../favicon/favicon.ico"/><link rel="icon" href="../../favicon/favicon.ico" type="image/x-icon"/><link rel="apple-touch-icon" href="../../favicon/favicon.ico"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-57x57.png" sizes="57x57"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-60x60.png" sizes="60x60"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-72x72.png" sizes="72x72"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-76x76.png" sizes="76x76"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-114x114.png" sizes="114x114"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-120x120.png" sizes="120x120"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-144x144.png" sizes="144x144"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-152x152.png" sizes="152x152"/><link rel="icon" href="../../favicon/favicon-16x16.png" type="image/png" sizes="16x16"/><link rel="icon" href="../../favicon/favicon-32x32.png" type="image/png" sizes="32x32"/><link rel="icon" href="../../favicon/favicon-48x48.png" type="image/png" sizes="48x48"/><link rel="icon" href="../../favicon/favicon-96x96.png" type="image/png" sizes="96x96"/><link rel="icon" href="https://supabase.com/ui/favicon/favicon-128x128.png" type="image/png" sizes="128x128"/><link rel="icon" href="../../favicon/favicon-180x180.png" type="image/png" sizes="180x180"/><link rel="icon" href="../../favicon/favicon-196x196.png" type="image/png" sizes="196x196"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="../../_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_cbad29 antialiased"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&true)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}if(e==='light'||e==='dark')d.style.colorScheme=e}catch(e){}}()</script><div class="pt-10 md:pt-0"><main class="flex-1 max-w-site mx-auto w-full p-0"><div class="border-b"><div class="flex-1 items-start md:grid md:grid-cols-[220px_minmax(0,1fr)] lg:grid-cols-[240px_minmax(0,1fr)]"><div class="md:hidden fixed top-0 left-0 right-0 z-50 bg-background justify-between flex items-center px-8 py-3 border-b"><button data-size="tiny" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-«R2lmqlb»" data-state="closed" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-transparent border-strong hover:border-foreground-muted focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-xs px-2.5 py-1 h-[26px]"><div class="[&amp;_svg]:h-[14px] [&amp;_svg]:w-[14px] text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></div> <!-- --> </button></div><aside class="fixed z-30 top-0 hidden h-screen w-full shrink-0 md:sticky md:block bg-200 border-r border-muted/50"><div dir="ltr" class="relative overflow-hidden h-full" style="position:relative;--radix-scroll-area-corner-width:0px;--radix-scroll-area-corner-height:0px"><style>[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}</style><div data-radix-scroll-area-viewport="" class="h-full w-full rounded-[inherit]" style="overflow-x:hidden;overflow-y:hidden"><div style="min-width:100%;display:table"><nav class="flex flex-col h-full min-w-[220px]"><div class="p-6"><div class="flex items-start justify-between mb-4"><a href="../../../ui.html"><svg xmlns="http://www.w3.org/2000/svg" width="109" height="113" viewBox="0 0 109 113" fill="none" class="w-6 h-6"><path d="M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0625L99.1935 40.0625C107.384 40.0625 111.952 49.5226 106.859 55.9372L63.7076 110.284Z" fill="url(#paint0_linear)"></path><path d="M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0625L99.1935 40.0625C107.384 40.0625 111.952 49.5226 106.859 55.9372L63.7076 110.284Z" fill="url(#paint1_linear)" fill-opacity="0.2"></path><path d="M45.317 2.07103C48.1765 -1.53037 53.9745 0.442937 54.0434 5.041L54.4849 72.2922H9.83113C1.64038 72.2922 -2.92775 62.8321 2.1655 56.4175L45.317 2.07103Z" fill="#3ECF8E"></path><defs><linearGradient id="paint0_linear" x1="53.9738" y1="54.9738" x2="94.1635" y2="71.8293" gradientUnits="userSpaceOnUse"><stop stop-color="#249361"></stop><stop offset="1" stop-color="#3ECF8E"></stop></linearGradient><linearGradient id="paint1_linear" x1="36.1558" y1="30.5779" x2="54.4844" y2="65.0804" gradientUnits="userSpaceOnUse"><stop></stop><stop offset="1" stop-opacity="0"></stop></linearGradient></defs></svg></a></div><a class="mb-4 block" href="../../../ui.html"><h1>Supabase UI Library</h1></a><button data-size="tiny" type="button" class="cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border border-strong focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong px-2.5 py-1 relative h-8 w-full justify-start rounded-[0.5rem] bg-background text-sm font-normal text-foreground-muted shadow-none sm:pr-12 hover:border-foreground-muted hover:bg-surface-100 hover:text-foreground-lighter"> <span class="truncate"><span class="hidden lg:inline-flex">Search UI Library...</span><span class="inline-flex lg:hidden">Search...</span><kbd class="pointer-events-none absolute right-[0.3rem] top-[0.3rem] hidden h-5 select-none items-center gap-1 rounded border bg-surface-200 px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex text-foreground-light"><span class="text-sm">⌘</span>K</kbd></span> </button></div><div class="pb-6 space-y-0.5"><div class="font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest">Getting Started</div><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="../getting-started/introduction.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Introduction</a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="../getting-started/quickstart.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Quick Start</a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="../getting-started/faq.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>FAQ</a></div><div class="pb-6"><div class="font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest">Blocks</div><div class="space-y-0.5"><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="client.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Client</a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="password-based-auth.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Password-Based Auth</a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="social-auth.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Social Auth<div class="inline-flex items-center rounded-full bg-opacity-10 bg-brand text-brand-600 border border-brand-500 px-2.5 py-0.5 text-xs capitalize">NEW</div></a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="dropzone.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Dropzone</a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="realtime-cursor.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Realtime Cursor</a><a class="relative flex items-center justify-between h-6 text-sm px-6 bg-surface-200 text-foreground transition-all" href="current-user-avatar.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-100"></div>Current User Avatar</a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="https://supabase.com/ui/docs/nextjs/realtime-avatar-stack"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Realtime Avatar Stack</a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="https://supabase.com/ui/docs/nextjs/realtime-chat"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Realtime Chat</a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="https://supabase.com/ui/docs/infinite-query-hook"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Infinite Query Hook<div class="inline-flex items-center rounded-full bg-opacity-10 bg-brand text-brand-600 border border-brand-500 px-2.5 py-0.5 text-xs capitalize">NEW</div></a></div></div><div class="pb-6 flex-1"><div class="font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest">AI Editors Rules</div><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="https://supabase.com/ui/docs/ai-editors-rules/prompts"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Prompts</a></div><div class="pb-6"><div class="font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest">Platform</div><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="https://supabase.com/ui/docs/platform/platform-kit"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Platform Kit</a></div></nav></div></div></div></aside><div vaul-drawer-wrapper=""><div class="relative flex min-h-screen flex-col bg-background"><main class="relative lg:gap-10 xl:grid xl:grid-cols-[1fr_200px] px-8 md:px-16 py-20"><div class="mx-auto w-full min-w-0 max-w-4xl"><div class="mb-4 flex items-center space-x-1 text-sm text-foreground-muted"><div class="overflow-hidden text-ellipsis whitespace-nowrap">Docs</div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right h-4 w-4 text-foreground-muted"><path d="m9 18 6-6-6-6"></path></svg><div class="text-foreground-lighter">Current User Avatar</div></div><div class="flex flex-col lg:flex-row lg:items-end justify-between mb-5"><div class="space-y-2"><h1 class="scroll-m-20 text-2xl lg:text-4xl tracking-tight">Current User Avatar</h1><p class="text-base lg:text-lg text-foreground-light"><span data-br="«R154utpmqlb»" data-brr="1" style="display:inline-block;vertical-align:top;text-decoration:inherit;text-wrap:balance">Supabase Auth-aware avatar</span><script>self.__wrap_n=self.__wrap_n||(self.CSS&&CSS.supports("text-wrap","balance")?1:2);self.__wrap_b=(e,n,t)=>{let o=(t=t||document.querySelector(`[data-br="${e}"]`)).parentElement,r=e=>t.style.maxWidth=e+"px";t.style.maxWidth="";let s=o.clientWidth,a=o.clientHeight,c=s/2-.25,l=s+.5,i;if(s){for(r(c),c=Math.max(t.scrollWidth,c);c+1<l;)r(i=Math.round((c+l)/2)),o.clientHeight===a?l=i:c=i;r(l*n+s*(1-n))}t.__wrap_o||"undefined"!=typeof ResizeObserver&&(t.__wrap_o=new ResizeObserver(()=>{self.__wrap_b(0,+t.dataset.brr,t)})).observe(o)};self.__wrap_n!=1&&self.__wrap_b("«R154utpmqlb»",1)</script></p></div><button type="button" role="combobox" aria-controls="radix-«R94utpmqlb»" aria-expanded="false" aria-autocomplete="none" dir="ltr" data-state="closed" class="flex items-center justify-between rounded-md border border-strong hover:border-stronger bg-alternative dark:bg-muted hover:bg-selection ring-offset-background-control data-[placeholder]:text-foreground-lighter focus:outline-none ring-border-control focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200 data-[state=open]:bg-selection data-[state=open]:border-stronger gap-2 text-sm leading-4 px-3 py-2 h-[34px] w-[180px] mt-4 lg:mt-0"><span style="pointer-events:none"></span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-4 w-4 text-foreground-lighter" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><select aria-hidden="true" tabindex="-1" style="position:absolute;border:0;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;word-wrap:normal"></select></div><div class="flex flex-col -space-y-px"></div><div class="pb-12"><div class="mdx"><div class="mt-4 mb-12"><div class="relative bg-studio rounded-md border"><div class="z-0 pointer-events-none absolute h-full w-full bg-[radial-gradient(hsla(var(--foreground-default)/0.02)_1px,transparent_1px)] [background-size:16px_16px] [mask-image:radial-gradient(ellipse_50%_50%_at_50%_50%,#000_70%,transparent_100%)]"></div><div class="z-10 relative"><div class="preview flex min-h-[350px] w-full justify-center p-10 theme-original items-center"><!--$--><div class="flex flex-col gap-4 items-center justify-center"><span class="relative flex h-12 w-12 shrink-0 overflow-hidden rounded-full"><span class="flex h-full w-full items-center justify-center rounded-full bg-muted">?</span></span><span class="text-sm text-foreground-light">It seems like you&#x27;re not logged in. Login via the<!-- --> <a target="_blank" rel="noopener noreferrer" href="https://supabase.com/dashboard/sign-in" class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2">Dashboard</a> <!-- -->to see your avatar.</span></div><!--/$--></div></div></div></div>
<h2 class="font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0" id="installation"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#installation"><span class="icon icon-link"></span></a>Installation</h2>
<div class="mt-4"><!--$!--><template data-dgst="BAILOUT_TO_CLIENT_SIDE_RENDERING"></template><!--/$--><a href="https://v0.dev/chat/api/open?url=https://supabase.com/ui/r/current-user-avatar-nextjs.json" target="_blank" rel="noreferrer" class="inline-flex items-center justify-center font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-foreground-muted focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 py-2 h-7 gap-1 rounded-lg shadow-none bg-black px-3 text-xs text-white hover:bg-black hover:text-white dark:bg-white dark:text-black w-fit shrink-0 mt-4" aria-label="Open in v0">Open in<!-- --> <svg viewBox="0 0 40 20" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-current"><path d="M23.3919 0H32.9188C36.7819 0 39.9136 3.13165 39.9136 6.99475V16.0805H36.0006V6.99475C36.0006 6.90167 35.9969 6.80925 35.9898 6.71766L26.4628 16.079C26.4949 16.08 26.5272 16.0805 26.5595 16.0805H36.0006V19.7762H26.5595C22.6964 19.7762 19.4788 16.6139 19.4788 12.7508V3.68923H23.3919V12.7508C23.3919 12.9253 23.4054 13.0977 23.4316 13.2668L33.1682 3.6995C33.0861 3.6927 33.003 3.68923 32.9188 3.68923H23.3919V0Z" fill="currentColor"></path><path d="M13.7688 19.0956L0 3.68759H5.53933L13.6231 12.7337V3.68759H17.7535V17.5746C17.7535 19.6705 15.1654 20.6584 13.7688 19.0956Z" fill="currentColor"></path></svg></a></div>
<h2 class="font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0" id="folder-structure"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#folder-structure"><span class="icon icon-link"></span></a>Folder structure</h2>
<div class="flex mt-4 border rounded-lg overflow-hidden h-[652px] not-prose"><div class="w-64 grow-0 shrink-0 flex-0 py-2 border-r bg-muted/30 overflow-y-auto"><ul class="tree w-full" role="tree" aria-multiselectable="false" aria-label="file browser"><li role="treeitem" aria-expanded="true" aria-setsize="3" aria-posinset="1" aria-level="1" aria-disabled="false" tabindex="0" class="tree-branch-wrapper"><div class="group relative transition-colors h-[28px] flex items-center text-sm cursor-pointer select-none text-foreground-light hover:bg-control aria-expanded:bg-transparent data-[state=open]:bg-transparent gap-1.5" aria-selected="false" aria-expanded="true" style="padding-left:16px" data-treeview-is-branch="true" data-treeview-level="1"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right text-foreground-muted group-aria-selected:text-foreground-light group-aria-expanded:text-foreground-light transition-transform duration-200 group-aria-expanded:rotate-90"><path d="m9 18 6-6-6-6"></path></svg><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-folder-open transition-colors text-foreground-muted group-aria-selected:text-foreground-light group-aria-expanded:text-foreground-light"><path d="m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2"></path></svg><span class="truncate text-sm" title="components">components</span><form class="hidden"><input autofocus="" class="rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive block w-full text-sm px-2 py-1 h-7" value="components"/></form></div><ul role="group" class="tree-node-group tree-node-group--expanded"><li role="none" class="tree-leaf-list-item tree-leaf-list-item--selected"><div role="treeitem" tabindex="-1" class="group relative transition-colors h-[28px] flex items-center text-sm cursor-pointer select-none hover:bg-control aria-expanded:bg-transparent data-[state=open]:bg-transparent text-foreground !bg-selection gap-1.5" aria-setsize="1" aria-posinset="1" aria-level="2" aria-disabled="false" aria-selected="true" aria-expanded="false" style="padding-left:35px" data-treeview-is-branch="false" data-treeview-level="2"><div style="left:23px" class="absolute h-full w-px bg-border-strong"></div><div class="absolute left-0 h-full w-0.5 bg-foreground"></div><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file shrink-0"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path></svg><span class="truncate text-sm" title="current-user-avatar.tsx">current-user-avatar.tsx</span><form class="hidden"><input autofocus="" class="rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive block w-full text-sm px-2 py-1 h-7" value="current-user-avatar.tsx"/></form></div></li></ul></li><li role="treeitem" aria-expanded="true" aria-setsize="3" aria-posinset="2" aria-level="1" aria-disabled="false" tabindex="-1" class="tree-branch-wrapper"><div class="group relative transition-colors h-[28px] flex items-center text-sm cursor-pointer select-none text-foreground-light hover:bg-control aria-expanded:bg-transparent data-[state=open]:bg-transparent gap-1.5" aria-selected="false" aria-expanded="true" style="padding-left:16px" data-treeview-is-branch="true" data-treeview-level="1"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right text-foreground-muted group-aria-selected:text-foreground-light group-aria-expanded:text-foreground-light transition-transform duration-200 group-aria-expanded:rotate-90"><path d="m9 18 6-6-6-6"></path></svg><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-folder-open transition-colors text-foreground-muted group-aria-selected:text-foreground-light group-aria-expanded:text-foreground-light"><path d="m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2"></path></svg><span class="truncate text-sm" title="hooks">hooks</span><form class="hidden"><input autofocus="" class="rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive block w-full text-sm px-2 py-1 h-7" value="hooks"/></form></div><ul role="group" class="tree-node-group tree-node-group--expanded"><li role="none" class="tree-leaf-list-item"><div role="treeitem" tabindex="-1" class="group relative transition-colors h-[28px] flex items-center text-sm cursor-pointer select-none text-foreground-light hover:bg-control aria-expanded:bg-transparent data-[state=open]:bg-transparent gap-1.5" aria-setsize="2" aria-posinset="1" aria-level="2" aria-disabled="false" aria-selected="false" aria-expanded="false" style="padding-left:35px" data-treeview-is-branch="false" data-treeview-level="2"><div style="left:23px" class="absolute h-full w-px bg-border-strong"></div><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file shrink-0"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path></svg><span class="truncate text-sm" title="use-current-user-image.ts">use-current-user-image.ts</span><form class="hidden"><input autofocus="" class="rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive block w-full text-sm px-2 py-1 h-7" value="use-current-user-image.ts"/></form></div></li><li role="none" class="tree-leaf-list-item"><div role="treeitem" tabindex="-1" class="group relative transition-colors h-[28px] flex items-center text-sm cursor-pointer select-none text-foreground-light hover:bg-control aria-expanded:bg-transparent data-[state=open]:bg-transparent gap-1.5" aria-setsize="2" aria-posinset="2" aria-level="2" aria-disabled="false" aria-selected="false" aria-expanded="false" style="padding-left:35px" data-treeview-is-branch="false" data-treeview-level="2"><div style="left:23px" class="absolute h-full w-px bg-border-strong"></div><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file shrink-0"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path></svg><span class="truncate text-sm" title="use-current-user-name.ts">use-current-user-name.ts</span><form class="hidden"><input autofocus="" class="rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive block w-full text-sm px-2 py-1 h-7" value="use-current-user-name.ts"/></form></div></li></ul></li><li role="treeitem" aria-expanded="true" aria-setsize="3" aria-posinset="3" aria-level="1" aria-disabled="false" tabindex="-1" class="tree-branch-wrapper"><div class="group relative transition-colors h-[28px] flex items-center text-sm cursor-pointer select-none text-foreground-light hover:bg-control aria-expanded:bg-transparent data-[state=open]:bg-transparent gap-1.5" aria-selected="false" aria-expanded="true" style="padding-left:16px" data-treeview-is-branch="true" data-treeview-level="1"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right text-foreground-muted group-aria-selected:text-foreground-light group-aria-expanded:text-foreground-light transition-transform duration-200 group-aria-expanded:rotate-90"><path d="m9 18 6-6-6-6"></path></svg><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-folder-open transition-colors text-foreground-muted group-aria-selected:text-foreground-light group-aria-expanded:text-foreground-light"><path d="m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2"></path></svg><span class="truncate text-sm" title="lib">lib</span><form class="hidden"><input autofocus="" class="rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive block w-full text-sm px-2 py-1 h-7" value="lib"/></form></div><ul role="group" class="tree-node-group tree-node-group--expanded"><li role="treeitem" aria-expanded="true" aria-setsize="1" aria-posinset="1" aria-level="2" aria-disabled="false" tabindex="-1" class="tree-branch-wrapper"><div class="group relative transition-colors h-[28px] flex items-center text-sm cursor-pointer select-none text-foreground-light hover:bg-control aria-expanded:bg-transparent data-[state=open]:bg-transparent gap-1.5" aria-selected="false" aria-expanded="true" style="padding-left:35px" data-treeview-is-branch="true" data-treeview-level="2"><div style="left:23px" class="absolute h-full w-px bg-border-strong"></div><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right text-foreground-muted group-aria-selected:text-foreground-light group-aria-expanded:text-foreground-light transition-transform duration-200 group-aria-expanded:rotate-90"><path d="m9 18 6-6-6-6"></path></svg><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-folder-open transition-colors text-foreground-muted group-aria-selected:text-foreground-light group-aria-expanded:text-foreground-light"><path d="m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2"></path></svg><span class="truncate text-sm" title="supabase">supabase</span><form class="hidden"><input autofocus="" class="rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive block w-full text-sm px-2 py-1 h-7" value="supabase"/></form></div><ul role="group" class="tree-node-group tree-node-group--expanded"><li role="none" class="tree-leaf-list-item"><div role="treeitem" tabindex="-1" class="group relative transition-colors h-[28px] flex items-center text-sm cursor-pointer select-none text-foreground-light hover:bg-control aria-expanded:bg-transparent data-[state=open]:bg-transparent gap-1.5" aria-setsize="3" aria-posinset="1" aria-level="3" aria-disabled="false" aria-selected="false" aria-expanded="false" style="padding-left:54px" data-treeview-is-branch="false" data-treeview-level="3"><div style="left:23px" class="absolute h-full w-px bg-border-strong"></div><div style="left:42px" class="absolute h-full w-px bg-border-strong"></div><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file shrink-0"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path></svg><span class="truncate text-sm" title="client.ts">client.ts</span><form class="hidden"><input autofocus="" class="rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive block w-full text-sm px-2 py-1 h-7" value="client.ts"/></form></div></li><li role="none" class="tree-leaf-list-item"><div role="treeitem" tabindex="-1" class="group relative transition-colors h-[28px] flex items-center text-sm cursor-pointer select-none text-foreground-light hover:bg-control aria-expanded:bg-transparent data-[state=open]:bg-transparent gap-1.5" aria-setsize="3" aria-posinset="2" aria-level="3" aria-disabled="false" aria-selected="false" aria-expanded="false" style="padding-left:54px" data-treeview-is-branch="false" data-treeview-level="3"><div style="left:23px" class="absolute h-full w-px bg-border-strong"></div><div style="left:42px" class="absolute h-full w-px bg-border-strong"></div><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file shrink-0"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path></svg><span class="truncate text-sm" title="middleware.ts">middleware.ts</span><form class="hidden"><input autofocus="" class="rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive block w-full text-sm px-2 py-1 h-7" value="middleware.ts"/></form></div></li><li role="none" class="tree-leaf-list-item"><div role="treeitem" tabindex="-1" class="group relative transition-colors h-[28px] flex items-center text-sm cursor-pointer select-none text-foreground-light hover:bg-control aria-expanded:bg-transparent data-[state=open]:bg-transparent gap-1.5" aria-setsize="3" aria-posinset="3" aria-level="3" aria-disabled="false" aria-selected="false" aria-expanded="false" style="padding-left:54px" data-treeview-is-branch="false" data-treeview-level="3"><div style="left:23px" class="absolute h-full w-px bg-border-strong"></div><div style="left:42px" class="absolute h-full w-px bg-border-strong"></div><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file shrink-0"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path></svg><span class="truncate text-sm" title="server.ts">server.ts</span><form class="hidden"><input autofocus="" class="rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive block w-full text-sm px-2 py-1 h-7" value="server.ts"/></form></div></li></ul></li></ul></li></ul></div><div class="group relative max-w-[90vw] md:max-w-none overflow-auto w-full"><pre class="code-block border border-surface p-4 w-full !my-0 !bg-surface-100 outline-none focus:border-foreground-lighter/50 h-full max-w-none !w-full flex-1 font-mono text-xs rounded-none border-none" contentEditable="true" style="display:block;overflow-x:auto;color:#888;font-size:13px;line-height:1.4"><code class="language-ts" style="white-space:pre"><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">1</span><span style="color:#3ECF8E">&#x27;use client&#x27;</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">2</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">3</span><span></span><span style="color:#569cd6;font-weight:normal">import</span><span> { useCurrentUserImage } </span><span style="color:#569cd6;font-weight:normal">from</span><span> </span><span style="color:#3ECF8E">&#x27;@/hooks/use-current-user-image&#x27;</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">4</span><span></span><span style="color:#569cd6;font-weight:normal">import</span><span> { useCurrentUserName } </span><span style="color:#569cd6;font-weight:normal">from</span><span> </span><span style="color:#3ECF8E">&#x27;@/hooks/use-current-user-name&#x27;</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">5</span><span></span><span style="color:#569cd6;font-weight:normal">import</span><span> { Avatar, AvatarFallback, AvatarImage } </span><span style="color:#569cd6;font-weight:normal">from</span><span> </span><span style="color:#3ECF8E">&#x27;@/components/ui/avatar&#x27;</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">6</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">7</span><span></span><span style="color:#569cd6;font-weight:normal">export</span><span> </span><span style="color:#569cd6;font-weight:normal">const</span><span> CurrentUserAvatar = </span><span class="hljs-function">() =&gt;</span><span> {
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">8</span><span>  </span><span style="color:#569cd6;font-weight:normal">const</span><span> profileImage = useCurrentUserImage()
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">9</span><span>  </span><span style="color:#569cd6;font-weight:normal">const</span><span> name = useCurrentUserName()
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">10</span><span>  </span><span style="color:#569cd6;font-weight:normal">const</span><span> initials = name
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">11</span><span>    ?.split(</span><span style="color:#3ECF8E">&#x27; &#x27;</span><span>)
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">12</span><span>    ?.map(</span><span class="hljs-function">(</span><span class="hljs-function hljs-params">word</span><span class="hljs-function">) =&gt;</span><span> word[</span><span class="hljs-number">0</span><span>])
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">13</span><span>    ?.join(</span><span style="color:#3ECF8E">&#x27;&#x27;</span><span>)
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">14</span>    ?.toUpperCase()
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">15</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">16</span><span>  </span><span style="color:#569cd6;font-weight:normal">return</span><span> (
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">17</span>    &lt;Avatar&gt;
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">18</span>      {profileImage &amp;&amp; &lt;AvatarImage src={profileImage} alt={initials} /&gt;}
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">19</span>      &lt;AvatarFallback&gt;{initials}&lt;/AvatarFallback&gt;
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">20</span>    &lt;/Avatar&gt;
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">21</span>  )
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">22</span>}</span></code></pre><div class="absolute right-2 top-2 opacity-0 group-hover:opacity-100 transition "><button data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs py-1 h-[26px] px-1.5"><div class="[&amp;_svg]:h-[14px] [&amp;_svg]:w-[14px] text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></div> <!-- --> </button></div></div></div>
<h2 class="font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0" id="introduction"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#introduction"><span class="icon icon-link"></span></a>Introduction</h2>
<p class="leading-7 [&amp;:not(:first-child)]:mt-6 text-foreground-light">The <code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">CurrentUserAvatar</code> component connects to Supabase Auth to fetch the user data and show an avatar. It uses the <code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">user_metadata</code>
property which gets populated automatically by Supabase Auth if the user logged in via a provider. If the user doesn&#x27;t have a profile image, it renders their initials. If the user is logged out, it renders a <code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">?</code> as a fallback, which you can change.</p>
<h2 class="font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0" id="usage"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#usage"><span class="icon icon-link"></span></a>Usage</h2>
<p class="leading-7 [&amp;:not(:first-child)]:mt-6 text-foreground-light">The <code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">CurrentUserAvatar</code> component is designed to be used anywhere in your app. Add the <code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">&lt;CurrentUserAvatar /&gt;</code> component to your page and it will render the avatar of the current user, with a fallback.</p>
<div data-rehype-pretty-code-fragment=""><pre class="mb-4 mt-6 max-h-[650px] overflow-x-auto rounded-lg border bg-surface-75/75 py-4 text-foreground-light" data-language="tsx" data-theme="default"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm" data-language="tsx" data-theme="default"><span class="line"><span style="color:var(--code-token-punctuation)">&#x27;</span><span style="color:var(--code-token-string-expression)">use client</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-punctuation)"> {</span><span style="color:var(--code-token-parameter)"> CurrentUserAvatar</span><span style="color:var(--code-token-punctuation)"> }</span><span style="color:var(--code-token-keyword)"> from</span><span style="color:var(--code-token-punctuation)"> &#x27;</span><span style="color:var(--code-token-string-expression)">@/components/current-user-avatar</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-keyword)">const</span><span style="color:var(--code-token-function)"> CurrentUserAvatarDemo</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-punctuation)"> ()</span><span style="color:var(--code-token-keyword)"> =&gt;</span><span style="color:var(--code-token-punctuation)"> {</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">  return</span><span style="color:var(--code-foreground)"> (</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">    &lt;</span><span style="color:var(--code-token-function)">Header </span><span style="color:var(--code-token-property)">className</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)">flex items-center justify-between</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">      &lt;</span><span style="color:var(--code-token-function)">h1</span><span style="color:var(--code-token-punctuation)">&gt;</span><span style="color:var(--code-token-function)">Lumon Industries</span><span style="color:var(--code-token-punctuation)">&lt;/</span><span style="color:var(--code-token-function)">h1</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">      &lt;</span><span style="color:var(--code-token-function)">CurrentUserAvatar </span><span style="color:var(--code-token-punctuation)">/&gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">    &lt;/</span><span style="color:var(--code-token-function)">Header</span><span style="color:var(--code-token-punctuation)">&gt;</span></span>
<span class="line"><span style="color:var(--code-foreground)">  )</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">}</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-keyword)">export</span><span style="color:var(--code-token-keyword)"> default</span><span style="color:var(--code-token-parameter)"> CurrentUserAvatarDemo</span></span></code></pre><button data-size="small" type="button" class="justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong hover:border-foreground-muted focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-sm leading-4 z-10 h-6 w-6 text-foreground-muted hover:bg-surface-100 hover:text-foreground p-0 absolute right-4 top-4"> <span class="truncate"><span class="sr-only">Copy</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy h-3 w-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></span> </button></div>
<h2 class="font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0" id="props"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#props"><span class="icon icon-link"></span></a>Props</h2>
<p class="leading-7 [&amp;:not(:first-child)]:mt-6 text-foreground-light">This component doesn&#x27;t accept any props. If you wish to change the fallback, you can do so by changing the <code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">CurrentUserAvatar</code> component directly.</p>
<h2 class="font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0" id="further-reading"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#further-reading"><span class="icon icon-link"></span></a>Further reading</h2>
<ul class="my-6 ml-6 list-disc text-foreground-light">
<li class="mt-2"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2" href="https://supabase.com/docs/guides/auth/users">Auth users</a></li>
</ul></div></div></div><div class="hidden text-sm xl:block"><div class="sticky top-16 -mt-10 pt-4"><div dir="ltr" class="relative overflow-hidden pb-10" style="position:relative;--radix-scroll-area-corner-width:0px;--radix-scroll-area-corner-height:0px"><style>[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}</style><div data-radix-scroll-area-viewport="" class="h-full w-full rounded-[inherit]" style="overflow-x:hidden;overflow-y:hidden"><div style="min-width:100%;display:table"><div class="sticky top-16 -mt-10 h-[calc(100vh-3.5rem)] py-12"></div></div></div></div></div></div></main><!--$--><!--/$--><!--$--><!--/$--></div></div></div></div></main></div><footer class="py-6 px-4 md:px-8 md:py-0 mx-auto w-full max-w-site"><div class="flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row"><p class="text-balance text-center text-sm leading-loose text-foreground-muted md:text-left">Built by<!-- --> <a href="https://twitter.com/supabase" target="_blank" rel="noreferrer" class="font-medium underline underline-offset-4 hover:text-foreground-lighter">Supabase</a>. The source code is available on<!-- --> <a href="https://github.com/supabase/supabase/tree/master/apps/ui-library" target="_blank" rel="noreferrer" class="font-medium underline underline-offset-4 hover:text-foreground-lighter">GitHub</a>.</p><p class="text-balance text-center text-sm leading-loose text-foreground-muted">Site inspired by<!-- --> <a href="https://www.radix-ui.com/themes/docs/overview/getting-started" target="_blank" rel="noreferrer" class="font-medium underline underline-offset-4 hover:text-foreground-lighter">Radix</a>,<!-- --> <a href="https://ui.shadcn.com" target="_blank" rel="noreferrer" class="font-medium underline underline-offset-4 hover:text-foreground-lighter">shadcn/ui</a>.</p></div></footer><script src="../../_next/static/chunks/webpack-e33ce3369a09e8d8.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n3:I[52595,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"4944\",\"static/chunks/app/(app)/layout-68e9ee4f0ef3c93b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"MobileMenuSheet\"]\n4:I[86084,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app"])</script><script>self.__next_f.push([1,"/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"SheetTrigger\"]\n5:I[35621,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"Button\"]\n6:I[86084,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513"])</script><script>self.__next_f.push([1,"\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"SheetContent\"]\n7:I[28694,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"ScrollArea\"]\n8:I[15531,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4"])</script><script>self.__next_f.push([1,"WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"\"]\n9:I[47642,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"4944\",\"static/chunks/app/(app)/layout-68e9ee4f0ef3c93b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"ThemeSwitcherDropdown\"]\na:I[28044,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4"])</script><script>self.__next_f.push([1,"WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"4944\",\"static/chunks/app/(app)/layout-68e9ee4f0ef3c93b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"CommandMenu\"]\nb:I[22792,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"4944\",\"static/chunks/app/(app)/layout-68e9ee4f0ef3c93b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"default\"]\nc:I[93926,[],\"\"]\nd:I[6252,[],\"\"]\ne:I[63645,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv"])</script><script>self.__next_f.push([1,"1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"4944\",\"static/chunks/app/(app)/layout-68e9ee4f0ef3c93b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"TelemetryWrapper\"]\n10:I[18206,[],\"MetadataBoundary\"]\n12:I[18206,[],\"OutletBoundary\"]\n15:I[38670,[],\"AsyncMetadataOutlet\"]\n17:I[18206,[],\"ViewportBoundary\"]\n19:I[47249,[],\"\"]\n:HL[\"/ui/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/ui/_next/static/css/03f63ad6e897ae58.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"style\"]\n:HL[\"/ui/_next/static/css/954cd54edf7b3efb.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"style\"]\n:HL[\"/ui/_next/static/css/be16f9d4f36b2c7a.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"style\"]\n:HL[\"/ui/_next/static/css/99f16e312f80aa1b.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"style\"]\n:HL[\"/ui/_next/static/css/f12065469b9a7bd1.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"InVsSGMudyjao1kx6wnTY\",\"p\":\"/ui\",\"c\":[\"\",\"docs\",\"nextjs\",\"current-user-avatar\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"(app)\",{\"children\":[\"docs\",{\"children\":[[\"slug\",\"nextjs/current-user-avatar\",\"oc\"],{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/ui/_next/static/css/03f63ad6e897ae58.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/ui/_next/static/css/954cd54edf7b3efb.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"2\",{\"rel\":\"stylesheet\",\"href\":\"/ui/_next/static/css/be16f9d4f36b2c7a.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],\"$L2\"]}],{\"children\":[\"(app)\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/ui/_next/static/css/99f16e312f80aa1b.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[[\"$\",\"div\",null,{\"className\":\"pt-10 md:pt-0\",\"children\":[\"$\",\"main\",null,{\"className\":\"flex-1 max-w-site mx-auto w-full p-0\",\"children\":[\"$\",\"div\",null,{\"className\":\"border-b\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex-1 items-start md:grid md:grid-cols-[220px_minmax(0,1fr)] lg:grid-cols-[240px_minmax(0,1fr)]\",\"children\":[[[\"$\",\"div\",null,{\"className\":\"md:hidden fixed top-0 left-0 right-0 z-50 bg-background justify-between flex items-center px-8 py-3 border-b\",\"children\":[[\"$\",\"$L3\",null,{\"children\":[[\"$\",\"$L4\",null,{\"asChild\":true,\"children\":[\"$\",\"$L5\",null,{\"type\":\"outline\",\"icon\":[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-menu\",\"children\":[[\"$\",\"line\",\"1e0a9i\",{\"x1\":\"4\",\"x2\":\"20\",\"y1\":\"12\",\"y2\":\"12\"}],[\"$\",\"line\",\"1owob3\",{\"x1\":\"4\",\"x2\":\"20\",\"y1\":\"6\",\"y2\":\"6\"}],[\"$\",\"line\",\"yk5zj1\",{\"x1\":\"4\",\"x2\":\"20\",\"y1\":\"18\",\"y2\":\"18\"}],\"$undefined\"]}]}]}],[\"$\",\"$L6\",null,{\"side\":\"left\",\"className\":\"p-0 w-80\",\"showClose\":false,\"children\":[\"$\",\"$L7\",null,{\"className\":\"h-full\",\"children\":[\"$\",\"nav\",null,{\"className\":\"flex flex-col h-full min-w-[220px]\",\"children\":[[\"$\",\"div\",null,{\"className\":\"p-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-start justify-between mb-4\",\"children\":[[\"$\",\"$L8\",null,{\"href\":\"/\",\"children\":[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":\"109\",\"height\":\"113\",\"viewBox\":\"0 0 109 113\",\"fill\":\"none\",\"className\":\"w-6 h-6\",\"children\":[[\"$\",\"path\",null,{\"d\":\"M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0625L99.1935 40.0625C107.384 40.0625 111.952 49.5226 106.859 55.9372L63.7076 110.284Z\",\"fill\":\"url(#paint0_linear)\"}],[\"$\",\"path\",null,{\"d\":\"M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0625L99.1935 40.0625C107.384 40.0625 111.952 49.5226 106.859 55.9372L63.7076 110.284Z\",\"fill\":\"url(#paint1_linear)\",\"fillOpacity\":\"0.2\"}],[\"$\",\"path\",null,{\"d\":\"M45.317 2.07103C48.1765 -1.53037 53.9745 0.442937 54.0434 5.041L54.4849 72.2922H9.83113C1.64038 72.2922 -2.92775 62.8321 2.1655 56.4175L45.317 2.07103Z\",\"fill\":\"#3ECF8E\"}],[\"$\",\"defs\",null,{\"children\":[[\"$\",\"linearGradient\",null,{\"id\":\"paint0_linear\",\"x1\":\"53.9738\",\"y1\":\"54.9738\",\"x2\":\"94.1635\",\"y2\":\"71.8293\",\"gradientUnits\":\"userSpaceOnUse\",\"children\":[[\"$\",\"stop\",null,{\"stopColor\":\"#249361\"}],[\"$\",\"stop\",null,{\"offset\":\"1\",\"stopColor\":\"#3ECF8E\"}]]}],[\"$\",\"linearGradient\",null,{\"id\":\"paint1_linear\",\"x1\":\"36.1558\",\"y1\":\"30.5779\",\"x2\":\"54.4844\",\"y2\":\"65.0804\",\"gradientUnits\":\"userSpaceOnUse\",\"children\":[[\"$\",\"stop\",null,{}],[\"$\",\"stop\",null,{\"offset\":\"1\",\"stopOpacity\":\"0\"}]]}]]}]]}]}],[\"$\",\"$L9\",null,{}]]}],[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"mb-4 block\",\"children\":[\"$\",\"h1\",null,{\"children\":\"Supabase UI Library\"}]}],[\"$\",\"$La\",null,{}]]}],[\"$\",\"div\",null,{\"className\":\"pb-6 space-y-0.5\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest\",\"children\":\"Getting Started\"}],[[\"$\",\"$Lb\",\"/docs/getting-started/introduction-0\",{\"item\":{\"title\":\"Introduction\",\"href\":\"/docs/getting-started/introduction\",\"items\":[],\"commandItemLabel\":\"Introduction\"}}],[\"$\",\"$Lb\",\"/docs/getting-started/quickstart-1\",{\"item\":{\"title\":\"Quick Start\",\"href\":\"/docs/getting-started/quickstart\",\"items\":[],\"commandItemLabel\":\"Quick Start\"}}],[\"$\",\"$Lb\",\"/docs/getting-started/faq-2\",{\"item\":{\"title\":\"FAQ\",\"href\":\"/docs/getting-started/faq\",\"items\":[],\"commandItemLabel\":\"FAQ\"}}]]]}],[\"$\",\"div\",null,{\"className\":\"pb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest\",\"children\":\"Blocks\"}],[\"$\",\"div\",null,{\"className\":\"space-y-0.5\",\"children\":[[\"$\",\"$Lb\",\"/docs/nextjs/client-0\",{\"item\":{\"title\":\"Client\",\"supportedFrameworks\":[\"nextjs\",\"react-router\",\"tanstack\",\"react\"],\"href\":\"/docs/nextjs/client\",\"items\":[],\"commandItemLabel\":\"Supabase Client\"}}],[\"$\",\"$Lb\",\"/docs/nextjs/password-based-auth-1\",{\"item\":{\"title\":\"Password-Based Auth\",\"supportedFrameworks\":[\"nextjs\",\"react-router\",\"tanstack\",\"react\"],\"href\":\"/docs/nextjs/password-based-auth\",\"items\":[],\"commandItemLabel\":\"Password-Based Auth\"}}],[\"$\",\"$Lb\",\"/docs/nextjs/social-auth-2\",{\"item\":{\"title\":\"Social Auth\",\"supportedFrameworks\":[\"nextjs\",\"react-router\",\"tanstack\",\"react\"],\"href\":\"/docs/nextjs/social-auth\",\"items\":[],\"new\":true,\"commandItemLabel\":\"Social Auth\"}}],[\"$\",\"$Lb\",\"/docs/nextjs/dropzone-3\",{\"item\":{\"title\":\"Dropzone\",\"supportedFrameworks\":[\"nextjs\",\"react-router\",\"tanstack\",\"react\"],\"href\":\"/docs/nextjs/dropzone\",\"items\":[],\"commandItemLabel\":\"Dropzone (File Upload)\"}}],[\"$\",\"$Lb\",\"/docs/nextjs/realtime-cursor-4\",{\"item\":{\"title\":\"Realtime Cursor\",\"supportedFrameworks\":[\"nextjs\",\"react-router\",\"tanstack\",\"react\"],\"href\":\"/docs/nextjs/realtime-cursor\",\"items\":[],\"commandItemLabel\":\"Realtime Cursor\"}}],[\"$\",\"$Lb\",\"/docs/nextjs/current-user-avatar-5\",{\"item\":{\"title\":\"Current User Avatar\",\"supportedFrameworks\":[\"nextjs\",\"react-router\",\"tanstack\",\"react\"],\"href\":\"/docs/nextjs/current-user-avatar\",\"items\":[],\"commandItemLabel\":\"Current User Avatar\"}}],[\"$\",\"$Lb\",\"/docs/nextjs/realtime-avatar-stack-6\",{\"item\":{\"title\":\"Realtime Avatar Stack\",\"supportedFrameworks\":[\"nextjs\",\"react-router\",\"tanstack\",\"react\"],\"href\":\"/docs/nextjs/realtime-avatar-stack\",\"items\":[],\"commandItemLabel\":\"Realtime Avatar Stack\"}}],[\"$\",\"$Lb\",\"/docs/nextjs/realtime-chat-7\",{\"item\":{\"title\":\"Realtime Chat\",\"supportedFrameworks\":[\"nextjs\",\"react-router\",\"tanstack\",\"react\"],\"href\":\"/docs/nextjs/realtime-chat\",\"items\":[],\"commandItemLabel\":\"Realtime Chat\"}}],[\"$\",\"$Lb\",\"/docs/infinite-query-hook-8\",{\"item\":{\"title\":\"Infinite Query Hook\",\"supportedFrameworks\":[],\"href\":\"/docs/infinite-query-hook\",\"new\":true,\"items\":[],\"commandItemLabel\":\"Infinite Query Hook\"}}]]}]]}],[\"$\",\"div\",null,{\"className\":\"pb-6 flex-1\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest\",\"children\":\"AI Editors Rules\"}],[[\"$\",\"$Lb\",\"/docs/ai-editors-rules/prompts-0\",{\"item\":{\"title\":\"Prompts\",\"href\":\"/docs/ai-editors-rules/prompts\",\"items\":[],\"commandItemLabel\":\"AI Editors Rules\"}}]]]}],[\"$\",\"div\",null,{\"className\":\"pb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest\",\"children\":\"Platform\"}],[[\"$\",\"$Lb\",\"/docs/platform/platform-kit-0\",{\"item\":{\"title\":\"Platform Kit\",\"href\":\"/docs/platform/platform-kit\",\"items\":[],\"commandItemLabel\":\"Platform Kit\"}}]]]}]]}]}]}]]}],[\"$\",\"$L9\",null,{}]]}],[\"$\",\"aside\",null,{\"className\":\"fixed z-30 top-0 hidden h-screen w-full shrink-0 md:sticky md:block bg-200 border-r border-muted/50\",\"children\":[\"$\",\"$L7\",null,{\"className\":\"h-full\",\"children\":[\"$\",\"nav\",null,{\"className\":\"flex flex-col h-full min-w-[220px]\",\"children\":[[\"$\",\"div\",null,{\"className\":\"p-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-start justify-between mb-4\",\"children\":[[\"$\",\"$L8\",null,{\"href\":\"/\",\"children\":[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":\"109\",\"height\":\"113\",\"viewBox\":\"0 0 109 113\",\"fill\":\"none\",\"className\":\"w-6 h-6\",\"children\":[[\"$\",\"path\",null,{\"d\":\"M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0625L99.1935 40.0625C107.384 40.0625 111.952 49.5226 106.859 55.9372L63.7076 110.284Z\",\"fill\":\"url(#paint0_linear)\"}],[\"$\",\"path\",null,{\"d\":\"M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0625L99.1935 40.0625C107.384 40.0625 111.952 49.5226 106.859 55.9372L63.7076 110.284Z\",\"fill\":\"url(#paint1_linear)\",\"fillOpacity\":\"0.2\"}],[\"$\",\"path\",null,{\"d\":\"M45.317 2.07103C48.1765 -1.53037 53.9745 0.442937 54.0434 5.041L54.4849 72.2922H9.83113C1.64038 72.2922 -2.92775 62.8321 2.1655 56.4175L45.317 2.07103Z\",\"fill\":\"#3ECF8E\"}],[\"$\",\"defs\",null,{\"children\":[[\"$\",\"linearGradient\",null,{\"id\":\"paint0_linear\",\"x1\":\"53.9738\",\"y1\":\"54.9738\",\"x2\":\"94.1635\",\"y2\":\"71.8293\",\"gradientUnits\":\"userSpaceOnUse\",\"children\":[[\"$\",\"stop\",null,{\"stopColor\":\"#249361\"}],[\"$\",\"stop\",null,{\"offset\":\"1\",\"stopColor\":\"#3ECF8E\"}]]}],[\"$\",\"linearGradient\",null,{\"id\":\"paint1_linear\",\"x1\":\"36.1558\",\"y1\":\"30.5779\",\"x2\":\"54.4844\",\"y2\":\"65.0804\",\"gradientUnits\":\"userSpaceOnUse\",\"children\":[[\"$\",\"stop\",null,{}],[\"$\",\"stop\",null,{\"offset\":\"1\",\"stopOpacity\":\"0\"}]]}]]}]]}]}],[\"$\",\"$L9\",null,{}]]}],[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"mb-4 block\",\"children\":[\"$\",\"h1\",null,{\"children\":\"Supabase UI Library\"}]}],[\"$\",\"$La\",null,{}]]}],[\"$\",\"div\",null,{\"className\":\"pb-6 space-y-0.5\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest\",\"children\":\"Getting Started\"}],[[\"$\",\"$Lb\",\"/docs/getting-started/introduction-0\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:1:props:children:1:0:props:item\"}],[\"$\",\"$Lb\",\"/docs/getting-started/quickstart-1\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:1:props:children:1:1:props:item\"}],[\"$\",\"$Lb\",\"/docs/getting-started/faq-2\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:1:props:children:1:2:props:item\"}]]]}],[\"$\",\"div\",null,{\"className\":\"pb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest\",\"children\":\"Blocks\"}],[\"$\",\"div\",null,{\"className\":\"space-y-0.5\",\"children\":[[\"$\",\"$Lb\",\"/docs/nextjs/client-0\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:0:props:item\"}],[\"$\",\"$Lb\",\"/docs/nextjs/password-based-auth-1\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:1:props:item\"}],[\"$\",\"$Lb\",\"/docs/nextjs/social-auth-2\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:2:props:item\"}],[\"$\",\"$Lb\",\"/docs/nextjs/dropzone-3\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:3:props:item\"}],[\"$\",\"$Lb\",\"/docs/nextjs/realtime-cursor-4\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:4:props:item\"}],[\"$\",\"$Lb\",\"/docs/nextjs/current-user-avatar-5\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:5:props:item\"}],[\"$\",\"$Lb\",\"/docs/nextjs/realtime-avatar-stack-6\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:6:props:item\"}],[\"$\",\"$Lb\",\"/docs/nextjs/realtime-chat-7\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:7:props:item\"}],[\"$\",\"$Lb\",\"/docs/infinite-query-hook-8\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:8:props:item\"}]]}]]}],[\"$\",\"div\",null,{\"className\":\"pb-6 flex-1\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest\",\"children\":\"AI Editors Rules\"}],[[\"$\",\"$Lb\",\"/docs/ai-editors-rules/prompts-0\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:3:props:children:1:0:props:item\"}]]]}],[\"$\",\"div\",null,{\"className\":\"pb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest\",\"children\":\"Platform\"}],[[\"$\",\"$Lb\",\"/docs/platform/platform-kit-0\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:4:props:children:1:0:props:item\"}]]]}]]}]}]}]],[\"$\",\"div\",null,{\"vaul-drawer-wrapper\":\"\",\"children\":[\"$\",\"div\",null,{\"className\":\"relative flex min-h-screen flex-col bg-background\",\"children\":[\"$\",\"$Lc\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Ld\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}]}]}]}],[\"$\",\"footer\",null,{\"className\":\"py-6 px-4 md:px-8 md:py-0 mx-auto w-full max-w-site\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row\",\"children\":[[\"$\",\"p\",null,{\"className\":\"text-balance text-center text-sm leading-loose text-foreground-muted md:text-left\",\"children\":[\"Built by\",\" \",[\"$\",\"a\",null,{\"href\":\"https://twitter.com/supabase\",\"target\":\"_blank\",\"rel\":\"noreferrer\",\"className\":\"font-medium underline underline-offset-4 hover:text-foreground-lighter\",\"children\":\"Supabase\"}],\". The source code is available on\",\" \",[\"$\",\"a\",null,{\"href\":\"https://github.com/supabase/supabase/tree/master/apps/ui-library\",\"target\":\"_blank\",\"rel\":\"noreferrer\",\"className\":\"font-medium underline underline-offset-4 hover:text-foreground-lighter\",\"children\":\"GitHub\"}],\".\"]}],[\"$\",\"p\",null,{\"className\":\"text-balance text-center text-sm leading-loose text-foreground-muted\",\"children\":[\"Site inspired by\",\" \",[\"$\",\"a\",null,{\"href\":\"https://www.radix-ui.com/themes/docs/overview/getting-started\",\"target\":\"_blank\",\"rel\":\"noreferrer\",\"className\":\"font-medium underline underline-offset-4 hover:text-foreground-lighter\",\"children\":\"Radix\"}],\",\",\" \",[\"$\",\"a\",null,{\"href\":\"https://ui.shadcn.com\",\"target\":\"_blank\",\"rel\":\"noreferrer\",\"className\":\"font-medium underline underline-offset-4 hover:text-foreground-lighter\",\"children\":\"shadcn/ui\"}],\".\"]}]]}]}],[\"$\",\"$Le\",null,{}]]]}],{\"children\":[\"docs\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Lc\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Ld\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[[\"slug\",\"nextjs/current-user-avatar\",\"oc\"],[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Lc\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Ld\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[\"$Lf\",[\"$\",\"$L10\",null,{\"children\":\"$L11\"}],[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/ui/_next/static/css/f12065469b9a7bd1.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"$L12\",null,{\"children\":[\"$L13\",\"$L14\",[\"$\",\"$L15\",null,{\"promise\":\"$@16\"}]]}]]}],{},null,false]},null,false]},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"TW6b2Xmea7Cr96rzWyv4w\",{\"children\":[[\"$\",\"$L17\",null,{\"children\":\"$L18\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$19\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"1a:I[39346,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"TelemetryTagManager\"]\n1b:I[12287,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/ch"])</script><script>self.__next_f.push([1,"unks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"FeatureFlagProvider\"]\n1c:I[22104,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"ThemeProvider\"]\n1d:I[26854,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4"])</script><script>self.__next_f.push([1,"WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"SonnerToaster\"]\n1e:\"$Sreact.suspense\"\n1f:I[38670,[],\"AsyncMetadata\"]\n"])</script><script>self.__next_f.push([1,"2:[\"$\",\"html\",null,{\"lang\":\"en\",\"suppressHydrationWarning\":true,\"children\":[[\"$\",\"head\",null,{}],[\"$\",\"body\",null,{\"className\":\"__className_cbad29 antialiased\",\"children\":[[\"$\",\"$L1a\",null,{}],[\"$\",\"$L1b\",null,{\"API_URL\":\"https://api.supabase.com/platform\",\"children\":[\"$\",\"$L1c\",null,{\"themes\":[\"dark\",\"light\",\"classic-dark\"],\"defaultTheme\":\"system\",\"enableSystem\":true,\"children\":[[\"$\",\"$Lc\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Ld\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:1:props:children:props:children:props:notFound:0:1:props:style\",\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:1:props:children:props:children:props:notFound:0:1:props:children:props:children:1:props:style\",\"children\":404}],[\"$\",\"div\",null,{\"style\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:1:props:children:props:children:props:notFound:0:1:props:children:props:children:2:props:style\",\"children\":[\"$\",\"h2\",null,{\"style\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:1:props:children:props:children:props:notFound:0:1:props:children:props:children:2:props:children:props:style\",\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}],[\"$\",\"$L1d\",null,{}]]}]}]]}]]}]\n"])</script><script>self.__next_f.push([1,"11:[\"$\",\"$1e\",null,{\"fallback\":null,\"children\":[\"$\",\"$L1f\",null,{\"promise\":\"$@20\"}]}]\n"])</script><script>self.__next_f.push([1,"14:null\n"])</script><script>self.__next_f.push([1,"21:I[79904,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"default\"]\n22:I[50619,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2c"])</script><script>self.__next_f.push([1,"b44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"FrameworkSelector\"]\n23:I[30169,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"ComponentPreview\"]\n24:I[8186,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx"])</script><script>self.__next_f.push([1,"8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"StyleWrapper\"]\n25:I[45719,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/d"])</script><script>self.__next_f.push([1,"ocs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"CopyButton\"]\n27:I[42988,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"BlockItem\"]\n29:I[22090,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"st"])</script><script>self.__next_f.push([1,"atic/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"DashboardTableOfContents\"]\n26:T565,'use client'\n\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/components/ui/avatar'\nimport { useUser } from 'common'\n\nconst CurrentUserAvatarDemo = () =\u003e {\n  // this demo only works on supabase.com because all apps are on the same domain and share cookies\n  const user = useUser()\n\n  const profileImage = user?.user_metadata.avatar_url ?? null\n  const name = (user?.user_metadata.full_name as string) ?? '?'\n  const initials = name\n    ?.split(' ')\n    ?.map((word) =\u003e word[0])\n    ?.join('')\n    ?.toUpperCase()\n\n  return (\n    \u003cdiv className=\"flex flex-col gap-4 items-center justify-center\"\u003e\n      \u003cAvatar\u003e\n        {profileImage \u0026\u0026 \u003cAvatarImage src={profileImage} alt={initials} /\u003e}\n        \u003cAvatarFallback\u003e{initials}\u003c/AvatarFallback\u003e\n      \u003c/Avatar\u003e\n\n      {!user \u0026\u0026 (\n        \u003cspan className=\"text-sm text-foreground-light\"\u003e\n          It seems like you\u0026apos;re not logged in. Login via the{' '}\n          \u003ca\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            href=\"https://supabase.com/dashboard/sign-in\"\n            className=\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2\"\n          \u003e\n            Dashboard\n          \u003c/a\u003e{' '}\n          to see your avatar.\n        \u003c/span\u003e\n      )}\n    \u003c/div\u003e\n  )\n}\n\nexport CurrentUserAvatarDemo\n"])</script><script>self.__next_f.push([1,"f:[\"$\",\"main\",null,{\"className\":\"relative lg:gap-10 xl:grid xl:grid-cols-[1fr_200px] px-8 md:px-16 py-20\",\"children\":[[\"$\",\"div\",null,{\"className\":\"mx-auto w-full min-w-0 max-w-4xl\",\"children\":[[\"$\",\"div\",null,{\"className\":\"mb-4 flex items-center space-x-1 text-sm text-foreground-muted\",\"children\":[[\"$\",\"div\",null,{\"className\":\"overflow-hidden text-ellipsis whitespace-nowrap\",\"children\":\"Docs\"}],[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-chevron-right h-4 w-4 text-foreground-muted\",\"children\":[[\"$\",\"path\",\"mthhwq\",{\"d\":\"m9 18 6-6-6-6\"}],\"$undefined\"]}],[\"$\",\"div\",null,{\"className\":\"text-foreground-lighter\",\"children\":\"Current User Avatar\"}]]}],[\"$\",\"div\",null,{\"className\":\"flex flex-col lg:flex-row lg:items-end justify-between mb-5\",\"children\":[[\"$\",\"div\",null,{\"className\":\"space-y-2\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"scroll-m-20 text-2xl lg:text-4xl tracking-tight\",\"children\":\"Current User Avatar\"}],[\"$\",\"p\",null,{\"className\":\"text-base lg:text-lg text-foreground-light\",\"children\":[\"$\",\"$L21\",null,{\"children\":\"Supabase Auth-aware avatar\"}]}]]}],[\"$\",\"$L22\",null,{}]]}],[\"$\",\"div\",null,{\"className\":\"flex flex-col -space-y-px\",\"children\":[\"$undefined\",\"$undefined\",\"$undefined\",\"$undefined\"]}],[\"$\",\"div\",null,{\"className\":\"pb-12\",\"children\":[\"$\",\"div\",null,{\"className\":\"mdx\",\"children\":[[\"$\",\"$L23\",null,{\"name\":\"current-user-avatar-demo\",\"showCode\":false,\"children\":[\"$\",\"div\",null,{\"data-rehype-pretty-code-fragment\":\"\",\"children\":[\"$\",\"$L24\",null,{\"styleName\":\"$undefined\",\"children\":[[\"$\",\"pre\",null,{\"className\":\"mb-4 mt-6 max-h-[650px] overflow-x-auto rounded-lg border bg-surface-75/75 py-4 text-foreground-light\",\"data-language\":\"tsx\",\"data-theme\":\"default\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"data-language\":\"tsx\",\"data-theme\":\"default\",\"children\":[[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"use client\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" Avatar\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" AvatarFallback\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\",\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" AvatarImage\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" }\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" from\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"@/components/components/ui/avatar\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" useUser\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" }\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" from\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"common\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" CurrentUserAvatarDemo\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" ()\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"  // this demo only works on supabase.com because all apps are on the same domain and share cookies\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"  const\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" user\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" useUser\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"  const\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" profileImage\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" user\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"?.\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"user_metadata\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"avatar_url\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" ??\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" null\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"  const\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" name\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"user\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"?.\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"user_metadata\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\".\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"full_name\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" as\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" string\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" ??\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"?\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"  const\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" initials\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" name\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    ?.\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"split\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    ?.\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"map\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"word\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\")\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" word\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"[\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"0\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"]\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    ?.\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"join\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"''\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    ?.\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"toUpperCase\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"  return\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"className\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"flex flex-col gap-4 items-center justify-center\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"      \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Avatar\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"        {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"profileImage\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" \u0026\u0026\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"AvatarImage \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"src\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"profileImage\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\" alt\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"initials\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" /\u003e}\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"        \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"AvatarFallback\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"initials\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\u003c/\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"AvatarFallback\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"      \u003c/\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Avatar\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"      {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"!\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"user\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" \u0026\u0026\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"        \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"span \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"className\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"text-sm text-foreground-light\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"          It seems like you\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u0026\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\"apos\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\";\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"re not logged in. Login via the\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"          \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"a\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"            target\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"_blank\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"            rel\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"noopener noreferrer\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"            href\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"https://supabase.com/dashboard/sign-in\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"            className\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"          \u003e\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"            Dashboard\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"          \u003c/\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"a\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"          to see your avatar.\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"        \u003c/\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"span\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"      )\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    \u003c/\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"div\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  )\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"export\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" CurrentUserAvatarDemo\"}]]}]]}]}],[\"$\",\"$L25\",null,{\"value\":\"$26\",\"src\":\"registry/default/examples/current-user-avatar-demo.tsx\",\"className\":\"absolute right-4 top-4\"}]]}]}]}],\"\\n\",[\"$\",\"h2\",null,{\"className\":\"font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0\",\"id\":\"installation\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#installation\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Installation\"]}],\"\\n\",[\"$\",\"$L27\",null,{\"name\":\"current-user-avatar-nextjs\",\"description\":\"Renders the avatar of the current user.\"}],\"\\n\",[\"$\",\"h2\",null,{\"className\":\"font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0\",\"id\":\"folder-structure\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#folder-structure\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Folder structure\"]}],\"\\n\",\"$L28\",\"\\n\",[\"$\",\"h2\",null,{\"className\":\"font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0\",\"id\":\"introduction\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#introduction\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Introduction\"]}],\"\\n\",[\"$\",\"p\",null,{\"className\":\"leading-7 [\u0026:not(:first-child)]:mt-6 text-foreground-light\",\"children\":[\"The \",[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"CurrentUserAvatar\"}],\" component connects to Supabase Auth to fetch the user data and show an avatar. It uses the \",[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"user_metadata\"}],\"\\nproperty which gets populated automatically by Supabase Auth if the user logged in via a provider. If the user doesn't have a profile image, it renders their initials. If the user is logged out, it renders a \",[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"?\"}],\" as a fallback, which you can change.\"]}],\"\\n\",[\"$\",\"h2\",null,{\"className\":\"font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0\",\"id\":\"usage\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#usage\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Usage\"]}],\"\\n\",[\"$\",\"p\",null,{\"className\":\"leading-7 [\u0026:not(:first-child)]:mt-6 text-foreground-light\",\"children\":[\"The \",[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"CurrentUserAvatar\"}],\" component is designed to be used anywhere in your app. Add the \",[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"\u003cCurrentUserAvatar /\u003e\"}],\" component to your page and it will render the avatar of the current user, with a fallback.\"]}],\"\\n\",[\"$\",\"div\",null,{\"data-rehype-pretty-code-fragment\":\"\",\"children\":[\"$\",\"$L24\",null,{\"styleName\":\"$undefined\",\"children\":[[\"$\",\"pre\",null,{\"className\":\"mb-4 mt-6 max-h-[650px] overflow-x-auto rounded-lg border bg-surface-75/75 py-4 text-foreground-light\",\"data-language\":\"tsx\",\"data-theme\":\"default\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"data-language\":\"tsx\",\"data-theme\":\"default\",\"children\":[[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"use client\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" CurrentUserAvatar\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" }\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" from\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"@/components/current-user-avatar\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"const\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" CurrentUserAvatarDemo\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" ()\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"  return\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\" (\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Header \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"className\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"flex items-center justify-between\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"      \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"h1\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Lumon Industries\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003c/\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"h1\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"      \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"CurrentUserAvatar \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"/\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"    \u003c/\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"Header\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"  )\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"export\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" default\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" CurrentUserAvatarDemo\"}]]}]]}]}],[\"$\",\"$L25\",null,{\"value\":\"'use client'\\n\\nimport { CurrentUserAvatar } from '@/components/current-user-avatar'\\n\\nconst CurrentUserAvatarDemo = () =\u003e {\\n  return (\\n    \u003cHeader className=\\\"flex items-center justify-between\\\"\u003e\\n      \u003ch1\u003eLumon Industries\u003c/h1\u003e\\n      \u003cCurrentUserAvatar /\u003e\\n    \u003c/Header\u003e\\n  )\\n}\\n\\nexport default CurrentUserAvatarDemo\\n\",\"src\":\"$undefined\",\"className\":\"absolute right-4 top-4\"}]]}]}],\"\\n\",[\"$\",\"h2\",null,{\"className\":\"font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0\",\"id\":\"props\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#props\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Props\"]}],\"\\n\",[\"$\",\"p\",null,{\"className\":\"leading-7 [\u0026:not(:first-child)]:mt-6 text-foreground-light\",\"children\":[\"This component doesn't accept any props. If you wish to change the fallback, you can do so by changing the \",[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"CurrentUserAvatar\"}],\" component directly.\"]}],\"\\n\",[\"$\",\"h2\",null,{\"className\":\"font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0\",\"id\":\"further-reading\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#further-reading\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Further reading\"]}],\"\\n\",[\"$\",\"ul\",null,{\"className\":\"my-6 ml-6 list-disc text-foreground-light\",\"children\":[\"\\n\",[\"$\",\"li\",null,{\"className\":\"mt-2\",\"children\":[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2\",\"href\":\"https://supabase.com/docs/guides/auth/users\",\"children\":\"Auth users\"}]}],\"\\n\"]}]]}]}]]}],[\"$\",\"div\",null,{\"className\":\"hidden text-sm xl:block\",\"children\":[\"$\",\"div\",null,{\"className\":\"sticky top-16 -mt-10 pt-4\",\"children\":[\"$\",\"$L7\",null,{\"className\":\"pb-10\",\"children\":[\"$\",\"div\",null,{\"className\":\"sticky top-16 -mt-10 h-[calc(100vh-3.5rem)] py-12\",\"children\":[\"$\",\"$L29\",null,{\"toc\":{\"items\":[{\"url\":\"#installation\",\"title\":\"Installation\"},{\"url\":\"#folder-structure\",\"title\":\"Folder structure\"},{\"url\":\"#introduction\",\"title\":\"Introduction\"},{\"url\":\"#usage\",\"title\":\"Usage\"},{\"url\":\"#props\",\"title\":\"Props\"},{\"url\":\"#further-reading\",\"title\":\"Further reading\"}]}}]}]}]}]}]]}]\n"])</script><script>self.__next_f.push([1,"2a:I[38866,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"BlockItemCode\"]\n2b:T864,"])</script><script>self.__next_f.push([1,"import { createServerClient } from '@supabase/ssr'\nimport { NextResponse, type NextRequest } from 'next/server'\n\nexport async function updateSession(request: NextRequest) {\n  let supabaseResponse = NextResponse.next({\n    request,\n  })\n\n  const supabase = createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return request.cookies.getAll()\n        },\n        setAll(cookiesToSet) {\n          cookiesToSet.forEach(({ name, value }) =\u003e request.cookies.set(name, value))\n          supabaseResponse = NextResponse.next({\n            request,\n          })\n          cookiesToSet.forEach(({ name, value, options }) =\u003e\n            supabaseResponse.cookies.set(name, value, options)\n          )\n        },\n      },\n    }\n  )\n\n  // Do not run code between createServerClient and\n  // supabase.auth.getClaims(). A simple mistake could make it very hard to debug\n  // issues with users being randomly logged out.\n\n  // IMPORTANT: DO NOT REMOVE auth.getClaims()\n  const { data } = await supabase.auth.getClaims()\n\n  const user = data?.claims\n\n  if (\n    !user \u0026\u0026\n    !request.nextUrl.pathname.startsWith('/login') \u0026\u0026\n    !request.nextUrl.pathname.startsWith('/auth')\n  ) {\n    // no user, potentially respond by redirecting the user to the login page\n    const url = request.nextUrl.clone()\n    url.pathname = '/auth/login'\n    return NextResponse.redirect(url)\n  }\n\n  // IMPORTANT: You *must* return the supabaseResponse object as it is.\n  // If you're creating a new response object with NextResponse.next() make sure to:\n  // 1. Pass the request in it, like so:\n  //    const myNewResponse = NextResponse.next({ request })\n  // 2. Copy over the cookies, like so:\n  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())\n  // 3. Change the myNewResponse object to fit your needs, but avoid changing\n  //    the cookies!\n  // 4. Finally:\n  //    return myNewResponse\n  // If this is not done, you may be causing the browser and server to go out\n  // of sync and terminate the user's session prematurely!\n\n  return supabaseResponse\n}\n"])</script><script>self.__next_f.push([1,"28:[\"$\",\"$L2a\",null,{\"files\":[{\"name\":\"components\",\"path\":\"/components\",\"originalPath\":\"registry/default/blocks/current-user-avatar/components/current-user-avatar.tsx\",\"type\":\"directory\",\"children\":[{\"name\":\"current-user-avatar.tsx\",\"path\":\"/components/current-user-avatar.tsx\",\"originalPath\":\"registry/default/blocks/current-user-avatar/components/current-user-avatar.tsx\",\"type\":\"file\",\"content\":\"'use client'\\n\\nimport { useCurrentUserImage } from '@/hooks/use-current-user-image'\\nimport { useCurrentUserName } from '@/hooks/use-current-user-name'\\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\\n\\nexport const CurrentUserAvatar = () =\u003e {\\n  const profileImage = useCurrentUserImage()\\n  const name = useCurrentUserName()\\n  const initials = name\\n    ?.split(' ')\\n    ?.map((word) =\u003e word[0])\\n    ?.join('')\\n    ?.toUpperCase()\\n\\n  return (\\n    \u003cAvatar\u003e\\n      {profileImage \u0026\u0026 \u003cAvatarImage src={profileImage} alt={initials} /\u003e}\\n      \u003cAvatarFallback\u003e{initials}\u003c/AvatarFallback\u003e\\n    \u003c/Avatar\u003e\\n  )\\n}\\n\"}]},{\"name\":\"hooks\",\"path\":\"/hooks\",\"originalPath\":\"registry/default/blocks/current-user-avatar/hooks/use-current-user-image.ts\",\"type\":\"directory\",\"children\":[{\"name\":\"use-current-user-image.ts\",\"path\":\"/hooks/use-current-user-image.ts\",\"originalPath\":\"registry/default/blocks/current-user-avatar/hooks/use-current-user-image.ts\",\"type\":\"file\",\"content\":\"import { createClient } from '@/lib/supabase/client'\\nimport { useEffect, useState } from 'react'\\n\\nexport const useCurrentUserImage = () =\u003e {\\n  const [image, setImage] = useState\u003cstring | null\u003e(null)\\n\\n  useEffect(() =\u003e {\\n    const fetchUserImage = async () =\u003e {\\n      const { data, error } = await createClient().auth.getSession()\\n      if (error) {\\n        console.error(error)\\n      }\\n\\n      setImage(data.session?.user.user_metadata.avatar_url ?? null)\\n    }\\n    fetchUserImage()\\n  }, [])\\n\\n  return image\\n}\\n\"},{\"name\":\"use-current-user-name.ts\",\"path\":\"/hooks/use-current-user-name.ts\",\"originalPath\":\"registry/default/blocks/current-user-avatar/hooks/use-current-user-name.ts\",\"type\":\"file\",\"content\":\"import { createClient } from '@/lib/supabase/client'\\nimport { useEffect, useState } from 'react'\\n\\nexport const useCurrentUserName = () =\u003e {\\n  const [name, setName] = useState\u003cstring | null\u003e(null)\\n\\n  useEffect(() =\u003e {\\n    const fetchProfileName = async () =\u003e {\\n      const { data, error } = await createClient().auth.getSession()\\n      if (error) {\\n        console.error(error)\\n      }\\n\\n      setName(data.session?.user.user_metadata.full_name ?? '?')\\n    }\\n\\n    fetchProfileName()\\n  }, [])\\n\\n  return name || '?'\\n}\\n\"}]},{\"name\":\"lib\",\"path\":\"/lib\",\"originalPath\":\"registry/default/clients/nextjs/lib/supabase/client.ts\",\"type\":\"directory\",\"children\":[{\"name\":\"supabase\",\"path\":\"/lib/supabase\",\"originalPath\":\"registry/default/clients/nextjs/lib/supabase/client.ts\",\"type\":\"directory\",\"children\":[{\"name\":\"client.ts\",\"path\":\"/lib/supabase/client.ts\",\"originalPath\":\"registry/default/clients/nextjs/lib/supabase/client.ts\",\"type\":\"file\",\"content\":\"import { createBrowserClient } from '@supabase/ssr'\\n\\nexport function createClient() {\\n  return createBrowserClient(\\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\\n  )\\n}\\n\"},{\"name\":\"middleware.ts\",\"path\":\"/lib/supabase/middleware.ts\",\"originalPath\":\"registry/default/clients/nextjs/lib/supabase/middleware.ts\",\"type\":\"file\",\"content\":\"$2b\"},{\"name\":\"server.ts\",\"path\":\"/lib/supabase/server.ts\",\"originalPath\":\"registry/default/clients/nextjs/lib/supabase/server.ts\",\"type\":\"file\",\"content\":\"import { createServerClient } from '@supabase/ssr'\\nimport { cookies } from 'next/headers'\\n\\nexport async function createClient() {\\n  const cookieStore = await cookies()\\n\\n  return createServerClient(\\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\\n    {\\n      cookies: {\\n        getAll() {\\n          return cookieStore.getAll()\\n        },\\n        setAll(cookiesToSet) {\\n          try {\\n            cookiesToSet.forEach(({ name, value, options }) =\u003e\\n              cookieStore.set(name, value, options)\\n            )\\n          } catch {\\n            // The `setAll` method was called from a Server Component.\\n            // This can be ignored if you have middleware refreshing\\n            // user sessions.\\n          }\\n        },\\n      },\\n    }\\n  )\\n}\\n\"}]}]}]}]\n"])</script><script>self.__next_f.push([1,"18:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n13:null\n"])</script><script>self.__next_f.push([1,"20:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Current User Avatar\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Supabase Auth-aware avatar\"}],[\"$\",\"meta\",\"2\",{\"name\":\"application-name\",\"content\":\"Supabase UI Library\"}],[\"$\",\"meta\",\"3\",{\"property\":\"og:title\",\"content\":\"Current User Avatar\"}],[\"$\",\"meta\",\"4\",{\"property\":\"og:description\",\"content\":\"Supabase Auth-aware avatar\"}],[\"$\",\"meta\",\"5\",{\"property\":\"og:url\",\"content\":\"https://supabase.com/undefined/docs/nextjs/current-user-avatar\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:image\",\"content\":\"https://supabase.com/ui/img/supabase-og-image.png\"}],[\"$\",\"meta\",\"7\",{\"property\":\"og:type\",\"content\":\"article\"}],[\"$\",\"meta\",\"8\",{\"property\":\"article:published_time\",\"content\":\"2025-07-30T14:07:10.034Z\"}],[\"$\",\"meta\",\"9\",{\"property\":\"article:modified_time\",\"content\":\"2025-07-30T14:07:10.034Z\"}],[\"$\",\"meta\",\"10\",{\"property\":\"article:author\",\"content\":\"Supabase\"}],[\"$\",\"meta\",\"11\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"12\",{\"name\":\"twitter:site\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"13\",{\"name\":\"twitter:creator\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"14\",{\"name\":\"twitter:title\",\"content\":\"Current User Avatar\"}],[\"$\",\"meta\",\"15\",{\"name\":\"twitter:description\",\"content\":\"Supabase Auth-aware avatar\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:image\",\"content\":\"https://supabase.com/ui/img/supabase-og-image.png\"}],[\"$\",\"link\",\"17\",{\"rel\":\"shortcut icon\",\"href\":\"/ui/favicon/favicon.ico\"}],[\"$\",\"link\",\"18\",{\"rel\":\"icon\",\"href\":\"/ui/favicon/favicon.ico\",\"type\":\"image/x-icon\"}],[\"$\",\"link\",\"19\",{\"rel\":\"apple-touch-icon\",\"href\":\"/ui/favicon/favicon.ico\"}],[\"$\",\"link\",\"20\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/ui/favicon/apple-icon-57x57.png\",\"sizes\":\"57x57\"}],[\"$\",\"link\",\"21\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/ui/favicon/apple-icon-60x60.png\",\"sizes\":\"60x60\"}],[\"$\",\"link\",\"22\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/ui/favicon/apple-icon-72x72.png\",\"sizes\":\"72x72\"}],[\"$\",\"link\",\"23\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/ui/favicon/apple-icon-76x76.png\",\"sizes\":\"76x76\"}],[\"$\",\"link\",\"24\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/ui/favicon/apple-icon-114x114.png\",\"sizes\":\"114x114\"}],[\"$\",\"link\",\"25\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/ui/favicon/apple-icon-120x120.png\",\"sizes\":\"120x120\"}],[\"$\",\"link\",\"26\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/ui/favicon/apple-icon-144x144.png\",\"sizes\":\"144x144\"}],[\"$\",\"link\",\"27\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/ui/favicon/apple-icon-152x152.png\",\"sizes\":\"152x152\"}],[\"$\",\"link\",\"28\",{\"rel\":\"icon\",\"href\":\"/ui/favicon/favicon-16x16.png\",\"type\":\"image/png\",\"sizes\":\"16x16\"}],[\"$\",\"link\",\"29\",{\"rel\":\"icon\",\"href\":\"/ui/favicon/favicon-32x32.png\",\"type\":\"image/png\",\"sizes\":\"32x32\"}],[\"$\",\"link\",\"30\",{\"rel\":\"icon\",\"href\":\"/ui/favicon/favicon-48x48.png\",\"type\":\"image/png\",\"sizes\":\"48x48\"}],[\"$\",\"link\",\"31\",{\"rel\":\"icon\",\"href\":\"/ui/favicon/favicon-96x96.png\",\"type\":\"image/png\",\"sizes\":\"96x96\"}],[\"$\",\"link\",\"32\",{\"rel\":\"icon\",\"href\":\"/ui/favicon/favicon-128x128.png\",\"type\":\"image/png\",\"sizes\":\"128x128\"}],[\"$\",\"link\",\"33\",{\"rel\":\"icon\",\"href\":\"/ui/favicon/favicon-180x180.png\",\"type\":\"image/png\",\"sizes\":\"180x180\"}],[\"$\",\"link\",\"34\",{\"rel\":\"icon\",\"href\":\"/ui/favicon/favicon-196x196.png\",\"type\":\"image/png\",\"sizes\":\"196x196\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"16:{\"metadata\":\"$20:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>