<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="https://supabase.com/ui/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" type="font/woff2"/><link rel="stylesheet" href="../../_next/static/css/03f63ad6e897ae58.css" data-precedence="next"/><link rel="stylesheet" href="../../_next/static/css/954cd54edf7b3efb.css" data-precedence="next"/><link rel="stylesheet" href="../../_next/static/css/be16f9d4f36b2c7a.css" data-precedence="next"/><link rel="stylesheet" href="../../_next/static/css/99f16e312f80aa1b.css" data-precedence="next"/><link rel="stylesheet" href="https://supabase.com/ui/_next/static/css/f12065469b9a7bd1.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="../../_next/static/chunks/webpack-e33ce3369a09e8d8.js"/><script src="../../_next/static/chunks/1c2ff98c-7d3bc7c78beee601.js" async=""></script><script src="../../_next/static/chunks/2053-b9dd66de32bef85e.js" async=""></script><script src="../../_next/static/chunks/main-app-acf3b350459af11a.js" async=""></script><script src="../../_next/static/chunks/6985-d9c94a68747ee58b.js" async=""></script><script src="../../_next/static/chunks/2105-5c6fb0a066c1340f.js" async=""></script><script src="../../_next/static/chunks/3502-8ffc67f87c6f59da.js" async=""></script><script src="../../_next/static/chunks/6276-e32f7d13c5c52f3b.js" async=""></script><script src="../../_next/static/chunks/6701-6b908838eebce949.js" async=""></script><script src="../../_next/static/chunks/6602-18415a95b5bdd8fe.js" async=""></script><script src="../../_next/static/chunks/5710-2cb44b983b4cfc0c.js" async=""></script><script src="../../_next/static/chunks/8443-3b56b7405ddfa048.js" async=""></script><script src="../../_next/static/chunks/494-582b02a90a51684d.js" async=""></script><script src="../../_next/static/chunks/app/(app)/layout-68e9ee4f0ef3c93b.js" async=""></script><script src="../../_next/static/chunks/3276-a6118a55186eb94a.js" async=""></script><script src="../../_next/static/chunks/898-7244b379490d4af4.js" async=""></script><script src="https://supabase.com/ui/_next/static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3" async=""></script><script src="../../_next/static/chunks/8815-1dd29c457db8514c.js" async=""></script><script src="https://supabase.com/ui/_next/static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3" async=""></script><meta name="next-size-adjust" content=""/><title>Realtime Chat</title><meta name="description" content="Real-time chat component for collaborative applications"/><meta name="application-name" content="Supabase UI Library"/><meta property="og:title" content="Realtime Chat"/><meta property="og:description" content="Real-time chat component for collaborative applications"/><meta property="og:url" content="https://supabase.com/undefined/docs/nextjs/realtime-chat"/><meta property="og:image" content="https://supabase.com/ui/img/supabase-og-image.png"/><meta property="og:type" content="article"/><meta property="article:published_time" content="2025-07-30T14:07:10.034Z"/><meta property="article:modified_time" content="2025-07-30T14:07:10.034Z"/><meta property="article:author" content="Supabase"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:site" content="@supabase"/><meta name="twitter:creator" content="@supabase"/><meta name="twitter:title" content="Realtime Chat"/><meta name="twitter:description" content="Real-time chat component for collaborative applications"/><meta name="twitter:image" content="https://supabase.com/ui/img/supabase-og-image.png"/><link rel="shortcut icon" href="../../favicon/favicon.ico"/><link rel="icon" href="../../favicon/favicon.ico" type="image/x-icon"/><link rel="apple-touch-icon" href="../../favicon/favicon.ico"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-57x57.png" sizes="57x57"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-60x60.png" sizes="60x60"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-72x72.png" sizes="72x72"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-76x76.png" sizes="76x76"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-114x114.png" sizes="114x114"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-120x120.png" sizes="120x120"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-144x144.png" sizes="144x144"/><link rel="apple-touch-icon-precomposed" href="../../favicon/apple-icon-152x152.png" sizes="152x152"/><link rel="icon" href="../../favicon/favicon-16x16.png" type="image/png" sizes="16x16"/><link rel="icon" href="../../favicon/favicon-32x32.png" type="image/png" sizes="32x32"/><link rel="icon" href="../../favicon/favicon-48x48.png" type="image/png" sizes="48x48"/><link rel="icon" href="../../favicon/favicon-96x96.png" type="image/png" sizes="96x96"/><link rel="icon" href="https://supabase.com/ui/favicon/favicon-128x128.png" type="image/png" sizes="128x128"/><link rel="icon" href="../../favicon/favicon-180x180.png" type="image/png" sizes="180x180"/><link rel="icon" href="../../favicon/favicon-196x196.png" type="image/png" sizes="196x196"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="../../_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_cbad29 antialiased"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&true)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}if(e==='light'||e==='dark')d.style.colorScheme=e}catch(e){}}()</script><div class="pt-10 md:pt-0"><main class="flex-1 max-w-site mx-auto w-full p-0"><div class="border-b"><div class="flex-1 items-start md:grid md:grid-cols-[220px_minmax(0,1fr)] lg:grid-cols-[240px_minmax(0,1fr)]"><div class="md:hidden fixed top-0 left-0 right-0 z-50 bg-background justify-between flex items-center px-8 py-3 border-b"><button data-size="tiny" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-«R2lmqlb»" data-state="closed" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-transparent border-strong hover:border-foreground-muted focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-xs px-2.5 py-1 h-[26px]"><div class="[&amp;_svg]:h-[14px] [&amp;_svg]:w-[14px] text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></div> <!-- --> </button></div><aside class="fixed z-30 top-0 hidden h-screen w-full shrink-0 md:sticky md:block bg-200 border-r border-muted/50"><div dir="ltr" class="relative overflow-hidden h-full" style="position:relative;--radix-scroll-area-corner-width:0px;--radix-scroll-area-corner-height:0px"><style>[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}</style><div data-radix-scroll-area-viewport="" class="h-full w-full rounded-[inherit]" style="overflow-x:hidden;overflow-y:hidden"><div style="min-width:100%;display:table"><nav class="flex flex-col h-full min-w-[220px]"><div class="p-6"><div class="flex items-start justify-between mb-4"><a href="../../../ui.html"><svg xmlns="http://www.w3.org/2000/svg" width="109" height="113" viewBox="0 0 109 113" fill="none" class="w-6 h-6"><path d="M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0625L99.1935 40.0625C107.384 40.0625 111.952 49.5226 106.859 55.9372L63.7076 110.284Z" fill="url(#paint0_linear)"></path><path d="M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0625L99.1935 40.0625C107.384 40.0625 111.952 49.5226 106.859 55.9372L63.7076 110.284Z" fill="url(#paint1_linear)" fill-opacity="0.2"></path><path d="M45.317 2.07103C48.1765 -1.53037 53.9745 0.442937 54.0434 5.041L54.4849 72.2922H9.83113C1.64038 72.2922 -2.92775 62.8321 2.1655 56.4175L45.317 2.07103Z" fill="#3ECF8E"></path><defs><linearGradient id="paint0_linear" x1="53.9738" y1="54.9738" x2="94.1635" y2="71.8293" gradientUnits="userSpaceOnUse"><stop stop-color="#249361"></stop><stop offset="1" stop-color="#3ECF8E"></stop></linearGradient><linearGradient id="paint1_linear" x1="36.1558" y1="30.5779" x2="54.4844" y2="65.0804" gradientUnits="userSpaceOnUse"><stop></stop><stop offset="1" stop-opacity="0"></stop></linearGradient></defs></svg></a></div><a class="mb-4 block" href="../../../ui.html"><h1>Supabase UI Library</h1></a><button data-size="tiny" type="button" class="cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border border-strong focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong px-2.5 py-1 relative h-8 w-full justify-start rounded-[0.5rem] bg-background text-sm font-normal text-foreground-muted shadow-none sm:pr-12 hover:border-foreground-muted hover:bg-surface-100 hover:text-foreground-lighter"> <span class="truncate"><span class="hidden lg:inline-flex">Search UI Library...</span><span class="inline-flex lg:hidden">Search...</span><kbd class="pointer-events-none absolute right-[0.3rem] top-[0.3rem] hidden h-5 select-none items-center gap-1 rounded border bg-surface-200 px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex text-foreground-light"><span class="text-sm">⌘</span>K</kbd></span> </button></div><div class="pb-6 space-y-0.5"><div class="font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest">Getting Started</div><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="../getting-started/introduction.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Introduction</a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="../getting-started/quickstart.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Quick Start</a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="../getting-started/faq.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>FAQ</a></div><div class="pb-6"><div class="font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest">Blocks</div><div class="space-y-0.5"><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="client.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Client</a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="password-based-auth.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Password-Based Auth</a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="social-auth.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Social Auth<div class="inline-flex items-center rounded-full bg-opacity-10 bg-brand text-brand-600 border border-brand-500 px-2.5 py-0.5 text-xs capitalize">NEW</div></a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="dropzone.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Dropzone</a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="realtime-cursor.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Realtime Cursor</a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="current-user-avatar.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Current User Avatar</a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="realtime-avatar-stack.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Realtime Avatar Stack</a><a class="relative flex items-center justify-between h-6 text-sm px-6 bg-surface-200 text-foreground transition-all" href="realtime-chat.html"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-100"></div>Realtime Chat</a><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="https://supabase.com/ui/docs/infinite-query-hook"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Infinite Query Hook<div class="inline-flex items-center rounded-full bg-opacity-10 bg-brand text-brand-600 border border-brand-500 px-2.5 py-0.5 text-xs capitalize">NEW</div></a></div></div><div class="pb-6 flex-1"><div class="font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest">AI Editors Rules</div><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="https://supabase.com/ui/docs/ai-editors-rules/prompts"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Prompts</a></div><div class="pb-6"><div class="font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest">Platform</div><a class="relative flex items-center justify-between h-6 text-sm text-foreground-lighter px-6 hover:bg-surface-100 hover:text-foreground transition-all" href="https://supabase.com/ui/docs/platform/platform-kit"><div class="transition absolute left-0 w-1 h-full bg-foreground opacity-0"></div>Platform Kit</a></div></nav></div></div></div></aside><div vaul-drawer-wrapper=""><div class="relative flex min-h-screen flex-col bg-background"><main class="relative lg:gap-10 xl:grid xl:grid-cols-[1fr_200px] px-8 md:px-16 py-20"><div class="mx-auto w-full min-w-0 max-w-4xl"><div class="mb-4 flex items-center space-x-1 text-sm text-foreground-muted"><div class="overflow-hidden text-ellipsis whitespace-nowrap">Docs</div><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right h-4 w-4 text-foreground-muted"><path d="m9 18 6-6-6-6"></path></svg><div class="text-foreground-lighter">Realtime Chat</div></div><div class="flex flex-col lg:flex-row lg:items-end justify-between mb-5"><div class="space-y-2"><h1 class="scroll-m-20 text-2xl lg:text-4xl tracking-tight">Realtime Chat</h1><p class="text-base lg:text-lg text-foreground-light"><span data-br="«R154utpmqlb»" data-brr="1" style="display:inline-block;vertical-align:top;text-decoration:inherit;text-wrap:balance">Real-time chat component for collaborative applications</span><script>self.__wrap_n=self.__wrap_n||(self.CSS&&CSS.supports("text-wrap","balance")?1:2);self.__wrap_b=(e,n,t)=>{let o=(t=t||document.querySelector(`[data-br="${e}"]`)).parentElement,r=e=>t.style.maxWidth=e+"px";t.style.maxWidth="";let s=o.clientWidth,a=o.clientHeight,c=s/2-.25,l=s+.5,i;if(s){for(r(c),c=Math.max(t.scrollWidth,c);c+1<l;)r(i=Math.round((c+l)/2)),o.clientHeight===a?l=i:c=i;r(l*n+s*(1-n))}t.__wrap_o||"undefined"!=typeof ResizeObserver&&(t.__wrap_o=new ResizeObserver(()=>{self.__wrap_b(0,+t.dataset.brr,t)})).observe(o)};self.__wrap_n!=1&&self.__wrap_b("«R154utpmqlb»",1)</script></p></div><button type="button" role="combobox" aria-controls="radix-«R94utpmqlb»" aria-expanded="false" aria-autocomplete="none" dir="ltr" data-state="closed" class="flex items-center justify-between rounded-md border border-strong hover:border-stronger bg-alternative dark:bg-muted hover:bg-selection ring-offset-background-control data-[placeholder]:text-foreground-lighter focus:outline-none ring-border-control focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200 data-[state=open]:bg-selection data-[state=open]:border-stronger gap-2 text-sm leading-4 px-3 py-2 h-[34px] w-[180px] mt-4 lg:mt-0"><span style="pointer-events:none"></span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down h-4 w-4 text-foreground-lighter" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button><select aria-hidden="true" tabindex="-1" style="position:absolute;border:0;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;word-wrap:normal"></select></div><div class="flex flex-col -space-y-px"></div><div class="pb-12"><div class="mdx"><div class="flex flex-row -space-x-px w-full"><div class="mt-4 w-full"><div class="relative border overflow-hidden bg-muted min-h-[150px] h-[600px] rounded-none"><div class="w-full h-full flex items-center justify-center"><!--$--><iframe src="https://supabase.com/ui/example/realtime-chat-demo?roomName=room-646" style="border:none;width:100%;height:100%;display:block" name="preview-frame"></iframe><!--/$--></div></div></div><div class="mt-4 w-full"><div class="relative border overflow-hidden bg-muted min-h-[150px] h-[600px] rounded-none"><div class="w-full h-full flex items-center justify-center"><!--$--><iframe src="https://supabase.com/ui/example/realtime-chat-demo?roomName=room-646" style="border:none;width:100%;height:100%;display:block" name="preview-frame"></iframe><!--/$--></div></div></div></div>
<h2 class="font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0" id="installation"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#installation"><span class="icon icon-link"></span></a>Installation</h2>
<div class="mt-4"><!--$!--><template data-dgst="BAILOUT_TO_CLIENT_SIDE_RENDERING"></template><!--/$--><a href="https://v0.dev/chat/api/open?url=https://supabase.com/ui/r/realtime-chat-nextjs.json" target="_blank" rel="noreferrer" class="inline-flex items-center justify-center font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-foreground-muted focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 py-2 h-7 gap-1 rounded-lg shadow-none bg-black px-3 text-xs text-white hover:bg-black hover:text-white dark:bg-white dark:text-black w-fit shrink-0 mt-4" aria-label="Open in v0">Open in<!-- --> <svg viewBox="0 0 40 20" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-current"><path d="M23.3919 0H32.9188C36.7819 0 39.9136 3.13165 39.9136 6.99475V16.0805H36.0006V6.99475C36.0006 6.90167 35.9969 6.80925 35.9898 6.71766L26.4628 16.079C26.4949 16.08 26.5272 16.0805 26.5595 16.0805H36.0006V19.7762H26.5595C22.6964 19.7762 19.4788 16.6139 19.4788 12.7508V3.68923H23.3919V12.7508C23.3919 12.9253 23.4054 13.0977 23.4316 13.2668L33.1682 3.6995C33.0861 3.6927 33.003 3.68923 32.9188 3.68923H23.3919V0Z" fill="currentColor"></path><path d="M13.7688 19.0956L0 3.68759H5.53933L13.6231 12.7337V3.68759H17.7535V17.5746C17.7535 19.6705 15.1654 20.6584 13.7688 19.0956Z" fill="currentColor"></path></svg></a></div>
<h2 class="font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0" id="folder-structure"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#folder-structure"><span class="icon icon-link"></span></a>Folder structure</h2>
<div class="flex mt-4 border rounded-lg overflow-hidden h-[652px] not-prose"><div class="w-64 grow-0 shrink-0 flex-0 py-2 border-r bg-muted/30 overflow-y-auto"><ul class="tree w-full" role="tree" aria-multiselectable="false" aria-label="file browser"><li role="treeitem" aria-expanded="true" aria-setsize="3" aria-posinset="1" aria-level="1" aria-disabled="false" tabindex="0" class="tree-branch-wrapper"><div class="group relative transition-colors h-[28px] flex items-center text-sm cursor-pointer select-none text-foreground-light hover:bg-control aria-expanded:bg-transparent data-[state=open]:bg-transparent gap-1.5" aria-selected="false" aria-expanded="true" style="padding-left:16px" data-treeview-is-branch="true" data-treeview-level="1"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right text-foreground-muted group-aria-selected:text-foreground-light group-aria-expanded:text-foreground-light transition-transform duration-200 group-aria-expanded:rotate-90"><path d="m9 18 6-6-6-6"></path></svg><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-folder-open transition-colors text-foreground-muted group-aria-selected:text-foreground-light group-aria-expanded:text-foreground-light"><path d="m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2"></path></svg><span class="truncate text-sm" title="components">components</span><form class="hidden"><input autofocus="" class="rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive block w-full text-sm px-2 py-1 h-7" value="components"/></form></div><ul role="group" class="tree-node-group tree-node-group--expanded"><li role="none" class="tree-leaf-list-item tree-leaf-list-item--selected"><div role="treeitem" tabindex="-1" class="group relative transition-colors h-[28px] flex items-center text-sm cursor-pointer select-none hover:bg-control aria-expanded:bg-transparent data-[state=open]:bg-transparent text-foreground !bg-selection gap-1.5" aria-setsize="2" aria-posinset="1" aria-level="2" aria-disabled="false" aria-selected="true" aria-expanded="false" style="padding-left:35px" data-treeview-is-branch="false" data-treeview-level="2"><div style="left:23px" class="absolute h-full w-px bg-border-strong"></div><div class="absolute left-0 h-full w-0.5 bg-foreground"></div><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file shrink-0"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path></svg><span class="truncate text-sm" title="chat-message.tsx">chat-message.tsx</span><form class="hidden"><input autofocus="" class="rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive block w-full text-sm px-2 py-1 h-7" value="chat-message.tsx"/></form></div></li><li role="none" class="tree-leaf-list-item"><div role="treeitem" tabindex="-1" class="group relative transition-colors h-[28px] flex items-center text-sm cursor-pointer select-none text-foreground-light hover:bg-control aria-expanded:bg-transparent data-[state=open]:bg-transparent gap-1.5" aria-setsize="2" aria-posinset="2" aria-level="2" aria-disabled="false" aria-selected="false" aria-expanded="false" style="padding-left:35px" data-treeview-is-branch="false" data-treeview-level="2"><div style="left:23px" class="absolute h-full w-px bg-border-strong"></div><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file shrink-0"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path></svg><span class="truncate text-sm" title="realtime-chat.tsx">realtime-chat.tsx</span><form class="hidden"><input autofocus="" class="rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive block w-full text-sm px-2 py-1 h-7" value="realtime-chat.tsx"/></form></div></li></ul></li><li role="treeitem" aria-expanded="true" aria-setsize="3" aria-posinset="2" aria-level="1" aria-disabled="false" tabindex="-1" class="tree-branch-wrapper"><div class="group relative transition-colors h-[28px] flex items-center text-sm cursor-pointer select-none text-foreground-light hover:bg-control aria-expanded:bg-transparent data-[state=open]:bg-transparent gap-1.5" aria-selected="false" aria-expanded="true" style="padding-left:16px" data-treeview-is-branch="true" data-treeview-level="1"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right text-foreground-muted group-aria-selected:text-foreground-light group-aria-expanded:text-foreground-light transition-transform duration-200 group-aria-expanded:rotate-90"><path d="m9 18 6-6-6-6"></path></svg><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-folder-open transition-colors text-foreground-muted group-aria-selected:text-foreground-light group-aria-expanded:text-foreground-light"><path d="m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2"></path></svg><span class="truncate text-sm" title="hooks">hooks</span><form class="hidden"><input autofocus="" class="rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive block w-full text-sm px-2 py-1 h-7" value="hooks"/></form></div><ul role="group" class="tree-node-group tree-node-group--expanded"><li role="none" class="tree-leaf-list-item"><div role="treeitem" tabindex="-1" class="group relative transition-colors h-[28px] flex items-center text-sm cursor-pointer select-none text-foreground-light hover:bg-control aria-expanded:bg-transparent data-[state=open]:bg-transparent gap-1.5" aria-setsize="2" aria-posinset="1" aria-level="2" aria-disabled="false" aria-selected="false" aria-expanded="false" style="padding-left:35px" data-treeview-is-branch="false" data-treeview-level="2"><div style="left:23px" class="absolute h-full w-px bg-border-strong"></div><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file shrink-0"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path></svg><span class="truncate text-sm" title="use-chat-scroll.tsx">use-chat-scroll.tsx</span><form class="hidden"><input autofocus="" class="rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive block w-full text-sm px-2 py-1 h-7" value="use-chat-scroll.tsx"/></form></div></li><li role="none" class="tree-leaf-list-item"><div role="treeitem" tabindex="-1" class="group relative transition-colors h-[28px] flex items-center text-sm cursor-pointer select-none text-foreground-light hover:bg-control aria-expanded:bg-transparent data-[state=open]:bg-transparent gap-1.5" aria-setsize="2" aria-posinset="2" aria-level="2" aria-disabled="false" aria-selected="false" aria-expanded="false" style="padding-left:35px" data-treeview-is-branch="false" data-treeview-level="2"><div style="left:23px" class="absolute h-full w-px bg-border-strong"></div><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file shrink-0"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path></svg><span class="truncate text-sm" title="use-realtime-chat.tsx">use-realtime-chat.tsx</span><form class="hidden"><input autofocus="" class="rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive block w-full text-sm px-2 py-1 h-7" value="use-realtime-chat.tsx"/></form></div></li></ul></li><li role="treeitem" aria-expanded="true" aria-setsize="3" aria-posinset="3" aria-level="1" aria-disabled="false" tabindex="-1" class="tree-branch-wrapper"><div class="group relative transition-colors h-[28px] flex items-center text-sm cursor-pointer select-none text-foreground-light hover:bg-control aria-expanded:bg-transparent data-[state=open]:bg-transparent gap-1.5" aria-selected="false" aria-expanded="true" style="padding-left:16px" data-treeview-is-branch="true" data-treeview-level="1"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right text-foreground-muted group-aria-selected:text-foreground-light group-aria-expanded:text-foreground-light transition-transform duration-200 group-aria-expanded:rotate-90"><path d="m9 18 6-6-6-6"></path></svg><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-folder-open transition-colors text-foreground-muted group-aria-selected:text-foreground-light group-aria-expanded:text-foreground-light"><path d="m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2"></path></svg><span class="truncate text-sm" title="lib">lib</span><form class="hidden"><input autofocus="" class="rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive block w-full text-sm px-2 py-1 h-7" value="lib"/></form></div><ul role="group" class="tree-node-group tree-node-group--expanded"><li role="treeitem" aria-expanded="true" aria-setsize="1" aria-posinset="1" aria-level="2" aria-disabled="false" tabindex="-1" class="tree-branch-wrapper"><div class="group relative transition-colors h-[28px] flex items-center text-sm cursor-pointer select-none text-foreground-light hover:bg-control aria-expanded:bg-transparent data-[state=open]:bg-transparent gap-1.5" aria-selected="false" aria-expanded="true" style="padding-left:35px" data-treeview-is-branch="true" data-treeview-level="2"><div style="left:23px" class="absolute h-full w-px bg-border-strong"></div><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right text-foreground-muted group-aria-selected:text-foreground-light group-aria-expanded:text-foreground-light transition-transform duration-200 group-aria-expanded:rotate-90"><path d="m9 18 6-6-6-6"></path></svg><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-folder-open transition-colors text-foreground-muted group-aria-selected:text-foreground-light group-aria-expanded:text-foreground-light"><path d="m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2"></path></svg><span class="truncate text-sm" title="supabase">supabase</span><form class="hidden"><input autofocus="" class="rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive block w-full text-sm px-2 py-1 h-7" value="supabase"/></form></div><ul role="group" class="tree-node-group tree-node-group--expanded"><li role="none" class="tree-leaf-list-item"><div role="treeitem" tabindex="-1" class="group relative transition-colors h-[28px] flex items-center text-sm cursor-pointer select-none text-foreground-light hover:bg-control aria-expanded:bg-transparent data-[state=open]:bg-transparent gap-1.5" aria-setsize="3" aria-posinset="1" aria-level="3" aria-disabled="false" aria-selected="false" aria-expanded="false" style="padding-left:54px" data-treeview-is-branch="false" data-treeview-level="3"><div style="left:23px" class="absolute h-full w-px bg-border-strong"></div><div style="left:42px" class="absolute h-full w-px bg-border-strong"></div><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file shrink-0"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path></svg><span class="truncate text-sm" title="client.ts">client.ts</span><form class="hidden"><input autofocus="" class="rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive block w-full text-sm px-2 py-1 h-7" value="client.ts"/></form></div></li><li role="none" class="tree-leaf-list-item"><div role="treeitem" tabindex="-1" class="group relative transition-colors h-[28px] flex items-center text-sm cursor-pointer select-none text-foreground-light hover:bg-control aria-expanded:bg-transparent data-[state=open]:bg-transparent gap-1.5" aria-setsize="3" aria-posinset="2" aria-level="3" aria-disabled="false" aria-selected="false" aria-expanded="false" style="padding-left:54px" data-treeview-is-branch="false" data-treeview-level="3"><div style="left:23px" class="absolute h-full w-px bg-border-strong"></div><div style="left:42px" class="absolute h-full w-px bg-border-strong"></div><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file shrink-0"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path></svg><span class="truncate text-sm" title="middleware.ts">middleware.ts</span><form class="hidden"><input autofocus="" class="rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive block w-full text-sm px-2 py-1 h-7" value="middleware.ts"/></form></div></li><li role="none" class="tree-leaf-list-item"><div role="treeitem" tabindex="-1" class="group relative transition-colors h-[28px] flex items-center text-sm cursor-pointer select-none text-foreground-light hover:bg-control aria-expanded:bg-transparent data-[state=open]:bg-transparent gap-1.5" aria-setsize="3" aria-posinset="3" aria-level="3" aria-disabled="false" aria-selected="false" aria-expanded="false" style="padding-left:54px" data-treeview-is-branch="false" data-treeview-level="3"><div style="left:23px" class="absolute h-full w-px bg-border-strong"></div><div style="left:42px" class="absolute h-full w-px bg-border-strong"></div><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file shrink-0"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path></svg><span class="truncate text-sm" title="server.ts">server.ts</span><form class="hidden"><input autofocus="" class="rounded-md border border-control bg-foreground/[.026] file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50 aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive block w-full text-sm px-2 py-1 h-7" value="server.ts"/></form></div></li></ul></li></ul></li></ul></div><div class="group relative max-w-[90vw] md:max-w-none overflow-auto w-full"><pre class="code-block border border-surface p-4 w-full !my-0 !bg-surface-100 outline-none focus:border-foreground-lighter/50 h-full max-w-none !w-full flex-1 font-mono text-xs rounded-none border-none" contentEditable="true" style="display:block;overflow-x:auto;color:#888;font-size:13px;line-height:1.4"><code class="language-ts" style="white-space:pre"><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">1</span><span style="color:#569cd6;font-weight:normal">import</span><span> { cn } </span><span style="color:#569cd6;font-weight:normal">from</span><span> </span><span style="color:#3ECF8E">&#x27;@/lib/utils&#x27;</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">2</span><span></span><span style="color:#569cd6;font-weight:normal">import</span><span> </span><span style="color:#569cd6;font-weight:normal">type</span><span> { ChatMessage } </span><span style="color:#569cd6;font-weight:normal">from</span><span> </span><span style="color:#3ECF8E">&#x27;@/hooks/use-realtime-chat&#x27;</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">3</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">4</span><span></span><span style="color:#569cd6;font-weight:normal">interface</span><span> ChatMessageItemProps {
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">5</span><span>  </span><span class="hljs-attr">message</span><span>: ChatMessage
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">6</span><span>  </span><span class="hljs-attr">isOwnMessage</span><span>: </span><span style="color:#3ECF8E">boolean</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">7</span><span>  </span><span class="hljs-attr">showHeader</span><span>: </span><span style="color:#3ECF8E">boolean</span><span>
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">8</span>}
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">9</span>
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">10</span><span></span><span style="color:#569cd6;font-weight:normal">export</span><span> </span><span style="color:#569cd6;font-weight:normal">const</span><span> ChatMessageItem = </span><span class="hljs-function">(</span><span class="hljs-function hljs-params">{ message, isOwnMessage, showHeader }: ChatMessageItemProps</span><span class="hljs-function">) =&gt;</span><span> {
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">11</span><span>  </span><span style="color:#569cd6;font-weight:normal">return</span><span> (
</span></span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">12</span>    &lt;div className={`flex mt-2 ${isOwnMessage ? &#x27;justify-end&#x27; : &#x27;justify-start&#x27;}`}&gt;
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">13</span>      &lt;div
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">14</span>        className={cn(&#x27;max-w-[75%] w-fit flex flex-col gap-1&#x27;, {
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">15</span>          &#x27;items-end&#x27;: isOwnMessage,
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">16</span>        })}
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">17</span>      &gt;
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">18</span>        {showHeader &amp;&amp; (
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">19</span>          &lt;div
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">20</span>            className={cn(&#x27;flex items-center gap-2 text-xs px-3&#x27;, {
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">21</span>              &#x27;justify-end flex-row-reverse&#x27;: isOwnMessage,
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">22</span>            })}
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">23</span>          &gt;
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">24</span>            &lt;span className={&#x27;font-medium&#x27;}&gt;{message.user.name}&lt;/span&gt;
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">25</span>            &lt;span className=&quot;text-foreground/50 text-xs&quot;&gt;
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">26</span>              {new Date(message.createdAt).toLocaleTimeString(&#x27;en-US&#x27;, {
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">27</span>                hour: &#x27;2-digit&#x27;,
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">28</span>                minute: &#x27;2-digit&#x27;,
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">29</span>                hour12: true,
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">30</span>              })}
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">31</span>            &lt;/span&gt;
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">32</span>          &lt;/div&gt;
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">33</span>        )}
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">34</span>        &lt;div
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">35</span>          className={cn(
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">36</span>            &#x27;py-2 px-3 rounded-xl text-sm w-fit&#x27;,
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">37</span>            isOwnMessage ? &#x27;bg-primary text-primary-foreground&#x27; : &#x27;bg-muted text-foreground&#x27;
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">38</span>          )}
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">39</span>        &gt;
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">40</span>          {message.content}
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">41</span>        &lt;/div&gt;
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">42</span>      &lt;/div&gt;
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">43</span>    &lt;/div&gt;
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">44</span>  )
</span><span><span class="comment linenumber react-syntax-highlighter-line-number" style="display:inline-block;min-width:44px;padding-right:4px;text-align:center;user-select:none;padding-left:4px;margin-right:12px;color:#828282;font-size:12px;padding-top:4px;padding-bottom:4px">45</span>}</span></code></pre><div class="absolute right-2 top-2 opacity-0 group-hover:opacity-100 transition "><button data-size="tiny" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs py-1 h-[26px] px-1.5"><div class="[&amp;_svg]:h-[14px] [&amp;_svg]:w-[14px] text-foreground-lighter"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></div> <!-- --> </button></div></div></div>
<h2 class="font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0" id="introduction"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#introduction"><span class="icon icon-link"></span></a>Introduction</h2>
<p class="leading-7 [&amp;:not(:first-child)]:mt-6 text-foreground-light">The Realtime Chat component provides a complete chat interface that enables users to exchange messages in real-time within a shared room.</p>
<h2 class="font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0" id="usage"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#usage"><span class="icon icon-link"></span></a>Usage</h2>
<h3 class="font-heading mt-8 scroll-m-20 text-xl tracking-tight" id="basic-usage"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#basic-usage"><span class="icon icon-link"></span></a>Basic usage</h3>
<div data-rehype-pretty-code-fragment=""><pre class="mb-4 mt-6 max-h-[650px] overflow-x-auto rounded-lg border bg-surface-75/75 py-4 text-foreground-light" data-language="tsx" data-theme="default"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm" data-language="tsx" data-theme="default"><span class="line"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-punctuation)"> {</span><span style="color:var(--code-token-parameter)"> RealtimeChat</span><span style="color:var(--code-token-punctuation)"> }</span><span style="color:var(--code-token-keyword)"> from</span><span style="color:var(--code-token-punctuation)"> &#x27;</span><span style="color:var(--code-token-string-expression)">@/components/realtime-chat</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-keyword)">export</span><span style="color:var(--code-token-keyword)"> default</span><span style="color:var(--code-token-keyword)"> function</span><span style="color:var(--code-token-function)"> ChatPage</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-token-punctuation)"> {</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">  return</span><span style="color:var(--code-token-punctuation)"> &lt;</span><span style="color:var(--code-token-function)">RealtimeChat </span><span style="color:var(--code-token-property)">roomName</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)">my-chat-room</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-property)"> username</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)">john_doe</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-punctuation)"> /&gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">}</span></span></code></pre><button data-size="small" type="button" class="justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong hover:border-foreground-muted focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-sm leading-4 z-10 h-6 w-6 text-foreground-muted hover:bg-surface-100 hover:text-foreground p-0 absolute right-4 top-4"> <span class="truncate"><span class="sr-only">Copy</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy h-3 w-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></span> </button></div>
<h3 class="font-heading mt-8 scroll-m-20 text-xl tracking-tight" id="with-initial-messages"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#with-initial-messages"><span class="icon icon-link"></span></a>With initial messages</h3>
<div data-rehype-pretty-code-fragment=""><pre class="mb-4 mt-6 max-h-[650px] overflow-x-auto rounded-lg border bg-surface-75/75 py-4 text-foreground-light" data-language="tsx" data-theme="default"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm" data-language="tsx" data-theme="default"><span class="line"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-punctuation)"> {</span><span style="color:var(--code-token-parameter)"> RealtimeChat</span><span style="color:var(--code-token-punctuation)"> }</span><span style="color:var(--code-token-keyword)"> from</span><span style="color:var(--code-token-punctuation)"> &#x27;</span><span style="color:var(--code-token-string-expression)">@/components/realtime-chat</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-punctuation)"> {</span><span style="color:var(--code-token-parameter)"> useMessagesQuery</span><span style="color:var(--code-token-punctuation)"> }</span><span style="color:var(--code-token-keyword)"> from</span><span style="color:var(--code-token-punctuation)"> &#x27;</span><span style="color:var(--code-token-string-expression)">@/hooks/use-messages-query</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-keyword)">export</span><span style="color:var(--code-token-keyword)"> default</span><span style="color:var(--code-token-keyword)"> function</span><span style="color:var(--code-token-function)"> ChatPage</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-token-punctuation)"> {</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">  const</span><span style="color:var(--code-token-punctuation)"> {</span><span style="color:var(--code-token-parameter)"> data</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-constant)"> messages</span><span style="color:var(--code-token-punctuation)"> }</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-function)"> useMessagesQuery</span><span style="color:var(--code-foreground)">()</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-keyword)">  return</span><span style="color:var(--code-token-punctuation)"> &lt;</span><span style="color:var(--code-token-function)">RealtimeChat </span><span style="color:var(--code-token-property)">roomName</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)">my-chat-room</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-property)"> username</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)">john_doe</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-property)"> messages</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-token-parameter)">messages</span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-punctuation)"> /&gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">}</span></span></code></pre><button data-size="small" type="button" class="justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong hover:border-foreground-muted focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-sm leading-4 z-10 h-6 w-6 text-foreground-muted hover:bg-surface-100 hover:text-foreground p-0 absolute right-4 top-4"> <span class="truncate"><span class="sr-only">Copy</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy h-3 w-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></span> </button></div>
<h3 class="font-heading mt-8 scroll-m-20 text-xl tracking-tight" id="storing-messages"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#storing-messages"><span class="icon icon-link"></span></a>Storing messages</h3>
<div data-rehype-pretty-code-fragment=""><pre class="mb-4 mt-6 max-h-[650px] overflow-x-auto rounded-lg border bg-surface-75/75 py-4 text-foreground-light" data-language="tsx" data-theme="default"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm" data-language="tsx" data-theme="default"><span class="line"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-punctuation)"> {</span><span style="color:var(--code-token-parameter)"> RealtimeChat</span><span style="color:var(--code-token-punctuation)"> }</span><span style="color:var(--code-token-keyword)"> from</span><span style="color:var(--code-token-punctuation)"> &#x27;</span><span style="color:var(--code-token-string-expression)">@/components/realtime-chat</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-punctuation)"> {</span><span style="color:var(--code-token-parameter)"> useMessagesQuery</span><span style="color:var(--code-token-punctuation)"> }</span><span style="color:var(--code-token-keyword)"> from</span><span style="color:var(--code-token-punctuation)"> &#x27;</span><span style="color:var(--code-token-string-expression)">@/hooks/use-messages-query</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">import</span><span style="color:var(--code-token-punctuation)"> {</span><span style="color:var(--code-token-parameter)"> storeMessages</span><span style="color:var(--code-token-punctuation)"> }</span><span style="color:var(--code-token-keyword)"> from</span><span style="color:var(--code-token-punctuation)"> &#x27;</span><span style="color:var(--code-token-string-expression)">@/lib/store-messages</span><span style="color:var(--code-token-punctuation)">&#x27;</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-keyword)">export</span><span style="color:var(--code-token-keyword)"> default</span><span style="color:var(--code-token-keyword)"> function</span><span style="color:var(--code-token-function)"> ChatPage</span><span style="color:var(--code-token-punctuation)">()</span><span style="color:var(--code-token-punctuation)"> {</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">  const</span><span style="color:var(--code-token-punctuation)"> {</span><span style="color:var(--code-token-parameter)"> data</span><span style="color:var(--code-token-punctuation)">:</span><span style="color:var(--code-token-constant)"> messages</span><span style="color:var(--code-token-punctuation)"> }</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-function)"> useMessagesQuery</span><span style="color:var(--code-foreground)">()</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">  const</span><span style="color:var(--code-token-function)"> handleMessage</span><span style="color:var(--code-token-keyword)"> =</span><span style="color:var(--code-token-punctuation)"> (</span><span style="color:var(--code-token-parameter)">messages</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-token-function)"> ChatMessage</span><span style="color:var(--code-token-punctuation)">[])</span><span style="color:var(--code-token-keyword)"> =&gt;</span><span style="color:var(--code-token-punctuation)"> {</span></span>
<span class="line"><span style="color:var(--code-token-comment);font-style:italic">    // Store messages in your database</span></span>
<span class="line"><span style="color:var(--code-token-keyword)">    await</span><span style="color:var(--code-token-function)"> storeMessages</span><span style="color:var(--code-foreground)">(</span><span style="color:var(--code-token-parameter)">messages</span><span style="color:var(--code-foreground)">)</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">  }</span></span>
<span class="line"> </span>
<span class="line"><span style="color:var(--code-token-keyword)">  return</span><span style="color:var(--code-token-punctuation)"> &lt;</span><span style="color:var(--code-token-function)">RealtimeChat </span><span style="color:var(--code-token-property)">roomName</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)">my-chat-room</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-property)"> username</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-string-expression)">john_doe</span><span style="color:var(--code-token-punctuation)">&quot;</span><span style="color:var(--code-token-property)"> onMessage</span><span style="color:var(--code-token-keyword)">=</span><span style="color:var(--code-token-punctuation)">{</span><span style="color:var(--code-token-parameter)">handleMessage</span><span style="color:var(--code-token-punctuation)">}</span><span style="color:var(--code-token-punctuation)"> /&gt;</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">}</span></span></code></pre><button data-size="small" type="button" class="justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong hover:border-foreground-muted focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-sm leading-4 z-10 h-6 w-6 text-foreground-muted hover:bg-surface-100 hover:text-foreground p-0 absolute right-4 top-4"> <span class="truncate"><span class="sr-only">Copy</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy h-3 w-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></span> </button></div>
<h2 class="font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0" id="features"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#features"><span class="icon icon-link"></span></a>Features</h2>
<ul class="my-6 ml-6 list-disc text-foreground-light">
<li class="mt-2">Real-time message synchronization</li>
<li class="mt-2">Message persistence support with <code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">onMessage</code> and <code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">messages</code> props</li>
<li class="mt-2">Customizable message appearance</li>
<li class="mt-2">Automatic scroll-to-bottom on new messages</li>
<li class="mt-2">Room-based isolation for scoped conversations</li>
<li class="mt-2">Low-latency updates using Supabase Realtime</li>
</ul>
<h2 class="font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0" id="props"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#props"><span class="icon icon-link"></span></a>Props</h2>
<div class="my-6 w-full overflow-y-auto"><table class="w-full"><thead><tr class="m-0 border-t p-0 even:bg-surface-75/75"><th class="border px-4 py-2 text-left font-normal [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right">Prop</th><th class="border px-4 py-2 text-left font-normal [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right">Type</th><th class="border px-4 py-2 text-left font-normal [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right">Description</th></tr></thead><tbody><tr class="m-0 border-t p-0 even:bg-surface-75/75"><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">roomName</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">string</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right">Unique identifier for the shared chat room.</td></tr><tr class="m-0 border-t p-0 even:bg-surface-75/75"><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">username</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">string</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right">Name of the current user; used to identify message senders.</td></tr><tr class="m-0 border-t p-0 even:bg-surface-75/75"><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">onMessage?</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">(messages: ChatMessage[]) =&gt; void</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right">Optional callback to handle messages, useful for persistence.</td></tr><tr class="m-0 border-t p-0 even:bg-surface-75/75"><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">messages?</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">ChatMessage[]</code></td><td class="border text-foreground-light px-4 py-2 text-left [&amp;[align=center]]:text-center [&amp;[align=right]]:text-right">Optional initial messages to display in the chat.</td></tr></tbody></table></div>
<h3 class="font-heading mt-8 scroll-m-20 text-xl tracking-tight" id="chatmessage-type"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#chatmessage-type"><span class="icon icon-link"></span></a>ChatMessage type</h3>
<div data-rehype-pretty-code-fragment=""><pre class="mb-4 mt-6 max-h-[650px] overflow-x-auto rounded-lg border bg-surface-75/75 py-4 text-foreground-light" data-language="typescript" data-theme="default"><code class="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm" data-language="typescript" data-theme="default"><span class="line"><span style="color:var(--code-token-keyword)">interface</span><span style="color:var(--code-token-function)"> ChatMessage</span><span style="color:var(--code-token-punctuation)"> {</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">  id</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-token-function)"> string</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">  content</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-token-function)"> string</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">  user</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-token-punctuation)"> {</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">    name</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-token-function)"> string</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">  }</span></span>
<span class="line"><span style="color:var(--code-token-parameter)">  createdAt</span><span style="color:var(--code-token-keyword)">:</span><span style="color:var(--code-token-function)"> string</span></span>
<span class="line"><span style="color:var(--code-token-punctuation)">}</span></span></code></pre><button data-size="small" type="button" class="justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-transparent border-strong hover:border-foreground-muted focus-visible:outline-border-strong data-[state=open]:border-stronger data-[state=open]:outline-border-strong text-sm leading-4 z-10 h-6 w-6 text-foreground-muted hover:bg-surface-100 hover:text-foreground p-0 absolute right-4 top-4"> <span class="truncate"><span class="sr-only">Copy</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy h-3 w-3"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></span> </button></div>
<h2 class="font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0" id="further-reading"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor" aria-label="Link to section" href="#further-reading"><span class="icon icon-link"></span></a>Further reading</h2>
<ul class="my-6 ml-6 list-disc text-foreground-light">
<li class="mt-2"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2" href="../../../docs/guides/realtime/broadcast.html">Realtime Broadcast</a></li>
<li class="mt-2"><a class="text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2" href="../../../docs/guides/realtime/authorization.html">Realtime authorization</a></li>
</ul></div></div></div><div class="hidden text-sm xl:block"><div class="sticky top-16 -mt-10 pt-4"><div dir="ltr" class="relative overflow-hidden pb-10" style="position:relative;--radix-scroll-area-corner-width:0px;--radix-scroll-area-corner-height:0px"><style>[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}</style><div data-radix-scroll-area-viewport="" class="h-full w-full rounded-[inherit]" style="overflow-x:hidden;overflow-y:hidden"><div style="min-width:100%;display:table"><div class="sticky top-16 -mt-10 h-[calc(100vh-3.5rem)] py-12"></div></div></div></div></div></div></main><!--$--><!--/$--><!--$--><!--/$--></div></div></div></div></main></div><footer class="py-6 px-4 md:px-8 md:py-0 mx-auto w-full max-w-site"><div class="flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row"><p class="text-balance text-center text-sm leading-loose text-foreground-muted md:text-left">Built by<!-- --> <a href="https://twitter.com/supabase" target="_blank" rel="noreferrer" class="font-medium underline underline-offset-4 hover:text-foreground-lighter">Supabase</a>. The source code is available on<!-- --> <a href="https://github.com/supabase/supabase/tree/master/apps/ui-library" target="_blank" rel="noreferrer" class="font-medium underline underline-offset-4 hover:text-foreground-lighter">GitHub</a>.</p><p class="text-balance text-center text-sm leading-loose text-foreground-muted">Site inspired by<!-- --> <a href="https://www.radix-ui.com/themes/docs/overview/getting-started" target="_blank" rel="noreferrer" class="font-medium underline underline-offset-4 hover:text-foreground-lighter">Radix</a>,<!-- --> <a href="https://ui.shadcn.com" target="_blank" rel="noreferrer" class="font-medium underline underline-offset-4 hover:text-foreground-lighter">shadcn/ui</a>.</p></div></footer><script src="../../_next/static/chunks/webpack-e33ce3369a09e8d8.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n3:I[52595,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"4944\",\"static/chunks/app/(app)/layout-68e9ee4f0ef3c93b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"MobileMenuSheet\"]\n4:I[86084,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app"])</script><script>self.__next_f.push([1,"/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"SheetTrigger\"]\n5:I[35621,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"Button\"]\n6:I[86084,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513"])</script><script>self.__next_f.push([1,"\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"SheetContent\"]\n7:I[28694,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"ScrollArea\"]\n8:I[15531,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4"])</script><script>self.__next_f.push([1,"WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"\"]\n9:I[47642,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"4944\",\"static/chunks/app/(app)/layout-68e9ee4f0ef3c93b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"ThemeSwitcherDropdown\"]\na:I[28044,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4"])</script><script>self.__next_f.push([1,"WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"4944\",\"static/chunks/app/(app)/layout-68e9ee4f0ef3c93b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"CommandMenu\"]\nb:I[22792,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"4944\",\"static/chunks/app/(app)/layout-68e9ee4f0ef3c93b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"default\"]\nc:I[93926,[],\"\"]\nd:I[6252,[],\"\"]\ne:I[63645,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv"])</script><script>self.__next_f.push([1,"1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"4944\",\"static/chunks/app/(app)/layout-68e9ee4f0ef3c93b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"TelemetryWrapper\"]\n10:I[18206,[],\"MetadataBoundary\"]\n12:I[18206,[],\"OutletBoundary\"]\n15:I[38670,[],\"AsyncMetadataOutlet\"]\n17:I[18206,[],\"ViewportBoundary\"]\n19:I[47249,[],\"\"]\n:HL[\"/ui/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/ui/_next/static/css/03f63ad6e897ae58.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"style\"]\n:HL[\"/ui/_next/static/css/954cd54edf7b3efb.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"style\"]\n:HL[\"/ui/_next/static/css/be16f9d4f36b2c7a.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"style\"]\n:HL[\"/ui/_next/static/css/99f16e312f80aa1b.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"style\"]\n:HL[\"/ui/_next/static/css/f12065469b9a7bd1.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"InVsSGMudyjao1kx6wnTY\",\"p\":\"/ui\",\"c\":[\"\",\"docs\",\"nextjs\",\"realtime-chat\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"(app)\",{\"children\":[\"docs\",{\"children\":[[\"slug\",\"nextjs/realtime-chat\",\"oc\"],{\"children\":[\"__PAGE__\",{}]}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/ui/_next/static/css/03f63ad6e897ae58.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/ui/_next/static/css/954cd54edf7b3efb.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"2\",{\"rel\":\"stylesheet\",\"href\":\"/ui/_next/static/css/be16f9d4f36b2c7a.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],\"$L2\"]}],{\"children\":[\"(app)\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/ui/_next/static/css/99f16e312f80aa1b.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[[\"$\",\"div\",null,{\"className\":\"pt-10 md:pt-0\",\"children\":[\"$\",\"main\",null,{\"className\":\"flex-1 max-w-site mx-auto w-full p-0\",\"children\":[\"$\",\"div\",null,{\"className\":\"border-b\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex-1 items-start md:grid md:grid-cols-[220px_minmax(0,1fr)] lg:grid-cols-[240px_minmax(0,1fr)]\",\"children\":[[[\"$\",\"div\",null,{\"className\":\"md:hidden fixed top-0 left-0 right-0 z-50 bg-background justify-between flex items-center px-8 py-3 border-b\",\"children\":[[\"$\",\"$L3\",null,{\"children\":[[\"$\",\"$L4\",null,{\"asChild\":true,\"children\":[\"$\",\"$L5\",null,{\"type\":\"outline\",\"icon\":[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-menu\",\"children\":[[\"$\",\"line\",\"1e0a9i\",{\"x1\":\"4\",\"x2\":\"20\",\"y1\":\"12\",\"y2\":\"12\"}],[\"$\",\"line\",\"1owob3\",{\"x1\":\"4\",\"x2\":\"20\",\"y1\":\"6\",\"y2\":\"6\"}],[\"$\",\"line\",\"yk5zj1\",{\"x1\":\"4\",\"x2\":\"20\",\"y1\":\"18\",\"y2\":\"18\"}],\"$undefined\"]}]}]}],[\"$\",\"$L6\",null,{\"side\":\"left\",\"className\":\"p-0 w-80\",\"showClose\":false,\"children\":[\"$\",\"$L7\",null,{\"className\":\"h-full\",\"children\":[\"$\",\"nav\",null,{\"className\":\"flex flex-col h-full min-w-[220px]\",\"children\":[[\"$\",\"div\",null,{\"className\":\"p-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-start justify-between mb-4\",\"children\":[[\"$\",\"$L8\",null,{\"href\":\"/\",\"children\":[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":\"109\",\"height\":\"113\",\"viewBox\":\"0 0 109 113\",\"fill\":\"none\",\"className\":\"w-6 h-6\",\"children\":[[\"$\",\"path\",null,{\"d\":\"M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0625L99.1935 40.0625C107.384 40.0625 111.952 49.5226 106.859 55.9372L63.7076 110.284Z\",\"fill\":\"url(#paint0_linear)\"}],[\"$\",\"path\",null,{\"d\":\"M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0625L99.1935 40.0625C107.384 40.0625 111.952 49.5226 106.859 55.9372L63.7076 110.284Z\",\"fill\":\"url(#paint1_linear)\",\"fillOpacity\":\"0.2\"}],[\"$\",\"path\",null,{\"d\":\"M45.317 2.07103C48.1765 -1.53037 53.9745 0.442937 54.0434 5.041L54.4849 72.2922H9.83113C1.64038 72.2922 -2.92775 62.8321 2.1655 56.4175L45.317 2.07103Z\",\"fill\":\"#3ECF8E\"}],[\"$\",\"defs\",null,{\"children\":[[\"$\",\"linearGradient\",null,{\"id\":\"paint0_linear\",\"x1\":\"53.9738\",\"y1\":\"54.9738\",\"x2\":\"94.1635\",\"y2\":\"71.8293\",\"gradientUnits\":\"userSpaceOnUse\",\"children\":[[\"$\",\"stop\",null,{\"stopColor\":\"#249361\"}],[\"$\",\"stop\",null,{\"offset\":\"1\",\"stopColor\":\"#3ECF8E\"}]]}],[\"$\",\"linearGradient\",null,{\"id\":\"paint1_linear\",\"x1\":\"36.1558\",\"y1\":\"30.5779\",\"x2\":\"54.4844\",\"y2\":\"65.0804\",\"gradientUnits\":\"userSpaceOnUse\",\"children\":[[\"$\",\"stop\",null,{}],[\"$\",\"stop\",null,{\"offset\":\"1\",\"stopOpacity\":\"0\"}]]}]]}]]}]}],[\"$\",\"$L9\",null,{}]]}],[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"mb-4 block\",\"children\":[\"$\",\"h1\",null,{\"children\":\"Supabase UI Library\"}]}],[\"$\",\"$La\",null,{}]]}],[\"$\",\"div\",null,{\"className\":\"pb-6 space-y-0.5\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest\",\"children\":\"Getting Started\"}],[[\"$\",\"$Lb\",\"/docs/getting-started/introduction-0\",{\"item\":{\"title\":\"Introduction\",\"href\":\"/docs/getting-started/introduction\",\"items\":[],\"commandItemLabel\":\"Introduction\"}}],[\"$\",\"$Lb\",\"/docs/getting-started/quickstart-1\",{\"item\":{\"title\":\"Quick Start\",\"href\":\"/docs/getting-started/quickstart\",\"items\":[],\"commandItemLabel\":\"Quick Start\"}}],[\"$\",\"$Lb\",\"/docs/getting-started/faq-2\",{\"item\":{\"title\":\"FAQ\",\"href\":\"/docs/getting-started/faq\",\"items\":[],\"commandItemLabel\":\"FAQ\"}}]]]}],[\"$\",\"div\",null,{\"className\":\"pb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest\",\"children\":\"Blocks\"}],[\"$\",\"div\",null,{\"className\":\"space-y-0.5\",\"children\":[[\"$\",\"$Lb\",\"/docs/nextjs/client-0\",{\"item\":{\"title\":\"Client\",\"supportedFrameworks\":[\"nextjs\",\"react-router\",\"tanstack\",\"react\"],\"href\":\"/docs/nextjs/client\",\"items\":[],\"commandItemLabel\":\"Supabase Client\"}}],[\"$\",\"$Lb\",\"/docs/nextjs/password-based-auth-1\",{\"item\":{\"title\":\"Password-Based Auth\",\"supportedFrameworks\":[\"nextjs\",\"react-router\",\"tanstack\",\"react\"],\"href\":\"/docs/nextjs/password-based-auth\",\"items\":[],\"commandItemLabel\":\"Password-Based Auth\"}}],[\"$\",\"$Lb\",\"/docs/nextjs/social-auth-2\",{\"item\":{\"title\":\"Social Auth\",\"supportedFrameworks\":[\"nextjs\",\"react-router\",\"tanstack\",\"react\"],\"href\":\"/docs/nextjs/social-auth\",\"items\":[],\"new\":true,\"commandItemLabel\":\"Social Auth\"}}],[\"$\",\"$Lb\",\"/docs/nextjs/dropzone-3\",{\"item\":{\"title\":\"Dropzone\",\"supportedFrameworks\":[\"nextjs\",\"react-router\",\"tanstack\",\"react\"],\"href\":\"/docs/nextjs/dropzone\",\"items\":[],\"commandItemLabel\":\"Dropzone (File Upload)\"}}],[\"$\",\"$Lb\",\"/docs/nextjs/realtime-cursor-4\",{\"item\":{\"title\":\"Realtime Cursor\",\"supportedFrameworks\":[\"nextjs\",\"react-router\",\"tanstack\",\"react\"],\"href\":\"/docs/nextjs/realtime-cursor\",\"items\":[],\"commandItemLabel\":\"Realtime Cursor\"}}],[\"$\",\"$Lb\",\"/docs/nextjs/current-user-avatar-5\",{\"item\":{\"title\":\"Current User Avatar\",\"supportedFrameworks\":[\"nextjs\",\"react-router\",\"tanstack\",\"react\"],\"href\":\"/docs/nextjs/current-user-avatar\",\"items\":[],\"commandItemLabel\":\"Current User Avatar\"}}],[\"$\",\"$Lb\",\"/docs/nextjs/realtime-avatar-stack-6\",{\"item\":{\"title\":\"Realtime Avatar Stack\",\"supportedFrameworks\":[\"nextjs\",\"react-router\",\"tanstack\",\"react\"],\"href\":\"/docs/nextjs/realtime-avatar-stack\",\"items\":[],\"commandItemLabel\":\"Realtime Avatar Stack\"}}],[\"$\",\"$Lb\",\"/docs/nextjs/realtime-chat-7\",{\"item\":{\"title\":\"Realtime Chat\",\"supportedFrameworks\":[\"nextjs\",\"react-router\",\"tanstack\",\"react\"],\"href\":\"/docs/nextjs/realtime-chat\",\"items\":[],\"commandItemLabel\":\"Realtime Chat\"}}],[\"$\",\"$Lb\",\"/docs/infinite-query-hook-8\",{\"item\":{\"title\":\"Infinite Query Hook\",\"supportedFrameworks\":[],\"href\":\"/docs/infinite-query-hook\",\"new\":true,\"items\":[],\"commandItemLabel\":\"Infinite Query Hook\"}}]]}]]}],[\"$\",\"div\",null,{\"className\":\"pb-6 flex-1\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest\",\"children\":\"AI Editors Rules\"}],[[\"$\",\"$Lb\",\"/docs/ai-editors-rules/prompts-0\",{\"item\":{\"title\":\"Prompts\",\"href\":\"/docs/ai-editors-rules/prompts\",\"items\":[],\"commandItemLabel\":\"AI Editors Rules\"}}]]]}],[\"$\",\"div\",null,{\"className\":\"pb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest\",\"children\":\"Platform\"}],[[\"$\",\"$Lb\",\"/docs/platform/platform-kit-0\",{\"item\":{\"title\":\"Platform Kit\",\"href\":\"/docs/platform/platform-kit\",\"items\":[],\"commandItemLabel\":\"Platform Kit\"}}]]]}]]}]}]}]]}],[\"$\",\"$L9\",null,{}]]}],[\"$\",\"aside\",null,{\"className\":\"fixed z-30 top-0 hidden h-screen w-full shrink-0 md:sticky md:block bg-200 border-r border-muted/50\",\"children\":[\"$\",\"$L7\",null,{\"className\":\"h-full\",\"children\":[\"$\",\"nav\",null,{\"className\":\"flex flex-col h-full min-w-[220px]\",\"children\":[[\"$\",\"div\",null,{\"className\":\"p-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex items-start justify-between mb-4\",\"children\":[[\"$\",\"$L8\",null,{\"href\":\"/\",\"children\":[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":\"109\",\"height\":\"113\",\"viewBox\":\"0 0 109 113\",\"fill\":\"none\",\"className\":\"w-6 h-6\",\"children\":[[\"$\",\"path\",null,{\"d\":\"M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0625L99.1935 40.0625C107.384 40.0625 111.952 49.5226 106.859 55.9372L63.7076 110.284Z\",\"fill\":\"url(#paint0_linear)\"}],[\"$\",\"path\",null,{\"d\":\"M63.7076 110.284C60.8481 113.885 55.0502 111.912 54.9813 107.314L53.9738 40.0625L99.1935 40.0625C107.384 40.0625 111.952 49.5226 106.859 55.9372L63.7076 110.284Z\",\"fill\":\"url(#paint1_linear)\",\"fillOpacity\":\"0.2\"}],[\"$\",\"path\",null,{\"d\":\"M45.317 2.07103C48.1765 -1.53037 53.9745 0.442937 54.0434 5.041L54.4849 72.2922H9.83113C1.64038 72.2922 -2.92775 62.8321 2.1655 56.4175L45.317 2.07103Z\",\"fill\":\"#3ECF8E\"}],[\"$\",\"defs\",null,{\"children\":[[\"$\",\"linearGradient\",null,{\"id\":\"paint0_linear\",\"x1\":\"53.9738\",\"y1\":\"54.9738\",\"x2\":\"94.1635\",\"y2\":\"71.8293\",\"gradientUnits\":\"userSpaceOnUse\",\"children\":[[\"$\",\"stop\",null,{\"stopColor\":\"#249361\"}],[\"$\",\"stop\",null,{\"offset\":\"1\",\"stopColor\":\"#3ECF8E\"}]]}],[\"$\",\"linearGradient\",null,{\"id\":\"paint1_linear\",\"x1\":\"36.1558\",\"y1\":\"30.5779\",\"x2\":\"54.4844\",\"y2\":\"65.0804\",\"gradientUnits\":\"userSpaceOnUse\",\"children\":[[\"$\",\"stop\",null,{}],[\"$\",\"stop\",null,{\"offset\":\"1\",\"stopOpacity\":\"0\"}]]}]]}]]}]}],[\"$\",\"$L9\",null,{}]]}],[\"$\",\"$L8\",null,{\"href\":\"/\",\"className\":\"mb-4 block\",\"children\":[\"$\",\"h1\",null,{\"children\":\"Supabase UI Library\"}]}],[\"$\",\"$La\",null,{}]]}],[\"$\",\"div\",null,{\"className\":\"pb-6 space-y-0.5\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest\",\"children\":\"Getting Started\"}],[[\"$\",\"$Lb\",\"/docs/getting-started/introduction-0\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:1:props:children:1:0:props:item\"}],[\"$\",\"$Lb\",\"/docs/getting-started/quickstart-1\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:1:props:children:1:1:props:item\"}],[\"$\",\"$Lb\",\"/docs/getting-started/faq-2\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:1:props:children:1:2:props:item\"}]]]}],[\"$\",\"div\",null,{\"className\":\"pb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest\",\"children\":\"Blocks\"}],[\"$\",\"div\",null,{\"className\":\"space-y-0.5\",\"children\":[[\"$\",\"$Lb\",\"/docs/nextjs/client-0\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:0:props:item\"}],[\"$\",\"$Lb\",\"/docs/nextjs/password-based-auth-1\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:1:props:item\"}],[\"$\",\"$Lb\",\"/docs/nextjs/social-auth-2\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:2:props:item\"}],[\"$\",\"$Lb\",\"/docs/nextjs/dropzone-3\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:3:props:item\"}],[\"$\",\"$Lb\",\"/docs/nextjs/realtime-cursor-4\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:4:props:item\"}],[\"$\",\"$Lb\",\"/docs/nextjs/current-user-avatar-5\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:5:props:item\"}],[\"$\",\"$Lb\",\"/docs/nextjs/realtime-avatar-stack-6\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:6:props:item\"}],[\"$\",\"$Lb\",\"/docs/nextjs/realtime-chat-7\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:7:props:item\"}],[\"$\",\"$Lb\",\"/docs/infinite-query-hook-8\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:2:props:children:1:props:children:8:props:item\"}]]}]]}],[\"$\",\"div\",null,{\"className\":\"pb-6 flex-1\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest\",\"children\":\"AI Editors Rules\"}],[[\"$\",\"$Lb\",\"/docs/ai-editors-rules/prompts-0\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:3:props:children:1:0:props:item\"}]]]}],[\"$\",\"div\",null,{\"className\":\"pb-6\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-mono uppercase text-xs text-foreground-lighter/75 mb-2 px-6 tracking-widest\",\"children\":\"Platform\"}],[[\"$\",\"$Lb\",\"/docs/platform/platform-kit-0\",{\"item\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:0:0:props:children:0:props:children:1:props:children:props:children:props:children:4:props:children:1:0:props:item\"}]]]}]]}]}]}]],[\"$\",\"div\",null,{\"vaul-drawer-wrapper\":\"\",\"children\":[\"$\",\"div\",null,{\"className\":\"relative flex min-h-screen flex-col bg-background\",\"children\":[\"$\",\"$Lc\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Ld\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}]}]}]}],[\"$\",\"footer\",null,{\"className\":\"py-6 px-4 md:px-8 md:py-0 mx-auto w-full max-w-site\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row\",\"children\":[[\"$\",\"p\",null,{\"className\":\"text-balance text-center text-sm leading-loose text-foreground-muted md:text-left\",\"children\":[\"Built by\",\" \",[\"$\",\"a\",null,{\"href\":\"https://twitter.com/supabase\",\"target\":\"_blank\",\"rel\":\"noreferrer\",\"className\":\"font-medium underline underline-offset-4 hover:text-foreground-lighter\",\"children\":\"Supabase\"}],\". The source code is available on\",\" \",[\"$\",\"a\",null,{\"href\":\"https://github.com/supabase/supabase/tree/master/apps/ui-library\",\"target\":\"_blank\",\"rel\":\"noreferrer\",\"className\":\"font-medium underline underline-offset-4 hover:text-foreground-lighter\",\"children\":\"GitHub\"}],\".\"]}],[\"$\",\"p\",null,{\"className\":\"text-balance text-center text-sm leading-loose text-foreground-muted\",\"children\":[\"Site inspired by\",\" \",[\"$\",\"a\",null,{\"href\":\"https://www.radix-ui.com/themes/docs/overview/getting-started\",\"target\":\"_blank\",\"rel\":\"noreferrer\",\"className\":\"font-medium underline underline-offset-4 hover:text-foreground-lighter\",\"children\":\"Radix\"}],\",\",\" \",[\"$\",\"a\",null,{\"href\":\"https://ui.shadcn.com\",\"target\":\"_blank\",\"rel\":\"noreferrer\",\"className\":\"font-medium underline underline-offset-4 hover:text-foreground-lighter\",\"children\":\"shadcn/ui\"}],\".\"]}]]}]}],[\"$\",\"$Le\",null,{}]]]}],{\"children\":[\"docs\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Lc\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Ld\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[[\"slug\",\"nextjs/realtime-chat\",\"oc\"],[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$Lc\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Ld\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[\"$Lf\",[\"$\",\"$L10\",null,{\"children\":\"$L11\"}],[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/ui/_next/static/css/f12065469b9a7bd1.css?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"$L12\",null,{\"children\":[\"$L13\",\"$L14\",[\"$\",\"$L15\",null,{\"promise\":\"$@16\"}]]}]]}],{},null,false]},null,false]},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"fuYYwA2lJnTpxsibbsrEA\",{\"children\":[[\"$\",\"$L17\",null,{\"children\":\"$L18\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$19\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"1a:I[39346,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"TelemetryTagManager\"]\n1b:I[12287,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/ch"])</script><script>self.__next_f.push([1,"unks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"FeatureFlagProvider\"]\n1c:I[22104,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"ThemeProvider\"]\n1d:I[26854,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4"])</script><script>self.__next_f.push([1,"WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"SonnerToaster\"]\n1e:\"$Sreact.suspense\"\n1f:I[38670,[],\"AsyncMetadata\"]\n"])</script><script>self.__next_f.push([1,"2:[\"$\",\"html\",null,{\"lang\":\"en\",\"suppressHydrationWarning\":true,\"children\":[[\"$\",\"head\",null,{}],[\"$\",\"body\",null,{\"className\":\"__className_cbad29 antialiased\",\"children\":[[\"$\",\"$L1a\",null,{}],[\"$\",\"$L1b\",null,{\"API_URL\":\"https://api.supabase.com/platform\",\"children\":[\"$\",\"$L1c\",null,{\"themes\":[\"dark\",\"light\",\"classic-dark\"],\"defaultTheme\":\"system\",\"enableSystem\":true,\"children\":[[\"$\",\"$Lc\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$Ld\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:1:props:children:props:children:props:notFound:0:1:props:style\",\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:1:props:children:props:children:props:notFound:0:1:props:children:props:children:1:props:style\",\"children\":404}],[\"$\",\"div\",null,{\"style\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:1:props:children:props:children:props:notFound:0:1:props:children:props:children:2:props:style\",\"children\":[\"$\",\"h2\",null,{\"style\":\"$0:f:0:1:2:children:1:props:children:1:0:props:children:props:children:props:children:props:children:1:props:children:props:children:props:notFound:0:1:props:children:props:children:2:props:children:props:style\",\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}],[\"$\",\"$L1d\",null,{}]]}]}]]}]]}]\n"])</script><script>self.__next_f.push([1,"11:[\"$\",\"$1e\",null,{\"fallback\":null,\"children\":[\"$\",\"$L1f\",null,{\"promise\":\"$@20\"}]}]\n"])</script><script>self.__next_f.push([1,"14:null\n"])</script><script>self.__next_f.push([1,"21:I[79904,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"default\"]\n22:I[50619,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2c"])</script><script>self.__next_f.push([1,"b44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"FrameworkSelector\"]\n23:I[16107,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"BlockPreview\"]\n24:I[42988,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL"])</script><script>self.__next_f.push([1,"3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"BlockItem\"]\n26:I[8186,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B"])</script><script>self.__next_f.push([1,"%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"StyleWrapper\"]\n27:I[45719,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"CopyButton\"]\n28:I[22090,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static"])</script><script>self.__next_f.push([1,"/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"DashboardTableOfContents\"]\n"])</script><script>self.__next_f.push([1,"f:[\"$\",\"main\",null,{\"className\":\"relative lg:gap-10 xl:grid xl:grid-cols-[1fr_200px] px-8 md:px-16 py-20\",\"children\":[[\"$\",\"div\",null,{\"className\":\"mx-auto w-full min-w-0 max-w-4xl\",\"children\":[[\"$\",\"div\",null,{\"className\":\"mb-4 flex items-center space-x-1 text-sm text-foreground-muted\",\"children\":[[\"$\",\"div\",null,{\"className\":\"overflow-hidden text-ellipsis whitespace-nowrap\",\"children\":\"Docs\"}],[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-chevron-right h-4 w-4 text-foreground-muted\",\"children\":[[\"$\",\"path\",\"mthhwq\",{\"d\":\"m9 18 6-6-6-6\"}],\"$undefined\"]}],[\"$\",\"div\",null,{\"className\":\"text-foreground-lighter\",\"children\":\"Realtime Chat\"}]]}],[\"$\",\"div\",null,{\"className\":\"flex flex-col lg:flex-row lg:items-end justify-between mb-5\",\"children\":[[\"$\",\"div\",null,{\"className\":\"space-y-2\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"scroll-m-20 text-2xl lg:text-4xl tracking-tight\",\"children\":\"Realtime Chat\"}],[\"$\",\"p\",null,{\"className\":\"text-base lg:text-lg text-foreground-light\",\"children\":[\"$\",\"$L21\",null,{\"children\":\"Real-time chat component for collaborative applications\"}]}]]}],[\"$\",\"$L22\",null,{}]]}],[\"$\",\"div\",null,{\"className\":\"flex flex-col -space-y-px\",\"children\":[\"$undefined\",\"$undefined\",\"$undefined\",\"$undefined\"]}],[\"$\",\"div\",null,{\"className\":\"pb-12\",\"children\":[\"$\",\"div\",null,{\"className\":\"mdx\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex flex-row -space-x-px w-full\",\"children\":[[\"$\",\"$L23\",null,{\"name\":\"realtime-chat-demo?roomName=room-646\",\"isPair\":true}],[\"$\",\"$L23\",null,{\"name\":\"realtime-chat-demo?roomName=room-646\",\"isPair\":true}]]}],\"\\n\",[\"$\",\"h2\",null,{\"className\":\"font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0\",\"id\":\"installation\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#installation\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Installation\"]}],\"\\n\",[\"$\",\"$L24\",null,{\"name\":\"realtime-chat-nextjs\",\"description\":\"Renders a real-time chat interface for users in a shared room.\"}],\"\\n\",[\"$\",\"h2\",null,{\"className\":\"font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0\",\"id\":\"folder-structure\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#folder-structure\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Folder structure\"]}],\"\\n\",\"$L25\",\"\\n\",[\"$\",\"h2\",null,{\"className\":\"font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0\",\"id\":\"introduction\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#introduction\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Introduction\"]}],\"\\n\",[\"$\",\"p\",null,{\"className\":\"leading-7 [\u0026:not(:first-child)]:mt-6 text-foreground-light\",\"children\":\"The Realtime Chat component provides a complete chat interface that enables users to exchange messages in real-time within a shared room.\"}],\"\\n\",[\"$\",\"h2\",null,{\"className\":\"font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0\",\"id\":\"usage\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#usage\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Usage\"]}],\"\\n\",[\"$\",\"h3\",null,{\"className\":\"font-heading mt-8 scroll-m-20 text-xl tracking-tight\",\"id\":\"basic-usage\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#basic-usage\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Basic usage\"]}],\"\\n\",[\"$\",\"div\",null,{\"data-rehype-pretty-code-fragment\":\"\",\"children\":[\"$\",\"$L26\",null,{\"styleName\":\"$undefined\",\"children\":[[\"$\",\"pre\",null,{\"className\":\"mb-4 mt-6 max-h-[650px] overflow-x-auto rounded-lg border bg-surface-75/75 py-4 text-foreground-light\",\"data-language\":\"tsx\",\"data-theme\":\"default\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"data-language\":\"tsx\",\"data-theme\":\"default\",\"children\":[[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" RealtimeChat\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" }\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" from\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"@/components/realtime-chat\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"export\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" default\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" function\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" ChatPage\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"  return\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"RealtimeChat \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"roomName\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"my-chat-room\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\" username\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"john_doe\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" /\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]}]]}]}],[\"$\",\"$L27\",null,{\"value\":\"import { RealtimeChat } from '@/components/realtime-chat'\\n\\nexport default function ChatPage() {\\n  return \u003cRealtimeChat roomName=\\\"my-chat-room\\\" username=\\\"john_doe\\\" /\u003e\\n}\\n\",\"src\":\"$undefined\",\"className\":\"absolute right-4 top-4\"}]]}]}],\"\\n\",[\"$\",\"h3\",null,{\"className\":\"font-heading mt-8 scroll-m-20 text-xl tracking-tight\",\"id\":\"with-initial-messages\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#with-initial-messages\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"With initial messages\"]}],\"\\n\",[\"$\",\"div\",null,{\"data-rehype-pretty-code-fragment\":\"\",\"children\":[\"$\",\"$L26\",null,{\"styleName\":\"$undefined\",\"children\":[[\"$\",\"pre\",null,{\"className\":\"mb-4 mt-6 max-h-[650px] overflow-x-auto rounded-lg border bg-surface-75/75 py-4 text-foreground-light\",\"data-language\":\"tsx\",\"data-theme\":\"default\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"data-language\":\"tsx\",\"data-theme\":\"default\",\"children\":[[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" RealtimeChat\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" }\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" from\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"@/components/realtime-chat\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" useMessagesQuery\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" }\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" from\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"@/hooks/use-messages-query\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"export\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" default\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" function\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" ChatPage\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"  const\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" data\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" messages\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" }\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" useMessagesQuery\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"  return\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"RealtimeChat \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"roomName\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"my-chat-room\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\" username\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"john_doe\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\" messages\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"messages\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" /\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]}]]}]}],[\"$\",\"$L27\",null,{\"value\":\"import { RealtimeChat } from '@/components/realtime-chat'\\nimport { useMessagesQuery } from '@/hooks/use-messages-query'\\n\\nexport default function ChatPage() {\\n  const { data: messages } = useMessagesQuery()\\n\\n  return \u003cRealtimeChat roomName=\\\"my-chat-room\\\" username=\\\"john_doe\\\" messages={messages} /\u003e\\n}\\n\",\"src\":\"$undefined\",\"className\":\"absolute right-4 top-4\"}]]}]}],\"\\n\",[\"$\",\"h3\",null,{\"className\":\"font-heading mt-8 scroll-m-20 text-xl tracking-tight\",\"id\":\"storing-messages\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#storing-messages\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Storing messages\"]}],\"\\n\",[\"$\",\"div\",null,{\"data-rehype-pretty-code-fragment\":\"\",\"children\":[\"$\",\"$L26\",null,{\"styleName\":\"$undefined\",\"children\":[[\"$\",\"pre\",null,{\"className\":\"mb-4 mt-6 max-h-[650px] overflow-x-auto rounded-lg border bg-surface-75/75 py-4 text-foreground-light\",\"data-language\":\"tsx\",\"data-theme\":\"default\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"data-language\":\"tsx\",\"data-theme\":\"default\",\"children\":[[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" RealtimeChat\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" }\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" from\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"@/components/realtime-chat\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" useMessagesQuery\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" }\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" from\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"@/hooks/use-messages-query\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"import\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" storeMessages\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" }\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" from\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" '\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"@/lib/store-messages\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"'\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"export\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" default\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" function\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" ChatPage\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"()\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"  const\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\" data\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-constant)\"},\"children\":\" messages\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" }\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" useMessagesQuery\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"()\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"  const\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" handleMessage\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" (\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"messages\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" ChatMessage\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"[])\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\" =\u003e\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-comment)\",\"fontStyle\":\"italic\"},\"children\":\"    // Store messages in your database\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"    await\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" storeMessages\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\"(\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"messages\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-foreground)\"},\"children\":\")\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"  }\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":\" \"}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"  return\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" \u003c\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\"RealtimeChat \"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\"roomName\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"my-chat-room\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\" username\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-string-expression)\"},\"children\":\"john_doe\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"\\\"\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-property)\"},\"children\":\" onMessage\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"=\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"{\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"handleMessage\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" /\u003e\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]}]]}]}],[\"$\",\"$L27\",null,{\"value\":\"import { RealtimeChat } from '@/components/realtime-chat'\\nimport { useMessagesQuery } from '@/hooks/use-messages-query'\\nimport { storeMessages } from '@/lib/store-messages'\\n\\nexport default function ChatPage() {\\n  const { data: messages } = useMessagesQuery()\\n  const handleMessage = (messages: ChatMessage[]) =\u003e {\\n    // Store messages in your database\\n    await storeMessages(messages)\\n  }\\n\\n  return \u003cRealtimeChat roomName=\\\"my-chat-room\\\" username=\\\"john_doe\\\" onMessage={handleMessage} /\u003e\\n}\\n\",\"src\":\"$undefined\",\"className\":\"absolute right-4 top-4\"}]]}]}],\"\\n\",[\"$\",\"h2\",null,{\"className\":\"font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0\",\"id\":\"features\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#features\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Features\"]}],\"\\n\",[\"$\",\"ul\",null,{\"className\":\"my-6 ml-6 list-disc text-foreground-light\",\"children\":[\"\\n\",[\"$\",\"li\",null,{\"className\":\"mt-2\",\"children\":\"Real-time message synchronization\"}],\"\\n\",[\"$\",\"li\",null,{\"className\":\"mt-2\",\"children\":[\"Message persistence support with \",[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"onMessage\"}],\" and \",[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"messages\"}],\" props\"]}],\"\\n\",[\"$\",\"li\",null,{\"className\":\"mt-2\",\"children\":\"Customizable message appearance\"}],\"\\n\",[\"$\",\"li\",null,{\"className\":\"mt-2\",\"children\":\"Automatic scroll-to-bottom on new messages\"}],\"\\n\",[\"$\",\"li\",null,{\"className\":\"mt-2\",\"children\":\"Room-based isolation for scoped conversations\"}],\"\\n\",[\"$\",\"li\",null,{\"className\":\"mt-2\",\"children\":\"Low-latency updates using Supabase Realtime\"}],\"\\n\"]}],\"\\n\",[\"$\",\"h2\",null,{\"className\":\"font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0\",\"id\":\"props\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#props\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Props\"]}],\"\\n\",[\"$\",\"div\",null,{\"className\":\"my-6 w-full overflow-y-auto\",\"children\":[\"$\",\"table\",null,{\"className\":\"w-full\",\"children\":[[\"$\",\"thead\",null,{\"children\":[\"$\",\"tr\",null,{\"className\":\"m-0 border-t p-0 even:bg-surface-75/75\",\"children\":[[\"$\",\"th\",null,{\"className\":\"border px-4 py-2 text-left font-normal [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":\"Prop\"}],[\"$\",\"th\",null,{\"className\":\"border px-4 py-2 text-left font-normal [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":\"Type\"}],[\"$\",\"th\",null,{\"className\":\"border px-4 py-2 text-left font-normal [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":\"Description\"}]]}]}],[\"$\",\"tbody\",null,{\"children\":[[\"$\",\"tr\",null,{\"className\":\"m-0 border-t p-0 even:bg-surface-75/75\",\"children\":[[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"roomName\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"string\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":\"Unique identifier for the shared chat room.\"}]]}],[\"$\",\"tr\",null,{\"className\":\"m-0 border-t p-0 even:bg-surface-75/75\",\"children\":[[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"username\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"string\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":\"Name of the current user; used to identify message senders.\"}]]}],[\"$\",\"tr\",null,{\"className\":\"m-0 border-t p-0 even:bg-surface-75/75\",\"children\":[[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"onMessage?\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"(messages: ChatMessage[]) =\u003e void\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":\"Optional callback to handle messages, useful for persistence.\"}]]}],[\"$\",\"tr\",null,{\"className\":\"m-0 border-t p-0 even:bg-surface-75/75\",\"children\":[[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"messages?\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"children\":\"ChatMessage[]\"}]}],[\"$\",\"td\",null,{\"className\":\"border text-foreground-light px-4 py-2 text-left [\u0026[align=center]]:text-center [\u0026[align=right]]:text-right\",\"children\":\"Optional initial messages to display in the chat.\"}]]}]]}]]}]}],\"\\n\",[\"$\",\"h3\",null,{\"className\":\"font-heading mt-8 scroll-m-20 text-xl tracking-tight\",\"id\":\"chatmessage-type\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#chatmessage-type\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"ChatMessage type\"]}],\"\\n\",[\"$\",\"div\",null,{\"data-rehype-pretty-code-fragment\":\"\",\"children\":[\"$\",\"$L26\",null,{\"styleName\":\"$undefined\",\"children\":[[\"$\",\"pre\",null,{\"className\":\"mb-4 mt-6 max-h-[650px] overflow-x-auto rounded-lg border bg-surface-75/75 py-4 text-foreground-light\",\"data-language\":\"typescript\",\"data-theme\":\"default\",\"children\":[\"$\",\"code\",null,{\"className\":\"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm\",\"data-language\":\"typescript\",\"data-theme\":\"default\",\"children\":[[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\"interface\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" ChatMessage\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"  id\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" string\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"  content\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" string\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"  user\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\" {\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"    name\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" string\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"  }\"}]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-parameter)\"},\"children\":\"  createdAt\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-keyword)\"},\"children\":\":\"}],[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-function)\"},\"children\":\" string\"}]]}],\"\\n\",[\"$\",\"span\",null,{\"className\":\"line\",\"children\":[\"$\",\"span\",null,{\"style\":{\"color\":\"var(--code-token-punctuation)\"},\"children\":\"}\"}]}]]}]}],[\"$\",\"$L27\",null,{\"value\":\"interface ChatMessage {\\n  id: string\\n  content: string\\n  user: {\\n    name: string\\n  }\\n  createdAt: string\\n}\\n\",\"src\":\"$undefined\",\"className\":\"absolute right-4 top-4\"}]]}]}],\"\\n\",[\"$\",\"h2\",null,{\"className\":\"font-heading mt-12 scroll-m-20 border-b pb-2 text-2xl tracking-tight first:mt-0\",\"id\":\"further-reading\",\"children\":[[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2 subheading-anchor\",\"aria-label\":\"Link to section\",\"href\":\"#further-reading\",\"children\":[\"$\",\"span\",null,{\"className\":\"icon icon-link\"}]}],\"Further reading\"]}],\"\\n\",[\"$\",\"ul\",null,{\"className\":\"my-6 ml-6 list-disc text-foreground-light\",\"children\":[\"\\n\",[\"$\",\"li\",null,{\"className\":\"mt-2\",\"children\":[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2\",\"href\":\"https://supabase.com/docs/guides/realtime/broadcast\",\"children\":\"Realtime Broadcast\"}]}],\"\\n\",[\"$\",\"li\",null,{\"className\":\"mt-2\",\"children\":[\"$\",\"a\",null,{\"className\":\"text-foreground underline decoration-1 decoration-foreground-muted underline-offset-4 transition-colors hover:decoration-brand hover:decoration-2\",\"href\":\"https://supabase.com/docs/guides/realtime/authorization\",\"children\":\"Realtime authorization\"}]}],\"\\n\"]}]]}]}]]}],[\"$\",\"div\",null,{\"className\":\"hidden text-sm xl:block\",\"children\":[\"$\",\"div\",null,{\"className\":\"sticky top-16 -mt-10 pt-4\",\"children\":[\"$\",\"$L7\",null,{\"className\":\"pb-10\",\"children\":[\"$\",\"div\",null,{\"className\":\"sticky top-16 -mt-10 h-[calc(100vh-3.5rem)] py-12\",\"children\":[\"$\",\"$L28\",null,{\"toc\":{\"items\":[{\"url\":\"#installation\",\"title\":\"Installation\"},{\"url\":\"#folder-structure\",\"title\":\"Folder structure\"},{\"url\":\"#introduction\",\"title\":\"Introduction\"},{\"url\":\"#usage\",\"title\":\"Usage\",\"items\":[{\"url\":\"#basic-usage\",\"title\":\"Basic usage\"},{\"url\":\"#with-initial-messages\",\"title\":\"With initial messages\"},{\"url\":\"#storing-messages\",\"title\":\"Storing messages\"}]},{\"url\":\"#features\",\"title\":\"Features\"},{\"url\":\"#props\",\"title\":\"Props\",\"items\":[{\"url\":\"#chatmessage-type\",\"title\":\"ChatMessage type\"}]},{\"url\":\"#further-reading\",\"title\":\"Further reading\"}]}}]}]}]}]}]]}]\n"])</script><script>self.__next_f.push([1,"29:I[38866,[\"6985\",\"static/chunks/6985-d9c94a68747ee58b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"2105\",\"static/chunks/2105-5c6fb0a066c1340f.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3502\",\"static/chunks/3502-8ffc67f87c6f59da.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6276\",\"static/chunks/6276-e32f7d13c5c52f3b.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6701\",\"static/chunks/6701-6b908838eebce949.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"6602\",\"static/chunks/6602-18415a95b5bdd8fe.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"3276\",\"static/chunks/3276-a6118a55186eb94a.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"898\",\"static/chunks/898-7244b379490d4af4.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5513\",\"static/chunks/5513-914367b37e08a710.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"5710\",\"static/chunks/5710-2cb44b983b4cfc0c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8443\",\"static/chunks/8443-3b56b7405ddfa048.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"494\",\"static/chunks/494-582b02a90a51684d.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"8815\",\"static/chunks/8815-1dd29c457db8514c.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\",\"757\",\"static/chunks/app/(app)/docs/%5B%5B...slug%5D%5D/page-7b82ea734ea0fc88.js?dpl=dpl_Lv1jkt2yqpu4WGsQF1Qvqswx8sL3\"],\"BlockItemCode\"]\n2a:T555,import { cn } from '@/lib/utils'\nimport type { ChatMessage } from '@/hooks/use-realtime-chat'\n\ninterface ChatMessageItemProps {\n  message: ChatMessage\n  isOwnMessage: boolean\n  showHeader: boolean\n}\n\nexport const ChatMessageItem = ({ message, isOwnMessage, showHeader }: ChatMessageItemProps) =\u003e {\n  return (\n    \u003cdiv className={`flex mt-2 ${isOwnMessage ? 'justify-end' : 'justify-start'}`}\u003e\n      \u003cdiv\n        className={cn('max-w-[75%] w-fit flex flex-col gap-1', {\n          'items-end': isOwnMessage,\n        })}\n      \u003e\n        {showHeader \u0026\u0026 (\n          \u003cdiv\n            className={cn('flex items-center gap-2 text-xs px-3', {\n              'justify-end flex-row-reverse': isOwnMessage,\n            })}\n          \u003e\n            \u003cspan className={'font-medium'}\u003e{message.user.name}\u003c/spa"])</script><script>self.__next_f.push([1,"n\u003e\n            \u003cspan className=\"text-foreground/50 text-xs\"\u003e\n              {new Date(message.createdAt).toLocaleTimeString('en-US', {\n                hour: '2-digit',\n                minute: '2-digit',\n                hour12: true,\n              })}\n            \u003c/span\u003e\n          \u003c/div\u003e\n        )}\n        \u003cdiv\n          className={cn(\n            'py-2 px-3 rounded-xl text-sm w-fit',\n            isOwnMessage ? 'bg-primary text-primary-foreground' : 'bg-muted text-foreground'\n          )}\n        \u003e\n          {message.content}\n        \u003c/div\u003e\n      \u003c/div\u003e\n    \u003c/div\u003e\n  )\n}\n2b:T1117,"])</script><script>self.__next_f.push([1,"'use client'\n\nimport { cn } from '@/lib/utils'\nimport { ChatMessageItem } from '@/components/chat-message'\nimport { useChatScroll } from '@/hooks/use-chat-scroll'\nimport {\n  type ChatMessage,\n  useRealtimeChat,\n} from '@/hooks/use-realtime-chat'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Send } from 'lucide-react'\nimport { useCallback, useEffect, useMemo, useState } from 'react'\n\ninterface RealtimeChatProps {\n  roomName: string\n  username: string\n  onMessage?: (messages: ChatMessage[]) =\u003e void\n  messages?: ChatMessage[]\n}\n\n/**\n * Realtime chat component\n * @param roomName - The name of the room to join. Each room is a unique chat.\n * @param username - The username of the user\n * @param onMessage - The callback function to handle the messages. Useful if you want to store the messages in a database.\n * @param messages - The messages to display in the chat. Useful if you want to display messages from a database.\n * @returns The chat component\n */\nexport const RealtimeChat = ({\n  roomName,\n  username,\n  onMessage,\n  messages: initialMessages = [],\n}: RealtimeChatProps) =\u003e {\n  const { containerRef, scrollToBottom } = useChatScroll()\n\n  const {\n    messages: realtimeMessages,\n    sendMessage,\n    isConnected,\n  } = useRealtimeChat({\n    roomName,\n    username,\n  })\n  const [newMessage, setNewMessage] = useState('')\n\n  // Merge realtime messages with initial messages\n  const allMessages = useMemo(() =\u003e {\n    const mergedMessages = [...initialMessages, ...realtimeMessages]\n    // Remove duplicates based on message id\n    const uniqueMessages = mergedMessages.filter(\n      (message, index, self) =\u003e index === self.findIndex((m) =\u003e m.id === message.id)\n    )\n    // Sort by creation date\n    const sortedMessages = uniqueMessages.sort((a, b) =\u003e a.createdAt.localeCompare(b.createdAt))\n\n    return sortedMessages\n  }, [initialMessages, realtimeMessages])\n\n  useEffect(() =\u003e {\n    if (onMessage) {\n      onMessage(allMessages)\n    }\n  }, [allMessages, onMessage])\n\n  useEffect(() =\u003e {\n    // Scroll to bottom whenever messages change\n    scrollToBottom()\n  }, [allMessages, scrollToBottom])\n\n  const handleSendMessage = useCallback(\n    (e: React.FormEvent) =\u003e {\n      e.preventDefault()\n      if (!newMessage.trim() || !isConnected) return\n\n      sendMessage(newMessage)\n      setNewMessage('')\n    },\n    [newMessage, isConnected, sendMessage]\n  )\n\n  return (\n    \u003cdiv className=\"flex flex-col h-full w-full bg-background text-foreground antialiased\"\u003e\n      {/* Messages */}\n      \u003cdiv ref={containerRef} className=\"flex-1 overflow-y-auto p-4 space-y-4\"\u003e\n        {allMessages.length === 0 ? (\n          \u003cdiv className=\"text-center text-sm text-muted-foreground\"\u003e\n            No messages yet. Start the conversation!\n          \u003c/div\u003e\n        ) : null}\n        \u003cdiv className=\"space-y-1\"\u003e\n          {allMessages.map((message, index) =\u003e {\n            const prevMessage = index \u003e 0 ? allMessages[index - 1] : null\n            const showHeader = !prevMessage || prevMessage.user.name !== message.user.name\n\n            return (\n              \u003cdiv\n                key={message.id}\n                className=\"animate-in fade-in slide-in-from-bottom-4 duration-300\"\n              \u003e\n                \u003cChatMessageItem\n                  message={message}\n                  isOwnMessage={message.user.name === username}\n                  showHeader={showHeader}\n                /\u003e\n              \u003c/div\u003e\n            )\n          })}\n        \u003c/div\u003e\n      \u003c/div\u003e\n\n      \u003cform onSubmit={handleSendMessage} className=\"flex w-full gap-2 border-t border-border p-4\"\u003e\n        \u003cInput\n          className={cn(\n            'rounded-full bg-background text-sm transition-all duration-300',\n            isConnected \u0026\u0026 newMessage.trim() ? 'w-[calc(100%-36px)]' : 'w-full'\n          )}\n          type=\"text\"\n          value={newMessage}\n          onChange={(e) =\u003e setNewMessage(e.target.value)}\n          placeholder=\"Type a message...\"\n          disabled={!isConnected}\n        /\u003e\n        {isConnected \u0026\u0026 newMessage.trim() \u0026\u0026 (\n          \u003cButton\n            className=\"aspect-square rounded-full animate-in fade-in slide-in-from-right-4 duration-300\"\n            type=\"submit\"\n            disabled={!isConnected}\n          \u003e\n            \u003cSend className=\"size-4\" /\u003e\n          \u003c/Button\u003e\n        )}\n      \u003c/form\u003e\n    \u003c/div\u003e\n  )\n}\n"])</script><script>self.__next_f.push([1,"2c:T71a,'use client'\n\nimport { createClient } from '@/lib/supabase/client'\nimport { useCallback, useEffect, useState } from 'react'\n\ninterface UseRealtimeChatProps {\n  roomName: string\n  username: string\n}\n\nexport interface ChatMessage {\n  id: string\n  content: string\n  user: {\n    name: string\n  }\n  createdAt: string\n}\n\nconst EVENT_MESSAGE_TYPE = 'message'\n\nexport function useRealtimeChat({ roomName, username }: UseRealtimeChatProps) {\n  const supabase = createClient()\n  const [messages, setMessages] = useState\u003cChatMessage[]\u003e([])\n  const [channel, setChannel] = useState\u003cReturnType\u003ctypeof supabase.channel\u003e | null\u003e(null)\n  const [isConnected, setIsConnected] = useState(false)\n\n  useEffect(() =\u003e {\n    const newChannel = supabase.channel(roomName)\n\n    newChannel\n      .on('broadcast', { event: EVENT_MESSAGE_TYPE }, (payload) =\u003e {\n        setMessages((current) =\u003e [...current, payload.payload as ChatMessage])\n      })\n      .subscribe(async (status) =\u003e {\n        if (status === 'SUBSCRIBED') {\n          setIsConnected(true)\n        }\n      })\n\n    setChannel(newChannel)\n\n    return () =\u003e {\n      supabase.removeChannel(newChannel)\n    }\n  }, [roomName, username, supabase])\n\n  const sendMessage = useCallback(\n    async (content: string) =\u003e {\n      if (!channel || !isConnected) return\n\n      const message: ChatMessage = {\n        id: crypto.randomUUID(),\n        content,\n        user: {\n          name: username,\n        },\n        createdAt: new Date().toISOString(),\n      }\n\n      // Update local state immediately for the sender\n      setMessages((current) =\u003e [...current, message])\n\n      await channel.send({\n        type: 'broadcast',\n        event: EVENT_MESSAGE_TYPE,\n        payload: message,\n      })\n    },\n    [channel, isConnected, username]\n  )\n\n  return { messages, sendMessage, isConnected }\n}\n2d:T864,"])</script><script>self.__next_f.push([1,"import { createServerClient } from '@supabase/ssr'\nimport { NextResponse, type NextRequest } from 'next/server'\n\nexport async function updateSession(request: NextRequest) {\n  let supabaseResponse = NextResponse.next({\n    request,\n  })\n\n  const supabase = createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return request.cookies.getAll()\n        },\n        setAll(cookiesToSet) {\n          cookiesToSet.forEach(({ name, value }) =\u003e request.cookies.set(name, value))\n          supabaseResponse = NextResponse.next({\n            request,\n          })\n          cookiesToSet.forEach(({ name, value, options }) =\u003e\n            supabaseResponse.cookies.set(name, value, options)\n          )\n        },\n      },\n    }\n  )\n\n  // Do not run code between createServerClient and\n  // supabase.auth.getClaims(). A simple mistake could make it very hard to debug\n  // issues with users being randomly logged out.\n\n  // IMPORTANT: DO NOT REMOVE auth.getClaims()\n  const { data } = await supabase.auth.getClaims()\n\n  const user = data?.claims\n\n  if (\n    !user \u0026\u0026\n    !request.nextUrl.pathname.startsWith('/login') \u0026\u0026\n    !request.nextUrl.pathname.startsWith('/auth')\n  ) {\n    // no user, potentially respond by redirecting the user to the login page\n    const url = request.nextUrl.clone()\n    url.pathname = '/auth/login'\n    return NextResponse.redirect(url)\n  }\n\n  // IMPORTANT: You *must* return the supabaseResponse object as it is.\n  // If you're creating a new response object with NextResponse.next() make sure to:\n  // 1. Pass the request in it, like so:\n  //    const myNewResponse = NextResponse.next({ request })\n  // 2. Copy over the cookies, like so:\n  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())\n  // 3. Change the myNewResponse object to fit your needs, but avoid changing\n  //    the cookies!\n  // 4. Finally:\n  //    return myNewResponse\n  // If this is not done, you may be causing the browser and server to go out\n  // of sync and terminate the user's session prematurely!\n\n  return supabaseResponse\n}\n"])</script><script>self.__next_f.push([1,"25:[\"$\",\"$L29\",null,{\"files\":[{\"name\":\"components\",\"path\":\"/components\",\"originalPath\":\"registry/default/blocks/realtime-chat/components/chat-message.tsx\",\"type\":\"directory\",\"children\":[{\"name\":\"chat-message.tsx\",\"path\":\"/components/chat-message.tsx\",\"originalPath\":\"registry/default/blocks/realtime-chat/components/chat-message.tsx\",\"type\":\"file\",\"content\":\"$2a\"},{\"name\":\"realtime-chat.tsx\",\"path\":\"/components/realtime-chat.tsx\",\"originalPath\":\"registry/default/blocks/realtime-chat/components/realtime-chat.tsx\",\"type\":\"file\",\"content\":\"$2b\"}]},{\"name\":\"hooks\",\"path\":\"/hooks\",\"originalPath\":\"registry/default/blocks/realtime-chat/hooks/use-chat-scroll.tsx\",\"type\":\"directory\",\"children\":[{\"name\":\"use-chat-scroll.tsx\",\"path\":\"/hooks/use-chat-scroll.tsx\",\"originalPath\":\"registry/default/blocks/realtime-chat/hooks/use-chat-scroll.tsx\",\"type\":\"file\",\"content\":\"import { useCallback, useRef } from 'react'\\n\\nexport function useChatScroll() {\\n  const containerRef = useRef\u003cHTMLDivElement\u003e(null)\\n\\n  const scrollToBottom = useCallback(() =\u003e {\\n    if (!containerRef.current) return\\n\\n    const container = containerRef.current\\n    container.scrollTo({\\n      top: container.scrollHeight,\\n      behavior: 'smooth',\\n    })\\n  }, [])\\n\\n  return { containerRef, scrollToBottom }\\n}\\n\"},{\"name\":\"use-realtime-chat.tsx\",\"path\":\"/hooks/use-realtime-chat.tsx\",\"originalPath\":\"registry/default/blocks/realtime-chat/hooks/use-realtime-chat.tsx\",\"type\":\"file\",\"content\":\"$2c\"}]},{\"name\":\"lib\",\"path\":\"/lib\",\"originalPath\":\"registry/default/clients/nextjs/lib/supabase/client.ts\",\"type\":\"directory\",\"children\":[{\"name\":\"supabase\",\"path\":\"/lib/supabase\",\"originalPath\":\"registry/default/clients/nextjs/lib/supabase/client.ts\",\"type\":\"directory\",\"children\":[{\"name\":\"client.ts\",\"path\":\"/lib/supabase/client.ts\",\"originalPath\":\"registry/default/clients/nextjs/lib/supabase/client.ts\",\"type\":\"file\",\"content\":\"import { createBrowserClient } from '@supabase/ssr'\\n\\nexport function createClient() {\\n  return createBrowserClient(\\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\\n  )\\n}\\n\"},{\"name\":\"middleware.ts\",\"path\":\"/lib/supabase/middleware.ts\",\"originalPath\":\"registry/default/clients/nextjs/lib/supabase/middleware.ts\",\"type\":\"file\",\"content\":\"$2d\"},{\"name\":\"server.ts\",\"path\":\"/lib/supabase/server.ts\",\"originalPath\":\"registry/default/clients/nextjs/lib/supabase/server.ts\",\"type\":\"file\",\"content\":\"import { createServerClient } from '@supabase/ssr'\\nimport { cookies } from 'next/headers'\\n\\nexport async function createClient() {\\n  const cookieStore = await cookies()\\n\\n  return createServerClient(\\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\\n    {\\n      cookies: {\\n        getAll() {\\n          return cookieStore.getAll()\\n        },\\n        setAll(cookiesToSet) {\\n          try {\\n            cookiesToSet.forEach(({ name, value, options }) =\u003e\\n              cookieStore.set(name, value, options)\\n            )\\n          } catch {\\n            // The `setAll` method was called from a Server Component.\\n            // This can be ignored if you have middleware refreshing\\n            // user sessions.\\n          }\\n        },\\n      },\\n    }\\n  )\\n}\\n\"}]}]}]}]\n"])</script><script>self.__next_f.push([1,"18:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n13:null\n"])</script><script>self.__next_f.push([1,"20:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Realtime Chat\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Real-time chat component for collaborative applications\"}],[\"$\",\"meta\",\"2\",{\"name\":\"application-name\",\"content\":\"Supabase UI Library\"}],[\"$\",\"meta\",\"3\",{\"property\":\"og:title\",\"content\":\"Realtime Chat\"}],[\"$\",\"meta\",\"4\",{\"property\":\"og:description\",\"content\":\"Real-time chat component for collaborative applications\"}],[\"$\",\"meta\",\"5\",{\"property\":\"og:url\",\"content\":\"https://supabase.com/undefined/docs/nextjs/realtime-chat\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:image\",\"content\":\"https://supabase.com/ui/img/supabase-og-image.png\"}],[\"$\",\"meta\",\"7\",{\"property\":\"og:type\",\"content\":\"article\"}],[\"$\",\"meta\",\"8\",{\"property\":\"article:published_time\",\"content\":\"2025-07-30T14:07:10.034Z\"}],[\"$\",\"meta\",\"9\",{\"property\":\"article:modified_time\",\"content\":\"2025-07-30T14:07:10.034Z\"}],[\"$\",\"meta\",\"10\",{\"property\":\"article:author\",\"content\":\"Supabase\"}],[\"$\",\"meta\",\"11\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"12\",{\"name\":\"twitter:site\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"13\",{\"name\":\"twitter:creator\",\"content\":\"@supabase\"}],[\"$\",\"meta\",\"14\",{\"name\":\"twitter:title\",\"content\":\"Realtime Chat\"}],[\"$\",\"meta\",\"15\",{\"name\":\"twitter:description\",\"content\":\"Real-time chat component for collaborative applications\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:image\",\"content\":\"https://supabase.com/ui/img/supabase-og-image.png\"}],[\"$\",\"link\",\"17\",{\"rel\":\"shortcut icon\",\"href\":\"/ui/favicon/favicon.ico\"}],[\"$\",\"link\",\"18\",{\"rel\":\"icon\",\"href\":\"/ui/favicon/favicon.ico\",\"type\":\"image/x-icon\"}],[\"$\",\"link\",\"19\",{\"rel\":\"apple-touch-icon\",\"href\":\"/ui/favicon/favicon.ico\"}],[\"$\",\"link\",\"20\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/ui/favicon/apple-icon-57x57.png\",\"sizes\":\"57x57\"}],[\"$\",\"link\",\"21\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/ui/favicon/apple-icon-60x60.png\",\"sizes\":\"60x60\"}],[\"$\",\"link\",\"22\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/ui/favicon/apple-icon-72x72.png\",\"sizes\":\"72x72\"}],[\"$\",\"link\",\"23\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/ui/favicon/apple-icon-76x76.png\",\"sizes\":\"76x76\"}],[\"$\",\"link\",\"24\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/ui/favicon/apple-icon-114x114.png\",\"sizes\":\"114x114\"}],[\"$\",\"link\",\"25\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/ui/favicon/apple-icon-120x120.png\",\"sizes\":\"120x120\"}],[\"$\",\"link\",\"26\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/ui/favicon/apple-icon-144x144.png\",\"sizes\":\"144x144\"}],[\"$\",\"link\",\"27\",{\"rel\":\"apple-touch-icon-precomposed\",\"href\":\"/ui/favicon/apple-icon-152x152.png\",\"sizes\":\"152x152\"}],[\"$\",\"link\",\"28\",{\"rel\":\"icon\",\"href\":\"/ui/favicon/favicon-16x16.png\",\"type\":\"image/png\",\"sizes\":\"16x16\"}],[\"$\",\"link\",\"29\",{\"rel\":\"icon\",\"href\":\"/ui/favicon/favicon-32x32.png\",\"type\":\"image/png\",\"sizes\":\"32x32\"}],[\"$\",\"link\",\"30\",{\"rel\":\"icon\",\"href\":\"/ui/favicon/favicon-48x48.png\",\"type\":\"image/png\",\"sizes\":\"48x48\"}],[\"$\",\"link\",\"31\",{\"rel\":\"icon\",\"href\":\"/ui/favicon/favicon-96x96.png\",\"type\":\"image/png\",\"sizes\":\"96x96\"}],[\"$\",\"link\",\"32\",{\"rel\":\"icon\",\"href\":\"/ui/favicon/favicon-128x128.png\",\"type\":\"image/png\",\"sizes\":\"128x128\"}],[\"$\",\"link\",\"33\",{\"rel\":\"icon\",\"href\":\"/ui/favicon/favicon-180x180.png\",\"type\":\"image/png\",\"sizes\":\"180x180\"}],[\"$\",\"link\",\"34\",{\"rel\":\"icon\",\"href\":\"/ui/favicon/favicon-196x196.png\",\"type\":\"image/png\",\"sizes\":\"196x196\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"16:{\"metadata\":\"$20:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>