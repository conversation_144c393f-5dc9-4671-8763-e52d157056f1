"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[898],{80898:(e,t,n)=>{n.d(t,{Ut:()=>o,tG:()=>w});var r=n(86836);let o=Symbol("RESET"),i=(e,t,n)=>(t.has(n)?t:t.set(n,e())).get(n),a=new WeakMap,u=(e,t,n)=>(t.has(n)?t:t.set(n,e())).get(n),s=new WeakMap,l=e=>"function"==typeof(null==e?void 0:e.then),d=function(e=()=>{try{return window.localStorage}catch(e){"undefined"!=typeof window&&console.warn(e);return}},t){let n,r,o={getItem:(t,o)=>{var i,a;let u=e=>{if(n!==(e=e||"")){try{r=JSON.parse(e,void 0)}catch(e){return o}n=e}return r},s=null!=(a=null==(i=e())?void 0:i.getItem(t))?a:null;return l(s)?s.then(u):u(s)},setItem:(t,n)=>{var r;return null==(r=e())?void 0:r.setItem(t,JSON.stringify(n,void 0))},removeItem:t=>{var n;return null==(n=e())?void 0:n.removeItem(t)}};return"undefined"!=typeof window&&"function"==typeof window.addEventListener&&window.Storage&&(o.subscribe=(t,n,r)=>{if(!(e()instanceof window.Storage))return()=>{};let o=o=>{if(o.storageArea===e()&&o.key===t){let e;try{e=JSON.parse(o.newValue||"")}catch(t){e=r}n(e)}};return window.addEventListener("storage",o),()=>{window.removeEventListener("storage",o)}}),o}();function w(e,t,n=d,i){let a=null==i?void 0:i.getOnInit,u=(0,r.eU)(a?n.getItem(e,t):t);return u.debugPrivate=!0,u.onMount=r=>{let o;return r(n.getItem(e,t)),n.subscribe&&(o=n.subscribe(e,r,t)),o},(0,r.eU)(e=>e(u),(r,i,a)=>{let s="function"==typeof a?a(r(u)):a;return s===o?(i(u,t),n.removeItem(e)):s instanceof Promise?s.then(t=>(i(u,t),n.setItem(e,t))):(i(u,s),n.setItem(e,s))})}let c=new WeakMap,f=(e,t,n)=>(t.has(n)?t:t.set(n,e())).get(n),v=new WeakMap}}]);