(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4944],{1873:e=>{e.exports={loading:"loading-anim_loading__rDN7u",dash:"loading-anim_dash__HIPR0"}},4473:(e,t,r)=>{"use strict";r.d(t,{H:()=>s});let s="https://api.supabase.com/platform"},4913:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(35484);r(37811);let n=s.cn},22792:(e,t,r)=>{"use strict";r.d(t,{default:()=>h});var s=r(31710),n=r(15531),o=r.n(n),a=r(67468);r(54990);var i=r(40844),l=r(48767),d=r(35484);let c=(0,r(11448).F)("inline-flex items-center px-2.5 py-0.5 rounded-full text-xs bg-opacity-10",{variants:{variant:{default:"bg-surface-200 text-foreground-light border border-strong",warning:"bg-warning text-warning-600 border border-warning-500",success:"bg-brand text-brand-600 border border-brand-500",destructive:"bg-destructive text-destructive-600 border border-destructive-500",brand:"bg-brand text-brand-600 border border-brand-500",secondary:"bg-secondary hover:bg-secondary/80 border-transparent text-secondary-foreground",outline:"bg-transparent text border border-foreground-muted"},size:{small:"px-2.5 py-0.5 text-xs",large:"px-3 py-0.5 rounded-full text-sm"},dot:{true:"-ml-0.5 mr-1.5 h-2 w-2 rounded-full"}},defaultVariants:{variant:"default",size:"small"}});function m(e){let{className:t,variant:r="default",size:n,dot:o=!1,children:a,...i}=e;return(0,s.jsxs)("div",{className:(0,d.cn)(c({variant:r,size:n}),t),...i,children:[o&&(0,s.jsx)("svg",{className:c({dot:o}),fill:"currentColor",viewBox:"0 0 8 8",children:(0,s.jsx)("circle",{cx:"4",cy:"4",r:"3"})}),a]})}let u=e=>{let{item:t,onClick:r,...n}=e,{setOpen:c}=(0,l.H)(),{framework:u}=(0,i.u)(),h=(0,a.usePathname)(),v=h.split("/"),p=v[v.length-1],b=(e,t)=>{let r=e.supportedFrameworks||[];return!(r.length>0)||r.includes(t)},x=t.href;if(t.supportedFrameworks){if(x&&x.startsWith("/docs/")){let e=x.split("/");e.length>=3&&u&&b(t,u)&&e.length>=4&&(x="/docs/".concat(u,"/").concat(e[3]))}}else!x&&p&&(x=u&&b(t,u)?"/docs/".concat(u,"/").concat(p):"/docs/".concat(p));let g=h===x;return(0,s.jsxs)(o(),{href:x||"#",...n,onClick:e=>{c(!1),r&&r(e)},className:(0,d.cn)("relative","flex","items-center justify-between","h-6","text-sm","text-foreground-lighter px-6",!g&&"hover:bg-surface-100 hover:text-foreground",g&&"bg-surface-200 text-foreground","transition-all",n.className),children:[(0,s.jsx)("div",{className:(0,d.cn)("transition","absolute left-0 w-1 h-full bg-foreground",g?"opacity-100":"opacity-0")}),t.title,t.new&&(0,s.jsx)(m,{variant:"brand",className:"capitalize",children:"NEW"})]})};u.displayName="NavigationItem";let h=u},28044:(e,t,r)=>{"use strict";r.d(t,{CommandMenu:()=>p});var s=r(31710),n=r(4743),o=r(79671),a=r(81682);let i=(0,r(47966).A)("Laptop",[["path",{d:"M20 16V7a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v9m16 0H4m16 0 1.28 2.55a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45L4 16",key:"tarvll"}]]);var l=r(47375),d=r(67468),c=r(54990),m=r(48392),u=r(4913),h=r(35621),v=r(84458);function p(e){let{...t}=e,r=(0,d.useRouter)(),[p,b]=c.useState(!1),{setTheme:x}=(0,l.D)();c.useEffect(()=>{let e=e=>{("k"===e.key&&(e.metaKey||e.ctrlKey)||"/"===e.key)&&(e.target instanceof HTMLElement&&e.target.isContentEditable||e.target instanceof HTMLInputElement||e.target instanceof HTMLTextAreaElement||e.target instanceof HTMLSelectElement||(e.preventDefault(),b(e=>!e)))};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[]);let g=c.useCallback(e=>{b(!1),e()},[]);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(h.Button,{type:"outline",className:(0,u.cn)("relative h-8 w-full justify-start rounded-[0.5rem] bg-background text-sm font-normal text-foreground-muted shadow-none sm:pr-12\n            hover:border-foreground-muted hover:bg-surface-100 hover:text-foreground-lighter\n          "),onClick:()=>b(!0),...t,children:[(0,s.jsx)("span",{className:"hidden lg:inline-flex",children:"Search UI Library..."}),(0,s.jsx)("span",{className:"inline-flex lg:hidden",children:"Search..."}),(0,s.jsxs)("kbd",{className:"pointer-events-none absolute right-[0.3rem] top-[0.3rem] hidden h-5 select-none items-center gap-1 rounded border bg-surface-200 px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex text-foreground-light",children:[(0,s.jsx)("span",{className:"text-sm",children:"⌘"}),"K"]})]}),(0,s.jsxs)(v.CommandDialog,{open:p,onOpenChange:b,children:[(0,s.jsx)(v.CommandInput,{placeholder:"Type a command or search..."}),(0,s.jsxs)(v.CommandList,{children:[(0,s.jsx)(v.CommandEmpty,{children:"No results found."}),(0,s.jsx)(v.CommandGroup,{heading:"Pages",children:m.jt.map(e=>(0,s.jsxs)(v.CommandItem,{value:e.label,onSelect:()=>g(()=>r.push(e.href)),children:[(0,s.jsx)("div",{className:"mr-2 flex h-4 w-4 items-center justify-center",children:(0,s.jsx)(n.A,{className:"h-3 w-3",strokeWidth:1})}),e.label]},e.href))},"pages"),(0,s.jsx)(v.CommandSeparator,{}),(0,s.jsxs)(v.CommandGroup,{heading:"Theme",children:[(0,s.jsxs)(v.CommandItem,{onSelect:()=>g(()=>x("light")),children:[(0,s.jsx)(o.A,{className:"mr-2 h-4 w-4",strokeWidth:1}),"Light"]}),(0,s.jsxs)(v.CommandItem,{onSelect:()=>g(()=>x("dark")),children:[(0,s.jsx)(a.A,{className:"mr-2 h-4 w-4",strokeWidth:1}),"Dark"]}),(0,s.jsxs)(v.CommandItem,{onSelect:()=>g(()=>x("classic-dark")),children:[(0,s.jsx)(a.A,{className:"mr-2 h-4 w-4",strokeWidth:1}),"Classic dark"]}),(0,s.jsxs)(v.CommandItem,{onSelect:()=>g(()=>x("system")),children:[(0,s.jsx)(i,{className:"mr-2 h-4 w-4",strokeWidth:1}),"System"]})]})]})]})]})}},37207:(e,t,r)=>{Promise.resolve().then(r.bind(r,63645)),Promise.resolve().then(r.bind(r,28044)),Promise.resolve().then(r.bind(r,52595)),Promise.resolve().then(r.bind(r,22792)),Promise.resolve().then(r.bind(r,47642)),Promise.resolve().then(r.t.bind(r,15531,23)),Promise.resolve().then(r.bind(r,97218)),Promise.resolve().then(r.bind(r,99284)),Promise.resolve().then(r.bind(r,43195)),Promise.resolve().then(r.bind(r,10955)),Promise.resolve().then(r.bind(r,35621)),Promise.resolve().then(r.bind(r,80335)),Promise.resolve().then(r.bind(r,55782)),Promise.resolve().then(r.bind(r,41727)),Promise.resolve().then(r.bind(r,16973)),Promise.resolve().then(r.bind(r,525)),Promise.resolve().then(r.bind(r,37505)),Promise.resolve().then(r.bind(r,90527)),Promise.resolve().then(r.bind(r,20874)),Promise.resolve().then(r.bind(r,96903)),Promise.resolve().then(r.bind(r,91)),Promise.resolve().then(r.bind(r,34819)),Promise.resolve().then(r.bind(r,78921)),Promise.resolve().then(r.t.bind(r,1873,23)),Promise.resolve().then(r.bind(r,89524)),Promise.resolve().then(r.bind(r,35095)),Promise.resolve().then(r.bind(r,91219)),Promise.resolve().then(r.bind(r,73812)),Promise.resolve().then(r.bind(r,36393)),Promise.resolve().then(r.bind(r,71208)),Promise.resolve().then(r.bind(r,91965)),Promise.resolve().then(r.bind(r,17711)),Promise.resolve().then(r.bind(r,3244)),Promise.resolve().then(r.bind(r,1517)),Promise.resolve().then(r.bind(r,52336)),Promise.resolve().then(r.bind(r,31829)),Promise.resolve().then(r.bind(r,25415)),Promise.resolve().then(r.bind(r,12401)),Promise.resolve().then(r.bind(r,55813)),Promise.resolve().then(r.bind(r,8856)),Promise.resolve().then(r.bind(r,46845)),Promise.resolve().then(r.bind(r,84458)),Promise.resolve().then(r.bind(r,62800)),Promise.resolve().then(r.bind(r,79099)),Promise.resolve().then(r.bind(r,53176)),Promise.resolve().then(r.bind(r,43129)),Promise.resolve().then(r.bind(r,85986)),Promise.resolve().then(r.bind(r,40989)),Promise.resolve().then(r.bind(r,3999)),Promise.resolve().then(r.bind(r,2889)),Promise.resolve().then(r.bind(r,75435)),Promise.resolve().then(r.bind(r,28182)),Promise.resolve().then(r.bind(r,57782)),Promise.resolve().then(r.bind(r,51834)),Promise.resolve().then(r.bind(r,8121)),Promise.resolve().then(r.bind(r,28694)),Promise.resolve().then(r.bind(r,18807)),Promise.resolve().then(r.bind(r,83020)),Promise.resolve().then(r.bind(r,86084)),Promise.resolve().then(r.bind(r,13463)),Promise.resolve().then(r.bind(r,33440)),Promise.resolve().then(r.bind(r,41274)),Promise.resolve().then(r.bind(r,43575)),Promise.resolve().then(r.bind(r,62007)),Promise.resolve().then(r.bind(r,72451)),Promise.resolve().then(r.bind(r,48744)),Promise.resolve().then(r.bind(r,73867)),Promise.resolve().then(r.bind(r,34265)),Promise.resolve().then(r.bind(r,29817)),Promise.resolve().then(r.bind(r,79155)),Promise.resolve().then(r.bind(r,69975)),Promise.resolve().then(r.bind(r,44561)),Promise.resolve().then(r.bind(r,92691)),Promise.resolve().then(r.bind(r,6753)),Promise.resolve().then(r.bind(r,38169)),Promise.resolve().then(r.bind(r,80129)),Promise.resolve().then(r.bind(r,17973))},40844:(e,t,r)=>{"use strict";r.d(t,{U:()=>i,u:()=>l});var s=r(31710),n=r(48392),o=r(54990);let a=(0,o.createContext)(void 0);function i(e){let{children:t}=e,[r,i]=(0,o.useState)("nextjs");return(0,o.useEffect)(()=>{let e=localStorage.getItem("preferredFramework");e&&Object.keys(n.e2).includes(e)&&i(e)},[]),(0,s.jsx)(a.Provider,{value:{framework:r,setFramework:e=>{i(e),localStorage.setItem("preferredFramework",e)}},children:t})}function l(){let e=(0,o.useContext)(a);if(void 0===e)throw Error("useFramework must be used within a FrameworkProvider");return e}},47642:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ThemeSwitcherDropdown:()=>m});var s=r(31710),n=r(79671),o=r(81682),a=r(47375),i=r(54990),l=(r(51834),r(53176)),d=r(35621),c=r(59803);let m=()=>{let[e,t]=(0,i.useState)(!1),{theme:r,setTheme:m,resolvedTheme:u}=(0,a.D)();if((0,i.useEffect)(()=>{t(!0)},[]),!e)return null;let h="text-foreground-light group-data-[state=open]:text-foreground";return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(l.DropdownMenu,{children:[(0,s.jsx)(l.DropdownMenuTrigger,{asChild:!0,children:(0,s.jsx)(d.Button,{type:"text",size:"tiny",className:"px-1 group",icon:(null==u?void 0:u.includes("light"))?(0,s.jsx)(n.A,{className:h}):(0,s.jsx)(o.A,{className:h})})}),(0,s.jsxs)(l.DropdownMenuContent,{className:"w-56",align:"start",children:[(0,s.jsx)(l.DropdownMenuLabel,{children:"Theme"}),(0,s.jsx)(l.DropdownMenuSeparator,{}),(0,s.jsx)(l.DropdownMenuRadioGroup,{value:r,onValueChange:e=>m(e),children:c.h.map(e=>(0,s.jsx)(l.DropdownMenuRadioItem,{value:e.value,children:e.name},e.value))})]})]})})}},48392:(e,t,r)=>{"use strict";r.d(t,{Oq:()=>s,e2:()=>o,jt:()=>n});let s={title:"Components",items:[{title:"Client",supportedFrameworks:["nextjs","react-router","tanstack","react"],href:"/docs/nextjs/client",items:[],commandItemLabel:"Supabase Client"},{title:"Password-Based Auth",supportedFrameworks:["nextjs","react-router","tanstack","react"],href:"/docs/nextjs/password-based-auth",items:[],commandItemLabel:"Password-Based Auth"},{title:"Social Auth",supportedFrameworks:["nextjs","react-router","tanstack","react"],href:"/docs/nextjs/social-auth",items:[],new:!0,commandItemLabel:"Social Auth"},{title:"Dropzone",supportedFrameworks:["nextjs","react-router","tanstack","react"],href:"/docs/nextjs/dropzone",items:[],commandItemLabel:"Dropzone (File Upload)"},{title:"Realtime Cursor",supportedFrameworks:["nextjs","react-router","tanstack","react"],href:"/docs/nextjs/realtime-cursor",items:[],commandItemLabel:"Realtime Cursor"},{title:"Current User Avatar",supportedFrameworks:["nextjs","react-router","tanstack","react"],href:"/docs/nextjs/current-user-avatar",items:[],commandItemLabel:"Current User Avatar"},{title:"Realtime Avatar Stack",supportedFrameworks:["nextjs","react-router","tanstack","react"],href:"/docs/nextjs/realtime-avatar-stack",items:[],commandItemLabel:"Realtime Avatar Stack"},{title:"Realtime Chat",supportedFrameworks:["nextjs","react-router","tanstack","react"],href:"/docs/nextjs/realtime-chat",items:[],commandItemLabel:"Realtime Chat"},{title:"Infinite Query Hook",supportedFrameworks:[],href:"/docs/infinite-query-hook",new:!0,items:[],commandItemLabel:"Infinite Query Hook"}]},n=[...[{title:"Introduction",href:"/docs/getting-started/introduction",items:[],commandItemLabel:"Introduction"},{title:"Quick Start",href:"/docs/getting-started/quickstart",items:[],commandItemLabel:"Quick Start"},{title:"FAQ",href:"/docs/getting-started/faq",items:[],commandItemLabel:"FAQ"}].map(e=>({label:e.commandItemLabel,href:e.href})),...[{title:"Prompts",href:"/docs/ai-editors-rules/prompts",items:[],commandItemLabel:"AI Editors Rules"}].map(e=>({label:e.commandItemLabel,href:e.href})),...s.items.map(e=>({label:e.commandItemLabel,href:e.href}))],o={nextjs:"Next.js","react-router":"React Router",tanstack:"TanStack Start",react:"React SPA"}},48767:(e,t,r)=>{"use strict";r.d(t,{H:()=>o,x:()=>n});var s=r(54990);let n=(0,s.createContext)(null);function o(){let e=(0,s.useContext)(n);if(null===e)throw Error("useMobileMenu must be used within a MobileMenuProvider. Ensure your component is wrapped with MobileMenuProvider.");return e}},52595:(e,t,r)=>{"use strict";r.d(t,{MobileMenuSheet:()=>a});var s=r(31710),n=r(48767),o=r(86084);function a(e){let{children:t}=e,{open:r,setOpen:a}=(0,n.H)();return(0,s.jsx)(o.Sheet,{open:r,onOpenChange:a,children:t})}},59803:(e,t,r)=>{"use strict";r.d(t,{h:()=>s});let s=[{name:"Dark",value:"dark"},{name:"Light",value:"light"},{name:"Classic Dark",value:"classic-dark"},{name:"System",value:"system"}]},63645:(e,t,r)=>{"use strict";r.d(t,{TelemetryWrapper:()=>R});var s=r(31710),n=r(4473),o=r(56631),a=r(54990),i=r(97218),l=r(35484),d=r(94485),c=r(66096),m=r.n(c),u=r(35621),h=r(15531),v=r.n(h),p=r(35095),b=r(44561),x=r(11448);let g=(0,x.F)((0,l.cn)("relative w-full text-sm rounded-lg border p-4 [&>svg~*]:pl-10 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground","[&>svg]:w-[23px] [&>svg]:h-[23px] [&>svg]:p-1 [&>svg]:flex [&>svg]:rounded"),{variants:{variant:{default:"text-foreground bg-alternative border [&>svg]:text-background [&>svg]:bg-foreground",destructive:"text border-destructive-400 bg-destructive-200 [&>svg]:text-destructive-200 [&>svg]:bg-destructive-600",warning:"border-warning-400 bg-warning-200 [&>svg]:text-warning-200 [&>svg]:bg-warning-600"}},defaultVariants:{variant:"default"}}),f=a.forwardRef((e,t)=>{let{className:r,variant:n,...o}=e;return(0,s.jsx)("div",{ref:t,role:"alert",className:(0,l.cn)(g({variant:n}),r),...o})});f.displayName="Alert";let j=a.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("h5",{ref:t,className:(0,l.cn)("mb-1",r),...n})});j.displayName="AlertTitle";let C=a.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("text-sm [&_p]:leading-relaxed text-foreground-light font-normal",r),...n})});C.displayName="AlertDescription";let w={note:"default",tip:"default",caution:"warning",danger:"destructive",deprecation:"warning",default:"default",warning:"warning",destructive:"destructive"},P=()=>(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 21 20",className:"w-6 h-6",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M0.625 9.8252C0.625 4.44043 4.99023 0.0751953 10.375 0.0751953C15.7598 0.0751953 20.125 4.44043 20.125 9.8252C20.125 15.21 15.7598 19.5752 10.375 19.5752C4.99023 19.5752 0.625 15.21 0.625 9.8252ZM9.3584 4.38135C9.45117 4.28857 9.55518 4.20996 9.66699 4.14648C9.88086 4.02539 10.1245 3.96045 10.375 3.96045C10.5845 3.96045 10.7896 4.00586 10.9766 4.09229C11.1294 4.1626 11.2705 4.26025 11.3916 4.38135C11.6611 4.65088 11.8125 5.0166 11.8125 5.39795C11.8125 5.5249 11.7959 5.6499 11.7637 5.77002C11.6987 6.01172 11.5718 6.23438 11.3916 6.41455C11.1221 6.68408 10.7563 6.83545 10.375 6.83545C9.99365 6.83545 9.62793 6.68408 9.3584 6.41455C9.08887 6.14502 8.9375 5.7793 8.9375 5.39795C8.9375 5.29492 8.94873 5.19287 8.97021 5.09375C9.02783 4.82568 9.16162 4.57812 9.3584 4.38135ZM10.375 15.6899C10.0933 15.6899 9.82275 15.5781 9.62354 15.3789C9.42432 15.1797 9.3125 14.9092 9.3125 14.6274V9.31494C9.3125 9.0332 9.42432 8.7627 9.62354 8.56348C9.82275 8.36426 10.0933 8.25244 10.375 8.25244C10.6567 8.25244 10.9272 8.36426 11.1265 8.56348C11.3257 8.7627 11.4375 9.0332 11.4375 9.31494V14.6274C11.4375 14.7944 11.3979 14.9575 11.3242 15.104C11.2739 15.2046 11.2075 15.2979 11.1265 15.3789C10.9272 15.5781 10.6567 15.6899 10.375 15.6899Z"})}),k=()=>(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 22 20",className:"w-6 h-6",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.15137 1.95117C9.30615 -0.0488281 12.1943 -0.0488281 13.3481 1.95117L20.7031 14.6992C21.8574 16.6992 20.4131 19.1992 18.104 19.1992H3.39502C1.08594 19.1992 -0.356933 16.6992 0.797364 14.6992L8.15137 1.95117ZM11.7666 16.0083C11.4971 16.2778 11.1313 16.4292 10.75 16.4292C10.3687 16.4292 10.0029 16.2778 9.7334 16.0083C9.46387 15.7388 9.3125 15.373 9.3125 14.9917C9.3125 14.9307 9.31641 14.8706 9.32373 14.811C9.33545 14.7197 9.35547 14.6304 9.38379 14.5439L9.41406 14.4609C9.48584 14.2803 9.59375 14.1147 9.7334 13.9751C10.0029 13.7056 10.3687 13.5542 10.75 13.5542C11.1313 13.5542 11.4971 13.7056 11.7666 13.9751C12.0361 14.2446 12.1875 14.6104 12.1875 14.9917C12.1875 15.373 12.0361 15.7388 11.7666 16.0083ZM10.75 4.69971C11.0317 4.69971 11.3022 4.81152 11.5015 5.01074C11.7007 5.20996 11.8125 5.48047 11.8125 5.76221V11.0747C11.8125 11.3564 11.7007 11.627 11.5015 11.8262C11.3022 12.0254 11.0317 12.1372 10.75 12.1372C10.4683 12.1372 10.1978 12.0254 9.99854 11.8262C9.79932 11.627 9.6875 11.3564 9.6875 11.0747V5.76221C9.6875 5.48047 9.79932 5.20996 9.99854 5.01074C10.1978 4.81152 10.4683 4.69971 10.75 4.69971Z"})}),y=(0,x.F)("",{variants:{type:{default:"[&>svg]:bg-foreground-muted",warning:"",destructive:""}}}),N=(0,x.F)("",{variants:{type:{default:"bg-surface-200/25 border border-default",warning:"bg-alternative border border-default",destructive:"bg-alternative border border-default"}}}),L=(0,a.forwardRef)((e,t)=>{var r,n,o,a,i,d,c,m;let{type:u="note",variant:h,showIcon:v=!0,label:p,title:b,description:x,children:g,...L}=e,I=h?w[h]:w[u];return(0,s.jsxs)(f,{ref:t,variant:I,...L,className:(0,l.cn)("mb-2",y({type:I}),N({type:I}),L.className),children:[v&&"warning"===I||"destructive"===I?(0,s.jsx)(k,{}):v?(0,s.jsx)(P,{}):null,p||b?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(j,{...null==(r=L.childProps)?void 0:r.title,className:(0,l.cn)("text mt-0.5 flex gap-3 text-sm [&_p]:mb-1.5 [&_p]:mt-0",!p&&"flex-col",null==(o=L.childProps)||null==(n=o.title)?void 0:n.className),children:p||b}),x&&(0,s.jsx)(C,{className:null==(i=L.childProps)||null==(a=i.description)?void 0:a.className,children:x}),g&&(0,s.jsx)(C,{...null==(d=L.childProps)?void 0:d.description,className:(0,l.cn)("[&_p]:mb-1.5 [&_p]:mt-0",null==(m=L.childProps)||null==(c=m.description)?void 0:c.className),children:g})]}):(0,s.jsx)("div",{className:"text mt [&_p]:mb-1.5 [&_p]:mt-0 mt-0.5 [&_p:last-child]:mb-0",children:g})]})}),I=e=>{let{children:t,...r}=e,[n,i]=(0,a.useState)(!1),{categories:l,updateServices:d}=(0,o.BL)(),[c,m]=(0,a.useState)(()=>new Map);function u(e){let t=new Map(c);e.forEach(e=>{t.set(e.id,e.status)}),m(t)}return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("button",{...r,onClick:()=>i(!0),children:t}),(0,s.jsx)(p.default,{closable:!0,visible:n,alignFooter:"right",onCancel:()=>{i(!1)},onConfirm:()=>{d(Array.from(c.entries()).map(e=>{let[t,r]=e;return{serviceId:t,status:r}})),i(!1)},header:"Privacy Settings",onInteractOutside:e=>{"dismissableLayer.pointerDownOutside"===e.type&&i(!n)},className:"max-w-[calc(100vw-4rem)]",size:"medium",children:(0,s.jsx)("div",{className:"pt-3 divide-y divide-border",children:null===l?(0,s.jsx)(p.default.Content,{children:(0,s.jsx)(L,{type:"warning",title:"Unable to Load Privacy Settings",description:(0,s.jsxs)(s.Fragment,{children:["We couldn't load the privacy settings due to an ad blocker or network error. Please disable any ad blockers and try again. If the problem persists, please"," ",(0,s.jsx)(v(),{href:"https://supabase.com/dashboard/support/new",className:"underline",children:"contact support"}),"."]})})}):[...l].reverse().map(e=>(0,s.jsx)(S,{category:e,handleServicesChange:u},e.slug))})})]})};function S(e){let{category:t,handleServicesChange:r}=e,[n,o]=(0,a.useState)(()=>t.services.every(e=>e.consent.status));return(0,s.jsx)(p.default.Content,{children:(0,s.jsx)(b.default,{checked:n,defaultChecked:n,disabled:t.isEssential,onChange:function(){o(!n),r(t.services.map(e=>({id:e.id,status:!n})))},label:t.label,descriptionText:(0,s.jsxs)(s.Fragment,{children:[t.description,(0,s.jsx)("br",{}),(0,s.jsx)(v(),{href:"https://supabase.com/privacy#8-cookies-and-similar-technologies-used-on-our-european-services",className:"underline",children:"Learn more"})]})})},t.slug)}let A=e=>{let{onAccept:t=m(),onOptOut:r=m()}=e,n=(0,o.dv)(639);return(0,s.jsxs)("div",{className:"py-1 flex flex-col gap-y-3 w-full",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-sm text-foreground",children:["We use cookies to collect data and improve our services."," ",(0,s.jsx)("a",{target:"_blank",rel:"noreferrer noopener",href:"https://supabase.com/privacy#8-cookies-and-similar-technologies-used-on-our-european-services",className:"hidden sm:inline underline underline-offset-2 decoration-foreground-lighter hover:decoration-foreground-light transition-all",children:"Learn more"})," "]}),(0,s.jsxs)("div",{className:"flex items-center justify-start gap-x-2 sm:hidden",children:[(0,s.jsx)("a",{target:"_blank",rel:"noreferrer noopener",href:"https://supabase.com/privacy#8-cookies-and-similar-technologies-used-on-our-european-services",className:"underline underline-offset-2 text-foreground-light hover:decoration-foreground-light transition-all",children:"Learn more"}),(0,s.jsx)("span",{className:"text-foreground-lighter text-xs",children:"•"}),(0,s.jsx)(I,{className:"underline underline-offset-2 inline text-light",children:"Privacy settings"})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(u.Button,{type:"default",onClick:t,size:n?"small":"tiny",block:n,children:"Accept"}),(0,s.jsx)(u.Button,{type:n?"outline":"text",onClick:r,size:n?"small":"tiny",block:n,children:"Opt out"}),(0,s.jsx)(u.Button,{asChild:!0,type:"text",className:"hidden sm:block text-light hover:text-foreground",children:(0,s.jsx)(I,{children:"Privacy settings"})})]})]})},{TELEMETRY_DATA:M}=o.rS,F=()=>{let e=(0,a.useRef)(),t=(0,d.s)(o.YK),r=(0,a.useCallback)(()=>{o.Bd&&(t.acceptAll(),e.current&&i.toast.dismiss(e.current))},[t.acceptAll]),n=(0,a.useCallback)(()=>{o.Bd&&(t.denyAll(),document.cookie="".concat(M,"=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/"),e.current&&i.toast.dismiss(e.current))},[t.denyAll]);return(0,a.useEffect)(()=>{o.Bd&&t.showConsentToast?e.current=(0,i.toast)((0,s.jsx)(A,{onAccept:r,onOptOut:n}),{id:"consent-toast",position:"bottom-right",duration:1/0,closeButton:!1,dismissible:!1,className:(0,l.cn)("!w-screen !fixed !border-t !h-auto !left-0 !bottom-0 !top-auto !right-0 !rounded-none !max-w-none !bg-overlay !text","sm:!w-full sm:!max-w-[356px] sm:!left-auto sm:!right-8 sm:!bottom-8 sm:!rounded-lg sm:border")}):e.current&&i.toast.dismiss(e.current)},[t.showConsentToast]),{hasAcceptedConsent:t.hasConsented}},R=()=>{let{hasAcceptedConsent:e}=F();return(0,s.jsx)(o.LZ,{API_URL:n.H,hasAcceptedConsent:e,enabled:o.pe})}},79671:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(47966).A)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},81682:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(47966).A)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[2486,6985,2105,3502,6276,6701,6602,5710,8443,494,4735,2053,7358],()=>t(37207)),_N_E=e.O()}]);