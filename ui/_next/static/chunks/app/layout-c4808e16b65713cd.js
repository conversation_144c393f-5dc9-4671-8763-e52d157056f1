(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{20431:()=>{},22104:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>u});var a=r(31710),s=r(63276),o=r(47375),n=r(40844),i=r(54990),l=r(48767);function m(e){let{children:t}=e,[r,s]=(0,i.useState)(!1),o=(0,i.useCallback)(e=>{s(e)},[]),n=(0,i.useCallback)(()=>{s(e=>!e)},[]);return(0,a.jsx)(l.x.Provider,{value:{open:r,setOpen:o,toggle:n},children:t})}var c=r(56631),d=r(48744);function u(e){let{children:t,...r}=e;return(0,a.jsx)(c.OJ,{children:(0,a.jsx)(s.Provider,{children:(0,a.jsx)(o.N,{...r,children:(0,a.jsx)(m,{children:(0,a.jsx)(n.U,{children:(0,a.jsx)(d.TooltipProvider,{delayDuration:0,children:t})})})})})})}},26854:(e,t,r)=>{"use strict";r.d(t,{SonnerToaster:()=>n});var a=r(31710),s=r(61313),o=r(41274);function n(){let[e]=(0,s.U)();return(0,a.jsx)(o.SonnerToaster,{position:e.sonnerPosition,expand:e.sonnerExpand})}},40844:(e,t,r)=>{"use strict";r.d(t,{U:()=>i,u:()=>l});var a=r(31710),s=r(48392),o=r(54990);let n=(0,o.createContext)(void 0);function i(e){let{children:t}=e,[r,i]=(0,o.useState)("nextjs");return(0,o.useEffect)(()=>{let e=localStorage.getItem("preferredFramework");e&&Object.keys(s.e2).includes(e)&&i(e)},[]),(0,a.jsx)(n.Provider,{value:{framework:r,setFramework:e=>{i(e),localStorage.setItem("preferredFramework",e)}},children:t})}function l(){let e=(0,o.useContext)(n);if(void 0===e)throw Error("useFramework must be used within a FrameworkProvider");return e}},48392:(e,t,r)=>{"use strict";r.d(t,{Oq:()=>a,e2:()=>o,jt:()=>s});let a={title:"Components",items:[{title:"Client",supportedFrameworks:["nextjs","react-router","tanstack","react"],href:"/docs/nextjs/client",items:[],commandItemLabel:"Supabase Client"},{title:"Password-Based Auth",supportedFrameworks:["nextjs","react-router","tanstack","react"],href:"/docs/nextjs/password-based-auth",items:[],commandItemLabel:"Password-Based Auth"},{title:"Social Auth",supportedFrameworks:["nextjs","react-router","tanstack","react"],href:"/docs/nextjs/social-auth",items:[],new:!0,commandItemLabel:"Social Auth"},{title:"Dropzone",supportedFrameworks:["nextjs","react-router","tanstack","react"],href:"/docs/nextjs/dropzone",items:[],commandItemLabel:"Dropzone (File Upload)"},{title:"Realtime Cursor",supportedFrameworks:["nextjs","react-router","tanstack","react"],href:"/docs/nextjs/realtime-cursor",items:[],commandItemLabel:"Realtime Cursor"},{title:"Current User Avatar",supportedFrameworks:["nextjs","react-router","tanstack","react"],href:"/docs/nextjs/current-user-avatar",items:[],commandItemLabel:"Current User Avatar"},{title:"Realtime Avatar Stack",supportedFrameworks:["nextjs","react-router","tanstack","react"],href:"/docs/nextjs/realtime-avatar-stack",items:[],commandItemLabel:"Realtime Avatar Stack"},{title:"Realtime Chat",supportedFrameworks:["nextjs","react-router","tanstack","react"],href:"/docs/nextjs/realtime-chat",items:[],commandItemLabel:"Realtime Chat"},{title:"Infinite Query Hook",supportedFrameworks:[],href:"/docs/infinite-query-hook",new:!0,items:[],commandItemLabel:"Infinite Query Hook"}]},s=[...[{title:"Introduction",href:"/docs/getting-started/introduction",items:[],commandItemLabel:"Introduction"},{title:"Quick Start",href:"/docs/getting-started/quickstart",items:[],commandItemLabel:"Quick Start"},{title:"FAQ",href:"/docs/getting-started/faq",items:[],commandItemLabel:"FAQ"}].map(e=>({label:e.commandItemLabel,href:e.href})),...[{title:"Prompts",href:"/docs/ai-editors-rules/prompts",items:[],commandItemLabel:"AI Editors Rules"}].map(e=>({label:e.commandItemLabel,href:e.href})),...a.items.map(e=>({label:e.commandItemLabel,href:e.href}))],o={nextjs:"Next.js","react-router":"React Router",tanstack:"TanStack Start",react:"React SPA"}},48767:(e,t,r)=>{"use strict";r.d(t,{H:()=>o,x:()=>s});var a=r(54990);let s=(0,a.createContext)(null);function o(){let e=(0,a.useContext)(s);if(null===e)throw Error("useMobileMenu must be used within a MobileMenuProvider. Ensure your component is wrapped with MobileMenuProvider.");return e}},49358:(e,t,r)=>{Promise.resolve().then(r.bind(r,22104)),Promise.resolve().then(r.bind(r,26854)),Promise.resolve().then(r.t.bind(r,20431,23)),Promise.resolve().then(r.t.bind(r,86533,23)),Promise.resolve().then(r.bind(r,42883)),Promise.resolve().then(r.bind(r,12287)),Promise.resolve().then(r.bind(r,21330)),Promise.resolve().then(r.bind(r,43701)),Promise.resolve().then(r.bind(r,97626)),Promise.resolve().then(r.bind(r,66533)),Promise.resolve().then(r.bind(r,39346))},61313:(e,t,r)=>{"use strict";r.d(t,{U:()=>o});var a=r(63276);let s=(0,r(80898).tG)("config",{style:"default",radius:.5,sonnerPosition:"bottom-right",sonnerExpand:!1});function o(){return(0,a.useAtom)(s)}},86533:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_cbad29"}}},e=>{var t=t=>e(e.s=t);e.O(0,[152,2006,917,6985,2105,3502,6701,3276,898,5710,8443,4735,2053,7358],()=>t(49358)),_N_E=e.O()}]);