(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5710],{10287:(e,t,n)=>{"use strict";n.d(t,{pe:()=>r,db:()=>a,rS:()=>s.r});let r=!0,a=!0;var s=n(35434)},12287:(e,t,n)=>{"use strict";n.r(t),n.d(t,{FeatureFlagContext:()=>p,FeatureFlagProvider:()=>g,getFeatureFlags:()=>u,trackFeatureFlag:()=>d,useFeatureFlags:()=>h});var r=n(31710),a=n(27224),s=n(54990),o=n(42883),l=n(25949),i=n(22847),c=n(21330);async function u(e){return await (0,i.J)("".concat((0,c.ensurePlatformSuffix)(e),"/telemetry/feature-flags"))}async function d(e,t){(0,l.up)()&&await (0,i.b)("".concat((0,c.ensurePlatformSuffix)(e),"/telemetry/feature-flags/track"),{body:t})}let p=(0,s.createContext)({API_URL:void 0,configcat:{},posthog:{},hasLoaded:!1}),g=e=>{let{API_URL:t,enabled:n=!0,getConfigCatFlags:l,children:i}=e,c=(0,o.useUser)(),[d,g]=(0,s.useState)({API_URL:t,configcat:{},posthog:{},hasLoaded:!1});return(0,s.useEffect)(()=>{let e=!0;return async function(){if(!n)return;let r={configcat:{},posthog:{}},[a,s]=await Promise.all([u(t),"function"==typeof l?l(null==c?void 0:c.email):Promise.resolve([])]);if(a&&(r.posthog=a),"function"==typeof l){let e={};try{let t=function(){let e=document.cookie.split(";"),t={};for(var n=0;n<e.length;n++){var[r,a]=e[n].split("=");t[r.trim()]=unescape(a)}return t}();e=JSON.parse(t["vercel-flag-overrides"])}catch(e){}s.forEach(t=>{var n,a;r.configcat[t.settingKey]=null!=(a=e[t.settingKey])?a:null===t.settingValue?null:null!=(n=t.settingValue)&&n})}r.hasLoaded=!0,e&&g(r)}(),()=>{e=!1}},[n,null==c?void 0:c.email]),(0,r.jsxs)(p.Provider,{value:d,children:[(0,r.jsx)(a.P,{values:d.configcat}),i]})},h=()=>(0,s.useContext)(p)},21330:(e,t,n)=>{"use strict";n.r(t),n.d(t,{detectBrowser:()=>a,ensurePlatformSuffix:()=>i,isBrowser:()=>s,mergeRefs:()=>c,useReducedMotion:()=>l});var r=n(54990);let a=()=>{if(navigator){if(-1!==navigator.userAgent.indexOf("Chrome"))return"Chrome";else if(-1!==navigator.userAgent.indexOf("Firefox"))return"Firefox";else if(-1!==navigator.userAgent.indexOf("Safari"))return"Safari"}},s=!0,o=window.matchMedia("(prefers-reduced-motion: reduce)"),l=()=>!!o&&(0,r.useSyncExternalStore)(e=>(o.addEventListener("change",e),()=>{o.removeEventListener("change",e)}),()=>o.matches,()=>!1);function i(e){return e.endsWith("/platform")?e:"".concat(e,"/platform")}function c(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>{t.forEach(t=>{"function"==typeof t?t(e):null!==t&&"object"==typeof t&&null!==t&&"current"in t&&(t.current=e)})}}},22847:(e,t,n)=>{"use strict";n.d(t,{J:()=>a,b:()=>s});var r=n(42883);async function a(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{headers:n,...a}=t,s=await (0,r.getAccessToken)();return fetch(e,{method:"GET",headers:new Headers({"Content-Type":"application/json",Accept:"application/json",...s&&{Authorization:"Bearer ".concat(s)},...n}),credentials:"include",referrerPolicy:"no-referrer-when-downgrade",...a}).then(e=>e.json()).catch(e=>{throw e})}async function s(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{headers:a,...s}=n,o=await (0,r.getAccessToken)();return fetch(e,{method:"POST",headers:new Headers({"Content-Type":"application/json",Accept:"application/json",...o&&{Authorization:"Bearer ".concat(o)},...a}),credentials:"include",referrerPolicy:"no-referrer-when-downgrade",body:JSON.stringify(t),...s}).catch(e=>{throw e})}},25949:(e,t,n)=>{"use strict";n.d(t,{BL:()=>c,YK:()=>l,up:()=>i});var r=n(22761),a=n(94485),s=n(10287),o=n(37811);let l=(0,r.BX)({UC:null,categories:null,showConsentToast:!1,hasConsented:!1,acceptAll:()=>{if(!l.UC)return;let e=l.hasConsented;l.hasConsented=!0,l.showConsentToast=!1,l.UC.acceptAllServices().then(()=>{var e,t;l.categories=null!=(t=null==(e=l.UC)?void 0:e.getCategoriesBaseInfo())?t:null}).catch(()=>{l.hasConsented=e,l.showConsentToast=!0})},denyAll:()=>{if(!l.UC)return;let e=l.hasConsented;l.hasConsented=!1,l.showConsentToast=!1,l.UC.denyAllServices().then(()=>{var e,t;l.categories=null!=(t=null==(e=l.UC)?void 0:e.getCategoriesBaseInfo())?t:null}).catch(()=>{l.showConsentToast=e})},updateServices:e=>{l.UC&&(l.showConsentToast=!1,l.UC.updateServices(e).then(()=>{var e,t,n,r;l.hasConsented=null!=(n=null==(e=l.UC)?void 0:e.areAllConsentsAccepted())&&n,l.categories=null!=(r=null==(t=l.UC)?void 0:t.getCategoriesBaseInfo())?r:null}).catch(()=>{l.showConsentToast=!0}))}});function i(){return(0,r.P9)(l).hasConsented}function c(){let e=(0,a.s)(l);return{hasAccepted:e.hasConsented,categories:e.categories,acceptAll:e.acceptAll,denyAll:e.denyAll,updateServices:e.updateServices}}(async function e(){var e;if(!s.pe)return;if("local"===o.env.NEXT_PUBLIC_ENVIRONMENT||"staging"===o.env.NEXT_PUBLIC_ENVIRONMENT){l.hasConsented=!0;return}let{default:t}=await Promise.all([n.e(3245),n.e(3455)]).then(n.bind(n,31466)),r=new t("Ot6xUzyQgU2yCM",{rulesetId:"Ot6xUzyQgU2yCM",useRulesetId:!0}),a=await r.init();l.UC=r;let i=r.areAllConsentsAccepted();l.showConsentToast=0===a.initialLayer,l.hasConsented=i,l.categories=r.getCategoriesBaseInfo(),i||(null==(e=localStorage)?void 0:e.getItem(s.rS.TELEMETRY_CONSENT))!=="true"||(l.acceptAll(),localStorage.removeItem(s.rS.TELEMETRY_CONSENT))})()},27415:(e,t,n)=>{"use strict";n.d(t,{uw:()=>I});var r,a,s,o,l=n(77353),i=n(37811);let c=i.env.NEXT_PUBLIC_STORAGE_KEY||"supabase.dashboard.auth.token",u=i.env.NEXT_PUBLIC_AUTH_DEBUG_KEY||"supabase.dashboard.auth.debug",d=i.env.NEXT_PUBLIC_AUTH_DEBUG_PERSISTED_KEY||"supabase.dashboard.auth.debug.persist",p=i.env.NEXT_PUBLIC_AUTH_NAVIGATOR_LOCK_KEY||"supabase.dashboard.auth.navigatorLock.disabled";function g(e){try{var t,n;return null==(n=globalThis)||null==(t=n.localStorage)?void 0:t.getItem(e)}catch(e){return null}}let h="true"===g(u),f="true"===g(d),m="true"!==g(p),E=!i.env.NEXT_PUBLIC_AUTH_DETECT_SESSION_IN_URL||"true"===i.env.NEXT_PUBLIC_AUTH_DETECT_SESSION_IN_URL,_=!!(m&&(null==(a=globalThis)||null==(r=a.navigator)?void 0:r.locks));!m||(null==(o=globalThis)||null==(s=o.navigator)?void 0:s.locks)||console.warn("This browser does not support the Navigator Locks API. Please update it.");let S=Math.random().toString(16).substring(2),v=new Promise((e,t)=>{if(!f)return void e(null);let n=indexedDB.open("auth-debug-log",1);n.onupgradeneeded=e=>{var t;let n=null==e||null==(t=e.target)?void 0:t.result;n&&n.createObjectStore("events",{autoIncrement:!0})},n.onsuccess=t=>{console.log("Opened persisted auth debug log IndexedDB database",S),e(t.target.result)},n.onerror=t=>{console.error("Failed to open persisted auth debug log IndexedDB database",t),e(null)}}),I=new l.UJ({url:"https://alt.supabase.io/auth/v1",storageKey:c,detectSessionInUrl:E,debug:!!h&&(!f||function(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];console.log(e,...n);let a=structuredClone(n);a.forEach(e=>{"object"==typeof e&&null!==e&&(delete e.user,delete e.access_token,delete e.token_type,delete e.provider_token)}),(async()=>{try{let t=await v;if(!t)return;let n=t.transaction(["events"],"readwrite");n.onerror=e=>{console.error("Failed to write to persisted auth debug log IndexedDB database",e),v=Promise.resolve(null)},n.objectStore("events").add({m:e.replace(/^GoTrueClient@/i,""),a:a,l:window.location.pathname,t:S})}catch(e){console.error("Failed to log to persisted auth debug log IndexedDB database",e),v=Promise.resolve(null)}})()}),lock:_?l.JS:void 0,..."localStorage"in globalThis?{storage:globalThis.localStorage,userStorage:globalThis.localStorage}:null})},35434:(e,t,n)=>{"use strict";n.d(t,{r:()=>r,s:()=>s});let r={AI_ASSISTANT_STATE:e=>"supabase-ai-assistant-state-".concat(e),SIDEBAR_BEHAVIOR:"supabase-sidebar-behavior",EDITOR_PANEL_STATE:"supabase-editor-panel-state",UI_PREVIEW_API_SIDE_PANEL:"supabase-ui-api-side-panel",UI_PREVIEW_CLS:"supabase-ui-cls",UI_PREVIEW_INLINE_EDITOR:"supabase-ui-preview-inline-editor",UI_PREVIEW_UNIFIED_LOGS:"supabase-ui-preview-unified-logs",UI_ONBOARDING_NEW_PAGE_SHOWN:"supabase-ui-onboarding-new-page-shown",UI_PREVIEW_REALTIME_SETTINGS:"supabase-ui-realtime-settings",UI_PREVIEW_BRANCHING_2_0:"supabase-ui-branching-2-0",UI_PREVIEW_ADVISOR_RULES:"supabase-ui-advisor-rules",NEW_LAYOUT_NOTICE_ACKNOWLEDGED:"new-layout-notice-acknowledge",TABS_INTERFACE_ACKNOWLEDGED:"tabs-interface-acknowledge",TERMS_OF_SERVICE_ACKNOWLEDGED:"terms-of-service-acknowledged",AI_ASSISTANT_MCP_OPT_IN:"ai-assistant-mcp-opt-in",DASHBOARD_HISTORY:e=>"dashboard-history-".concat(e),STORAGE_PREFERENCE:e=>"storage-explorer-".concat(e),SQL_EDITOR_INTELLISENSE:"supabase_sql-editor-intellisense-enabled",SQL_EDITOR_SPLIT_SIZE:"supabase_sql-editor-split-size",SQL_EDITOR_AI_SCHEMA:e=>"supabase_sql-editor-ai-schema-enabled-".concat(e),SQL_EDITOR_AI_OPEN:"supabase_sql-editor-ai-open",SQL_EDITOR_LAST_SELECTED_DB:e=>"sql-editor-last-selected-db-".concat(e),SQL_EDITOR_SQL_BLOCK_ACKNOWLEDGED:e=>"sql-editor-sql-block-acknowledged-".concat(e),SQL_EDITOR_SECTION_STATE:e=>"sql-editor-section-state-".concat(e),SQL_EDITOR_SORT:e=>"sql-editor-sort-".concat(e),LOG_EXPLORER_SPLIT_SIZE:"supabase_log-explorer-split-size",GRAPHIQL_RLS_BYPASS_WARNING:"graphiql-rls-bypass-warning-dismissed",CLS_DIFF_WARNING:"cls-diff-warning-dismissed",CLS_SELECT_STAR_WARNING:"cls-select-star-warning-dismissed",QUERY_PERF_SHOW_BOTTOM_SECTION:"supabase-query-perf-show-bottom-section",ACCOUNT_DELETION_REQUEST:"supabase-account-deletion-request",SENTRY_USER_ID:"supabase-sentry-user-id",LAST_SIGN_IN_METHOD:"supabase-last-sign-in-method",LAST_SELECTED_SCHEMA:e=>"last-selected-schema-".concat(e),SCHEMA_VISUALIZER_POSITIONS:(e,t)=>"schema-visualizer-positions-".concat(e,"-").concat(t),EXPAND_NAVIGATION_PANEL:"supabase-expand-navigation-panel",GITHUB_AUTHORIZATION_STATE:"supabase-github-authorization-state",FLY_POSTGRES_DEPRECATION_WARNING:"fly-postgres-deprecation-warning-dismissed",API_KEYS_FEEDBACK_DISMISSED:e=>"supabase-api-keys-feedback-dismissed-".concat(e),MIDDLEWARE_OUTAGE_BANNER:"middleware-outage-banner-2025-05-16",AUTH_USERS_COLUMNS_CONFIGURATION:e=>"supabase-auth-users-columns-".concat(e),REPORT_DATERANGE:"supabase-report-daterange",API_KEYS_VIEW:e=>"supabase-api-keys-view-".concat(e),LAST_VISITED_LOGS_PAGE:"supabase-last-visited-logs-page",LAST_VISITED_ORGANIZATION:"last-visited-organization",USER_IMPERSONATION_SELECTOR_PREVIOUS_SEARCHES:e=>"user-impersonation-selector-previous-searches-".concat(e),TELEMETRY_CONSENT:"supabase-consent-ph",TELEMETRY_DATA:"supabase-telemetry-data",SAVED_ORG:"docs.ui.user.selected.org",SAVED_PROJECT:"docs.ui.user.selected.project",SAVED_BRANCH:"docs.ui.user.selected.branch",HIDE_PROMO_TOAST:"supabase-hide-promo-toast-lw15-ticket",BLOG_VIEW:"supabase-blog-view"},a=["graphiql:theme","theme","supabaseDarkMode","supabase.dashboard.auth.debug","supabase.dashboard.auth.navigatorLock.disabled",r.TELEMETRY_CONSENT,r.UI_PREVIEW_API_SIDE_PANEL,r.UI_PREVIEW_INLINE_EDITOR,r.UI_PREVIEW_CLS,r.UI_PREVIEW_UNIFIED_LOGS,r.LAST_SIGN_IN_METHOD,r.HIDE_PROMO_TOAST,r.BLOG_VIEW,r.AI_ASSISTANT_MCP_OPT_IN,r.UI_PREVIEW_REALTIME_SETTINGS,r.UI_PREVIEW_BRANCHING_2_0];function s(){for(let e in localStorage)a.includes(e)||localStorage.removeItem(e)}},35484:(e,t,n)=>{"use strict";n.d(t,{cn:()=>s});var r=n(47323),a=n(12675);function s(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,a.Q)((0,r.$)(t))}},39346:(e,t,n)=>{"use strict";n.r(t),n.d(t,{PageTelemetry:()=>I,TelemetryTagManager:()=>_,handlePageLeaveTelemetry:()=>v,handlePageTelemetry:()=>S,handleResetTelemetry:()=>A,sendTelemetryEvent:()=>T,sendTelemetryIdentify:()=>y,useTelemetryIdentify:()=>b});var r=n(31710),a=n(31076),s=n(67468),o=n(50497),l=n(54990),i=n(68493),c=n(42883),u=n(25949),d=n(10287),p=n(12287),g=n(22847),h=n(21330),f=n(43701),m=n(59632);let{TELEMETRY_DATA:E}=d.rS,_=()=>{if(d.pe&&1)return(0,r.jsx)(o.default,{id:"consent",strategy:"afterInteractive",dangerouslySetInnerHTML:{__html:"(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],j=d.createElement(s);j.async=true;j.src=\"https://ss.supabase.com/4icgbaujh.js?\"+i;f.parentNode.insertBefore(j,f);})(window,document,'script','dataLayer','60a389s=aWQ9R1RNLVdDVlJMTU43&page=2');"}})};function S(e,t,n,r){return(0,g.b)("".concat((0,h.ensurePlatformSuffix)(e),"/telemetry/page"),void 0!==r?{feature_flags:n,...r}:{...(0,m.J)(t),feature_flags:n},{headers:{Version:"2"}})}function v(e,t,n){var r;return(0,g.b)("".concat((0,h.ensurePlatformSuffix)(e),"/telemetry/page-leave"),{body:{pathname:t,page_url:h.isBrowser?window.location.href:"",page_title:h.isBrowser?null==(r=document)?void 0:r.title:"",feature_flags:n}})}let I=e=>{var t,n,r;let{API_URL:o,hasAcceptedConsent:c,enabled:u=!0}=e,d=(0,a.useRouter)(),g=null==d?void 0:d.pathname,m=(0,s.usePathname)(),_=(0,p.useFeatureFlags)(),v="undefined"!=typeof document?null==(t=document)?void 0:t.title:"",I="undefined"!=typeof document?null==(n=document)?void 0:n.referrer:"";(0,f.useTelemetryCookie)({hasAcceptedConsent:c,title:v,referrer:I});let T=null!=(r=null!=g?g:m)?r:h.isBrowser?window.location.pathname:void 0,y=(0,i.A)(T),A=(0,i.A)(_.posthog),w=(0,l.useCallback)(()=>u&&c?S(o,y.current,A.current).catch(e=>{console.error("Problem sending telemetry page:",e)}):Promise.resolve(),[o,u,c]),R=(0,l.useCallback)(()=>u&&c?S(o,y.current,A.current).catch(e=>{console.error("Problem sending telemetry page-leave:",e)}):Promise.resolve(),[o,u,c]),C=(0,l.useRef)(!1);return(0,l.useEffect)(()=>{var e;if((null==(e=null==d?void 0:d.isReady)||e)&&c&&_.hasLoaded&&!C.current){let e=document.cookie.split(";").find(e=>e.trim().startsWith(E));if(e)try{let t=e.split("=")[1],n=JSON.parse(decodeURIComponent(t));S(o,y.current,A.current,n),document.cookie="".concat(E,"=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/")}catch(e){console.error("Invalid telemetry data:",e)}else S(o,y.current,A.current);C.current=!0}},[null==d?void 0:d.isReady,c,_.hasLoaded]),(0,l.useEffect)(()=>{if(null!==d)return d.events.on("routeChangeComplete",e),()=>{d.events.off("routeChangeComplete",e)};function e(){C.current&&w()}},[d]),(0,l.useEffect)(()=>{null===d&&m&&!C.current&&w()},[m,d,w]),(0,l.useEffect)(()=>{if(!u)return;let e=()=>R();return window.addEventListener("beforeunload",e),()=>window.removeEventListener("beforeunload",e)},[u,R]),b(o),null};function T(e,t,n){var r,a;if(!(0,u.up)())return;let s={...(0,m.J)(n),action:t.action,custom_properties:"properties"in t?t.properties:{},groups:"groups"in t?{...t.groups}:{}};return(null==(r=s.groups)?void 0:r.project)==="Unknown"&&(delete s.groups.project,(null==(a=s.groups)?void 0:a.organization)==="Unknown"&&delete s.groups),(0,g.b)("".concat((0,h.ensurePlatformSuffix)(e),"/telemetry/event"),s,{headers:{Version:"2"}})}function y(e,t){return(0,u.up)()?(0,g.b)("".concat((0,h.ensurePlatformSuffix)(e),"/telemetry/identify"),t,{headers:{Version:"2"}}):Promise.resolve()}function b(e){let t=(0,c.useUser)();(0,l.useEffect)(()=>{(null==t?void 0:t.id)&&y(e,{user_id:t.id})},[e,null==t?void 0:t.id])}function A(e){return(0,g.b)("".concat(e,"/telemetry/reset"),{})}},42883:(e,t,n)=>{"use strict";n.r(t),n.d(t,{AuthContext:()=>i,AuthProvider:()=>c,getAccessToken:()=>v,logOut:()=>_,signOut:()=>E,useAuth:()=>u,useAuthError:()=>f,useIsLoggedIn:()=>h,useIsMFAEnabled:()=>m,useIsUserLoading:()=>g,useSession:()=>d,useUser:()=>p});var r=n(31710),a=n(54990),s=n(35434),o=n(27415);let l={access_token:void 0,expires_at:0,expires_in:0,refresh_token:"",token_type:"",user:{aud:"",app_metadata:{},confirmed_at:"",created_at:"",email:"",email_confirmed_at:"",id:"",identities:[],last_signed_in_at:"",phone:"",role:"",updated_at:"",user_metadata:{}}},i=(0,a.createContext)({session:null,error:null,isLoading:!0,refreshSession:()=>Promise.resolve(null)}),c=e=>{let{alwaysLoggedIn:t,children:n}=e,[s,c]=(0,a.useState)({session:null,error:null,isLoading:!0});(0,a.useEffect)(()=>{let e=!0;return o.uw.initialize().then(t=>{let{error:n}=t;e&&null!==n&&c(e=>({...e,error:n}))}),()=>{e=!1}},[]),(0,a.useEffect)(()=>{let{data:{subscription:e}}=o.uw.onAuthStateChange((e,t)=>{c(e=>({session:t,error:null!==t?null:e.error,isLoading:!1}))});return e.unsubscribe},[]);let u=(0,a.useCallback)(async()=>{let{data:{session:e}}=await o.uw.refreshSession();return e},[]),d=(0,a.useMemo)(()=>t?{session:l,error:null,isLoading:!1,refreshSession:u}:{...s,refreshSession:u},[s,u]);return(0,r.jsx)(i.Provider,{value:d,children:n})},u=()=>(0,a.useContext)(i),d=()=>u().session,p=()=>{var e,t;return null!=(t=null==(e=d())?void 0:e.user)?t:null},g=()=>u().isLoading,h=()=>null!==p(),f=()=>u().error,m=()=>{let e=p();return null!==e&&e.factors&&e.factors.length>0},E=async()=>await o.uw.signOut(),_=async()=>{await E(),(0,s.s)()},S=null;async function v(){let e=null!=S&&!!S.expires_at&&S.expires_at-Math.ceil(Date.now()/1e3)<30;if(!S||e){let{data:{session:e},error:t}=await o.uw.getSession();if(t)throw t;return null==e?void 0:e.access_token}return S.access_token}o.uw.onAuthStateChange((e,t)=>{S=t})},43701:(e,t,n)=>{"use strict";n.r(t),n.d(t,{DocsSearchResultType:()=>v,useAnchorObserver:()=>a,useBreakpoint:()=>i,useConstant:()=>c,useCopy:()=>u,useDebounce:()=>g,useDocsSearch:()=>y,useEffectEvent:()=>b,useIsomorphicLayoutEffect:()=>A,useOnChange:()=>w,useParams:()=>C,useSearchParamsShallow:()=>x,useTelemetryCookie:()=>L,useThemeSandbox:()=>P});var r=n(54990);function a(e,t){let[n,a]=(0,r.useState)([]);return(0,r.useEffect)(()=>{let n=[],r=new IntersectionObserver(e=>{for(let t of e)t.isIntersecting&&!n.includes(t.target.id)?n=[...n,t.target.id]:!t.isIntersecting&&n.includes(t.target.id)&&(n=n.filter(e=>e!==t.target.id));n.length>0&&a(n)},{rootMargin:t?"-80px 0% -70% 0%":"-20px 0% -40% 0%",threshold:1});function s(){let n=document.scrollingElement;n&&(0===n.scrollTop&&t?a(e.slice(0,1)):n.scrollTop+n.clientHeight>=n.scrollHeight-6&&a(n=>n.length>0&&!t?e.slice(e.indexOf(n[0])):e.slice(-1)))}for(let t of e){let e=document.getElementById(t);e&&r.observe(e)}return s(),window.addEventListener("scroll",s),()=>{window.removeEventListener("scroll",s),r.disconnect()}},[t,e]),t?n.slice(0,1):n}var s=n(29657),o=n(93816);let l={sm:639,md:767,lg:1023,xl:1027,"2xl":1535};function i(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"lg",[t,n]=(0,r.useState)(!1),{width:a}=(0,s.A)(),i="string"==typeof e?l[e]:e;return(0,o.A)(()=>{a<=i?n(!0):n(!1)},[a]),t}function c(e){let t=(0,r.useRef)();return t.current||(t.current={v:e()}),t.current.v}function u(){let[e,t]=(0,r.useState)(!1);return{copied:e,handleCopy:function(){t(!0),setTimeout(()=>{t(!1)},1e3)}}}var d=n(79559),p=n.n(d);let g=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3,n=(0,r.useRef)();return(0,r.useEffect)(()=>{n.current=e},[e]),(0,r.useMemo)(()=>p()(()=>{var e;null==(e=n.current)||e.call(n)},t),[])};var h=n(77645),f=n.n(h),m=n(8424),E=n.n(m);let _="https://kooojyxekpoqgkqekszi.supabase.co",S="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imtvb29qeXhla3BvcWdrcWVrc3ppIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDEzNjIxMDUsImV4cCI6MjA1NjkzODEwNX0.TcBj65rq0m_fk_c4i0oerF9CSyrTVATe-z23WJccWeY";var v=function(e){return e.Markdown="markdown",e.Reference="reference",e.Integration="partner-integration",e.GithubDiscussion="github-discussions",e}(v||{});function I(e){if("object"!=typeof e||null===e||!("id"in e&&"path"in e&&"type"in e&&"title"in e))return null;let t=[];return"headings"in e&&Array.isArray(e.headings)&&"slugs"in e&&Array.isArray(e.slugs)&&e.headings.length===e.slugs.length&&e.headings.forEach((n,r)=>{let a=e.slugs[r];n&&a&&t.push({heading:n,slug:a})}),{id:e.id,path:e.path,type:e.type,title:e.title,subtitle:"subtitle"in e?e.subtitle:null,description:"description"in e?e.description:null,sections:t}}function T(e,t){if(e.key>t.key)return e;switch(t.type){case"resultsReturned":let n=2===t.sourcesLoaded,r=f()(t.results.map(I)),a="partialResults"===e.status&&e.key===t.key?E()(e.results.concat(r),e=>e.id):r;if(!a.length)return n?{status:"noResults",key:t.key}:{status:"loading",key:t.key,staleResults:"results"in e?e.results:"staleResults"in e?e.staleResults:[]};return n?{status:"fullResults",key:t.key,results:a}:{status:"partialResults",key:t.key,results:a};case"newSearchDispatched":return{status:"loading",key:t.key,staleResults:"results"in e?e.results:"staleResults"in e?e.staleResults:[]};case"reset":return{status:"initial",key:t.key};case"errored":if(2===t.sourcesLoaded&&!("results"in e))return{status:"error",key:t.key,message:t.message};return e;default:return e}}let y=()=>{let[e,t]=(0,r.useReducer)(T,{status:"initial",key:0}),n=(0,r.useRef)(0),a=(0,r.useCallback)(async e=>{n.current+=1;let r=n.current;t({type:"newSearchDispatched",key:r});let a=0;fetch("".concat(_,"/rest/v1/rpc/docs_search_fts"),{method:"POST",headers:{"content-type":"application/json",...S&&{apikey:S,authorization:"Bearer ".concat(S)}},body:JSON.stringify({query:e.trim()})}).then(e=>e.json()).then(e=>{if(a+=1,Array.isArray(e))t({type:"resultsReturned",key:r,sourcesLoaded:a,results:e});else{var n;t({type:"errored",key:r,sourcesLoaded:a,message:null!=(n=null==e?void 0:e.message)?n:""})}}).catch(e=>{a+=1,console.error("[ERROR] Error fetching Full Text Search results: ".concat(e)),t({type:"errored",key:r,sourcesLoaded:a,message:""})}),fetch("".concat(_).concat("/functions/v1/","search-embeddings"),{method:"POST",body:JSON.stringify({query:e})}).then(e=>e.json()).then(e=>{if(!Array.isArray(e))throw Error("didn't get expected results array");t({type:"resultsReturned",key:r,sourcesLoaded:a+=1,results:e})}).catch(e=>{var n;t({type:"errored",key:r,sourcesLoaded:a+=1,message:null!=(n=e.message)?n:""})})},[]),s=(0,r.useMemo)(()=>p()(a,150),[a]);return{searchState:e,handleDocsSearch:a,handleDocsSearchDebounced:s,resetSearch:(0,r.useCallback)(()=>{n.current+=1,t({type:"reset",key:n.current})},[])}};function b(e){let t=(0,r.useRef)(e);return t.current=e,(0,r.useCallback)(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.current(...n)},[])}let A="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function w(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function e(t,n){return Array.isArray(t)&&Array.isArray(n)?n.length!==t.length||t.some((t,r)=>e(t,n[r])):t!==n},[a,s]=(0,r.useState)(e);n(a,e)&&(t(e,a),s(e))}var R=n(31076);function C(){let e=(0,R.useRouter)(),t=null==e?void 0:e.query,n={...t};return Object.keys(n).forEach(e=>{let t=e.includes("-")?e.split("-").map((e,t)=>0===t?e:e.charAt(0).toUpperCase()+e.slice(1)).join(""):e;t!==e&&(n[t]=n[e],delete n[e])}),(0,r.useMemo)(()=>Object.fromEntries(Object.entries(n).map(e=>{let[t,n]=e;return Array.isArray(n)?[t,n[0]]:[t,n]})),[t])}let x=()=>{let e="supabase.events.packages.common.useSearchParamsShallow",t=(0,r.useId)(),n=(0,r.useRef)(),a=(0,r.useCallback)((r,a)=>(clearTimeout(n.current),"ext"===a.target&&(n.current=setTimeout(()=>{document.dispatchEvent(new CustomEvent(e,{detail:{id:t}}))})),a.newParams),[t]);(0,r.useEffect)(()=>()=>clearTimeout(n.current),[]);let[s,o]=(0,r.useReducer)(a,void 0,()=>new URLSearchParams);(0,r.useEffect)(()=>{let n=e=>{e.detail.id!==t&&o({target:"int",newParams:new URLSearchParams(window.location.search)})};return document.addEventListener(e,n),()=>document.removeEventListener(e,n)},[t]),(0,r.useEffect)(()=>{o({target:"int",newParams:new URLSearchParams(window.location.search)})},[]);let l=(0,r.useCallback)(e=>s.has(e),[s]),i=(0,r.useCallback)(e=>s.get(e),[s]),c=(0,r.useCallback)(e=>s.getAll(e),[s]),u=(0,r.useCallback)((e,t)=>{let n=new URL(window.location.href);n.searchParams.set(e,t),window.history.replaceState(null,"",n),o({target:"ext",newParams:n.searchParams})},[]),d=(0,r.useCallback)((e,t)=>{let n=new URL(window.location.href);n.searchParams.append(e,t),window.history.replaceState(null,"",n),o({target:"ext",newParams:n.searchParams})},[]),p=(0,r.useCallback)(e=>{let t=new URL(window.location.href);t.searchParams.delete(e),window.history.replaceState(null,"",t),o({target:"ext",newParams:t.searchParams})},[]);return(0,r.useMemo)(()=>({has:l,get:i,getAll:c,append:d,set:u,delete:p}),[l,i,c,d,u,p])};var k=n(10287),O=n(59632);function L(e){let{hasAcceptedConsent:t,title:n,referrer:a}=e,s=(0,R.useRouter)(),o=k.rS.TELEMETRY_DATA;(0,r.useEffect)(()=>{if(!(null==s?void 0:s.isReady))return;let e=document.cookie.split(";"),n=k.db?"path=/; domain=supabase.com":"path=/";if(e.find(e=>e.trim().startsWith(o)))return;let r=(0,O.J)(s.pathname);if(!t){let e=encodeURIComponent(JSON.stringify(r));document.cookie="".concat(o,"=").concat(e,"; ").concat(n)}},[t,null==s?void 0:s.isReady,o,n,null==s?void 0:s.pathname,a])}let N={"--brand-accent":"160deg 100% 50%","--brand-default":"159.9deg 100% 38.6%","--brand-600":"136deg 59.5% 70%","--brand-500":"160.4deg 100% 19.2%","--brand-400":"160.4deg 100% 9.6%","--brand-300":"158.4deg 100% 4.9%","--brand-200":"162deg 100% 2%","--border-stronger":"200deg 16.1% 22%","--border-strong":"200deg 16.5% 17.8%","--border-alternative":"200deg 16.4% 13.1%","--border-control":"200deg 10% 14%","--border-overlay":"200deg 20% 9%","--border-secondary":"200deg 21.6% 10%","--border-muted":"200deg 14.9% 9.2%","--border-default":"200deg 10% 11%","--background-muted":"200deg 9.1% 10.8%","--background-overlay-hover":"200deg 20% 11%","--background-overlay-default":"200deg 14.3% 6.9%","--background-surface-300":"200deg 20% 10%","--background-surface-200":"200deg 20% 7%","--background-surface-100":"200deg 20% 6%","--background-control":"200deg 9% 11%","--background-selection":"200deg 9.8% 10%","--background-alternative":"200deg 20% 2.9%","--background-default":"200deg 20% 4%","--foreground-muted":"200deg 10% 35%","--foreground-lighter":"200deg 8% 55%","--foreground-light":"200deg 5% 69%","--foreground-default":"200deg 0% 93%"},P=()=>{if(k.db)return null;let e=window.location.hash,t=localStorage.getItem("theme-sandbox"),a=e.includes("#theme-sandbox")||null!==t,[s,o]=(0,r.useState)(t?JSON.parse(t):N),l=document.querySelector(":root"),i=(e,t)=>{c(),o(n=>({...n,[e]:t}))},c=()=>{Object.entries(s).map(e=>{let[t,n]=e;return l.style.setProperty(t,n)}),localStorage.setItem("theme-sandbox",JSON.stringify(s))},u=async()=>{if(!a)return;let e=new(await n.e(8350).then(n.bind(n,68350))).GUI;e.width=500,Object.entries(N).map(t=>{var n;let[r,a]=t;if(!s[r])return localStorage.removeItem("theme-sandbox");let o=r.split("-")[2];return(null!=(n=e.__folders[o])?n:e.addFolder(o)).add(s,r).name(r).onChange(e=>{i(r,e)})});var t={"Apply Theme":function(){c()},"Exit Sandbox":function(){e.destroy()},"Reset localStorage":function(){localStorage.removeItem("theme-sandbox"),o(N)}};e.add(t,"Apply Theme"),e.add(t,"Reset localStorage"),e.add(t,"Exit Sandbox"),e.load};return(0,r.useEffect)(()=>{u()},[]),{themeConfig:s,handleSetThemeConfig:i,isSandbox:a}}},56631:(e,t,n)=>{"use strict";n.d(t,{BL:()=>a.BL,Bd:()=>o.isBrowser,Jd:()=>r.useUser,LZ:()=>i.PageTelemetry,OJ:()=>r.AuthProvider,Ot:()=>i.sendTelemetryEvent,YK:()=>a.YK,dv:()=>l.useBreakpoint,pe:()=>s.pe,rS:()=>s.rS});var r=n(42883),a=n(25949),s=n(10287);n(27415);var o=n(21330),l=n(43701);n(97626),n(66533);var i=n(39346);n(12287)},59632:(e,t,n)=>{"use strict";n.d(t,{J:()=>a});var r=n(21330);function a(e){var t,n,a;return{page_url:r.isBrowser?window.location.href:"",page_title:r.isBrowser?null==(t=document)?void 0:t.title:"",pathname:e||(r.isBrowser?window.location.pathname:""),ph:{referrer:r.isBrowser?null==(n=document)?void 0:n.referrer:"",language:null!=(a=navigator.language)?a:"en-US",user_agent:navigator.userAgent,search:r.isBrowser?window.location.search:"",viewport_height:r.isBrowser?window.innerHeight:0,viewport_width:r.isBrowser?window.innerWidth:0}}}},66533:(e,t,n)=>{"use strict";n.r(t),n.d(t,{ThemeProvider:()=>s});var r=n(31710),a=n(47375);function s(e){let{children:t,...n}=e;return(0,r.jsx)(a.N,{themes:["dark","light"],defaultTheme:"dark",...n,children:t})}},72385:()=>{},97626:(e,t,n)=>{"use strict";n.r(t),n.d(t,{DEFAULT_FAVICON_ROUTE:()=>i,DEFAULT_FAVICON_THEME_COLOR:()=>l,default:()=>c});var r=n(31710),a=n(36631),s=n.n(a),o=n(51415);let l="1E1E1E",i="/favicon",c=e=>{let{applicationName:t,route:n=i,themeColor:a=l,includeRssXmlFeed:c=!1,includeManifest:u=!1,includeMsApplicationConfig:d=!1}=e,{basePath:p}=(0,o.useRouter)();return(0,r.jsxs)(s(),{children:[(0,r.jsx)("link",{rel:"apple-touch-icon-precomposed",sizes:"57x57",href:"".concat(p).concat(n,"/apple-icon-57x57.png")}),(0,r.jsx)("link",{rel:"apple-touch-icon-precomposed",sizes:"60x60",href:"".concat(p).concat(n,"/apple-icon-60x60.png")}),(0,r.jsx)("link",{rel:"apple-touch-icon-precomposed",sizes:"72x72",href:"".concat(p).concat(n,"/apple-icon-72x72.png")}),(0,r.jsx)("link",{rel:"apple-touch-icon-precomposed",sizes:"76x76",href:"".concat(p).concat(n,"/apple-icon-76x76.png")}),(0,r.jsx)("link",{rel:"apple-touch-icon-precomposed",sizes:"114x114",href:"".concat(p).concat(n,"/apple-icon-114x114.png")}),(0,r.jsx)("link",{rel:"apple-touch-icon-precomposed",sizes:"120x120",href:"".concat(p).concat(n,"/apple-icon-120x120.png")}),(0,r.jsx)("link",{rel:"apple-touch-icon-precomposed",sizes:"144x144",href:"".concat(p).concat(n,"/apple-icon-144x144.png")}),(0,r.jsx)("link",{rel:"apple-touch-icon-precomposed",sizes:"152x152",href:"".concat(p).concat(n,"/apple-icon-152x152.png")}),(0,r.jsx)("link",{rel:"icon",type:"image/png",href:"".concat(p).concat(n,"/favicon-16x16.png"),sizes:"16x16"}),(0,r.jsx)("link",{rel:"icon",type:"image/png",href:"".concat(p).concat(n,"/favicon-32x32.png"),sizes:"32x32"}),(0,r.jsx)("link",{rel:"icon",type:"image/png",href:"".concat(p).concat(n,"/favicon-48x48.png"),sizes:"48x48"}),(0,r.jsx)("link",{rel:"icon",type:"image/png",href:"".concat(p).concat(n,"/favicon-96x96.png"),sizes:"96x96"}),(0,r.jsx)("link",{rel:"icon",type:"image/png",href:"".concat(p).concat(n,"/favicon-128.png"),sizes:"128x128"}),(0,r.jsx)("link",{rel:"icon",type:"image/png",href:"".concat(p).concat(n,"/favicon-180x180.png"),sizes:"180x180"}),(0,r.jsx)("link",{rel:"icon",type:"image/png",href:"".concat(p).concat(n,"/favicon-196x196.png"),sizes:"196x196"}),(0,r.jsx)("meta",{name:"application-name",content:null!=t?t:"&nbsp;"}),(0,r.jsx)("meta",{name:"msapplication-TileColor",content:"#".concat(a)}),(0,r.jsx)("meta",{name:"msapplication-TileImage",content:"".concat(p).concat(n,"/mstile-144x144.png")}),(0,r.jsx)("meta",{name:"msapplication-square70x70logo",content:"".concat(p).concat(n,"/mstile-70x70.png")}),(0,r.jsx)("meta",{name:"msapplication-square150x150logo",content:"".concat(p).concat(n,"/mstile-150x150.png")}),(0,r.jsx)("meta",{name:"msapplication-wide310x150logo",content:"".concat(p).concat(n,"/mstile-310x150.png")}),(0,r.jsx)("meta",{name:"msapplication-square310x310logo",content:"".concat(p).concat(n,"/mstile-310x310.png")}),(0,r.jsx)("meta",{name:"theme-color",content:"#".concat(a)}),(0,r.jsx)("link",{rel:"shortcut icon",href:"".concat(p).concat(n,"/favicon.ico")}),(0,r.jsx)("link",{rel:"icon",type:"image/x-icon",href:"".concat(p).concat(n,"/favicon.ico")}),(0,r.jsx)("link",{rel:"apple-touch-icon",href:"".concat(p).concat(n,"/favicon.ico")}),c&&(0,r.jsx)("link",{rel:"alternate",type:"application/rss+xml",href:"".concat(p,"/feed.xml")}),u&&(0,r.jsx)("link",{rel:"manifest",href:"".concat(p).concat(n,"/manifest.json")}),d&&(0,r.jsx)("meta",{name:"msapplication-config",content:"".concat(p).concat(n,"/browserconfig.xml")})]})}}}]);