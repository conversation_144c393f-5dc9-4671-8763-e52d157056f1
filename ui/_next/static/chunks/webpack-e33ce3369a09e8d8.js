(()=>{"use strict";var e={},t={};function r(a){var o=t[a];if(void 0!==o)return o.exports;var c=t[a]={id:a,loaded:!1,exports:{}},d=!0;try{e[a].call(c.exports,c,c.exports,r),d=!1}finally{d&&delete t[a]}return c.loaded=!0,c.exports}r.m=e,(()=>{var e=[];r.O=(t,a,o,c)=>{if(a){c=c||0;for(var d=e.length;d>0&&e[d-1][2]>c;d--)e[d]=e[d-1];e[d]=[a,o,c];return}for(var n=1/0,d=0;d<e.length;d++){for(var[a,o,c]=e[d],f=!0,i=0;i<a.length;i++)(!1&c||n>=c)&&Object.keys(r.O).every(e=>r.O[e](a[i]))?a.splice(i--,1):(f=!1,c<n&&(n=c));if(f){e.splice(d--,1);var s=o();void 0!==s&&(t=s)}}return t}})(),r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;r.t=function(a,o){if(1&o&&(a=this(a)),8&o||"object"==typeof a&&a&&(4&o&&a.__esModule||16&o&&"function"==typeof a.then))return a;var c=Object.create(null);r.r(c);var d={};e=e||[null,t({}),t([]),t(t)];for(var n=2&o&&a;"object"==typeof n&&!~e.indexOf(n);n=t(n))Object.getOwnPropertyNames(n).forEach(e=>d[e]=()=>a[e]);return d.default=()=>a,r.d(c,d),c}})(),r.d=(e,t)=>{for(var a in t)r.o(t,a)&&!r.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},r.f={},r.e=e=>Promise.all(Object.keys(r.f).reduce((t,a)=>(r.f[a](e,t),t),[])),r.u=e=>2551===e?"static/chunks/2551-e06ae08de67e936b.js":9711===e?"static/chunks/9711-5bae21198dee7ddc.js":"static/chunks/"+(({3245:"54b60c1a",9892:"877f94c9"})[e]||e)+"."+({660:"ca06bc4e7f7d2b5b",1121:"5f16f06acbd579b8",1141:"a70fe5a6dc28ec06",2258:"b7f3295819328a16",2323:"964b331f03cca194",2519:"1c6381d4480dd768",2688:"a943ca5af13147ec",3245:"43bda671d0e0de4b",3384:"3a62155b6c8f2962",3455:"3ad25aff28e25975",3475:"291bffc15513e3ef",3605:"02cce29511c56d4a",3696:"edd182e551768f93",3704:"e28713a4b20a7511",3779:"d9ae7c03929ca7a4",4520:"e762ef77d68edcea",4552:"e5b163936e236b96",4907:"1634ab7c3963df1f",4994:"1b2f419ff5943c5c",6117:"873ee28ce06eab62",6383:"4af1c41e16cfe13c",6662:"dd40593aa41f7bb6",7015:"928d4655c15841d2",7705:"2ec91564973b52fc",8216:"725f1ed8955550ff",8350:"8f8e6a5526cee8f0",8402:"eb33001f51529cc3",8465:"d9c9d2a2881cd511",8590:"67d0de71cd8766b7",8764:"79df3d4e58c387b2",9247:"2473cf0aa25705b0",9271:"669bceed8a22fe69",9496:"51b698c92ed73d80",9498:"6d5a5d083aa9b777",9875:"0f9ffd6c8e5721c1",9892:"4ec90e480d8aacb6",9914:"871a1922ea7f1b15",9929:"34b740ea1f5e851d"})[e]+".js",r.miniCssF=e=>{},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.hmd=e=>((e=Object.create(e)).children||(e.children=[]),Object.defineProperty(e,"exports",{enumerable:!0,set:()=>{throw Error("ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: "+e.id)}}),e),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="_N_E:";r.l=(a,o,c,d)=>{if(e[a])return void e[a].push(o);if(void 0!==c)for(var n,f,i=document.getElementsByTagName("script"),s=0;s<i.length;s++){var u=i[s];if(u.getAttribute("src")==a||u.getAttribute("data-webpack")==t+c){n=u;break}}n||(f=!0,(n=document.createElement("script")).charset="utf-8",n.timeout=120,r.nc&&n.setAttribute("nonce",r.nc),n.setAttribute("data-webpack",t+c),n.src=r.tu(a)),e[a]=[o];var b=(t,r)=>{n.onerror=n.onload=null,clearTimeout(l);var o=e[a];if(delete e[a],n.parentNode&&n.parentNode.removeChild(n),o&&o.forEach(e=>e(r)),t)return t(r)},l=setTimeout(b.bind(null,void 0,{type:"timeout",target:n}),12e4);n.onerror=b.bind(null,n.onerror),n.onload=b.bind(null,n.onload),f&&document.head.appendChild(n)}})(),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{var e;r.tt=()=>(void 0===e&&(e={createScriptURL:e=>e},"undefined"!=typeof trustedTypes&&trustedTypes.createPolicy&&(e=trustedTypes.createPolicy("nextjs#bundler",e))),e)})(),r.tu=e=>r.tt().createScriptURL(e),r.p="/ui/_next/",(()=>{var e={8068:0,152:0,2006:0,917:0,2486:0,8081:0};r.f.j=(t,a)=>{var o=r.o(e,t)?e[t]:void 0;if(0!==o)if(o)a.push(o[2]);else if(/^(80(68|81)|152|2006|2486|917)$/.test(t))e[t]=0;else{var c=new Promise((r,a)=>o=e[t]=[r,a]);a.push(o[2]=c);var d=r.p+r.u(t),n=Error();r.l(d,a=>{if(r.o(e,t)&&(0!==(o=e[t])&&(e[t]=void 0),o)){var c=a&&("load"===a.type?"missing":a.type),d=a&&a.target&&a.target.src;n.message="Loading chunk "+t+" failed.\n("+c+": "+d+")",n.name="ChunkLoadError",n.type=c,n.request=d,o[1](n)}},"chunk-"+t,t)}},r.O.j=t=>0===e[t];var t=(t,a)=>{var o,c,[d,n,f]=a,i=0;if(d.some(t=>0!==e[t])){for(o in n)r.o(n,o)&&(r.m[o]=n[o]);if(f)var s=f(r)}for(t&&t(a);i<d.length;i++)c=d[i],r.o(e,c)&&e[c]&&e[c][0](),e[c]=0;return r.O(s)},a=self.webpackChunk_N_E=self.webpackChunk_N_E||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))})(),r.nc=void 0})();