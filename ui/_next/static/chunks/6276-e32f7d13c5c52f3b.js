"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6276],{4923:(e,t,r)=>{r.d(t,{c:()=>l});var n=r(54990);function l(e){let t=(0,n.useRef)(e);return(0,n.useEffect)(()=>{t.current=e}),(0,n.useMemo)(()=>(...e)=>{var r;return null==(r=t.current)?void 0:r.call(t,...e)},[])}},17423:(e,t,r)=>{r.d(t,{X:()=>u});var n=r(54990),l=r(57873);function u(e){let[t,r]=(0,n.useState)(void 0);return(0,l.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,l;if(!Array.isArray(t)||!t.length)return;let u=t[0];if("borderBoxSize"in u){let e=u.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,l=t.blockSize}else n=e.offsetWidth,l=e.offsetHeight;r({width:n,height:l})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},19598:(e,t,r)=>{r.d(t,{A:()=>n});function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}},32247:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(54990);function l(e){let t=(0,n.useRef)({value:e,previous:e});return(0,n.useMemo)(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},32505:(e,t,r)=>{r.d(t,{A:()=>u,q:()=>l});var n=r(54990);function l(e,t){let r=(0,n.createContext)(t);function l(e){let{children:t,...l}=e,u=(0,n.useMemo)(()=>l,Object.values(l));return(0,n.createElement)(r.Provider,{value:u},t)}return l.displayName=e+"Provider",[l,function(l){let u=(0,n.useContext)(r);if(u)return u;if(void 0!==t)return t;throw Error(`\`${l}\` must be used within \`${e}\``)}]}function u(e,t=[]){let r=[],l=()=>{let t=r.map(e=>(0,n.createContext)(e));return function(r){let l=(null==r?void 0:r[e])||t;return(0,n.useMemo)(()=>({[`__scope${e}`]:{...r,[e]:l}}),[r,l])}};return l.scopeName=e,[function(t,l){let u=(0,n.createContext)(l),o=r.length;function i(t){let{scope:r,children:l,...i}=t,a=(null==r?void 0:r[e][o])||u,c=(0,n.useMemo)(()=>i,Object.values(i));return(0,n.createElement)(a.Provider,{value:c},l)}return r=[...r,l],i.displayName=t+"Provider",[i,function(r,i){let a=(null==i?void 0:i[e][o])||u,c=(0,n.useContext)(a);if(c)return c;if(void 0!==l)return l;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=r.reduce((t,{useScope:r,scopeName:n})=>{let l=r(e)[`__scope${n}`];return{...t,...l}},{});return(0,n.useMemo)(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return r.scopeName=t.scopeName,r}(l,...t)]}},51439:(e,t,r)=>{r.d(t,{s:()=>u,t:()=>l});var n=r(54990);function l(...e){return t=>e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}function u(...e){return(0,n.useCallback)(l(...e),e)}},57873:(e,t,r)=>{r.d(t,{N:()=>l});var n=r(54990);let l=(null==globalThis?void 0:globalThis.document)?n.useLayoutEffect:()=>{}},70878:(e,t,r)=>{r.d(t,{hO:()=>a,sG:()=>i});var n=r(19598),l=r(54990),u=r(779),o=r(82010);let i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=(0,l.forwardRef)((e,r)=>{let{asChild:u,...i}=e,a=u?o.DX:t;return(0,l.useEffect)(()=>{window[Symbol.for("radix-ui")]=!0},[]),(0,l.createElement)(a,(0,n.A)({},i,{ref:r}))});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function a(e,t){e&&(0,u.flushSync)(()=>e.dispatchEvent(t))}},73887:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(null==e||e(n),!1===r||!n.defaultPrevented)return null==t?void 0:t(n)}}},82010:(e,t,r)=>{r.d(t,{DX:()=>o,xV:()=>a});var n=r(19598),l=r(54990),u=r(51439);let o=(0,l.forwardRef)((e,t)=>{let{children:r,...u}=e,o=l.Children.toArray(r),a=o.find(c);if(a){let e=a.props.children,r=o.map(t=>t!==a?t:l.Children.count(e)>1?l.Children.only(null):(0,l.isValidElement)(e)?e.props.children:null);return(0,l.createElement)(i,(0,n.A)({},u,{ref:t}),(0,l.isValidElement)(e)?(0,l.cloneElement)(e,void 0,r):null)}return(0,l.createElement)(i,(0,n.A)({},u,{ref:t}),r)});o.displayName="Slot";let i=(0,l.forwardRef)((e,t)=>{let{children:r,...n}=e;return(0,l.isValidElement)(r)?(0,l.cloneElement)(r,{...function(e,t){let r={...t};for(let n in t){let l=e[n],u=t[n];/^on[A-Z]/.test(n)?l&&u?r[n]=(...e)=>{u(...e),l(...e)}:l&&(r[n]=l):"style"===n?r[n]={...l,...u}:"className"===n&&(r[n]=[l,u].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props),ref:t?(0,u.t)(t,r.ref):r.ref}):l.Children.count(r)>1?l.Children.only(null):null});i.displayName="SlotClone";let a=({children:e})=>(0,l.createElement)(l.Fragment,null,e);function c(e){return(0,l.isValidElement)(e)&&e.type===a}},98931:(e,t,r)=>{r.d(t,{i:()=>u});var n=r(54990),l=r(4923);function u({prop:e,defaultProp:t,onChange:r=()=>{}}){let[u,o]=function({defaultProp:e,onChange:t}){let r=(0,n.useState)(e),[u]=r,o=(0,n.useRef)(u),i=(0,l.c)(t);return(0,n.useEffect)(()=>{o.current!==u&&(i(u),o.current=u)},[u,o,i]),r}({defaultProp:t,onChange:r}),i=void 0!==e,a=i?e:u,c=(0,l.c)(r);return[a,(0,n.useCallback)(t=>{if(i){let r="function"==typeof t?t(e):t;r!==e&&c(r)}else o(t)},[i,e,o,c])]}}}]);