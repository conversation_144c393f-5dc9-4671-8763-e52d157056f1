"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3276],{63276:(e,t,r)=>{r.r(t),r.d(t,{Provider:()=>i,useAtom:()=>c,useAtomValue:()=>d,useSetAtom:()=>f,useStore:()=>u});var n=r(54990),l=r(86836);let o=(0,n.createContext)(void 0),u=e=>{let t=(0,n.useContext)(o);return(null==e?void 0:e.store)||t||(0,l.zp)()},i=e=>{let{children:t,store:r}=e,u=(0,n.useRef)();return r||u.current||(u.current=(0,l.y$)()),(0,n.createElement)(o.Provider,{value:r||u.current},t)},a=e=>"function"==typeof(null==e?void 0:e.then),s=n.use||(e=>{if("pending"===e.status)throw e;if("fulfilled"===e.status)return e.value;if("rejected"===e.status)throw e.reason;throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e});function d(e,t){let r=u(t),[[l,o,i],d]=(0,n.useReducer)(t=>{let n=r.get(e);return Object.is(t[0],n)&&t[1]===r&&t[2]===e?t:[n,r,e]},void 0,()=>[r.get(e),r,e]),f=l;(o!==r||i!==e)&&(d(),f=r.get(e));let c=null==t?void 0:t.delay;return(0,n.useEffect)(()=>{let t=r.sub(e,()=>{if("number"==typeof c)return void setTimeout(d,c);d()});return d(),t},[r,e,c]),(0,n.useDebugValue)(f),a(f)?s(f):f}function f(e,t){let r=u(t);return(0,n.useCallback)(function(){for(var t=arguments.length,n=Array(t),l=0;l<t;l++)n[l]=arguments[l];if(!("write"in e))throw Error("not writable atom");return r.set(e,...n)},[r,e])}function c(e,t){return[d(e,t),f(e,t)]}},86836:(e,t,r)=>{let n;r.d(t,{eU:()=>o,y$:()=>S,zp:()=>T});let l=0;function o(e,t){let r=`atom${++l}`,n={toString:()=>r};return"function"==typeof e?n.read=e:(n.init=e,n.read=u,n.write=i),t&&(n.write=t),n}function u(e){return e(this)}function i(e,t,r){return t(this,"function"==typeof r?r(e(this)):r)}let a=(e,t)=>e.unstable_is?e.unstable_is(t):t===e,s=e=>"init"in e,d=e=>!!e.write,f=new WeakMap,c=(e,t)=>{f.set(e,t),e.catch(()=>{}).finally(()=>f.delete(e))},h=(e,t)=>{let r=f.get(e);r&&(f.delete(e),r(t))},v=(e,t)=>{e.status="fulfilled",e.value=t},w=(e,t)=>{e.status="rejected",e.reason=t},g=e=>"function"==typeof(null==e?void 0:e.then),p=(e,t)=>!!e&&"v"in e&&"v"in t&&Object.is(e.v,t.v),_=(e,t)=>!!e&&"e"in e&&"e"in t&&Object.is(e.e,t.e),E=e=>!!e&&"v"in e&&e.v instanceof Promise,m=(e,t)=>"v"in e&&"v"in t&&e.v.orig&&e.v.orig===t.v.orig,b=e=>{if("e"in e)throw e.e;return e.v},y=()=>{let e,t,r=new WeakMap,n=new WeakMap,l=[],o=new WeakMap;e=new Set,t=new Set;let u=e=>r.get(e),i=(e,t)=>{t.d.forEach((t,r)=>{var n;if(!o.has(r)){let e=u(r);null==(n=l[l.length-1])||n.add(r),o.set(r,[e,new Set]),e&&i(r,e)}o.get(r)[1].add(e)})},f=(e,t)=>{var n;Object.freeze(t);let a=u(e);if(r.set(e,t),o.has(e)||(null==(n=l[l.length-1])||n.add(e),o.set(e,[a,new Set]),i(e,t)),E(a)){let e="v"in t?t.v instanceof Promise?t.v:Promise.resolve(t.v):Promise.reject(t.e);a.v!==e&&h(a.v,e)}},y=(e,t,r,n)=>{let l=new Map(n?t.d:null),o=!1;r.forEach((r,n)=>{!r&&a(e,n)&&(r=t),r?(l.set(n,r),t.d.get(n)!==r&&(o=!0)):console.warn("[Bug] atom state not found")}),(o||t.d.size!==l.size)&&(t.d=l)},S=(e,t,r,n)=>{let l=u(e),o={d:(null==l?void 0:l.d)||new Map,v:t};if(r&&y(e,o,r,n),p(l,o)&&l.d===o.d)return l;if(E(l)&&E(o)&&m(l,o))if(l.d===o.d)return l;else o.v=l.v;return f(e,o),o},T=(e,t,r,l)=>{if(g(t)){let o,i=()=>{let t=u(e);if(!E(t)||t.v!==a)return;let l=S(e,a,r);n.has(e)&&t.d!==l.d&&I(e,l,t.d)},a=new Promise((e,r)=>{let n=!1;t.then(t=>{n||(n=!0,v(a,t),e(t),i())},e=>{n||(n=!0,w(a,e),r(e),i())}),o=t=>{n||(n=!0,t.then(e=>v(a,e),e=>w(a,e)),e(t))}});return a.orig=t,a.status="pending",c(a,e=>{e&&o(e),null==l||l()}),S(e,a,r,!0)}return S(e,t,r)},k=(e,t,r)=>{let n=u(e),l={d:(null==n?void 0:n.d)||new Map,e:t};return(r&&y(e,l,r),_(n,l)&&n.d===l.d)?n:(f(e,l),l)},A=(e,t)=>{let r,l,o=u(e);if(!(null==t?void 0:t(e))&&o&&(n.has(e)||Array.from(o.d).every(([r,n])=>{if(r===e)return!0;let l=A(r,t);return l===n||p(l,n)})))return o;let i=new Map,f=!0;try{let n=e.read(r=>{if(a(e,r)){let e=u(r);if(e)return i.set(r,e),b(e);if(s(r))return i.set(r,void 0),r.init;throw Error("no atom init")}let n=A(r,t);return i.set(r,n),b(n)},{get signal(){return r||(r=new AbortController),r.signal},get setSelf(){return d(e)||console.warn("setSelf function cannot be used with read-only atom"),!l&&d(e)&&(l=(...t)=>{if(f&&console.warn("setSelf function cannot be called in sync"),!f)return j(e,...t)}),l}});return T(e,n,i,()=>null==r?void 0:r.abort())}catch(t){return k(e,t,i)}finally{f=!1}},O=e=>{let t=e=>{var t,r;let l=new Set(null==(t=n.get(e))?void 0:t.t);return null==(r=o.get(e))||r[1].forEach(e=>{l.add(e)}),l},r=[],l=new Set,a=e=>{if(!l.has(e)){for(let r of(l.add(e),t(e)))e!==r&&a(r);r.push(e)}};a(e);let s=new Set([e]),d=e=>l.has(e);for(let e=r.length-1;e>=0;--e){let t=r[e],n=u(t);if(!n)continue;let o=!1;for(let e of n.d.keys())if(e!==t&&s.has(e)){o=!0;break}if(o){let e=A(t,d);i(t,e),p(n,e)||s.add(t)}l.delete(t)}},M=(t,...r)=>t.write(e=>b(A(e)),(r,...n)=>{let o,i=l.length>0;if(i||l.push(new Set([r])),a(t,r)){if(!s(r))throw Error("atom not writable");p(u(r),T(r,n[0]))||O(r)}else o=M(r,...n);if(!i){let t=R(l.pop());e.forEach(e=>e({type:"async-write",flushed:t}))}return o},...r),j=(t,...r)=>{l.push(new Set([t]));let n=M(t,...r),o=R(l.pop());return e.forEach(e=>e({type:"write",flushed:o})),n},z=(e,r,l)=>{var o;let i=n.get(e);if(i)return r&&i.t.add(r),i;let a=l||[];null==(o=u(e))||o.d.forEach((t,r)=>{r!==e&&z(r,e,a)}),A(e);let s={t:new Set(r&&[r]),l:new Set};if(n.set(e,s),t.add(e),d(e)&&e.onMount){let{onMount:t}=e;a.push(()=>{let r=t((...t)=>j(e,...t));r&&(s.u=r)})}return l||a.forEach(e=>e()),s},P=(e,t)=>!t.l.size&&(!t.t.size||1===t.t.size&&t.t.has(e)),C=(e,r)=>{if(!P(e,r))return;let l=r.u;l&&l(),n.delete(e),t.delete(e);let o=u(e);o?(E(o)&&h(o.v),o.d.forEach((t,r)=>{if(r!==e){let t=n.get(r);t&&(t.t.delete(e),C(r,t))}})):console.warn("[Bug] could not find atom state to unmount",e)},I=(e,t,r)=>{let l=new Set(t.d.keys()),o=new Set;null==r||r.forEach((t,r)=>{if(l.has(r))return void l.delete(r);o.add(r);let u=n.get(r);u&&u.t.delete(e)}),l.forEach(t=>{z(t,e)}),o.forEach(e=>{let t=n.get(e);t&&C(e,t)})},R=e=>{let t;t=new Set;let r=[],l=e=>{var t;if(!o.has(e))return;let[n,i]=o.get(e);o.delete(e),r.push([e,n]),i.forEach(l),null==(t=u(e))||t.d.forEach((e,t)=>l(t))};return e.forEach(l),r.forEach(([e,r])=>{let l=u(e);if(!l)return void console.warn("[Bug] no atom state to flush");if(l!==r){let o=n.get(e);o&&l.d!==(null==r?void 0:r.d)&&I(e,l,null==r?void 0:r.d),o&&!(!E(r)&&(p(r,l)||_(r,l)))&&(o.l.forEach(e=>e()),t.add(e))}}),t};return{get:e=>b(A(e)),set:j,sub:(t,r)=>{let n=z(t),l=R([t]),o=n.l;return o.add(r),e.forEach(e=>e({type:"sub",flushed:l})),()=>{o.delete(r),C(t,n),e.forEach(e=>e({type:"unsub"}))}},dev_subscribe_store:t=>(e.add(t),()=>{e.delete(t)}),dev_get_mounted_atoms:()=>t.values(),dev_get_atom_state:e=>r.get(e),dev_get_mounted:e=>n.get(e),dev_restore_atoms:t=>{for(let[e,r]of(l.push(new Set),t))s(e)&&(T(e,r),O(e));let r=R(l.pop());e.forEach(e=>e({type:"restore",flushed:r}))}}};Symbol("CONTINUE_PROMISE");let S=y,T=()=>(n||(n=y(),globalThis.__JOTAI_DEFAULT_STORE__||(globalThis.__JOTAI_DEFAULT_STORE__=n),globalThis.__JOTAI_DEFAULT_STORE__!==n&&console.warn("Detected multiple Jotai instances. It may cause unexpected behavior with the default store. https://github.com/pmndrs/jotai/discussions/2044")),n)}}]);