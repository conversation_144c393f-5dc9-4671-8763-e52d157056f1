(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[494],{91:(e,t,r)=>{"use strict";r.d(t,{default:()=>x});var a=r(31710),n=r(54990),s=r(56694),o=r(34727),l=r(76285),i=r(328),d=r(760),c=r(40598),u=r(81401),f=r(35484),m=r(35621),p=r(52994);function g(e){let{autoComplete:t,autoFocus:r,className:g,inputClassName:x,iconContainerClassName:b,copy:h,defaultValue:v,descriptionText:y,disabled:w,error:j,icon:N,id:C="",name:k="",inputRef:_,label:z,afterLabel:S,beforeLabel:R,labelOptional:A,layout:L,onChange:M,onBlur:D,onCopy:F,placeholder:I,type:T="text",value:E,style:B,reveal:O=!1,actions:P,size:V="medium",borderless:G=!1,validation:H,...Z}=e,[W,U]=(0,n.useState)("Copy"),[q,J]=(0,n.useState)(!0),Y=(0,c.A)("input"),{formContextOnChange:X,values:K,errors:$,handleBlur:Q,touched:ee,fieldLevelValidation:et}=(0,p.x)();K&&!E&&(E=K[C||k]),j||($&&!j&&(j=$[C||k]),j=ee&&ee[C]?j:void 0),(0,n.useEffect)(()=>{H&&et(C,H(E))},[]);let er=["peer/input",Y.base];return j&&er.push(Y.variants.error),j||er.push(Y.variants.standard),V&&er.push(Y.size[V]),N&&er.push(Y.with_icon),w&&er.push(Y.disabled),x&&er.push(x),(0,a.jsx)(o.H,{label:z,afterLabel:S,beforeLabel:R,labelOptional:A,layout:L,id:C,error:j,descriptionText:y,style:B,size:V,className:g,children:(0,a.jsxs)("div",{className:Y.container,children:[(0,a.jsx)("input",{"data-size":V,autoComplete:t,autoFocus:r,defaultValue:v,disabled:w,id:C,name:k,onChange:function(e){M&&M(e),X&&X(e),H&&et(C,H(e.target.value))},onBlur:function(e){Q&&setTimeout(()=>{Q(e)},100),D&&D(e)},onCopy:F,placeholder:I,ref:_,type:T,value:O&&q?d.ne:E,className:(0,f.cn)(er),...Z}),N&&(0,a.jsx)(i.A,{size:V,icon:N,className:b}),h||j||P?(0,a.jsxs)("div",{className:Y.actions_container,children:[j&&(0,a.jsx)(l.A,{size:V}),h&&!(O&&q)?(0,a.jsx)(m.Button,{size:"tiny",type:"default",icon:(0,a.jsx)(s.A,{}),onClick:()=>{var e;return e=E,void(0,u.l)(e,()=>{U("Copied"),setTimeout(()=>{U("Copy")},3e3),null==F||F()})},children:W}):null,O&&q?(0,a.jsx)(m.Button,{size:"tiny",type:"default",onClick:function(){J(!1)},children:"Reveal"}):null,P&&P]}):null]})})}g.TextArea=function(e){let{className:t,textAreaClassName:r,descriptionText:i,disabled:d,error:f,icon:g,id:x="",name:b="",label:h,afterLabel:v,beforeLabel:y,labelOptional:w,layout:j,onChange:N,onBlur:C,placeholder:k,value:_,style:z,rows:S=4,limit:R,size:A,borderless:L=!1,validation:M,copy:D=!1,onCopy:F,actions:I,...T}=e,[E,B]=(0,n.useState)(0),[O,P]=(0,n.useState)("Copy"),{formContextOnChange:V,values:G,errors:H,handleBlur:Z,touched:W,fieldLevelValidation:U}=(0,p.x)();G&&!_&&(_=G[x||b]),f||(H&&!f&&(f=H[x||b]),f=W&&W[x||b]?f:void 0),(0,n.useEffect)(()=>{M&&U(x,M(_))},[]);let q=(0,c.A)("input"),J=[q.base];return f&&J.push(q.variants.error),f||J.push(q.variants.standard),g&&J.push(q.with_icon),A&&J.push(q.size[A]),d&&J.push(q.disabled),r&&J.push(r),(0,a.jsx)(o.H,{className:t,label:h,afterLabel:v,beforeLabel:y,labelOptional:w,layout:j,id:x,error:f,descriptionText:i,style:z,size:A,children:(0,a.jsxs)("div",{className:q.container,children:[(0,a.jsx)("textarea",{disabled:d,id:x,name:b,rows:S,cols:100,placeholder:k,onChange:function(e){B(e.target.value.length),N&&N(e),V&&V(e),M&&U(x,M(e.target.value))},onBlur:function(e){Z&&setTimeout(()=>{Z(e)},100),C&&C(e)},onCopy:F,value:_,className:J.join(" "),maxLength:R,...T}),D||f||I?(0,a.jsx)("div",{className:q.textarea_actions_container,children:(0,a.jsxs)("div",{className:q.textarea_actions_container_items,children:[f&&(0,a.jsx)(l.A,{size:A}),D&&(0,a.jsx)(m.Button,{size:"tiny",type:"default",onClick:()=>{var e;return e=_,void(0,u.l)(e,()=>{P("Copied"),setTimeout(()=>{P("Copy")},3e3),null==F||F()})},icon:(0,a.jsx)(s.A,{}),children:O}),I&&I]})}):null]})})};let x=g},328:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(31710);r(54990);var n=r(40598),s=r(35484);function o(e){let{icon:t,className:r,size:o}=e,l=(0,n.A)("inputIconContainer");return(0,a.jsx)("div",{className:(0,s.cn)(l.base,l.size[o],r),children:t})}},525:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ExpandingTextArea:()=>i});var a=r(31710),n=r(54990),s=r(35484);let o=["bg-control"],l=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("textarea",{className:(0,s.cn)("flex min-h-10 w-full rounded-md border border-control bg-control px-3 py-2 text-base md:text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted","focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50",...o,r),ref:t,...n})});l.displayName="TextArea";let i=(0,n.forwardRef)((e,t)=>{let{className:r,value:o,...i}=e,d=(0,n.useRef)(null);(0,n.useImperativeHandle)(t,()=>d.current,[]);let c=e=>{e&&(o?(e.style.height="auto",e.style.height=e.scrollHeight+"px"):(e.style.height="auto",e.style.minHeight="36px"))};return(0,a.jsx)(l,{ref:e=>{e&&(d.current=e,c(e))},rows:1,"aria-expanded":!1,className:(0,s.cn)("h-auto resize-none box-border",r),value:o,...i})});i.displayName="ExpandingTextArea"},1517:(e,t,r)=>{"use strict";r.r(t),r.d(t,{AspectRatio:()=>a});let a=r(36526).b},2889:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Menubar:()=>g,MenubarCheckboxItem:()=>w,MenubarContent:()=>v,MenubarGroup:()=>u,MenubarItem:()=>y,MenubarLabel:()=>N,MenubarMenu:()=>c,MenubarPortal:()=>f,MenubarRadioGroup:()=>p,MenubarRadioItem:()=>j,MenubarSeparator:()=>C,MenubarShortcut:()=>k,MenubarSub:()=>m,MenubarSubContent:()=>h,MenubarSubTrigger:()=>b,MenubarTrigger:()=>x});var a=r(31710),n=r(6559),s=r(12861),o=r(60601),l=r(4743),i=r(54990),d=r(35484);let c=n.W1,u=n.YJ,f=n.ZL,m=n.Pb,p=n.z6,g=i.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.bL,{ref:t,className:(0,d.cn)("flex h-10 items-center space-x-1 rounded-md border bg-background p-1",r),...s})});g.displayName=n.bL.displayName;let x=i.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.l9,{ref:t,className:(0,d.cn)("flex cursor-default select-none items-center rounded-sm px-3 py-1.5 text-sm font-medium outline-none focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground",r),...s})});x.displayName=n.l9.displayName;let b=i.forwardRef((e,t)=>{let{className:r,inset:o,children:l,...i}=e;return(0,a.jsxs)(n.ZP,{ref:t,className:(0,d.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground",o&&"pl-8",r),...i,children:[l,(0,a.jsx)(s.A,{className:"ml-auto h-4 w-4"})]})});b.displayName=n.ZP.displayName;let h=i.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.G5,{ref:t,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-overlay p-1 text-popover-foreground shadow-md animate-in data-[side=bottom]:slide-in-from-top-1 data-[side=left]:slide-in-from-right-1 data-[side=right]:slide-in-from-left-1 data-[side=top]:slide-in-from-bottom-1",r),...s})});h.displayName=n.G5.displayName;let v=i.forwardRef((e,t)=>{let{className:r,align:s="start",alignOffset:o=-4,sideOffset:l=8,...i}=e;return(0,a.jsx)(n.ZL,{children:(0,a.jsx)(n.UC,{ref:t,align:s,alignOffset:o,sideOffset:l,className:(0,d.cn)("z-50 min-w-[12rem] overflow-hidden rounded-md border bg-overlay p-1 text-popover-foreground shadow-md animate-in slide-in-from-top-1",r),...i})})});v.displayName=n.UC.displayName;let y=i.forwardRef((e,t)=>{let{className:r,inset:s,...o}=e;return(0,a.jsx)(n.q7,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s&&"pl-8",r),...o})});y.displayName=n.q7.displayName;let w=i.forwardRef((e,t)=>{let{className:r,children:s,checked:l,...i}=e;return(0,a.jsxs)(n.H_,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),checked:l,...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.VF,{children:(0,a.jsx)(o.A,{className:"h-4 w-4"})})}),s]})});w.displayName=n.H_.displayName;let j=i.forwardRef((e,t)=>{let{className:r,children:s,...o}=e;return(0,a.jsxs)(n.hN,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...o,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.VF,{children:(0,a.jsx)(l.A,{className:"h-2 w-2 fill-current"})})}),s]})});j.displayName=n.hN.displayName;let N=i.forwardRef((e,t)=>{let{className:r,inset:s,...o}=e;return(0,a.jsx)(n.JU,{ref:t,className:(0,d.cn)("px-2 py-1.5 text-sm font-semibold",s&&"pl-8",r),...o})});N.displayName=n.JU.displayName;let C=i.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",r),...s})});C.displayName=n.wv.displayName;let k=e=>{let{className:t,...r}=e;return(0,a.jsx)("span",{className:(0,d.cn)("ml-auto text-xs tracking-widest text-foreground-muted",t),...r})};k.displayname="MenubarShortcut"},3244:(e,t,r)=>{"use strict";r.r(t),r.d(t,{AlertDialog:()=>d,AlertDialogAction:()=>v,AlertDialogCancel:()=>y,AlertDialogContent:()=>p,AlertDialogDescription:()=>h,AlertDialogFooter:()=>x,AlertDialogHeader:()=>g,AlertDialogTitle:()=>b,AlertDialogTrigger:()=>c});var a=r(31710),n=r(84706),s=r(54990),o=r(35484),l=r(35621),i=r(11448);let d=n.bL,c=n.l9,u=e=>{let{children:t,...r}=e;return(0,a.jsx)(n.ZL,{...r,children:(0,a.jsx)("div",{className:"fixed inset-0 z-50 flex items-end justify-center sm:items-center",children:t})})};u.displayName=n.ZL.displayName;let f=s.forwardRef((e,t)=>{let{className:r,centered:s=!0,...l}=e;return(0,a.jsx)(n.hJ,{ref:t,className:(0,o.cn)("bg-black/40 backdrop-blur-sm","z-50 fixed inset-0 grid place-items-center overflow-y-auto data-closed:animate-overlay-hide py-8",!s&&"flex flex-col flex-start pb-8 sm:pt-12 md:pt-20 lg:pt-32 xl:pt-40 px-5",r),...l})});f.displayName=n.hJ.displayName;let m=(0,i.F)((0,o.cn)("relative z-50 w-full max-w-screen border shadow-md dark:shadow-sm","data-[state=open]:animate-in data-[state=closed]:animate-out","data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95","data-[state=closed]:slide-out-to-left-[0%] data-[state=closed]:slide-out-to-top-[0%]","data-[state=open]:slide-in-from-left-[0%] data-[state=open]:slide-in-from-top-[0%]","sm:rounded-lg md:w-full","bg-dash-sidebar"),{variants:{size:{tiny:"sm:align-middle sm:w-full sm:max-w-xs",small:"sm:align-middle sm:w-full sm:max-w-sm",medium:"sm:align-middle sm:w-full sm:max-w-lg",large:"sm:align-middle sm:w-full md:max-w-xl",xlarge:"sm:align-middle sm:w-full md:max-w-3xl",xxlarge:"sm:align-middle sm:w-full md:max-w-6xl",xxxlarge:"sm:align-middle sm:w-full md:max-w-7xl"}},defaultVariants:{size:"medium"}}),p=s.forwardRef((e,t)=>{let{className:r,children:s,size:l,dialogOverlayProps:i,centered:d=!0,...c}=e;return(0,a.jsx)(u,{children:(0,a.jsx)(f,{centered:d,...i,children:(0,a.jsx)(n.UC,{ref:t,className:(0,o.cn)(m({size:l}),r),...c,children:s})})})});p.displayName=n.UC.displayName;let g=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-2 text-center sm:text-left",t),...r})};g.displayName="AlertDialogHeader";let x=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 border-t py-3 px-5",t),...r})};x.displayName="AlertDialogFooter";let b=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.hE,{ref:t,className:(0,o.cn)("text-base text-foreground border-b px-5 py-3",r),...s})});b.displayName=n.hE.displayName;let h=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.VY,{ref:t,className:(0,o.cn)("text-sm text-foreground-light px-5 py-3",r),...s})});h.displayName=n.VY.displayName;let v=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.rc,{ref:t,className:(0,o.cn)((0,l.buttonVariants)({type:"primary",size:"tiny"}),r),...s})});v.displayName=n.rc.displayName;let y=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.ZD,{ref:t,className:(0,o.cn)((0,l.buttonVariants)({type:"default",size:"tiny"}),"mt-2 sm:mt-0",r),...s})});y.displayName=n.ZD.displayName},3999:(e,t,r)=>{"use strict";r.d(t,{Label:()=>d});var a=r(31710),n=r(88470),s=r(11448),o=r(54990),l=r(35484);let i=(0,s.F)("text-sm text leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=o.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.b,{ref:t,className:(0,l.cn)(i(),r),...s})});d.displayName=n.b.displayName},6753:(e,t,r)=>{"use strict";r.r(t),r.d(t,{AiIconAnimation:()=>d});var a=r(31710),n=r(68105),s=r(72880),o=r(31568),l=r(54990),i=r(35484);let d=(0,l.memo)(e=>{let{size:t=24,loading:r=!1,className:d,allowHoverEffect:c=!1}=e,u=Math.max(1.5,t/46),f=(0,l.useRef)(null),[m,p]=(0,l.useState)(!1),g=(0,n.d)(0),x=(0,n.d)(0),b=(0,s.z)(g,{stiffness:300,damping:30}),h=(0,s.z)(x,{stiffness:300,damping:30});return(0,a.jsxs)("div",{className:(0,i.cn)("text-brand-600 flex justify-center items-center relative",d),style:{width:t,height:t,position:"relative"},children:[(0,a.jsx)("div",{ref:f,className:"absolute flex items-center justify-center",style:{width:2*t,height:2*t,left:-t/2,top:-t/2},onMouseMove:e=>{if(!c||!f.current)return;let t=f.current.getBoundingClientRect(),r=t.left+t.width/2,a=t.top+t.height/2,n=e.clientX-r,s=e.clientY-a;g.set(n/5),x.set(s/5)},onMouseEnter:()=>p(!0),onMouseLeave:()=>{p(!1),g.set(0),x.set(0)}}),(0,a.jsxs)(o.P.svg,{width:t,height:t,viewBox:"0 0 46 46",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)(o.P.path,{fillRule:"evenodd",clipRule:"evenodd",d:"M23 1.78677L44.2132 23L23 44.2132L1.7868 23L23 1.78677ZM23 0.372559L23.7071 1.07967L44.9203 22.2929L45.6274 23L44.9203 23.7071L23.7071 44.9203L23 45.6274L22.2929 44.9203L1.07969 23.7071L0.372583 23L1.07969 22.2929L22.2929 1.07967L23 0.372559Z",fill:"none",stroke:"currentColor",strokeWidth:u,animate:r?"loading":m?"hover":"rest",variants:{rest:{rotate:0},loading:{rotate:360},hover:{rotate:10}},transition:{duration:2,repeat:r?1/0:0,ease:"circInOut",type:"spring",stiffness:60,damping:10}}),(0,a.jsx)(o.P.path,{fillRule:"evenodd",clipRule:"evenodd",d:"M30 23C30 26.866 26.866 30 23 30C19.134 30 16 26.866 16 23C16 19.134 19.134 16 23 16C26.866 16 30 19.134 30 23ZM31 23C31 27.4183 27.4183 31 23 31C18.5817 31 15 27.4183 15 23C15 18.5817 18.5817 15 23 15C27.4183 15 31 18.5817 31 23Z",fill:"none",stroke:"currentColor",strokeWidth:u,variants:{rest:{scale:1,x:0,y:0},loading:{scale:[1,1.1,1],x:0,y:0},hover:{scale:1.1}},animate:m?"hover":r?"loading":"rest",style:{x:b,y:h},transition:{duration:2,repeat:r?1/0:0,ease:"easeInOut"}})]})]})})},8121:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ResizableHandle:()=>d,ResizablePanel:()=>i,ResizablePanelGroup:()=>l});var a=r(31710),n=r(54504),s=r(62908),o=r(35484);let l=e=>{let{className:t,...r}=e;return(0,a.jsx)(s.YZ,{className:(0,o.cn)("flex h-full w-full data-[panel-group-direction=vertical]:flex-col",t),...r})},i=s.Zk,d=e=>{let{withHandle:t,className:r,...l}=e;return(0,a.jsx)(s.TW,{className:(0,o.cn)("relative flex w-px items-center justify-center bg-border after:absolute after:inset-y-0 after:left-1/2 after:w-1 after:-translate-x-1/2 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-1 data-[panel-group-direction=vertical]:h-px data-[panel-group-direction=vertical]:w-full data-[panel-group-direction=vertical]:after:left-0 data-[panel-group-direction=vertical]:after:h-1 data-[panel-group-direction=vertical]:after:w-full data-[panel-group-direction=vertical]:after:-translate-y-1/2 data-[panel-group-direction=vertical]:after:translate-x-0 [&[data-panel-group-direction=vertical]>div]:rotate-90","data-[resize-handle-state=drag]:bg-border-strong","group","transition-colors",r),...l,children:t&&(0,a.jsx)("div",{className:(0,o.cn)("z-10 flex h-4 w-3 items-center justify-center rounded-sm border bg-border focus:bg-surface-400","opacity-0 transition-opacity duration-200","group-data-[resize-handle-state=hover]:opacity-100","hover:bg-surface-400","group-data-[resize-handle-state=drag]:opacity-100","group-data-[resize-handle-state=drag]:bg-foreground-muted"),children:(0,a.jsx)(n.A,{className:"h-2.5 w-2.5"})})})}},8856:(e,t,r)=>{"use strict";r.d(t,{Checkbox:()=>i});var a=r(31710),n=r(61303),s=r(60601),o=r(54990),l=r(35484);let i=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,a.jsx)(n.bL,{ref:t,className:(0,l.cn)("peer flex items-center justify-center h-4 w-4 shrink-0 rounded border border-control bg-control/25 ring-offset-background","transition-colors duration-150 ease-in-out","hover:border-strong","focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2","disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-foreground data-[state=checked]:text-background",r),...o,children:(0,a.jsx)(n.C1,{className:(0,l.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(s.A,{className:"h-3 w-3",strokeWidth:4})})})});i.displayName=n.bL.displayName},10955:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Alert:()=>f});var a=r(31710),n=r(54990),s=r(40598),o=r(98317),l=r(55629),i=r(10636),d=r(6611),c=r(63089);let u={danger:(0,a.jsx)(o.A,{strokeWidth:1.5,size:18}),success:(0,a.jsx)(l.A,{strokeWidth:1.5,size:18}),warning:(0,a.jsx)(i.A,{strokeWidth:1.5,size:18}),info:(0,a.jsx)(d.A,{strokeWidth:1.5,size:18}),neutral:(0,a.jsx)(a.Fragment,{})};function f(e){let{variant:t="neutral",className:r,title:o,withIcon:l,closable:i,children:d,icon:f,actions:m}=e,p=(0,s.A)("alert"),[g,x]=(0,n.useState)(!0),b=[p.base];b.push(p.variant[t].base),r&&b.push(r);let h=[p.description,p.variant[t].description],v=[p.close];return(0,a.jsx)(a.Fragment,{children:g&&(0,a.jsxs)("div",{className:b.join(" "),children:[l?(0,a.jsx)("div",{className:p.variant[t].icon,children:l&&u[t]}):null,f&&f,(0,a.jsxs)("div",{className:"flex flex-1 items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:[p.variant[t].header,p.header].join(" "),children:o}),(0,a.jsx)("div",{className:h.join(" "),children:d})]}),m]}),i&&(0,a.jsx)("button",{"aria-label":"Close alert",onClick:()=>x(!1),className:v.join(" "),children:(0,a.jsx)(c.A,{strokeWidth:2,size:16})})]})})}},12255:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let a={border:{brand:"border-brand-600",primary:"border-default",secondary:"border-secondary",alternative:"border-alternative"},placeholder:"placeholder-foreground-muted",focus:"\n    outline-none\n    focus:ring-current focus:ring-2\n  ",size:{text:{tiny:"text-xs",small:"text-sm leading-4",medium:"text-sm",large:"text-base",xlarge:"text-base"},padding:{tiny:"px-2.5 py-1",small:"px-3 py-2",medium:"px-4 py-2",large:"px-4 py-2",xlarge:"px-6 py-3"}}},n={tiny:"".concat(a.size.text.tiny," ").concat(a.size.padding.tiny),small:"".concat(a.size.text.small," ").concat(a.size.padding.small),medium:"".concat(a.size.text.medium," ").concat(a.size.padding.medium),large:"".concat(a.size.text.large," ").concat(a.size.padding.large),xlarge:"".concat(a.size.text.xlarge," ").concat(a.size.padding.xlarge)},s={accordion:{variants:{default:{base:"\n          flex flex-col\n          space-y-3\n        ",container:"\n          group\n          first:rounded-tl-md first:rounded-tr-md\n          last:rounded-bl-md last:rounded-br-md\n          overflow-hidden\n          will-change-transform\n        ",trigger:"\n          flex flex-row\n          gap-3\n          items-center\n          w-full\n          text-left\n          cursor-pointer\n\n          outline-none\n          focus-visible:ring-1\n          focus-visible:z-10\n          ring-foreground-light\n        ",content:"\n          data-open:animate-slide-down\n          data-closed:animate-slide-up\n        ",panel:"\n          py-3\n        "},bordered:{base:"\n          flex flex-col\n          -space-y-px\n        ",container:"\n          group\n          border\n          border-default\n\n          first:rounded-tl-md first:rounded-tr-md\n          last:rounded-bl-md last:rounded-br-md\n        ",trigger:"\n          flex flex-row\n          items-center\n          px-6 py-4\n          w-full\n          text-left\n          cursor-pointer\n\n          font-medium\n          text-base\n          bg-transparent\n\n          outline-none\n          focus-visible:ring-1\n          focus-visible:z-10\n          ring-foreground-light\n\n          transition-colors\n          hover:bg-background\n\n          overflow-hidden\n\n          group-first:rounded-tl-md group-first:rounded-tr-md\n          group-last:rounded-bl-md group-last:rounded-br-md\n        ",content:"\n          data-open:animate-slide-down\n          data-closed:animate-slide-up\n        ",panel:"\n          px-6 py-3\n          border-t border-strong\n          bg-background\n        "}},justified:"justify-between",chevron:{base:"\n        text-foreground-lighter\n        rotate-0\n        group-state-open:rotate-180\n        group-data-[state=open]:rotate-180\n        ease-&lsqb;cubic-bezier(0.87,_0,_0.13,_1)&rsqb;\n        transition-transform duration-300\n        duration-200\n      ",align:{left:"order-first",right:"order-last"}},animate:{enter:"transition-max-height ease-in-out duration-700 overflow-hidden",enterFrom:"max-h-0",enterTo:"max-h-screen",leave:"transition-max-height ease-in-out duration-300 overflow-hidden",leaveFrom:"max-h-screen",leaveTo:"max-h-0"}},badge:{base:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-normal bg-opacity-10",size:{large:"px-3 py-0.5 rounded-full text-sm"},dot:"-ml-0.5 mr-1.5 h-2 w-2 rounded-full",color:{brand:"bg-brand-500 text-brand-600 border border-brand-500",brandAlt:"bg-brand bg-opacity-100 text-background border border-brand",scale:"bg-background text-foreground-light border border-strong",tomato:"bg-tomato-200 text-tomato-1100 border border-tomato-700",red:"bg-red-200 text-red-1100 border border-red-700",crimson:"bg-crimson-200 text-crimson-1100 border border-crimson-700",pink:"bg-pink-200 text-pink-1100 border border-pink-700",purple:"bg-purple-200 text-purple-1100 border border-purple-700",violet:"bg-violet-200 text-violet-1100 border border-violet-700",indigo:"bg-indigo-200 text-indigo-1100 border border-indigo-700",blue:"bg-blue-200 text-blue-1100 border border-blue-700",green:"bg-opacity-10 bg-brand-500 text-brand-600 border border-brand-500",grass:"bg-grass-200 text-grass-1100 border border-grass-700",orange:"bg-orange-200 text-orange-1100 border border-orange-700",yellow:"bg-yellow-200 text-yellow-1100 border border-yellow-700",amber:"bg-amber-200 text-amber-1100 border border-amber-700",gold:"bg-gold-200 text-gold-1100 border border-gold-700",gray:"bg-200 text-gray-1100 border border-gray-700",slate:"bg-slate-200 text-slate-1100 border border-slate-700"}},alert:{base:"\n      relative rounded-md border py-4 px-6\n      flex space-x-4 items-start\n    ",header:"block text-sm font-normal mb-1",description:"text-xs",variant:{danger:{base:"bg-red-200 text-red-1200 border-red-700",icon:"text-red-900",header:"text-red-1200",description:"text-red-1100"},warning:{base:"bg-amber-200 border-amber-700",icon:"text-amber-900",header:"text-amber-1200",description:"text-amber-1100"},info:{base:"bg-alternative border",icon:"text-foreground-lighter",header:"text-foreground",description:"text-foreground-light"},success:{base:"bg-brand-300 border-brand-400",icon:"text-brand",header:"text-brand-600",description:"text-brand-600"},neutral:{base:"bg-surface-100 border-default",icon:"text-foreground-muted",header:"text-foreground",description:"text-foreground-light"}},close:"\n      absolute\n      right-6 top-4\n      p-0 m-0\n      text-foreground-muted\n      cursor-pointer transition ease-in-out\n      bg-transparent border-transparent focus:outline-none\n      opacity-50 hover:opacity-100"},card:{base:"\n      bg-surface-100\n\n      border\n      ".concat(a.border.primary,"\n\n      flex flex-col\n      rounded-md shadow-lg overflow-hidden relative\n    "),hoverable:"transition hover:-translate-y-1 hover:shadow-2xl",head:"px-8 py-6 flex justify-between\n    border-b\n      ".concat(a.border.primary," "),content:"p-8"},tabs:{base:"w-full justify-between space-y-4",underlined:{list:"\n        flex items-center border-b\n        ".concat(a.border.secondary,"\n        "),base:"\n        relative\n        cursor-pointer\n        text-foreground-lighter\n        flex\n        items-center\n        space-x-2\n        text-center\n        transition\n        focus:outline-none\n        focus-visible:ring\n        focus-visible:ring-foreground-muted\n        focus-visible:border-foreground-muted\n      ",inactive:"\n        hover:text-foreground\n      ",active:"\n        !text-foreground\n        border-b-2 border-foreground\n      "},pills:{list:"flex space-x-1",base:"\n        relative\n        cursor-pointer\n        flex\n        items-center\n        space-x-2\n        text-center\n        transition\n        shadow-sm\n        rounded\n        border\n        focus:outline-none\n        focus-visible:ring\n        focus-visible:ring-foreground-muted\n        focus-visible:border-foreground-muted\n        ",inactive:"\n        bg-background\n        border-strong hover:border-foreground-muted\n        text-foreground-muted hover:text-foreground\n      ",active:"\n        bg-selection\n        text-foreground\n        border-stronger\n      "},"rounded-pills":{list:"flex flex-wrap gap-2",base:"\n        relative\n        cursor-pointer\n        flex\n        items-center\n        space-x-2\n        text-center\n        transition\n        shadow-sm\n        rounded-full\n        focus:outline-none\n        focus-visible:ring\n        focus-visible:ring-foreground-muted\n        focus-visible:border-foreground-muted\n        ",inactive:"\n        bg-surface-200 hover:bg-surface-300\n        hover:border-foreground-lighter\n        text-foreground-lighter hover:text-foreground\n      ",active:"\n        bg-foreground\n        text-background\n        border-foreground\n      "},block:"w-full flex items-center justify-center",size:{...n},scrollable:"overflow-auto whitespace-nowrap no-scrollbar mask-fadeout-right",wrappable:"flex-wrap",content:"focus:outline-none transition-height"},input:{base:"\n      block\n      box-border\n      w-full\n      rounded-md\n      shadow-sm\n      transition-all\n      text-foreground\n      border\n      focus-visible:shadow-md\n      ".concat(a.focus,"\n      focus-visible:border-foreground-muted\n      focus-visible:ring-background-control\n      ").concat(a.placeholder,"\n      group\n    "),variants:{standard:"\n        bg-foreground/[.026]\n        border border-control\n        ",error:"\n        bg-destructive-200\n        border border-destructive-500\n        focus:ring-destructive-400\n        placeholder:text-destructive-400\n       "},container:"relative",with_icon:"pl-10",size:{...n},disabled:"opacity-50",actions_container:"absolute inset-y-0 right-0 pl-3 pr-1 flex space-x-1 items-center",textarea_actions_container:"absolute inset-y-1.5 right-0 pl-3 pr-1 flex space-x-1 items-start",textarea_actions_container_items:"flex items-center"},select:{base:"\n      block\n      box-border\n      w-full\n      rounded-md\n      shadow-sm\n      transition-all\n      text-foreground\n      border\n      focus-visible:shadow-md\n      ".concat(a.focus,"\n      focus-visible:border-foreground-muted\n      focus-visible:ring-background-control\n      ").concat(a.placeholder,"\n\n      appearance-none\n      bg-none\n    "),variants:{standard:"\n        bg-background\n        border border-strong\n        ",error:"\n        bg-destructive-200\n        border border-destructive-500\n        focus:ring-destructive-400\n        placeholder:text-destructive-400\n       "},container:"relative",with_icon:"pl-10",size:{...n},disabled:"opacity-50",actions_container:"absolute inset-y-0 right-0 pl-3 pr-1 mr-5 flex items-center",chevron_container:"absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none",chevron:"h-5 w-5 text-foreground-lighter"},inputNumber:{base:"\n      block\n      box-border\n      w-full\n      rounded-md\n      shadow-sm\n      transition-all\n      text-foreground\n      border\n      focus-visible:shadow-md\n      ".concat(a.focus,"\n      focus-visible:border-foreground-muted\n      focus-visible:ring-background-control\n      ").concat(a.placeholder,"\n\n      appearance-none\n      bg-none\n    "),variants:{standard:"\n        bg-control\n        border border-strong\n      ",error:"\n        bg-destructive-200\n        border border-destructive-500\n        focus:ring-destructive-400\n        placeholder:text-destructive-400\n       "},disabled:"opacity-50",container:"relative",with_icon:"pl-10",size:{...n},actions_container:"absolute inset-y-0 right-0 pl-3 pr-1 flex space-x-1 items-center"},checkbox:{base:"\n      bg-transparent\n      ".concat(a.focus,"\n      focus:ring-border-muted\n      text-brand\n      border-strong\n      shadow-sm\n      rounded\n      cursor-pointer\n    "),container:"flex cursor-pointer leading-none",size:{tiny:"h-3 w-3 mt-1 mr-3",small:"h-3.5 w-3.5 mt-0.5 mr-3.5",medium:"h-4 w-4 mt-0.5 mr-3.5",large:"h-5 w-5 mt-0.5 mr-4",xlarge:"h-5 w-5 mt-0.5 mr-4"},disabled:"opacity-50",label:{base:"text-foreground-light cursor-pointer",...a.size.text},label_before:{base:"text-border",...a.size.text},label_after:{base:"text-border",...a.size.text},description:{base:"text-foreground-lighter",...a.size.text},group:"space-y-3"},radio:{base:"\n      absolute\n      ".concat(a.focus,"\n      focus:ring-brand-400\n      border-strong\n\n      text-brand\n      shadow-sm\n      cursor-pointer\n      peer\n\n      bg-surface-100\n    "),hidden:"absolute opacity-0",size:{tiny:"h-3 w-3",small:"h-3.5 w-3.5",medium:"h-4 w-4",large:"h-5 w-5",xlarge:"h-5 w-5"},variants:{cards:{container:{base:"relative cursor-pointer flex",align:{vertical:"flex flex-col space-y-1",horizontal:"flex flex-row space-x-2"}},group:"-space-y-px shadow-sm",base:"\n          transition\n          border\n          first:rounded-tl-md first:rounded-tr-md\n          last:rounded-bl-md last:rounded-br-md\n        ",size:{tiny:"px-5 py-3",small:"px-6 py-4",medium:"px-6 py-4",large:"px-8 p-4",xlarge:"px-8 p-4"},inactive:"\n          bg-surface-200\n          border-alternative\n          hover:border-strong\n          hover:bg-surface-300\n        ",active:"\n          bg-selection z-10\n          border-stronger\n          border-1\n        ",radio_offset:"left-4"},"stacked-cards":{container:{base:"relative cursor-pointer flex items-center justify-between",align:{vertical:"flex flex-col space-y-1",horizontal:"flex flex-row space-x-2"}},group:"space-y-3",base:"\n          transition\n          rounded-md\n          border\n          shadow-sm\n        ",size:{tiny:"px-5 py-3",small:"px-6 py-4",medium:"px-6 py-4",large:"px-8 p-4",xlarge:"px-8 p-4"},inactive:"\n          bg-surface-200\n          border-alternative\n          hover:border-strong\n          hover:bg-surface-300\n        ",active:"\n          bg-selection z-10\n          border-stronger\n          border-1\n        ",radio_offset:"left-4"},"small-cards":{container:{base:"relative cursor-pointer flex",align:{vertical:"flex flex-col space-y-1 items-center justify-center",horizontal:"flex flex-row space-x-2"}},group:"flex flex-row gap-3",base:"\n          transition\n          border\n          rounded-lg\n          grow\n          items-center\n          flex-wrap\n          justify-center\n          shadow-sm\n        ",size:{tiny:"px-5 py-3",small:"px-6 py-4",medium:"px-6 py-4",large:"px-8 p-4",xlarge:"px-8 p-4"},inactive:"\n          bg-surface-200\n          border-alternative\n          hover:border-strong\n          hover:bg-surface-300\n        ",active:"\n          bg-selection z-10\n          border-stronger border-1\n        ",radio_offset:"left-4"},"large-cards":{container:{base:"relative cursor-pointer flex",align:{vertical:"flex flex-col space-y-1",horizontal:"flex flex-row space-x-2"}},group:"grid grid-cols-12 gap-3",base:"\n          transition\n          border border-stronger\n          shadow-sm\n          rounded-lg\n          grow\n        ",size:{tiny:"px-5 py-3",small:"px-6 py-4",medium:"px-6 py-4",large:"px-8 p-4",xlarge:"px-8 p-4"},inactive:"\n          bg-surface-200\n          border-alternative\n          hover:border-strong\n          hover:bg-surface-300\n        ",active:"\n          bg-selection z-10\n          border-strong\n          border-1\n        ",radio_offset:"left-4"},list:{container:{base:"relative cursor-pointer flex",size:{tiny:"pl-6",small:"pl-6",medium:"pl-7",large:"pl-10",xlarge:"pl-10"},align:{vertical:"flex flex-col space-y-1",horizontal:"flex flex-row space-x-2"}},group:"space-y-4",base:"",size:{tiny:"0",small:"0",medium:"0",large:"0",xlarge:"0"},active:"",radio_offset:"left-0"}},label:{base:"text-foreground-light cursor-pointer",...a.size.text},label_before:{base:"text-border",...a.size.text},label_after:{base:"text-border",...a.size.text},description:{base:"text-foreground-lighter",...a.size.text},optionalLabel:{base:"text-foreground-lighter",...a.size.text},disabled:"opacity-50 cursor-auto border-dashed"},sidepanel:{base:"\n      z-50\n      bg-dash-sidebar\n      flex flex-col\n      fixed\n      inset-y-0\n      h-full lg:h-screen\n      border-l\n      shadow-xl\n    ",header:"\n      space-y-1 py-4 px-4 bg-dash-sidebar sm:px-6\n      border-b\n    ",contents:"\n      relative\n      flex-1\n      overflow-y-auto\n    ",content:"\n      px-4 sm:px-6\n    ",footer:"\n      flex justify-end gap-2\n      p-4 bg-overlay\n      border-t\n    ",size:{medium:"w-screen max-w-md h-full",large:"w-screen max-w-2xl h-full",xlarge:"w-screen max-w-3xl h-full",xxlarge:"w-screen max-w-4xl h-full",xxxlarge:"w-screen max-w-5xl h-full",xxxxlarge:"w-screen max-w-6xl h-full"},align:{left:"\n        left-0\n        data-open:animate-panel-slide-left-out\n        data-closed:animate-panel-slide-left-in\n      ",right:"\n        right-0\n        data-open:animate-panel-slide-right-out\n        data-closed:animate-panel-slide-right-in\n      "},separator:"\n      w-full\n      h-px\n      my-2\n      bg-border\n    ",overlay:"\n      z-50\n      fixed\n      bg-alternative\n      h-full w-full\n      left-0\n      top-0\n      opacity-75\n      data-closed:animate-fade-out-overlay-bg\n      data-open:animate-fade-in-overlay-bg\n    ",trigger:"\n      border-none bg-transparent p-0 focus:ring-0\n    "},toggle:{base:"\n      p-0 relative\n      inline-flex flex-shrink-0\n      border-2 border-transparent\n      rounded-full\n      cursor-pointer\n      transition-colors ease-in-out duration-200\n      ".concat(a.focus,"\n      focus:!ring-border\n      bg-foreground-muted/40\n\n      hover:bg-foreground-muted/60\n    "),active:"\n      !bg-brand\n      !hover:bg-brand\n    ",handle_container:{tiny:"h-4 w-7",small:"h-6 w-11",medium:"h-6 w-11",large:"h-7 w-12",xlarge:"h-7 w-12"},handle:{base:"\n        inline-block h-5 w-5\n        rounded-full\n        bg-white\n        shadow ring-0\n        transition\n        ease-in-out duration-200\n      ",tiny:"!h-3 !w-3",small:"!h-5 !w-5",medium:"!h-5 !w-5",large:"!h-6 !w-6",xlarge:"!h-6 !w-6"},handle_active:{tiny:" translate-x-3 dark:bg-white",small:"translate-x-5 dark:bg-white",medium:"translate-x-5 dark:bg-white",large:"translate-x-5 dark:bg-white",xlarge:"translate-x-5 dark:bg-white"},disabled:"opacity-75 cursor-not-allowed"},form_layout:{container:"grid gap-2",flex:{left:{base:"flex flex-row gap-6",content:"",labels:"order-2",data_input:"order-1"},right:{base:"flex flex-row gap-6 justify-between",content:"order-last",labels:"",data_input:"text-right"}},responsive:"md:grid md:grid-cols-12",non_responsive:"grid grid-cols-12 gap-2",labels_horizontal_layout:"flex flex-row space-x-2 justify-between col-span-12",labels_vertical_layout:"flex flex-col space-y-2 col-span-4",data_input_horizontal_layout:"col-span-12",non_box_data_input_spacing_vertical:"my-3",non_box_data_input_spacing_horizontal:"my-3 md:mt-0 mb-3",data_input_vertical_layout:"col-span-8",data_input_vertical_layout__align_right:"text-right",label:{base:"block text-foreground-light",size:{...a.size.text}},label_optional:{base:"text-foreground-lighter",size:{...a.size.text}},description:{base:"mt-2 text-foreground-lighter leading-normal",size:{...a.size.text}},label_before:{base:"text-foreground-lighter ",size:{...a.size.text}},label_after:{base:"text-foreground-lighter",size:{...a.size.text}},error:{base:"\n        text-red-900\n        transition-all\n        data-show:mt-2\n        data-show:animate-slide-down-normal\n        data-hide:animate-slide-up-normal\n      ",size:{...a.size.text}},size:{tiny:"text-xs",small:"text-sm leading-4",medium:"text-sm",large:"text-base",xlarge:"text-base"}},popover:{trigger:"\n      flex\n      border-none\n      rounded\n      bg-transparent\n      p-0\n      outline-none\n      outline-offset-1\n      transition-all\n      focus:outline-4\n      focus:outline-border-control\n    ",content:"\n      z-40\n      bg-overlay\n      border border-overlay\n      rounded\n      shadow-lg\n      data-open:animate-dropdown-content-show\n      data-closed:animate-dropdown-content-hide\n      min-w-fit\n\n      origin-popover\n      data-open:animate-dropdown-content-show\n      data-closed:animate-dropdown-content-hide\n    ",size:{tiny:"w-40",small:"w-48",medium:"w-64",large:"w-80",xlarge:"w-96",content:"w-auto"},header:"\n      bg-surface-200\n      space-y-1 py-1.5 px-3\n      border-b border-overlay\n    ",footer:"\n      bg-surface-200\n      py-1.5 px-3\n      border-t border-overlay\n    ",close:"\n      transition\n      text-foreground-lighter\n    ",separator:"\n      w-full\n      h-px\n      my-2\n      bg-border-overlay\n    "},menu:{item:{base:"\n        cursor-pointer\n        flex space-x-3 items-center\n        outline-none\n        focus-visible:ring-1 ring-foreground-muted focus-visible:z-10\n        group\n      ",content:{base:"transition truncate text-sm w-full",normal:"text-foreground-light group-hover:text-foreground",active:"text-foreground font-semibold"},icon:{base:"transition truncate text-sm",normal:"text-foreground-lighter group-hover:text-foreground-light",active:"text-foreground"},variants:{text:{base:"\n            py-1\n          ",normal:"\n            font-normal\n            border-default\n            group-hover:border-foreground-muted",active:"\n            font-semibold\n            text-foreground-muted\n            z-10\n          "},border:{base:"\n            px-4 py-1\n          ",normal:"\n            border-l\n            font-normal\n            border-default\n            group-hover:border-foreground-muted",active:"\n            font-semibold\n\n            text-foreground-muted\n            z-10\n\n            border-l\n            border-brand\n            group-hover:border-brand\n          ",rounded:"rounded-md"},pills:{base:"\n            px-3 py-1\n          ",normal:"\n            font-normal\n            border-default\n            group-hover:border-foreground-muted",active:"\n            font-semibold\n            bg-surface-200\n            text-foreground-lighter\n            z-10\n\n            rounded-md\n          "}}},group:{base:"\n        flex space-x-3\n        mb-2\n        font-normal\n      ",icon:"text-foreground-lighter",content:"text-sm text-foreground-lighter w-full",variants:{text:"",pills:"px-3",border:""}}},modal:{base:"\n      relative\n      bg-dash-sidebar\n      my-4 max-w-screen\n      border border-overlay\n      rounded-md\n      shadow-xl\n      data-open:animate-overlay-show\n      data-closed:animate-overlay-hide\n\n    ",header:"\n      bg-surface-200\n      space-y-1 py-3 px-4 sm:px-5\n      border-b border-overlay\n      flex items-center justify-between\n    ",footer:"\n      flex justify-end gap-2\n      py-3 px-5\n      border-t border-overlay\n    ",size:{tiny:"sm:align-middle sm:w-full sm:max-w-xs",small:"sm:align-middle sm:w-full sm:max-w-sm",medium:"sm:align-middle sm:w-full sm:max-w-lg",large:"sm:align-middle sm:w-full md:max-w-xl",xlarge:"sm:align-middle sm:w-full md:max-w-3xl",xxlarge:"sm:align-middle sm:w-full max-w-screen md:max-w-6xl",xxxlarge:"sm:align-middle sm:w-full md:max-w-7xl"},overlay:"\n      z-40\n      fixed\n      bg-alternative\n      h-full w-full\n      left-0\n      top-0\n      opacity-75\n      data-closed:animate-fade-out-overlay-bg\n      data-open:animate-fade-in-overlay-bg\n    ",scroll_overlay:"\n      z-40\n      fixed\n      inset-0\n      grid\n      place-items-center\n      overflow-y-auto\n      data-open:animate-overlay-show data-closed:animate-overlay-hide\n    ",separator:"\n      w-full\n      h-px\n      my-2\n      bg-border-overlay\n    ",content:"px-5"},listbox:{base:"\n      block\n      box-border\n      w-full\n      rounded-md\n      shadow-sm\n      text-foreground\n      border\n      focus-visible:shadow-md\n      ".concat(a.focus,"\n      focus-visible:border-foreground-muted\n      focus-visible:ring-background-control\n      ").concat(a.placeholder,"\n      indent-px\n      transition-all\n      bg-none\n    "),container:"relative",label:"truncate",variants:{standard:"\n        bg-control\n        border border-control\n\n        aria-expanded:border-foreground-muted\n        aria-expanded:ring-border-muted\n        aria-expanded:ring-2\n        ",error:"\n        bg-destructive-200\n        border border-destructive-500\n        focus:ring-destructive-400\n        placeholder:text-destructive-400\n       "},options_container_animate:"\n      transition\n      data-open:animate-slide-down\n      data-open:opacity-1\n      data-closed:animate-slide-up\n      data-closed:opacity-0\n    ",options_container:"\n      bg-overlay\n      shadow-lg\n      border border-solid\n      border-overlay max-h-60\n      rounded-md py-1 text-base\n      sm:text-sm z-10 overflow-hidden overflow-y-scroll\n\n      origin-dropdown\n      data-open:animate-dropdown-content-show\n      data-closed:animate-dropdown-content-hide\n    ",with_icon:"pl-2",addOnBefore:"\n      w-full flex flex-row items-center space-x-3\n    ",size:{...n},disabled:"opacity-50",actions_container:"absolute inset-y-0 right-0 pl-3 pr-1 flex space-x-1 items-center",chevron_container:"absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none",chevron:"h-5 w-5 text-foreground-muted",option:"\n      w-listbox\n      transition cursor-pointer select-none relative py-2 pl-3 pr-9\n      text-foreground-light\n      text-sm\n      hover:bg-border-overlay\n      focus:bg-border-overlay\n      focus:text-foreground\n      border-none\n      focus:outline-none\n    ",option_active:"text-foreground bg-selection",option_disabled:"cursor-not-allowed opacity-60",option_inner:"flex items-center space-x-3",option_check:"absolute inset-y-0 right-0 flex items-center pr-3 text-brand",option_check_active:"text-brand",option_check_icon:"h-5 w-5"},collapsible:{content:"\n      data-open:animate-slide-down-normal\n      data-closed:animate-slide-up-normal\n    "},inputErrorIcon:{base:"\n      flex items-center\n      right-3 pr-2 pl-2\n      inset-y-0\n      pointer-events-none\n      text-red-900\n    "},inputIconContainer:{base:"\n    absolute inset-y-0\n    left-0 pl-3 flex\n    items-center pointer-events-none\n    text-foreground-light\n    [&_svg]:stroke-[1.5]\n    ",size:{tiny:"[&_svg]:h-[14px] [&_svg]:w-[14px]",small:"[&_svg]:h-[18px] [&_svg]:w-[18px]",medium:"[&_svg]:h-[20px] [&_svg]:w-[20px]",large:"[&_svg]:h-[20px] [&_svg]:w-[20px]",xlarge:"[&_svg]:h-[24px] [&_svg]:w-[24px]",xxlarge:"[&_svg]:h-[30px] [&_svg]:w-[30px]",xxxlarge:"[&_svg]:h-[42px] [&_svg]:w-[42px]"}},icon:{container:"flex-shrink-0 flex items-center justify-center rounded-full p-3"},loading:{base:"relative",content:{base:"transition-opacity duration-300",active:"opacity-40"},spinner:"\n      absolute\n      text-foreground-lighter animate-spin\n      inset-0\n      size-5\n      m-auto\n    "}}},12401:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Calendar:()=>d});var a=r(31710),n=r(55796),s=r(12861);r(54990);var o=r(52966),l=r(35484),i=r(25415);function d(e){var t,r;let{className:d,classNames:c,showOutsideDays:u=!0,...f}=e,m="range"===f.mode&&!!(null==(t=f.selected)?void 0:t.from)&&!!(null==(r=f.selected)?void 0:r.to);return(0,a.jsx)(o.hv,{showOutsideDays:u,className:(0,l.cn)("p-3",d),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:(0,l.cn)((0,i.r)({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-foreground-muted rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:(0,l.cn)("text-center text-sm p-0 relative [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md","last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20"),day:(0,l.cn)((0,i.r)({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_selected:"bg-brand-500 text-foreground text-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"text-foreground-muted opacity-50",day_disabled:"text-foreground-muted opacity-50",day_range_start:(0,l.cn)(m&&"bg-brand-500 rounded-r-none"),day_range_middle:"aria-selected:bg-brand-400 rounded-none",day_range_end:(0,l.cn)(m&&"bg-brand-500 rounded-l-none"),day_hidden:"invisible",...c},components:{IconLeft:()=>(0,a.jsx)(n.A,{className:"h-4 w-4"}),IconRight:()=>(0,a.jsx)(s.A,{className:"h-4 w-4"})},...f})}d.displayName="Calendar"},13463:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Sidebar:()=>h,SidebarContent:()=>_,SidebarFooter:()=>C,SidebarGroup:()=>z,SidebarGroupAction:()=>R,SidebarGroupContent:()=>A,SidebarGroupLabel:()=>S,SidebarHeader:()=>N,SidebarInput:()=>j,SidebarInset:()=>w,SidebarMenu:()=>L,SidebarMenuAction:()=>I,SidebarMenuBadge:()=>T,SidebarMenuButton:()=>F,SidebarMenuItem:()=>M,SidebarMenuSkeleton:()=>E,SidebarMenuSub:()=>B,SidebarMenuSubButton:()=>P,SidebarMenuSubItem:()=>O,SidebarProvider:()=>b,SidebarRail:()=>y,SidebarSeparator:()=>k,SidebarTrigger:()=>v,sidebarMenuButtonVariants:()=>D,useSidebar:()=>x});var a=r(31710),n=r(1351),s=r(11448),o=r(80247),l=r(54990),i=r(35484),d=r(25415),c=r(94081),u=r(83020),f=r(86084);function m(e){let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,i.cn)("animate-pulse rounded-md bg-muted",t),...r})}var p=r(48744);let g=l.createContext(null);function x(){let e=l.useContext(g);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}let b=l.forwardRef((e,t)=>{let{defaultOpen:r=!0,open:n,onOpenChange:s,className:o,style:d,children:c,...u}=e,f=function(){let[e,t]=l.useState(void 0);return l.useEffect(()=>{let e=window.matchMedia("(max-width: ".concat(767,"px)")),r=()=>{t(window.innerWidth<768)};return e.addEventListener("change",r),t(window.innerWidth<768),()=>e.removeEventListener("change",r)},[]),!!e}(),[m,x]=l.useState(!1),[b,h]=l.useState(r),v=null!=n?n:b,y=l.useCallback(e=>{let t="function"==typeof e?e(v):e;s?s(t):h(t),document.cookie="".concat("sidebar:state","=").concat(t,"; path=/; max-age=").concat(604800)},[s,v]),w=l.useCallback(()=>f?x(e=>!e):y(e=>!e),[f,y,x]),j=v?"expanded":"collapsed",N=l.useMemo(()=>({state:j,open:v,setOpen:y,isMobile:f,openMobile:m,setOpenMobile:x,toggleSidebar:w}),[j,v,y,f,m,x,w]);return(0,a.jsx)(g.Provider,{value:N,children:(0,a.jsx)(p.TooltipProvider,{delayDuration:0,children:(0,a.jsx)("div",{style:{"--sidebar-width":"13rem","--sidebar-width-icon":"3rem",...d},className:(0,i.cn)("group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar",o),ref:t,...u,children:c})})})});b.displayName="SidebarProvider";let h=l.forwardRef((e,t)=>{let{overflowing:r=!1,side:n="left",variant:s="sidebar",collapsible:o="offcanvas",className:l,children:d,...c}=e,{isMobile:u,state:m,openMobile:p,setOpenMobile:g}=x();return"none"===o?(0,a.jsx)("div",{className:(0,i.cn)("flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground",l),ref:t,...c,children:d}):u?(0,a.jsx)(f.Sheet,{open:p,onOpenChange:g,...c,children:(0,a.jsx)(f.SheetContent,{"data-sidebar":"sidebar","data-mobile":"true",className:"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:n,children:(0,a.jsx)("div",{className:"flex h-full w-full flex-col",children:d})})}):(0,a.jsxs)("div",{ref:t,className:(0,i.cn)(r?"w-12":"","relative group peer hidden md:block text-sidebar-foreground","flex-shrink-0"),"data-state":m,"data-collapsible":"collapsed"===m?o:"","data-variant":s,"data-side":n,children:[(0,a.jsx)("div",{className:(0,i.cn)(r?"absolute top-0":"relative","duration-100 h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===s||"inset"===s?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon]")}),(0,a.jsx)("div",{className:(0,i.cn)("absolute top-0 h-full","duration-100 inset-y-0 z-10 hidden w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex","left"===n?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===s||"inset"===s?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",l),...c,children:(0,a.jsx)("div",{"data-sidebar":"sidebar",className:"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow",children:d})})]})});h.displayName="Sidebar";let v=l.forwardRef((e,t)=>{let{className:r,onClick:n,...s}=e,{toggleSidebar:l}=x();return(0,a.jsxs)(d.Button,{ref:t,"data-sidebar":"trigger",variant:"ghost",size:"icon",className:(0,i.cn)("h-7 w-7",r),onClick:e=>{null==n||n(e),l()},...s,children:[(0,a.jsx)(o.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})});v.displayName="SidebarTrigger";let y=l.forwardRef((e,t)=>{let{className:r,...n}=e,{toggleSidebar:s}=x();return(0,a.jsx)("button",{ref:t,"data-sidebar":"rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:s,title:"Toggle Sidebar",className:(0,i.cn)("absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex","[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",r),...n})});y.displayName="SidebarRail";let w=l.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("main",{ref:t,className:(0,i.cn)("relative flex min-h-svh flex-1 flex-col bg-background","peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow",r),...n})});w.displayName="SidebarInset";let j=l.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(c.p,{ref:t,"data-sidebar":"input",className:(0,i.cn)("h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",r),...n})});j.displayName="SidebarInput";let N=l.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,"data-sidebar":"header",className:(0,i.cn)("flex flex-col gap-2 p-2",r),...n})});N.displayName="SidebarHeader";let C=l.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,"data-sidebar":"footer",className:(0,i.cn)("flex flex-col gap-2 p-2",r),...n})});C.displayName="SidebarFooter";let k=l.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)(u.Separator,{ref:t,"data-sidebar":"separator",className:(0,i.cn)("mx-2 w-auto bg-sidebar-border",r),...n})});k.displayName="SidebarSeparator";let _=l.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,"data-sidebar":"content",className:(0,i.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",r),...n})});_.displayName="SidebarContent";let z=l.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,"data-sidebar":"group",className:(0,i.cn)("relative flex w-full min-w-0 flex-col p-2",r),...n})});z.displayName="SidebarGroup";let S=l.forwardRef((e,t)=>{let{className:r,asChild:s=!1,...o}=e,l=s?n.DX:"div";return(0,a.jsx)(l,{ref:t,"data-sidebar":"group-label",className:(0,i.cn)("duration-100 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-5 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",r),...o})});S.displayName="SidebarGroupLabel";let R=l.forwardRef((e,t)=>{let{className:r,asChild:s=!1,...o}=e,l=s?n.DX:"button";return(0,a.jsx)(l,{ref:t,"data-sidebar":"group-action",className:(0,i.cn)("absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-5 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","group-data-[collapsible=icon]:hidden",r),...o})});R.displayName="SidebarGroupAction";let A=l.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,"data-sidebar":"group-content",className:(0,i.cn)("w-full text-sm",r),...n})});A.displayName="SidebarGroupContent";let L=l.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("ul",{ref:t,"data-sidebar":"menu",className:(0,i.cn)("flex w-full min-w-0 flex-col gap-1",r),...n})});L.displayName="SidebarMenu";let M=l.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("li",{ref:t,"data-sidebar":"menu-item",className:(0,i.cn)("group/menu-item relative",r),...n})});M.displayName="SidebarMenuItem";let D=(0,s.F)((0,i.cn)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md py-2 px-1.5 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground [&>span:last-child]:truncate [&>svg]:size-5 [&>svg]:shrink-0 text-foreground-lighter data-[active=true]:text-foreground"),{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:!p-0"},hasIcon:{true:"group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!pl-1.5 group-data-[collapsible=icon]:!pr-2",false:""}},defaultVariants:{variant:"default",size:"default",hasIcon:!0}}),F=l.forwardRef((e,t)=>{let{asChild:r=!1,isActive:s=!1,variant:o="default",size:l="default",hasIcon:d=!0,tooltip:c,className:u,...f}=e,m=r?n.DX:"button",{isMobile:g,state:b}=x(),h=(0,a.jsx)(m,{ref:t,"data-sidebar":"menu-button","data-size":l,"data-active":s,"data-has-icon":d,className:(0,i.cn)(D({variant:o,size:l,hasIcon:d}),u),...f});return c?("string"==typeof c&&(c={children:c}),(0,a.jsxs)(p.Tooltip,{children:[(0,a.jsx)(p.TooltipTrigger,{asChild:!0,children:h}),(0,a.jsx)(p.TooltipContent,{side:"right",align:"center",hidden:"collapsed"!==b||g,...c})]})):h});F.displayName="SidebarMenuButton";let I=l.forwardRef((e,t)=>{let{className:r,asChild:s=!1,showOnHover:o=!1,...l}=e,d=s?n.DX:"button";return(0,a.jsx)(d,{ref:t,"data-sidebar":"menu-action",className:(0,i.cn)("absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-5 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",o&&"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0",r),...l})});I.displayName="SidebarMenuAction";let T=l.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,"data-sidebar":"menu-badge",className:(0,i.cn)("absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",r),...n})});T.displayName="SidebarMenuBadge";let E=l.forwardRef((e,t)=>{let{className:r,showIcon:n=!1,...s}=e,o=l.useMemo(()=>"".concat(Math.floor(40*Math.random())+50,"%"),[]);return(0,a.jsxs)("div",{ref:t,"data-sidebar":"menu-skeleton",className:(0,i.cn)("rounded-md h-8 flex gap-2 px-2 items-center",r),...s,children:[n&&(0,a.jsx)(m,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),(0,a.jsx)(m,{className:"h-4 flex-1 max-w-[--skeleton-width]","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":o}})]})});E.displayName="SidebarMenuSkeleton";let B=l.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("ul",{ref:t,"data-sidebar":"menu-sub",className:(0,i.cn)("mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",r),...n})});B.displayName="SidebarMenuSub";let O=l.forwardRef((e,t)=>{let{...r}=e;return(0,a.jsx)("li",{ref:t,...r})});O.displayName="SidebarMenuSubItem";let P=l.forwardRef((e,t)=>{let{asChild:r=!1,size:s="md",isActive:o,className:l,...d}=e,c=r?n.DX:"a";return(0,a.jsx)(c,{ref:t,"data-sidebar":"menu-sub-button","data-size":s,"data-active":o,className:(0,i.cn)("flex h-6 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-5 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===s&&"text-xs","md"===s&&"text-sm","group-data-[collapsible=icon]:hidden",l),...d})});P.displayName="SidebarMenuSubButton"},16973:(e,t,r)=>{"use strict";r.d(t,{default:()=>p});var a=r(31710),n=r(54990),s=r(15256);let o=function(e){let{id:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(t)return t;if("object"==typeof e)if(Array.isArray(e)){let t=e.find(e=>"string"==typeof e&&l(e));return void 0!==t?i(t):e.map(e=>"string"!=typeof e?e.props.children:e.trim()).map(e=>"string"!=typeof e?e:d(e)).join("-").toLowerCase()}else{let t=e.props.children;return"string"==typeof t?d(t):t}if("string"==typeof e)return l(e)?i(e):d(e)},l=e=>e.includes("[#")&&e.includes("]"),i=e=>e.slice(e.indexOf("[#")+2,e.indexOf("]")),d=e=>e.toLowerCase().trim().replace(/[^a-z0-9- ]/g,"").replace(/[ ]/g,"-"),c=e=>"object"==typeof e&&Array.isArray(e)?e.filter(e=>!("string"==typeof e&&l(e))):"string"==typeof e&&e.indexOf("[#")>0?e.slice(0,e.indexOf("[#")):e,u=e=>{let t=document.querySelectorAll(".toc-menu a"),r=document.querySelector(".toc-menu .toc__menu-item--active");null==r||r.classList.remove("toc__menu-item--active"),t.forEach(t=>{t.href.split("#")[1]===e&&t.classList.add("toc__menu-item--active")})},f=()=>{let e=document.querySelector(".toc-menu .toc__menu-item--active");null==e||e.classList.remove("toc__menu-item--active")},m=(0,n.forwardRef)((e,t)=>{let{tag:r,customAnchor:l,children:i,...d}=e,m=l||o(i,d),p="#".concat(m),{ref:g}=(0,s.Wx)({threshold:1,rootMargin:"-20% 0% -35% 0px",onChange:(e,t)=>{0===window.scrollY&&f(),e&&u(t.target.id)}}),x=(0,n.useCallback)(e=>{g(e),"function"==typeof t?t(e):t&&(t.current=e)},[t,g]);return(0,a.jsxs)("".concat(r),{id:m,ref:x,className:"group scroll-mt-24",...d,children:[c(i),m&&(0,a.jsx)("a",{href:p,"aria-hidden":"true",className:"ml-2 opacity-0 group-hover:opacity-100 transition",children:(0,a.jsx)("span",{"aria-hidden":"true",children:"#"})})]})});m.displayName="Heading";let p=m},17711:(e,t,r)=>{"use strict";r.d(t,{Accordion:()=>i,AccordionContent:()=>u,AccordionItem:()=>d,AccordionTrigger:()=>c});var a=r(31710),n=r(65980),s=r(59365),o=r(54990),l=r(35484);let i=n.bL,d=o.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.q7,{ref:t,className:(0,l.cn)("border-b",r),...s})});d.displayName="AccordionItem";let c=o.forwardRef((e,t)=>{let{className:r,children:o,hideIcon:i,...d}=e;return(0,a.jsx)(n.Y9,{className:"flex",children:(0,a.jsxs)(n.l9,{ref:t,className:(0,l.cn)("flex flex-1 gap-2 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180 text-left",r),...d,children:[o,!i&&(0,a.jsx)(s.A,{className:"h-4 w-4 transition-transform duration-200 shrink-0"})]})})});c.displayName=n.l9.displayName;let u=o.forwardRef((e,t)=>{let{className:r,children:s,...o}=e;return(0,a.jsx)(n.UC,{ref:t,className:(0,l.cn)("overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",r),...o,children:(0,a.jsx)("div",{className:"pb-4 pt-0",children:s})})});u.displayName=n.UC.displayName},17973:(e,t,r)=>{"use strict";r.r(t),r.d(t,{markdownComponents:()=>i});var a=r(31710),n=r(55782),s=r(74503),o=r(35484);let l=e=>(0,a.jsx)("span",{className:(0,o.cn)("next-image--dynamic-fill",e.className),children:(0,a.jsx)(s.default,{...e,className:"rounded-md border",layout:"fill"})}),i={mono:e=>(0,a.jsx)("code",{className:"text-sm",children:e.children}),code:e=>(0,a.jsx)(n.CodeBlock,{...e}),img:e=>l(e),Image:e=>l(e)}},18807:(e,t,r)=>{"use strict";r.d(t,{Select:()=>f,SelectContent:()=>v,SelectGroup:()=>m,SelectItem:()=>w,SelectLabel:()=>y,SelectScrollDownButton:()=>h,SelectScrollUpButton:()=>b,SelectSeparator:()=>j,SelectTrigger:()=>x,SelectValue:()=>p});var a=r(31710),n=r(20258),s=r(59365),o=r(23208),l=r(60601),i=r(54990),d=r(11448),c=r(760),u=r(35484);let f=n.bL,m=n.YJ,p=i.forwardRef((e,t)=>{let{placeholder:r,...s}=e;return(0,a.jsx)(n.WT,{placeholder:"string"==typeof r?(0,a.jsx)("span",{children:r}):r,...s,ref:t})});p.displayName=n.WT.displayName;let g=(0,d.F)("",{variants:{size:{...c.vs}},defaultVariants:{size:c.FE}}),x=i.forwardRef((e,t)=>{let{className:r,children:o,size:l,...i}=e;return(0,a.jsxs)(n.l9,{ref:t,className:(0,u.cn)("flex w-full items-center justify-between rounded-md border border-strong hover:border-stronger bg-alternative dark:bg-muted hover:bg-selection text-xs ring-offset-background-control data-[placeholder]:text-foreground-lighter focus:outline-none ring-border-control focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200","data-[state=open]:bg-selection data-[state=open]:border-stronger","gap-2",g({size:l}),r),...i,children:[o,(0,a.jsx)(n.In,{asChild:!0,children:(0,a.jsx)(s.A,{className:"h-4 w-4 text-foreground-lighter",strokeWidth:1.5})})]})});x.displayName=n.l9.displayName;let b=i.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.PP,{ref:t,className:(0,u.cn)("flex cursor-default items-center justify-center py-1 text-foreground-muted",r),...s,children:(0,a.jsx)(o.A,{className:"h-4 w-4"})})});b.displayName=n.PP.displayName;let h=i.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,a.jsx)(n.wn,{ref:t,className:(0,u.cn)("flex cursor-default items-center justify-center py-1 text-foreground-muted",r),...o,children:(0,a.jsx)(s.A,{className:"h-4 w-4"})})});h.displayName=n.wn.displayName;let v=i.forwardRef((e,t)=>{let{className:r,children:s,position:o="popper",...l}=e;return(0,a.jsx)(n.ZL,{children:(0,a.jsxs)(n.UC,{ref:t,className:(0,u.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-overlay text-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===o&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:o,...l,children:[(0,a.jsx)(b,{}),(0,a.jsx)(n.LM,{className:(0,u.cn)("p-1","popper"===o&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,a.jsx)(h,{})]})})});v.displayName=n.UC.displayName;let y=i.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.JU,{ref:t,className:(0,u.cn)("py-1.5 pl-8 pr-2 text-xs text-foreground-lighter/75 uppercase tracking-wider font-mono",r),...s})});y.displayName=n.JU.displayName;let w=i.forwardRef((e,t)=>{let{className:r,children:s,...o}=e;return(0,a.jsxs)(n.q7,{ref:t,className:(0,u.cn)("group","relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-overlay-hover text-foreground-light focus:text-foreground data-[state=checked]:text-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...o,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.VF,{className:"h-3.5 w-3.5 bg-foreground rounded-full flex justify-center items-center",children:(0,a.jsx)(l.A,{className:"h-2 w-2 text-background-overlay",strokeWidth:6})})}),(0,a.jsx)(n.p4,{children:"string"==typeof s?(0,a.jsx)("span",{children:s}):s})]})});w.displayName=n.q7.displayName;let j=i.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.wv,{ref:t,className:(0,u.cn)("-mx-1 my-1 h-px bg-border-overlay",r),...s})});j.displayName=n.wv.displayName},20874:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var a=r(31710),n=r(54990),s=r(40598),o=r(35484);let l=(0,n.createContext)({contextSize:"small",className:""}),i=function(e){let{className:t,size:r,type:n="Mail",color:i,strokeWidth:d,fill:c,stroke:u,background:f,src:m,icon:p,...g}=e,x=(0,s.A)("icon");return(0,a.jsx)(l.Consumer,{children:e=>{let{contextSize:n,className:s}=e,l={tiny:14,small:18,medium:20,large:20,xlarge:24,xxlarge:30,xxxlarge:42},b=l.large,h=21;n&&(h=n?"string"==typeof n?l[n]:n:b),r&&(h=r?"string"==typeof r?l[r]:r:b);let v=!i&&!c&&!u,y=["sbui-icon",t];s&&y.push(s);let w=m?(0,a.jsx)("div",{className:"relative",style:{width:h+"px",height:h+"px"},children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",color:v?"currentColor":i,fill:v?"none":c||"none",stroke:v?"currentColor":u,className:(0,o.cn)(y),width:"100%",height:"100%",strokeWidth:null!=d?d:void 0,...g,children:m})}):(0,a.jsx)(p,{color:v?"currentColor":i,stroke:v?"currentColor":u,className:(0,o.cn)(y),strokeWidth:d,size:h,fill:v?"none":c||"none",...g});return f?(0,a.jsx)("div",{className:x.container,children:w}):w}})}},25415:(e,t,r)=>{"use strict";r.d(t,{Button:()=>d,r:()=>i});var a=r(31710),n=r(1351),s=r(11448),o=r(54990),l=r(35484);let i=(0,s.F)("inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-foreground-muted focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-blue-300 text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-control bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=o.forwardRef((e,t)=>{let{className:r,variant:s,size:o,asChild:d=!1,...c}=e,u=d?n.DX:"button";return(0,a.jsx)(u,{className:(0,l.cn)(i({variant:s,size:o,className:r})),ref:t,...c})});d.displayName="Button"},28182:(e,t,r)=>{"use strict";r.d(t,{Popover:()=>d,PopoverAnchor:()=>u,PopoverContent:()=>f,PopoverSeparator:()=>m,PopoverTrigger:()=>c});var a=r(31710),n=r(40039),s=r(54990),o=r(35484),l=r(69102),i=r.n(l);let d=n.bL,c=n.l9,u=n.Mz,f=s.forwardRef((e,t)=>{let{className:r,align:l="center",sideOffset:d=4,portal:c=!1,sameWidthAsTrigger:u=!1,...f}=e,m=c?n.ZL:s.Fragment;return(0,a.jsx)(m,{children:(0,a.jsx)(n.UC,{ref:t,align:l,sideOffset:d,className:(0,o.cn)(u?i()["popover-trigger-width"]:"","z-50 w-72 rounded-md border border-overlay bg-overlay p-4 text-popover-foreground shadow-md outline-none animate-in data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...f})})});f.displayName="PopoverContent";let m=s.forwardRef((e,t)=>{let{className:r,children:n,...s}=e;return(0,a.jsx)("div",{ref:t,...s,className:(0,o.cn)("w-full h-px bg-border-overlay",r)})});m.displayName="PopoverSeparator"},28694:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ScrollArea:()=>l,ScrollBar:()=>d,ScrollViewport:()=>i});var a=r(31710),n=r(50285),s=r(54990),o=r(35484);let l=s.forwardRef((e,t)=>{let{className:r,children:s,...l}=e;return(0,a.jsxs)(n.bL,{ref:t,className:(0,o.cn)("relative overflow-hidden",r),...l,children:[(0,a.jsx)(n.LM,{className:"h-full w-full rounded-[inherit]",children:s}),(0,a.jsx)(d,{}),(0,a.jsx)(n.OK,{})]})});l.displayName=n.bL.displayName;let i=s.forwardRef((e,t)=>{let{className:r,children:s,...l}=e;return(0,a.jsx)(n.LM,{ref:t,className:(0,o.cn)("size-full rounded-[inherit]",r),...l,children:s})});i.displayName=n.LM.displayName;let d=s.forwardRef((e,t)=>{let{className:r,orientation:s="vertical",...l}=e;return(0,a.jsx)(n.VM,{ref:t,orientation:s,className:(0,o.cn)("flex touch-none select-none transition-colors","vertical"===s&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===s&&"h-2.5 border-t border-t-transparent p-[1px]",r),...l,children:(0,a.jsx)(n.lr,{className:"relative flex-1 rounded-full bg-border"})})});d.displayName=n.VM.displayName},29817:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var a=r(31710),n=r(54151),s=r(54990),o=r(40598);let l=e=>{var t,r,l;let{defaultActiveId:i,activeId:d,type:c="pills",size:u="tiny",block:f,onChange:m,onClick:p,scrollable:g,wrappable:x,addOnBefore:b,addOnAfter:h,listClassNames:v,baseClassNames:y,refs:w,children:j}=e,N=s.Children.toArray(j),[C,k]=(0,s.useState)(null!=(l=null!=d?d:i)?l:null==N||null==(r=N[0])||null==(t=r.props)?void 0:t.id);(0,s.useMemo)(()=>{d&&d!==C&&k(d)},[d]);let _=(0,o.A)("tabs");function z(e){null==p||p(e),e!==C&&(null==m||m(e),k(e))}let S=[_[c].list];return g&&S.push(_.scrollable),x&&S.push(_.wrappable),v&&S.push(v),(0,a.jsxs)(n.bL,{value:C,className:[_.base,y].join(" "),ref:null==w?void 0:w.base,children:[(0,a.jsxs)(n.B8,{className:S.join(" "),ref:null==w?void 0:w.list,children:[b,N.map(e=>{let t=C===e.props.id,r=[_[c].base,_.size[u]];return t?r.push(_[c].active):r.push(_[c].inactive),f&&r.push(_.block),(0,a.jsxs)(n.l9,{onKeyDown:t=>{"Enter"===t.key&&(t.preventDefault(),z(e.props.id))},onClick:()=>z(e.props.id),value:e.props.id,className:r.join(" "),children:[e.props.icon,(0,a.jsx)("span",{children:e.props.label}),e.props.iconRight]},"".concat(e.props.id,"-tab-button"))}),h]}),N]})};l.Panel=e=>{let{children:t,id:r,className:s}=e,l=(0,o.A)("tabs");return(0,a.jsx)(n.UC,{value:r,className:[l.content,s].join(" "),children:t})};let i=l},31829:(e,t,r)=>{"use strict";r.d(t,{ButtonGroup:()=>l,ButtonGroupItem:()=>i});var a=r(31710),n=r(54990),s=r(35484),o=r(35621);let l=n.forwardRef((e,t)=>{let{className:r,children:n,...o}=e;return(0,a.jsx)("div",{ref:t,className:(0,s.cn)("flex flex-col items-stretch border border-control rounded-md overflow-hidden",r),...o,children:n})});l.displayName="ButtonGroup";let i=n.forwardRef((e,t)=>{let{className:r,children:n,icon:l,asChild:i,...d}=e;return(0,a.jsx)(o.Button,{ref:t,type:"text",icon:l,asChild:i,className:(0,s.cn)("h-auto py-2 rounded-none justify-start border-0 border-b border-border last:border-b-0",r),...d,children:n})});i.displayName="ButtonGroupItem"},33440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Slider:()=>l});var a=r(31710),n=r(87730),s=r(54990),o=r(35484);let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsxs)(n.bL,{ref:t,className:(0,o.cn)("relative flex w-full touch-none select-none items-center",r),...s,children:[(0,a.jsx)(n.CC,{className:"relative h-1 w-full grow overflow-hidden rounded-full bg-surface-300",children:(0,a.jsx)(n.Q6,{className:"absolute h-full bg-foreground-muted"})}),(0,a.jsx)(n.zi,{className:"block h-5 w-5 rounded-full border-2 border-background-surface-100 bg-foreground ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]})});l.displayName=n.bL.displayName},34265:(e,t,r)=>{"use strict";r.r(t),r.d(t,{SimpleCodeBlock:()=>u});var a=r(31710),n=r(47375),s=r(98840),o=r(54990),l=r(35621),i=r(35484),d=r(81401);!function(e){let t=[/\b(?:async|sync|yield)\*/,/\b(?:abstract|assert|async|await|break|case|catch|class|const|continue|covariant|default|deferred|do|dynamic|else|enum|export|extends|extension|external|factory|final|finally|for|get|hide|if|implements|import|in|interface|library|mixin|new|null|on|operator|part|rethrow|return|set|show|static|super|switch|sync|this|throw|try|typedef|var|void|while|with|yield)\b/],r=/(^|[^\w.])(?:[a-z]\w*\s*\.\s*)*(?:[A-Z]\w*\s*\.\s*)*/.source,a={pattern:RegExp(r+/[A-Z](?:[\d_A-Z]*[a-z]\w*)?\b/.source),lookbehind:!0,inside:{namespace:{pattern:/^[a-z]\w*(?:\s*\.\s*[a-z]\w*)*(?:\s*\.)?/,inside:{punctuation:/\./}}}};e.languages.dart=e.languages.extend("clike",{"class-name":[a,{pattern:RegExp(r+/[A-Z]\w*(?=\s+\w+\s*[;,=()])/.source),lookbehind:!0,inside:a.inside}],keyword:t,operator:/\bis!|\b(?:as|is)\b|\+\+|--|&&|\|\||<<=?|>>=?|~(?:\/=?)?|[+\-*\/%&^|=!<>]=?|\?/}),e.languages.insertBefore("dart","string",{"string-literal":{pattern:/r?(?:("""|''')[\s\S]*?\1|(["'])(?:\\.|(?!\2)[^\\\r\n])*\2(?!\2))/,greedy:!0,inside:{interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$(?:\w+|\{(?:[^{}]|\{[^{}]*\})*\})/,lookbehind:!0,inside:{punctuation:/^\$\{?|\}$/,expression:{pattern:/[\s\S]+/,inside:e.languages.dart}}},string:/[\s\S]+/}},string:void 0}),e.languages.insertBefore("dart","class-name",{metadata:{pattern:/@\w+/,alias:"function"}}),e.languages.insertBefore("dart","class-name",{generics:{pattern:/<(?:[\w\s,.&?]|<(?:[\w\s,.&?]|<(?:[\w\s,.&?]|<[\w\s,.&?]*>)*>)*>)*>/,inside:{"class-name":a,keyword:t,punctuation:/[<>(),.:]/,operator:/[?&|]/}}})}(s.My);let c={defaultLanguage:"js"},u=e=>{var t;let{children:r,parentClassName:u,className:f,showCopy:m=!0}=e,{resolvedTheme:p}=(0,n.D)(),[g,x]=(0,o.useState)(!1),b=(0,o.useRef)(null),h=[];(0,o.useEffect)(()=>{if(!g)return;let e=setTimeout(()=>x(!1),2e3);return()=>clearTimeout(e)},[g]);let v=f&&f.replace(/language-/,"");!v&&c.defaultLanguage&&(v=c.defaultLanguage);let y=e=>{(0,d.l)(e,()=>x(!0))};return(0,a.jsx)(s.f4,{theme:"dark"===p?s.Zj.nightOwl:s.Zj.nightOwlLight,code:null!=(t=null==r?void 0:r.trim())?t:"",language:v,children:e=>{let{className:t,tokens:n,getLineProps:s,getTokenProps:o}=e;return(0,a.jsxs)("div",{className:"Code codeBlockWrapper group",children:[(0,a.jsx)("pre",{ref:b,className:(0,i.cn)("codeBlock",t,u),children:n.map((e,t)=>{let r=s({line:e,key:t});return h.includes(t+1)&&(r.className="".concat(r.className," docusaurus-highlight-code-line")),(0,a.jsx)("div",{...r,children:e.map((e,t)=>(0,a.jsx)("span",{...o({token:e,key:t})},t))},t)})}),m&&(0,a.jsx)("div",{className:"invisible absolute right-0 top-0 opacity-0 transition-opacity group-hover:visible group-hover:opacity-100",children:(0,a.jsx)(l.Button,{size:"tiny",type:"default",onClick:()=>y(r),children:g?"Copied":"Copy"})})]})}})}},34727:(e,t,r)=>{"use strict";r.d(t,{H:()=>o});var a=r(31710);r(54990);var n=r(12255),s=r(35484);function o(e){let{align:t="left",children:r,className:o,descriptionText:l,error:i,id:d,label:c,labelOptional:u,layout:f="vertical",style:m,labelLayout:p,responsive:g=!0,size:x="medium",beforeLabel:b,afterLabel:h,nonBoxInput:v=!c}=e,y=n.A.form_layout,w="flex"===f,j=[];j.push(y.size[x]);let N=[],C=[];"horizontal"===f||p||w?"horizontal"===p?N.push(y.labels_horizontal_layout):N.push(y.labels_vertical_layout):N.push(y.labels_horizontal_layout),"horizontal"!==f?C.push(y.data_input_horizontal_layout):(C.push(y.data_input_vertical_layout),"right"===t&&C.push(y.data_input_vertical_layout__align_right)),w?(j.push(y.flex[t].base),"left"===t&&(N.push(y.flex.left.labels),C.push(y.flex.left.data_input)),"right"===t&&(N.push(y.flex.right.labels),C.push(y.flex.right.data_input))):j.push(y.container,g?y.responsive:y.non_responsive),o&&j.push(o);let k=!!(c||b||h),_=(0,a.jsx)("p",{"data-state":i?"show":"hide",className:[y.error.base,y.error.size[x]].join(" "),children:i}),z=l&&(0,a.jsx)("div",{className:[y.description.base,y.description.size[x]].join(" "),id:d+"-description",children:l});return(0,a.jsxs)("div",{className:j.join(" "),children:[w&&(0,a.jsx)("div",{className:y.flex[t].content,children:r}),k||u||"horizontal"===f?(0,a.jsxs)("div",{className:N.join(" "),children:[k&&(0,a.jsxs)("label",{className:(0,s.cn)(y.label.base,y.label.size[x],"break-all"),htmlFor:d,children:[b&&(0,a.jsx)("span",{className:[y.label_before.base,y.label_before.size[x]].join(" "),id:d+"-before",children:b}),c,h&&(0,a.jsx)("span",{className:[y.label_after.base,y.label_after.size[x]].join(" "),id:d+"-after",children:h})]}),u&&(0,a.jsx)("span",{className:[y.label_optional.base,y.label_optional.size[x]].join(" "),id:d+"-optional",children:u}),w&&(0,a.jsxs)(a.Fragment,{children:[z,_]})]}):null,!w&&(0,a.jsx)("div",{className:C.join(" "),style:m,children:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:v&&c&&"vertical"===f?y.non_box_data_input_spacing_vertical:v&&c&&"horizontal"===f?y.non_box_data_input_spacing_horizontal:"",children:r}),_,z]})})]})}},34819:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var a=r(31710),n=r(54990),s=r(34727),o=r(76285),l=r(328),i=r(40598),d=r(52994);let c=function(e){let{defaultValue:t,descriptionText:r,error:c,icon:u,inputRef:f,label:m,afterLabel:p,beforeLabel:g,labelOptional:x,layout:b,value:h,actions:v,size:y="medium",validation:w,id:j="",name:N="",...C}=e,k=(0,i.A)("inputNumber"),{formContextOnChange:_,values:z,errors:S,handleBlur:R,touched:A,fieldLevelValidation:L}=(0,d.x)();z&&!h&&(h=z[j||N]),c||(S&&!c&&(c=S[j||N]),c=A&&A[j||N]?c:void 0),(0,n.useEffect)(()=>{w&&L(j,w(h))},[]);let M=[k.base];return c&&M.push(k.variants.error),c||M.push(k.variants.standard),u&&M.push(k.with_icon),y&&M.push(k.size[y]),C.disabled&&M.push(k.disabled),(0,a.jsx)("div",{className:C.className,children:(0,a.jsx)(s.H,{label:m,afterLabel:p,beforeLabel:g,labelOptional:x,layout:b,id:j,error:c,descriptionText:r,style:C.style,size:y,children:(0,a.jsxs)("div",{className:k.container,children:[(0,a.jsx)("input",{"data-size":y,id:j,name:N,onChange:function(e){C.onChange&&C.onChange(e),_&&_(e),w&&L(j,w(e.target.value))},onBlur:function(e){R&&R(e),C.onBlur&&C.onBlur(e)},type:"number",ref:f,value:h,className:M.join(" "),...C}),u&&(0,a.jsx)(l.A,{size:y,icon:u}),c||v?(0,a.jsxs)("div",{className:k.actions_container,children:[c&&(0,a.jsx)(o.A,{size:y}),v&&v]}):null]})})})}},35095:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var a=r(31710),n=r(54990),s=r(35484),o=r(35621),l=r(79099);let i=(0,n.forwardRef)((e,t)=>{let{children:r,customFooter:i,description:d,hideFooter:c=!1,alignFooter:u="left",layout:f="horizontal",loading:m=!1,cancelText:p="Cancel",onConfirm:g=()=>{},onCancel:x=()=>{},confirmText:b="Confirm",showCloseButton:h=!0,footerBackground:v,variant:y="success",visible:w=!1,size:j="large",style:N,overlayStyle:C,contentStyle:k,triggerElement:_,header:z,modal:S,defaultOpen:R,...A}=e,[L,M]=n.useState(!!w&&w);(0,n.useEffect)(()=>{M(w)},[w]);let D=i||(0,a.jsxs)("div",{className:"flex w-full space-x-2",style:{width:"100%",justifyContent:"vertical"===f?"center":"right"===u?"flex-end":"flex-start"},children:[(0,a.jsx)(o.Button,{type:"default",onClick:x,disabled:m,children:p}),(0,a.jsx)(o.Button,{onClick:g,disabled:m,loading:m,type:"danger"===y?"danger":"warning"===y?"warning":"primary",children:b})]});return(0,a.jsxs)(l.Dialog,{open:L,defaultOpen:R,onOpenChange:function(e){void 0===w||e?M(e):x()},modal:S,children:[_&&(0,a.jsx)(l.DialogTrigger,{children:_}),(0,a.jsxs)(l.DialogContent,{ref:t,hideClose:!h,...A,size:j,children:[z||d?(0,a.jsxs)(l.DialogHeader,{className:(0,s.cn)("border-b"),padding:"small",children:[z&&(0,a.jsx)(l.DialogTitle,{children:z}),d&&(0,a.jsx)(l.DialogDescription,{children:d})]}):null,r,!c&&(0,a.jsx)(l.DialogFooter,{padding:"small",children:D})]})]})}),d=(0,n.forwardRef)((e,t)=>{let{...r}=e;return(0,a.jsx)(l.DialogSection,{ref:t,...r,padding:"small",className:(0,s.cn)(r.className)})}),c=(0,n.forwardRef)((e,t)=>{let{...r}=e;return(0,a.jsx)(l.DialogSectionSeparator,{ref:t,...r})});i.Content=d,i.Separator=c;let u=i},36393:(e,t,r)=>{"use strict";r.r(t),r.d(t,{RadioGroupStacked:()=>d,RadioGroupStackedItem:()=>c});var a=r(31710),n=r(74319),s=r(54990),o=r(4743),l=r(3999),i=r(35484);let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.bL,{className:(0,i.cn)("flex flex-col -space-y-px w-full",r),...s,ref:t})});d.displayName="RadioGroupStacked";let c=s.forwardRef((e,t)=>{let{image:r,label:s,showIndicator:d=!0,...c}=e;return(0,a.jsx)(n.q7,{ref:t,...c,className:(0,i.cn)("flex flex-col gap-2","w-full","bg-overlay/50 disabled:opacity-50","border","first-of-type:rounded-t-lg last-of-type:rounded-b-lg","shadow-sm","enabled:hover:bg-surface-300","enabled:hover:border-foreground-muted","enabled:cursor-pointer disabled:cursor-not-allowed","hover:z-[1] focus-visible:z-[1]","data-[state=checked]:z-[1]","data-[state=checked]:ring-1 data-[state=checked]:ring-border","data-[state=checked]:bg-surface-300","data-[state=checked]:border-foreground-muted","transition","group",c.className),children:(0,a.jsxs)("div",{className:"flex gap-3 w-full px-[21px] py-3",children:[d&&(0,a.jsx)("div",{className:(0,i.cn)("aspect-square h-4 w-4 min-w-4 min-h-4","rounded-full border group-data-[state=checked]:border-foreground-muted","group-focus:border-foreground-muted","group-hover:border-foreground-muted","ring-offset-background","group-focus:outline-none","group-focus-visible:ring-2 group-focus-visible:ring-ring group-focus-visible:ring-offset-2","flex items-center justify-center","transition"),children:(0,a.jsx)(n.C1,{className:"flex items-center justify-center",children:(0,a.jsx)(o.A,{className:"h-2.5 w-2.5 fill-current text-current"})})}),(0,a.jsxs)("div",{className:"flex flex-col gap-0.25 items-start",children:[(0,a.jsx)(l.Label,{htmlFor:c.value,className:(0,i.cn)("block","-mt-[0.15rem]","text-sm transition-colors text-left","text-light","enabled:group-hover:text-foreground group-data-[state=checked]:text-foreground"),children:s}),c.description&&(0,a.jsx)("p",{className:"text-sm text-foreground-lighter",children:c.description}),c.children]})]})})});c.displayName="RadioGroupStackedItem"},37505:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var a=r(31710),n=r(54990),s=r(62065),o=r(52994);function l(e,t){if(!t.error)return delete e[t.key],e;if(t)return{...e,[t.key]:t.error};throw Error()}function i(e){let{validate:t,...r}=e,[i,d]=(0,n.useReducer)(l,null),c=(0,s.Wx)({validateOnBlur:!0,...r,validationSchema:r.validationSchema,initialValues:r.initialValues,onSubmit:r.onSubmit,validate:t||function(){return i}});return(0,a.jsx)("form",{id:r.id,name:r.name,onSubmit:c.handleSubmit,className:r.className,style:r.style,method:"POST",children:(0,a.jsx)(o.h,{values:c.values,errors:c.errors,formContextOnChange:c.handleChange,handleBlur:c.handleBlur,touched:c.touched,fieldLevelValidation:function(e,t){d({key:e,error:t})},children:r.children({errors:c.errors,touched:c.touched,isSubmitting:c.isSubmitting,isValidating:c.isValidating,submitCount:c.submitCount,initialValues:c.initialValues,values:c.values,handleReset:c.handleReset,resetForm:c.resetForm,setFieldValue:c.setFieldValue})})})}},38169:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var a=r(31710),n=r(54990),s=r(67468),o=r(35484),l=r(63089);let i=e=>{var t;let{show:r=!0,dismissable:i=!0,className:d,children:c,announcementKey:u}=e,[f,m]=(0,n.useState)(!0),p=(0,s.usePathname)(),g=null!=(t=null==p?void 0:p.includes("launch-week"))&&t;if(!r)return null;let x=u.replace(/ /g,"");return((0,n.useEffect)(function(){"hidden"===window.localStorage.getItem(x)&&m(!0),window.localStorage.getItem(x)||m(!1)},[]),!g&&f)?null:(0,a.jsxs)("div",{className:(0,o.cn)("relative z-40 w-full",d),children:[i&&!g&&(0,a.jsx)("div",{className:"absolute z-50 right-4 flex h-full items-center opacity-100 text-foreground transition-opacity hover:opacity-100",onClick:function(e){return e.stopPropagation(),window.localStorage.setItem(x,"hidden"),m(!0)},children:(0,a.jsx)(l.A,{size:16})}),c]})}},40598:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(54990),n=r(12255),s=r(69975);function o(e){let{theme:{[e]:t}}=(0,a.useContext)(s.ThemeContext);return t||(t=n.A.accordion),t=JSON.parse(t=JSON.stringify(t).replace(/\\n/g,"").replace(/\s\s+/g," "))}},40989:(e,t,r)=>{"use strict";r.r(t),r.d(t,{InputOTP:()=>i,InputOTPGroup:()=>d,InputOTPSeparator:()=>u,InputOTPSlot:()=>c,REGEXP_ONLY_DIGITS_AND_CHARS:()=>n.Ag});var a=r(31710),n=r(73456),s=r(11970),o=r(54990),l=r(35484);let i=o.forwardRef((e,t)=>{let{className:r,containerClassName:s,...o}=e;return(0,a.jsx)(n.wE,{ref:t,containerClassName:(0,l.cn)("flex items-center gap-2 has-[:disabled]:opacity-50",s),className:(0,l.cn)("disabled:cursor-not-allowed",r),...o})});i.displayName="InputOTP";let d=o.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center",r),...n})});d.displayName="InputOTPGroup";let c=o.forwardRef((e,t)=>{let{index:r,className:s,...i}=e,{char:d,hasFakeCaret:c,isActive:u}=o.useContext(n.dK).slots[r];return(0,a.jsxs)("div",{ref:t,className:(0,l.cn)("relative flex h-10 w-10 items-center justify-center border-y border-r border-input text-sm transition-all first:rounded-l-md first:border-l last:rounded-r-md",u&&"z-10 ring-2 ring-ring ring-offset-background",s),...i,children:[d,c&&(0,a.jsx)("div",{className:"pointer-events-none absolute inset-0 flex items-center justify-center",children:(0,a.jsx)("div",{className:"h-4 w-px animate-caret-blink bg-foreground duration-1000"})})]})});c.displayName="InputOTPSlot";let u=o.forwardRef((e,t)=>{let{...r}=e;return(0,a.jsx)("div",{ref:t,role:"separator",...r,children:(0,a.jsx)(s.A,{})})});u.displayName="InputOTPSeparator"},41727:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var a=r(31710),n=r(59024);r(54990);var s=r(40598);let o=e=>{let{open:t,children:r,className:s,...o}=e;return(0,a.jsx)(n.bL,{asChild:o.asChild,defaultOpen:o.defaultOpen,open:t,onOpenChange:o.onOpenChange,disabled:o.disabled,className:s,children:r})};o.Trigger=function(e){let{children:t,asChild:r}=e;return(0,a.jsx)(n.l9,{asChild:r,children:t})},o.Content=function(e){let{asChild:t,children:r,className:o}=e,l=(0,s.A)("collapsible");return(0,a.jsx)(n.UC,{asChild:t,className:[l.content,o].join(" "),children:r})};let l=o},43129:(e,t,r)=>{"use strict";r.d(t,{Form:()=>u,FormControl:()=>h,FormDescription:()=>v,FormField:()=>m,FormItem:()=>x,FormLabel:()=>b,FormMessage:()=>y,useFormField:()=>p,useWatch:()=>o.FH});var a=r(31710),n=r(1351),s=r(54990),o=r(79929),l=r(35484),i=r(3999),d=r(98832),c=r(31568);let u=o.Op,f=s.createContext({}),m=e=>{let{...t}=e;return(0,a.jsx)(f.Provider,{value:{name:t.name},children:(0,a.jsx)(o.xI,{...t})})},p=()=>{let e=s.useContext(f),t=s.useContext(g),{getFieldState:r,formState:a}=(0,o.xW)(),n=r(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...n}},g=s.createContext({}),x=s.forwardRef((e,t)=>{let{asChild:r,...o}=e,l=s.useId(),i=r?n.DX:"div";return(0,a.jsx)(g.Provider,{value:{id:l},children:(0,a.jsx)(i,{ref:t,...o})})});x.displayName="FormItem";let b=s.forwardRef((e,t)=>{let{className:r,...n}=e,{error:s,formItemId:o}=p();return(0,a.jsx)(i.Label,{ref:t,className:(0,l.cn)("text-foreground-light","transition-colors",s&&"!text-destructive",r,"leading-normal"),htmlFor:o,...n})});b.displayName="FormLabel";let h=s.forwardRef((e,t)=>{let{...r}=e,{error:s,formItemId:o,formDescriptionId:l,formMessageId:i}=p();return(0,a.jsx)(n.DX,{ref:t,id:o,"aria-describedby":s?"".concat(l," ").concat(i):"".concat(l),"aria-invalid":!!s,...r})});h.displayName="FormControl";let v=s.forwardRef((e,t)=>{let{className:r,...n}=e,{formDescriptionId:s}=p();return(0,a.jsx)("div",{ref:t,id:s,className:(0,l.cn)("text-sm text-foreground-light",r),...n})});v.displayName="FormDescription";let y=s.forwardRef((e,t)=>{let{className:r,children:n,...s}=e,{error:o,formMessageId:i}=p(),u=o?String(null==o?void 0:o.message):n;return(0,a.jsx)(d.N,{initial:!1,children:u?(0,a.jsx)(c.P.div,{initial:{opacity:0,y:-5,height:0},animate:{opacity:1,y:0,height:"auto"},exit:{opacity:0,y:-5,height:0},transition:{duration:.15,ease:"easeInOut"},children:(0,a.jsx)("p",{ref:t,id:i,className:(0,l.cn)("text-sm text-destructive",r),...s,children:u})},i):null})});y.displayName="FormMessage"},43195:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var a=r(31710),n=r(65980),s=r(54990),o=r(59365),l=r(40598);let i=(0,s.createContext)({chevronAlign:"left",justified:!0,type:"default"});function d(e){let{children:t,className:r,onChange:s,openBehaviour:o="multiple",type:d="default",defaultValue:c,justified:u=!1,chevronAlign:f="left"}=e,m=[(0,l.A)("accordion").variants[d].base];return r&&m.push(r),(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(n.bL,{type:o,onValueChange:function(e){s&&s(e),e==typeof String&&e.split(" ")},defaultValue:c,className:m.join(" "),children:(0,a.jsx)(i.Provider,{value:{chevronAlign:f,justified:u,type:d,defaultValue:c},children:(0,a.jsx)("div",{children:t})})})})}d.Item=function(e){let{children:t,className:r,header:d,id:c,disabled:u}=e,f=(0,l.A)("accordion"),[m,p]=(0,s.useState)(!1),{type:g,justified:x,chevronAlign:b}=(0,s.useContext)(i),h=[f.variants[g].trigger];x&&h.push(f.justified),r&&h.push(r);let v=[f.chevron.base,f.chevron.align[b]];return m&&!u&&v.unshift("!rotate-180"),(0,a.jsxs)(n.q7,{value:c,className:f.variants[g].container,disabled:u,onClick:()=>{p(!m)},children:[(0,a.jsxs)(n.l9,{className:h.join(" "),children:[d,!u&&(0,a.jsx)(o.A,{"aria-hidden":!0,className:v.join(" "),strokeWidth:2})]}),(0,a.jsx)(n.UC,{className:f.variants[g].content,children:(0,a.jsx)("div",{className:f.variants[g].panel,children:t})})]})};let c=d},43575:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Switch:()=>c});var a=r(31710),n=r(31170),s=r(11448),o=r(54990),l=r(35484);let i=(0,s.F)("peer inline-flex shrink-0 cursor-pointer items-center rounded-full border transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-brand data-[state=checked]:hover:bg-brand-600/90 data-[state=unchecked]:bg-control data-[state=unchecked]:hover:bg-border",{variants:{size:{small:"h-[16px] w-[28px]",medium:"h-[20px] w-[34px]",large:"h-[24px] w-[44px]"}},defaultVariants:{size:"medium"}}),d=(0,s.F)("pointer-events-none block rounded-full bg-foreground-lighter data-[state=checked]:bg-white shadow-lg ring-0 transition-transform",{variants:{size:{small:"h-[12px] w-[12px] data-[state=checked]:translate-x-[13px] data-[state=unchecked]:translate-x-[1px]",medium:"h-[16px] w-[16px] data-[state=checked]:translate-x-[15px] data-[state=unchecked]:translate-x-[1px]",large:"h-[18px] w-[18px] data-[state=checked]:translate-x-[22px] data-[state=unchecked]:translate-x-[3px]"}},defaultVariants:{size:"medium"}}),c=o.forwardRef((e,t)=>{let{className:r,size:s,...o}=e;return(0,a.jsx)(n.bL,{className:(0,l.cn)(i({size:s}),r),...o,ref:t,children:(0,a.jsx)(n.zi,{className:(0,l.cn)(d({size:s}))})})});c.displayName=n.bL.displayName},44561:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var a=r(31710),n=r(47323),s=r(54990),o=r(34727),l=r(40598),i=r(52994);let d=function(e){var t;let{disabled:r,id:d="",name:c="",layout:u="flex",error:f,descriptionText:m,label:p,afterLabel:g,beforeLabel:x,labelOptional:b,onChange:h,onBlur:v,defaultChecked:y,checked:w,className:j,align:N="left",size:C="medium",validation:k,labelLayout:_,...z}=e,S=(0,l.A)("toggle"),{formContextOnChange:R,values:A,errors:L,handleBlur:M,touched:D,fieldLevelValidation:F}=(0,i.x)();A&&!w&&(w=A[d||c]);let[I,T]=(0,s.useState)(null!=(t=y||w)&&t),E=null!=w?w:I;(0,s.useEffect)(()=>{T(E)},[]),f||(L&&!f&&(f=L[d||c]),f=D&&D[d||c]?f:void 0);let B=[S.base,S.handle_container[C]];E&&B.push(S.active);let O=[S.handle.base,S.handle[C]];return E&&O.push(S.handle_active[C]),(0,a.jsx)(o.H,{className:j,label:p,afterLabel:g,beforeLabel:x,labelOptional:b,layout:u,id:d,error:f,align:N,descriptionText:m,size:C,labelLayout:_,nonBoxInput:!0,children:(0,a.jsx)("button",{type:"button",id:d,name:c,className:(0,n.A)(...B,r&&"opacity-50 cursor-default"),onClick:function(){h&&h(!E),T(!I);let e={};e.target={type:"checkbox",name:c,id:d,value:!E,checked:!E},R&&R(e),k&&F(d,k(!I))},disabled:r,onBlur:function(e){setTimeout(()=>{M&&M(e)},100),v&&v(e)},...z,children:(0,a.jsx)("span",{"aria-hidden":"true",className:O.join(" ")})})})}},46845:(e,t,r)=>{"use strict";r.d(t,{Collapsible:()=>n,CollapsibleContent:()=>o,CollapsibleTrigger:()=>s});var a=r(59024);let n=a.bL,s=a.R6,o=a.Ke},51834:(e,t,r)=>{"use strict";r.d(t,{RadioGroup:()=>i,RadioGroupItem:()=>d,RadioGroupLargeItem:()=>c});var a=r(31710),n=r(74319),s=r(4743),o=r(54990),l=r(35484);let i=o.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.bL,{className:(0,l.cn)("grid gap-2",r),...s,ref:t})});i.displayName=n.bL.displayName;let d=o.forwardRef((e,t)=>{let{className:r,children:o,...i}=e;return(0,a.jsx)(n.q7,{ref:t,className:(0,l.cn)("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),...i,children:(0,a.jsx)(n.C1,{className:"flex items-center justify-center",children:(0,a.jsx)(s.A,{className:"h-2.5 w-2.5 fill-current text-current"})})})});d.displayName=n.q7.displayName;let c=o.forwardRef((e,t)=>{let{image:r,label:s,showIndicator:o=!0,...i}=e;return(0,a.jsxs)(n.q7,{ref:t,...i,className:(0,l.cn)("flex flex-col gap-2","w-48","bg-surface-200","rounded-md border border-strong","p-2","shadow-sm","hover:border-stronger hover:bg-surface-300","data-[state=checked]:border-primary","data-[state=checked]:ring-1 data-[state=checked]:ring-border","data-[state=checked]:bg-selection data-[state=checked]:border-foreground","transition-colors","group",i.className),children:[i.children,(0,a.jsxs)("div",{className:"flex gap-2 w-full",children:[o&&(0,a.jsxs)("div",{className:"relative w-3 h-3 min-w-3 mt-0.5",children:[(0,a.jsx)(n.C1,{className:(0,l.cn)("absolute","w-[10px] h-[10px]","left-[1px] top-[1px]","border border-background-surface-300","rounded-full","data-[state=checked]:border-background-surface-300","data-[state=checked]:ring-foreground","data-[state=checked]:bg-foreground")}),(0,a.jsx)("div",{className:(0,l.cn)("absolute","w-3 h-3","border border-stronger","rounded-full","group-hover:border-foreground-light","group-data-[state=checked]:border-foreground","transition-colors")})]}),(0,a.jsx)("label",{htmlFor:i.value,className:(0,l.cn)("text-xs transition-colors text-left","text-light","group-hover:text-foreground group-data-[state=checked]:text-foreground",i.disabled?"cursor-not-allowed":"cursor-pointer"),children:s})]})]})});c.displayName=n.q7.displayName},52336:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Avatar:()=>l,AvatarFallback:()=>d,AvatarImage:()=>i});var a=r(31710),n=r(70547),s=r(54990),o=r(35484);let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.bL,{ref:t,className:(0,o.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",r),...s})});l.displayName=n.bL.displayName;let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n._V,{ref:t,className:(0,o.cn)("aspect-square h-full w-full",r),...s})});i.displayName=n._V.displayName;let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.H4,{ref:t,className:(0,o.cn)("flex h-full w-full items-center justify-center rounded-full bg-surface-100 border",r),...s})});d.displayName=n.H4.displayName},52994:(e,t,r)=>{"use strict";r.d(t,{h:()=>o,x:()=>l});var a=r(31710),n=r(54990);let s=(0,n.createContext)({formContextOnChange:null,values:null,errors:null,handleBlur:null,touched:null,fieldLevelValidation:null}),o=e=>{let{formContextOnChange:t,values:r,errors:n,handleBlur:o,touched:l,fieldLevelValidation:i}=e;return(0,a.jsx)(s.Provider,{value:{formContextOnChange:t,values:r,errors:n,handleBlur:o,touched:l,fieldLevelValidation:i},children:e.children})},l=()=>{let e=(0,n.useContext)(s);if(void 0===e)throw Error("useFormContextOnChange must be used within a FormContextProvider.");return e}},53176:(e,t,r)=>{"use strict";r.r(t),r.d(t,{DropdownMenu:()=>c,DropdownMenuCheckboxItem:()=>y,DropdownMenuContent:()=>h,DropdownMenuGroup:()=>f,DropdownMenuItem:()=>v,DropdownMenuLabel:()=>j,DropdownMenuPortal:()=>m,DropdownMenuRadioGroup:()=>g,DropdownMenuRadioItem:()=>w,DropdownMenuSeparator:()=>N,DropdownMenuShortcut:()=>C,DropdownMenuSub:()=>p,DropdownMenuSubContent:()=>b,DropdownMenuSubTrigger:()=>x,DropdownMenuTrigger:()=>u});var a=r(31710),n=r(46067),s=r(12861),o=r(60601),l=r(4743),i=r(54990),d=r(35484);let c=e=>{let{modal:t=!1,...r}=e;return(0,a.jsx)(n.bL,{modal:t,...r})},u=n.l9,f=n.YJ,m=n.ZL,p=n.Pb,g=n.z6,x=i.forwardRef((e,t)=>{let{className:r,inset:o,children:l,...i}=e;return(0,a.jsxs)(n.ZP,{ref:t,className:(0,d.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-xs outline-none focus:bg-overlay-hover data-[state=open]:bg-overlay-hover data-[state=open]:text-strong",o&&"pl-8",r),...i,children:[l,(0,a.jsx)(s.A,{className:"h-4 w-4 !ml-auto"})]})});x.displayName=n.ZP.displayName;let b=i.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.G5,{ref:t,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border border-overlay bg-overlay p-1 text-foreground-light shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...s})});b.displayName=n.G5.displayName;let h=i.forwardRef((e,t)=>{let{className:r,sideOffset:s=4,...o}=e;return(0,a.jsx)(n.ZL,{children:(0,a.jsx)(n.UC,{ref:t,sideOffset:s,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border border-overlay bg-overlay p-1 text-foreground-light shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 w-64",r),...o})})});h.displayName=n.UC.displayName;let v=i.forwardRef((e,t)=>{let{className:r,inset:s,...o}=e;return(0,a.jsx)(n.q7,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-xs outline-none transition-colors focus:bg-overlay-hover focus:text-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s&&"pl-8",r),...o})});v.displayName=n.q7.displayName;let y=i.forwardRef((e,t)=>{let{className:r,children:s,checked:l,...i}=e;return(0,a.jsxs)(n.H_,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-xs outline-none transition-colors focus:bg-overlay-hover focus:text-strong data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),checked:l,...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.VF,{children:(0,a.jsx)(o.A,{className:"h-4 w-4"})})}),s]})});y.displayName=n.H_.displayName;let w=i.forwardRef((e,t)=>{let{className:r,children:s,...o}=e;return(0,a.jsxs)(n.hN,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-xs outline-none transition-colors focus:bg-overlay-hover focus:text-strong data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...o,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.VF,{children:(0,a.jsx)(l.A,{className:"h-2 w-2 fill-current"})})}),s]})});w.displayName=n.hN.displayName;let j=i.forwardRef((e,t)=>{let{className:r,inset:s,...o}=e;return(0,a.jsx)(n.JU,{ref:t,className:(0,d.cn)("px-2 py-1.5 text-xs text-foreground-lighter",s&&"pl-8",r),...o})});j.displayName=n.JU.displayName;let N=i.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-border-overlay",r),...s})});N.displayName=n.wv.displayName;let C=e=>{let{className:t,...r}=e;return(0,a.jsx)("span",{className:(0,d.cn)("ml-auto text-xs tracking-widest opacity-60",t),...r})};C.displayName="DropdownMenuShortcut"},55782:(e,t,r)=>{"use strict";r.r(t),r.d(t,{CodeBlock:()=>R});var a=r(31710),n=r(66096),s=r.n(n),o=r(60601),l=r(56694),i=r(47375),d=r(54990),c=r(53896),u=r(71314),f=r(35484),m=r(35621);let p=e=>({hljs:{display:"block",overflowX:"auto",color:e?"#ddd":"#888"},"hljs-tag":{color:"#569cd6"},"hljs-keyword":{color:"#569cd6",fontWeight:"normal"},"hljs-selector-tag":{color:"#569cd6",fontWeight:"normal"},"hljs-literal":{color:"#569cd6",fontWeight:"normal"},"hljs-strong":{color:"#569cd6"},"hljs-name":{color:"#569cd6"},"hljs-code":{color:"#66d9ef"},"hljs-class .hljs-title":{color:"gray"},"hljs-attribute":{color:"#bf79db"},"hljs-symbol":{color:"#bf79db"},"hljs-regexp":{color:"#bf79db"},"hljs-link":{color:"#bf79db"},"hljs-string":{color:"#3ECF8E"},"hljs-bullet":{color:"#3ECF8E"},"hljs-subst":{color:"#3ECF8E"},"hljs-title":{color:"#3ECF8E",fontWeight:"normal"},"hljs-section":{color:"#3ECF8E",fontWeight:"normal"},"hljs-emphasis":{color:"#3ECF8E"},"hljs-type":{color:"#3ECF8E",fontWeight:"normal"},"hljs-built_in":{color:"#3ECF8E"},"hljs-builtin-name":{color:"#3ECF8E"},"hljs-selector-attr":{color:"#3ECF8E"},"hljs-selector-pseudo":{color:"#3ECF8E"},"hljs-addition":{color:"#3ECF8E"},"hljs-variable":{color:"#3ECF8E"},"hljs-template-tag":{color:"#3ECF8E"},"hljs-template-variable":{color:"#3ECF8E"},"hljs-comment":{color:e?"#999":"#888"},"hljs-quote":{color:"#75715e"},"hljs-deletion":{color:"#75715e"},"hljs-meta":{color:"#75715e"},"hljs-doctag":{fontWeight:"normal"},"hljs-selector-id":{fontWeight:"normal"}});var g=r(80352),x=r.n(g),b=r(7331),h=r(790),v=r(7432),y=r(19695),w=r(64021),j=r(80414),N=r(79143),C=r(49532),k=r(17735),_=r(99227),z=r(56107),S=r(79878);let R=e=>{var t,r,n,g;let{title:R,language:A,linesToHighlight:L=[],highlightBorder:M,styleConfig:D,className:F,wrapperClassName:I,value:T,theme:E,children:B,hideCopy:O=!1,hideLineNumbers:P=!1,wrapLines:V=!0,renderer:G,focusable:H=!0,onCopyCallback:Z=s()}=e,{resolvedTheme:W}=(0,i.D)(),U=null==W?void 0:W.includes("dark"),q=null!=E?E:p(U),[J,Y]=(0,d.useState)(!1),X=()=>{Y(!0),Z(),setTimeout(()=>{Y(!1)},1e3)},K=d.Children.toArray(B),[$]=1===K.length?K:[],Q=null!=(r=null!=T?T:"string"==typeof $?$:void 0)?r:B,ee="string"==typeof(Q=null!=(n=null==Q||null==(t=Q.trimEnd)?void 0:t.call(Q))?n:Q)&&Q.length<70?"short-inline-codeblock":"",et=A||(F?F.replace("language-",""):"js");"jsx"===et&&(et="js"),u.A.registerLanguage("js",j.A),u.A.registerLanguage("ts",S.A),u.A.registerLanguage("py",_.A),u.A.registerLanguage("sql",z.A),u.A.registerLanguage("bash",b.A),u.A.registerLanguage("dart",v.A),u.A.registerLanguage("csharp",h.A),u.A.registerLanguage("json",N.A),u.A.registerLanguage("kotlin",C.A),u.A.registerLanguage("curl",x()),u.A.registerLanguage("http",w.A),u.A.registerLanguage("php",k.A),u.A.registerLanguage("python",_.A),u.A.registerLanguage("go",y.A),("bash"===et||"sh"===et)&&(P=!0);let er=!P;return(0,a.jsxs)(a.Fragment,{children:[R&&(0,a.jsx)("div",{className:"text-sm rounded-t-md bg-surface-100 py-2 px-4 border border-b-0 border-default font-sans",children:R}),F?(0,a.jsxs)("div",{className:(0,f.cn)("group relative max-w-[90vw] md:max-w-none overflow-auto",I),children:[(0,a.jsx)(u.A,{suppressContentEditableWarning:!0,language:et,wrapLines:V,style:q,className:(0,f.cn)("code-block border border-surface p-4 w-full !my-0 !bg-surface-100 outline-none focus:border-foreground-lighter/50","".concat(R?"rounded-t-none rounded-b-md":"rounded-md"),"".concat(er?"":"pl-6"),F),customStyle:{fontSize:13,lineHeight:1.4},showLineNumbers:er,lineProps:e=>L.includes(e)?{style:{display:"block",backgroundColor:(null==D?void 0:D.highlightBackgroundColor)?null==D?void 0:D.highlightBackgroundColor:"hsl(var(--background-selection))",borderLeft:M?"1px solid ".concat((null==D?void 0:D.highlightBorderColor)?null==D?void 0:D.highlightBorderColor:"hsl(var(--foreground-default)",")"):null},class:"hljs-line-highlight"}:{},lineNumberContainerStyle:{paddingTop:"128px"},lineNumberStyle:{minWidth:"44px",paddingLeft:"4px",paddingRight:"4px",marginRight:"12px",color:null!=(g=null==D?void 0:D.lineNumber)?g:"#828282",textAlign:"center",fontSize:12,paddingTop:"4px",paddingBottom:"4px"},renderer:G,contentEditable:H,onBeforeInput:e=>(e.preventDefault(),!1),onKeyDown:e=>{if("Backspace"===e.code)return e.preventDefault(),!1},children:Q}),!O&&(T||B)&&F?(0,a.jsx)("div",{className:["absolute right-2 top-2","opacity-0 group-hover:opacity-100 transition","".concat(U?"dark":"")].join(" "),children:(0,a.jsx)(c.CopyToClipboard,{text:T||B,children:(0,a.jsx)(m.Button,{type:"default",className:"px-1.5",icon:J?(0,a.jsx)(o.A,{}):(0,a.jsx)(l.A,{}),onClick:()=>X(),children:J?"Copied":""})})}):null]}):(0,a.jsx)("code",{className:ee,children:T||B})]})}},55813:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ChartContainer:()=>f,ChartLegend:()=>x,ChartLegendContent:()=>b,ChartStyle:()=>m,ChartTooltip:()=>p,ChartTooltipContent:()=>g});var a=r(31710),n=r(54990),s=r(51706),o=r(36513),l=r(18873),i=r(35484);let d={light:"",dark:".dark"},c=n.createContext(null);function u(){let e=n.useContext(c);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}let f=n.forwardRef((e,t)=>{let{id:r,className:o,children:l,config:d={},...u}=e,f=n.useId(),p="chart-".concat(r||f.replace(/:/g,""));return(0,a.jsx)(c.Provider,{value:{config:d},children:(0,a.jsxs)("div",{"data-chart":p,ref:t,className:(0,i.cn)("flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-foreground-muted [&_.recharts-cartesian-grid_line]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line-line]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none",o),...u,children:[(0,a.jsx)(m,{id:p,config:d}),(0,a.jsx)(s.u,{children:l})]})})});f.displayName="Chart";let m=e=>{let{id:t,config:r}=e,n=Object.entries(r).filter(e=>{let[t,r]=e;return r.theme||r.color});return n.length?(0,a.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(d).map(e=>{let[r,a]=e;return"\n".concat(a," [data-chart=").concat(t,"] {\n").concat(n.map(e=>{var t;let[a,n]=e,s=(null==(t=n.theme)?void 0:t[r])||n.color;return s?"  --color-".concat(a,": ").concat(s,";"):null}).join("\n"),"\n}\n")}).join("\n")}}):null},p=o.m,g=n.forwardRef((e,t)=>{let{active:r,payload:s,className:o,indicator:l="dot",hideLabel:d=!1,hideIndicator:c=!1,label:f,labelFormatter:m,labelSuffix:p,labelClassName:g,formatter:x,color:b,nameKey:v,labelKey:y}=e,{config:w}=u(),j=n.useMemo(()=>{var e;if(d||!(null==s?void 0:s.length))return null;let[t]=s,r="".concat(y||t.dataKey||t.name||"value"),n=h(w,t,r),o=y||"string"!=typeof f?null==n?void 0:n.label:(null==(e=w[f])?void 0:e.label)||f;return m?(0,a.jsx)("div",{className:(0,i.cn)("font-medium",g),children:m(o,s)}):o?(0,a.jsx)("div",{className:(0,i.cn)("font-medium",g),children:o}):null},[f,m,s,d,g,w,y]);if(!r||!(null==s?void 0:s.length))return null;let N=1===s.length&&"dot"!==l;return(0,a.jsxs)("div",{ref:t,className:(0,i.cn)("grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg px-2.5 py-1.5 text-xs shadow-xl",o),children:[N?null:j,(0,a.jsx)("div",{className:"grid gap-1.5",children:s.map((e,t)=>{let r="".concat(v||e.name||e.dataKey||"value"),n=h(w,e,r),s=b||e.payload.fill||e.color;return(0,a.jsx)("div",{className:(0,i.cn)("flex w-full items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-foreground-muted","dot"===l&&"items-center"),children:x&&(null==e?void 0:e.value)!==void 0&&e.name?x(e.value,e.name,e,t,e.payload):(0,a.jsxs)(a.Fragment,{children:[(null==n?void 0:n.icon)?(0,a.jsx)(n.icon,{}):!c&&(0,a.jsx)("div",{className:(0,i.cn)("shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]",{"h-2.5 w-2.5":"dot"===l,"w-1":"line"===l,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===l,"my-0.5":N&&"dashed"===l}),style:{"--color-bg":s,"--color-border":s}}),(0,a.jsxs)("div",{className:(0,i.cn)("flex flex-1 justify-between leading-none",N?"items-end":"items-center"),children:[(0,a.jsxs)("div",{className:"grid gap-1.5",children:[N?j:null,(0,a.jsx)("span",{className:"text-foreground-light",children:(null==n?void 0:n.label)||e.name})]}),e.value&&(0,a.jsxs)("span",{className:"font-mono font-medium tabular-nums text-foreground",children:[e.value.toLocaleString(),p]})]})]})},e.dataKey)})})]})});g.displayName="ChartTooltip";let x=l.s,b=n.forwardRef((e,t)=>{let{className:r,hideIcon:n=!1,payload:s,verticalAlign:o="bottom",nameKey:l}=e,{config:d}=u();return(null==s?void 0:s.length)?(0,a.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center justify-center gap-4","top"===o?"pb-3":"pt-3",r),children:s.map(e=>{let t="".concat(l||e.dataKey||"value"),r=h(d,e,t);return(0,a.jsxs)("div",{className:(0,i.cn)("flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-foreground-muted"),children:[(null==r?void 0:r.icon)&&!n?(0,a.jsx)(r.icon,{}):(0,a.jsx)("div",{className:"h-2 w-2 shrink-0 rounded-[2px]",style:{backgroundColor:e.color}}),null==r?void 0:r.label]},e.value)})}):null});function h(e,t,r){if("object"!=typeof t||null===t)return;let a="payload"in t&&"object"==typeof t.payload&&null!==t.payload?t.payload:void 0,n=r;return r in t&&"string"==typeof t[r]?n=t[r]:a&&r in a&&"string"==typeof a[r]&&(n=a[r]),n in e?e[n]:e[r]}b.displayName="ChartLegend"},57782:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Progress:()=>l});var a=r(31710),n=r(89082),s=r(54990),o=r(35484);let l=s.forwardRef((e,t)=>{let{className:r,value:s,...l}=e;return(0,a.jsx)(n.bL,{ref:t,className:(0,o.cn)("relative h-1 w-full overflow-hidden rounded-full bg-surface-300",r),...l,children:(0,a.jsx)(n.C1,{className:"h-full w-full flex-1 bg-foreground transition-all",style:{transform:"translateX(-".concat(100-(s||0),"%)")}})})});l.displayName=n.bL.displayName},62007:(e,t,r)=>{"use strict";r.d(t,{Tabs:()=>l,TabsContent:()=>c,TabsList:()=>i,TabsTrigger:()=>d});var a=r(31710),n=r(54151),s=r(54990),o=r(35484);let l=n.bL,i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.B8,{ref:t,className:(0,o.cn)("flex items-center border-b",r),...s})});i.displayName=n.B8.displayName;let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.l9,{ref:t,className:(0,o.cn)("inline-flex items-center justify-center whitespace-nowrap py-1.5 text-sm  ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:text-foreground data-[state=active]:shadow-sm text-foreground-lighter hover:text-foreground data-[state=active]:border-foreground border-b-2 border-transparent","group",r),...s})});d.displayName=n.l9.displayName;let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.UC,{ref:t,className:(0,o.cn)("mt-4 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",r),...s})});c.displayName=n.UC.displayName},62800:(e,t,r)=>{"use strict";r.d(t,{ContextMenu:()=>c,ContextMenuCheckboxItem:()=>y,ContextMenuContent:()=>h,ContextMenuGroup:()=>f,ContextMenuItem:()=>v,ContextMenuLabel:()=>j,ContextMenuPortal:()=>m,ContextMenuRadioGroup:()=>g,ContextMenuRadioItem:()=>w,ContextMenuSeparator:()=>N,ContextMenuShortcut:()=>C,ContextMenuSub:()=>p,ContextMenuSubContent:()=>b,ContextMenuSubTrigger:()=>x,ContextMenuTrigger:()=>u});var a=r(31710),n=r(26788),s=r(12861),o=r(60601),l=r(4743),i=r(54990),d=r(35484);let c=n.bL,u=n.l9,f=n.YJ,m=n.ZL,p=n.Pb,g=n.z6,x=i.forwardRef((e,t)=>{let{className:r,inset:o,children:l,...i}=e;return(0,a.jsxs)(n.ZP,{ref:t,className:(0,d.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-xs outline-none focus:bg-selection focus:text-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground",o&&"pl-8",r),...i,children:[l,(0,a.jsx)(s.A,{className:"ml-auto h-4 w-4"})]})});x.displayName=n.ZP.displayName;let b=i.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.G5,{ref:t,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-overlay p-1 text-foreground-light shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...s})});b.displayName=n.G5.displayName;let h=i.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.ZL,{children:(0,a.jsx)(n.UC,{ref:t,className:(0,d.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-overlay p-1 text-foreground-light shadow-md animate-in fade-in-80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...s})})});h.displayName=n.UC.displayName;let v=i.forwardRef((e,t)=>{let{className:r,inset:s,...o}=e;return(0,a.jsx)(n.q7,{ref:t,className:(0,d.cn)("relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-xs outline-none focus:bg-selection focus:text-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s&&"pl-8",r),...o})});v.displayName=n.q7.displayName;let y=i.forwardRef((e,t)=>{let{className:r,children:s,checked:l,...i}=e;return(0,a.jsxs)(n.H_,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-xs outline-none focus:bg-selection focus:text-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),checked:l,...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.VF,{children:(0,a.jsx)(o.A,{className:"h-4 w-4"})})}),s]})});y.displayName=n.H_.displayName;let w=i.forwardRef((e,t)=>{let{className:r,children:s,...o}=e;return(0,a.jsxs)(n.hN,{ref:t,className:(0,d.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-xs outline-none focus:bg-selection focus:text-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...o,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.VF,{children:(0,a.jsx)(l.A,{className:"h-2 w-2 fill-current"})})}),s]})});w.displayName=n.hN.displayName;let j=i.forwardRef((e,t)=>{let{className:r,inset:s,...o}=e;return(0,a.jsx)(n.JU,{ref:t,className:(0,d.cn)("px-2 py-1.5 text-xs text-foreground-light",s&&"pl-8",r),...o})});j.displayName=n.JU.displayName;let N=i.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.wv,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-border",r),...s})});N.displayName=n.wv.displayName;let C=e=>{let{className:t,...r}=e;return(0,a.jsx)("span",{className:(0,d.cn)("ml-auto text-xs tracking-widest text-muted-foreground",t),...r})};C.displayName="ContextMenuShortcut"},69102:e=>{e.exports={"popover-trigger-width":"popover_popover-trigger-width__O8y9q"}},69975:(e,t,r)=>{"use strict";r.d(t,{ThemeContext:()=>s});var a=r(54990),n=r(12255);let s=(0,a.createContext)({theme:n.A})},71208:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var a=r(31710),n=r(54990),s=r(34727),o=r(40598),l=r(52994);let i=(0,n.createContext)({parentCallback:e=>{},type:"",name:"",activeId:"",parentSize:""});function d(e){var t;let{id:r=(t=46656*Math.random()|0,("00000"+(46656*Math.random()|0).toString(36)).slice(-3)+("00000"+t.toString(36)).slice(-3)),disabled:n,value:s,label:d,afterLabel:c,beforeLabel:u,description:f,name:m,checked:p,className:g,onChange:x,onBlur:b,hidden:h=!1,size:v="medium",align:y="vertical",optionalLabel:w,addOnBefore:j,children:N}=e,C=(0,o.A)("radio"),{handleBlur:k}=(0,l.x)();function _(e){k&&k(e),b&&b(e)}return(0,a.jsx)(i.Consumer,{children:e=>{let{parentCallback:t,type:o,name:l,activeId:i,parentSize:b}=e;v=b||v;let k=i===r||!!p||!1!==p&&void 0,z=[g,C.variants[o].container.base,"list"===o&&!h&&C.variants[o].container.size[v]];return z.push(C.variants[o].base),z.push(C.variants[o].size[v]),k?z.push(C.variants[o].active):z.push(C.variants[o].inactive),n&&z.push(C.disabled),"list"!==o&&(h=!0),(0,a.jsxs)("label",{htmlFor:r,className:z.join(" "),children:[(0,a.jsx)("input",{id:r,name:l||m,type:"radio",className:[C.base,C.size[v],h&&C.hidden,C.variants[o].radio_offset,""].join(" "),checked:k,disabled:n,value:s||r,onChange:e=>{t&&t(e),x&&x(e)},onBlur:_}),j,N||(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:[C.label.base,C.label[v],C.variants[o].container.align[y]].join(" "),children:[u&&(0,a.jsx)("div",{className:[C.label_before.base,C.label_before[v]].join(" "),children:u}),(0,a.jsx)("div",{children:d}),c&&(0,a.jsx)("div",{className:[C.label_after.base,C.label_after[v]].join(" "),children:c}),f&&(0,a.jsx)("div",{className:[C.description.base,C.description[v]].join(" "),children:f})]}),w&&(0,a.jsx)("div",{className:[C.optionalLabel.base,C.optionalLabel[v]].join(" "),children:w})]})]})}})}d.Group=function(e){let{id:t,layout:r,error:c,descriptionText:u,label:f,afterLabel:m,beforeLabel:p,labelOptional:g,children:x,className:b,type:h="list",options:v,value:y,name:w,onChange:j,size:N="medium",validation:C,groupClassName:k,labelsLayout:_="vertical"}=e,[z,S]=(0,n.useState)(""),R=(0,o.A)("radio"),{formContextOnChange:A,values:L,errors:M,touched:D,fieldLevelValidation:F}=(0,l.x)();return L&&!y&&(y=L[t||w]),c||(M&&!c&&(c=M[t||w]),c=D&&D[t||w]?c:void 0),(0,n.useEffect)(()=>{C&&F(t,C(y))},[]),(0,n.useEffect)(()=>{S(y)},[y]),(0,a.jsx)("fieldset",{name:w,className:b,children:(0,a.jsx)(s.H,{nonBoxInput:!0,label:f,afterLabel:m,beforeLabel:p,labelOptional:g,layout:r,id:t,error:c,descriptionText:u,size:N,labelLayout:_,children:(0,a.jsx)("div",{className:k||R.variants[h].group,children:(0,a.jsx)(i.Provider,{value:{parentCallback:function(e){j&&j(e),A&&A(e),C&&F(t,C(e.target.value)),S(e.target.id)},type:h,name:w,activeId:z,parentSize:N},children:v?v.map(e=>(0,a.jsx)(d,{id:e.id,label:e.label,beforeLabel:e.beforeLabel,afterLabel:e.afterLabel,value:e.value,description:e.description})):x})})})})};let c=d},72451:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Toggle:()=>d,toggleVariants:()=>i});var a=r(31710),n=r(48030),s=r(11448),o=r(54990),l=r(35484);let i=(0,s.F)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors data-[state=on]:bg-accent data-[state=on]:bg-surface-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ring-offset-background bg-surface-200 hover:bg-surface-300 px-3 py-1 h-auto text-foreground transition-all",{variants:{variant:{default:"bg-transparent",outline:"bg-transparent border border-control hover:bg-accent hover:text-accent-foreground"},size:{default:"h-10 px-3",sm:"h-9 px-2.5",lg:"h-11 px-5"}},defaultVariants:{variant:"default",size:"default"}}),d=o.forwardRef((e,t)=>{let{className:r,variant:s,size:o,...d}=e;return(0,a.jsx)(n.b,{ref:t,className:(0,l.cn)(i({variant:s,size:o,className:r})),...d})});d.displayName=n.b.displayName},73812:(e,t,r)=>{"use strict";r.r(t),r.d(t,{RadioGroupCard:()=>i,RadioGroupCardItem:()=>d});var a=r(31710),n=r(74319),s=r(4743),o=r(54990),l=r(35484);let i=o.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.bL,{className:(0,l.cn)("grid gap-2",r),...s,ref:t})});i.displayName=n.bL.displayName;let d=o.forwardRef((e,t)=>{let{image:r,label:o,showIndicator:i=!0,...d}=e;return(0,a.jsxs)(n.q7,{ref:t,...d,className:(0,l.cn)("flex flex-col gap-2","w-48","bg-overlay","rounded-md","border","p-2","hover:border-foreground-muted","hover:z-[1] focus-visible:z-[1]","data-[state=checked]:z-[1]","data-[state=checked]:ring-2 data-[state=checked]:ring-border","data-[state=checked]:bg-surface-200 dark:data-[state=checked]:bg-surface-300","data-[state=checked]:border-foreground/50","transition-colors","group",d.className),children:[d.children,(0,a.jsxs)("label",{className:"flex gap-2 w-full",id:d.id,htmlFor:d.value,children:[i&&(0,a.jsx)("div",{className:" aspect-square h-4 w-4  rounded-full border group-data-[state=checked]:border-foreground-muted group-focus:border-foreground-muted group-hover:border-foreground-muted ring-offset-background  group-focus:outline-none  group-focus-visible:ring-2 group-focus-visible:ring-ring group-focus-visible:ring-offset-2  group-disabled:cursor-not-allowed group-disabled:opacity-50 flex items-center justify-center transition ",children:(0,a.jsx)(n.C1,{className:"flex items-center justify-center",children:(0,a.jsx)(s.A,{className:"h-2.5 w-2.5 fill-current text-current"})})}),(0,a.jsx)("div",{className:(0,l.cn)("w-full","text-xs transition-colors text-left","text-light","group-hover:text-foreground group-data-[state=checked]:text-foreground",d.disabled?"cursor-not-allowed":"cursor-pointer"),children:o})]})]})});d.displayName=n.q7.displayName},73867:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var a=r(31710),n=r(62464);r(54990);var s=r(35621),o=r(48744),l=r(40598);let i=e=>{let{id:t,disabled:r,className:i,children:d,header:c,visible:u,open:f,size:m="medium",loading:p,align:g="right",hideFooter:x=!1,customFooter:b,onConfirm:h,onCancel:v,confirmText:y="Confirm",cancelText:w="Cancel",triggerElement:j,defaultOpen:N,tooltip:C,...k}=e,_=(0,l.A)("sidepanel"),z=b||(0,a.jsxs)("div",{className:_.footer,children:[(0,a.jsx)("div",{children:(0,a.jsx)(s.Button,{disabled:p,type:"default",onClick:()=>v?v():null,children:w})}),void 0!==h&&(0,a.jsxs)(o.Tooltip,{children:[(0,a.jsx)(o.TooltipTrigger,{asChild:!0,children:(0,a.jsx)("span",{className:"inline-block",children:(0,a.jsx)(s.Button,{htmlType:"submit",disabled:r||p,loading:p,onClick:()=>h?h():null,children:y})})}),void 0!==C&&(0,a.jsx)(o.TooltipContent,{side:"bottom",children:C})]})]});return f=f||u,(0,a.jsxs)(n.bL,{open:f,onOpenChange:function(e){void 0!==u&&!e&&v&&v()},defaultOpen:N,children:[j&&(0,a.jsx)(n.l9,{asChild:!0,className:_.trigger,children:j}),(0,a.jsxs)(n.ZL,{children:[(0,a.jsx)(n.hJ,{className:_.overlay}),(0,a.jsxs)(n.UC,{className:[_.base,_.size[m],_.align[g],i&&i].join(" "),onOpenAutoFocus:k.onOpenAutoFocus,onCloseAutoFocus:k.onCloseAutoFocus,onEscapeKeyDown:k.onEscapeKeyDown,onPointerDownOutside:k.onPointerDownOutside,onInteractOutside:e=>{var t;(null==(t=e.target)?void 0:t.closest("#toast"))&&e.preventDefault(),k.onInteractOutside&&k.onInteractOutside(e)},children:[c&&(0,a.jsx)("header",{className:_.header,children:c}),(0,a.jsx)("div",{className:_.contents,children:d}),!x&&z]})]})]})};i.Content=function(e){let{children:t,className:r}=e,n=(0,l.A)("sidepanel");return(0,a.jsx)("div",{className:[n.content,r].join(" ").trim(),children:t})},i.Separator=function(){let e=(0,l.A)("sidepanel");return(0,a.jsx)("div",{className:e.separator})};let d=i},75435:(e,t,r)=>{"use strict";r.r(t),r.d(t,{NavigationMenu:()=>d,NavigationMenuContent:()=>p,NavigationMenuIndicator:()=>b,NavigationMenuItem:()=>u,NavigationMenuLink:()=>g,NavigationMenuList:()=>c,NavigationMenuTrigger:()=>m,NavigationMenuViewport:()=>x,navigationMenuTriggerStyle:()=>f});var a=r(31710),n=r(63342),s=r(11448),o=r(59365),l=r(54990),i=r(35484);let d=l.forwardRef((e,t)=>{let{className:r,viewportClassName:s,renderViewport:o=!0,children:l,...d}=e;return(0,a.jsxs)(n.bL,{ref:t,className:(0,i.cn)("relative z-10 flex flex-1 items-center justify-center",r),...d,children:[l,o&&(0,a.jsx)(x,{className:s})]})});d.displayName=n.bL.displayName;let c=l.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.B8,{ref:t,className:(0,i.cn)("group flex flex-1 list-none items-center justify-center space-x-1",r),...s})});c.displayName=n.B8.displayName;let u=n.q7,f=(0,s.F)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none bg-background hover:bg-accent hover:text-accent-foreground data-[state=open]:bg-accent/50 data-[active]:bg-accent/50 h-10 py-2 px-4 group w-max"),m=l.forwardRef((e,t)=>{let{className:r,children:s,...l}=e;return(0,a.jsxs)(n.l9,{ref:t,className:(0,i.cn)("group",r),...l,children:[s," ",(0,a.jsx)(o.A,{className:"relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180","aria-hidden":"true"})]})});m.displayName=n.l9.displayName;let p=l.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.UC,{ref:t,className:(0,i.cn)("left-0 top-0 w-full data-[motion^=from-]:animate-fade-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 md:absolute md:w-auto ",r),...s})});p.displayName=n.UC.displayName;let g=n.N_,x=l.forwardRef((e,t)=>{let{className:r,containerProps:s,...o}=e;return(0,a.jsx)("div",{...s,className:(0,i.cn)("absolute left-0 top-full flex justify-center",null==s?void 0:s.className),children:(0,a.jsx)(n.LM,{className:(0,i.cn)("origin-top-center relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border bg text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=open]:fade-in data-[state=open]:slide-in-from-right-10 data-[state=open]:duration-100 data-[state=open]:ease-out data-[state=closed]:animate-out data-[state=closed]:fade-out data-[state=closed]:duration-75 md:w-[var(--radix-navigation-menu-viewport-width)]",r),ref:t,...o})})});x.displayName=n.LM.displayName;let b=l.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.C1,{ref:t,className:(0,i.cn)("top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in",r),...s,children:(0,a.jsx)("div",{className:"relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm bg-border shadow-md"})})});b.displayName=n.C1.displayName},76285:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(31710);r(54990);var n=r(40598),s=r(1856);let o={tiny:14,small:16,medium:20,large:24,xlarge:32};function l(e){let{style:t,size:r="medium"}=e,l=(0,n.A)("inputErrorIcon");return(0,a.jsx)("div",{className:l.base,style:t,children:(0,a.jsx)(s.A,{size:o[r],strokeWidth:2})})}},78921:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var a=r(31710),n=r(46067),s=r(41394),o=r.n(s),l=r(54990),i=r(34727),d=r(76285),c=r(328),u=r(40598),f=r(35484),m=r(52994);let p=(0,l.createContext)({onChange:e=>{},selected:void 0});var g=r(60601);function x(e){let{children:t,className:r,buttonClassName:s,descriptionText:g,error:x,icon:b,id:h="",name:v="",label:y,labelOptional:w,layout:j,value:N,onChange:C,onFocus:k,onBlur:_,style:z,size:S="medium",defaultValue:R,validation:A,disabled:L,optionsWidth:M}=e,[D,F]=(0,l.useState)(void 0),[I,T]=(0,l.useState)({}),E=(0,u.A)("listbox"),B=(0,l.useRef)(null),{formContextOnChange:O,values:P,errors:V,handleBlur:G,touched:H,fieldLevelValidation:Z}=(0,m.x)();P&&!N&&(N=P[h||v],R=P[h||v]),x||(V&&!x&&(x=V[h||v]),x=H&&H[h||v]?x:void 0),(0,l.useEffect)(()=>{void 0!==N&&F(N)},[N]),(0,l.useEffect)(()=>{function e(){var e;document.documentElement.style.setProperty("--width-listbox","".concat(M||(null==(e=B.current)?void 0:e.offsetWidth),"px"))}return window.addEventListener("resize",e),e(),()=>window.removeEventListener("resize",e)},[]),(0,l.useEffect)(()=>{var e;let r=o()(t);function a(e){return r.find(t=>t.props.value===e)}if(N){F(N);let e=a(N);T((null==e?void 0:e.props)?e.props:void 0);return}if(D){let e=a(D);T((null==e?void 0:e.props)?e.props:void 0);return}if(R){F(R);let e=a(D);T((null==e?void 0:e.props)?e.props:void 0);return}T(null==(e=r[0])?void 0:e.props)},[D]);let W=[E.container,E.base,s],U=[E.addOnBefore];return x&&W.push(E.variants.error),x||W.push(E.variants.standard),b&&U.push(E.with_icon),S&&W.push(E.size[S]),L&&W.push(E.disabled),(0,a.jsx)(i.H,{label:y,labelOptional:w,layout:j,id:h,error:x,descriptionText:g,className:r,style:z,size:S,children:(0,a.jsxs)(n.bL,{children:[(0,a.jsx)(n.l9,{asChild:!0,disabled:L,children:(0,a.jsxs)("button",{"data-size":S,ref:B,className:(0,f.cn)(W),onBlur:function(e){G&&G(e),_&&_(e)},onFocus:k,name:v,id:h,children:[(0,a.jsxs)("span",{className:(0,f.cn)(U),children:[b&&(0,a.jsx)(c.A,{size:S,icon:b}),(null==I?void 0:I.addOnBefore)&&(0,a.jsx)(I.addOnBefore,{}),(0,a.jsx)("span",{className:E.label,children:null==I?void 0:I.label})]}),(0,a.jsx)("span",{className:E.chevron_container,children:(0,a.jsx)("svg",{className:E.chevron,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 3a1 1 0 01.707.293l3 3a1 1 0 01-1.414 1.414L10 5.414 7.707 7.707a1 1 0 01-1.414-1.414l3-3A1 1 0 0110 3zm-3.707 9.293a1 1 0 011.414 0L10 14.586l2.293-2.293a1 1 0 011.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z",clipRule:"evenodd"})})}),x&&(0,a.jsx)("div",{className:E.actions_container,children:x&&(0,a.jsx)(d.A,{size:S})})]})}),(0,a.jsx)(n.UC,{sideOffset:6,loop:!0,side:"bottom",align:"center",className:E.options_container,children:(0,a.jsx)("div",{children:(0,a.jsx)(p.Provider,{value:{onChange:function(e){C&&C(e),F(e);let t={};t.target={type:"select",name:v,id:h,value:e,checked:void 0},O&&O(t),A&&Z(h,A(e))},selected:D},children:t})})})]})})}x.Option=function(e){let{id:t,value:r,label:s,disabled:o=!1,children:l,className:i="",addOnBefore:d}=e,c=(0,u.A)("listbox");return(0,a.jsx)(p.Consumer,{children:e=>{let{onChange:s,selected:u}=e,m=u===r;return(0,a.jsxs)(n.q7,{className:(0,f.cn)(c.option,m?c.option_active:" ",o?c.option_disabled:" ",i),onSelect:()=>o?{}:s(r),children:[(0,a.jsxs)("div",{className:c.option_inner,children:[d&&d({active:m,selected:u}),(0,a.jsx)("span",{children:"function"==typeof l?l({active:m,selected:u}):l})]}),m?(0,a.jsx)("span",{className:(0,f.cn)(c.option_check,m?c.option_check_active:""),children:(0,a.jsx)(g.A,{className:c.option_check_icon,"aria-hidden":"true"})}):null]},t)}})};let b=x},79099:(e,t,r)=>{"use strict";r.r(t),r.d(t,{DIALOG_PADDING_X:()=>f,DIALOG_PADDING_X_SMALL:()=>c,DIALOG_PADDING_Y:()=>u,DIALOG_PADDING_Y_SMALL:()=>d,Dialog:()=>p,DialogClose:()=>C,DialogContent:()=>v,DialogDescription:()=>N,DialogFooter:()=>w,DialogHeader:()=>y,DialogSection:()=>k,DialogSectionSeparator:()=>_,DialogTitle:()=>j,DialogTrigger:()=>g});var a=r(31710),n=r(62464),s=r(63089),o=r(54990),l=r(11448),i=r(35484);let d="py-4",c="px-4 md:px-5",u="py-6",f="px-4 md:px-7",m=(0,l.F)("",{variants:{padding:{medium:"".concat(u," ").concat(f),small:"".concat(d," ").concat(c)}},defaultVariants:{padding:"small"}}),p=n.bL,g=n.l9,x=e=>(0,a.jsx)(n.ZL,{...e});x.displayName=n.ZL.displayName;let b=o.forwardRef((e,t)=>{let{className:r,centered:s=!0,...o}=e;return(0,a.jsx)(n.hJ,{ref:t,className:(0,i.cn)("bg-black/40 backdrop-blur-sm","z-50 fixed inset-0 grid place-items-center overflow-y-auto data-closed:animate-overlay-hide py-8",!s&&"flex flex-col flex-start pb-8 sm:pt-12 md:pt-20 lg:pt-32 xl:pt-40 px-5",r),...o})});b.displayName=n.hJ.displayName;let h=(0,l.F)((0,i.cn)("relative z-50 w-full max-w-screen border shadow-md dark:shadow-sm","data-[state=open]:animate-in data-[state=closed]:animate-out","data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95","data-[state=closed]:slide-out-to-left-[0%] data-[state=closed]:slide-out-to-top-[0%]","data-[state=open]:slide-in-from-left-[0%] data-[state=open]:slide-in-from-top-[0%]","sm:rounded-lg md:w-full","bg-dash-sidebar"),{variants:{size:{tiny:"sm:align-middle sm:w-full sm:max-w-xs",small:"sm:align-middle sm:w-full sm:max-w-sm",medium:"sm:align-middle sm:w-full sm:max-w-lg",large:"sm:align-middle sm:w-full md:max-w-xl",xlarge:"sm:align-middle sm:w-full md:max-w-3xl",xxlarge:"sm:align-middle sm:w-full md:max-w-6xl",xxxlarge:"sm:align-middle sm:w-full md:max-w-7xl"}},defaultVariants:{size:"medium"}}),v=o.forwardRef((e,t)=>{let{className:r,children:o,size:l,hideClose:d,dialogOverlayProps:c,centered:u=!0,...f}=e;return(0,a.jsx)(x,{children:(0,a.jsx)(b,{centered:u,...c,children:(0,a.jsxs)(n.UC,{ref:t,className:(0,i.cn)(h({size:l}),r),...f,children:[o,!d&&(0,a.jsxs)(n.bm,{className:"absolute right-4 top-4 rounded-sm opacity-20 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-foreground-muted",children:[(0,a.jsx)(s.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})})})});v.displayName=n.UC.displayName;let y=o.forwardRef((e,t)=>{let{className:r,padding:n,...s}=e;return(0,a.jsx)("div",{ref:t,...s,className:(0,i.cn)("flex flex-col gap-1.5 text-center sm:text-left",m({padding:n}),r)})});y.displayName="DialogHeader";let w=o.forwardRef((e,t)=>{let{className:r,children:n,padding:s,...o}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2","border-t",m({padding:s}),r),...o,children:n})});w.displayName="DialogFooter";let j=o.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.hE,{ref:t,className:(0,i.cn)("text-base leading-none font-normal",r),...s})});j.displayName=n.hE.displayName;let N=o.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.VY,{ref:t,className:(0,i.cn)("text-sm text-foreground-lighter",r),...s})});N.displayName=n.VY.displayName;let C=o.forwardRef((e,t)=>{let{className:r,children:s,...o}=e;return(0,a.jsx)(n.bm,{ref:t,className:(0,i.cn)("opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-foreground-muted",r),...o,children:s})});C.displayName=n.bm.displayName;let k=o.forwardRef((e,t)=>{let{className:r,children:n,padding:s,...o}=e;return(0,a.jsx)("div",{ref:t,...o,className:(0,i.cn)(m({padding:s}),"overflow-hidden",r),children:n})});k.displayName="DialogSection";let _=o.forwardRef((e,t)=>{let{className:r,children:n,...s}=e;return(0,a.jsx)("div",{ref:t,...s,className:(0,i.cn)("w-full h-px bg-border",r)})});_.displayName="DialogSectionSeparator"},79155:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var a=r(31710),n=r(15531),s=r.n(n),o=r(35484),l=r(12861);let i=function(e){let{url:t="",label:r,className:n,counter:i,hasChevron:d=!0,chevronAnimation:c="translate",target:u="_self",...f}=e;return(0,a.jsx)(s(),{href:t,className:(0,o.cn)("group/text-link text-foreground-light hover:text-foreground mt-3 block cursor-pointer text-sm focus-visible:ring-2 focus-visible:outline-none focus-visible:rounded-sm focus-visible:ring-foreground-lighter focus-visible:text-foreground",n),target:u,...f,children:(0,a.jsxs)("div",{className:"group flex items-center gap-1",children:[(0,a.jsx)("span",{className:"sr-only",children:"".concat(r," about ").concat(t)}),(0,a.jsx)("span",{children:r}),i&&(0,a.jsxs)("span",{className:"text-xs flex items-center justify-center text-foreground-lighter group-hover/text-link:text-foreground",children:["(",i,")"]}),d&&(0,a.jsx)("div",{className:(0,o.cn)("transition-all group-hover:ml-0.5","fadeIn"===c&&"opacity-0 group-hover:opacity-100"),children:(0,a.jsx)(l.A,{size:14,strokeWidth:2})})]})})}},80129:(e,t,r)=>{"use strict";r.r(t),r.d(t,{useOnClickOutside:()=>n});var a=r(54990);function n(e,t){(0,a.useEffect)(()=>{let r=r=>{!e.current||e.current.contains(r.target)||t(r)};return document.addEventListener("mousedown",r),document.addEventListener("touchstart",r),()=>{document.removeEventListener("mousedown",r),document.removeEventListener("touchstart",r)}},[e,t])}},80335:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Checkbox:()=>d,default:()=>c});var a=r(31710),n=r(54990),s=r(34727),o=r(40598),l=r(52994);let i=(0,n.createContext)({parentCallback:e=>{},parentSize:""});function d(e){let{className:t,id:r="",name:n="",label:s,afterLabel:d,beforeLabel:c,description:u,checked:f,value:m,onChange:p,onBlur:g,size:x="medium",disabled:b=!1,...h}=e,{formContextOnChange:v,values:y,handleBlur:w}=(0,l.x)(),j=(0,o.A)("checkbox");return(0,a.jsx)(i.Consumer,{children:e=>{let{parentCallback:o,parentSize:l}=e,i=r||n||(s?s.toLowerCase().replace(/^[^A-Z0-9]+/gi,"").replace(/ /g,"-"):void 0);x=l||x;let N=n||i,C=null!=f?f:void 0,k=[j.container];return t&&k.push(t),y&&void 0===f&&(C=y[r||n]),(0,a.jsxs)("div",{className:k.join(" "),children:[(0,a.jsx)("input",{id:i,name:N,type:"checkbox",className:[j.base,j.size[x]].join(" "),onChange:function(e){o&&o(e),p&&p(e),v&&v(e)},onBlur:function(e){w&&setTimeout(()=>{w(e)},100),g&&g(e)},checked:C,value:m||i,disabled:b,...h}),(0,a.jsxs)("label",{className:[j.label.base,j.label[x]].join(" "),htmlFor:i,children:[(0,a.jsxs)("span",{children:[c&&(0,a.jsx)("span",{className:[j.label_before.base,j.label_before[x]].join(" "),children:c}),s,d&&(0,a.jsx)("span",{className:[j.label_after.base,j.label_after[x]].join(" "),children:d})]}),u&&(0,a.jsx)("p",{className:[j.description.base,j.description[x]].join(" "),children:u})]})]})}})}d.Group=function(e){let{id:t,layout:r="vertical",error:n,descriptionText:l,label:c,afterLabel:u,beforeLabel:f,labelOptional:m,children:p,className:g,options:x,onChange:b,size:h="medium"}=e,v=(0,o.A)("checkbox");return(0,a.jsx)(s.H,{label:c,afterLabel:u,beforeLabel:f,labelOptional:m,layout:r,id:t,error:n,descriptionText:l,className:g,size:h,children:(0,a.jsx)(i.Provider,{value:{parentCallback:e=>{b&&b(e)},parentSize:h},children:(0,a.jsx)("div",{className:v.group,children:x?x.map(e=>(0,a.jsx)(d,{id:e.id,value:e.value,label:e.label,beforeLabel:e.beforeLabel,afterLabel:e.afterLabel,checked:e.checked,name:e.name,description:e.description,defaultChecked:e.defaultChecked},e.id)):p})})})};let c=d},81401:(e,t,r)=>{"use strict";r.d(t,{l:()=>o});var a=r(66096),n=r.n(a),s=r(97218);let o=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:n();if(window.document.hasFocus()){var r;if(null==(r=navigator.clipboard)?void 0:r.write){let r=new ClipboardItem({"text/plain":Promise.resolve(e).then(e=>new Blob([e],{type:"text/plain"}))});setTimeout(()=>{navigator.clipboard.write([r]).then(t)},0)}else Promise.resolve(e).then(e=>{var t;return null==(t=navigator.clipboard)?void 0:t.writeText(e)}).then(t)}else s.toast.error("Unable to copy to clipboard")}},83020:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Separator:()=>l});var a=r(31710),n=r(90306),s=r(54990),o=r(35484);let l=s.forwardRef((e,t)=>{let{className:r,orientation:s="horizontal",decorative:l=!0,...i}=e;return(0,a.jsx)(n.b,{ref:t,decorative:l,orientation:s,className:(0,o.cn)("shrink-0 bg-border-muted","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",r),...i})});l.displayName=n.b.displayName},84458:(e,t,r)=>{"use strict";r.d(t,{Command:()=>c,CommandDialog:()=>u,CommandEmpty:()=>p,CommandGroup:()=>g,CommandInput:()=>f,CommandItem:()=>b,CommandList:()=>m,CommandSeparator:()=>x,CommandShortcut:()=>h});var a=r(31710),n=r(53273),s=r(73759),o=r(63089),l=r(54990),i=r(35484),d=r(79099);let c=l.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.uB,{ref:t,className:(0,i.cn)("flex h-full w-full flex-col overflow-hidden rounded-md bg-overlay text-foreground-light",r),...s})});c.displayName=n.uB.displayName;let u=e=>{let{children:t,...r}=e;return(0,a.jsx)(d.Dialog,{...r,children:(0,a.jsx)(d.DialogContent,{className:"overflow-hidden p-0 shadow-lg",children:(0,a.jsx)(c,{className:"[&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-foreground-muted [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-group]]:px-2 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5",children:t})})})},f=l.forwardRef((e,t)=>{var r;let{className:l,wrapperClassName:d,showResetIcon:c=!1,showSearchIcon:u=!0,handleReset:f,...m}=e;return(0,a.jsxs)("div",{className:(0,i.cn)("flex items-center border-b px-3",d),"cmdk-input-wrapper":"",children:[u&&(0,a.jsx)(s.A,{className:"h-4 w-4 shrink-0 opacity-50"}),(0,a.jsx)(n.uB.Input,{ref:t,className:(0,i.cn)("flex h-9 w-full rounded-md bg-transparent py-3 md:text-xs text outline-none placeholder:text-muted disabled:cursor-not-allowed disabled:opacity-50 border-none focus:ring-0",l),...m}),c&&(0,a.jsx)("button",{onClick:f,className:(0,i.cn)("text-foreground-lighter hover:text-foreground-light hover:cursor-pointer transition-all opacity-0 duration-100",!!(null==(r=m.value)?void 0:r.length)&&"opacity-100"),children:(0,a.jsx)(o.A,{size:14})})]})});f.displayName=n.uB.Input.displayName;let m=l.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.uB.List,{ref:t,className:(0,i.cn)("max-h-[300px] overflow-y-auto overflow-x-hidden",r),...s})});m.displayName=n.uB.List.displayName;let p=l.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.uB.Empty,{ref:t,className:(0,i.cn)("py-6 text-center text-xs",r),...s})});p.displayName=n.uB.Empty.displayName;let g=l.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.uB.Group,{ref:t,className:(0,i.cn)("overflow-hidden p-1 text-foreground-light [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-normal [&_[cmdk-group-heading]]:text-foreground-muted","[&_[cmdk-group-heading]]:font-mono","[&_[cmdk-group-heading]]:uppercase","[&_[cmdk-group-heading]]:tracking-wider",r),...s})});g.displayName=n.uB.Group.displayName;let x=l.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.uB.Separator,{ref:t,className:(0,i.cn)("-mx-1 h-px bg-border-overlay",r),...s})});x.displayName=n.uB.Separator.displayName;let b=l.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.uB.Item,{ref:t,className:(0,i.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-xs outline-none data-[selected=true]:bg-overlay-hover data-[selected=true]:text-strong data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50",r),...s})});b.displayName=n.uB.Item.displayName;let h=e=>{let{className:t,...r}=e;return(0,a.jsx)("span",{className:(0,i.cn)("ml-auto text-xs tracking-widest text-foreground-muted",t),...r})};h.displayName="CommandShortcut"},85986:(e,t,r)=>{"use strict";r.r(t),r.d(t,{HoverCard:()=>l,HoverCardContent:()=>d,HoverCardTrigger:()=>i});var a=r(31710),n=r(54426),s=r(54990),o=r(35484);let l=n.bL,i=n.l9,d=s.forwardRef((e,t)=>{let{className:r,align:s="center",animate:l="zoom-in",sideOffset:i=4,...d}=e;return(0,a.jsx)(n.ZL,{children:(0,a.jsx)(n.UC,{ref:t,align:s,sideOffset:i,className:(0,o.cn)("z-50 w-64 rounded-md border bg-overlay p-4 text-popover-foreground shadow-md outline-none","zoom-in"===l?"animate-in zoom-in-[99%]":"animate-in fade-in-50 data-[side=bottom]:slide-in-from-top-1 data-[side=left]:slide-in-from-right-1 data-[side=right]:slide-in-from-left-1 data-[side=top]:slide-in-from-bottom-1",r),...d})})});d.displayName=n.UC.displayName},86084:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Sheet:()=>d,SheetClose:()=>u,SheetContent:()=>x,SheetDescription:()=>w,SheetFooter:()=>v,SheetHeader:()=>b,SheetSection:()=>h,SheetTitle:()=>y,SheetTrigger:()=>c});var a=r(31710),n=r(62464),s=r(11448),o=r(54990),l=r(63089),i=r(35484);let d=n.bL,c=n.l9,u=n.bm;(0,s.F)("fixed inset-0 z-50 flex",{variants:{side:{top:"items-start",bottom:"items-end",left:"justify-start",right:"justify-end"}},defaultVariants:{side:"right"}});let f=e=>{let{side:t,children:r,...s}=e;return(0,a.jsx)(n.ZL,{...s,children:r})};f.displayName=n.ZL.displayName;let m=o.forwardRef((e,t)=>{let{className:r,children:s,...o}=e;return(0,a.jsx)(n.hJ,{className:(0,i.cn)("fixed inset-0 z-50 bg-alternative/90 backdrop-blur-sm transition-all duration-100 data-[state=closed]:animate-out data-[state=closed]:fade-out data-[state=open]:fade-in",r),...o,ref:t})});m.displayName=n.hJ.displayName;let p=(0,i.cn)(["fixed z-50 scale-100 gap-4 bg-studio opacity-100 shadow-lg","data-[state=open]:animate-in data-[state=open]:duration-300 data-[state=closed]:animate-out data-[state=closed]:duration-300"]),g=(0,s.F)(p,{variants:{side:{top:"data-[state=open]:slide-in-from-top data-[state=closed]:slide-out-to-top w-full border-b inset-x-0 top-0",bottom:"data-[state=open]:slide-in-from-bottom data-[state=closed]:slide-out-to-bottom w-full border-t inset-x-0 bottom-0",left:"data-[state=open]:slide-in-from-left data-[state=closed]:slide-out-to-left h-full border-r inset-y-0 left-0",right:"data-[state=open]:slide-in-from-right data-[state=closed]:slide-out-to-right h-full border-l inset-y-0 right-0"},size:{content:"",default:"",sm:"",lg:"",xl:"",xxl:"",full:""}},compoundVariants:[{side:["top","bottom"],size:"content",class:"max-h-screen"},{side:["top","bottom"],size:"default",class:"h-1/3"},{side:["top","bottom"],size:"sm",class:"h-1/4"},{side:["top","bottom"],size:"lg",class:"h-1/2"},{side:["top","bottom"],size:"xl",class:"h-5/6"},{side:["top","bottom"],size:"full",class:"h-screen"},{side:["right","left"],size:"content",class:"max-w-screen"},{side:["right","left"],size:"default",class:"lg:w-1/3"},{side:["right","left"],size:"sm",class:"lg:w-1/4"},{side:["right","left"],size:"lg",class:"lg:w-1/2"},{side:["right","left"],size:"xl",class:"lg:w-4/6"},{side:["right","left"],size:"xxl",class:"w-5/6"},{side:["right","left"],size:"full",class:"w-screen"}],defaultVariants:{side:"right",size:"default"}}),x=o.forwardRef((e,t)=>{let{side:r,size:s,className:o,children:d,showClose:c=!0,...u}=e;return(0,a.jsxs)(f,{side:r,children:[(0,a.jsx)(m,{}),(0,a.jsxs)(n.UC,{ref:t,className:(0,i.cn)(g({side:r,size:s}),o),...u,children:[d,c?(0,a.jsxs)(n.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]}):null]})]})});x.displayName=n.UC.displayName;let b=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,i.cn)("px-5 py-4 text-center sm:text-left border-b bg-dash-sidebar",t),...r})};b.displayName="SheetHeader";let h=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,i.cn)("px-5 py-4",t),...r})};h.displayName="SheetSection";let v=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,i.cn)("px-5 py-3 border-t w-full","flex flex-col-reverse sm:flex-row sm:justify-end gap-2",t),...r})};v.displayName="SheetFooter";let y=o.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.hE,{ref:t,className:(0,i.cn)("text-lg text-foreground",r),...s})});y.displayName=n.hE.displayName;let w=o.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.VY,{ref:t,className:(0,i.cn)("text-sm text-foreground-light",r),...s})});w.displayName=n.VY.displayName},89524:(e,t,r)=>{"use strict";r.d(t,{default:()=>f});var a=r(31710),n=r(54990),s=r(40598),o=r(35484);function l(e){let{children:t,className:r,tag:n="div",style:s}=e;return(0,a.jsx)("".concat(n),{style:s,children:t})}l.Title=function(e){let{className:t,level:r=1,children:n,style:s}=e;return(0,a.jsx)("h".concat(r),{style:s,children:n})},l.Text=function(e){let{className:t,children:r,style:n,type:s,disabled:o,mark:l,code:i,keyboard:d,underline:c,strikethrough:u,strong:f,small:m}=e;return i?(0,a.jsx)("code",{style:n,children:r}):l?(0,a.jsx)("mark",{style:n,children:r}):d?(0,a.jsx)("kbd",{style:n,children:r}):f?(0,a.jsx)("strong",{style:n,children:r}):(0,a.jsx)("span",{style:n,children:r})},l.Link=function(e){let{children:t,target:r="_blank",href:n,className:s,onClick:o,style:l}=e;return(0,a.jsx)("a",{onClick:o,href:n,target:r,rel:"noopener noreferrer",style:l,children:t})};let i=(0,n.createContext)({type:"text"}),d=e=>{let{type:t}=e;return(0,a.jsx)(i.Provider,{value:{type:t},children:e.children})},c=()=>{let e=(0,n.useContext)(i);if(void 0===e)throw Error("MenuContext must be used within a MenuContextProvider.");return e};function u(e){let{children:t,className:r,ulClassName:n,style:s,type:o="text"}=e;return(0,a.jsx)("nav",{role:"menu","aria-label":"Sidebar","aria-orientation":"vertical","aria-labelledby":"options-menu",className:r,style:s,children:(0,a.jsx)(d,{type:o,children:(0,a.jsx)("ul",{className:n,children:t})})})}u.Item=function(e){let{children:t,icon:r,active:n,rounded:l,onClick:i,doNotCloseOverlay:d=!1,showActiveBar:u=!1,style:f}=e,m=(0,s.A)("menu"),{type:p}=c(),g=[m.item.base];g.push(m.item.variants[p].base),n?g.push(m.item.variants[p].active):g.push(m.item.variants[p].normal);let x=[m.item.content.base];n?x.push(m.item.content.active):x.push(m.item.content.normal);let b=[m.item.icon.base];return n?b.push(m.item.icon.active):b.push(m.item.icon.normal),(0,a.jsxs)("li",{role:"menuitem",className:(0,o.cn)("outline-none",g),style:f,onClick:i,"aria-current":n?"page":void 0,children:[r&&(0,a.jsx)("div",{className:"".concat(b.join(" ")," min-w-fit"),children:r}),(0,a.jsx)("span",{className:x.join(" "),children:t})]})},u.Group=function(e){let{children:t,icon:r,title:n}=e,o=(0,s.A)("menu"),{type:l}=c();return(0,a.jsxs)("div",{className:[o.group.base,o.group.variants[l]].join(" "),children:[r&&(0,a.jsx)("span",{className:o.group.icon,children:r}),(0,a.jsx)("span",{className:o.group.content,children:n}),t]})},u.Misc=function(e){let{children:t}=e;return(0,a.jsx)("div",{children:(0,a.jsx)(l.Text,{children:(0,a.jsx)("span",{children:t})})})};let f=u},90527:(e,t,r)=>{"use strict";r.r(t),r.d(t,{IconBackground:()=>n});var a=r(31710);let n=e=>(0,a.jsx)("div",{className:"shrink-0 bg-brand-200 dark:bg-brand-400 border border-brand-300 dark:border-brand-400 w-8 h-8 flex items-center justify-center rounded",children:e.children})},91219:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var a=r(31710),n=r(40039);r(54990);var s=r(63089),o=r(40598);function l(e){let{align:t="center",ariaLabel:r,arrow:s=!1,children:l,className:i,defaultOpen:d=!1,modal:c,onOpenChange:u,open:f,overlay:m,side:p="bottom",sideOffset:g=6,style:x,header:b,footer:h,size:v="content",disabled:y,"data-testid":w}=e,j=(0,o.A)("popover"),N=[j.content,j.size[v]];return i&&N.push(i),(0,a.jsxs)(n.bL,{defaultOpen:d,modal:c,onOpenChange:u,open:f,children:[(0,a.jsx)(n.l9,{disabled:y,className:j.trigger,"aria-label":r,"data-testid":w,children:l}),(0,a.jsx)(n.ZL,{children:(0,a.jsxs)(n.UC,{sideOffset:g,side:p,align:t,className:N.join(" "),style:x,children:[s&&(0,a.jsx)(n.i3,{offset:10}),b&&(0,a.jsx)("div",{className:j.header,children:b}),m,h&&(0,a.jsx)("div",{className:j.footer,children:h})]})})]})}l.Separator=function(){let e=(0,o.A)("popover");return(0,a.jsx)("div",{className:e.separator})},l.Close=function(){let e=(0,o.A)("popover");return(0,a.jsx)(n.bm,{className:e.close,children:(0,a.jsx)(s.A,{size:14,strokeWidth:2})})};let i=l},91965:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ColLayout:()=>c,OptGroup:()=>m,Option:()=>f,default:()=>p});var a=r(31710),n=r(54990),s=r(34727),o=r(76285),l=r(328),i=r(40598),d=r(52994);let c=e=>(0,a.jsx)("div",{children:e.children});function u(e){let{autoComplete:t,autofocus:r,children:c,className:u,descriptionText:f,disabled:m,error:p,icon:g,id:x="",inputRef:b,label:h,afterLabel:v,beforeLabel:y,labelOptional:w,layout:j,name:N="",onChange:C,onBlur:k,placeholder:_,required:z,value:S,defaultValue:R,style:A,size:L="medium",borderless:M=!1,validation:D,...F}=e,{formContextOnChange:I,values:T,errors:E,handleBlur:B,touched:O,fieldLevelValidation:P}=(0,d.x)();T&&!S&&(S=T[x]),p||(E&&!p&&(p=E[x||N]),p=O&&O[x||N]?p:void 0),(0,n.useEffect)(()=>{D&&P(x,D(S))},[]);let V=(0,i.A)("select"),G=[V.container];u&&G.push(u);let H=[V.base];return p&&H.push(V.variants.error),p||H.push(V.variants.standard),g&&H.push(V.with_icon),L&&H.push(V.size[L]),m&&H.push(V.disabled),(0,a.jsx)(s.H,{label:h,afterLabel:v,beforeLabel:y,labelOptional:w,layout:j,id:x,error:p,descriptionText:f,className:u,style:A,size:L,children:(0,a.jsxs)("div",{className:V.container,children:[(0,a.jsx)("select",{id:x,name:N,"data-size":L,defaultValue:R,autoComplete:t,autoFocus:r,className:H.join(" "),onChange:function(e){C&&C(e),I&&I(e),D&&P(x,D(e.target.value))},onBlur:function(e){B&&B(e),k&&k(e)},ref:b,value:S,disabled:m,required:z,placeholder:_,...F,children:c}),g&&(0,a.jsx)(l.A,{size:L,icon:g}),p&&(0,a.jsx)("div",{className:V.actions_container,children:p&&(0,a.jsx)(o.A,{size:L})}),(0,a.jsx)("span",{className:V.chevron_container,children:(0,a.jsx)("svg",{className:V.chevron,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 3a1 1 0 01.707.293l3 3a1 1 0 01-1.414 1.414L10 5.414 7.707 7.707a1 1 0 01-1.414-1.414l3-3A1 1 0 0110 3zm-3.707 9.293a1 1 0 011.414 0L10 14.586l2.293-2.293a1 1 0 011.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})})}function f(e){let{value:t,children:r,selected:n}=e;return(0,a.jsx)("option",{value:t,selected:n,children:r})}function m(e){let{label:t,children:r}=e;return(0,a.jsx)("optgroup",{label:t,children:r})}u.Option=f,u.OptGroup=m;let p=u},92691:(e,t,r)=>{"use strict";r.r(t),r.d(t,{SQL_ICON:()=>x,TreeView:()=>m,TreeViewFolderIcon:()=>b,TreeViewItem:()=>g,TreeViewItemVariant:()=>p,flattenTree:()=>c.dG});var a=r(31710),n=r(11448),s=r(2015),o=r(12861),l=r(85338),i=r(48714),d=r(54990),c=r(12575),u=r(35484),f=r(94081);let m=c.Ay,p=(0,n.F)("group relative transition-colors h-[28px] flex items-center gap-3 text-sm cursor-pointer select-none text-foreground-light hover:bg-control aria-expanded:bg-transparent data-[state=open]:bg-transparent",{variants:{isSelected:{true:"text-foreground !bg-selection",false:""},isOpened:{true:"bg-control",false:""},isPreview:{true:"bg-control text-foreground",false:""}}}),g=(0,d.forwardRef)((e,t)=>{let{level:r=1,levelPadding:n=38,isExpanded:l=!1,isOpened:i=!1,isBranch:c=!1,isSelected:m=!1,isPreview:g=!1,isLoading:h=!1,xPadding:v=16,name:y="",icon:w,isEditing:j=!1,onEditSubmit:N,onDoubleClick:C,...k}=e,[_,z]=(0,d.useState)(y),S=(0,d.useRef)(null),R=(0,d.useRef)(0);(0,d.useEffect)(()=>{j?(R.current=Number(new Date),setTimeout(()=>{let e=S.current;e&&(document.activeElement!==e&&e.focus(),setTimeout(()=>{let t=e.value,r=t.lastIndexOf("."),a=r>0?r:t.length;try{e.setSelectionRange(0,a)}catch(e){console.error("Could not set selection range",e)}},50))},200)):z(y)},[j]),(0,d.useEffect)(()=>{h||z(y)},[h]);let{isDisabled:A,isHalfSelected:L,handleSelect:M,handleExpand:D,treeState:F,dispatch:I,...T}=k;return(0,a.jsxs)("div",{ref:t,...T,"aria-selected":m,"aria-expanded":!j&&l,onDoubleClick:C,className:(0,u.cn)(p({isSelected:m,isOpened:i,isPreview:g}),k.className),style:{paddingLeft:v+(r-1)*n/2,...k.style},"data-treeview-is-branch":c,"data-treeview-level":r,children:[Array.from({length:r-1}).map((e,t)=>(0,a.jsx)("div",{style:{left:v+t*n/2+7},className:"absolute h-full w-px bg-border-strong"},t)),m&&(0,a.jsx)("div",{className:"absolute left-0 h-full w-0.5 bg-foreground"}),c?(0,a.jsxs)(a.Fragment,{children:[h?(0,a.jsx)(s.A,{className:(0,u.cn)("text-foreground-muted animate-spin"),size:14}):(0,a.jsx)(o.A,{className:(0,u.cn)("text-foreground-muted","group-aria-selected:text-foreground-light","group-aria-expanded:text-foreground-light","transition-transform duration-200","group-aria-expanded:rotate-90"),size:14,strokeWidth:1.5}),(0,a.jsx)(b,{className:(0,u.cn)("transition-colors"," text-foreground-muted","group-aria-selected:text-foreground-light","group-aria-expanded:text-foreground-light"),isOpen:l,size:16,strokeWidth:1.5})]}):w||(0,a.jsx)(x,{className:(0,u.cn)("transition-colors","fill-foreground-muted","group-aria-selected:fill-foreground","w-5 h-5 shrink-0","-ml-0.5"),size:16,strokeWidth:1.5}),(0,a.jsx)("span",{className:(0,u.cn)(j&&"hidden","truncate text-sm"),title:y,children:y}),(0,a.jsx)("form",{onSubmit:e=>{e.preventDefault(),null==N||N(_)},className:(0,u.cn)(!j&&"hidden"),children:(0,a.jsx)(f.p,{autoFocus:!0,ref:S,onChange:e=>{z(e.target.value)},onBlur:e=>{if(Number(new Date)-R.current<400){var t;e.preventDefault(),null==(t=S.current)||t.focus()}else null==N||N(_)},onKeyDownCapture:e=>{if("Enter"===e.key){var t;null==(t=S.current)||t.blur()}else"Escape"===e.key?(z(y),null==N||N(y)):e.stopPropagation()},className:"block w-full text-sm px-2 py-1 h-7",value:_})})]})}),x=(0,d.forwardRef)((e,t)=>(0,a.jsxs)("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",...e,children:[(0,a.jsxs)("g",{clipPath:"url(#clip0_1018_49117)",children:[(0,a.jsx)("path",{d:"M20.8457 14.4531V15.6348H17.8916V14.4531H20.8457ZM18.3311 8.52539V15.6348H16.9004V8.52539H18.3311Z"}),(0,a.jsx)("path",{d:"M13.6865 14.5508L15.3857 16.084L14.4873 16.9092L12.8271 15.376L13.6865 14.5508ZM15.3564 11.5283V12.6318C15.3564 13.1429 15.2962 13.5938 15.1758 13.9844C15.0553 14.3717 14.8812 14.6956 14.6533 14.9561C14.4255 15.2132 14.1553 15.4069 13.8428 15.5371C13.5303 15.6673 13.1836 15.7324 12.8027 15.7324C12.4219 15.7324 12.0736 15.6673 11.7578 15.5371C11.4453 15.4069 11.1751 15.2132 10.9473 14.9561C10.7227 14.6956 10.5469 14.3717 10.4199 13.9844C10.2962 13.5938 10.2344 13.1429 10.2344 12.6318V11.5283C10.2344 11.0173 10.2962 10.568 10.4199 10.1807C10.5436 9.79329 10.7178 9.47103 10.9424 9.21387C11.1702 8.95345 11.4404 8.75814 11.7529 8.62793C12.0654 8.49447 12.4121 8.42773 12.793 8.42773C13.1771 8.42773 13.5254 8.49447 13.8379 8.62793C14.1504 8.75814 14.4206 8.95345 14.6484 9.21387C14.8796 9.47103 15.0553 9.79329 15.1758 10.1807C15.2962 10.568 15.3564 11.0173 15.3564 11.5283ZM13.9307 12.6318V11.5186C13.9307 11.1833 13.9062 10.8952 13.8574 10.6543C13.8086 10.4134 13.7354 10.2165 13.6377 10.0635C13.5433 9.91048 13.4245 9.79818 13.2812 9.72656C13.1413 9.65495 12.9785 9.61914 12.793 9.61914C12.6107 9.61914 12.4479 9.65495 12.3047 9.72656C12.1647 9.79818 12.0475 9.91048 11.9531 10.0635C11.8587 10.2165 11.7855 10.4134 11.7334 10.6543C11.6846 10.8952 11.6602 11.1833 11.6602 11.5186V12.6318C11.6602 12.9704 11.6846 13.2601 11.7334 13.501C11.7822 13.7419 11.8538 13.9404 11.9482 14.0967C12.0459 14.2497 12.1647 14.3636 12.3047 14.4385C12.4479 14.5101 12.6139 14.5459 12.8027 14.5459C12.985 14.5459 13.1462 14.5101 13.2861 14.4385C13.4294 14.3636 13.5482 14.2497 13.6426 14.0967C13.7402 13.9437 13.8118 13.7467 13.8574 13.5059C13.9062 13.2617 13.9307 12.9704 13.9307 12.6318Z"}),(0,a.jsx)("path",{d:"M7.47266 13.7646C7.47266 13.6377 7.46126 13.5221 7.43848 13.418C7.41569 13.3138 7.36686 13.2178 7.29199 13.1299C7.22038 13.042 7.11458 12.9541 6.97461 12.8662C6.83789 12.7783 6.65885 12.6872 6.4375 12.5928C6.17383 12.4821 5.91829 12.3649 5.6709 12.2412C5.4235 12.1143 5.20052 11.9678 5.00195 11.8018C4.80339 11.6357 4.64551 11.4404 4.52832 11.2158C4.41439 10.988 4.35742 10.721 4.35742 10.415C4.35742 10.1156 4.41113 9.84375 4.51855 9.59961C4.62923 9.35547 4.78548 9.14714 4.9873 8.97461C5.18913 8.79883 5.42513 8.66374 5.69531 8.56934C5.96875 8.47493 6.27311 8.42773 6.6084 8.42773C7.06413 8.42773 7.45801 8.52214 7.79004 8.71094C8.12533 8.89974 8.38411 9.15853 8.56641 9.4873C8.75195 9.81608 8.84473 10.1937 8.84473 10.6201H7.41895C7.41895 10.4183 7.38965 10.2409 7.33105 10.0879C7.27572 9.93164 7.1862 9.80957 7.0625 9.72168C6.9388 9.63379 6.7793 9.58984 6.58398 9.58984C6.40169 9.58984 6.25033 9.62728 6.12988 9.70215C6.0127 9.77376 5.9248 9.87305 5.86621 10C5.81087 10.1237 5.7832 10.2637 5.7832 10.4199C5.7832 10.5371 5.81087 10.6429 5.86621 10.7373C5.9248 10.8285 6.00456 10.9115 6.10547 10.9863C6.20638 11.0579 6.3252 11.1279 6.46191 11.1963C6.60189 11.2646 6.75488 11.3314 6.9209 11.3965C7.24316 11.5234 7.52799 11.6634 7.77539 11.8164C8.02279 11.9661 8.22949 12.1354 8.39551 12.3242C8.56152 12.5098 8.68685 12.7197 8.77148 12.9541C8.85612 13.1885 8.89844 13.4554 8.89844 13.7549C8.89844 14.0511 8.84635 14.3213 8.74219 14.5654C8.64128 14.8063 8.49316 15.0146 8.29785 15.1904C8.10254 15.363 7.86654 15.4964 7.58984 15.5908C7.31641 15.6852 7.01042 15.7324 6.67188 15.7324C6.3431 15.7324 6.03223 15.6868 5.73926 15.5957C5.44629 15.5013 5.1875 15.3597 4.96289 15.1709C4.74154 14.9788 4.56738 14.7363 4.44043 14.4434C4.31348 14.1471 4.25 13.7972 4.25 13.3936H5.68066C5.68066 13.6084 5.70182 13.7923 5.74414 13.9453C5.78646 14.0951 5.85156 14.2155 5.93945 14.3066C6.02734 14.3945 6.13477 14.4613 6.26172 14.5068C6.39193 14.5492 6.54004 14.5703 6.70605 14.5703C6.89486 14.5703 7.0446 14.5345 7.15527 14.4629C7.26921 14.3913 7.35059 14.2952 7.39941 14.1748C7.44824 14.0544 7.47266 13.9176 7.47266 13.7646Z"}),(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M20.5 5.73438H4.5C3.11929 5.73438 2 6.85366 2 8.23438V16.5039C2 17.8846 3.11929 19.0039 4.5 19.0039H20.5C21.8807 19.0039 23 17.8846 23 16.5039V8.23438C23 6.85366 21.8807 5.73438 20.5 5.73438ZM4.5 4.23438C2.29086 4.23438 0.5 6.02524 0.5 8.23438V16.5039C0.5 18.713 2.29086 20.5039 4.5 20.5039H20.5C22.7091 20.5039 24.5 18.713 24.5 16.5039V8.23438C24.5 6.02524 22.7091 4.23438 20.5 4.23438H4.5Z"})]}),(0,a.jsx)("defs",{children:(0,a.jsx)("clipPath",{id:"clip0_1018_49117",children:(0,a.jsx)("rect",{width:"24",height:"24",transform:"translate(0.5 0.269531)"})})})]})),b=(0,d.forwardRef)((e,t)=>{let{isOpen:r,...n}=e,s=r?l.A:i.A;return(0,a.jsx)(s,{ref:t,...n})})},94081:(e,t,r)=>{"use strict";r.d(t,{p:()=>d});var a=r(31710),n=r(11448),s=r(54990),o=r(760),l=r(35484);let i=(0,n.F)((0,l.cn)("flex h-10 w-full rounded-md border border-control bg-foreground/[.026] px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted","focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-background-control focus-visible:ring-offset-2 focus-visible:ring-offset-foreground-muted disabled:cursor-not-allowed disabled:opacity-50","aria-[] aria-[invalid=true]:bg-destructive-200 aria-[invalid=true]:border-destructive-400 aria-[invalid=true]:focus:border-destructive aria-[invalid=true]:focus-visible:border-destructive"),{variants:{size:{...o.vs}},defaultVariants:{size:o.FE}}),d=s.forwardRef((e,t)=>{let{className:r,type:n,size:s="small",...o}=e;return(0,a.jsx)("input",{type:n,ref:t,...o,className:(0,l.cn)(i({size:s}),r)})});d.displayName="Input"},96903:(e,t,r)=>{"use strict";r.d(t,{default:()=>f});var a=r(31710);r(55084);var n=r(54990),s=r(74503),o=r(47375),l=r(56631),i=r(35484),d=r(87308);let c=e=>{let{img:t}=e;return(0,a.jsx)("figure",{className:" [&_img]:rounded-md [&_img]:border [&_img]:bg-default ",children:t})},u=e=>{switch(e){case"left":return"text-left";case"right":return"text-right";default:return"text-center"}},f=e=>{let{src:t,alt:r="",zoomable:f,...m}=e,[p,g]=(0,n.useState)(!1),{resolvedTheme:x}=(0,o.D)(),b=(0,l.dv)(),h=f?d.A:"span",v="string"==typeof t?t:(null==x?void 0:x.includes("dark"))?t.dark:t.light;return((0,n.useEffect)(()=>{g(!0)},[]),p)?(0,a.jsxs)("figure",{className:(0,i.cn)("next-image--dynamic-fill",m.containerClassName),children:[(0,a.jsx)(h,{...f?{ZoomContent:c,zoomMargin:b?20:80}:void 0,children:(0,a.jsx)(s.default,{alt:r,src:v,sizes:f?"(max-width: 768px) 200vw, (max-width: 1200px) 120vw, 200vw":"(max-width: 768px) 100vw, (max-width: 1200px) 66vw, 33vw",className:m.className,style:m.style,...m},x)}),m.caption&&(0,a.jsx)("figcaption",{className:(0,i.cn)(u(m.captionAlign)),children:m.caption})]}):null}}}]);