"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8815],{16107:(e,t,s)=>{s.r(t),s.d(t,{BlockPreview:()=>o});var r=s(31710),a=s(54990),n=s(35484);function o(e){let{name:t,wide:s=!1,isPair:o=!1}=e,i=a.useMemo(()=>(0,r.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,r.jsx)(a.Suspense,{fallback:(0,r.jsx)("div",{className:"flex items-center text-sm text-muted-foreground",children:"Loading..."}),children:(0,r.jsx)("iframe",{src:"".concat("/ui","/example/").concat(t),style:{border:"none",width:"100%",height:"100%",display:"block"},name:"preview-frame"})})}),[t]);return(0,r.jsx)("div",{className:(0,n.cn)("mt-4 w-full",s?"2xl:-ml-12 2xl:-mr-12":""),children:(0,r.jsx)("div",{className:(0,n.cn)("relative border rounded-lg overflow-hidden bg-muted min-h-[150px] h-[600px]",o&&"rounded-none"),children:i})})}},30169:(e,t,s)=>{s.d(t,{ComponentPreview:()=>d});var r=s(31710),a=s(66710),n=s(54990),o=s(61313),i=s(35484),l=s(46845),u=s(39966),c=s(12861);function d(e){let{name:t,children:s,className:d,extractClassname:p,extractedClassNames:g,align:m="center",showGrid:f=!1,showDottedGrid:y=!0,showCode:b=!0,wide:k=!1,...h}=e,[x]=(0,o.U)(),v=u.R.findIndex(e=>e.name===x.style),[j,w]=n.useState(!1),z=n.useMemo(()=>{var e;let s=null==(e=a.j.default[t])?void 0:e.component;return s?(0,r.jsx)(s,{}):(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Component"," ",(0,r.jsx)("code",{className:"relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm",children:t})," ","not found in registry."]})},[t,x.style]),D=n.Children.toArray(s)[v],_=n.useMemo(()=>(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:(0,i.cn)("preview flex min-h-[350px] w-full justify-center p-10 theme-original",{"items-center":"center"===m,"items-start":"start"===m,"items-end":"end"===m}),children:(0,r.jsx)(n.Suspense,{fallback:(0,r.jsx)("div",{className:"flex items-center text-sm text-muted-foreground",children:"Loading..."}),children:z})})}),[z,m]);return(0,r.jsxs)("div",{className:(0,i.cn)("mt-4 mb-12",k?"2xl:-ml-12 2xl:-mr-12":""),children:[(0,r.jsxs)("div",{className:(0,i.cn)("relative bg-studio",b?"rounded-tl-md rounded-tr-md border-t border-l border-r":"rounded-md border"),children:[f&&(0,r.jsx)("div",{className:"pointer-events-none absolute h-full w-full bg-[linear-gradient(to_right,hsla(var(--foreground-default)/0.02)_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:24px_24px]"}),y&&(0,r.jsx)("div",{className:"z-0 pointer-events-none absolute h-full w-full bg-[radial-gradient(hsla(var(--foreground-default)/0.02)_1px,transparent_1px)] [background-size:16px_16px] [mask-image:radial-gradient(ellipse_50%_50%_at_50%_50%,#000_70%,transparent_100%)]"}),(0,r.jsx)("div",{className:"z-10 relative",children:_})]}),b&&(0,r.jsxs)(l.Collapsible,{children:[(0,r.jsxs)(l.CollapsibleTrigger,{className:"\n            flex \n            gap-3 items-center \n            w-full\n            font-mono\n            text-xs \n            text-foreground-light\n            px-4 py-4 \n            border border-r \n            group\n            data-[state=closed]:rounded-bl-md data-[state=closed]:rounded-br-md\n            \n        ",children:[(0,r.jsx)(c.A,{className:"transition-all group-data-[state=open]:rotate-90 text-foreground-lighter",size:14}),"View code"]}),(0,r.jsx)(l.CollapsibleContent,{className:"transition-all",children:(0,r.jsx)("div",{className:(0,i.cn)("relative","w-full rounded-md [&_pre]:my-0","[&_pre]:overflow-auto","[&_pre]:max-h-[320px]","[&_pre]:rounded-tr-none [&_pre]:rounded-tl-none [&_pre]:border-t-transparent"),children:D})})]})]})}},39966:(e,t,s)=>{s.d(t,{R:()=>r});let r=[{name:"default",label:"Default"}]},61313:(e,t,s)=>{s.d(t,{U:()=>n});var r=s(63276);let a=(0,s(80898).tG)("config",{style:"default",radius:.5,sonnerPosition:"bottom-right",sonnerExpand:!1});function n(){return(0,r.useAtom)(a)}},66710:(e,t,s)=>{s.d(t,{j:()=>a});var r=s(54990);let a={default:{"password-based-auth-nextjs":{name:"password-based-auth-nextjs",type:"registry:block",registryDependencies:["button","card","input","label"],component:r.lazy(()=>Promise.all([s.e(2551),s.e(9711),s.e(6117)]).then(s.bind(s,96117))),source:"",files:["registry/default/blocks/password-based-auth-nextjs/app/auth/login/page.tsx","registry/default/blocks/password-based-auth-nextjs/app/auth/error/page.tsx","registry/default/blocks/password-based-auth-nextjs/app/protected/page.tsx","registry/default/blocks/password-based-auth-nextjs/app/auth/confirm/route.ts","registry/default/blocks/password-based-auth-nextjs/components/login-form.tsx","registry/default/blocks/password-based-auth-nextjs/middleware.ts","registry/default/blocks/password-based-auth-nextjs/app/auth/sign-up/page.tsx","registry/default/blocks/password-based-auth-nextjs/app/auth/sign-up-success/page.tsx","registry/default/blocks/password-based-auth-nextjs/components/sign-up-form.tsx","registry/default/blocks/password-based-auth-nextjs/app/auth/forgot-password/page.tsx","registry/default/blocks/password-based-auth-nextjs/app/auth/update-password/page.tsx","registry/default/blocks/password-based-auth-nextjs/components/forgot-password-form.tsx","registry/default/blocks/password-based-auth-nextjs/components/update-password-form.tsx","registry/default/blocks/password-based-auth-nextjs/components/logout-button.tsx","registry/default/clients/nextjs/lib/supabase/client.ts","registry/default/clients/nextjs/lib/supabase/middleware.ts","registry/default/clients/nextjs/lib/supabase/server.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"password-based-auth-react":{name:"password-based-auth-react",type:"registry:block",registryDependencies:["button","card","input","label"],component:r.lazy(()=>Promise.all([s.e(2551),s.e(9914)]).then(s.bind(s,69914))),source:"",files:["registry/default/blocks/password-based-auth-react/components/login-form.tsx","registry/default/blocks/password-based-auth-react/components/sign-up-form.tsx","registry/default/blocks/password-based-auth-react/components/forgot-password-form.tsx","registry/default/blocks/password-based-auth-react/components/update-password-form.tsx","registry/default/clients/react/lib/supabase/client.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"password-based-auth-react-router":{name:"password-based-auth-react-router",type:"registry:block",registryDependencies:["button","card","input","label"],component:r.lazy(()=>Promise.all([s.e(9892),s.e(2551),s.e(9711),s.e(2519)]).then(s.bind(s,62519))),source:"",files:["registry/default/blocks/password-based-auth-react-router/app/routes/auth.confirm.tsx","registry/default/blocks/password-based-auth-react-router/app/routes/auth.error.tsx","registry/default/blocks/password-based-auth-react-router/app/routes/forgot-password.tsx","registry/default/blocks/password-based-auth-react-router/app/routes/login.tsx","registry/default/blocks/password-based-auth-react-router/app/routes/logout.tsx","registry/default/blocks/password-based-auth-react-router/app/routes/protected.tsx","registry/default/blocks/password-based-auth-react-router/app/routes/sign-up.tsx","registry/default/blocks/password-based-auth-react-router/app/routes/update-password.tsx","registry/default/blocks/password-based-auth-react-router/app/routes.ts","registry/default/clients/react-router/lib/supabase/client.ts","registry/default/clients/react-router/lib/supabase/server.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"password-based-auth-tanstack":{name:"password-based-auth-tanstack",type:"registry:block",registryDependencies:["button","card","input","label"],component:r.lazy(()=>Promise.all([s.e(2551),s.e(9711),s.e(2688),s.e(2258)]).then(s.bind(s,52258))),source:"",files:["registry/default/blocks/password-based-auth-tanstack/routes/login.tsx","registry/default/blocks/password-based-auth-tanstack/routes/auth/error.tsx","registry/default/blocks/password-based-auth-tanstack/routes/_protected.tsx","registry/default/blocks/password-based-auth-tanstack/routes/_protected/protected.tsx","registry/default/blocks/password-based-auth-tanstack/routes/auth/confirm.ts","registry/default/blocks/password-based-auth-tanstack/components/login-form.tsx","registry/default/blocks/password-based-auth-tanstack/routes/sign-up.tsx","registry/default/blocks/password-based-auth-tanstack/routes/sign-up-success.tsx","registry/default/blocks/password-based-auth-tanstack/components/sign-up-form.tsx","registry/default/blocks/password-based-auth-tanstack/routes/forgot-password.tsx","registry/default/blocks/password-based-auth-tanstack/routes/update-password.tsx","registry/default/blocks/password-based-auth-tanstack/components/forgot-password-form.tsx","registry/default/blocks/password-based-auth-tanstack/components/update-password-form.tsx","registry/default/blocks/password-based-auth-tanstack/lib/supabase/fetch-user-server-fn.ts","registry/default/clients/tanstack/lib/supabase/client.ts","registry/default/clients/tanstack/lib/supabase/server.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"social-auth-nextjs":{name:"social-auth-nextjs",type:"registry:block",registryDependencies:["button","card"],component:r.lazy(()=>Promise.all([s.e(2551),s.e(9711),s.e(660)]).then(s.bind(s,60660))),source:"",files:["registry/default/blocks/social-auth-nextjs/app/auth/login/page.tsx","registry/default/blocks/social-auth-nextjs/app/auth/error/page.tsx","registry/default/blocks/social-auth-nextjs/app/protected/page.tsx","registry/default/blocks/social-auth-nextjs/app/auth/oauth/route.ts","registry/default/blocks/social-auth-nextjs/components/login-form.tsx","registry/default/blocks/social-auth-nextjs/middleware.ts","registry/default/blocks/social-auth-nextjs/components/logout-button.tsx","registry/default/clients/nextjs/lib/supabase/client.ts","registry/default/clients/nextjs/lib/supabase/middleware.ts","registry/default/clients/nextjs/lib/supabase/server.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"social-auth-react":{name:"social-auth-react",type:"registry:block",registryDependencies:["button","card"],component:r.lazy(()=>Promise.all([s.e(2551),s.e(9711),s.e(8465)]).then(s.bind(s,58465))),source:"",files:["registry/default/blocks/social-auth-react/components/login-form.tsx","registry/default/clients/react/lib/supabase/client.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"social-auth-react-router":{name:"social-auth-react-router",type:"registry:block",registryDependencies:["button","card"],component:r.lazy(()=>Promise.all([s.e(9892),s.e(1141)]).then(s.bind(s,33219))),source:"",files:["registry/default/blocks/social-auth-react-router/app/routes/auth.error.tsx","registry/default/blocks/social-auth-react-router/app/routes/auth.oauth.tsx","registry/default/blocks/social-auth-react-router/app/routes/login.tsx","registry/default/blocks/social-auth-react-router/app/routes/logout.tsx","registry/default/blocks/social-auth-react-router/app/routes/protected.tsx","registry/default/blocks/social-auth-react-router/app/routes.ts","registry/default/clients/react-router/lib/supabase/client.ts","registry/default/clients/react-router/lib/supabase/server.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"social-auth-tanstack":{name:"social-auth-tanstack",type:"registry:block",registryDependencies:["button","card"],component:r.lazy(()=>Promise.all([s.e(2551),s.e(9711),s.e(6383)]).then(s.bind(s,26383))),source:"",files:["registry/default/blocks/social-auth-tanstack/components/login-form.tsx","registry/default/blocks/social-auth-tanstack/lib/supabase/fetch-user-server-fn.ts","registry/default/blocks/social-auth-tanstack/routes/_protected.tsx","registry/default/blocks/social-auth-tanstack/routes/_protected/protected.tsx","registry/default/blocks/social-auth-tanstack/routes/auth/error.tsx","registry/default/blocks/social-auth-tanstack/routes/auth/oauth.ts","registry/default/blocks/social-auth-tanstack/routes/login.tsx","registry/default/clients/tanstack/lib/supabase/client.ts","registry/default/clients/tanstack/lib/supabase/server.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"dropzone-nextjs":{name:"dropzone-nextjs",type:"registry:component",registryDependencies:["button"],component:r.lazy(()=>s.e(2323).then(s.bind(s,32323))),source:"",files:["registry/default/blocks/dropzone/components/dropzone.tsx","registry/default/blocks/dropzone/hooks/use-supabase-upload.ts","registry/default/clients/nextjs/lib/supabase/client.ts","registry/default/clients/nextjs/lib/supabase/middleware.ts","registry/default/clients/nextjs/lib/supabase/server.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"dropzone-react":{name:"dropzone-react",type:"registry:component",registryDependencies:["button"],component:r.lazy(()=>s.e(2323).then(s.bind(s,32323))),source:"",files:["registry/default/blocks/dropzone/components/dropzone.tsx","registry/default/blocks/dropzone/hooks/use-supabase-upload.ts","registry/default/clients/react/lib/supabase/client.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"dropzone-react-router":{name:"dropzone-react-router",type:"registry:component",registryDependencies:["button"],component:r.lazy(()=>s.e(2323).then(s.bind(s,32323))),source:"",files:["registry/default/blocks/dropzone/components/dropzone.tsx","registry/default/blocks/dropzone/hooks/use-supabase-upload.ts","registry/default/clients/react-router/lib/supabase/client.ts","registry/default/clients/react-router/lib/supabase/server.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"dropzone-tanstack":{name:"dropzone-tanstack",type:"registry:component",registryDependencies:["button"],component:r.lazy(()=>s.e(2323).then(s.bind(s,32323))),source:"",files:["registry/default/blocks/dropzone/components/dropzone.tsx","registry/default/blocks/dropzone/hooks/use-supabase-upload.ts","registry/default/clients/tanstack/lib/supabase/client.ts","registry/default/clients/tanstack/lib/supabase/server.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"realtime-cursor-nextjs":{name:"realtime-cursor-nextjs",type:"registry:component",registryDependencies:[],component:r.lazy(()=>s.e(3704).then(s.bind(s,23704))),source:"",files:["registry/default/blocks/realtime-cursor/components/cursor.tsx","registry/default/blocks/realtime-cursor/components/realtime-cursors.tsx","registry/default/blocks/realtime-cursor/hooks/use-realtime-cursors.ts","registry/default/clients/nextjs/lib/supabase/client.ts","registry/default/clients/nextjs/lib/supabase/middleware.ts","registry/default/clients/nextjs/lib/supabase/server.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"realtime-cursor-react":{name:"realtime-cursor-react",type:"registry:component",registryDependencies:[],component:r.lazy(()=>s.e(3704).then(s.bind(s,23704))),source:"",files:["registry/default/blocks/realtime-cursor/components/cursor.tsx","registry/default/blocks/realtime-cursor/components/realtime-cursors.tsx","registry/default/blocks/realtime-cursor/hooks/use-realtime-cursors.ts","registry/default/clients/react/lib/supabase/client.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"realtime-cursor-react-router":{name:"realtime-cursor-react-router",type:"registry:component",registryDependencies:[],component:r.lazy(()=>s.e(3704).then(s.bind(s,23704))),source:"",files:["registry/default/blocks/realtime-cursor/components/cursor.tsx","registry/default/blocks/realtime-cursor/components/realtime-cursors.tsx","registry/default/blocks/realtime-cursor/hooks/use-realtime-cursors.ts","registry/default/clients/react-router/lib/supabase/client.ts","registry/default/clients/react-router/lib/supabase/server.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"realtime-cursor-tanstack":{name:"realtime-cursor-tanstack",type:"registry:component",registryDependencies:[],component:r.lazy(()=>s.e(3704).then(s.bind(s,23704))),source:"",files:["registry/default/blocks/realtime-cursor/components/cursor.tsx","registry/default/blocks/realtime-cursor/components/realtime-cursors.tsx","registry/default/blocks/realtime-cursor/hooks/use-realtime-cursors.ts","registry/default/clients/tanstack/lib/supabase/client.ts","registry/default/clients/tanstack/lib/supabase/server.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"current-user-avatar-nextjs":{name:"current-user-avatar-nextjs",type:"registry:component",registryDependencies:["avatar"],component:r.lazy(()=>Promise.all([s.e(2551),s.e(9711),s.e(3696)]).then(s.bind(s,3696))),source:"",files:["registry/default/blocks/current-user-avatar/components/current-user-avatar.tsx","registry/default/blocks/current-user-avatar/hooks/use-current-user-name.ts","registry/default/blocks/current-user-avatar/hooks/use-current-user-image.ts","registry/default/clients/nextjs/lib/supabase/client.ts","registry/default/clients/nextjs/lib/supabase/middleware.ts","registry/default/clients/nextjs/lib/supabase/server.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"current-user-avatar-react":{name:"current-user-avatar-react",type:"registry:component",registryDependencies:["avatar"],component:r.lazy(()=>Promise.all([s.e(2551),s.e(9711),s.e(3696)]).then(s.bind(s,3696))),source:"",files:["registry/default/blocks/current-user-avatar/components/current-user-avatar.tsx","registry/default/blocks/current-user-avatar/hooks/use-current-user-name.ts","registry/default/blocks/current-user-avatar/hooks/use-current-user-image.ts","registry/default/clients/react/lib/supabase/client.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"current-user-avatar-react-router":{name:"current-user-avatar-react-router",type:"registry:component",registryDependencies:["avatar"],component:r.lazy(()=>Promise.all([s.e(2551),s.e(9711),s.e(3696)]).then(s.bind(s,3696))),source:"",files:["registry/default/blocks/current-user-avatar/components/current-user-avatar.tsx","registry/default/blocks/current-user-avatar/hooks/use-current-user-name.ts","registry/default/blocks/current-user-avatar/hooks/use-current-user-image.ts","registry/default/clients/react-router/lib/supabase/client.ts","registry/default/clients/react-router/lib/supabase/server.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"current-user-avatar-tanstack":{name:"current-user-avatar-tanstack",type:"registry:component",registryDependencies:["avatar"],component:r.lazy(()=>Promise.all([s.e(2551),s.e(9711),s.e(3696)]).then(s.bind(s,3696))),source:"",files:["registry/default/blocks/current-user-avatar/components/current-user-avatar.tsx","registry/default/blocks/current-user-avatar/hooks/use-current-user-name.ts","registry/default/blocks/current-user-avatar/hooks/use-current-user-image.ts","registry/default/clients/tanstack/lib/supabase/client.ts","registry/default/clients/tanstack/lib/supabase/server.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"realtime-avatar-stack-nextjs":{name:"realtime-avatar-stack-nextjs",type:"registry:component",registryDependencies:["avatar","tooltip"],component:r.lazy(()=>s.e(8216).then(s.bind(s,78216))),source:"",files:["registry/default/blocks/realtime-avatar-stack/components/avatar-stack.tsx","registry/default/blocks/realtime-avatar-stack/components/realtime-avatar-stack.tsx","registry/default/blocks/realtime-avatar-stack/hooks/use-realtime-presence-room.ts","registry/default/blocks/current-user-avatar/hooks/use-current-user-name.ts","registry/default/blocks/current-user-avatar/hooks/use-current-user-image.ts","registry/default/clients/nextjs/lib/supabase/client.ts","registry/default/clients/nextjs/lib/supabase/middleware.ts","registry/default/clients/nextjs/lib/supabase/server.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"realtime-avatar-stack-react":{name:"realtime-avatar-stack-react",type:"registry:component",registryDependencies:["avatar","tooltip"],component:r.lazy(()=>s.e(8216).then(s.bind(s,78216))),source:"",files:["registry/default/blocks/realtime-avatar-stack/components/avatar-stack.tsx","registry/default/blocks/realtime-avatar-stack/components/realtime-avatar-stack.tsx","registry/default/blocks/realtime-avatar-stack/hooks/use-realtime-presence-room.ts","registry/default/blocks/current-user-avatar/hooks/use-current-user-name.ts","registry/default/blocks/current-user-avatar/hooks/use-current-user-image.ts","registry/default/clients/react/lib/supabase/client.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"realtime-avatar-stack-react-router":{name:"realtime-avatar-stack-react-router",type:"registry:component",registryDependencies:["avatar","tooltip"],component:r.lazy(()=>s.e(8216).then(s.bind(s,78216))),source:"",files:["registry/default/blocks/realtime-avatar-stack/components/avatar-stack.tsx","registry/default/blocks/realtime-avatar-stack/components/realtime-avatar-stack.tsx","registry/default/blocks/realtime-avatar-stack/hooks/use-realtime-presence-room.ts","registry/default/blocks/current-user-avatar/hooks/use-current-user-name.ts","registry/default/blocks/current-user-avatar/hooks/use-current-user-image.ts","registry/default/clients/react-router/lib/supabase/client.ts","registry/default/clients/react-router/lib/supabase/server.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"realtime-avatar-stack-tanstack":{name:"realtime-avatar-stack-tanstack",type:"registry:component",registryDependencies:["avatar","tooltip"],component:r.lazy(()=>s.e(8216).then(s.bind(s,78216))),source:"",files:["registry/default/blocks/realtime-avatar-stack/components/avatar-stack.tsx","registry/default/blocks/realtime-avatar-stack/components/realtime-avatar-stack.tsx","registry/default/blocks/realtime-avatar-stack/hooks/use-realtime-presence-room.ts","registry/default/blocks/current-user-avatar/hooks/use-current-user-name.ts","registry/default/blocks/current-user-avatar/hooks/use-current-user-image.ts","registry/default/clients/tanstack/lib/supabase/client.ts","registry/default/clients/tanstack/lib/supabase/server.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"realtime-chat-nextjs":{name:"realtime-chat-nextjs",type:"registry:component",registryDependencies:["input","button"],component:r.lazy(()=>s.e(4907).then(s.bind(s,94907))),source:"",files:["registry/default/blocks/realtime-chat/components/chat-message.tsx","registry/default/blocks/realtime-chat/components/realtime-chat.tsx","registry/default/blocks/realtime-chat/hooks/use-realtime-chat.tsx","registry/default/blocks/realtime-chat/hooks/use-chat-scroll.tsx","registry/default/clients/nextjs/lib/supabase/client.ts","registry/default/clients/nextjs/lib/supabase/middleware.ts","registry/default/clients/nextjs/lib/supabase/server.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"realtime-chat-react":{name:"realtime-chat-react",type:"registry:component",registryDependencies:["input","button"],component:r.lazy(()=>s.e(4907).then(s.bind(s,94907))),source:"",files:["registry/default/blocks/realtime-chat/components/chat-message.tsx","registry/default/blocks/realtime-chat/components/realtime-chat.tsx","registry/default/blocks/realtime-chat/hooks/use-realtime-chat.tsx","registry/default/blocks/realtime-chat/hooks/use-chat-scroll.tsx","registry/default/clients/react/lib/supabase/client.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"realtime-chat-react-router":{name:"realtime-chat-react-router",type:"registry:component",registryDependencies:["input","button"],component:r.lazy(()=>s.e(4907).then(s.bind(s,94907))),source:"",files:["registry/default/blocks/realtime-chat/components/chat-message.tsx","registry/default/blocks/realtime-chat/components/realtime-chat.tsx","registry/default/blocks/realtime-chat/hooks/use-realtime-chat.tsx","registry/default/blocks/realtime-chat/hooks/use-chat-scroll.tsx","registry/default/clients/react-router/lib/supabase/client.ts","registry/default/clients/react-router/lib/supabase/server.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"realtime-chat-tanstack":{name:"realtime-chat-tanstack",type:"registry:component",registryDependencies:["input","button"],component:r.lazy(()=>s.e(4907).then(s.bind(s,94907))),source:"",files:["registry/default/blocks/realtime-chat/components/chat-message.tsx","registry/default/blocks/realtime-chat/components/realtime-chat.tsx","registry/default/blocks/realtime-chat/hooks/use-realtime-chat.tsx","registry/default/blocks/realtime-chat/hooks/use-chat-scroll.tsx","registry/default/clients/tanstack/lib/supabase/client.ts","registry/default/clients/tanstack/lib/supabase/server.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"infinite-query-hook":{name:"infinite-query-hook",type:"registry:block",registryDependencies:[],source:"",files:["registry/default/blocks/infinite-query-hook/hooks/use-infinite-query.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"supabase-client-nextjs":{name:"supabase-client-nextjs",type:"registry:lib",registryDependencies:[],source:"",files:["registry/default/clients/nextjs/lib/supabase/client.ts","registry/default/clients/nextjs/lib/supabase/middleware.ts","registry/default/clients/nextjs/lib/supabase/server.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"supabase-client-react":{name:"supabase-client-react",type:"registry:lib",registryDependencies:[],source:"",files:["registry/default/clients/react/lib/supabase/client.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"supabase-client-react-router":{name:"supabase-client-react-router",type:"registry:lib",registryDependencies:[],source:"",files:["registry/default/clients/react-router/lib/supabase/client.ts","registry/default/clients/react-router/lib/supabase/server.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"supabase-client-tanstack":{name:"supabase-client-tanstack",type:"registry:lib",registryDependencies:[],source:"",files:["registry/default/clients/tanstack/lib/supabase/client.ts","registry/default/clients/tanstack/lib/supabase/server.ts"],category:"undefined",subcategory:"undefined",chunks:[]},"platform-kit-nextjs":{name:"platform-kit-nextjs",type:"registry:block",registryDependencies:["alert","badge","button","card","chart","command","dialog","drawer","form","hover-card","input","label","popover","select","skeleton","switch","table","toggle","toggle-group","tooltip"],component:r.lazy(()=>s.e(9496).then(s.bind(s,9496))),source:"",files:["registry/default/platform/platform-kit-nextjs/app/api/ai/sql/route.ts","registry/default/platform/platform-kit-nextjs/app/api/supabase-proxy/[...path]/route.ts","registry/default/platform/platform-kit-nextjs/components/dynamic-form.tsx","registry/default/platform/platform-kit-nextjs/components/logo-supabase.tsx","registry/default/platform/platform-kit-nextjs/components/results-table.tsx","registry/default/platform/platform-kit-nextjs/components/sql-editor.tsx","registry/default/platform/platform-kit-nextjs/components/supabase-manager/auth.tsx","registry/default/platform/platform-kit-nextjs/components/supabase-manager/database.tsx","registry/default/platform/platform-kit-nextjs/components/supabase-manager/index.tsx","registry/default/platform/platform-kit-nextjs/components/supabase-manager/logs.tsx","registry/default/platform/platform-kit-nextjs/components/supabase-manager/secrets.tsx","registry/default/platform/platform-kit-nextjs/components/supabase-manager/storage.tsx","registry/default/platform/platform-kit-nextjs/components/supabase-manager/suggestions.tsx","registry/default/platform/platform-kit-nextjs/components/supabase-manager/users.tsx","registry/default/platform/platform-kit-nextjs/components/users-growth-chart.tsx","registry/default/platform/platform-kit-nextjs/hooks/use-auth.ts","registry/default/platform/platform-kit-nextjs/hooks/use-logs.ts","registry/default/platform/platform-kit-nextjs/hooks/use-run-query.ts","registry/default/platform/platform-kit-nextjs/hooks/use-secrets.ts","registry/default/platform/platform-kit-nextjs/hooks/use-storage.ts","registry/default/platform/platform-kit-nextjs/hooks/use-suggestions.ts","registry/default/platform/platform-kit-nextjs/hooks/use-tables.ts","registry/default/platform/platform-kit-nextjs/hooks/use-user-counts.ts","registry/default/platform/platform-kit-nextjs/lib/logs.ts","registry/default/platform/platform-kit-nextjs/lib/management-api-schema.d.ts","registry/default/platform/platform-kit-nextjs/lib/management-api.ts","registry/default/platform/platform-kit-nextjs/lib/pg-meta/sql.ts","registry/default/platform/platform-kit-nextjs/lib/pg-meta/index.ts","registry/default/platform/platform-kit-nextjs/lib/pg-meta/types.ts","registry/default/platform/platform-kit-nextjs/lib/schemas/auth.ts","registry/default/platform/platform-kit-nextjs/lib/schemas/secrets.ts","registry/default/platform/platform-kit-nextjs/contexts/SheetNavigationContext.tsx"],category:"undefined",subcategory:"undefined",chunks:[]},"ai-editor-rules":{name:"ai-editor-rules",type:"registry:file",registryDependencies:[],source:"",files:["registry/default/ai-editor-rules/create-db-functions.mdc","registry/default/ai-editor-rules/create-migration.mdc","registry/default/ai-editor-rules/create-rls-policies.mdc","registry/default/ai-editor-rules/postgres-sql-style-guide.mdc","registry/default/ai-editor-rules/writing-supabase-edge-functions.mdc"],category:"undefined",subcategory:"undefined",chunks:[]},"dropzone-demo":{name:"dropzone-demo",type:"registry:example",registryDependencies:[],component:r.lazy(()=>Promise.all([s.e(2551),s.e(9711),s.e(4994),s.e(3605)]).then(s.bind(s,43605))),source:"",files:["registry/default/examples/dropzone-demo.tsx"],category:"undefined",subcategory:"undefined",chunks:[]},"realtime-cursor-demo":{name:"realtime-cursor-demo",type:"registry:example",registryDependencies:[],component:r.lazy(()=>Promise.all([s.e(2551),s.e(9711),s.e(9498)]).then(s.bind(s,49498))),source:"",files:["registry/default/examples/realtime-cursor-demo.tsx"],category:"undefined",subcategory:"undefined",chunks:[]},"password-based-auth-demo":{name:"password-based-auth-demo",type:"registry:example",registryDependencies:[],component:r.lazy(()=>Promise.all([s.e(2551),s.e(9711),s.e(4552)]).then(s.bind(s,72171))),source:"",files:["registry/default/examples/password-based-auth.tsx"],category:"undefined",subcategory:"undefined",chunks:[]},"current-user-avatar-demo":{name:"current-user-avatar-demo",type:"registry:example",registryDependencies:[],component:r.lazy(()=>s.e(9875).then(s.bind(s,69875))),source:"",files:["registry/default/examples/current-user-avatar-demo.tsx"],category:"undefined",subcategory:"undefined",chunks:[]},"current-user-avatar-preview":{name:"current-user-avatar-preview",type:"registry:example",registryDependencies:[],component:r.lazy(()=>s.e(8590).then(s.bind(s,38590))),source:"",files:["registry/default/examples/current-user-avatar-preview.tsx"],category:"undefined",subcategory:"undefined",chunks:[]},"realtime-avatar-stack-demo":{name:"realtime-avatar-stack-demo",type:"registry:example",registryDependencies:[],component:r.lazy(()=>Promise.all([s.e(2551),s.e(9711),s.e(3384)]).then(s.bind(s,73384))),source:"",files:["registry/default/examples/realtime-avatar-stack-demo.tsx"],category:"undefined",subcategory:"undefined",chunks:[]},"realtime-avatar-stack-preview":{name:"realtime-avatar-stack-preview",type:"registry:example",registryDependencies:[],component:r.lazy(()=>s.e(7015).then(s.bind(s,7015))),source:"",files:["registry/default/examples/realtime-avatar-stack-preview.tsx"],category:"undefined",subcategory:"undefined",chunks:[]},"infinite-query-hook-demo":{name:"infinite-query-hook-demo",type:"registry:example",registryDependencies:[],component:r.lazy(()=>Promise.all([s.e(2551),s.e(9711),s.e(9271)]).then(s.bind(s,29271))),source:"",files:["registry/default/examples/infinite-query-hook-demo.tsx"],category:"undefined",subcategory:"undefined",chunks:[]}}}}}]);