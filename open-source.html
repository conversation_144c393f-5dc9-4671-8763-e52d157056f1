<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="rss.xml" data-next-head=""/><link rel="manifest" href="favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:type" content="website" data-next-head=""/><meta property="og:image:alt" content="Supabase Og Image" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Supabase Open Source Community</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Supabase is an open source company, actively fostering collaboration and supporting existing open source tools and communities." data-next-head=""/><meta property="og:title" content="Supabase Open Source Community" data-next-head=""/><meta property="og:description" content="Supabase is an open source company, actively fostering collaboration and supporting existing open source tools and communities." data-next-head=""/><meta property="og:url" content="https://supabase.com//open-source" data-next-head=""/><meta property="og:image" content="https://supabase.com/images/og/supabase-og.png" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/19ca6a02fa2c793e.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/19ca6a02fa2c793e.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-p=""/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/1023-5842c2ab78118ff0.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2263-07e8ebb96cc2b014.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/335-fd132d9f9490d44a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/open-source-78bb3e1bf21693ae.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqcla6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="_next/supabase-logo-wordmark--light.png 1x, _next/supabase-logo-wordmark--light.png 2x" src="_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="_next/supabase-logo-wordmark--dark.png 1x, _next/supabase-logo-wordmark--dark.png 2x" src="_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R1eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R1eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R2eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R2eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqcla6:-trigger-radix-:R3eaqcla6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqcla6:-content-radix-:R3eaqcla6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav></div><main class="min-h-screen relative"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 overflow-hidden relative mx-auto !py-0 sm:!py-0 md:!py-4 lg:!pt-16 lg:!pb-12"><div class="container relative w-full mx-auto px-6 pt-2 pb-0 sm:px-16 xl:px-20"><div class="flex flex-col text-center items-center"><div class="col-span-12 mt-8 lg:col-span-7 lg:mt-0 xl:col-span-6 xl:col-start-7"></div><div class="relative w-full z-10 flex flex-col items-center space-y-2 mx-auto max-w-2xl"><div><div class="mb-2 flex justify-center items-center gap-3"><span class="text-brand font-mono uppercase tracking-widest text-sm">The Power of Collaboration</span></div></div><div class="animations_appear-from-bottom__Z4upz"><h1 class="h1 text-3xl md:text-4xl tracking-[-1.5px]"><span class="heading-gradient">Open Source Community</span></h1><p class="p !text-foreground-light">Supabase is an open source company, actively fostering collaboration<br class="hidden md:inline"/> and supporting existing open source tools and communities.</p></div><div class="w-full sm:w-auto flex flex-col items-stretch sm:flex-row pt-2 sm:items-center gap-2"></div></div></div></div></div><div class="absolute z-[-4] flex flex-col top-0 left-0 w-full h-[500px] overflow-hidden pointer-events-none"><div class="absolute bottom-0 z-[1] w-full h-4/5 bg-gradient-to-t from-background to-transparent"></div><div class="absolute top-0 z-[1] w-full h-2/5 bg-gradient-to-b from-background to-transparent"></div></div><div class="absolute inset-0 w-full h-[300px] lg:h-[500px] overflow-hidden pointer-events-none hero_hero-container__dEpXQ"><div class="absolute select-none pointer-events-none inset-0 z-[3] blur-[100px] w-full h-full opacity-60" style="transform:translateZ(0px)"></div><div class="absolute bottom-0 z-[-2] w-full h-full bg-gradient-to-t from-background to-transparent"></div><div class="relative -z-10 ![perspective:1200px] sm:![perspective:1200px] md:![perspective:1200px] lg:![perspective:1200px]"><div style="transform:rotateX(85deg);position:absolute;top:0;bottom:0;left:0;right:0"><div class="hero_hero-grid-lines__geWjS"><svg preserveAspectRatio="none" width="100%" height="100%" viewBox="0 0 Infinity Infinity" fill="none" xmlns="http://www.w3.org/2000/svg"><line x1="Infinity" y1="NaN" x2="0" y2="NaN" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="0" y2="Infinity" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="NaN" y1="Infinity" x2="NaN" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="url(#electric-pulse)" stroke-linecap="round" stroke-width="4" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="url(#electric-pulse-2)" stroke-linecap="round" stroke-width="4" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><line x1="Infinity" y1="Infinity" x2="Infinity" y2="0" stroke="#015a42" stroke-width="1" shape-rendering="geometricPrecision"></line><defs><linearGradient id="electric-pulse" gradientUnits="userSpaceOnUse"><stop stop-color="#015a42" stop-opacity="0"></stop><stop stop-color="#015a42" stop-opacity="0.8"></stop><stop offset="1" stop-color="var(--colors-brand9)" stop-opacity="0"></stop></linearGradient><linearGradient id="electric-pulse-2" gradientUnits="userSpaceOnUse"><stop stop-color="#015a42" stop-opacity="0"></stop><stop stop-color="#015a42" stop-opacity="0.8"></stop><stop offset="1" stop-color="var(--colors-brand9)" stop-opacity="0"></stop></linearGradient></defs></svg></div></div></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !pt-0"><div class="flex flex-col gap-8 xl:gap-10"><div class="flex flex-wrap mx-auto items-center justify-center gap-y-6 gap-x-8 text-sm"><a class="text-foreground-lighter hover:underline flex gap-1 items-center" target="_blank" href="https://github.com/supabase/supabase/blob/master/DEVELOPERS.md"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="fill-foreground-lighter grouop-hover:fill-foreground w-4 h-4"><path fill-rule="evenodd" clip-rule="evenodd" d="M12 3.33215C7.09969 3.33215 3.12744 7.31061 3.12744 12.2198C3.12744 16.1459 5.66943 19.4775 9.19538 20.6523C9.63901 20.7339 9.80049 20.4597 9.80049 20.2237C9.80049 20.0135 9.7934 19.4536 9.78896 18.7127C7.32061 19.2495 6.79979 17.5211 6.79979 17.5211C6.39698 16.4937 5.81494 16.2204 5.81494 16.2204C5.00931 15.6703 5.87616 15.681 5.87616 15.681C6.76608 15.7431 7.23455 16.5966 7.23455 16.5966C8.02598 17.9541 9.31161 17.562 9.81646 17.3348C9.89809 16.7608 10.127 16.3695 10.3808 16.1477C8.41105 15.9232 6.33931 15.1602 6.33931 11.7549C6.33931 10.7851 6.68534 9.99101 7.25229 9.36993C7.16091 9.14545 6.85658 8.24134 7.33925 7.0187C7.33925 7.0187 8.08454 6.77914 9.7792 7.92903C10.503 7.73162 11.2498 7.63108 12 7.63002C12.7542 7.63357 13.5128 7.73206 14.2217 7.92903C15.9155 6.77914 16.659 7.01781 16.659 7.01781C17.1434 8.24134 16.8382 9.14545 16.7477 9.36993C17.3155 9.99101 17.6598 10.7851 17.6598 11.7549C17.6598 15.169 15.5845 15.9205 13.6086 16.1406C13.9271 16.4147 14.2102 16.9569 14.2102 17.7864C14.2102 18.9736 14.1995 19.9327 14.1995 20.2237C14.1995 20.4615 14.3592 20.7383 14.8099 20.6514C16.5767 20.0588 18.1126 18.9259 19.2005 17.4129C20.2884 15.8999 20.8733 14.0833 20.8726 12.2198C20.8726 7.31061 16.8994 3.33215 12 3.33215Z" fill="currentColor"></path></svg>How to contribute</a><a class="text-foreground-lighter hover:underline flex gap-1 items-center" target="_blank" href="https://github.com/supabase/.github/blob/main/CODE_OF_CONDUCT.md"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="fill-foreground-lighter grouop-hover:fill-foreground w-4 h-4"><path fill-rule="evenodd" clip-rule="evenodd" d="M12 3.33215C7.09969 3.33215 3.12744 7.31061 3.12744 12.2198C3.12744 16.1459 5.66943 19.4775 9.19538 20.6523C9.63901 20.7339 9.80049 20.4597 9.80049 20.2237C9.80049 20.0135 9.7934 19.4536 9.78896 18.7127C7.32061 19.2495 6.79979 17.5211 6.79979 17.5211C6.39698 16.4937 5.81494 16.2204 5.81494 16.2204C5.00931 15.6703 5.87616 15.681 5.87616 15.681C6.76608 15.7431 7.23455 16.5966 7.23455 16.5966C8.02598 17.9541 9.31161 17.562 9.81646 17.3348C9.89809 16.7608 10.127 16.3695 10.3808 16.1477C8.41105 15.9232 6.33931 15.1602 6.33931 11.7549C6.33931 10.7851 6.68534 9.99101 7.25229 9.36993C7.16091 9.14545 6.85658 8.24134 7.33925 7.0187C7.33925 7.0187 8.08454 6.77914 9.7792 7.92903C10.503 7.73162 11.2498 7.63108 12 7.63002C12.7542 7.63357 13.5128 7.73206 14.2217 7.92903C15.9155 6.77914 16.659 7.01781 16.659 7.01781C17.1434 8.24134 16.8382 9.14545 16.7477 9.36993C17.3155 9.99101 17.6598 10.7851 17.6598 11.7549C17.6598 15.169 15.5845 15.9205 13.6086 16.1406C13.9271 16.4147 14.2102 16.9569 14.2102 17.7864C14.2102 18.9736 14.1995 19.9327 14.1995 20.2237C14.1995 20.4615 14.3592 20.7383 14.8099 20.6514C16.5767 20.0588 18.1126 18.9259 19.2005 17.4129C20.2884 15.8999 20.8733 14.0833 20.8726 12.2198C20.8726 7.31061 16.8994 3.33215 12 3.33215Z" fill="currentColor"></path></svg>Code of Conduct</a><a class="text-foreground-lighter hover:underline flex gap-1.5 items-center" href="open-source/contributing/supasquad.html"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users w-4 h-4"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>SupaSquad</a><a class="text-foreground-lighter hover:underline flex gap-1.5 items-center" target="_blank" href="https://docs.google.com/forms/d/e/1FAIpQLSfJoQ6_uWymc4DJok2YVY8K_jp27S6HrnOIKmtHuDhBCWetDg/viewform?pli=1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-pencil w-4 h-4"><path d="M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z"></path><path d="m15 5 4 4"></path></svg>Community Content Program</a></div><div class="w-full gap-2 flex flex-col items-center"><div class="relative flex border justify-center h-fit max-w-full w-full md:w-auto overflow-hidden items-center rounded-full bg-surface-100 [&amp;_.swiper-wrapper]:w-full [&amp;_.swiper-slide]:w-fit"><div class="swiper relative flex md:!hidden justify-center max-w-full w-full overflow-hidden items-center rounded-full bg-surface-100 p-2" style="padding:10px"><div class="swiper-wrapper"><div class="swiper-slide"><button class="rounded-full px-4 md:px-3 py-2 md:py-1 nowrap flex group gap-1 transition-all text-strong bg-surface-300" aria-selected="true" role="tab"><div class="flex flex-nowrap text-sm lg:text-base gap-1 lg:gap-2 items-center"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="shrink-0 text-brand"><path d="M3.502 6h8.996v4H3.502V6ZM3 10.002h10v4H3v-4ZM3 2h10v4H3V2Z" stroke="currentColor" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path></svg><span class="whitespace-nowrap tracking-tight lg:tracking-normal">Database</span></div></button></div><div class="swiper-slide"><button class="rounded-full px-4 md:px-3 py-2 md:py-1 nowrap flex group gap-1 transition-all text-foreground-lighter bg-surface-200 hover:bg-overlay-hover" aria-selected="false" role="tab"><div class="flex flex-nowrap text-sm lg:text-base gap-1 lg:gap-2 items-center"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-foreground-light shrink-0"><path d="M3.49414 9.97461H8.49414M3.49414 9.97461V11.9746H8.49414V9.97461M3.49414 9.97461V7.97461H8.49414V9.97461M10 5V3C10 1.89543 9.10457 1 8 1C6.89543 1 6 1.89543 6 3V5M3.47266 7L3.47266 12C3.47266 13.1046 4.36809 14 5.47266 14H10.4727C11.5772 14 12.4727 13.1046 12.4727 12V7C12.4727 5.89543 11.5772 5 10.4727 5L5.47266 5C4.36809 5 3.47266 5.89543 3.47266 7Z" stroke="currentColor" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path></svg><span class="whitespace-nowrap tracking-tight lg:tracking-normal">Auth</span></div></button></div><div class="swiper-slide"><button class="rounded-full px-4 md:px-3 py-2 md:py-1 nowrap flex group gap-1 transition-all text-foreground-lighter bg-surface-200 hover:bg-overlay-hover" aria-selected="false" role="tab"><div class="flex flex-nowrap text-sm lg:text-base gap-1 lg:gap-2 items-center"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-foreground-light shrink-0"><path d="M12.9997 7.50869V5.60119L9.38151 2.00024H3.99967C3.44739 2.00024 2.99967 2.44796 2.99967 3.00024V5.99976M12.9645 5.58447L9.38004 2L9.38004 4.58447C9.38004 5.13676 9.82776 5.58447 10.38 5.58447L12.9645 5.58447ZM4.44135 5.99976H2.97363C2.42135 5.99976 1.97363 6.44747 1.97363 6.99976V11.9998C1.97363 13.1043 2.86906 13.9998 3.97363 13.9998H11.9736C13.0782 13.9998 13.9736 13.1043 13.9736 11.9998V8.50869C13.9736 7.95641 13.5259 7.50869 12.9736 7.50869H6.79396C6.53157 7.50869 6.27968 7.40556 6.09263 7.22153L5.14268 6.28692C4.95563 6.10289 4.70375 5.99976 4.44135 5.99976Z" stroke="currentColor" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path></svg><span class="whitespace-nowrap tracking-tight lg:tracking-normal">Storage</span></div></button></div><div class="swiper-slide"><button class="rounded-full px-4 md:px-3 py-2 md:py-1 nowrap flex group gap-1 transition-all text-foreground-lighter bg-surface-200 hover:bg-overlay-hover" aria-selected="false" role="tab"><div class="flex flex-nowrap text-sm lg:text-base gap-1 lg:gap-2 items-center"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-foreground-light shrink-0"><path d="M1.857 11.36a7 7 0 0 1 9.41-9.551M4.774 14.212a7 7 0 0 0 9.41-9.497m-8.812 7.845a2 2 0 1 1-4 0 2 2 0 0 1 4 0Zm9.296-9.13a2 2 0 1 1-4 0 2 2 0 0 1 4 0ZM12.5 8a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0Z" stroke="currentColor" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path></svg><span class="whitespace-nowrap tracking-tight lg:tracking-normal">Edge Functions</span></div></button></div><div class="swiper-slide"><button class="rounded-full px-4 md:px-3 py-2 md:py-1 nowrap flex group gap-1 transition-all text-foreground-lighter bg-surface-200 hover:bg-overlay-hover" aria-selected="false" role="tab"><div class="flex flex-nowrap text-sm lg:text-base gap-1 lg:gap-2 items-center"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-foreground-light shrink-0"><path d="M5.362.984v2.35m-1.866.144L1.365 1.282m2.052 3.92H1.052m8.023 9.653L4.557 4.523 15 9.115l-4.748 1.182-1.177 4.558Z" stroke="currentColor" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path></svg><span class="whitespace-nowrap tracking-tight lg:tracking-normal">Realtime</span></div></button></div><div class="swiper-slide"><button class="rounded-full px-4 md:px-3 py-2 md:py-1 nowrap flex group gap-1 transition-all text-foreground-lighter bg-surface-200 hover:bg-overlay-hover" aria-selected="false" role="tab"><div class="flex flex-nowrap text-sm lg:text-base gap-1 lg:gap-2 items-center"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-foreground-light shrink-0"><path d="M7.99886 7.63216V14.4892M7.99886 7.63216L14.0488 4.11804M7.99886 7.63216L1.94922 4.11819M1.94922 4.11819V8.32332M1.94922 4.11819V4.08217L5.57319 1.97717M14.049 8.36007V4.08217L10.4251 1.97717M11.8165 12.4072L7.99913 14.6245L4.18177 12.4072" stroke="currentColor" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path></svg><span class="whitespace-nowrap tracking-tight lg:tracking-normal">Vector</span></div></button></div><div class="swiper-slide"><button class="rounded-full px-4 md:px-3 py-2 md:py-1 nowrap flex group gap-1 transition-all text-foreground-lighter bg-surface-200 hover:bg-overlay-hover" aria-selected="false" role="tab"><div class="flex flex-nowrap text-sm lg:text-base gap-1 lg:gap-2 items-center"><span class="whitespace-nowrap tracking-tight lg:tracking-normal">Other</span></div></button></div></div><div class="not-sr-only absolute inset-0 left-auto bg-gradient-to-r from-transparent to-background-surface-100 w-10 z-20 pointer-events-none transition-opacity opacity-100"></div><div class="not-sr-only absolute inset-0 right-auto bg-gradient-to-l from-transparent to-background-surface-100 w-10 z-20 pointer-events-none opacity-0 transition-opacity"></div></div><div class="hidden md:flex flex-nowrap overflow-x-scroll items-center p-2 md:p-1 gap-2 no-scrollbar"><button class="rounded-full px-4 md:px-3 py-2 md:py-1 nowrap flex group gap-1 transition-all text-strong bg-surface-300" aria-selected="true" role="tab"><div class="flex flex-nowrap text-sm lg:text-base gap-1 lg:gap-2 items-center"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="shrink-0 text-brand"><path d="M3.502 6h8.996v4H3.502V6ZM3 10.002h10v4H3v-4ZM3 2h10v4H3V2Z" stroke="currentColor" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path></svg><span class="whitespace-nowrap tracking-tight lg:tracking-normal">Database</span></div></button><button class="rounded-full px-4 md:px-3 py-2 md:py-1 nowrap flex group gap-1 transition-all text-foreground-lighter bg-surface-200 hover:bg-overlay-hover" aria-selected="false" role="tab"><div class="flex flex-nowrap text-sm lg:text-base gap-1 lg:gap-2 items-center"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-foreground-light shrink-0"><path d="M3.49414 9.97461H8.49414M3.49414 9.97461V11.9746H8.49414V9.97461M3.49414 9.97461V7.97461H8.49414V9.97461M10 5V3C10 1.89543 9.10457 1 8 1C6.89543 1 6 1.89543 6 3V5M3.47266 7L3.47266 12C3.47266 13.1046 4.36809 14 5.47266 14H10.4727C11.5772 14 12.4727 13.1046 12.4727 12V7C12.4727 5.89543 11.5772 5 10.4727 5L5.47266 5C4.36809 5 3.47266 5.89543 3.47266 7Z" stroke="currentColor" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path></svg><span class="whitespace-nowrap tracking-tight lg:tracking-normal">Auth</span></div></button><button class="rounded-full px-4 md:px-3 py-2 md:py-1 nowrap flex group gap-1 transition-all text-foreground-lighter bg-surface-200 hover:bg-overlay-hover" aria-selected="false" role="tab"><div class="flex flex-nowrap text-sm lg:text-base gap-1 lg:gap-2 items-center"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-foreground-light shrink-0"><path d="M12.9997 7.50869V5.60119L9.38151 2.00024H3.99967C3.44739 2.00024 2.99967 2.44796 2.99967 3.00024V5.99976M12.9645 5.58447L9.38004 2L9.38004 4.58447C9.38004 5.13676 9.82776 5.58447 10.38 5.58447L12.9645 5.58447ZM4.44135 5.99976H2.97363C2.42135 5.99976 1.97363 6.44747 1.97363 6.99976V11.9998C1.97363 13.1043 2.86906 13.9998 3.97363 13.9998H11.9736C13.0782 13.9998 13.9736 13.1043 13.9736 11.9998V8.50869C13.9736 7.95641 13.5259 7.50869 12.9736 7.50869H6.79396C6.53157 7.50869 6.27968 7.40556 6.09263 7.22153L5.14268 6.28692C4.95563 6.10289 4.70375 5.99976 4.44135 5.99976Z" stroke="currentColor" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path></svg><span class="whitespace-nowrap tracking-tight lg:tracking-normal">Storage</span></div></button><button class="rounded-full px-4 md:px-3 py-2 md:py-1 nowrap flex group gap-1 transition-all text-foreground-lighter bg-surface-200 hover:bg-overlay-hover" aria-selected="false" role="tab"><div class="flex flex-nowrap text-sm lg:text-base gap-1 lg:gap-2 items-center"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-foreground-light shrink-0"><path d="M1.857 11.36a7 7 0 0 1 9.41-9.551M4.774 14.212a7 7 0 0 0 9.41-9.497m-8.812 7.845a2 2 0 1 1-4 0 2 2 0 0 1 4 0Zm9.296-9.13a2 2 0 1 1-4 0 2 2 0 0 1 4 0ZM12.5 8a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0Z" stroke="currentColor" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path></svg><span class="whitespace-nowrap tracking-tight lg:tracking-normal">Edge Functions</span></div></button><button class="rounded-full px-4 md:px-3 py-2 md:py-1 nowrap flex group gap-1 transition-all text-foreground-lighter bg-surface-200 hover:bg-overlay-hover" aria-selected="false" role="tab"><div class="flex flex-nowrap text-sm lg:text-base gap-1 lg:gap-2 items-center"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-foreground-light shrink-0"><path d="M5.362.984v2.35m-1.866.144L1.365 1.282m2.052 3.92H1.052m8.023 9.653L4.557 4.523 15 9.115l-4.748 1.182-1.177 4.558Z" stroke="currentColor" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path></svg><span class="whitespace-nowrap tracking-tight lg:tracking-normal">Realtime</span></div></button><button class="rounded-full px-4 md:px-3 py-2 md:py-1 nowrap flex group gap-1 transition-all text-foreground-lighter bg-surface-200 hover:bg-overlay-hover" aria-selected="false" role="tab"><div class="flex flex-nowrap text-sm lg:text-base gap-1 lg:gap-2 items-center"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-foreground-light shrink-0"><path d="M7.99886 7.63216V14.4892M7.99886 7.63216L14.0488 4.11804M7.99886 7.63216L1.94922 4.11819M1.94922 4.11819V8.32332M1.94922 4.11819V4.08217L5.57319 1.97717M14.049 8.36007V4.08217L10.4251 1.97717M11.8165 12.4072L7.99913 14.6245L4.18177 12.4072" stroke="currentColor" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path></svg><span class="whitespace-nowrap tracking-tight lg:tracking-normal">Vector</span></div></button><button class="rounded-full px-4 md:px-3 py-2 md:py-1 nowrap flex group gap-1 transition-all text-foreground-lighter bg-surface-200 hover:bg-overlay-hover" aria-selected="false" role="tab"><div class="flex flex-nowrap text-sm lg:text-base gap-1 lg:gap-2 items-center"><span class="whitespace-nowrap tracking-tight lg:tracking-normal">Other</span></div></button></div></div></div><div class="relative w-full h-fit grid md:grid-cols-2 lg:grid-cols-3 gap-4"><div class="col-span-full flex justify-center items-center min-h-[300px]"><div class="w-full h-full flex flex-col items-center justify-center"><div><svg width="60" height="62" viewBox="0 0 60 62" fill="none" xmlns="http://www.w3.org/2000/svg" class="loading-anim_loading__rDN7u"><path d="M30.2571 4.12811L30.257 4.12389C30.2133 1.21067 26.5349 -0.034778 24.7224 2.24311L1.76109 31.0996C-1.21104 34.8348 1.45637 40.34 6.23131 40.34H29.4845L29.7563 58.4432C29.8 61.3564 33.4783 62.6016 35.2908 60.324L34.8996 60.0127L35.2908 60.324L58.2521 31.4674C61.2241 27.7322 58.5568 22.227 53.782 22.227H30.3762L30.2571 4.12811Z" stroke="hsl(var(--brand-default))" stroke-width="2" stroke-linecap="round"></path></svg></div></div></div></div></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0"><div class="w-full border-b"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20"><div class="flex flex-col gap-8 xl:gap-10"><div class="w-full gap-2 flex flex-col items-center text-center"><h2 class="text-3xl xl:text-4xl max-w-[280px] sm:max-w-xs xl:max-w-[360px] tracking-[-1px]">Sponsored Projects</h2><p class="text-foreground-lighter mb-4 max-w-sm">We don&#x27;t just live and breath open-source, we also sponsor projects we love.</p></div><div class="relative w-full h-fit grid md:grid-cols-2 lg:grid-cols-3 gap-4"><a target="_blank" href="https://github.com/PostgREST/postgrest"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger"><div class="z-10 w-full h-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light relative group flex flex-col gap-2 p-4 md:min-h-[170px] md:h-[200px]"><div class="flex gap-1 items-center"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="fill-foreground-lighter grouopp-hover:fill-foreground"><path fill-rule="evenodd" clip-rule="evenodd" d="M12 3.33215C7.09969 3.33215 3.12744 7.31061 3.12744 12.2198C3.12744 16.1459 5.66943 19.4775 9.19538 20.6523C9.63901 20.7339 9.80049 20.4597 9.80049 20.2237C9.80049 20.0135 9.7934 19.4536 9.78896 18.7127C7.32061 19.2495 6.79979 17.5211 6.79979 17.5211C6.39698 16.4937 5.81494 16.2204 5.81494 16.2204C5.00931 15.6703 5.87616 15.681 5.87616 15.681C6.76608 15.7431 7.23455 16.5966 7.23455 16.5966C8.02598 17.9541 9.31161 17.562 9.81646 17.3348C9.89809 16.7608 10.127 16.3695 10.3808 16.1477C8.41105 15.9232 6.33931 15.1602 6.33931 11.7549C6.33931 10.7851 6.68534 9.99101 7.25229 9.36993C7.16091 9.14545 6.85658 8.24134 7.33925 7.0187C7.33925 7.0187 8.08454 6.77914 9.7792 7.92903C10.503 7.73162 11.2498 7.63108 12 7.63002C12.7542 7.63357 13.5128 7.73206 14.2217 7.92903C15.9155 6.77914 16.659 7.01781 16.659 7.01781C17.1434 8.24134 16.8382 9.14545 16.7477 9.36993C17.3155 9.99101 17.6598 10.7851 17.6598 11.7549C17.6598 15.169 15.5845 15.9205 13.6086 16.1406C13.9271 16.4147 14.2102 16.9569 14.2102 17.7864C14.2102 18.9736 14.1995 19.9327 14.1995 20.2237C14.1995 20.4615 14.3592 20.7383 14.8099 20.6514C16.5767 20.0588 18.1126 18.9259 19.2005 17.4129C20.2884 15.8999 20.8733 14.0833 20.8726 12.2198C20.8726 7.31061 16.8994 3.33215 12 3.33215Z" fill="currentColor"></path></svg><p class="text-foreground group-hover:text-brand text-lg m-0 leading-none">postgrest</p></div><p class="text-sm flex-1 text-foreground-lighter">PostgREST is a standalone web server that turns your PostgreSQL database directly into a RESTful API.</p><div class="text-sm w-full flex justify-between text-foreground-lighter mt-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right w-4 stroke-[1.5px]"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a target="_blank" href="https://github.com/pgroonga/pgroonga"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger"><div class="z-10 w-full h-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light relative group flex flex-col gap-2 p-4 md:min-h-[170px] md:h-[200px]"><div class="flex gap-1 items-center"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="fill-foreground-lighter grouopp-hover:fill-foreground"><path fill-rule="evenodd" clip-rule="evenodd" d="M12 3.33215C7.09969 3.33215 3.12744 7.31061 3.12744 12.2198C3.12744 16.1459 5.66943 19.4775 9.19538 20.6523C9.63901 20.7339 9.80049 20.4597 9.80049 20.2237C9.80049 20.0135 9.7934 19.4536 9.78896 18.7127C7.32061 19.2495 6.79979 17.5211 6.79979 17.5211C6.39698 16.4937 5.81494 16.2204 5.81494 16.2204C5.00931 15.6703 5.87616 15.681 5.87616 15.681C6.76608 15.7431 7.23455 16.5966 7.23455 16.5966C8.02598 17.9541 9.31161 17.562 9.81646 17.3348C9.89809 16.7608 10.127 16.3695 10.3808 16.1477C8.41105 15.9232 6.33931 15.1602 6.33931 11.7549C6.33931 10.7851 6.68534 9.99101 7.25229 9.36993C7.16091 9.14545 6.85658 8.24134 7.33925 7.0187C7.33925 7.0187 8.08454 6.77914 9.7792 7.92903C10.503 7.73162 11.2498 7.63108 12 7.63002C12.7542 7.63357 13.5128 7.73206 14.2217 7.92903C15.9155 6.77914 16.659 7.01781 16.659 7.01781C17.1434 8.24134 16.8382 9.14545 16.7477 9.36993C17.3155 9.99101 17.6598 10.7851 17.6598 11.7549C17.6598 15.169 15.5845 15.9205 13.6086 16.1406C13.9271 16.4147 14.2102 16.9569 14.2102 17.7864C14.2102 18.9736 14.1995 19.9327 14.1995 20.2237C14.1995 20.4615 14.3592 20.7383 14.8099 20.6514C16.5767 20.0588 18.1126 18.9259 19.2005 17.4129C20.2884 15.8999 20.8733 14.0833 20.8726 12.2198C20.8726 7.31061 16.8994 3.33215 12 3.33215Z" fill="currentColor"></path></svg><p class="text-foreground group-hover:text-brand text-lg m-0 leading-none">pgroonga</p></div><p class="text-sm flex-1 text-foreground-lighter">PGroonga is a PostgreSQL extension to use Groonga as index. PGroonga makes PostgreSQL fast full text search platform for all languages!</p><div class="text-sm w-full flex justify-between text-foreground-lighter mt-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right w-4 stroke-[1.5px]"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a target="_blank" href="https://github.com/michelp/pgsodium"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger"><div class="z-10 w-full h-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light relative group flex flex-col gap-2 p-4 md:min-h-[170px] md:h-[200px]"><div class="flex gap-1 items-center"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="fill-foreground-lighter grouopp-hover:fill-foreground"><path fill-rule="evenodd" clip-rule="evenodd" d="M12 3.33215C7.09969 3.33215 3.12744 7.31061 3.12744 12.2198C3.12744 16.1459 5.66943 19.4775 9.19538 20.6523C9.63901 20.7339 9.80049 20.4597 9.80049 20.2237C9.80049 20.0135 9.7934 19.4536 9.78896 18.7127C7.32061 19.2495 6.79979 17.5211 6.79979 17.5211C6.39698 16.4937 5.81494 16.2204 5.81494 16.2204C5.00931 15.6703 5.87616 15.681 5.87616 15.681C6.76608 15.7431 7.23455 16.5966 7.23455 16.5966C8.02598 17.9541 9.31161 17.562 9.81646 17.3348C9.89809 16.7608 10.127 16.3695 10.3808 16.1477C8.41105 15.9232 6.33931 15.1602 6.33931 11.7549C6.33931 10.7851 6.68534 9.99101 7.25229 9.36993C7.16091 9.14545 6.85658 8.24134 7.33925 7.0187C7.33925 7.0187 8.08454 6.77914 9.7792 7.92903C10.503 7.73162 11.2498 7.63108 12 7.63002C12.7542 7.63357 13.5128 7.73206 14.2217 7.92903C15.9155 6.77914 16.659 7.01781 16.659 7.01781C17.1434 8.24134 16.8382 9.14545 16.7477 9.36993C17.3155 9.99101 17.6598 10.7851 17.6598 11.7549C17.6598 15.169 15.5845 15.9205 13.6086 16.1406C13.9271 16.4147 14.2102 16.9569 14.2102 17.7864C14.2102 18.9736 14.1995 19.9327 14.1995 20.2237C14.1995 20.4615 14.3592 20.7383 14.8099 20.6514C16.5767 20.0588 18.1126 18.9259 19.2005 17.4129C20.2884 15.8999 20.8733 14.0833 20.8726 12.2198C20.8726 7.31061 16.8994 3.33215 12 3.33215Z" fill="currentColor"></path></svg><p class="text-foreground group-hover:text-brand text-lg m-0 leading-none">pgsodium</p></div><p class="text-sm flex-1 text-foreground-lighter">Modern cryptography for PostgreSQL using libsodium.</p><div class="text-sm w-full flex justify-between text-foreground-lighter mt-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right w-4 stroke-[1.5px]"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a target="_blank" href="https://opencollective.com/supabase"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger"><div class="z-10 w-full h-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light relative group flex flex-col gap-2 p-4 min-h-[120px] md:h-[140px]"><div class="flex gap-1 items-center"><p class="text-foreground group-hover:text-brand text-lg m-0 leading-none">Open Collective Profile</p></div><p class="text-sm flex-1 text-foreground-lighter">We have contributed with more than $250,000 on paying sponsorships.</p><div class="text-sm w-full flex justify-between text-foreground-lighter mt-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right w-4 stroke-[1.5px]"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a target="_blank" href="https://www.socallinuxexpo.org/sites/default/files/presentations/solving-postgres-wicked-problems.pdf"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger"><div class="z-10 w-full h-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light relative group flex flex-col gap-2 p-4 min-h-[120px] md:h-[140px]"><div class="flex gap-1 items-center"><p class="text-foreground group-hover:text-brand text-lg m-0 leading-none">OrioleDB</p></div><p class="text-sm flex-1 text-foreground-lighter">Sponsoring OrioleDB – the next generation storage engine for PostgreSQL</p><div class="text-sm w-full flex justify-between text-foreground-lighter mt-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right w-4 stroke-[1.5px]"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a><a target="_blank" href="https://elixir-lang.org/blog/2022/10/05/my-future-with-elixir-set-theoretic-types/#:~:text=is%20sponsored%20by-,Supabase,-(they%20are"><div class="group/panel relative rounded-lg md:rounded-xl p-px bg-surface-75 bg-gradient-to-b from-border to-border/50 dark:to-surface-100 transition-all hover:shadow-md flex items-center justify-center hover:bg-none hover:!bg-border-stronger"><div class="z-10 w-full h-full rounded-[7px] md:rounded-[11px] bg-surface-75 overflow-hidden text-foreground-light relative group flex flex-col gap-2 p-4 min-h-[120px] md:h-[140px]"><div class="flex gap-1 items-center"><p class="text-foreground group-hover:text-brand text-lg m-0 leading-none">Elixir</p></div><p class="text-sm flex-1 text-foreground-lighter">Elixir is a dynamic, functional language for building scalable and maintainable applications.</p><div class="text-sm w-full flex justify-between text-foreground-lighter mt-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-right w-4 stroke-[1.5px]"><path d="M7 7h10v10"></path><path d="M7 17 17 7"></path></svg></div><div class="absolute z-10 inset-0 w-full h-full pointer-events-none opacity-20"></div></div></div></a></div></div></div><div class="bg-background grid grid-cols-12 items-center gap-4 border-t py-32 text-center px-16"><div class="col-span-12"><h2 class="h2"><span class="text-foreground-lighter">Build in a weekend,</span><span class="text-foreground block sm:inline"> scale to millions</span></h2></div><div class="flex items-center justify-center gap-2 col-span-12 mt-4"><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-sm px-4 py-2 h-[38px]" href="dashboard/org.html"><span class="truncate">Start your project</span></a><a data-size="medium" type="button" class="relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-sm px-4 py-2 h-[38px]" href="contact/sales.html"><span class="truncate">Request a demo</span></a></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="_next/supabase-logo-wordmark--light.png 1x, _next/supabase-logo-wordmark--light.png 2x" src="_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="_next/supabase-logo-wordmark--dark.png 1x, _next/supabase-logo-wordmark--dark.png 2x" src="_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="partners/integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{}},"page":"/open-source","query":{},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","nextExport":true,"autoExport":true,"isFallback":false,"scriptLoader":[]}</script></body></html>