<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../../rss.xml" data-next-head=""/><link rel="manifest" href="../../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:type" content="website" data-next-head=""/><meta property="og:image:alt" content="Supabase Og Image" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Draftbit | Works With Supabase</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Draftbit is back-end agnostic and connects to Supabase via REST API." data-next-head=""/><meta property="og:title" content="Draftbit | Works With Supabase" data-next-head=""/><meta property="og:description" content="Draftbit is back-end agnostic and connects to Supabase via REST API." data-next-head=""/><meta property="og:url" content="https://supabase.com/partners/integrations/draftbit" data-next-head=""/><meta property="og:image" content="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/draftbit-1.png" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2263-07e8ebb96cc2b014.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8658-639e1bb66549b52b.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/partners/integrations/%5Bslug%5D-cff8a94339d02c6e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqela6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R1eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R1eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R2eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R2eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R3eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R3eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav></div><main class="relative min-h-screen"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20"><div class="col-span-12 mx-auto mb-2 max-w-5xl space-y-10 lg:col-span-2"><a class="text-foreground hover:text-foreground-lighter flex cursor-pointer items-center transition-colors" href="../integrations.html"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left"><path d="m15 18-6-6 6-6"></path></svg>Back</a><div class="flex items-center space-x-4"><img alt="Draftbit" loading="lazy" width="56" height="56" decoding="async" data-nimg="1" class="bg-surface-200 flex-shrink-f0 h-14 w-14 rounded-full" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdraftbit%2Fdraftbit-logo.png&amp;w=64&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdraftbit%2Fdraftbit-logo.png&amp;w=128&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdraftbit%2Fdraftbit-logo.png&amp;w=128&amp;q=75"/><h1 class="h1" style="margin-bottom:0">Draftbit</h1></div><div class="bg-gradient-to-t from-background-alternative to-background border-b p-6 [&amp;_.swiper-container]:overflow-visible" style="margin-left:calc(50% - 50vw);margin-right:calc(50% - 50vw)"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0 !px-3 lg:!px-12 xl:!p-0 mx-auto max-w-5xl"><div class="swiper"><div class="swiper-wrapper"><div class="swiper-slide"><div class="relative block overflow-hidden rounded-md"><img alt="Draftbit" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto;background-size:cover;background-position:50% 50%;background-repeat:no-repeat;background-image:url(&quot;data:image/svg+xml;charset=utf-8,%3Csvg xmlns=&#x27;http://www.w3.org/2000/svg&#x27; viewBox=&#x27;0 0 1460 960&#x27;%3E%3Cfilter id=&#x27;b&#x27; color-interpolation-filters=&#x27;sRGB&#x27;%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3CfeColorMatrix values=&#x27;1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1&#x27; result=&#x27;s&#x27;/%3E%3CfeFlood x=&#x27;0&#x27; y=&#x27;0&#x27; width=&#x27;100%25&#x27; height=&#x27;100%25&#x27;/%3E%3CfeComposite operator=&#x27;out&#x27; in=&#x27;s&#x27;/%3E%3CfeComposite in2=&#x27;SourceGraphic&#x27;/%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3C/filter%3E%3Cimage width=&#x27;100%25&#x27; height=&#x27;100%25&#x27; x=&#x27;0&#x27; y=&#x27;0&#x27; preserveAspectRatio=&#x27;none&#x27; style=&#x27;filter: url(%23b);&#x27; href=&#x27;/images/blur.png&#x27;/%3E%3C/svg%3E&quot;)" sizes="100vw" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdraftbit%2Fdraftbit-1.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdraftbit%2Fdraftbit-1.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdraftbit%2Fdraftbit-1.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdraftbit%2Fdraftbit-1.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdraftbit%2Fdraftbit-1.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdraftbit%2Fdraftbit-1.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdraftbit%2Fdraftbit-1.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdraftbit%2Fdraftbit-1.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdraftbit%2Fdraftbit-1.png&amp;w=3840&amp;q=75"/></div></div><div class="swiper-slide"><div class="relative block overflow-hidden rounded-md"><img alt="Draftbit" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto;background-size:cover;background-position:50% 50%;background-repeat:no-repeat;background-image:url(&quot;data:image/svg+xml;charset=utf-8,%3Csvg xmlns=&#x27;http://www.w3.org/2000/svg&#x27; viewBox=&#x27;0 0 1460 960&#x27;%3E%3Cfilter id=&#x27;b&#x27; color-interpolation-filters=&#x27;sRGB&#x27;%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3CfeColorMatrix values=&#x27;1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1&#x27; result=&#x27;s&#x27;/%3E%3CfeFlood x=&#x27;0&#x27; y=&#x27;0&#x27; width=&#x27;100%25&#x27; height=&#x27;100%25&#x27;/%3E%3CfeComposite operator=&#x27;out&#x27; in=&#x27;s&#x27;/%3E%3CfeComposite in2=&#x27;SourceGraphic&#x27;/%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3C/filter%3E%3Cimage width=&#x27;100%25&#x27; height=&#x27;100%25&#x27; x=&#x27;0&#x27; y=&#x27;0&#x27; preserveAspectRatio=&#x27;none&#x27; style=&#x27;filter: url(%23b);&#x27; href=&#x27;/images/blur.png&#x27;/%3E%3C/svg%3E&quot;)" sizes="100vw" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdraftbit%2Fdraftbit-2.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdraftbit%2Fdraftbit-2.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdraftbit%2Fdraftbit-2.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdraftbit%2Fdraftbit-2.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdraftbit%2Fdraftbit-2.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdraftbit%2Fdraftbit-2.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdraftbit%2Fdraftbit-2.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdraftbit%2Fdraftbit-2.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdraftbit%2Fdraftbit-2.png&amp;w=3840&amp;q=75"/></div></div></div></div></div></div><div class="grid gap-y-12 lg:grid-cols-8 lg:space-x-12"><div class="lg:col-span-5 overflow-hidden"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Overview</h2><div class="prose"><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p>Draftbit is a &quot;pro-code&quot; low-code mobile app building platform. Draftbit exports React Native source code that is 100% run on open-source languages and libraries.</p>
<h2>Documentation</h2>
<p>This guide explains how to connect a Supabase back-end to a Draftbit front-end and then configure all CRUD operations necessary to build a simple mobile app.</p>
<p><a href="https://draftb.it/3Fkbask">Draftbit</a> is a &quot;pro-code&quot; low-code mobile app building platform. Draftbit exports React Native source code that is 100% run on open-source languages and libraries.</p>
<p>Draftbit is back-end agnostic and connects to Supabase via REST API.</p>
<blockquote>
<p>Note: For the demonstration purpose of this guide, we are using a pre-populated database in Supabase. We are calling <code>Groceries</code>. To follow along, rename it any way you prefer.</p>
</blockquote>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/prepopulated-database.png" alt="Prepopulated Database"/></p>
<p>If you don’t have a Draftbit account, create one <a href="https://draftb.it/3Fkbask">here</a>. Once you’ve got your account set up, Create a New App. You can select <code>Start From a Blank App</code> for this demo and proceed to the Builder interface.</p>
<h2>Step 1: Get the RESTful endpoint and Project API key</h2>
<p>To connect the REST API in the Draftbit app, the following fields are required:</p>
<ul>
<li>Base URL of the REST API, which is in the format: <code>https://&lt;your-domain&gt;.supabase.co/rest/v1</code> where the <code>&lt;your-domain</code> is a unique domain name generated by Supabase.</li>
<li>The <code>supabase-key</code> is the secret key.</li>
</ul>
<p>You can find these unique values in the API settings of your Supabase account.</p>
<ul>
<li>Click the Settings button from the top menu bar.</li>
<li>In Settings, select <strong>API</strong>.</li>
<li>In the <strong>Project URL</strong> section, select and copy the URL. It is the Base URL of your Supabase REST API. It will be required to make a connection to the Draftbit app.</li>
<li>Also, under <code>Project API keys</code>, select and copy the API key under <code>anon</code>. It is required for every request made to the Supabase database.</li>
</ul>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/endpoint.png" alt="Get Supabase connection string"/></p>
<h2>Step 2: Save Supabase API key as Authorization Header in Draftbit</h2>
<p>To authorize your Draftbit app with Supabse, in the builder interface:</p>
<ul>
<li>Open the <strong>Settings</strong> tab from the top menu bar.</li>
<li>In Project Settings, navigate to App Variables.</li>
<li>Enter a name to access the API Key such as <code>Authorization_Header</code>. When making the service connection in the next section, it will be passed as the value for the header <code>Authorization</code>.</li>
<li>The value of this key requires you to enter an authorization token that starts with syntax <code>Bearer &lt;your-api-key&gt;</code> (the space between <code>Bearer</code> and <code>&lt;your-api-key&gt;</code> is required). Click <strong>Add</strong> after adding the value.</li>
<li>Enter another key name to access the API Key such as <code>Api_Key_Header</code>. When making the service connection in the next section, it will be passed as the header <code>apiKey</code> value.</li>
<li>The value of this key requires you to enter an authorization token that starts with syntax is <code>&lt;your-api-key&gt;</code>. Click <strong>Add</strong> after adding the value.</li>
<li>Click <strong>Save</strong> to save these keys and close the modal.</li>
</ul>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/authheader.png" alt="Add header values in Draftbit"/></p>
<h2>Step 3: Add Supabase RESTful endpoint in Draftbit</h2>
<p>In your Draftbit builder interface:</p>
<ul>
<li>
<p>Open the <strong>API &amp; Cloud Services</strong> modal from the top menu bar.</p>
</li>
<li>
<p>From the <strong>Connect a service</strong> menu, click on <strong>Rest API</strong>.</p>
</li>
<li>
<p>In Step 1: Enter a name for your REST API. Then, paste your <code>Base URL</code> (from the first section) into the Base URL field.</p>
</li>
<li>
<p>In Step 2: Under <strong>Key</strong> add <code>Authorization</code> and <code>apikey</code>. Then, under <strong>Value</strong>, select the global variables (from the previous section) to add the actual values for both keys.</p>
</li>
<li>
<p>Click Save.</p>
</li>
</ul>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/service.png" alt="Create a API service in Draftbit"/></p>
<h2>Making API requests with Supabase &amp; Draftbit</h2>
<h3>GET request to Fetch all records</h3>
<p>In this section, let&#x27;s populate a Fetch component with all the data from a simple Supabase and then display the data fetched from the Supabase data table in a List component.</p>
<p>For reference, here is a how the Components tree looks like for this screen:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/ctree.png" alt="Components tree"/></p>
<p>The next step is to create an endpoint. Let&#x27;s try fetching all the data using a <code>GET</code> HTTP request. Select the Supabase service in the <strong>API &amp; Cloud Services</strong> modal, and then:</p>
<ul>
<li>Click <strong>Add endpoint</strong>.</li>
<li>In Step 1: enter the name for the endpoint. Make sure the <strong>Method</strong> select is <code>GET</code>.</li>
<li>In Step 2: add the base name path: <code>/groceries/select=*</code>, where <code>groceries</code> is the table name in Supabase.</li>
<li>In Step 4: click the <strong>Test</strong> button next to the Endpoint input to verify the response coming from the Supabase.</li>
<li>Click Save.</li>
</ul>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/get-request.gif" alt="Creating a GET request endpoint"/></p>
<p>In the Builder, on the app screen:</p>
<ul>
<li>Select the Fetch component in the Components tree and go to the <a href="doc:introduction-to-the-builder#properties-panel">Data tab from Properties Panel</a>.</li>
<li>For <strong>Service</strong>, select the name of the Supabase Service.</li>
<li>For <strong>Endpoint</strong>, select the endpoint you want to fetch the data from.</li>
<li>Select the List component in the Components and go to the <a href="doc:introduction-to-the-builder#properties-panel">Data tab from Properties Panel</a>. In Data, select <code>Top-Level Response</code> from the dropdown menu.</li>
<li>Then, select the Text component in the Components and then go to the Data tab from the Properties Panel.</li>
<li>Add a <code>{{varName}}</code> value (inside the curly braces) to represent a column field from the Supabase. For example, add <code>{{title}}</code> to represent the column name from the Supabase Base.</li>
<li>Under <strong>Variables</strong>, you will see the variable name defined in the previous step. From the dropdown menu, select the appropriate field that represents the data field.</li>
</ul>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/fetchall.gif" alt="Fetching data on app screen"/></p>
<h3>GET request to fetch single row</h3>
<p>Open the <strong>API &amp; Cloud services</strong> modal from the top menu, select the Supabase service, and then:</p>
<ul>
<li>Click <strong>Add endpoint</strong>.</li>
<li>In Step 1: enter a name for the endpoint.</li>
<li>In Step 2: add the <code>/groceries/column-name=eq.{{column-name}}</code> variable. Then, add a Test value for the <code>{{column-name}}</code>. For example, it can be the <code>title</code> or the <code>id</code>.</li>
<li>In Step 4: click the <strong>Test</strong> button next to the Endpoint input to verify the response coming from the Supabase.</li>
<li>Click Save.</li>
</ul>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/getsingle.gif" alt="Creating endpoint to fetch a single row"/></p>
<p>On app screen:</p>
<ul>
<li>Select the Fetch component in the Components tree and go to the Data tab from Properties Panel</li>
<li>For <strong>Service</strong>, select the name of the Supabase Service.</li>
<li>For <strong>endpoint</strong>, select the endpoint you want to fetch the data from.</li>
<li>Set the value for the <code>id</code> in the Configuration &gt; URL Structure section to Navigation &gt; id.</li>
<li>Select the List component in the Components and go to the Data tab from Properties Panel. In Data, select <code>Top-Level Response</code> from the dropdown menu.</li>
<li>Then, select the Text component in the Components and then go to the Data tab from the Properties Panel.</li>
<li>Add a <code>{{varName}}</code> value (inside the curly braces) to represent the column field from the Supabase. For example, add <code>{{title}}</code> to represent the field and value from the Supabase data table.</li>
<li>Under <strong>Variables</strong>, you will see the variable name defined in the previous step. From the dropdown menu, select the appropriate field that represents the data field.</li>
</ul>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/fetchsingle.png" alt="Displaying data from single row"/></p>
<h3>POST request to submit a new row</h3>
<p>Submitting new Data from the Draftbit app to Supabase&#x27;s REST API requires the request to be sent using the HTTP <code>POST</code> method.</p>
<p>For this section, you need to use at least one component that accepts user input and has a Field Name prop to POST data using Supabase REST API.</p>
<p>You can use one of the following components in Draftbit:</p>
<ul>
<li>Text Input</li>
<li>Text Area/Text Field</li>
<li>Checkbox</li>
<li>Slider</li>
<li>Radio Button Group</li>
<li>Radio Button</li>
</ul>
<p>In addition, you need a Touchable component like a Button to attach the POST action. After you have created these components, we will create the <code>POST</code> endpoint:</p>
<ul>
<li>Click <strong>Add endpoint</strong>.</li>
<li>In Step 1: enter a name for the endpoint and select the Method to <code>POST</code>.</li>
<li>In Step 2: enter the base name as path: <code>/groceries</code>.</li>
<li>In Step 3: add a valid Body structure to submit a POST request. Add one or many <code>{{variable}}</code> for test values. Click Body Preview to validate the structure of the Body in the request. For the example, let&#x27;s create a variable called <code>{{inputValue}}</code>.</li>
<li>In Step 4: to see the new row added to the Supabase data table as JSON response inside the Builder, you have to pass a new header called <code>Prefer</code> with its value as <code>return=representation</code>.</li>
<li>In Step 5: click the <strong>Test</strong> button next to the Endpoint input to verify the response coming from the Supabase and click Save.</li>
</ul>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/postrequest.gif" alt="Make a POST request to add new data to Supabase database"/></p>
<p>Once you follow the above steps, you should get a 200 OK response with exactly the new record as a JSON you have entered for your schema.</p>
<p>An example of how Body in a request will look like:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>{</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  &quot;title&quot;: {{inputValue}}</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<p>Where <code>title</code> is the column name in your Supabase database table.</p>
<p>In Draftbit, using a Touchable or a Button component, you can trigger the action <strong>API Request</strong> to submit the data to the endpoint.</p>
<p>Now, there is a working <code>POST</code> request in Draftbit. Map its response to the components on your screen in Draftbit.</p>
<p>First, for each input component, make sure you have set the Field Names (found in the Configs tab, second from the left) to unique values. For example, in the screen below, there is one TextInput field component with the value of the <code>Field Name</code> prop of <code>textInputValue</code>.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/textinput.png" alt="Field Name prop on a TextInput component"/></p>
<p>Next, on your Button component, go to the Interactions tab in the Properties panel located on the far-right-hand side. Select an Action called <code>API request</code>.</p>
<p>In the API request action:</p>
<ul>
<li>In <strong>Service</strong>, select the name to Supabase API Service.</li>
<li>In <strong>Endpoint</strong>, select the name of the Endpoint.</li>
<li>Then add the configuration for the body request to be sent by selecting the values for <code>{{inputValue}}</code>.</li>
</ul>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/postapirequest.png" alt="Setting up the API Request to send a POST request"/></p>
<p>After completing the above steps, you can trigger the API request to submit new data to the Supabase database.</p>
<h3>PATCH request to Update a new record</h3>
<p>Updating an existing record from the Draftbit app to Supabase&#x27;s REST API requires the request to be sent using the HTTP <code>PATCH</code> method.</p>
<p>After you have created your screen components in the Draftbit builder, open the Supabase service and make the <code>PATCH</code> endpoint:</p>
<ul>
<li>Click <strong>Add endpoint</strong>.</li>
<li>In Step 1: enter a name for the endpoint and select the Method to <code>PATCH</code>.</li>
<li>In Step 2: enter the base name as path: <code>/groceries?id=eq.{{id}}</code>, where <code>id</code> is the value of an existing record in the database.</li>
<li>In Step 3: add a valid Body structure to submit a PATCH request. Add one or many <code>{{variable}}</code> for test values depending on the structure of your app. Click Body Preview to validate the structure of the Body in the request. For the example, let&#x27;s create a variable called <code>{{inputValue}}</code>.</li>
<li>In Step 5: click the <strong>Test</strong> button next to the Endpoint input to verify the response coming from the Supabase and click Save.</li>
</ul>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/patch.gif" alt="Creating an endpoint for PATCH request"/></p>
<p>Next, on your Button component, go to the Interactions tab in the Properties panel located on the far-right-hand side. Select an Action called <code>API request</code>.</p>
<p>In the API request action:</p>
<ul>
<li>In <strong>Service</strong>, select the name to Supabase API Service.</li>
<li>In <strong>Endpoint</strong>, select the name of the Endpoint.</li>
<li>Then add the configuration for the query param, and the body request to be sent by selecting the values for <code>{{inputValue}}</code>.</li>
</ul>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/patchapirequest.png" alt="Setting up the API Request to send a PATCH request"/></p>
<p>After completing the above steps, you can trigger the API request to update existing data in the Supabase database.</p>
<h3>DELETE request to remove an existing record</h3>
<p>The <code>DELETE</code> request is to the Supabase with an item&#x27;s <code>column-name</code> to remove that particular record from the table. You can use a <a href="https://supabase.io/docs/reference/javascript/using-filters">filter from Supabase</a> to filter the value of a specific <code>column-name</code>.</p>
<p>After you have created your screen components in the Draftbit builder, open the Supabase service and create the <code>DELETE</code> endpoint:</p>
<ul>
<li>Click <strong>Add endpoint</strong>.</li>
<li>In Step 1: enter a name for the endpoint and select the Method to <code>DELETE</code>.</li>
<li>In Step 2: add <code>/groceries/columnName=eq.{{columnName}}</code>. Then, add a Test value for the <code>{{columnName}}</code>. For example, the <code>{{columnName}}</code> here can be <code>id</code> of the record.</li>
<li>In Step 4: click the <strong>Test</strong> button next to the Endpoint input to verify the response from the Supabase.</li>
<li>Click Save.</li>
</ul>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/delete.gif" alt="Creating an endpoint for DELETE request"/></p>
<p>Next, on your Button component, go to the Interactions tab in the Properties panel located on the far-right-hand side. Select an Action called <code>API request</code>.</p>
<p>In the API Request action:</p>
<ul>
<li>In <strong>Service</strong>, select the name to Supabase API Service.</li>
<li>In <strong>Endpoint</strong>, select the name of the Endpoint.</li>
<li>Then, add the configuration for the query request to be sent by selecting a value. For example, in this case it will be the <code>id</code> of the record coming from the Navigation parameter.</li>
</ul>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/deleteapirequest.gif" alt="Setting up the API Request to send a DELETE request"/></p>
<h2>Resources</h2>
<ul>
<li><a href="https://draftb.it/3Fkbask">Draftbit</a> official website.</li>
<li><a href="https://community.draftbit.com/home">Draftbit Community</a>.</li>
<li><a href="https://docs.draftbit.com/">Draftbit</a> documentation.</li>
</ul></div></div><div class="lg:col-span-3"><div class="sticky top-20 flex flex-col gap-4"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Details</h2><div class="text-foreground divide-y"><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Developer</span><span class="text-foreground">Draftbit</span></div><div class="flex items-center justify-between py-2"><span class="text-lighter">Category</span><a class="text-brand hover:underline transition-colors" href="../integrations.html#low-code">Low-Code</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Website</span><a href="https://draftbit.com/?utm_source=supabase&amp;utm_medium=referral&amp;utm_campaign=docs-supabase" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors">draftbit.com</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Documentation</span><a href="https://draftbit.com/?utm_source=supabase&amp;utm_medium=referral&amp;utm_campaign=docs-supabase" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors"><span class="flex items-center space-x-1"><span>Learn</span><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></span></a></div></div><p class="text-foreground-light text-sm">Third-party integrations and docs are managed by Supabase partners.</p></div></div></div></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"partner":{"id":2,"slug":"draftbit","type":"technology","category":"Low-Code","developer":"Draftbit","title":"Draftbit","description":"Draftbit is back-end agnostic and connects to Supabase via REST API.","logo":"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/draftbit-logo.png","images":["https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/draftbit-1.png","https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/draftbit-2.png"],"overview":"Draftbit is a \"pro-code\" low-code mobile app building platform. Draftbit exports React Native source code that is 100% run on open-source languages and libraries.\n\n## Documentation\n\nThis guide explains how to connect a Supabase back-end to a Draftbit front-end and then configure all CRUD operations necessary to build a simple mobile app.\n\n[Draftbit](https://draftb.it/3Fkbask) is a \"pro-code\" low-code mobile app building platform. Draftbit exports React Native source code that is 100% run on open-source languages and libraries.\n\nDraftbit is back-end agnostic and connects to Supabase via REST API.\n\n\u003e Note: For the demonstration purpose of this guide, we are using a pre-populated database in Supabase. We are calling `Groceries`. To follow along, rename it any way you prefer.\n\n![Prepopulated Database](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/prepopulated-database.png)\n\nIf you don’t have a Draftbit account, create one [here](https://draftb.it/3Fkbask). Once you’ve got your account set up, Create a New App. You can select `Start From a Blank App` for this demo and proceed to the Builder interface.\n\n## Step 1: Get the RESTful endpoint and Project API key\n\nTo connect the REST API in the Draftbit app, the following fields are required:\n\n- Base URL of the REST API, which is in the format: `https://\u003cyour-domain\u003e.supabase.co/rest/v1` where the `\u003cyour-domain` is a unique domain name generated by Supabase.\n- The `supabase-key` is the secret key.\n\nYou can find these unique values in the API settings of your Supabase account.\n\n- Click the Settings button from the top menu bar.\n- In Settings, select **API**.\n- In the **Project URL** section, select and copy the URL. It is the Base URL of your Supabase REST API. It will be required to make a connection to the Draftbit app.\n- Also, under `Project API keys`, select and copy the API key under `anon`. It is required for every request made to the Supabase database.\n\n![Get Supabase connection string](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/endpoint.png)\n\n## Step 2: Save Supabase API key as Authorization Header in Draftbit\n\nTo authorize your Draftbit app with Supabse, in the builder interface:\n\n- Open the **Settings** tab from the top menu bar.\n- In Project Settings, navigate to App Variables.\n- Enter a name to access the API Key such as `Authorization_Header`. When making the service connection in the next section, it will be passed as the value for the header `Authorization`.\n- The value of this key requires you to enter an authorization token that starts with syntax `Bearer \u003cyour-api-key\u003e` (the space between `Bearer` and `\u003cyour-api-key\u003e` is required). Click **Add** after adding the value.\n- Enter another key name to access the API Key such as `Api_Key_Header`. When making the service connection in the next section, it will be passed as the header `apiKey` value.\n- The value of this key requires you to enter an authorization token that starts with syntax is `\u003cyour-api-key\u003e`. Click **Add** after adding the value.\n- Click **Save** to save these keys and close the modal.\n\n![Add header values in Draftbit](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/authheader.png)\n\n## Step 3: Add Supabase RESTful endpoint in Draftbit\n\nIn your Draftbit builder interface:\n\n- Open the **API \u0026 Cloud Services** modal from the top menu bar.\n- From the **Connect a service** menu, click on **Rest API**.\n\n- In Step 1: Enter a name for your REST API. Then, paste your `Base URL` (from the first section) into the Base URL field.\n- In Step 2: Under **Key** add `Authorization` and `apikey`. Then, under **Value**, select the global variables (from the previous section) to add the actual values for both keys.\n- Click Save.\n\n![Create a API service in Draftbit](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/service.png)\n\n## Making API requests with Supabase \u0026 Draftbit\n\n### GET request to Fetch all records\n\nIn this section, let's populate a Fetch component with all the data from a simple Supabase and then display the data fetched from the Supabase data table in a List component.\n\nFor reference, here is a how the Components tree looks like for this screen:\n\n![Components tree](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/ctree.png)\n\nThe next step is to create an endpoint. Let's try fetching all the data using a `GET` HTTP request. Select the Supabase service in the **API \u0026 Cloud Services** modal, and then:\n\n- Click **Add endpoint**.\n- In Step 1: enter the name for the endpoint. Make sure the **Method** select is `GET`.\n- In Step 2: add the base name path: `/groceries/select=*`, where `groceries` is the table name in Supabase.\n- In Step 4: click the **Test** button next to the Endpoint input to verify the response coming from the Supabase.\n- Click Save.\n\n![Creating a GET request endpoint](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/get-request.gif)\n\nIn the Builder, on the app screen:\n\n- Select the Fetch component in the Components tree and go to the [Data tab from Properties Panel](doc:introduction-to-the-builder#properties-panel).\n- For **Service**, select the name of the Supabase Service.\n- For **Endpoint**, select the endpoint you want to fetch the data from.\n- Select the List component in the Components and go to the [Data tab from Properties Panel](doc:introduction-to-the-builder#properties-panel). In Data, select `Top-Level Response` from the dropdown menu.\n- Then, select the Text component in the Components and then go to the Data tab from the Properties Panel.\n- Add a `{{varName}}` value (inside the curly braces) to represent a column field from the Supabase. For example, add `{{title}}` to represent the column name from the Supabase Base.\n- Under **Variables**, you will see the variable name defined in the previous step. From the dropdown menu, select the appropriate field that represents the data field.\n\n![Fetching data on app screen](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/fetchall.gif)\n\n### GET request to fetch single row\n\nOpen the **API \u0026 Cloud services** modal from the top menu, select the Supabase service, and then:\n\n- Click **Add endpoint**.\n- In Step 1: enter a name for the endpoint.\n- In Step 2: add the `/groceries/column-name=eq.{{column-name}}` variable. Then, add a Test value for the `{{column-name}}`. For example, it can be the `title` or the `id`.\n- In Step 4: click the **Test** button next to the Endpoint input to verify the response coming from the Supabase.\n- Click Save.\n\n![Creating endpoint to fetch a single row](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/getsingle.gif)\n\nOn app screen:\n\n- Select the Fetch component in the Components tree and go to the Data tab from Properties Panel\n- For **Service**, select the name of the Supabase Service.\n- For **endpoint**, select the endpoint you want to fetch the data from.\n- Set the value for the `id` in the Configuration \u003e URL Structure section to Navigation \u003e id.\n- Select the List component in the Components and go to the Data tab from Properties Panel. In Data, select `Top-Level Response` from the dropdown menu.\n- Then, select the Text component in the Components and then go to the Data tab from the Properties Panel.\n- Add a `{{varName}}` value (inside the curly braces) to represent the column field from the Supabase. For example, add `{{title}}` to represent the field and value from the Supabase data table.\n- Under **Variables**, you will see the variable name defined in the previous step. From the dropdown menu, select the appropriate field that represents the data field.\n\n![Displaying data from single row](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/fetchsingle.png)\n\n### POST request to submit a new row\n\nSubmitting new Data from the Draftbit app to Supabase's REST API requires the request to be sent using the HTTP `POST` method.\n\nFor this section, you need to use at least one component that accepts user input and has a Field Name prop to POST data using Supabase REST API.\n\nYou can use one of the following components in Draftbit:\n\n- Text Input\n- Text Area/Text Field\n- Checkbox\n- Slider\n- Radio Button Group\n- Radio Button\n\nIn addition, you need a Touchable component like a Button to attach the POST action. After you have created these components, we will create the `POST` endpoint:\n\n- Click **Add endpoint**.\n- In Step 1: enter a name for the endpoint and select the Method to `POST`.\n- In Step 2: enter the base name as path: `/groceries`.\n- In Step 3: add a valid Body structure to submit a POST request. Add one or many `{{variable}}` for test values. Click Body Preview to validate the structure of the Body in the request. For the example, let's create a variable called `{{inputValue}}`.\n- In Step 4: to see the new row added to the Supabase data table as JSON response inside the Builder, you have to pass a new header called `Prefer` with its value as `return=representation`.\n- In Step 5: click the **Test** button next to the Endpoint input to verify the response coming from the Supabase and click Save.\n\n![Make a POST request to add new data to Supabase database](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/postrequest.gif)\n\nOnce you follow the above steps, you should get a 200 OK response with exactly the new record as a JSON you have entered for your schema.\n\nAn example of how Body in a request will look like:\n\n```json\n{\n  \"title\": {{inputValue}}\n}\n```\n\nWhere `title` is the column name in your Supabase database table.\n\nIn Draftbit, using a Touchable or a Button component, you can trigger the action **API Request** to submit the data to the endpoint.\n\nNow, there is a working `POST` request in Draftbit. Map its response to the components on your screen in Draftbit.\n\nFirst, for each input component, make sure you have set the Field Names (found in the Configs tab, second from the left) to unique values. For example, in the screen below, there is one TextInput field component with the value of the `Field Name` prop of `textInputValue`.\n\n![Field Name prop on a TextInput component](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/textinput.png)\n\nNext, on your Button component, go to the Interactions tab in the Properties panel located on the far-right-hand side. Select an Action called `API request`.\n\nIn the API request action:\n\n- In **Service**, select the name to Supabase API Service.\n- In **Endpoint**, select the name of the Endpoint.\n- Then add the configuration for the body request to be sent by selecting the values for `{{inputValue}}`.\n\n![Setting up the API Request to send a POST request](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/postapirequest.png)\n\nAfter completing the above steps, you can trigger the API request to submit new data to the Supabase database.\n\n### PATCH request to Update a new record\n\nUpdating an existing record from the Draftbit app to Supabase's REST API requires the request to be sent using the HTTP `PATCH` method.\n\nAfter you have created your screen components in the Draftbit builder, open the Supabase service and make the `PATCH` endpoint:\n\n- Click **Add endpoint**.\n- In Step 1: enter a name for the endpoint and select the Method to `PATCH`.\n- In Step 2: enter the base name as path: `/groceries?id=eq.{{id}}`, where `id` is the value of an existing record in the database.\n- In Step 3: add a valid Body structure to submit a PATCH request. Add one or many `{{variable}}` for test values depending on the structure of your app. Click Body Preview to validate the structure of the Body in the request. For the example, let's create a variable called `{{inputValue}}`.\n- In Step 5: click the **Test** button next to the Endpoint input to verify the response coming from the Supabase and click Save.\n\n![Creating an endpoint for PATCH request](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/patch.gif)\n\nNext, on your Button component, go to the Interactions tab in the Properties panel located on the far-right-hand side. Select an Action called `API request`.\n\nIn the API request action:\n\n- In **Service**, select the name to Supabase API Service.\n- In **Endpoint**, select the name of the Endpoint.\n- Then add the configuration for the query param, and the body request to be sent by selecting the values for `{{inputValue}}`.\n\n![Setting up the API Request to send a PATCH request](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/patchapirequest.png)\n\nAfter completing the above steps, you can trigger the API request to update existing data in the Supabase database.\n\n### DELETE request to remove an existing record\n\nThe `DELETE` request is to the Supabase with an item's `column-name` to remove that particular record from the table. You can use a [filter from Supabase](https://supabase.io/docs/reference/javascript/using-filters) to filter the value of a specific `column-name`.\n\nAfter you have created your screen components in the Draftbit builder, open the Supabase service and create the `DELETE` endpoint:\n\n- Click **Add endpoint**.\n- In Step 1: enter a name for the endpoint and select the Method to `DELETE`.\n- In Step 2: add `/groceries/columnName=eq.{{columnName}}`. Then, add a Test value for the `{{columnName}}`. For example, the `{{columnName}}` here can be `id` of the record.\n- In Step 4: click the **Test** button next to the Endpoint input to verify the response from the Supabase.\n- Click Save.\n\n![Creating an endpoint for DELETE request](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/delete.gif)\n\nNext, on your Button component, go to the Interactions tab in the Properties panel located on the far-right-hand side. Select an Action called `API request`.\n\nIn the API Request action:\n\n- In **Service**, select the name to Supabase API Service.\n- In **Endpoint**, select the name of the Endpoint.\n- Then, add the configuration for the query request to be sent by selecting a value. For example, in this case it will be the `id` of the record coming from the Navigation parameter.\n\n![Setting up the API Request to send a DELETE request](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/deleteapirequest.gif)\n\n## Resources\n\n- [Draftbit](https://draftb.it/3Fkbask) official website.\n- [Draftbit Community](https://community.draftbit.com/home).\n- [Draftbit](https://docs.draftbit.com/) documentation.\n","website":"https://draftbit.com/?utm_source=supabase\u0026utm_medium=referral\u0026utm_campaign=docs-supabase","docs":"https://draftbit.com/?utm_source=supabase\u0026utm_medium=referral\u0026utm_campaign=docs-supabase","contact":29,"approved":true,"created_at":"2022-03-03T06:55:52.595852+00:00","tsv":"'/)':2253C '/3fkbask)':79C,2242C '/3fkbask).':171C '/docs/reference/javascript/using-filters)':2037C '/groceries':1351C,1797C '/groceries/column-name':992C '/groceries/columnname':2090C '/groceries/select':742C '/home).':2249C '/rest/v1':238C '/storage/v1/object/public/images/integrations/draftbit/documentation/authheader.png)':522C '/storage/v1/object/public/images/integrations/draftbit/documentation/ctree.png)':682C '/storage/v1/object/public/images/integrations/draftbit/documentation/delete.gif)':2141C '/storage/v1/object/public/images/integrations/draftbit/documentation/deleteapirequest.gif)':2237C '/storage/v1/object/public/images/integrations/draftbit/documentation/endpoint.png)':349C '/storage/v1/object/public/images/integrations/draftbit/documentation/fetchall.gif)':952C '/storage/v1/object/public/images/integrations/draftbit/documentation/fetchsingle.png)':1216C '/storage/v1/object/public/images/integrations/draftbit/documentation/get-request.gif)':780C '/storage/v1/object/public/images/integrations/draftbit/documentation/getsingle.gif)':1049C '/storage/v1/object/public/images/integrations/draftbit/documentation/patch.gif)':1895C '/storage/v1/object/public/images/integrations/draftbit/documentation/patchapirequest.png)':1979C '/storage/v1/object/public/images/integrations/draftbit/documentation/postapirequest.png)':1699C '/storage/v1/object/public/images/integrations/draftbit/documentation/postrequest.gif)':1467C '/storage/v1/object/public/images/integrations/draftbit/documentation/prepopulated-database.png)':157C '/storage/v1/object/public/images/integrations/draftbit/documentation/service.png)':619C '/storage/v1/object/public/images/integrations/draftbit/documentation/textinput.png)':1619C '1':202C,559C,720C,980C,1329C,1775C,2073C '100':36C,100C '2':351C,583C,736C,989C,1344C,1790C,2088C '200':1478C '3':524C,1354C,1815C '4':753C,1020C,1398C,2114C '5':1433C,1866C 'accept':1259C 'access':392C,455C 'account':165C,177C,273C 'action':1311C,1533C,1644C,1652C,1920C,1928C,2166C,2174C 'actual':604C 'ad':447C,502C,1404C 'add':445C,500C,515C,525C,586C,602C,716C,737C,890C,908C,976C,990C,999C,1152C,1170C,1325C,1355C,1365C,1459C,1671C,1771C,1816C,1826C,1947C,2069C,2089C,2094C,2193C 'addit':1298C 'agnost':7B,114C 'along':146C 'also':319C 'anon':331C 'anoth':451C 'api':14B,121C,209C,215C,230C,268C,286C,306C,322C,328C,354C,394C,457C,461C,538C,556C,566C,613C,621C,709C,961C,1235C,1274C,1534C,1646C,1650C,1660C,1690C,1709C,1738C,1922C,1926C,1936C,1970C,1989C,2168C,2172C,2182C,2228C 'apikey':480C,589C 'app':25C,75C,89C,183C,191C,219C,318C,365C,386C,786C,948C,1051C,1230C,1733C,1840C 'appropri':938C,1202C 'area/text':1288C 'attach':1308C 'author':357C,362C,398C,419C,430C,492C,587C 'back':5B,55C,112C 'back-end':4B,54C,111C 'bar':282C,380C,546C 'base':225C,300C,570C,578C,739C,918C,1347C,1793C 'bearer':436C,440C 'blank':190C 'bodi':1358C,1374C,1382C,1499C,1676C,1819C,1842C,1850C,1956C 'brace':897C,1159C 'build':26C,71C,90C 'builder':199C,370C,534C,783C,810C,856C,1415C,1760C,2058C 'button':277C,757C,1024C,1293C,1296C,1306C,1437C,1527C,1623C,1870C,1899C,2118C,2145C 'call':142C,1394C,1423C,1645C,1862C,1921C,2167C 'case':2211C 'checkbox':1290C 'click':274C,444C,499C,505C,553C,609C,715C,754C,771C,975C,1021C,1038C,1324C,1373C,1434C,1452C,1770C,1841C,1867C,1885C,2068C,2115C,2131C 'close':512C 'cloud':539C,710C,962C 'code':20C,23C,33C,84C,87C,97C,2257 'column':901C,913C,995C,1006C,1163C,1513C,2018C,2046C 'column-nam':994C,1005C,2017C,2045C 'columnnam':2092C,2100C,2104C 'come':767C,1034C,1447C,1880C,2220C 'communiti':2246C 'community.draftbit.com':2248C 'community.draftbit.com/home).':2247C 'complet':1701C,1981C 'compon':640C,663C,671C,678C,791C,794C,838C,841C,875C,878C,1056C,1059C,1109C,1112C,1137C,1140C,1257C,1282C,1303C,1317C,1528C,1557C,1567C,1599C,1616C,1624C,1756C,1900C,2054C,2146C 'config':1579C 'configur':65C,1099C,1673C,1949C,2195C 'connect':9B,51C,116C,212C,314C,345C,404C,468C,549C 'copi':294C,326C 'creat':166C,180C,611C,688C,773C,1040C,1315C,1320C,1391C,1753C,1859C,1887C,2051C,2064C,2133C 'crud':67C 'cur':896C,1158C 'data':644C,653C,658C,697C,800C,833C,846C,861C,884C,943C,946C,1065C,1089C,1117C,1123C,1146C,1181C,1207C,1210C,1226C,1270C,1408C,1461C,1539C,1714C,1994C 'databas':137C,154C,342C,1464C,1518C,1718C,1812C,1998C 'defin':927C,1191C 'delet':1999C,2007C,2066C,2085C,2137C,2233C 'demo':194C 'demonstr':125C 'depend':1834C 'display':651C,1209C 'doc':805C,851C 'docs.draftbit.com':2252C 'docs.draftbit.com/)':2251C 'document':45C,2254C 'domain':243C,247C 'draftb.it':78C,170C,2241C 'draftb.it/3fkbask)':77C,2240C 'draftb.it/3fkbask).':169C 'draftbit':1A,2B,15C,28C,59C,76C,92C,109C,164C,218C,317C,360C,364C,519C,530C,533C,616C,625C,1229C,1284C,1521C,1551C,1562C,1732C,1759C,2057C,2239C,2245C,2250C,2258 'dropdown':869C,934C,1131C,1198C 'end':6B,56C,62C,113C 'endpoint':206C,528C,690C,717C,726C,761C,777C,824C,827C,977C,986C,1028C,1041C,1080C,1083C,1323C,1326C,1335C,1441C,1542C,1663C,1669C,1769C,1772C,1781C,1874C,1889C,1939C,1945C,2067C,2070C,2079C,2122C,2135C,2185C,2191C 'enter':388C,428C,450C,490C,560C,721C,981C,1330C,1345C,1491C,1776C,1791C,2074C 'eq':993C,1799C,2091C 'everi':336C 'exact':1482C 'exampl':907C,1009C,1169C,1388C,1496C,1589C,1856C,2102C,2208C 'exist':1728C,1808C,1993C,2004C 'explain':48C 'export':29C,93C 'far':1638C,1914C,2160C 'far-right-hand':1637C,1913C,2159C 'fetch':629C,639C,654C,694C,790C,831C,945C,956C,1043C,1055C,1087C 'field':222C,580C,902C,939C,944C,1164C,1175C,1203C,1208C,1265C,1289C,1574C,1598C,1605C,1610C 'filter':2032C,2039C 'find':262C 'first':574C,1563C 'follow':145C,221C,1281C,1470C 'format':235C 'found':1576C 'front':61C 'front-end':60C 'generat':249C 'get':203C,343C,626C,700C,733C,775C,953C,1476C 'global':595C 'go':797C,843C,881C,1062C,1114C,1143C,1625C,1901C,2147C 'got':175C 'groceri':143C,744C 'group':1294C 'guid':47C,129C 'hand':1640C,1916C,2162C 'header':358C,399C,418C,463C,479C,516C,1422C 'http':701C,1244C,1747C 'id':1017C,1096C,1105C,1798C,1800C,1802C,2108C,2216C 'input':762C,1029C,1261C,1286C,1442C,1566C,1875C,2123C 'inputvalu':1395C,1508C,1686C,1863C,1966C 'insid':894C,1156C,1413C 'interact':1628C,1904C,2150C 'interfac':200C,371C,535C 'introduct':807C,853C 'introduction-to-the-build':806C,852C 'item':2015C 'json':1411C,1488C,1506C 'key':210C,255C,259C,323C,329C,355C,395C,424C,452C,458C,462C,486C,510C,585C,608C 'languag':42C,106C 'least':1255C 'left':1584C 'let':635C,691C,1389C,1857C 'level':865C,1127C 'librari':44C,108C 'like':674C,1304C,1505C 'list':662C,837C,1108C 'locat':1634C,1910C,2156C 'look':673C,1504C 'low':22C,86C,2256 'low-cod':21C,85C,2255 'made':338C 'make':312C,401C,465C,620C,727C,1454C,1568C,1766C 'mani':1368C,1829C 'map':1552C 'menu':281C,379C,545C,552C,870C,935C,968C,1132C,1199C 'method':730C,1246C,1339C,1749C,1785C,2083C 'mobil':24C,74C,88C 'modal':514C,541C,712C,964C 'name':248C,390C,453C,562C,723C,740C,748C,818C,914C,926C,983C,996C,1007C,1074C,1190C,1266C,1332C,1348C,1514C,1575C,1606C,1611C,1657C,1666C,1778C,1794C,1933C,1942C,2019C,2047C,2076C,2179C,2188C 'nativ':31C,95C 'navig':384C,1104C,2223C 'necessari':69C 'need':1251C,1300C 'new':182C,1222C,1225C,1402C,1421C,1460C,1484C,1713C,1724C 'next':407C,471C,684C,758C,1025C,1438C,1620C,1871C,1896C,2119C,2142C 'note':122C 'obuldanrptloktxcffvn.supabase.co':156C,348C,521C,618C,681C,779C,951C,1048C,1215C,1466C,1618C,1698C,1894C,1978C,2140C,2236C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/authheader.png)':520C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/ctree.png)':680C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/delete.gif)':2139C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/deleteapirequest.gif)':2235C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/endpoint.png)':347C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/fetchall.gif)':950C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/fetchsingle.png)':1214C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/get-request.gif)':778C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/getsingle.gif)':1047C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/patch.gif)':1893C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/patchapirequest.png)':1977C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/postapirequest.png)':1697C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/postrequest.gif)':1465C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/prepopulated-database.png)':155C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/service.png)':617C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/textinput.png)':1617C 'offici':2243C 'ok':1479C 'one':167C,1256C,1278C,1366C,1596C,1827C 'open':40C,104C,372C,536C,959C,1761C,2059C 'open-sourc':39C,103C 'oper':68C 'panel':804C,813C,850C,859C,889C,1069C,1121C,1151C,1633C,1909C,2155C 'param':1953C 'paramet':2224C 'particular':2023C 'pass':412C,476C,1419C 'past':568C 'patch':1719C,1748C,1768C,1787C,1824C,1891C,1975C 'path':741C,1350C,1796C 'platform':27C,91C 'popul':136C,637C 'post':1217C,1245C,1269C,1310C,1322C,1341C,1363C,1456C,1548C,1695C 'pre':135C 'pre-popul':134C 'prefer':152C,1424C 'prepopul':153C 'preview':1375C,1843C 'previous':599C,930C,1194C 'pro':19C,83C 'pro-cod':18C,82C 'proceed':196C 'project':208C,289C,321C,382C 'prop':1267C,1607C,1612C 'properti':803C,812C,849C,858C,888C,1068C,1120C,1150C,1632C,1908C,2154C 'properties-panel':811C,857C 'purpos':126C 'queri':1952C,2198C 'radio':1292C,1295C 'react':30C,94C 'record':631C,1485C,1725C,1729C,1809C,2005C,2024C,2111C,2219C 'refer':665C 'remov':2002C,2021C 'renam':147C 'repres':899C,911C,941C,1161C,1173C,1205C 'represent':1430C 'request':337C,622C,627C,702C,776C,954C,1218C,1238C,1364C,1385C,1457C,1502C,1535C,1549C,1647C,1651C,1677C,1691C,1696C,1710C,1720C,1741C,1825C,1853C,1892C,1923C,1927C,1957C,1971C,1976C,1990C,2000C,2008C,2138C,2169C,2173C,2199C,2229C,2234C 'requir':224C,310C,334C,425C,443C,487C,1236C,1739C 'resourc':2238C 'respons':766C,866C,1033C,1128C,1412C,1446C,1480C,1554C,1879C,2127C 'rest':13B,120C,205C,214C,229C,305C,527C,555C,565C,1234C,1273C,1737C 'return':1429C 'right':1639C,1915C,2161C 'row':958C,1046C,1213C,1223C,1403C 'run':37C,101C 'save':352C,506C,508C,610C,772C,1039C,1453C,1886C,2132C 'schema':1494C 'screen':677C,787C,949C,1052C,1560C,1592C,1755C,2053C 'second':1581C 'secret':258C 'section':291C,408C,472C,575C,600C,634C,1102C,1249C 'see':923C,1187C,1400C 'select':186C,285C,292C,324C,593C,703C,731C,788C,816C,825C,835C,862C,872C,936C,969C,1053C,1072C,1081C,1106C,1124C,1134C,1200C,1337C,1642C,1655C,1664C,1682C,1783C,1918C,1931C,1940C,1962C,2081C,2164C,2177C,2186C,2204C 'send':1693C,1973C,2231C 'sent':1241C,1680C,1744C,1960C,2202C 'servic':403C,467C,540C,551C,614C,706C,711C,815C,822C,963C,972C,1071C,1078C,1654C,1661C,1764C,1930C,1937C,2062C,2176C,2183C 'set':178C,269C,276C,284C,374C,383C,1091C,1572C,1687C,1967C,2225C 'side':1641C,1917C,2163C 'simpl':73C,647C 'singl':957C,1045C,1212C 'slider':1291C 'sourc':32C,41C,96C,105C 'space':438C 'specif':2044C 'start':187C,433C,495C 'step':201C,350C,523C,558C,582C,685C,719C,735C,752C,931C,979C,988C,1019C,1195C,1328C,1343C,1353C,1397C,1432C,1473C,1704C,1774C,1789C,1814C,1865C,1984C,2072C,2087C,2113C 'string':346C 'structur':1101C,1359C,1379C,1820C,1837C,1847C 'submit':1220C,1224C,1361C,1537C,1712C,1822C 'supabas':11B,53C,118C,139C,251C,254C,272C,304C,341C,344C,353C,526C,624C,648C,657C,705C,750C,770C,821C,905C,917C,971C,1037C,1077C,1167C,1180C,1232C,1272C,1407C,1450C,1463C,1517C,1659C,1717C,1735C,1763C,1883C,1935C,1997C,2012C,2034C,2061C,2130C,2181C 'supabase-key':253C 'supabase.co':237C 'supabase.co/rest/v1':236C 'supabase.io':2036C 'supabase.io/docs/reference/javascript/using-filters)':2035C 'supabs':367C 'sure':728C,1569C 'syntax':435C,497C 'tab':375C,801C,847C,885C,1066C,1118C,1147C,1580C,1629C,1905C,2151C 'tabl':659C,747C,1182C,1409C,1519C,2027C 'test':756C,1001C,1023C,1371C,1436C,1832C,1869C,2096C,2117C 'text':874C,1136C,1285C,1287C 'textinput':1597C,1615C 'textinputvalu':1609C 'titl':909C,1014C,1171C,1507C,1510C 'token':431C,493C 'top':280C,378C,544C,864C,967C,1126C 'top-level':863C,1125C 'touchabl':1302C,1524C 'tree':672C,679C,795C,1060C 'tri':693C 'trigger':1531C,1707C,1987C 'uniqu':246C,264C,1586C 'updat':1722C,1726C,1992C 'url':226C,290C,296C,301C,571C,579C,1100C 'use':132C,698C,1242C,1253C,1271C,1277C,1522C,1745C,2030C 'user':1260C 'valid':1357C,1377C,1818C,1845C 'valu':265C,415C,421C,449C,481C,483C,504C,517C,592C,605C,893C,1002C,1093C,1155C,1177C,1372C,1427C,1587C,1602C,1684C,1805C,1833C,1964C,2041C,2097C,2206C 'variabl':387C,596C,920C,925C,997C,1184C,1189C,1369C,1393C,1830C,1861C 'varnam':892C,1154C 've':174C 'verifi':764C,1031C,1444C,1877C,2125C 'via':12B,119C 'want':829C,1085C 'way':150C 'websit':2244C 'work':1547C 'your-domain':241C","video":null,"call_to_action_link":null,"featured":false},"overview":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nconst chCodeConfig = {\n  \"staticMediaQuery\": \"not screen, (max-width: 768px)\",\n  \"lineNumbers\": true,\n  \"showCopyButton\": true,\n  \"themeName\": \"supabase\"\n};\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    h2: \"h2\",\n    a: \"a\",\n    blockquote: \"blockquote\",\n    code: \"code\",\n    img: \"img\",\n    ul: \"ul\",\n    li: \"li\",\n    strong: \"strong\",\n    h3: \"h3\"\n  }, _provideComponents(), props.components), {CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Draftbit is a \\\"pro-code\\\" low-code mobile app building platform. Draftbit exports React Native source code that is 100% run on open-source languages and libraries.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Documentation\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This guide explains how to connect a Supabase back-end to a Draftbit front-end and then configure all CRUD operations necessary to build a simple mobile app.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://draftb.it/3Fkbask\",\n        children: \"Draftbit\"\n      }), \" is a \\\"pro-code\\\" low-code mobile app building platform. Draftbit exports React Native source code that is 100% run on open-source languages and libraries.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Draftbit is back-end agnostic and connects to Supabase via REST API.\"\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsxs(_components.p, {\n        children: [\"Note: For the demonstration purpose of this guide, we are using a pre-populated database in Supabase. We are calling \", _jsx(_components.code, {\n          children: \"Groceries\"\n        }), \". To follow along, rename it any way you prefer.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/prepopulated-database.png\",\n        alt: \"Prepopulated Database\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you don’t have a Draftbit account, create one \", _jsx(_components.a, {\n        href: \"https://draftb.it/3Fkbask\",\n        children: \"here\"\n      }), \". Once you’ve got your account set up, Create a New App. You can select \", _jsx(_components.code, {\n        children: \"Start From a Blank App\"\n      }), \" for this demo and proceed to the Builder interface.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 1: Get the RESTful endpoint and Project API key\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To connect the REST API in the Draftbit app, the following fields are required:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Base URL of the REST API, which is in the format: \", _jsx(_components.code, {\n          children: \"https://\u003cyour-domain\u003e.supabase.co/rest/v1\"\n        }), \" where the \", _jsx(_components.code, {\n          children: \"\u003cyour-domain\"\n        }), \" is a unique domain name generated by Supabase.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"The \", _jsx(_components.code, {\n          children: \"supabase-key\"\n        }), \" is the secret key.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You can find these unique values in the API settings of your Supabase account.\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Click the Settings button from the top menu bar.\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In Settings, select \", _jsx(_components.strong, {\n          children: \"API\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In the \", _jsx(_components.strong, {\n          children: \"Project URL\"\n        }), \" section, select and copy the URL. It is the Base URL of your Supabase REST API. It will be required to make a connection to the Draftbit app.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Also, under \", _jsx(_components.code, {\n          children: \"Project API keys\"\n        }), \", select and copy the API key under \", _jsx(_components.code, {\n          children: \"anon\"\n        }), \". It is required for every request made to the Supabase database.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/endpoint.png\",\n        alt: \"Get Supabase connection string\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 2: Save Supabase API key as Authorization Header in Draftbit\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To authorize your Draftbit app with Supabse, in the builder interface:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Open the \", _jsx(_components.strong, {\n          children: \"Settings\"\n        }), \" tab from the top menu bar.\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"In Project Settings, navigate to App Variables.\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Enter a name to access the API Key such as \", _jsx(_components.code, {\n          children: \"Authorization_Header\"\n        }), \". When making the service connection in the next section, it will be passed as the value for the header \", _jsx(_components.code, {\n          children: \"Authorization\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"The value of this key requires you to enter an authorization token that starts with syntax \", _jsx(_components.code, {\n          children: \"Bearer \u003cyour-api-key\u003e\"\n        }), \" (the space between \", _jsx(_components.code, {\n          children: \"Bearer\"\n        }), \" and \", _jsx(_components.code, {\n          children: \"\u003cyour-api-key\u003e\"\n        }), \" is required). Click \", _jsx(_components.strong, {\n          children: \"Add\"\n        }), \" after adding the value.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Enter another key name to access the API Key such as \", _jsx(_components.code, {\n          children: \"Api_Key_Header\"\n        }), \". When making the service connection in the next section, it will be passed as the header \", _jsx(_components.code, {\n          children: \"apiKey\"\n        }), \" value.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"The value of this key requires you to enter an authorization token that starts with syntax is \", _jsx(_components.code, {\n          children: \"\u003cyour-api-key\u003e\"\n        }), \". Click \", _jsx(_components.strong, {\n          children: \"Add\"\n        }), \" after adding the value.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Click \", _jsx(_components.strong, {\n          children: \"Save\"\n        }), \" to save these keys and close the modal.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/authheader.png\",\n        alt: \"Add header values in Draftbit\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 3: Add Supabase RESTful endpoint in Draftbit\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In your Draftbit builder interface:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"\\n\", _jsxs(_components.p, {\n          children: [\"Open the \", _jsx(_components.strong, {\n            children: \"API \u0026 Cloud Services\"\n          }), \" modal from the top menu bar.\"]\n        }), \"\\n\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"\\n\", _jsxs(_components.p, {\n          children: [\"From the \", _jsx(_components.strong, {\n            children: \"Connect a service\"\n          }), \" menu, click on \", _jsx(_components.strong, {\n            children: \"Rest API\"\n          }), \".\"]\n        }), \"\\n\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"\\n\", _jsxs(_components.p, {\n          children: [\"In Step 1: Enter a name for your REST API. Then, paste your \", _jsx(_components.code, {\n            children: \"Base URL\"\n          }), \" (from the first section) into the Base URL field.\"]\n        }), \"\\n\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"\\n\", _jsxs(_components.p, {\n          children: [\"In Step 2: Under \", _jsx(_components.strong, {\n            children: \"Key\"\n          }), \" add \", _jsx(_components.code, {\n            children: \"Authorization\"\n          }), \" and \", _jsx(_components.code, {\n            children: \"apikey\"\n          }), \". Then, under \", _jsx(_components.strong, {\n            children: \"Value\"\n          }), \", select the global variables (from the previous section) to add the actual values for both keys.\"]\n        }), \"\\n\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"\\n\", _jsx(_components.p, {\n          children: \"Click Save.\"\n        }), \"\\n\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/service.png\",\n        alt: \"Create a API service in Draftbit\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Making API requests with Supabase \u0026 Draftbit\"\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"GET request to Fetch all records\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In this section, let's populate a Fetch component with all the data from a simple Supabase and then display the data fetched from the Supabase data table in a List component.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"For reference, here is a how the Components tree looks like for this screen:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/ctree.png\",\n        alt: \"Components tree\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The next step is to create an endpoint. Let's try fetching all the data using a \", _jsx(_components.code, {\n        children: \"GET\"\n      }), \" HTTP request. Select the Supabase service in the \", _jsx(_components.strong, {\n        children: \"API \u0026 Cloud Services\"\n      }), \" modal, and then:\"]\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Click \", _jsx(_components.strong, {\n          children: \"Add endpoint\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In Step 1: enter the name for the endpoint. Make sure the \", _jsx(_components.strong, {\n          children: \"Method\"\n        }), \" select is \", _jsx(_components.code, {\n          children: \"GET\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In Step 2: add the base name path: \", _jsx(_components.code, {\n          children: \"/groceries/select=*\"\n        }), \", where \", _jsx(_components.code, {\n          children: \"groceries\"\n        }), \" is the table name in Supabase.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In Step 4: click the \", _jsx(_components.strong, {\n          children: \"Test\"\n        }), \" button next to the Endpoint input to verify the response coming from the Supabase.\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Click Save.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/get-request.gif\",\n        alt: \"Creating a GET request endpoint\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In the Builder, on the app screen:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Select the Fetch component in the Components tree and go to the \", _jsx(_components.a, {\n          href: \"doc:introduction-to-the-builder#properties-panel\",\n          children: \"Data tab from Properties Panel\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"For \", _jsx(_components.strong, {\n          children: \"Service\"\n        }), \", select the name of the Supabase Service.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"For \", _jsx(_components.strong, {\n          children: \"Endpoint\"\n        }), \", select the endpoint you want to fetch the data from.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Select the List component in the Components and go to the \", _jsx(_components.a, {\n          href: \"doc:introduction-to-the-builder#properties-panel\",\n          children: \"Data tab from Properties Panel\"\n        }), \". In Data, select \", _jsx(_components.code, {\n          children: \"Top-Level Response\"\n        }), \" from the dropdown menu.\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Then, select the Text component in the Components and then go to the Data tab from the Properties Panel.\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Add a \", _jsx(_components.code, {\n          children: \"{{varName}}\"\n        }), \" value (inside the curly braces) to represent a column field from the Supabase. For example, add \", _jsx(_components.code, {\n          children: \"{{title}}\"\n        }), \" to represent the column name from the Supabase Base.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Under \", _jsx(_components.strong, {\n          children: \"Variables\"\n        }), \", you will see the variable name defined in the previous step. From the dropdown menu, select the appropriate field that represents the data field.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/fetchall.gif\",\n        alt: \"Fetching data on app screen\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"GET request to fetch single row\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Open the \", _jsx(_components.strong, {\n        children: \"API \u0026 Cloud services\"\n      }), \" modal from the top menu, select the Supabase service, and then:\"]\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Click \", _jsx(_components.strong, {\n          children: \"Add endpoint\"\n        }), \".\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"In Step 1: enter a name for the endpoint.\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In Step 2: add the \", _jsx(_components.code, {\n          children: \"/groceries/column-name=eq.{{column-name}}\"\n        }), \" variable. Then, add a Test value for the \", _jsx(_components.code, {\n          children: \"{{column-name}}\"\n        }), \". For example, it can be the \", _jsx(_components.code, {\n          children: \"title\"\n        }), \" or the \", _jsx(_components.code, {\n          children: \"id\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In Step 4: click the \", _jsx(_components.strong, {\n          children: \"Test\"\n        }), \" button next to the Endpoint input to verify the response coming from the Supabase.\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Click Save.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/getsingle.gif\",\n        alt: \"Creating endpoint to fetch a single row\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"On app screen:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Select the Fetch component in the Components tree and go to the Data tab from Properties Panel\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"For \", _jsx(_components.strong, {\n          children: \"Service\"\n        }), \", select the name of the Supabase Service.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"For \", _jsx(_components.strong, {\n          children: \"endpoint\"\n        }), \", select the endpoint you want to fetch the data from.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Set the value for the \", _jsx(_components.code, {\n          children: \"id\"\n        }), \" in the Configuration \u003e URL Structure section to Navigation \u003e id.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Select the List component in the Components and go to the Data tab from Properties Panel. In Data, select \", _jsx(_components.code, {\n          children: \"Top-Level Response\"\n        }), \" from the dropdown menu.\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Then, select the Text component in the Components and then go to the Data tab from the Properties Panel.\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Add a \", _jsx(_components.code, {\n          children: \"{{varName}}\"\n        }), \" value (inside the curly braces) to represent the column field from the Supabase. For example, add \", _jsx(_components.code, {\n          children: \"{{title}}\"\n        }), \" to represent the field and value from the Supabase data table.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Under \", _jsx(_components.strong, {\n          children: \"Variables\"\n        }), \", you will see the variable name defined in the previous step. From the dropdown menu, select the appropriate field that represents the data field.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/fetchsingle.png\",\n        alt: \"Displaying data from single row\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"POST request to submit a new row\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Submitting new Data from the Draftbit app to Supabase's REST API requires the request to be sent using the HTTP \", _jsx(_components.code, {\n        children: \"POST\"\n      }), \" method.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"For this section, you need to use at least one component that accepts user input and has a Field Name prop to POST data using Supabase REST API.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You can use one of the following components in Draftbit:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Text Input\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Text Area/Text Field\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Checkbox\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Slider\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Radio Button Group\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Radio Button\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In addition, you need a Touchable component like a Button to attach the POST action. After you have created these components, we will create the \", _jsx(_components.code, {\n        children: \"POST\"\n      }), \" endpoint:\"]\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Click \", _jsx(_components.strong, {\n          children: \"Add endpoint\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In Step 1: enter a name for the endpoint and select the Method to \", _jsx(_components.code, {\n          children: \"POST\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In Step 2: enter the base name as path: \", _jsx(_components.code, {\n          children: \"/groceries\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In Step 3: add a valid Body structure to submit a POST request. Add one or many \", _jsx(_components.code, {\n          children: \"{{variable}}\"\n        }), \" for test values. Click Body Preview to validate the structure of the Body in the request. For the example, let's create a variable called \", _jsx(_components.code, {\n          children: \"{{inputValue}}\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In Step 4: to see the new row added to the Supabase data table as JSON response inside the Builder, you have to pass a new header called \", _jsx(_components.code, {\n          children: \"Prefer\"\n        }), \" with its value as \", _jsx(_components.code, {\n          children: \"return=representation\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In Step 5: click the \", _jsx(_components.strong, {\n          children: \"Test\"\n        }), \" button next to the Endpoint input to verify the response coming from the Supabase and click Save.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/postrequest.gif\",\n        alt: \"Make a POST request to add new data to Supabase database\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Once you follow the above steps, you should get a 200 OK response with exactly the new record as a JSON you have entered for your schema.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"An example of how Body in a request will look like:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"title\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"{inputValue\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-9)\",\n                  \"fontStyle\": \"italic\"\n                }\n              }\n            }, {\n              \"content\": \"}}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"json\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Where \", _jsx(_components.code, {\n        children: \"title\"\n      }), \" is the column name in your Supabase database table.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In Draftbit, using a Touchable or a Button component, you can trigger the action \", _jsx(_components.strong, {\n        children: \"API Request\"\n      }), \" to submit the data to the endpoint.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Now, there is a working \", _jsx(_components.code, {\n        children: \"POST\"\n      }), \" request in Draftbit. Map its response to the components on your screen in Draftbit.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"First, for each input component, make sure you have set the Field Names (found in the Configs tab, second from the left) to unique values. For example, in the screen below, there is one TextInput field component with the value of the \", _jsx(_components.code, {\n        children: \"Field Name\"\n      }), \" prop of \", _jsx(_components.code, {\n        children: \"textInputValue\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/textinput.png\",\n        alt: \"Field Name prop on a TextInput component\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Next, on your Button component, go to the Interactions tab in the Properties panel located on the far-right-hand side. Select an Action called \", _jsx(_components.code, {\n        children: \"API request\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In the API request action:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"In \", _jsx(_components.strong, {\n          children: \"Service\"\n        }), \", select the name to Supabase API Service.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In \", _jsx(_components.strong, {\n          children: \"Endpoint\"\n        }), \", select the name of the Endpoint.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Then add the configuration for the body request to be sent by selecting the values for \", _jsx(_components.code, {\n          children: \"{{inputValue}}\"\n        }), \".\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/postapirequest.png\",\n        alt: \"Setting up the API Request to send a POST request\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"After completing the above steps, you can trigger the API request to submit new data to the Supabase database.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"PATCH request to Update a new record\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Updating an existing record from the Draftbit app to Supabase's REST API requires the request to be sent using the HTTP \", _jsx(_components.code, {\n        children: \"PATCH\"\n      }), \" method.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"After you have created your screen components in the Draftbit builder, open the Supabase service and make the \", _jsx(_components.code, {\n        children: \"PATCH\"\n      }), \" endpoint:\"]\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Click \", _jsx(_components.strong, {\n          children: \"Add endpoint\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In Step 1: enter a name for the endpoint and select the Method to \", _jsx(_components.code, {\n          children: \"PATCH\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In Step 2: enter the base name as path: \", _jsx(_components.code, {\n          children: \"/groceries?id=eq.{{id}}\"\n        }), \", where \", _jsx(_components.code, {\n          children: \"id\"\n        }), \" is the value of an existing record in the database.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In Step 3: add a valid Body structure to submit a PATCH request. Add one or many \", _jsx(_components.code, {\n          children: \"{{variable}}\"\n        }), \" for test values depending on the structure of your app. Click Body Preview to validate the structure of the Body in the request. For the example, let's create a variable called \", _jsx(_components.code, {\n          children: \"{{inputValue}}\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In Step 5: click the \", _jsx(_components.strong, {\n          children: \"Test\"\n        }), \" button next to the Endpoint input to verify the response coming from the Supabase and click Save.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/patch.gif\",\n        alt: \"Creating an endpoint for PATCH request\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Next, on your Button component, go to the Interactions tab in the Properties panel located on the far-right-hand side. Select an Action called \", _jsx(_components.code, {\n        children: \"API request\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In the API request action:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"In \", _jsx(_components.strong, {\n          children: \"Service\"\n        }), \", select the name to Supabase API Service.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In \", _jsx(_components.strong, {\n          children: \"Endpoint\"\n        }), \", select the name of the Endpoint.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Then add the configuration for the query param, and the body request to be sent by selecting the values for \", _jsx(_components.code, {\n          children: \"{{inputValue}}\"\n        }), \".\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/patchapirequest.png\",\n        alt: \"Setting up the API Request to send a PATCH request\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"After completing the above steps, you can trigger the API request to update existing data in the Supabase database.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"DELETE request to remove an existing record\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The \", _jsx(_components.code, {\n        children: \"DELETE\"\n      }), \" request is to the Supabase with an item's \", _jsx(_components.code, {\n        children: \"column-name\"\n      }), \" to remove that particular record from the table. You can use a \", _jsx(_components.a, {\n        href: \"https://supabase.io/docs/reference/javascript/using-filters\",\n        children: \"filter from Supabase\"\n      }), \" to filter the value of a specific \", _jsx(_components.code, {\n        children: \"column-name\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"After you have created your screen components in the Draftbit builder, open the Supabase service and create the \", _jsx(_components.code, {\n        children: \"DELETE\"\n      }), \" endpoint:\"]\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Click \", _jsx(_components.strong, {\n          children: \"Add endpoint\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In Step 1: enter a name for the endpoint and select the Method to \", _jsx(_components.code, {\n          children: \"DELETE\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In Step 2: add \", _jsx(_components.code, {\n          children: \"/groceries/columnName=eq.{{columnName}}\"\n        }), \". Then, add a Test value for the \", _jsx(_components.code, {\n          children: \"{{columnName}}\"\n        }), \". For example, the \", _jsx(_components.code, {\n          children: \"{{columnName}}\"\n        }), \" here can be \", _jsx(_components.code, {\n          children: \"id\"\n        }), \" of the record.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In Step 4: click the \", _jsx(_components.strong, {\n          children: \"Test\"\n        }), \" button next to the Endpoint input to verify the response from the Supabase.\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Click Save.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/delete.gif\",\n        alt: \"Creating an endpoint for DELETE request\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Next, on your Button component, go to the Interactions tab in the Properties panel located on the far-right-hand side. Select an Action called \", _jsx(_components.code, {\n        children: \"API request\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In the API Request action:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"In \", _jsx(_components.strong, {\n          children: \"Service\"\n        }), \", select the name to Supabase API Service.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In \", _jsx(_components.strong, {\n          children: \"Endpoint\"\n        }), \", select the name of the Endpoint.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Then, add the configuration for the query request to be sent by selecting a value. For example, in this case it will be the \", _jsx(_components.code, {\n          children: \"id\"\n        }), \" of the record coming from the Navigation parameter.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/draftbit/documentation/deleteapirequest.gif\",\n        alt: \"Setting up the API Request to send a DELETE request\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Resources\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://draftb.it/3Fkbask\",\n          children: \"Draftbit\"\n        }), \" official website.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://community.draftbit.com/home\",\n          children: \"Draftbit Community\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://docs.draftbit.com/\",\n          children: \"Draftbit\"\n        }), \" documentation.\"]\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  chCodeConfig,\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{}}},"__N_SSG":true},"page":"/partners/integrations/[slug]","query":{"slug":"draftbit"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"isExperimentalCompile":false,"gsp":true,"scriptLoader":[]}</script></body></html>