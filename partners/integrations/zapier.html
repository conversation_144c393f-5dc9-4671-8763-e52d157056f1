<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../../rss.xml" data-next-head=""/><link rel="manifest" href="../../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:type" content="website" data-next-head=""/><meta property="og:image:alt" content="Supabase Og Image" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Zapier | Works With Supabase</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Zapier is a no-code workflow automation tool that seamlessly connects different applications and services" data-next-head=""/><meta property="og:title" content="Zapier | Works With Supabase" data-next-head=""/><meta property="og:description" content="Zapier is a no-code workflow automation tool that seamlessly connects different applications and services" data-next-head=""/><meta property="og:url" content="https://supabase.com/partners/integrations/zapier" data-next-head=""/><meta property="og:image" content="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/zapier-logomark.svg?t=2024-02-16T07%3A33%3A56.522Z" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2263-07e8ebb96cc2b014.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8658-639e1bb66549b52b.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/partners/integrations/%5Bslug%5D-cff8a94339d02c6e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqela6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R1eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R1eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R2eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R2eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R3eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R3eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav></div><main class="relative min-h-screen"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20"><div class="col-span-12 mx-auto mb-2 max-w-5xl space-y-10 lg:col-span-2"><a class="text-foreground hover:text-foreground-lighter flex cursor-pointer items-center transition-colors" href="../integrations.html"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left"><path d="m15 18-6-6 6-6"></path></svg>Back</a><div class="flex items-center space-x-4"><img alt="Zapier" loading="lazy" width="56" height="56" decoding="async" data-nimg="1" class="bg-surface-200 flex-shrink-f0 h-14 w-14 rounded-full" style="color:transparent" src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/zapier-logomark.svg?t=2024-02-16T07%3A33%3A56.522Z"/><h1 class="h1" style="margin-bottom:0">Zapier</h1></div><div class="bg-gradient-to-t from-background-alternative to-background border-b p-6 [&amp;_.swiper-container]:overflow-visible" style="margin-left:calc(50% - 50vw);margin-right:calc(50% - 50vw)"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0 !px-3 lg:!px-12 xl:!p-0 mx-auto max-w-5xl"><div class="swiper"><div class="swiper-wrapper"></div></div></div></div><div class="grid gap-y-12 lg:grid-cols-8 lg:space-x-12"><div class="lg:col-span-5 overflow-hidden"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Overview</h2><div class="prose"><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<h1><strong>Connecting Supabase to Zapier</strong></h1>
<p><strong>Zapier</strong> is a powerhouse in automation that effortlessly connects different platforms — Supabase included. This post will show you how to connect your Supabase PostgreSQL instance with Zapier.</p>
<p>It will emphasize security practices and highlight Supabase features that will help you manage your integration. Additionally, it addresses Zapier&#x27;s limitations and suggests alternatives to overcome them.</p>
<h1><strong>Walkthrough</strong></h1>
<p>This guide will show you how to send emails when database events occur, but it can be generalized to work with any Zapier connection.</p>
<h2><strong>Database Set up</strong></h2>
<h3>Create a user_profile table:</h3>
<p>In your project’s <a href="../../dashboard/project/_/sql/%5B%5B...routeSlug%5D%5D.html">SQL Editor</a>, execute the below query. This will be used as the example table for the walkthrough.</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>create table &quot;products&quot; (</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>	&quot;product_id&quot; serial primary key,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>	&quot;product_name&quot; varchar(512) not null,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>	&quot;price&quot; decimal(10, 2) not null,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>	&quot;description&quot; text,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>	&quot;stock_quantity&quot; INT DEFAULT 0</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>);</span></div></div><br/></code></div></div>
<h3>Insert some items into your product table:</h3>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>insert into &quot;products&quot; (</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>	product_name,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>	price,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>	description,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>	stock_quantity</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>)</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>values</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>(&#x27;Pokeball&#x27;, 4.99, &#x27;Basic Poké Ball for catching Pokémon.&#x27;, 100),</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>(&#x27;Great Ball&#x27;, 7.99, &#x27;Better than a Poké Ball, higher catch rate.&#x27;, 75),</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>(&#x27;Ultra Ball&#x27;, 12.99, &#x27;High-performance ball with excellent catch rate.&#x27;, 10);</span></div></div><br/></code></div></div>
<p>You should be able to see your newly created table in your <a href="../../dashboard/project/_/%5B%5B...routeSlug%5D%5D.html">Dashboard’s Table Editor</a></p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/first_image.png" alt="Dashboard Table Editor"/></p>
<h2><strong>Zapier Triggers and Actions:</strong></h2>
<p>Depending on your settings, Zapier will connect to your database every 2 to 15 minutes to observe changes. When information is added or updated in PostgreSQL, Zapier can initiate external events, like sending an email.</p>
<p>Here are the types of PostgreSQL events that Zapier can respond to:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/second_image.png" alt="Zapier trigger events"/></p>
<p>Unfortunately, at the time of writing, Zapier is unable to respond to destructive events, such as deleting a row or dropping a table. Likewise, when an external event occurs, such as a change in a Google Spreadsheet, Zapier can only update, insert, or read rows within PostgreSQL.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/thrid_image.png?t=2024-02-16T06%3A13%3A24.240Z" alt="PostgreSQL responses to external events"/></p>
<p>Zapier can essentially respond to any update or creation event, but it is incapable of observing delete or drop events in PostgreSQL. Don’t worry! This tutorial will offer alternatives to circumvent these limitations.</p>
<h2><strong>Creating a Custom Zapier User:</strong></h2>
<p><strong>Best practice</strong> demands that you create a new PostgreSQL user for Zapier. In the worst-case scenario where your Zapier account gets compromised, you want to have user restrictions against the custom user to protect your data.</p>
<h3>Create a “zapier” User in the <a href="../../dashboard/project/_/sql/%5B%5B...routeSlug%5D%5D.html">SQL Editor</a>:</h3>
<p>create user &quot;zapier&quot; with a “super secret password”;</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>CREATE USER &quot;zapier&quot; WITH PASSWORD &#x27;&lt;new password&gt;&#x27;;</span></div></div><br/></code></div></div>
<p>In the <a href="https://supabase.com/dashboard/project/_/database/roles">Dashboard’s Database section under roles</a>, you should be able to see your new user.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/fourth_image.png" alt="new role in Dashboard"/></p>
<p>All new database users are capable of seeing what database objects exist, such as tables and schemas, but they are unable to look at their data or definitions, nor are they able to call commands, such as SELECT or DELETE. You will need to explicitly grant the necessary privileges to your new “zapier” user. Here is a visual of all possible user privileges from the PostgreSQL <a href="https://www.postgresql.org/docs/current/ddl-priv.html">documentation</a>.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/fifth_image.png?t=2024-02-16T06%3A19%3A08.575Z" alt="PostgreSQL database object priveleges"/></p>
<p>You want to give your “zapier” user the bare minimum of necessary privileges. In this example, it will only need read access to the <code>products</code> table.</p>
<h3>Grant the Below Privileges to Your “zapier” User:</h3>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>-- optional if the table is in the “public” schema</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>grant usage on schema public to zapier;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>-- Grant SELECT privilege on the table</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>grant select on table products to “zapier”;</span></div></div><br/></code></div></div>
<p>If you enabled RLS for your table, you will also need to create policies for <code>zapier</code>.</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>create policy &quot;zapier can read from products table&quot;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>on public.products</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>for select</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>to &quot;zapier&quot;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>using (</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>	true</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>);</span></div></div><br/></code></div></div>
<p>With the following privileges set, you can connect to Zapier.</p>
<h2><strong>Configuring your Zap:</strong></h2>
<h3>In Zapier, Create a New <em><a href="https://zapier.com/app/zaps">Zap</a>:</em></h3>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/sixth_image.png?t=2024-02-16T06%3A22%3A15.178Z" alt="Zapier entry image"/></p>
<h3>Select PostgreSQL as Your Trigger:</h3>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/seventh_image.png?t=2024-02-16T06%3A23%3A22.512Z" alt="PostgreSQL trigger option in Zapier"/></p>
<h3>Connect PostgreSQL to Zapier:</h3>
<p>You will be prompted to connect your PostgreSQL instance to Zapier. You will need the following credentials:</p>
<ul>
<li>Host</li>
<li>Port</li>
<li>Database</li>
<li>Schema</li>
<li>Username</li>
<li>Password</li>
<li>SSL Certificate</li>
</ul>
<p>You can find your host, port, and database in your <a href="../../dashboard/project/_/settings/%5B%5B...routeSlug%5D%5D.html">Dashboard’s Database Settings</a>:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/eighth_image.png?t=2024-02-16T06%3A26%3A23.126Z" alt="Screenshot 2024-02-11 at 11.28.07 PM.png"/></p>
<p>You can enter your <code>zapier.&lt;project id&gt;</code> as your user and the password you created earlier in the tutorial.</p>
<p>Further down in the <a href="../../dashboard/project/_/settings/%5B%5B...routeSlug%5D%5D.html">Dashboard’s Database Settings</a>, you will see the option to download an SSL certificate. This certificate is necessary to prevent snooping and man-in-the-middle attacks.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/ninth_image.png" alt="Dashboard&#x27;s SSL certiciate"/></p>
<p>After connecting your instance, Zapier will allow you to define the event that triggers your Zap.</p>
<h3>Choose Your Trigger Event (Select <em>Custom Query):</em></h3>
<p><a href="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/tenth_image.png?t=2024-02-16T06%3A29%3A15.169Z">Possible PostgreSQL/Zapier trigger events</a></p>
<p>It will prompt you to insert a trigger query.</p>
<h3>Insert the Following SQL:</h3>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>select * from &quot;products&quot; where &quot;stock_quantity&quot; &lt;= 10;</span></div></div><br/></code></div></div>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/eleventh_image.png?t=2024-02-16T06%3A30%3A36.615Z" alt="Query in Zapier"/></p>
<p>Finally, it will ask you to perform a test using a row it received from your table. The only row that should have returned should be for the “Ultra Ball” product.</p>
<h3>Select a Product and go to the Next Step:</h3>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/twelfth_image.png?t=2024-02-16T06%3A38%3A14.842Z" alt="Selecting a prow from product&#x27;s table"/></p>
<p>You should be prompted to pick an application for an event to occur in after a trigger happens.</p>
<h3>Select Gmail for the <em>App</em> and <em>Send Email</em> for the <em>Event:</em></h3>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/thirteenth_image.png" alt="Selecting Event"/></p>
<h3>Connect Your Gmail Account to Zapier:</h3>
<p>Finally, it will ask you to compose an email with the values from your PostgreSQL table. I chose to make the below message, but you can format it however you want.</p>
<h3>Format Your Message:</h3>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/fourthteenth_image.png?t=2024-02-16T06%3A45%3A56.062Z" alt="Formatting Message"/></p>
<p><strong>Viola!</strong> You can now run a test in Zapier and publish your Zap. Depending on where you sent the test email, you may receive a message like the following:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/fifteenth_image.png" alt="email from zapier"/></p>
<p>In your <a href="https://supabase.com/dashboard/project/wavwvitxkgeviubqugal/logs/postgres-logs?s=zapier">Dashboard’s Postgres Logs</a>, you should be able to see when Zapier connects:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/sixteenth_image.png" alt="Viewing Zapier connection message"/></p>
<h2><strong>Clean up</strong></h2>
<p>In case you want to drop your “zapier” user, you can do so with the following queries:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>revoke select on table &quot;products&quot; from &quot;zapier&quot;;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>drop user &quot;zapier&quot;;</span></div></div><br/></code></div></div>
<h2><strong>The Limitations of Zapier:</strong></h2>
<h2>Non-Destructive Observations and Actions Only:</h2>
<p>As discussed earlier, Zapier is only capable of monitoring non-destructive actions. It can observe when rows are updated, or when columns or tables are created, but it is unable to see when they’re deleted/dropped. Similarly, Zapier cannot be used to destroy objects within your database.</p>
<h2>Limited Polling Frequency:</h2>
<p>Another concern is how frequently Zapier makes observations. Within the advanced settings, you can configure Zapier to observe your database as frequently as every 2 minutes, or as infrequently as every 15. Many users, though, need a broader window of observation.</p>
<h2>Max Observable Rows:</h2>
<p><a href="https://help.zapier.com/hc/en-us/articles/8496037193997-Common-Problems-with-PostgreSQL#some-new-rows-didn-t-trigger-my-zap-0-1">Zapier</a> <a href="https://help.zapier.com/hc/en-us/articles/8496037193997-Common-Problems-with-PostgreSQL#some-new-rows-didn-t-trigger-my-zap-0-1">is capable of observing only the 50 most recently created rows when using the &quot;New Row&quot; trigger.</a> So, if more than 50 rows are added in a polling interval, some data will be ignored.</p>
<h1><strong>Alternatives to Zapier:</strong></h1>
<p>The value of Zapier is that it can bridge together practically any service, making your life as a developer relatively simple. Even with its limitations, it is a robust and reliable solution. However, other integrations are available that may be more suitable for your workload.</p>
<p><a href="https://supabase.com/partners/integrations/n8n">n8n</a>, like Zapier, offers simple, no-code connectors (760 to be exact), but it is open source and can be self-hosted.</p>
<p>Other workflow automation alternatives that have partnered with Supabase include <a href="https://supabase.com/partners/integrations/sequin_io">Sequin.io</a> and <a href="https://supabase.com/partners/integrations/bracket">Brackets</a>.</p>
<p>If you just need to trigger outside services when database events occur, you could also leverage <a href="../../docs/guides/database/webhooks.html">Supabase Webhooks</a>! If the requests need to be scheduled, then this could be implemented natively with only PostgreSQL through the <a href="../../docs/guides/database/extensions/pg_cron.html">pg_cron extension</a> in tandem with either the <a href="../../docs/guides/database/extensions/pg_net.html">pg_net</a> or <a href="../../docs/guides/database/extensions/http.html">http</a> extensions.</p>
<h2><strong>Conclusion</strong></h2>
<p>In this walkthrough, we learned how to connect Zapier and Supabase, but also about the alternative solutions that may meet our integration needs</p></div></div><div class="lg:col-span-3"><div class="sticky top-20 flex flex-col gap-4"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Details</h2><div class="text-foreground divide-y"><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Developer</span><span class="text-foreground">Zapier</span></div><div class="flex items-center justify-between py-2"><span class="text-lighter">Category</span><a class="text-brand hover:underline transition-colors" href="../integrations.html#api">API</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Website</span><a href="https://zapier.com/" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors">zapier.com</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Documentation</span><a href="https://zapier.com/" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors"><span class="flex items-center space-x-1"><span>Learn</span><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></span></a></div></div><p class="text-foreground-light text-sm">Third-party integrations and docs are managed by Supabase partners.</p></div></div></div></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"partner":{"id":116,"slug":"zapier","type":"technology","category":"API","developer":"Zapier","title":"Zapier","description":"Zapier is a no-code workflow automation tool that seamlessly connects different applications and services","logo":"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/zapier-logomark.svg?t=2024-02-16T07%3A33%3A56.522Z","images":null,"overview":"# **Connecting Supabase to Zapier**\n\n**Zapier** is a powerhouse in automation that effortlessly connects different platforms — Supabase included. This post will show you how to connect your Supabase PostgreSQL instance with Zapier.\n\nIt will emphasize security practices and highlight Supabase features that will help you manage your integration. Additionally, it addresses Zapier's limitations and suggests alternatives to overcome them.\n\n# **Walkthrough**\n\nThis guide will show you how to send emails when database events occur, but it can be generalized to work with any Zapier connection.\n\n## **Database Set up**\n\n### Create a user_profile table:\n\nIn your project’s [SQL Editor](https://supabase.com/dashboard/project/_/sql/new), execute the below query. This will be used as the example table for the walkthrough.\n\n```sql\ncreate table \"products\" (\n\t\"product_id\" serial primary key,\n\t\"product_name\" varchar(512) not null,\n\t\"price\" decimal(10, 2) not null,\n\t\"description\" text,\n\t\"stock_quantity\" INT DEFAULT 0\n);\n```\n\n### Insert some items into your product table:\n\n```sql\ninsert into \"products\" (\n\tproduct_name,\n\tprice,\n\tdescription,\n\tstock_quantity\n)\nvalues\n('Pokeball', 4.99, 'Basic Poké Ball for catching Pokémon.', 100),\n('Great Ball', 7.99, 'Better than a Poké Ball, higher catch rate.', 75),\n('Ultra Ball', 12.99, 'High-performance ball with excellent catch rate.', 10);\n```\n\nYou should be able to see your newly created table in your [Dashboard’s Table Editor](https://supabase.com/dashboard/project/_/editor)\n\n![Dashboard Table Editor](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/first_image.png)\n\n## **Zapier Triggers and Actions:**\n\nDepending on your settings, Zapier will connect to your database every 2 to 15 minutes to observe changes. When information is added or updated in PostgreSQL, Zapier can initiate external events, like sending an email.\n\nHere are the types of PostgreSQL events that Zapier can respond to:\n\n![Zapier trigger events](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/second_image.png)\n\nUnfortunately, at the time of writing, Zapier is unable to respond to destructive events, such as deleting a row or dropping a table. Likewise, when an external event occurs, such as a change in a Google Spreadsheet, Zapier can only update, insert, or read rows within PostgreSQL.\n\n![PostgreSQL responses to external events](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/thrid_image.png?t=2024-02-16T06%3A13%3A24.240Z)\n\nZapier can essentially respond to any update or creation event, but it is incapable of observing delete or drop events in PostgreSQL. Don’t worry! This tutorial will offer alternatives to circumvent these limitations.\n\n## **Creating a Custom Zapier User:**\n\n**Best practice** demands that you create a new PostgreSQL user for Zapier. In the worst-case scenario where your Zapier account gets compromised, you want to have user restrictions against the custom user to protect your data.\n\n### Create a “zapier” User in the [SQL Editor](https://supabase.com/dashboard/project/_/sql/new):\n\ncreate user \"zapier\" with a “super secret password”;\n\n```sql\nCREATE USER \"zapier\" WITH PASSWORD '\u003cnew password\u003e';\n```\n\nIn the [Dashboard’s Database section under roles](https://supabase.com/dashboard/project/_/database/roles), you should be able to see your new user.\n\n![new role in Dashboard](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/fourth_image.png)\n\nAll new database users are capable of seeing what database objects exist, such as tables and schemas, but they are unable to look at their data or definitions, nor are they able to call commands, such as SELECT or DELETE. You will need to explicitly grant the necessary privileges to your new “zapier” user. Here is a visual of all possible user privileges from the PostgreSQL [documentation](https://www.postgresql.org/docs/current/ddl-priv.html).\n\n![PostgreSQL database object priveleges](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/fifth_image.png?t=2024-02-16T06%3A19%3A08.575Z)\n\nYou want to give your “zapier” user the bare minimum of necessary privileges. In this example, it will only need read access to the `products` table.\n\n### Grant the Below Privileges to Your “zapier” User:\n\n```sql\n-- optional if the table is in the “public” schema\ngrant usage on schema public to zapier;\n\n-- Grant SELECT privilege on the table\ngrant select on table products to “zapier”;\n```\n\nIf you enabled RLS for your table, you will also need to create policies for `zapier`.\n\n```sql\ncreate policy \"zapier can read from products table\"\non public.products\nfor select\nto \"zapier\"\nusing (\n\ttrue\n);\n```\n\nWith the following privileges set, you can connect to Zapier.\n\n## **Configuring your Zap:**\n\n### In Zapier, Create a New _[Zap](https://zapier.com/app/zaps):_\n\n![Zapier entry image](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/sixth_image.png?t=2024-02-16T06%3A22%3A15.178Z)\n\n### Select PostgreSQL as Your Trigger:\n\n![PostgreSQL trigger option in Zapier](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/seventh_image.png?t=2024-02-16T06%3A23%3A22.512Z)\n\n### Connect PostgreSQL to Zapier:\n\nYou will be prompted to connect your PostgreSQL instance to Zapier. You will need the following credentials:\n\n- Host\n- Port\n- Database\n- Schema\n- Username\n- Password\n- SSL Certificate\n\nYou can find your host, port, and database in your [Dashboard’s Database Settings](https://supabase.com/dashboard/project/_/settings/database):\n\n![Screenshot 2024-02-11 at 11.28.07 PM.png](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/eighth_image.png?t=2024-02-16T06%3A26%3A23.126Z)\n\nYou can enter your `zapier.\u003cproject id\u003e` as your user and the password you created earlier in the tutorial.\n\nFurther down in the [Dashboard’s Database Settings](https://supabase.com/dashboard/project/_/settings/database), you will see the option to download an SSL certificate. This certificate is necessary to prevent snooping and man-in-the-middle attacks.\n\n![Dashboard's SSL certiciate](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/ninth_image.png)\n\nAfter connecting your instance, Zapier will allow you to define the event that triggers your Zap.\n\n### Choose Your Trigger Event (Select _Custom Query):_\n\n[Possible PostgreSQL/Zapier trigger events](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/tenth_image.png?t=2024-02-16T06%3A29%3A15.169Z)\n\nIt will prompt you to insert a trigger query.\n\n### Insert the Following SQL:\n\n```sql\nselect * from \"products\" where \"stock_quantity\" \u003c= 10;\n```\n\n![Query in Zapier](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/eleventh_image.png?t=2024-02-16T06%3A30%3A36.615Z)\n\nFinally, it will ask you to perform a test using a row it received from your table. The only row that should have returned should be for the “Ultra Ball” product.\n\n### Select a Product and go to the Next Step:\n\n![Selecting a prow from product's table](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/twelfth_image.png?t=2024-02-16T06%3A38%3A14.842Z)\n\nYou should be prompted to pick an application for an event to occur in after a trigger happens.\n\n### Select Gmail for the _App_ and _Send Email_ for the _Event:_\n\n![Selecting Event](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/thirteenth_image.png)\n\n### Connect Your Gmail Account to Zapier:\n\nFinally, it will ask you to compose an email with the values from your PostgreSQL table. I chose to make the below message, but you can format it however you want.\n\n### Format Your Message:\n\n![Formatting Message](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/fourthteenth_image.png?t=2024-02-16T06%3A45%3A56.062Z)\n\n**Viola!** You can now run a test in Zapier and publish your Zap. Depending on where you sent the test email, you may receive a message like the following:\n\n![email from zapier](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/fifteenth_image.png)\n\nIn your [Dashboard’s Postgres Logs](https://supabase.com/dashboard/project/wavwvitxkgeviubqugal/logs/postgres-logs?s=zapier), you should be able to see when Zapier connects:\n\n![Viewing Zapier connection message](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/sixteenth_image.png)\n\n## **Clean up**\n\nIn case you want to drop your “zapier” user, you can do so with the following queries:\n\n```sql\nrevoke select on table \"products\" from \"zapier\";\ndrop user \"zapier\";\n```\n\n## **The Limitations of Zapier:**\n\n## Non-Destructive Observations and Actions Only:\n\nAs discussed earlier, Zapier is only capable of monitoring non-destructive actions. It can observe when rows are updated, or when columns or tables are created, but it is unable to see when they’re deleted/dropped. Similarly, Zapier cannot be used to destroy objects within your database.\n\n## Limited Polling Frequency:\n\nAnother concern is how frequently Zapier makes observations. Within the advanced settings, you can configure Zapier to observe your database as frequently as every 2 minutes, or as infrequently as every 15. Many users, though, need a broader window of observation.\n\n## Max Observable Rows:\n\n[Zapier](https://help.zapier.com/hc/en-us/articles/8496037193997-Common-Problems-with-PostgreSQL#some-new-rows-didn-t-trigger-my-zap-0-1) [is capable of observing only the 50 most recently created rows when using the \"New Row\" trigger.](https://help.zapier.com/hc/en-us/articles/8496037193997-Common-Problems-with-PostgreSQL#some-new-rows-didn-t-trigger-my-zap-0-1) So, if more than 50 rows are added in a polling interval, some data will be ignored.\n\n# **Alternatives to Zapier:**\n\nThe value of Zapier is that it can bridge together practically any service, making your life as a developer relatively simple. Even with its limitations, it is a robust and reliable solution. However, other integrations are available that may be more suitable for your workload.\n\n[n8n](https://supabase.com/partners/integrations/n8n), like Zapier, offers simple, no-code connectors (760 to be exact), but it is open source and can be self-hosted.\n\nOther workflow automation alternatives that have partnered with Supabase include [Sequin.io](https://supabase.com/partners/integrations/sequin_io) and [Brackets](https://supabase.com/partners/integrations/bracket).\n\nIf you just need to trigger outside services when database events occur, you could also leverage [Supabase Webhooks](https://supabase.com/docs/guides/database/webhooks)! If the requests need to be scheduled, then this could be implemented natively with only PostgreSQL through the [pg_cron extension](https://supabase.com/docs/guides/database/extensions/pg_cron) in tandem with either the [pg_net](https://supabase.com/docs/guides/database/extensions/pg_net) or [http](https://supabase.com/docs/guides/database/extensions/http) extensions.\n\n## **Conclusion**\n\nIn this walkthrough, we learned how to connect Zapier and Supabase, but also about the alternative solutions that may meet our integration needs\n","website":"https://zapier.com/","docs":"https://zapier.com/","contact":1,"approved":true,"created_at":"2024-02-16T05:36:23.703963+00:00","tsv":"'-02':742C '-11':743C '/app/zaps):_':674C '/dashboard/project/_/database/roles),':462C '/dashboard/project/_/editor)':231C '/dashboard/project/_/settings/database),':777C '/dashboard/project/_/settings/database):':739C '/dashboard/project/_/sql/new),':118C '/dashboard/project/_/sql/new):':437C '/dashboard/project/wavwvitxkgeviubqugal/logs/postgres-logs?s=zapier),':1038C '/docs/current/ddl-priv.html).':548C '/docs/guides/database/extensions/http)':1385C '/docs/guides/database/extensions/pg_cron)':1370C '/docs/guides/database/extensions/pg_net)':1380C '/docs/guides/database/webhooks)!':1346C '/hc/en-us/articles/8496037193997-common-problems-with-postgresql#some-new-rows-didn-t-trigger-my-zap-0-1)':1194C,1214C '/partners/integrations/bracket).':1325C '/partners/integrations/n8n),':1283C '/partners/integrations/sequin_io)':1320C '/storage/v1/object/public/images/integrations/zapier/eighth_image.png?t=2024-02-16t06%3a26%3a23.126z)':749C '/storage/v1/object/public/images/integrations/zapier/eleventh_image.png?t=2024-02-16t06%3a30%3a36.615z)':865C '/storage/v1/object/public/images/integrations/zapier/fifteenth_image.png)':1029C '/storage/v1/object/public/images/integrations/zapier/fifth_image.png?t=2024-02-16t06%3a19%3a08.575z)':555C '/storage/v1/object/public/images/integrations/zapier/first_image.png)':237C '/storage/v1/object/public/images/integrations/zapier/fourth_image.png)':478C '/storage/v1/object/public/images/integrations/zapier/fourthteenth_image.png?t=2024-02-16t06%3a45%3a56.062z)':994C '/storage/v1/object/public/images/integrations/zapier/ninth_image.png)':808C '/storage/v1/object/public/images/integrations/zapier/second_image.png)':294C '/storage/v1/object/public/images/integrations/zapier/seventh_image.png?t=2024-02-16t06%3a23%3a22.512z)':693C '/storage/v1/object/public/images/integrations/zapier/sixteenth_image.png)':1054C '/storage/v1/object/public/images/integrations/zapier/sixth_image.png?t=2024-02-16t06%3a22%3a15.178z)':680C '/storage/v1/object/public/images/integrations/zapier/tenth_image.png?t=2024-02-16t06%3a29%3a15.169z)':838C '/storage/v1/object/public/images/integrations/zapier/thirteenth_image.png)':949C '/storage/v1/object/public/images/integrations/zapier/thrid_image.png?t=2024-02-16t06%3a13%3a24.240z)':349C '/storage/v1/object/public/images/integrations/zapier/twelfth_image.png?t=2024-02-16t06%3a38%3a14.842z)':915C '0':161C '10':151C,212C,859C '100':188C '11.28.07':745C '12.99':203C '15':255C,1178C '2':152C,253C,1171C '2024':741C '4.99':181C '50':1201C,1219C '512':146C '7.99':191C '75':200C '760':1292C 'abl':216C,466C,510C,1042C 'access':577C 'account':410C,953C 'action':241C,1094C,1108C 'ad':263C,1222C 'addit':65C 'address':67C 'advanc':1157C 'allow':815C 'also':629C,1340C,1400C 'altern':73C,379C,1232C,1310C,1403C 'anoth':1147C 'api':1411 'app':938C 'applic':15B,923C 'ask':869C,959C 'attack':801C 'autom':9B,27C,1309C 'avail':1271C 'ball':184C,190C,196C,202C,207C,895C 'bare':564C 'basic':182C 'best':389C 'better':192C 'bracket':1322C 'bridg':1243C 'broader':1184C 'call':512C 'cannot':1135C 'capabl':484C,1102C,1196C 'case':405C,1058C 'catch':186C,198C,210C 'certici':805C 'certif':722C,787C,789C 'chang':259C,327C 'choos':825C 'chose':973C 'circumv':381C 'clean':1055C 'code':7B,1290C 'column':1118C 'command':513C 'compos':962C 'compromis':412C 'concern':1148C 'conclus':1387C 'configur':663C,1161C 'connect':13B,18C,30C,42C,101C,248C,660C,694C,703C,810C,950C,1047C,1050C,1395C 'connector':1291C 'could':1339C,1356C 'creat':105C,135C,221C,384C,394C,427C,438C,447C,632C,637C,668C,762C,1122C,1204C 'creation':358C 'credenti':714C 'cron':1366C 'custom':386C,421C,830C 'dashboard':225C,232C,454C,475C,733C,771C,802C,1032C 'data':426C,504C,1228C 'databas':88C,102C,251C,456C,481C,488C,550C,717C,730C,735C,773C,1143C,1166C,1335C 'decim':150C 'default':160C 'defin':818C 'definit':506C 'delet':311C,366C,518C 'deleted/dropped':1132C 'demand':391C 'depend':242C,1008C 'descript':155C,176C 'destroy':1139C 'destruct':307C,1091C,1107C 'develop':1253C 'differ':14B,31C 'discuss':1097C 'document':545C 'download':784C 'drop':315C,368C,1062C,1082C 'earlier':763C,1098C 'editor':115C,228C,234C,434C 'effortless':29C 'either':1374C 'email':86C,276C,941C,964C,1015C,1024C 'emphas':51C 'enabl':622C 'enter':752C 'entri':676C 'essenti':352C 'even':1256C 'event':89C,272C,283C,291C,308C,322C,346C,359C,369C,820C,828C,835C,926C,944C,946C,1336C 'everi':252C,1170C,1177C 'exact':1295C 'exampl':129C,571C 'excel':209C 'execut':119C 'exist':490C 'explicit':523C 'extens':1367C,1386C 'extern':271C,321C,345C 'featur':57C 'final':866C,956C 'find':725C 'follow':655C,713C,850C,1023C,1072C 'format':982C,987C,990C 'frequenc':1146C 'frequent':1151C,1168C 'general':95C 'get':411C 'give':559C 'gmail':935C,952C 'go':901C 'googl':330C 'grant':524C,582C,600C,607C,613C 'great':189C 'guid':79C 'happen':933C 'help':60C 'help.zapier.com':1193C,1213C 'help.zapier.com/hc/en-us/articles/8496037193997-common-problems-with-postgresql#some-new-rows-didn-t-trigger-my-zap-0-1)':1192C,1212C 'high':205C 'high-perform':204C 'higher':197C 'highlight':55C 'host':715C,727C,1306C 'howev':984C,1267C 'http':1382C 'id':139C 'ignor':1231C 'imag':677C 'implement':1358C 'incap':363C 'includ':34C,1316C 'inform':261C 'infrequ':1175C 'initi':270C 'insert':162C,170C,336C,844C,848C 'instanc':46C,706C,812C 'int':159C 'integr':64C,1269C,1409C 'interv':1226C 'item':164C 'key':142C 'learn':1392C 'leverag':1341C 'life':1250C 'like':273C,1021C,1284C 'likewis':318C 'limit':70C,383C,1086C,1144C,1259C 'log':1035C 'look':501C 'make':975C,1153C,1248C 'man':797C 'man-in-the-middl':796C 'manag':62C 'mani':1179C 'max':1188C 'may':1017C,1273C,1406C 'meet':1407C 'messag':978C,989C,991C,1020C,1051C 'middl':800C 'minimum':565C 'minut':256C,1172C 'monitor':1104C 'n8n':1280C 'name':144C,174C 'nativ':1359C 'necessari':526C,567C,791C 'need':521C,575C,630C,711C,1182C,1329C,1350C,1410C 'net':1377C 'new':396C,470C,472C,480C,530C,670C,1209C 'newli':220C 'next':904C 'no-cod':5B,1288C 'non':1090C,1106C 'non-destruct':1089C,1105C 'null':148C,154C 'object':489C,551C,1140C 'observ':258C,365C,1092C,1111C,1154C,1164C,1187C,1189C,1198C 'obuldanrptloktxcffvn.supabase.co':236C,293C,348C,477C,554C,679C,692C,748C,807C,837C,864C,914C,948C,993C,1028C,1053C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/eighth_image.png?t=2024-02-16t06%3a26%3a23.126z)':747C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/eleventh_image.png?t=2024-02-16t06%3a30%3a36.615z)':863C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/fifteenth_image.png)':1027C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/fifth_image.png?t=2024-02-16t06%3a19%3a08.575z)':553C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/first_image.png)':235C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/fourth_image.png)':476C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/fourthteenth_image.png?t=2024-02-16t06%3a45%3a56.062z)':992C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/ninth_image.png)':806C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/second_image.png)':292C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/seventh_image.png?t=2024-02-16t06%3a23%3a22.512z)':691C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/sixteenth_image.png)':1052C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/sixth_image.png?t=2024-02-16t06%3a22%3a15.178z)':678C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/tenth_image.png?t=2024-02-16t06%3a29%3a15.169z)':836C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/thirteenth_image.png)':947C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/thrid_image.png?t=2024-02-16t06%3a13%3a24.240z)':347C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/twelfth_image.png?t=2024-02-16t06%3a38%3a14.842z)':913C 'occur':90C,323C,928C,1337C 'offer':378C,1286C 'open':1299C 'option':591C,688C,782C 'outsid':1332C 'overcom':75C 'partner':1313C 'password':445C,451C,720C,760C 'perform':206C,872C 'pg':1365C,1376C 'pick':921C 'platform':32C 'pm.png':746C 'pokebal':180C 'poké':183C,195C 'pokémon':187C 'polici':633C,638C 'poll':1145C,1225C 'port':716C,728C 'possibl':539C,832C 'post':36C 'postgr':1034C 'postgresql':45C,267C,282C,341C,342C,371C,397C,544C,549C,682C,686C,695C,705C,970C,1362C 'postgresql/zapier':833C 'powerhous':25C 'practic':53C,390C,1245C 'prevent':793C 'price':149C,175C 'primari':141C 'priveleg':552C 'privileg':527C,541C,568C,585C,609C,656C 'product':137C,138C,143C,167C,172C,173C,580C,617C,643C,855C,896C,899C,910C,1079C 'profil':108C 'project':112C 'prompt':701C,841C,919C 'protect':424C 'prow':908C 'public':598C,604C 'public.products':646C 'publish':1005C 'quantiti':158C,178C,858C 'queri':122C,831C,847C,860C,1073C 'rate':199C,211C 're':1131C 'read':338C,576C,641C 'receiv':879C,1018C 'recent':1203C 'relat':1254C 'reliabl':1265C 'request':1349C 'respond':287C,305C,353C 'respons':343C 'restrict':418C 'return':889C 'revok':1075C 'rls':623C 'robust':1263C 'role':459C,473C 'row':313C,339C,877C,885C,1113C,1190C,1205C,1210C,1220C 'run':999C 'scenario':406C 'schedul':1353C 'schema':495C,599C,603C,718C 'screenshot':740C 'seamless':12B 'secret':444C 'section':457C 'secur':52C 'see':218C,468C,486C,780C,1044C,1128C 'select':516C,608C,614C,648C,681C,829C,853C,897C,906C,934C,945C,1076C 'self':1305C 'self-host':1304C 'send':85C,274C,940C 'sent':1012C 'sequin.io':1317C 'serial':140C 'servic':17B,1247C,1333C 'set':103C,245C,657C,736C,774C,1158C 'show':38C,81C 'similar':1133C 'simpl':1255C,1287C 'snoop':794C 'solut':1266C,1404C 'sourc':1300C 'spreadsheet':331C 'sql':114C,134C,169C,433C,446C,590C,636C,851C,852C,1074C 'ssl':721C,786C,804C 'step':905C 'stock':157C,177C,857C 'suggest':72C 'suitabl':1276C 'supabas':19C,33C,44C,56C,1315C,1342C,1398C 'supabase.com':117C,230C,436C,461C,738C,776C,1037C,1282C,1319C,1324C,1345C,1369C,1379C,1384C 'supabase.com/dashboard/project/_/database/roles),':460C 'supabase.com/dashboard/project/_/editor)':229C 'supabase.com/dashboard/project/_/settings/database),':775C 'supabase.com/dashboard/project/_/settings/database):':737C 'supabase.com/dashboard/project/_/sql/new),':116C 'supabase.com/dashboard/project/_/sql/new):':435C 'supabase.com/dashboard/project/wavwvitxkgeviubqugal/logs/postgres-logs?s=zapier),':1036C 'supabase.com/docs/guides/database/extensions/http)':1383C 'supabase.com/docs/guides/database/extensions/pg_cron)':1368C 'supabase.com/docs/guides/database/extensions/pg_net)':1378C 'supabase.com/docs/guides/database/webhooks)!':1344C 'supabase.com/partners/integrations/bracket).':1323C 'supabase.com/partners/integrations/n8n),':1281C 'supabase.com/partners/integrations/sequin_io)':1318C 'super':443C 'tabl':109C,130C,136C,168C,222C,227C,233C,317C,493C,581C,594C,612C,616C,626C,644C,882C,912C,971C,1078C,1120C 'tandem':1372C 'test':874C,1001C,1014C 'text':156C 'though':1181C 'time':298C 'togeth':1244C 'tool':10B 'trigger':239C,290C,685C,687C,822C,827C,834C,846C,932C,1211C,1331C 'true':652C 'tutori':376C,766C 'type':280C 'ultra':201C,894C 'unabl':303C,499C,1126C 'unfortun':295C 'updat':265C,335C,356C,1115C 'usag':601C 'use':126C,651C,875C,1137C,1207C 'user':107C,388C,398C,417C,422C,430C,439C,448C,471C,482C,532C,540C,562C,589C,757C,1065C,1083C,1180C 'usernam':719C 'valu':179C,967C,1236C 'varchar':145C 'view':1048C 'viola':995C 'visual':536C 'walkthrough':77C,133C,1390C 'want':414C,557C,986C,1060C 'webhook':1343C 'window':1185C 'within':340C,1141C,1155C 'work':97C 'workflow':8B,1308C 'workload':1279C 'worri':374C 'worst':404C 'worst-cas':403C 'write':300C 'www.postgresql.org':547C 'www.postgresql.org/docs/current/ddl-priv.html).':546C 'zap':665C,671C,824C,1007C 'zapier':1A,2B,21C,22C,48C,68C,100C,238C,246C,268C,285C,289C,301C,332C,350C,387C,400C,409C,429C,440C,449C,531C,561C,588C,606C,619C,635C,639C,650C,662C,667C,675C,690C,697C,708C,754C,813C,862C,955C,1003C,1026C,1046C,1049C,1064C,1081C,1084C,1088C,1099C,1134C,1152C,1162C,1191C,1234C,1238C,1285C,1396C,1412 'zapier.com':673C 'zapier.com/app/zaps):_':672C","video":null,"call_to_action_link":null,"featured":false},"overview":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nconst chCodeConfig = {\n  \"staticMediaQuery\": \"not screen, (max-width: 768px)\",\n  \"lineNumbers\": true,\n  \"showCopyButton\": true,\n  \"themeName\": \"supabase\"\n};\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    h1: \"h1\",\n    strong: \"strong\",\n    p: \"p\",\n    h2: \"h2\",\n    h3: \"h3\",\n    a: \"a\",\n    img: \"img\",\n    code: \"code\",\n    em: \"em\",\n    ul: \"ul\",\n    li: \"li\"\n  }, _provideComponents(), props.components), {CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsx(_components.h1, {\n      children: _jsx(_components.strong, {\n        children: \"Connecting Supabase to Zapier\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.strong, {\n        children: \"Zapier\"\n      }), \" is a powerhouse in automation that effortlessly connects different platforms — Supabase included. This post will show you how to connect your Supabase PostgreSQL instance with Zapier.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"It will emphasize security practices and highlight Supabase features that will help you manage your integration. Additionally, it addresses Zapier's limitations and suggests alternatives to overcome them.\"\n    }), \"\\n\", _jsx(_components.h1, {\n      children: _jsx(_components.strong, {\n        children: \"Walkthrough\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This guide will show you how to send emails when database events occur, but it can be generalized to work with any Zapier connection.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: _jsx(_components.strong, {\n        children: \"Database Set up\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Create a user_profile table:\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In your project’s \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard/project/_/sql/new\",\n        children: \"SQL Editor\"\n      }), \", execute the below query. This will be used as the example table for the walkthrough.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"create table\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" \\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"products\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t\\\"product_id\\\" \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"serial primary key\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t\\\"product_name\\\" \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"varchar\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"512\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"not null\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t\\\"price\\\" \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"decimal\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"10\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"2\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"not null\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t\\\"description\\\" \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"text\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t\\\"stock_quantity\\\" \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"INT DEFAULT \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"0\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \");\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Insert some items into your product table:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"insert into \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"products\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\tproduct_name,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\tprice,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\tdescription\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\tstock_quantity\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"values\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'Pokeball'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"4\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"99\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'Basic Poké Ball for catching Pokémon.'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"100\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"),\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'Great Ball'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"7\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"99\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'Better than a Poké Ball, higher catch rate.'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"75\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"),\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'Ultra Ball'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"12\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"99\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'High-performance ball with excellent catch rate.'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"10\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \");\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You should be able to see your newly created table in your \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard/project/_/editor\",\n        children: \"Dashboard’s Table Editor\"\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/first_image.png\",\n        alt: \"Dashboard Table Editor\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: _jsx(_components.strong, {\n        children: \"Zapier Triggers and Actions:\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Depending on your settings, Zapier will connect to your database every 2 to 15 minutes to observe changes. When information is added or updated in PostgreSQL, Zapier can initiate external events, like sending an email.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Here are the types of PostgreSQL events that Zapier can respond to:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/second_image.png\",\n        alt: \"Zapier trigger events\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Unfortunately, at the time of writing, Zapier is unable to respond to destructive events, such as deleting a row or dropping a table. Likewise, when an external event occurs, such as a change in a Google Spreadsheet, Zapier can only update, insert, or read rows within PostgreSQL.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/thrid_image.png?t=2024-02-16T06%3A13%3A24.240Z\",\n        alt: \"PostgreSQL responses to external events\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Zapier can essentially respond to any update or creation event, but it is incapable of observing delete or drop events in PostgreSQL. Don’t worry! This tutorial will offer alternatives to circumvent these limitations.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: _jsx(_components.strong, {\n        children: \"Creating a Custom Zapier User:\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.strong, {\n        children: \"Best practice\"\n      }), \" demands that you create a new PostgreSQL user for Zapier. In the worst-case scenario where your Zapier account gets compromised, you want to have user restrictions against the custom user to protect your data.\"]\n    }), \"\\n\", _jsxs(_components.h3, {\n      children: [\"Create a “zapier” User in the \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard/project/_/sql/new\",\n        children: \"SQL Editor\"\n      }), \":\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"create user \\\"zapier\\\" with a “super secret password”;\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"CREATE USER\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" \\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"zapier\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\" \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"WITH PASSWORD \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'\u003cnew password\u003e'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In the \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard/project/_/database/roles\",\n        children: \"Dashboard’s Database section under roles\"\n      }), \", you should be able to see your new user.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/fourth_image.png\",\n        alt: \"new role in Dashboard\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"All new database users are capable of seeing what database objects exist, such as tables and schemas, but they are unable to look at their data or definitions, nor are they able to call commands, such as SELECT or DELETE. You will need to explicitly grant the necessary privileges to your new “zapier” user. Here is a visual of all possible user privileges from the PostgreSQL \", _jsx(_components.a, {\n        href: \"https://www.postgresql.org/docs/current/ddl-priv.html\",\n        children: \"documentation\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/fifth_image.png?t=2024-02-16T06%3A19%3A08.575Z\",\n        alt: \"PostgreSQL database object priveleges\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You want to give your “zapier” user the bare minimum of necessary privileges. In this example, it will only need read access to the \", _jsx(_components.code, {\n        children: \"products\"\n      }), \" table.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Grant the Below Privileges to Your “zapier” User:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"-- optional if the table is in the “public” schema\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"grant\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" usage \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"on schema\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" public \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"to\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" zapier;\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"-- Grant SELECT privilege on the table\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"grant select on table\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" products \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"to\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" “zapier”;\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you enabled RLS for your table, you will also need to create policies for \", _jsx(_components.code, {\n        children: \"zapier\"\n      }), \".\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"create policy \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"zapier can read from products table\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"on \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"public\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"products\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"for \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"select\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"to \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"zapier\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"using\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\ttrue\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \");\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"With the following privileges set, you can connect to Zapier.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: _jsx(_components.strong, {\n        children: \"Configuring your Zap:\"\n      })\n    }), \"\\n\", _jsxs(_components.h3, {\n      children: [\"In Zapier, Create a New \", _jsxs(_components.em, {\n        children: [_jsx(_components.a, {\n          href: \"https://zapier.com/app/zaps\",\n          children: \"Zap\"\n        }), \":\"]\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/sixth_image.png?t=2024-02-16T06%3A22%3A15.178Z\",\n        alt: \"Zapier entry image\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Select PostgreSQL as Your Trigger:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/seventh_image.png?t=2024-02-16T06%3A23%3A22.512Z\",\n        alt: \"PostgreSQL trigger option in Zapier\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Connect PostgreSQL to Zapier:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You will be prompted to connect your PostgreSQL instance to Zapier. You will need the following credentials:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Host\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Port\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Database\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Schema\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Username\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Password\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"SSL Certificate\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You can find your host, port, and database in your \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard/project/_/settings/database\",\n        children: \"Dashboard’s Database Settings\"\n      }), \":\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/eighth_image.png?t=2024-02-16T06%3A26%3A23.126Z\",\n        alt: \"Screenshot 2024-02-11 at 11.28.07 PM.png\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You can enter your \", _jsx(_components.code, {\n        children: \"zapier.\u003cproject id\u003e\"\n      }), \" as your user and the password you created earlier in the tutorial.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Further down in the \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard/project/_/settings/database\",\n        children: \"Dashboard’s Database Settings\"\n      }), \", you will see the option to download an SSL certificate. This certificate is necessary to prevent snooping and man-in-the-middle attacks.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/ninth_image.png\",\n        alt: \"Dashboard's SSL certiciate\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"After connecting your instance, Zapier will allow you to define the event that triggers your Zap.\"\n    }), \"\\n\", _jsxs(_components.h3, {\n      children: [\"Choose Your Trigger Event (Select \", _jsx(_components.em, {\n        children: \"Custom Query):\"\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.a, {\n        href: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/tenth_image.png?t=2024-02-16T06%3A29%3A15.169Z\",\n        children: \"Possible PostgreSQL/Zapier trigger events\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"It will prompt you to insert a trigger query.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Insert the Following SQL:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"select * from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"products\\\" \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"where \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"stock_quantity\\\" \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003c= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"10\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/eleventh_image.png?t=2024-02-16T06%3A30%3A36.615Z\",\n        alt: \"Query in Zapier\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Finally, it will ask you to perform a test using a row it received from your table. The only row that should have returned should be for the “Ultra Ball” product.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Select a Product and go to the Next Step:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/twelfth_image.png?t=2024-02-16T06%3A38%3A14.842Z\",\n        alt: \"Selecting a prow from product's table\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You should be prompted to pick an application for an event to occur in after a trigger happens.\"\n    }), \"\\n\", _jsxs(_components.h3, {\n      children: [\"Select Gmail for the \", _jsx(_components.em, {\n        children: \"App\"\n      }), \" and \", _jsx(_components.em, {\n        children: \"Send Email\"\n      }), \" for the \", _jsx(_components.em, {\n        children: \"Event:\"\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/thirteenth_image.png\",\n        alt: \"Selecting Event\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Connect Your Gmail Account to Zapier:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Finally, it will ask you to compose an email with the values from your PostgreSQL table. I chose to make the below message, but you can format it however you want.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Format Your Message:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/fourthteenth_image.png?t=2024-02-16T06%3A45%3A56.062Z\",\n        alt: \"Formatting Message\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.strong, {\n        children: \"Viola!\"\n      }), \" You can now run a test in Zapier and publish your Zap. Depending on where you sent the test email, you may receive a message like the following:\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/fifteenth_image.png\",\n        alt: \"email from zapier\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In your \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard/project/wavwvitxkgeviubqugal/logs/postgres-logs?s=zapier\",\n        children: \"Dashboard’s Postgres Logs\"\n      }), \", you should be able to see when Zapier connects:\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zapier/sixteenth_image.png\",\n        alt: \"Viewing Zapier connection message\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: _jsx(_components.strong, {\n        children: \"Clean up\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In case you want to drop your “zapier” user, you can do so with the following queries:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"revoke select on table \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"products\\\" \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"zapier\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"drop user \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"zapier\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: _jsx(_components.strong, {\n        children: \"The Limitations of Zapier:\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Non-Destructive Observations and Actions Only:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"As discussed earlier, Zapier is only capable of monitoring non-destructive actions. It can observe when rows are updated, or when columns or tables are created, but it is unable to see when they’re deleted/dropped. Similarly, Zapier cannot be used to destroy objects within your database.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Limited Polling Frequency:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Another concern is how frequently Zapier makes observations. Within the advanced settings, you can configure Zapier to observe your database as frequently as every 2 minutes, or as infrequently as every 15. Many users, though, need a broader window of observation.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Max Observable Rows:\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://help.zapier.com/hc/en-us/articles/8496037193997-Common-Problems-with-PostgreSQL#some-new-rows-didn-t-trigger-my-zap-0-1\",\n        children: \"Zapier\"\n      }), \" \", _jsx(_components.a, {\n        href: \"https://help.zapier.com/hc/en-us/articles/8496037193997-Common-Problems-with-PostgreSQL#some-new-rows-didn-t-trigger-my-zap-0-1\",\n        children: \"is capable of observing only the 50 most recently created rows when using the \\\"New Row\\\" trigger.\"\n      }), \" So, if more than 50 rows are added in a polling interval, some data will be ignored.\"]\n    }), \"\\n\", _jsx(_components.h1, {\n      children: _jsx(_components.strong, {\n        children: \"Alternatives to Zapier:\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The value of Zapier is that it can bridge together practically any service, making your life as a developer relatively simple. Even with its limitations, it is a robust and reliable solution. However, other integrations are available that may be more suitable for your workload.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://supabase.com/partners/integrations/n8n\",\n        children: \"n8n\"\n      }), \", like Zapier, offers simple, no-code connectors (760 to be exact), but it is open source and can be self-hosted.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Other workflow automation alternatives that have partnered with Supabase include \", _jsx(_components.a, {\n        href: \"https://supabase.com/partners/integrations/sequin_io\",\n        children: \"Sequin.io\"\n      }), \" and \", _jsx(_components.a, {\n        href: \"https://supabase.com/partners/integrations/bracket\",\n        children: \"Brackets\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you just need to trigger outside services when database events occur, you could also leverage \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/database/webhooks\",\n        children: \"Supabase Webhooks\"\n      }), \"! If the requests need to be scheduled, then this could be implemented natively with only PostgreSQL through the \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/database/extensions/pg_cron\",\n        children: \"pg_cron extension\"\n      }), \" in tandem with either the \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/database/extensions/pg_net\",\n        children: \"pg_net\"\n      }), \" or \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/database/extensions/http\",\n        children: \"http\"\n      }), \" extensions.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: _jsx(_components.strong, {\n        children: \"Conclusion\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In this walkthrough, we learned how to connect Zapier and Supabase, but also about the alternative solutions that may meet our integration needs\"\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  chCodeConfig,\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{}}},"__N_SSG":true},"page":"/partners/integrations/[slug]","query":{"slug":"zapier"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>