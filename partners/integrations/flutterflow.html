<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../../rss.xml" data-next-head=""/><link rel="manifest" href="../../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:type" content="website" data-next-head=""/><meta property="og:image:alt" content="Supabase Og Image" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">FlutterFlow | Works With Supabase</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Create beautiful UI, generate clean code, and deploy to the app stores or web in one click. Fully extensible with custom code." data-next-head=""/><meta property="og:title" content="FlutterFlow | Works With Supabase" data-next-head=""/><meta property="og:description" content="Create beautiful UI, generate clean code, and deploy to the app stores or web in one click. Fully extensible with custom code." data-next-head=""/><meta property="og:url" content="https://supabase.com/partners/integrations/flutterflow" data-next-head=""/><meta property="og:image" content="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/flutterflow/flutterflow-1.jpeg" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2263-07e8ebb96cc2b014.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8658-639e1bb66549b52b.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/partners/integrations/%5Bslug%5D-cff8a94339d02c6e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqela6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R1eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R1eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R2eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R2eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R3eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R3eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav></div><main class="relative min-h-screen"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20"><div class="col-span-12 mx-auto mb-2 max-w-5xl space-y-10 lg:col-span-2"><a class="text-foreground hover:text-foreground-lighter flex cursor-pointer items-center transition-colors" href="../integrations.html"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left"><path d="m15 18-6-6 6-6"></path></svg>Back</a><div class="flex items-center space-x-4"><img alt="FlutterFlow" loading="lazy" width="56" height="56" decoding="async" data-nimg="1" class="bg-surface-200 flex-shrink-f0 h-14 w-14 rounded-full" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fflutterflow%2Fflutterflow-logo.jpeg&amp;w=64&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fflutterflow%2Fflutterflow-logo.jpeg&amp;w=128&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fflutterflow%2Fflutterflow-logo.jpeg&amp;w=128&amp;q=75"/><h1 class="h1" style="margin-bottom:0">FlutterFlow</h1></div><div class="bg-gradient-to-t from-background-alternative to-background border-b p-6 [&amp;_.swiper-container]:overflow-visible" style="margin-left:calc(50% - 50vw);margin-right:calc(50% - 50vw)"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0 !px-3 lg:!px-12 xl:!p-0 mx-auto max-w-5xl"><div class="swiper"><div class="swiper-wrapper"><div class="swiper-slide"><div class="relative block overflow-hidden rounded-md"><img alt="FlutterFlow" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto;background-size:cover;background-position:50% 50%;background-repeat:no-repeat;background-image:url(&quot;data:image/svg+xml;charset=utf-8,%3Csvg xmlns=&#x27;http://www.w3.org/2000/svg&#x27; viewBox=&#x27;0 0 1460 960&#x27;%3E%3Cfilter id=&#x27;b&#x27; color-interpolation-filters=&#x27;sRGB&#x27;%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3CfeColorMatrix values=&#x27;1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1&#x27; result=&#x27;s&#x27;/%3E%3CfeFlood x=&#x27;0&#x27; y=&#x27;0&#x27; width=&#x27;100%25&#x27; height=&#x27;100%25&#x27;/%3E%3CfeComposite operator=&#x27;out&#x27; in=&#x27;s&#x27;/%3E%3CfeComposite in2=&#x27;SourceGraphic&#x27;/%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3C/filter%3E%3Cimage width=&#x27;100%25&#x27; height=&#x27;100%25&#x27; x=&#x27;0&#x27; y=&#x27;0&#x27; preserveAspectRatio=&#x27;none&#x27; style=&#x27;filter: url(%23b);&#x27; href=&#x27;/images/blur.png&#x27;/%3E%3C/svg%3E&quot;)" sizes="100vw" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fflutterflow%2Fflutterflow-1.jpeg&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fflutterflow%2Fflutterflow-1.jpeg&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fflutterflow%2Fflutterflow-1.jpeg&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fflutterflow%2Fflutterflow-1.jpeg&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fflutterflow%2Fflutterflow-1.jpeg&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fflutterflow%2Fflutterflow-1.jpeg&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fflutterflow%2Fflutterflow-1.jpeg&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fflutterflow%2Fflutterflow-1.jpeg&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fflutterflow%2Fflutterflow-1.jpeg&amp;w=3840&amp;q=75"/></div></div><div class="swiper-slide"><div class="relative block overflow-hidden rounded-md"><img alt="FlutterFlow" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto;background-size:cover;background-position:50% 50%;background-repeat:no-repeat;background-image:url(&quot;data:image/svg+xml;charset=utf-8,%3Csvg xmlns=&#x27;http://www.w3.org/2000/svg&#x27; viewBox=&#x27;0 0 1460 960&#x27;%3E%3Cfilter id=&#x27;b&#x27; color-interpolation-filters=&#x27;sRGB&#x27;%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3CfeColorMatrix values=&#x27;1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1&#x27; result=&#x27;s&#x27;/%3E%3CfeFlood x=&#x27;0&#x27; y=&#x27;0&#x27; width=&#x27;100%25&#x27; height=&#x27;100%25&#x27;/%3E%3CfeComposite operator=&#x27;out&#x27; in=&#x27;s&#x27;/%3E%3CfeComposite in2=&#x27;SourceGraphic&#x27;/%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3C/filter%3E%3Cimage width=&#x27;100%25&#x27; height=&#x27;100%25&#x27; x=&#x27;0&#x27; y=&#x27;0&#x27; preserveAspectRatio=&#x27;none&#x27; style=&#x27;filter: url(%23b);&#x27; href=&#x27;/images/blur.png&#x27;/%3E%3C/svg%3E&quot;)" sizes="100vw" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fflutterflow%2Fflutterflow-2.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fflutterflow%2Fflutterflow-2.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fflutterflow%2Fflutterflow-2.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fflutterflow%2Fflutterflow-2.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fflutterflow%2Fflutterflow-2.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fflutterflow%2Fflutterflow-2.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fflutterflow%2Fflutterflow-2.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fflutterflow%2Fflutterflow-2.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fflutterflow%2Fflutterflow-2.png&amp;w=3840&amp;q=75"/></div></div></div></div></div></div><div class="grid gap-y-12 lg:grid-cols-8 lg:space-x-12"><div class="lg:col-span-5 overflow-hidden"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Overview</h2><div class="prose"><p>FlutterFlow is a low-code builder for developing native mobile applications using Flutter. You can use the simple drag-and-drop interface to build your app faster than traditional development.</p>
<h2>Documentation</h2>
<div role="alert" class="relative w-full text-sm rounded-lg p-4 [&amp;&gt;svg~*]:pl-10 [&amp;&gt;svg+div]:translate-y-[-3px] [&amp;&gt;svg]:absolute [&amp;&gt;svg]:left-4 [&amp;&gt;svg]:top-4 [&amp;&gt;svg]:w-[23px] [&amp;&gt;svg]:h-[23px] [&amp;&gt;svg]:p-1 [&amp;&gt;svg]:flex [&amp;&gt;svg]:rounded [&amp;&gt;svg]:text-warning-200 [&amp;&gt;svg]:bg-warning-600 mb-2 bg-alternative border border-default"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 22 20" class="w-6 h-6" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.15137 1.95117C9.30615 -0.0488281 12.1943 -0.0488281 13.3481 1.95117L20.7031 14.6992C21.8574 16.6992 20.4131 19.1992 18.104 19.1992H3.39502C1.08594 19.1992 -0.356933 16.6992 0.797364 14.6992L8.15137 1.95117ZM11.7666 16.0083C11.4971 16.2778 11.1313 16.4292 10.75 16.4292C10.3687 16.4292 10.0029 16.2778 9.7334 16.0083C9.46387 15.7388 9.3125 15.373 9.3125 14.9917C9.3125 14.9307 9.31641 14.8706 9.32373 14.811C9.33545 14.7197 9.35547 14.6304 9.38379 14.5439L9.41406 14.4609C9.48584 14.2803 9.59375 14.1147 9.7334 13.9751C10.0029 13.7056 10.3687 13.5542 10.75 13.5542C11.1313 13.5542 11.4971 13.7056 11.7666 13.9751C12.0361 14.2446 12.1875 14.6104 12.1875 14.9917C12.1875 15.373 12.0361 15.7388 11.7666 16.0083ZM10.75 4.69971C11.0317 4.69971 11.3022 4.81152 11.5015 5.01074C11.7007 5.20996 11.8125 5.48047 11.8125 5.76221V11.0747C11.8125 11.3564 11.7007 11.627 11.5015 11.8262C11.3022 12.0254 11.0317 12.1372 10.75 12.1372C10.4683 12.1372 10.1978 12.0254 9.99854 11.8262C9.79932 11.627 9.6875 11.3564 9.6875 11.0747V5.76221C9.6875 5.48047 9.79932 5.20996 9.99854 5.01074C10.1978 4.81152 10.4683 4.69971 10.75 4.69971Z"></path></svg><div class="text mt [&amp;_p]:mb-1.5 [&amp;_p]:mt-0 mt-0.5 [&amp;_p:last-child]:mb-0"><p>FlutterFlow and Supabase integration is currently in alpha, and supported features may be limited.</p></div></div>
<p><a href="https://flutterflow.io/">FlutterFlow</a> is a low-code builder for developing native mobile applications using Flutter. You can use the simple drag-and-drop interface to build your app faster than traditional development.</p>
<p>This guide gives you a quick overview of implementing basic CRUD operations using FlutterFlow and Supabase. You can find the full docs on FlutterFlow and Supabase <a href="https://docs.flutterflow.io/actions/actions/backend-database/supabase">here</a>.</p>
<div class="video-container"><iframe src="https://www.youtube-nocookie.com/embed/hw9Q-NjASbU" frameBorder="1" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen=""></iframe></div>
<h2>Step 1: Connect FlutterFlow to Supabase</h2>
<p>Before we dive into the code, this guide assumes that you have the following ready:</p>
<ul>
<li><a href="https://database.new/">Supabase</a> project created</li>
<li>Have setup tables in your Supabase project</li>
<li><a href="https://app.flutterflow.io/">FlutterFlow</a> project created</li>
</ul>
<p>You can then connect your Supabase project to your FlutterFlow project with the following steps:</p>
<ol>
<li>In your Supabase project, navigate to Project Settings &gt; API. Copy the Project URL.</li>
<li>Return to FlutterFlow, navigate to Settings and Integrations &gt; Integrations &gt; Supabase. Turn on the toggle (i.e., enable Supabase) and paste the API URL.</li>
<li>Similarly, from the Supabase API section, copy the anon key (under Project API keys) and paste it inside the FlutterFlow &gt; Settings and Integrations &gt; Integrations &gt; Supabase &gt; Anon Key.</li>
<li>Click on the Get Schema button. This will show the list of all tables with their schema (structure) created in Supabase.</li>
<li>(Optional) If you have defined an Array for any Column Data Type in Supabase, you must set its type here. To do so, tap the &quot;Click to set Array type&quot; and choose the right one.</li>
</ol>
<video width="99%" muted="" playsinline="" controls=""><source src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/videos/docs/guides/integrations/flutterflow/connect-flutterflow-to-supabase.mp4" type="video/mp4"/></video>
<h2>Step 2: Inserting rows</h2>
<p>Go to your project page on FlutterFlow and follow the steps below to define the Action to any widget.</p>
<ol>
<li>Select the Widget (e.g., Button) on which you want to define the action.</li>
<li>Select Actions from the Properties panel (the right menu), and click Open. This will open an Action flow Editor in a new popup window.<!-- -->
<ol>
<li>Click on + Add Action.</li>
<li>On the right side, search and select the Supabase &gt; Insert Row action.</li>
<li>Set the Table to your table name (e.g., assignments).</li>
<li>Under the Set Fields section, click on the + Add Field button.</li>
<li>Click on the Field name and scroll down to find the Value Source dropdown and change it to From Variable.</li>
<li>Click on UNSET and select Widget State &gt; Name of the TextField.</li>
<li>Similarly, add the field for the other UI elements.</li>
</ol>
</li>
</ol>
<video width="99%" muted="" playsinline="" controls=""><source src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/videos/docs/guides/integrations/flutterflow/insert.mp4" type="video/mp4"/></video>
<h2>Step 3: Selecting and displaying rows</h2>
<p>To query a Supabase table on a ListView:</p>
<ol>
<li>Select the ListView widget. Make sure you choose the ListView widget, not the ListTile.</li>
<li>Select Backend Query from the properties panel (the right menu), and click Add Backend Query.</li>
<li>Set the Query Type to Supabase Query.</li>
<li>Select your Table from the dropdown list</li>
<li>Set the Query Type to List of Rows.</li>
<li>Optional: If you want to display the limited result, say, for example, you have thousands of entries, but you want to display only 100, you can specify the limit.</li>
<li>Click Confirm.</li>
</ol>
<video width="99%" muted="" playsinline="" controls=""><source src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/videos/docs/guides/integrations/flutterflow/select.mp4" type="video/mp4"/></video>
<h2>Step 4: Updating rows</h2>
<p>Go to your project page on FlutterFlow and follow the steps below to define the Action to any widget.</p>
<ol>
<li>Select the Widget (e.g., Button) on which you want to define the action.</li>
<li>Select Actions from the Properties panel (the right menu), and click Open. This will open an Action flow Editor in a new popup window.<!-- -->
<ol>
<li>Click on + Add Action.</li>
<li>On the right side, search and select the Supabase &gt; Update Row action.</li>
<li>Set the Table to your table name (e.g., assignments).</li>
<li>Optional: If you want to get the rows after the update is finished, enable the Return Matching Rows option.</li>
<li>Now, you must set the row you want to update. Usually, this is done by finding a row in a table that matches the current row ID. To do so, click + Add Filter button inside the Matching Rows section.<!-- -->
<ol>
<li>Set the Field Name to the field that contains the IDs. Typically, this is the id column.</li>
<li>Set the Relation to Equal To because you want to find a row with the exact id.</li>
<li>Into the Value Source, you can select the From Variable and provide the id of the row for which you just updated values in the UI.</li>
</ol>
</li>
<li>Under the Set Fields section, click on the + Add Field button.</li>
<li>Click on the field name.</li>
<li>Scroll down to find the Value Source dropdown and change it to From Variable.</li>
<li>Click on UNSET and select Widget State &gt; Name of the TextField.</li>
<li>Similarly, add the field for the other UI elements.</li>
</ol>
</li>
</ol>
<h2>Step 5: Deleting rows</h2>
<p>Go to your project page on FlutterFlow and follow the steps below to define the Action to any widget.</p>
<ol>
<li>Select the Widget (e.g., Button) on which you want to define the action.</li>
<li>Select Actions from the Properties panel (the right menu), and click Open. This will open an Action flow Editor in a new popup window.<!-- -->
<ol>
<li>Click on + Add Action.</li>
<li>On the right side, search and select the Supabase -&gt; Delete Row action.</li>
<li>Set the Table to your table name (e.g., assignments).</li>
<li>Optional: Later, if you want to know which rows were deleted from a table, enable the Return Matching Rows option.</li>
<li>Now, you must set the row you want to delete. Usually, this is done by finding a row in a table that matches the current row ID. To do so, click + Add Filter button inside the Matching Rows section.<!-- -->
<ol>
<li>Set the Field Name to the field that contains the IDs. Typically, this is the id column.</li>
<li>Set the Relation to Equal To because you want to find a row with the exact id.</li>
<li>Into the Value Source, you can select the From Variable and provide the id of the row you want to delete.</li>
</ol>
</li>
</ol>
</li>
</ol>
<video width="99%" muted="" playsinline="" controls=""><source src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/videos/docs/guides/integrations/flutterflow/delete.mp4" type="video/mp4"/></video>
<h2>Resources</h2>
<p>You can find more detailed guides on FlutterFlow’s docs.</p>
<ul>
<li><a href="https://docs.flutterflow.io/actions/actions/backend-database/supabase">FlutterFlow Supabase available actions</a></li>
<li><a href="https://docs.flutterflow.io/data-and-backend/supabase/supabase-database/retrieving-data">Retrieving Data from Supabase on FlutterFlow</a></li>
<li><a href="https://docs.flutterflow.io/data-and-backend/supabase/supabase-database/adding-data">Adding data to Supabase DB from FlutterFlow</a></li>
</ul></div></div><div class="lg:col-span-3"><div class="sticky top-20 flex flex-col gap-4"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Details</h2><button class="w-full w-full"><div class="video-container overflow-hidden rounded hover:cursor-pointer"><div class=" absolute inset-0 z-10 text-white flex flex-col gap-3 items-center justify-center bg-alternative before:content[&#x27;&#x27;] before:absolute before:inset-0 before:bg-black before:opacity-30 before:-z-10 hover:before:opacity-50 before:transition-opacity "><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play w-5 h-5"><polygon points="6 3 20 12 6 21 6 3"></polygon></svg><p class="text-sm">Watch an introductory video</p></div><img alt="Video guide preview" loading="lazy" decoding="async" data-nimg="fill" class="absolute inset-0 object-cover blur-sm scale-105" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=3840&amp;q=75"/></div></button><div class="text-foreground divide-y"><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Developer</span><span class="text-foreground">FlutterFlow</span></div><div class="flex items-center justify-between py-2"><span class="text-lighter">Category</span><a class="text-brand hover:underline transition-colors" href="../integrations.html#low-code">Low-Code</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Website</span><a href="https://flutterflow.io/" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors">flutterflow.io</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Documentation</span><a href="https://docs.flutterflow.io/" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors"><span class="flex items-center space-x-1"><span>Learn</span><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></span></a></div></div><p class="text-foreground-light text-sm">Third-party integrations and docs are managed by Supabase partners.</p></div></div></div></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"partner":{"id":52,"slug":"flutterflow","type":"technology","category":"Low-Code","developer":"FlutterFlow","title":"FlutterFlow","description":"Create beautiful UI, generate clean code, and deploy to the app stores or web in one click. Fully extensible with custom code.","logo":"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/flutterflow/flutterflow-logo.jpeg","images":["https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/flutterflow/flutterflow-1.jpeg","https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/flutterflow/flutterflow-2.png"],"overview":"FlutterFlow is a low-code builder for developing native mobile applications using Flutter. You can use the simple drag-and-drop interface to build your app faster than traditional development.\n\n## Documentation\n\n\u003cAdmonition type=\"caution\"\u003e\n  FlutterFlow and Supabase integration is currently in alpha, and supported features may be limited.\n\u003c/Admonition\u003e\n\n[FlutterFlow](https://flutterflow.io/) is a low-code builder for developing native mobile applications using Flutter. You can use the simple drag-and-drop interface to build your app faster than traditional development.\n\nThis guide gives you a quick overview of implementing basic CRUD operations using FlutterFlow and Supabase. You can find the full docs on FlutterFlow and Supabase [here](https://docs.flutterflow.io/actions/actions/backend-database/supabase).\n\n\u003cdiv className=\"video-container\"\u003e\n  \u003ciframe\n    src=\"https://www.youtube-nocookie.com/embed/hw9Q-NjASbU\"\n    frameBorder=\"1\"\n    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n    allowFullScreen\n  \u003e\u003c/iframe\u003e\n\u003c/div\u003e\n\n## Step 1: Connect FlutterFlow to Supabase\n\nBefore we dive into the code, this guide assumes that you have the following ready:\n\n- [Supabase](https://database.new/) project created\n- Have setup tables in your Supabase project\n- [FlutterFlow](https://app.flutterflow.io/) project created\n\nYou can then connect your Supabase project to your FlutterFlow project with the following steps:\n\n1. In your Supabase project, navigate to Project Settings \u003e API. Copy the Project URL.\n2. Return to FlutterFlow, navigate to Settings and Integrations \u003e Integrations \u003e Supabase. Turn on the toggle (i.e., enable Supabase) and paste the API URL.\n3. Similarly, from the Supabase API section, copy the anon key (under Project API keys) and paste it inside the FlutterFlow \u003e Settings and Integrations \u003e Integrations \u003e Supabase \u003e Anon Key.\n4. Click on the Get Schema button. This will show the list of all tables with their schema (structure) created in Supabase.\n5. (Optional) If you have defined an Array for any Column Data Type in Supabase, you must set its type here. To do so, tap the \"Click to set Array type\" and choose the right one.\n\n\u003cvideo width=\"99%\" muted playsInline controls={true}\u003e\n  \u003csource\n    src=\"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/videos/docs/guides/integrations/flutterflow/connect-flutterflow-to-supabase.mp4\"\n    type=\"video/mp4\"\n  /\u003e\n\u003c/video\u003e\n\n## Step 2: Inserting rows\n\nGo to your project page on FlutterFlow and follow the steps below to define the Action to any widget.\n\n1. Select the Widget (e.g., Button) on which you want to define the action.\n2. Select Actions from the Properties panel (the right menu), and click Open. This will open an Action flow Editor in a new popup window.\n   1. Click on + Add Action.\n   2. On the right side, search and select the Supabase \u003e Insert Row action.\n   3. Set the Table to your table name (e.g., assignments).\n   4. Under the Set Fields section, click on the + Add Field button.\n   5. Click on the Field name and scroll down to find the Value Source dropdown and change it to From Variable.\n   6. Click on UNSET and select Widget State \u003e Name of the TextField.\n   7. Similarly, add the field for the other UI elements.\n\n\u003cvideo width=\"99%\" muted playsInline controls={true}\u003e\n  \u003csource\n    src=\"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/videos/docs/guides/integrations/flutterflow/insert.mp4\"\n    type=\"video/mp4\"\n  /\u003e\n\u003c/video\u003e\n\n## Step 3: Selecting and displaying rows\n\nTo query a Supabase table on a ListView:\n\n1. Select the ListView widget. Make sure you choose the ListView widget, not the ListTile.\n2. Select Backend Query from the properties panel (the right menu), and click Add Backend Query.\n3. Set the Query Type to Supabase Query.\n4. Select your Table from the dropdown list\n5. Set the Query Type to List of Rows.\n6. Optional: If you want to display the limited result, say, for example, you have thousands of entries, but you want to display only 100, you can specify the limit.\n7. Click Confirm.\n\n\u003cvideo width=\"99%\" muted playsInline controls={true}\u003e\n  \u003csource\n    src=\"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/videos/docs/guides/integrations/flutterflow/select.mp4\"\n    type=\"video/mp4\"\n  /\u003e\n\u003c/video\u003e\n\n## Step 4: Updating rows\n\nGo to your project page on FlutterFlow and follow the steps below to define the Action to any widget.\n\n1. Select the Widget (e.g., Button) on which you want to define the action.\n2. Select Actions from the Properties panel (the right menu), and click Open. This will open an Action flow Editor in a new popup window.\n   1. Click on + Add Action.\n   2. On the right side, search and select the Supabase \u003e Update Row action.\n   3. Set the Table to your table name (e.g., assignments).\n   4. Optional: If you want to get the rows after the update is finished, enable the Return Matching Rows option.\n   5. Now, you must set the row you want to update. Usually, this is done by finding a row in a table that matches the current row ID. To do so, click + Add Filter button inside the Matching Rows section.\n      1. Set the Field Name to the field that contains the IDs. Typically, this is the id column.\n      2. Set the Relation to Equal To because you want to find a row with the exact id.\n      3. Into the Value Source, you can select the From Variable and provide the id of the row for which you just updated values in the UI.\n   6. Under the Set Fields section, click on the + Add Field button.\n   7. Click on the field name.\n   8. Scroll down to find the Value Source dropdown and change it to From Variable.\n   9. Click on UNSET and select Widget State \u003e Name of the TextField.\n   10. Similarly, add the field for the other UI elements.\n\n## Step 5: Deleting rows\n\nGo to your project page on FlutterFlow and follow the steps below to define the Action to any widget.\n\n1. Select the Widget (e.g., Button) on which you want to define the action.\n2. Select Actions from the Properties panel (the right menu), and click Open. This will open an Action flow Editor in a new popup window.\n   1. Click on + Add Action.\n   2. On the right side, search and select the Supabase -\u003e Delete Row action.\n   3. Set the Table to your table name (e.g., assignments).\n   4. Optional: Later, if you want to know which rows were deleted from a table, enable the Return Matching Rows option.\n   5. Now, you must set the row you want to delete. Usually, this is done by finding a row in a table that matches the current row ID. To do so, click + Add Filter button inside the Matching Rows section.\n      1. Set the Field Name to the field that contains the IDs. Typically, this is the id column.\n      2. Set the Relation to Equal To because you want to find a row with the exact id.\n      3. Into the Value Source, you can select the From Variable and provide the id of the row you want to delete.\n\n\u003cvideo width=\"99%\" muted playsInline controls={true}\u003e\n  \u003csource\n    src=\"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/videos/docs/guides/integrations/flutterflow/delete.mp4\"\n    type=\"video/mp4\"\n  /\u003e\n\u003c/video\u003e\n\n## Resources\n\nYou can find more detailed guides on FlutterFlow’s docs.\n\n- [FlutterFlow Supabase available actions](https://docs.flutterflow.io/actions/actions/backend-database/supabase)\n- [Retrieving Data from Supabase on FlutterFlow](https://docs.flutterflow.io/data-and-backend/supabase/supabase-database/retrieving-data)\n- [Adding data to Supabase DB from FlutterFlow](https://docs.flutterflow.io/data-and-backend/supabase/supabase-database/adding-data)\n","website":"https://flutterflow.io/","docs":"https://docs.flutterflow.io/","contact":1,"approved":true,"created_at":"2022-12-16T11:46:02+00:00","tsv":"'/)':74C,160C,173C '/actions/actions/backend-database/supabase)':1084C '/actions/actions/backend-database/supabase).':135C '/data-and-backend/supabase/supabase-database/adding-data)':1103C '/data-and-backend/supabase/supabase-database/retrieving-data)':1093C '1':137C,191C,344C,383C,487C,606C,645C,733C,874C,913C,1002C '10':841C '100':567C '2':205C,322C,358C,388C,502C,620C,650C,751C,888C,918C,1020C '3':228C,401C,474C,518C,663C,769C,931C,1038C '4':256C,411C,526C,584C,673C,941C '5':278C,423C,534C,693C,852C,962C '6':444C,543C,796C '7':456C,573C,808C '8':814C '9':829C '99':316C,468C,578C,1062C 'action':340C,357C,360C,375C,387C,400C,602C,619C,622C,637C,649C,662C,870C,887C,890C,905C,917C,930C,1081C 'ad':1094C 'add':386C,420C,458C,515C,648C,725C,805C,843C,916C,994C 'alpha':64C 'anon':237C,254C 'api':200C,226C,233C,241C 'app':12B,51C,101C 'app.flutterflow.io':172C 'app.flutterflow.io/)':171C 'applic':35C,85C 'array':285C,307C 'assign':410C,672C,940C 'assum':150C 'avail':1080C 'backend':504C,516C 'basic':115C 'beauti':3B 'build':49C,99C 'builder':30C,80C 'button':262C,349C,422C,611C,727C,807C,879C,996C 'chang':439C,824C 'choos':310C,495C 'clean':6B 'click':18B,257C,304C,369C,384C,417C,424C,445C,514C,574C,631C,646C,724C,802C,809C,830C,899C,914C,993C 'code':7B,23B,29C,79C,147C,1106 'column':288C,750C,1019C 'confirm':575C 'connect':138C,179C 'contain':742C,1011C 'control':319C,471C,581C,1065C 'copi':201C,235C 'creat':2B,162C,175C,275C 'crud':116C 'current':62C,718C,987C 'custom':22B 'data':289C,1086C,1095C 'database.new':159C 'database.new/)':158C 'db':1098C 'defin':283C,338C,355C,600C,617C,868C,885C 'delet':853C,928C,952C,972C,1059C 'deploy':9B 'detail':1072C 'develop':32C,55C,82C,105C 'display':477C,549C,565C 'dive':144C 'doc':127C,1077C 'docs.flutterflow.io':134C,1083C,1092C,1102C 'docs.flutterflow.io/actions/actions/backend-database/supabase)':1082C 'docs.flutterflow.io/actions/actions/backend-database/supabase).':133C 'docs.flutterflow.io/data-and-backend/supabase/supabase-database/adding-data)':1101C 'docs.flutterflow.io/data-and-backend/supabase/supabase-database/retrieving-data)':1091C 'document':56C 'done':707C,976C 'drag':44C,94C 'drag-and-drop':43C,93C 'drop':46C,96C 'dropdown':437C,532C,822C 'e.g':348C,409C,610C,671C,878C,939C 'editor':377C,639C,907C 'element':465C,850C 'enabl':221C,687C,956C 'entri':560C 'equal':756C,1025C 'exact':767C,1036C 'exampl':555C 'extens':20B 'faster':52C,102C 'featur':67C 'field':415C,421C,427C,460C,736C,740C,800C,806C,812C,845C,1005C,1009C 'filter':726C,995C 'find':124C,433C,709C,762C,818C,978C,1031C,1070C 'finish':686C 'flow':376C,638C,906C 'flutter':37C,87C 'flutterflow':1A,24C,57C,71C,119C,129C,139C,170C,185C,208C,248C,331C,593C,861C,1075C,1078C,1090C,1100C,1107 'flutterflow.io':73C 'flutterflow.io/)':72C 'follow':155C,189C,333C,595C,863C 'full':126C 'fulli':19B 'generat':5B 'get':260C,679C 'give':108C 'go':325C,587C,855C 'guid':107C,149C,1073C 'i.e':220C 'id':720C,744C,749C,768C,783C,989C,1013C,1018C,1037C,1052C 'implement':114C 'insert':323C,398C 'insid':246C,728C,997C 'integr':60C,213C,214C,251C,252C 'interfac':47C,97C 'key':238C,242C,255C 'know':948C 'later':943C 'limit':70C,551C,572C 'list':267C,533C,540C 'listtil':501C 'listview':486C,490C,497C 'low':28C,78C,1105 'low-cod':27C,77C,1104 'make':492C 'match':690C,716C,730C,959C,985C,999C 'may':68C 'menu':367C,512C,629C,897C 'mobil':34C,84C 'must':294C,696C,965C 'mute':317C,469C,579C,1063C 'name':408C,428C,452C,670C,737C,813C,837C,938C,1006C 'nativ':33C,83C 'navig':196C,209C 'new':380C,642C,910C 'one':17B,313C 'open':370C,373C,632C,635C,900C,903C 'oper':117C 'option':279C,544C,674C,692C,942C,961C 'overview':112C 'page':329C,591C,859C 'panel':364C,509C,626C,894C 'past':224C,244C 'playsinlin':318C,470C,580C,1064C 'popup':381C,643C,911C 'project':161C,169C,174C,182C,186C,195C,198C,203C,240C,328C,590C,858C 'properti':363C,508C,625C,893C 'provid':781C,1050C 'queri':480C,505C,517C,521C,525C,537C 'quick':111C 'readi':156C 'relat':754C,1023C 'resourc':1067C 'result':552C 'retriev':1085C 'return':206C,689C,958C 'right':312C,366C,391C,511C,628C,653C,896C,921C 'row':324C,399C,478C,542C,586C,661C,681C,691C,699C,711C,719C,731C,764C,786C,854C,929C,950C,960C,968C,980C,988C,1000C,1033C,1055C 'say':553C 'schema':261C,273C 'scroll':430C,815C 'search':393C,655C,923C 'section':234C,416C,732C,801C,1001C 'select':345C,359C,395C,449C,475C,488C,503C,527C,607C,621C,657C,776C,834C,875C,889C,925C,1045C 'set':199C,211C,249C,295C,306C,402C,414C,519C,535C,664C,697C,734C,752C,799C,932C,966C,1003C,1021C 'setup':164C 'show':265C 'side':392C,654C,922C 'similar':229C,457C,842C 'simpl':42C,92C 'sourc':436C,773C,821C,1042C 'specifi':570C 'state':451C,836C 'step':136C,190C,321C,335C,473C,583C,597C,851C,865C 'store':13B 'structur':274C 'supabas':59C,121C,131C,141C,157C,168C,181C,194C,215C,222C,232C,253C,277C,292C,397C,482C,524C,659C,927C,1079C,1088C,1097C 'support':66C 'sure':493C 'tabl':165C,270C,404C,407C,483C,529C,666C,669C,714C,934C,937C,955C,983C 'tap':302C 'textfield':455C,840C 'thousand':558C 'toggl':219C 'tradit':54C,104C 'true':320C,472C,582C,1066C 'turn':216C 'type':290C,297C,308C,522C,538C 'typic':745C,1014C 'ui':4B,464C,795C,849C 'unset':447C,832C 'updat':585C,660C,684C,703C,791C 'url':204C,227C 'use':36C,40C,86C,90C,118C 'usual':704C,973C 'valu':435C,772C,792C,820C,1041C 'variabl':443C,779C,828C,1048C 'video':314C,466C,576C,1060C 'want':353C,547C,563C,615C,677C,701C,760C,883C,946C,970C,1029C,1057C 'web':15B 'widget':343C,347C,450C,491C,498C,605C,609C,835C,873C,877C 'width':315C,467C,577C,1061C 'window':382C,644C,912C","video":"hw9Q-NjASbU","call_to_action_link":null,"featured":false},"overview":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    h2: \"h2\",\n    a: \"a\",\n    ul: \"ul\",\n    li: \"li\",\n    ol: \"ol\"\n  }, _provideComponents(), props.components), {Admonition} = _components;\n  if (!Admonition) _missingMdxReference(\"Admonition\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(_components.p, {\n      children: \"FlutterFlow is a low-code builder for developing native mobile applications using Flutter. You can use the simple drag-and-drop interface to build your app faster than traditional development.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Documentation\"\n    }), \"\\n\", _jsx(Admonition, {\n      type: \"caution\",\n      children: _jsx(_components.p, {\n        children: \"FlutterFlow and Supabase integration is currently in alpha, and supported features may be limited.\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://flutterflow.io/\",\n        children: \"FlutterFlow\"\n      }), \" is a low-code builder for developing native mobile applications using Flutter. You can use the simple drag-and-drop interface to build your app faster than traditional development.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"This guide gives you a quick overview of implementing basic CRUD operations using FlutterFlow and Supabase. You can find the full docs on FlutterFlow and Supabase \", _jsx(_components.a, {\n        href: \"https://docs.flutterflow.io/actions/actions/backend-database/supabase\",\n        children: \"here\"\n      }), \".\"]\n    }), \"\\n\", _jsx(\"div\", {\n      className: \"video-container\",\n      children: _jsx(\"iframe\", {\n        src: \"https://www.youtube-nocookie.com/embed/hw9Q-NjASbU\",\n        frameBorder: \"1\",\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n        allowFullScreen: true\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 1: Connect FlutterFlow to Supabase\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Before we dive into the code, this guide assumes that you have the following ready:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://database.new/\",\n          children: \"Supabase\"\n        }), \" project created\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Have setup tables in your Supabase project\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://app.flutterflow.io/\",\n          children: \"FlutterFlow\"\n        }), \" project created\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You can then connect your Supabase project to your FlutterFlow project with the following steps:\"\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"In your Supabase project, navigate to Project Settings \u003e API. Copy the Project URL.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Return to FlutterFlow, navigate to Settings and Integrations \u003e Integrations \u003e Supabase. Turn on the toggle (i.e., enable Supabase) and paste the API URL.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Similarly, from the Supabase API section, copy the anon key (under Project API keys) and paste it inside the FlutterFlow \u003e Settings and Integrations \u003e Integrations \u003e Supabase \u003e Anon Key.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Click on the Get Schema button. This will show the list of all tables with their schema (structure) created in Supabase.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"(Optional) If you have defined an Array for any Column Data Type in Supabase, you must set its type here. To do so, tap the \\\"Click to set Array type\\\" and choose the right one.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(\"video\", {\n      width: \"99%\",\n      muted: true,\n      playsInline: true,\n      controls: true,\n      children: _jsx(\"source\", {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/videos/docs/guides/integrations/flutterflow/connect-flutterflow-to-supabase.mp4\",\n        type: \"video/mp4\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 2: Inserting rows\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Go to your project page on FlutterFlow and follow the steps below to define the Action to any widget.\"\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Select the Widget (e.g., Button) on which you want to define the action.\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Select Actions from the Properties panel (the right menu), and click Open. This will open an Action flow Editor in a new popup window.\", \"\\n\", _jsxs(_components.ol, {\n          children: [\"\\n\", _jsx(_components.li, {\n            children: \"Click on + Add Action.\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"On the right side, search and select the Supabase \u003e Insert Row action.\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"Set the Table to your table name (e.g., assignments).\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"Under the Set Fields section, click on the + Add Field button.\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"Click on the Field name and scroll down to find the Value Source dropdown and change it to From Variable.\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"Click on UNSET and select Widget State \u003e Name of the TextField.\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"Similarly, add the field for the other UI elements.\"\n          }), \"\\n\"]\n        }), \"\\n\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(\"video\", {\n      width: \"99%\",\n      muted: true,\n      playsInline: true,\n      controls: true,\n      children: _jsx(\"source\", {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/videos/docs/guides/integrations/flutterflow/insert.mp4\",\n        type: \"video/mp4\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 3: Selecting and displaying rows\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To query a Supabase table on a ListView:\"\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Select the ListView widget. Make sure you choose the ListView widget, not the ListTile.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Select Backend Query from the properties panel (the right menu), and click Add Backend Query.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Set the Query Type to Supabase Query.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Select your Table from the dropdown list\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Set the Query Type to List of Rows.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Optional: If you want to display the limited result, say, for example, you have thousands of entries, but you want to display only 100, you can specify the limit.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Click Confirm.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(\"video\", {\n      width: \"99%\",\n      muted: true,\n      playsInline: true,\n      controls: true,\n      children: _jsx(\"source\", {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/videos/docs/guides/integrations/flutterflow/select.mp4\",\n        type: \"video/mp4\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 4: Updating rows\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Go to your project page on FlutterFlow and follow the steps below to define the Action to any widget.\"\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Select the Widget (e.g., Button) on which you want to define the action.\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Select Actions from the Properties panel (the right menu), and click Open. This will open an Action flow Editor in a new popup window.\", \"\\n\", _jsxs(_components.ol, {\n          children: [\"\\n\", _jsx(_components.li, {\n            children: \"Click on + Add Action.\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"On the right side, search and select the Supabase \u003e Update Row action.\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"Set the Table to your table name (e.g., assignments).\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"Optional: If you want to get the rows after the update is finished, enable the Return Matching Rows option.\"\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"Now, you must set the row you want to update. Usually, this is done by finding a row in a table that matches the current row ID. To do so, click + Add Filter button inside the Matching Rows section.\", \"\\n\", _jsxs(_components.ol, {\n              children: [\"\\n\", _jsx(_components.li, {\n                children: \"Set the Field Name to the field that contains the IDs. Typically, this is the id column.\"\n              }), \"\\n\", _jsx(_components.li, {\n                children: \"Set the Relation to Equal To because you want to find a row with the exact id.\"\n              }), \"\\n\", _jsx(_components.li, {\n                children: \"Into the Value Source, you can select the From Variable and provide the id of the row for which you just updated values in the UI.\"\n              }), \"\\n\"]\n            }), \"\\n\"]\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"Under the Set Fields section, click on the + Add Field button.\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"Click on the field name.\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"Scroll down to find the Value Source dropdown and change it to From Variable.\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"Click on UNSET and select Widget State \u003e Name of the TextField.\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"Similarly, add the field for the other UI elements.\"\n          }), \"\\n\"]\n        }), \"\\n\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 5: Deleting rows\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Go to your project page on FlutterFlow and follow the steps below to define the Action to any widget.\"\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Select the Widget (e.g., Button) on which you want to define the action.\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Select Actions from the Properties panel (the right menu), and click Open. This will open an Action flow Editor in a new popup window.\", \"\\n\", _jsxs(_components.ol, {\n          children: [\"\\n\", _jsx(_components.li, {\n            children: \"Click on + Add Action.\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"On the right side, search and select the Supabase -\u003e Delete Row action.\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"Set the Table to your table name (e.g., assignments).\"\n          }), \"\\n\", _jsx(_components.li, {\n            children: \"Optional: Later, if you want to know which rows were deleted from a table, enable the Return Matching Rows option.\"\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"Now, you must set the row you want to delete. Usually, this is done by finding a row in a table that matches the current row ID. To do so, click + Add Filter button inside the Matching Rows section.\", \"\\n\", _jsxs(_components.ol, {\n              children: [\"\\n\", _jsx(_components.li, {\n                children: \"Set the Field Name to the field that contains the IDs. Typically, this is the id column.\"\n              }), \"\\n\", _jsx(_components.li, {\n                children: \"Set the Relation to Equal To because you want to find a row with the exact id.\"\n              }), \"\\n\", _jsx(_components.li, {\n                children: \"Into the Value Source, you can select the From Variable and provide the id of the row you want to delete.\"\n              }), \"\\n\"]\n            }), \"\\n\"]\n          }), \"\\n\"]\n        }), \"\\n\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(\"video\", {\n      width: \"99%\",\n      muted: true,\n      playsInline: true,\n      controls: true,\n      children: _jsx(\"source\", {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/videos/docs/guides/integrations/flutterflow/delete.mp4\",\n        type: \"video/mp4\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Resources\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You can find more detailed guides on FlutterFlow’s docs.\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://docs.flutterflow.io/actions/actions/backend-database/supabase\",\n          children: \"FlutterFlow Supabase available actions\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://docs.flutterflow.io/data-and-backend/supabase/supabase-database/retrieving-data\",\n          children: \"Retrieving Data from Supabase on FlutterFlow\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://docs.flutterflow.io/data-and-backend/supabase/supabase-database/adding-data\",\n          children: \"Adding data to Supabase DB from FlutterFlow\"\n        })\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{}}},"__N_SSG":true},"page":"/partners/integrations/[slug]","query":{"slug":"flutterflow"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"isExperimentalCompile":false,"gsp":true,"scriptLoader":[]}</script></body></html>