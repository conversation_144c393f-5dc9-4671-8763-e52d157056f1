<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../../rss.xml" data-next-head=""/><link rel="manifest" href="../../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:type" content="website" data-next-head=""/><meta property="og:image:alt" content="Supabase Og Image" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Directus | Works With Supabase</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Power any project with a modern, open source data platform." data-next-head=""/><meta property="og:title" content="Directus | Works With Supabase" data-next-head=""/><meta property="og:description" content="Power any project with a modern, open source data platform." data-next-head=""/><meta property="og:url" content="https://supabase.com/partners/integrations/directus" data-next-head=""/><meta property="og:image" content="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/directus/directus_og.png" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2263-07e8ebb96cc2b014.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8658-639e1bb66549b52b.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/partners/integrations/%5Bslug%5D-cff8a94339d02c6e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqela6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R1eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R1eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R2eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R2eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R3eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R3eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav></div><main class="relative min-h-screen"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20"><div class="col-span-12 mx-auto mb-2 max-w-5xl space-y-10 lg:col-span-2"><a class="text-foreground hover:text-foreground-lighter flex cursor-pointer items-center transition-colors" href="../integrations.html"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left"><path d="m15 18-6-6 6-6"></path></svg>Back</a><div class="flex items-center space-x-4"><img alt="Directus" loading="lazy" width="56" height="56" decoding="async" data-nimg="1" class="bg-surface-200 flex-shrink-f0 h-14 w-14 rounded-full" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdirectus%2Fdirectus_logo.jpeg&amp;w=64&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdirectus%2Fdirectus_logo.jpeg&amp;w=128&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdirectus%2Fdirectus_logo.jpeg&amp;w=128&amp;q=75"/><h1 class="h1" style="margin-bottom:0">Directus</h1></div><div class="bg-gradient-to-t from-background-alternative to-background border-b p-6 [&amp;_.swiper-container]:overflow-visible" style="margin-left:calc(50% - 50vw);margin-right:calc(50% - 50vw)"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0 !px-3 lg:!px-12 xl:!p-0 mx-auto max-w-5xl"><div class="swiper"><div class="swiper-wrapper"><div class="swiper-slide"><div class="relative block overflow-hidden rounded-md"><img alt="Directus" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto;background-size:cover;background-position:50% 50%;background-repeat:no-repeat;background-image:url(&quot;data:image/svg+xml;charset=utf-8,%3Csvg xmlns=&#x27;http://www.w3.org/2000/svg&#x27; viewBox=&#x27;0 0 1460 960&#x27;%3E%3Cfilter id=&#x27;b&#x27; color-interpolation-filters=&#x27;sRGB&#x27;%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3CfeColorMatrix values=&#x27;1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1&#x27; result=&#x27;s&#x27;/%3E%3CfeFlood x=&#x27;0&#x27; y=&#x27;0&#x27; width=&#x27;100%25&#x27; height=&#x27;100%25&#x27;/%3E%3CfeComposite operator=&#x27;out&#x27; in=&#x27;s&#x27;/%3E%3CfeComposite in2=&#x27;SourceGraphic&#x27;/%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3C/filter%3E%3Cimage width=&#x27;100%25&#x27; height=&#x27;100%25&#x27; x=&#x27;0&#x27; y=&#x27;0&#x27; preserveAspectRatio=&#x27;none&#x27; style=&#x27;filter: url(%23b);&#x27; href=&#x27;/images/blur.png&#x27;/%3E%3C/svg%3E&quot;)" sizes="100vw" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdirectus%2Fdirectus_og.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdirectus%2Fdirectus_og.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdirectus%2Fdirectus_og.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdirectus%2Fdirectus_og.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdirectus%2Fdirectus_og.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdirectus%2Fdirectus_og.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdirectus%2Fdirectus_og.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdirectus%2Fdirectus_og.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdirectus%2Fdirectus_og.png&amp;w=3840&amp;q=75"/></div></div></div></div></div></div><div class="grid gap-y-12 lg:grid-cols-8 lg:space-x-12"><div class="lg:col-span-5 overflow-hidden"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Overview</h2><div class="prose"><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p>Directus is an open-source data platform that layers on top of any SQL database, providing a powerful suite of tools. The Directus Engine provides dynamic REST and GraphQL APIs based on your schema, hooks and automation, authentication and access control, and file transformations. Directus Studio enables engineers and non-technical users alike to browse, manage, and visualize database content through a no-code app.</p>
<h2>Documentation</h2>
<p>In this guide, we will demonstrate how to create a new Supabase project, install a fresh instance of the Directus platform, then configure the two to work together seamlessly. If you&#x27;re unfamiliar with either of these systems, don&#x27;t worry! We&#x27;ll start off with an overview of each platform and explain how they complement each other, noting any overlap in capabilities.</p>
<h2>Introduction</h2>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/directus/documentation/supabase-20220608A.webp" alt="Supabase App"/></p>
<p><a href="../../index.html">Supabase</a> is an open-source Firebase alternative that provides a PostgreSQL database, storage, authentication, and a dynamic REST API based on your schema. While it is possible to self-host Supabase on your own infrastructure, this article will focus on Supabase Cloud&#x27;s Free plan, which is the fastest and easiest way to get started.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/directus/documentation/directus-20220608A.webp" alt="Directus App"/></p>
<p><a href="https://directus.io/">Directus</a> is an open-source data platform that layers on top of any SQL database, providing a powerful suite of tools. The Directus Engine provides dynamic REST and GraphQL APIs based on your schema, hooks and automation, authentication and access control, and file transformations. Directus Studio enables engineers and non-technical users alike to browse, manage, and visualize database content through a no-code app.</p>
<p>Supabase is a suite of open-source tools making Postgres databases, file storage, authentication, and edge functions more accessible to developers of all skill levels. Directus is also developer tooling and additionally provides a Data Studio that is safe and intuitive enough for anyone, including non-technical users, to use. This is the crucial bit that gives the two platforms such a strong “network effect.”</p>
<p>When these two systems are brought together, you get a scalable datastore, limitless connectivity options, and a no-code app that allows your technical and business teams to collaborate together efficiently.</p>
<p>The two platforms share an overlap of capabilities that deepens their integration and offers developers the freedom of choice across a broader spectrum of connectivity. Key areas of intersection include:</p>
<p>The ability to generate <em>powerful</em> APIs dynamically to connect data
User management and fine-grained access control
Digital asset storage and management.</p>
<p>More importantly, Directus and Supabase share a common vision for your data, making them quite symbiotic. Both solutions are completely open-source, with self-hosted and cloud deployment options available. They are unopinionated in their approach, with vendor-agnostic data storage, and they both focus on providing a polished developer experience along with comprehensive documentation.</p>
<p>By linking the Supabase database with your Directus Project, <em>you get a superset of data tools.</em> You&#x27;ll benefit from Supabase&#x27;s Postgres database and its <em>dev-centric</em> admin app with the raw power to run SQL queries, <strong><em>as well as</em></strong> the Directus no-code app, which enables intuitive permissions-based data access for the whole team.</p>
<p>Let&#x27;s dive into how we actually set up and link these two platforms to create a modern data stack powerhouse.</p>
<h2>Create a Supabase Project</h2>
<p>As mentioned, while you can <a href="https://supabase.com/docs/guides/getting-started/local-development">deploy Supabase locally</a>. For the purpose of this guide, we&#x27;ll use Supabase Cloud:</p>
<ol>
<li>Create a <strong>Supabase</strong> account by signing in with GitHub.</li>
<li>Give your organization a name (this can be changed later).</li>
<li>Click <strong>New Project</strong> and select your organization.</li>
<li>Follow the prompts, setting a project Name, Database Password, Region, and Pricing Plan, then click <strong>Create New Project</strong>.</li>
<li>After your project has been provisioned, navigate to <strong>Settings &gt; Database</strong> in the sidebar.</li>
<li>Scroll down to <strong>Connection Info</strong> and take note of your database&#x27;s <strong>Host</strong>, <strong>Database Name</strong>, <strong>Port</strong>, <strong>User</strong>, and <strong>Password</strong>. You will need to enter this during your Directus project setup.</li>
</ol>
<h2>Optional: Add PostGIS to Support Geometry and Mapping</h2>
<p>To take full advantage of the built-in geometry and mapping features Directus offers, we recommend enabling Geometric Data Support. To add PostGIS, follow these steps:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/directus/documentation/enable-PostGIS-20220608A.webp" alt="Enable PostGis"/></p>
<ol>
<li>From the sidebar, navigate to <strong>Database &gt; Extensions</strong>.</li>
<li>Use the search bar to look up <code>PostGIS</code>.</li>
<li>Toggle the PostGIS option to enable it.</li>
</ol>
<h2>Set up Directus</h2>
<p>At the time of writing this article, <a href="https://directus.cloud/">Directus Cloud</a> does not yet support hybrid deployments for connecting an external database. So, we&#x27;ll be deploying a self-hosted instance to connect with Supabase. To install a self-hosted instance of Directus that&#x27;s connected to our Supabase project, follow these steps:</p>
<ol>
<li>Run the following command in your terminal:</li>
</ol>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>npm init directus-project example-project</span></div></div><br/></code></div></div>
<ol start="2">
<li>Using the up/down arrow keys, select <code>Postgres</code> from the list:</li>
</ol>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>? Choose your database client Postgres</span></div></div><br/></code></div></div>
<ol start="3">
<li>Next, you will be prompted to input database credentials. Add in the Supabase Database Connection Info noted above as follows:</li>
</ol>
<ul>
<li><strong>Database Host</strong> – The IP address for your database.</li>
<li><strong>Port</strong> – Port number your database is running on.</li>
<li><strong>Database Name</strong> – Name of your existing database.</li>
<li><strong>Database User</strong> – Name of existing user in database.</li>
<li><strong>Database Password</strong> – Password to enter database.</li>
<li><strong>Enable SSL</strong> – Select Y for yes or N for no.</li>
<li><strong>Root</strong> – The root name.</li>
</ul>
<ol start="4">
<li>Now, simply set an email and password for your first Directus admin account. To be clear, this is Directus-specific, and is unrelated to your database user:</li>
</ol>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>Create your first admin user:</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>? Email: <EMAIL></span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>? Password: ********</span></div></div><br/></code></div></div>
<p>Once this is complete, you should see details about your new project:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>Your project has been created at &lt;file-path&gt;/example-project.</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>The configuration can be found in &lt;file-path&gt;/example-project/.env</span></div></div><br/></code></div></div>
<ol start="5">
<li>Lastly, navigate to your new project folder (in this case <code>example-project</code>) and start the platform:</li>
</ol>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>cd example-project</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>npx directus start</span></div></div><br/></code></div></div>
<p><strong>Please note:</strong> To prevent public accessibility when using the supabase-js library,turn on row level security (RLS) on all these tables inside of the Supabase Dashboard. By default when RLS is turned on these tables cannot be read from or written to with the supabase-js library.</p>
<p>That&#x27;s it! Your project is now up and running locally. You can access the Directus Studio in the browser via the URL displayed, and log in with the Directus admin credentials you entered above:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>✨ Server started at http://localhost:8055</span></div></div><br/></code></div></div>
<p>In a matter of minutes, we&#x27;ve created a flexible data backend, with access to an intuitive no-code app for managing and visualizing data along with a robust connectivity toolkit. This modern data stack is flexible and scalable enough to power any data-driven project… all you need to do is build the frontend!</p>
<h2>Next Steps</h2>
<p>From here, the sky&#x27;s the limit on what you can build. You&#x27;ll probably want to invite some new collaborators to your project and start architecting your data model.</p>
<p>Below are some additional resources to dive in and start exploring these two platforms:</p>
<p><strong>Directus</strong></p>
<ul>
<li>See the <a href="https://directus.io/guides/">Directus guides</a>.</li>
<li>Join the Directus community on <a href="https://directus.chat/">Discord</a>.</li>
<li>Check out the source code on the official <a href="https://github.com/directus/directus">Directus GitHub Repo</a>.</li>
</ul>
<p><strong>Supabase</strong></p>
<ul>
<li>Explore the <a href="../../docs.html">Supabase documentation</a></li>
<li>Join the Supabase community on <a href="https://discord.supabase.com/">Discord</a></li>
</ul></div></div><div class="lg:col-span-3"><div class="sticky top-20 flex flex-col gap-4"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Details</h2><div class="text-foreground divide-y"><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Developer</span><span class="text-foreground">Directus</span></div><div class="flex items-center justify-between py-2"><span class="text-lighter">Category</span><a class="text-brand hover:underline transition-colors" href="../integrations.html#data%20platform">Data Platform</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Website</span><a href="https://directus.io/" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors">directus.io</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Documentation</span><a href="https://directus.io/" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors"><span class="flex items-center space-x-1"><span>Learn</span><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></span></a></div></div><p class="text-foreground-light text-sm">Third-party integrations and docs are managed by Supabase partners.</p></div></div></div></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"partner":{"id":31,"slug":"directus","type":"technology","category":"Data Platform","developer":"Directus","title":"Directus","description":"Power any project with a modern, open source data platform.","logo":"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/directus/directus_logo.jpeg","images":["https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/directus/directus_og.png"],"overview":"Directus is an open-source data platform that layers on top of any SQL database, providing a powerful suite of tools. The Directus Engine provides dynamic REST and GraphQL APIs based on your schema, hooks and automation, authentication and access control, and file transformations. Directus Studio enables engineers and non-technical users alike to browse, manage, and visualize database content through a no-code app.\n\n## Documentation\n\nIn this guide, we will demonstrate how to create a new Supabase project, install a fresh instance of the Directus platform, then configure the two to work together seamlessly. If you're unfamiliar with either of these systems, don't worry! We'll start off with an overview of each platform and explain how they complement each other, noting any overlap in capabilities.\n\n## Introduction\n\n![Supabase App](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/directus/documentation/supabase-20220608A.webp)\n\n[Supabase](https://supabase.com/) is an open-source Firebase alternative that provides a PostgreSQL database, storage, authentication, and a dynamic REST API based on your schema. While it is possible to self-host Supabase on your own infrastructure, this article will focus on Supabase Cloud's Free plan, which is the fastest and easiest way to get started.\n\n![Directus App](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/directus/documentation/directus-20220608A.webp)\n\n[Directus](https://directus.io/) is an open-source data platform that layers on top of any SQL database, providing a powerful suite of tools. The Directus Engine provides dynamic REST and GraphQL APIs based on your schema, hooks and automation, authentication and access control, and file transformations. Directus Studio enables engineers and non-technical users alike to browse, manage, and visualize database content through a no-code app.\n\nSupabase is a suite of open-source tools making Postgres databases, file storage, authentication, and edge functions more accessible to developers of all skill levels. Directus is also developer tooling and additionally provides a Data Studio that is safe and intuitive enough for anyone, including non-technical users, to use. This is the crucial bit that gives the two platforms such a strong “network effect.”\n\nWhen these two systems are brought together, you get a scalable datastore, limitless connectivity options, and a no-code app that allows your technical and business teams to collaborate together efficiently.\n\nThe two platforms share an overlap of capabilities that deepens their integration and offers developers the freedom of choice across a broader spectrum of connectivity. Key areas of intersection include:\n\nThe ability to generate _powerful_ APIs dynamically to connect data\nUser management and fine-grained access control\nDigital asset storage and management.\n\nMore importantly, Directus and Supabase share a common vision for your data, making them quite symbiotic. Both solutions are completely open-source, with self-hosted and cloud deployment options available. They are unopinionated in their approach, with vendor-agnostic data storage, and they both focus on providing a polished developer experience along with comprehensive documentation.\n\nBy linking the Supabase database with your Directus Project, _you get a superset of data tools._ You'll benefit from Supabase's Postgres database and its _dev-centric_ admin app with the raw power to run SQL queries, **_as well as_** the Directus no-code app, which enables intuitive permissions-based data access for the whole team.\n\nLet's dive into how we actually set up and link these two platforms to create a modern data stack powerhouse.\n\n## Create a Supabase Project\n\nAs mentioned, while you can [deploy Supabase locally](/docs/guides/getting-started/local-development). For the purpose of this guide, we'll use Supabase Cloud:\n\n1. Create a **Supabase** account by signing in with GitHub.\n2. Give your organization a name (this can be changed later).\n3. Click **New Project** and select your organization.\n4. Follow the prompts, setting a project Name, Database Password, Region, and Pricing Plan, then click **Create New Project**.\n5. After your project has been provisioned, navigate to **Settings \u003e Database** in the sidebar.\n6. Scroll down to **Connection Info** and take note of your database's **Host**, **Database Name**, **Port**, **User**, and **Password**. You will need to enter this during your Directus project setup.\n\n## Optional: Add PostGIS to Support Geometry and Mapping\n\nTo take full advantage of the built-in geometry and mapping features Directus offers, we recommend enabling Geometric Data Support. To add PostGIS, follow these steps:\n\n![Enable PostGis](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/directus/documentation/enable-PostGIS-20220608A.webp)\n\n1. From the sidebar, navigate to **Database \u003e Extensions**.\n2. Use the search bar to look up `PostGIS`.\n3. Toggle the PostGIS option to enable it.\n\n## Set up Directus\n\nAt the time of writing this article, [Directus Cloud](https://directus.cloud/) does not yet support hybrid deployments for connecting an external database. So, we'll be deploying a self-hosted instance to connect with Supabase. To install a self-hosted instance of Directus that's connected to our Supabase project, follow these steps:\n\n1. Run the following command in your terminal:\n\n```bash\nnpm init directus-project example-project\n```\n\n2. Using the up/down arrow keys, select `Postgres` from the list:\n\n```bash\n? Choose your database client Postgres\n```\n\n3. Next, you will be prompted to input database credentials. Add in the Supabase Database Connection Info noted above as follows:\n\n- **Database Host** – The IP address for your database.\n- **Port** – Port number your database is running on.\n- **Database Name** – Name of your existing database.\n- **Database User** – Name of existing user in database.\n- **Database Password** – Password to enter database.\n- **Enable SSL** – Select Y for yes or N for no.\n- **Root** – The root name.\n\n4. Now, simply set an email and password for your first Directus admin account. To be clear, this is Directus-specific, and is unrelated to your database user:\n\n```bash\nCreate your first admin user:\n? Email: <EMAIL>\n? Password: ********\n```\n\nOnce this is complete, you should see details about your new project:\n\n```bash\nYour project has been created at \u003cfile-path\u003e/example-project.\nThe configuration can be found in \u003cfile-path\u003e/example-project/.env\n```\n\n5. Lastly, navigate to your new project folder (in this case `example-project`) and start the platform:\n\n```bash\ncd example-project\nnpx directus start\n```\n\n**Please note:** To prevent public accessibility when using the supabase-js library,turn on row level security (RLS) on all these tables inside of the Supabase Dashboard. By default when RLS is turned on these tables cannot be read from or written to with the supabase-js library.\n\nThat's it! Your project is now up and running locally. You can access the Directus Studio in the browser via the URL displayed, and log in with the Directus admin credentials you entered above:\n\n```bash\n✨ Server started at http://localhost:8055\n```\n\nIn a matter of minutes, we've created a flexible data backend, with access to an intuitive no-code app for managing and visualizing data along with a robust connectivity toolkit. This modern data stack is flexible and scalable enough to power any data-driven project… all you need to do is build the frontend!\n\n## Next Steps\n\nFrom here, the sky's the limit on what you can build. You'll probably want to invite some new collaborators to your project and start architecting your data model.\n\nBelow are some additional resources to dive in and start exploring these two platforms:\n\n**Directus**\n\n- See the [Directus guides](https://directus.io/guides/).\n- Join the Directus community on [Discord](https://directus.chat/).\n- Check out the source code on the official [Directus GitHub Repo](https://github.com/directus/directus).\n\n**Supabase**\n\n- Explore the [Supabase documentation](https://supabase.com/docs)\n- Join the Supabase community on [Discord](https://discord.supabase.com/)\n","website":"https://directus.io/","docs":"https://directus.io/","contact":58,"approved":true,"created_at":"2022-08-04T06:09:57+00:00","tsv":"'/)':153C,218C,773C,1256C '/).':1225C '/directus/directus).':1239C '/docs)':1247C '/docs/guides/getting-started/local-development':589C '/example-project':981C '/example-project/.env':988C '/guides/).':1216C '/storage/v1/object/public/images/integrations/directus/documentation/directus-20220608a.webp)':214C '/storage/v1/object/public/images/integrations/directus/documentation/enable-postgis-20220608a.webp)':733C '/storage/v1/object/public/images/integrations/directus/documentation/supabase-20220608a.webp)':149C '1':601C,734C,818C '2':611C,742C,835C '3':622C,751C,852C '4':630C,924C '5':649C,989C '6':663C '8055':1105C 'abil':416C 'access':52C,258C,305C,431C,551C,1020C,1078C,1119C 'account':605C,937C 'across':404C 'actual':562C 'add':695C,724C,862C 'addit':318C,1198C 'address':877C 'admin':525C,936C,957C,1095C '<EMAIL>':960C 'advantag':705C 'agnost':479C 'alik':66C,272C 'allow':375C 'along':492C,1132C 'also':314C 'altern':160C 'anyon':330C 'api':42C,172C,248C,420C 'app':79C,146C,211C,285C,373C,526C,543C,1126C 'approach':475C 'architect':1191C 'area':411C 'arrow':839C 'articl':191C,768C 'asset':434C 'authent':50C,167C,256C,300C 'autom':49C,255C 'avail':469C 'backend':1117C 'bar':746C 'base':43C,173C,249C,549C 'bash':826C,846C,953C,974C,1007C,1100C 'benefit':514C 'bit':342C 'broader':406C 'brought':358C 'brows':68C,274C 'browser':1084C 'build':1160C,1176C 'built':709C 'built-in':708C 'busi':379C 'cannot':1052C 'capabl':143C,392C 'case':999C 'cd':1008C 'centric':524C 'chang':620C 'check':1226C 'choic':403C 'choos':847C 'clear':940C 'click':623C,645C 'client':850C 'cloud':196C,466C,600C,770C 'code':78C,284C,372C,542C,1125C,1230C 'collabor':382C,1185C 'command':822C 'common':445C 'communiti':1220C,1251C 'complement':136C 'complet':457C,965C 'comprehens':494C 'configur':103C,983C 'connect':366C,409C,423C,667C,781C,796C,810C,867C,1136C 'content':73C,279C 'control':53C,259C,432C 'creat':89C,571C,577C,602C,646C,954C,979C,1113C 'credenti':861C,1096C 'crucial':341C 'dashboard':1042C 'data':10B,18C,224C,321C,424C,449C,480C,510C,550C,574C,721C,1116C,1131C,1140C,1151C,1193C,1257 'data-driven':1150C 'databas':27C,72C,165C,233C,278C,297C,500C,519C,638C,659C,674C,677C,740C,784C,849C,860C,866C,873C,880C,885C,889C,895C,896C,903C,904C,909C,951C 'datastor':364C 'deepen':394C 'default':1044C 'demonstr':86C 'deploy':467C,586C,779C,789C 'detail':969C 'dev':523C 'dev-centr':522C 'develop':307C,315C,399C,490C 'digit':433C 'directus':1A,12C,35C,57C,100C,210C,215C,241C,263C,312C,440C,503C,539C,691C,715C,761C,769C,807C,830C,935C,944C,1013C,1080C,1094C,1209C,1212C,1219C,1234C,1259 'directus-project':829C 'directus-specif':943C 'directus.chat':1224C 'directus.chat/).':1223C 'directus.cloud':772C 'directus.cloud/)':771C 'directus.io':217C,1215C 'directus.io/)':216C 'directus.io/guides/).':1214C 'discord':1222C,1253C 'discord.supabase.com':1255C 'discord.supabase.com/)':1254C 'display':1088C 'dive':558C,1201C 'document':80C,495C,1244C 'driven':1152C 'dynam':38C,170C,244C,421C 'easiest':205C 'edg':302C 'effect':352C 'effici':384C 'either':115C 'email':929C,959C 'enabl':59C,265C,545C,719C,729C,757C,910C 'engin':36C,60C,242C,266C 'enough':328C,1146C 'enter':687C,908C,1098C 'exampl':833C,1001C,1010C 'example-project':832C,1000C,1009C 'exist':894C,900C 'experi':491C 'explain':133C 'explor':1205C,1241C 'extens':741C 'extern':783C 'fastest':203C 'featur':714C 'file':55C,261C,298C 'fine':429C 'fine-grain':428C 'firebas':159C 'first':934C,956C 'flexibl':1115C,1143C 'focus':193C,485C 'folder':996C 'follow':631C,726C,815C,821C,872C 'found':986C 'free':198C 'freedom':401C 'fresh':96C 'frontend':1162C 'full':704C 'function':303C 'generat':418C 'geometr':720C 'geometri':699C,711C 'get':208C,361C,506C 'github':610C,1235C 'github.com':1238C 'github.com/directus/directus).':1237C 'give':344C,612C 'grain':430C 'graphql':41C,247C 'guid':83C,595C,1213C 'hook':47C,253C 'host':184C,464C,676C,793C,804C,874C 'hybrid':778C 'import':439C 'includ':331C,414C 'info':668C,868C 'infrastructur':189C 'init':828C 'input':859C 'insid':1038C 'instal':94C,800C 'instanc':97C,794C,805C 'integr':396C 'intersect':413C 'introduct':144C 'intuit':327C,546C,1122C 'invit':1182C 'ip':876C 'join':1217C,1248C 'js':1026C,1063C 'key':410C,840C 'last':990C 'later':621C 'layer':21C,227C 'let':556C 'level':311C,1031C 'librari':1027C,1064C 'limit':1171C 'limitless':365C 'link':497C,566C 'list':845C 'll':123C,513C,597C,787C,1178C 'local':588C,1075C 'localhost':1104C 'log':1090C 'look':748C 'make':295C,450C 'manag':69C,275C,426C,437C,1128C 'map':701C,713C 'matter':1108C 'mention':582C 'minut':1110C 'model':1194C 'modern':7B,573C,1139C 'n':917C 'name':616C,637C,678C,890C,891C,898C,923C 'navig':656C,738C,991C 'need':685C,1156C 'network':351C 'new':91C,624C,647C,972C,994C,1184C 'next':853C,1163C 'no-cod':76C,282C,370C,540C,1123C 'non':63C,269C,333C 'non-techn':62C,268C,332C 'note':139C,671C,869C,1016C 'npm':827C 'npx':1012C 'number':883C 'obuldanrptloktxcffvn.supabase.co':148C,213C,732C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/directus/documentation/directus-20220608a.webp)':212C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/directus/documentation/enable-postgis-20220608a.webp)':731C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/directus/documentation/supabase-20220608a.webp)':147C 'offer':398C,716C 'offici':1233C 'open':8B,16C,157C,222C,292C,459C 'open-sourc':15C,156C,221C,291C,458C 'option':367C,468C,694C,755C 'organ':614C,629C 'overlap':141C,390C 'overview':128C 'password':639C,682C,905C,906C,931C,961C 'permiss':548C 'permissions-bas':547C 'plan':199C,643C 'platform':11B,19C,101C,131C,225C,347C,387C,569C,1006C,1208C,1258 'pleas':1015C 'polish':489C 'port':679C,881C,882C 'possibl':180C 'postgi':696C,725C,730C,750C,754C 'postgr':296C,518C,842C,851C 'postgresql':164C 'power':2B,30C,236C,419C,530C,1148C 'powerhous':576C 'prevent':1018C 'price':642C 'probabl':1179C 'project':4B,93C,504C,580C,625C,636C,648C,652C,692C,814C,831C,834C,973C,976C,995C,1002C,1011C,1069C,1153C,1188C 'prompt':633C,857C 'provid':28C,37C,162C,234C,243C,319C,487C 'provis':655C 'public':1019C 'purpos':592C 'queri':534C 'quit':452C 'raw':529C 're':112C 'read':1054C 'recommend':718C 'region':640C 'repo':1236C 'resourc':1199C 'rest':39C,171C,245C 'rls':1033C,1046C 'robust':1135C 'root':920C,922C 'row':1030C 'run':532C,819C,887C,1074C 'safe':325C 'scalabl':363C,1145C 'schema':46C,176C,252C 'scroll':664C 'seamless':109C 'search':745C 'secur':1032C 'see':968C,1210C 'select':627C,841C,912C 'self':183C,463C,792C,803C 'self-host':182C,462C,791C,802C 'server':1101C 'set':563C,634C,658C,759C,927C 'setup':693C 'share':388C,443C 'sidebar':662C,737C 'sign':607C 'simpli':926C 'skill':310C 'sky':1168C 'solut':455C 'sourc':9B,17C,158C,223C,293C,460C,1229C 'specif':945C 'spectrum':407C 'sql':26C,232C,533C 'ssl':911C 'stack':575C,1141C 'start':124C,209C,1004C,1014C,1102C,1190C,1204C 'step':728C,817C,1164C 'storag':166C,299C,435C,481C 'strong':350C 'studio':58C,264C,322C,1081C 'suit':31C,237C,289C 'supabas':92C,145C,150C,185C,195C,286C,442C,499C,516C,579C,587C,599C,604C,798C,813C,865C,1025C,1041C,1062C,1240C,1243C,1250C 'supabase-j':1024C,1061C 'supabase.com':152C,1246C 'supabase.com/)':151C 'supabase.com/docs)':1245C 'superset':508C 'support':698C,722C,777C 'symbiot':453C 'system':118C,356C 'tabl':1037C,1051C 'take':670C,703C 'team':380C,555C 'technic':64C,270C,334C,377C 'termin':825C 'time':764C 'togeth':108C,359C,383C 'toggl':752C 'tool':33C,239C,294C,316C 'toolkit':1137C 'tools._':511C 'top':23C,229C 'transform':56C,262C 'turn':1028C,1048C 'two':105C,346C,355C,386C,568C,1207C 'unfamiliar':113C 'unopinion':472C 'unrel':948C 'up/down':838C 'url':1087C 'use':337C,598C,743C,836C,1022C 'user':65C,271C,335C,425C,680C,897C,901C,952C,958C 've':1112C 'vendor':478C 'vendor-agnost':477C 'via':1085C 'vision':446C 'visual':71C,277C,1130C 'want':1180C 'way':206C 'well':536C 'whole':554C 'work':107C 'worri':121C 'write':766C 'written':1057C 'y':913C 'yes':915C 'yet':776C","video":null,"call_to_action_link":null,"featured":false},"overview":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nconst chCodeConfig = {\n  \"staticMediaQuery\": \"not screen, (max-width: 768px)\",\n  \"lineNumbers\": true,\n  \"showCopyButton\": true,\n  \"themeName\": \"supabase\"\n};\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    h2: \"h2\",\n    img: \"img\",\n    a: \"a\",\n    em: \"em\",\n    strong: \"strong\",\n    ol: \"ol\",\n    li: \"li\",\n    code: \"code\",\n    ul: \"ul\"\n  }, _provideComponents(), props.components), {CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Directus is an open-source data platform that layers on top of any SQL database, providing a powerful suite of tools. The Directus Engine provides dynamic REST and GraphQL APIs based on your schema, hooks and automation, authentication and access control, and file transformations. Directus Studio enables engineers and non-technical users alike to browse, manage, and visualize database content through a no-code app.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Documentation\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In this guide, we will demonstrate how to create a new Supabase project, install a fresh instance of the Directus platform, then configure the two to work together seamlessly. If you're unfamiliar with either of these systems, don't worry! We'll start off with an overview of each platform and explain how they complement each other, noting any overlap in capabilities.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Introduction\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/directus/documentation/supabase-20220608A.webp\",\n        alt: \"Supabase App\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://supabase.com/\",\n        children: \"Supabase\"\n      }), \" is an open-source Firebase alternative that provides a PostgreSQL database, storage, authentication, and a dynamic REST API based on your schema. While it is possible to self-host Supabase on your own infrastructure, this article will focus on Supabase Cloud's Free plan, which is the fastest and easiest way to get started.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/directus/documentation/directus-20220608A.webp\",\n        alt: \"Directus App\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://directus.io/\",\n        children: \"Directus\"\n      }), \" is an open-source data platform that layers on top of any SQL database, providing a powerful suite of tools. The Directus Engine provides dynamic REST and GraphQL APIs based on your schema, hooks and automation, authentication and access control, and file transformations. Directus Studio enables engineers and non-technical users alike to browse, manage, and visualize database content through a no-code app.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Supabase is a suite of open-source tools making Postgres databases, file storage, authentication, and edge functions more accessible to developers of all skill levels. Directus is also developer tooling and additionally provides a Data Studio that is safe and intuitive enough for anyone, including non-technical users, to use. This is the crucial bit that gives the two platforms such a strong “network effect.”\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"When these two systems are brought together, you get a scalable datastore, limitless connectivity options, and a no-code app that allows your technical and business teams to collaborate together efficiently.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The two platforms share an overlap of capabilities that deepens their integration and offers developers the freedom of choice across a broader spectrum of connectivity. Key areas of intersection include:\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The ability to generate \", _jsx(_components.em, {\n        children: \"powerful\"\n      }), \" APIs dynamically to connect data\\nUser management and fine-grained access control\\nDigital asset storage and management.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"More importantly, Directus and Supabase share a common vision for your data, making them quite symbiotic. Both solutions are completely open-source, with self-hosted and cloud deployment options available. They are unopinionated in their approach, with vendor-agnostic data storage, and they both focus on providing a polished developer experience along with comprehensive documentation.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"By linking the Supabase database with your Directus Project, \", _jsx(_components.em, {\n        children: \"you get a superset of data tools.\"\n      }), \" You'll benefit from Supabase's Postgres database and its \", _jsx(_components.em, {\n        children: \"dev-centric\"\n      }), \" admin app with the raw power to run SQL queries, \", _jsx(_components.strong, {\n        children: _jsx(_components.em, {\n          children: \"as well as\"\n        })\n      }), \" the Directus no-code app, which enables intuitive permissions-based data access for the whole team.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Let's dive into how we actually set up and link these two platforms to create a modern data stack powerhouse.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Create a Supabase Project\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"As mentioned, while you can \", _jsx(_components.a, {\n        href: \"/docs/guides/getting-started/local-development\",\n        children: \"deploy Supabase locally\"\n      }), \". For the purpose of this guide, we'll use Supabase Cloud:\"]\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Create a \", _jsx(_components.strong, {\n          children: \"Supabase\"\n        }), \" account by signing in with GitHub.\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Give your organization a name (this can be changed later).\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Click \", _jsx(_components.strong, {\n          children: \"New Project\"\n        }), \" and select your organization.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Follow the prompts, setting a project Name, Database Password, Region, and Pricing Plan, then click \", _jsx(_components.strong, {\n          children: \"Create New Project\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"After your project has been provisioned, navigate to \", _jsx(_components.strong, {\n          children: \"Settings \u003e Database\"\n        }), \" in the sidebar.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Scroll down to \", _jsx(_components.strong, {\n          children: \"Connection Info\"\n        }), \" and take note of your database's \", _jsx(_components.strong, {\n          children: \"Host\"\n        }), \", \", _jsx(_components.strong, {\n          children: \"Database Name\"\n        }), \", \", _jsx(_components.strong, {\n          children: \"Port\"\n        }), \", \", _jsx(_components.strong, {\n          children: \"User\"\n        }), \", and \", _jsx(_components.strong, {\n          children: \"Password\"\n        }), \". You will need to enter this during your Directus project setup.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Optional: Add PostGIS to Support Geometry and Mapping\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To take full advantage of the built-in geometry and mapping features Directus offers, we recommend enabling Geometric Data Support. To add PostGIS, follow these steps:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/directus/documentation/enable-PostGIS-20220608A.webp\",\n        alt: \"Enable PostGis\"\n      })\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"From the sidebar, navigate to \", _jsx(_components.strong, {\n          children: \"Database \u003e Extensions\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Use the search bar to look up \", _jsx(_components.code, {\n          children: \"PostGIS\"\n        }), \".\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Toggle the PostGIS option to enable it.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Set up Directus\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"At the time of writing this article, \", _jsx(_components.a, {\n        href: \"https://directus.cloud/\",\n        children: \"Directus Cloud\"\n      }), \" does not yet support hybrid deployments for connecting an external database. So, we'll be deploying a self-hosted instance to connect with Supabase. To install a self-hosted instance of Directus that's connected to our Supabase project, follow these steps:\"]\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Run the following command in your terminal:\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"npm \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"init directus-project example-project\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.ol, {\n      start: \"2\",\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Using the up/down arrow keys, select \", _jsx(_components.code, {\n          children: \"Postgres\"\n        }), \" from the list:\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"?\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" Choose your database client Postgres\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.ol, {\n      start: \"3\",\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Next, you will be prompted to input database credentials. Add in the Supabase Database Connection Info noted above as follows:\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Database Host\"\n        }), \" – The IP address for your database.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Port\"\n        }), \" – Port number your database is running on.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Database Name\"\n        }), \" – Name of your existing database.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Database User\"\n        }), \" – Name of existing user in database.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Database Password\"\n        }), \" – Password to enter database.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Enable SSL\"\n        }), \" – Select Y for yes or N for no.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Root\"\n        }), \" – The root name.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.ol, {\n      start: \"4\",\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Now, simply set an email and password for your first Directus admin account. To be clear, this is Directus-specific, and is unrelated to your database user:\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"Create \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"your first admin user:\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"?\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" Email: <EMAIL>\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"?\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" Password: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"********\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Once this is complete, you should see details about your new project:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"Your \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"project has been created at \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"file-pat\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"h\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"/example-project.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"The \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"configuration can be found in \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"file-pat\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"h\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"/example-project/.env\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.ol, {\n      start: \"5\",\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Lastly, navigate to your new project folder (in this case \", _jsx(_components.code, {\n          children: \"example-project\"\n        }), \") and start the platform:\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"cd \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"example-project\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"npx \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"directus start\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.strong, {\n        children: \"Please note:\"\n      }), \" To prevent public accessibility when using the supabase-js library,turn on row level security (RLS) on all these tables inside of the Supabase Dashboard. By default when RLS is turned on these tables cannot be read from or written to with the supabase-js library.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"That's it! Your project is now up and running locally. You can access the Directus Studio in the browser via the URL displayed, and log in with the Directus admin credentials you entered above:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"✨ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"Server started at http://localhost:8055\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In a matter of minutes, we've created a flexible data backend, with access to an intuitive no-code app for managing and visualizing data along with a robust connectivity toolkit. This modern data stack is flexible and scalable enough to power any data-driven project… all you need to do is build the frontend!\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Next Steps\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"From here, the sky's the limit on what you can build. You'll probably want to invite some new collaborators to your project and start architecting your data model.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Below are some additional resources to dive in and start exploring these two platforms:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.strong, {\n        children: \"Directus\"\n      })\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"See the \", _jsx(_components.a, {\n          href: \"https://directus.io/guides/\",\n          children: \"Directus guides\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Join the Directus community on \", _jsx(_components.a, {\n          href: \"https://directus.chat/\",\n          children: \"Discord\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Check out the source code on the official \", _jsx(_components.a, {\n          href: \"https://github.com/directus/directus\",\n          children: \"Directus GitHub Repo\"\n        }), \".\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.strong, {\n        children: \"Supabase\"\n      })\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Explore the \", _jsx(_components.a, {\n          href: \"https://supabase.com/docs\",\n          children: \"Supabase documentation\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Join the Supabase community on \", _jsx(_components.a, {\n          href: \"https://discord.supabase.com/\",\n          children: \"Discord\"\n        })]\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  chCodeConfig,\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{}}},"__N_SSG":true},"page":"/partners/integrations/[slug]","query":{"slug":"directus"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"isExperimentalCompile":false,"gsp":true,"scriptLoader":[]}</script></body></html>