<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../../rss.xml" data-next-head=""/><link rel="manifest" href="../../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:type" content="website" data-next-head=""/><meta property="og:image:alt" content="Supabase Og Image" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Sequin | Works With Supabase</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Sequin adds async Postgres triggers to Supabase." data-next-head=""/><meta property="og:title" content="Sequin | Works With Supabase" data-next-head=""/><meta property="og:description" content="Sequin adds async Postgres triggers to Supabase." data-next-head=""/><meta property="og:url" content="https://supabase.com/partners/integrations/sequin_io" data-next-head=""/><meta property="og:image" content="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/sequin_io/sequin-stream-1.png" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2263-07e8ebb96cc2b014.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8658-639e1bb66549b52b.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/partners/integrations/%5Bslug%5D-cff8a94339d02c6e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqela6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R1eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R1eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R2eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R2eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R3eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R3eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav></div><main class="relative min-h-screen"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20"><div class="col-span-12 mx-auto mb-2 max-w-5xl space-y-10 lg:col-span-2"><a class="text-foreground hover:text-foreground-lighter flex cursor-pointer items-center transition-colors" href="../integrations.html"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left"><path d="m15 18-6-6 6-6"></path></svg>Back</a><div class="flex items-center space-x-4"><img alt="Sequin" loading="lazy" width="56" height="56" decoding="async" data-nimg="1" class="bg-surface-200 flex-shrink-f0 h-14 w-14 rounded-full" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin_logo.png&amp;w=64&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin_logo.png&amp;w=128&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin_logo.png&amp;w=128&amp;q=75"/><h1 class="h1" style="margin-bottom:0">Sequin</h1></div><div class="bg-gradient-to-t from-background-alternative to-background border-b p-6 [&amp;_.swiper-container]:overflow-visible" style="margin-left:calc(50% - 50vw);margin-right:calc(50% - 50vw)"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0 !px-3 lg:!px-12 xl:!p-0 mx-auto max-w-5xl"><div class="swiper"><div class="swiper-wrapper"><div class="swiper-slide"><div class="relative block overflow-hidden rounded-md"><img alt="Sequin" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto;background-size:cover;background-position:50% 50%;background-repeat:no-repeat;background-image:url(&quot;data:image/svg+xml;charset=utf-8,%3Csvg xmlns=&#x27;http://www.w3.org/2000/svg&#x27; viewBox=&#x27;0 0 1460 960&#x27;%3E%3Cfilter id=&#x27;b&#x27; color-interpolation-filters=&#x27;sRGB&#x27;%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3CfeColorMatrix values=&#x27;1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1&#x27; result=&#x27;s&#x27;/%3E%3CfeFlood x=&#x27;0&#x27; y=&#x27;0&#x27; width=&#x27;100%25&#x27; height=&#x27;100%25&#x27;/%3E%3CfeComposite operator=&#x27;out&#x27; in=&#x27;s&#x27;/%3E%3CfeComposite in2=&#x27;SourceGraphic&#x27;/%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3C/filter%3E%3Cimage width=&#x27;100%25&#x27; height=&#x27;100%25&#x27; x=&#x27;0&#x27; y=&#x27;0&#x27; preserveAspectRatio=&#x27;none&#x27; style=&#x27;filter: url(%23b);&#x27; href=&#x27;/images/blur.png&#x27;/%3E%3C/svg%3E&quot;)" sizes="100vw" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-1.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-1.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-1.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-1.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-1.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-1.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-1.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-1.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-1.png&amp;w=3840&amp;q=75"/></div></div><div class="swiper-slide"><div class="relative block overflow-hidden rounded-md"><img alt="Sequin" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto;background-size:cover;background-position:50% 50%;background-repeat:no-repeat;background-image:url(&quot;data:image/svg+xml;charset=utf-8,%3Csvg xmlns=&#x27;http://www.w3.org/2000/svg&#x27; viewBox=&#x27;0 0 1460 960&#x27;%3E%3Cfilter id=&#x27;b&#x27; color-interpolation-filters=&#x27;sRGB&#x27;%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3CfeColorMatrix values=&#x27;1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1&#x27; result=&#x27;s&#x27;/%3E%3CfeFlood x=&#x27;0&#x27; y=&#x27;0&#x27; width=&#x27;100%25&#x27; height=&#x27;100%25&#x27;/%3E%3CfeComposite operator=&#x27;out&#x27; in=&#x27;s&#x27;/%3E%3CfeComposite in2=&#x27;SourceGraphic&#x27;/%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3C/filter%3E%3Cimage width=&#x27;100%25&#x27; height=&#x27;100%25&#x27; x=&#x27;0&#x27; y=&#x27;0&#x27; preserveAspectRatio=&#x27;none&#x27; style=&#x27;filter: url(%23b);&#x27; href=&#x27;/images/blur.png&#x27;/%3E%3C/svg%3E&quot;)" sizes="100vw" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-2.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-2.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-2.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-2.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-2.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-2.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-2.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-2.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-2.png&amp;w=3840&amp;q=75"/></div></div><div class="swiper-slide"><div class="relative block overflow-hidden rounded-md"><img alt="Sequin" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto;background-size:cover;background-position:50% 50%;background-repeat:no-repeat;background-image:url(&quot;data:image/svg+xml;charset=utf-8,%3Csvg xmlns=&#x27;http://www.w3.org/2000/svg&#x27; viewBox=&#x27;0 0 1460 960&#x27;%3E%3Cfilter id=&#x27;b&#x27; color-interpolation-filters=&#x27;sRGB&#x27;%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3CfeColorMatrix values=&#x27;1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1&#x27; result=&#x27;s&#x27;/%3E%3CfeFlood x=&#x27;0&#x27; y=&#x27;0&#x27; width=&#x27;100%25&#x27; height=&#x27;100%25&#x27;/%3E%3CfeComposite operator=&#x27;out&#x27; in=&#x27;s&#x27;/%3E%3CfeComposite in2=&#x27;SourceGraphic&#x27;/%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3C/filter%3E%3Cimage width=&#x27;100%25&#x27; height=&#x27;100%25&#x27; x=&#x27;0&#x27; y=&#x27;0&#x27; preserveAspectRatio=&#x27;none&#x27; style=&#x27;filter: url(%23b);&#x27; href=&#x27;/images/blur.png&#x27;/%3E%3C/svg%3E&quot;)" sizes="100vw" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-3.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-3.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-3.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-3.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-3.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-3.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-3.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-3.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-3.png&amp;w=3840&amp;q=75"/></div></div><div class="swiper-slide"><div class="relative block overflow-hidden rounded-md"><img alt="Sequin" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto;background-size:cover;background-position:50% 50%;background-repeat:no-repeat;background-image:url(&quot;data:image/svg+xml;charset=utf-8,%3Csvg xmlns=&#x27;http://www.w3.org/2000/svg&#x27; viewBox=&#x27;0 0 1460 960&#x27;%3E%3Cfilter id=&#x27;b&#x27; color-interpolation-filters=&#x27;sRGB&#x27;%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3CfeColorMatrix values=&#x27;1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1&#x27; result=&#x27;s&#x27;/%3E%3CfeFlood x=&#x27;0&#x27; y=&#x27;0&#x27; width=&#x27;100%25&#x27; height=&#x27;100%25&#x27;/%3E%3CfeComposite operator=&#x27;out&#x27; in=&#x27;s&#x27;/%3E%3CfeComposite in2=&#x27;SourceGraphic&#x27;/%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3C/filter%3E%3Cimage width=&#x27;100%25&#x27; height=&#x27;100%25&#x27; x=&#x27;0&#x27; y=&#x27;0&#x27; preserveAspectRatio=&#x27;none&#x27; style=&#x27;filter: url(%23b);&#x27; href=&#x27;/images/blur.png&#x27;/%3E%3C/svg%3E&quot;)" sizes="100vw" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-4.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-4.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-4.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-4.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-4.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-4.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-4.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-4.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsequin_io%2Fsequin-stream-4.png&amp;w=3840&amp;q=75"/></div></div></div></div></div></div><div class="grid gap-y-12 lg:grid-cols-8 lg:space-x-12"><div class="lg:col-span-5 overflow-hidden"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Overview</h2><div class="prose"><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p>Sequin sends your Supabase rows and changes anywhere you want. It’s designed to never miss an <code>insert</code>, <code>update</code>, or <code>delete</code> and provide exactly-once process for all changes.</p>
<p>You can receive changes via HTTP push (webhooks) or pull (think SQS). You can easily send changes to your application, another service (i.e. trigger.dev, resend, ingest), or trigger a Supabase edge function.</p>
<p>At some point, almost every Supabase app needs to trigger side effects. Think of sending a new user a welcome email or fanning out Jobs when an order arrives. Sequin aims to give you the tools to make that happen.</p>
<h2>Killer features</h2>
<ul>
<li><strong>Never miss a message:</strong> Sequin persists messages until they are acknowledged (i.e. exactly once processing guarantees).</li>
<li><strong>SQL-based routing:</strong> Filter and route messages using SQL <code>where</code> conditions</li>
<li><strong>Replays:</strong> Rewind to any row on your table. Republish messages that match a SQL query.</li>
<li><strong>Backfills:</strong> Queue up records from this moment forward or the entire table.</li>
<li><strong>Observability and debugging:</strong> Sequin has a web console that adds the Supabase queue tooling you’ve been looking for.</li>
<li><strong>Skip the PL/pgsql:</strong> Define business logic in the language of your choice and in your application.</li>
<li><strong>Transforms:</strong> Transform message payloads with Lua functions.</li>
</ul>
<h2>Documentation</h2>
<p>This guide steps you through connecting Sequin to your Supabase database to send database changes anywhere.</p>
<h3>1. Connect to Sequin with a direct connection</h3>
<p>Sequin uses the Write Ahead Log (WAL) to capture changes from your Supabase database. Supabase&#x27;s connection pooling does not support the WAL, so you need to connect to your Supabase database using a direct connection.</p>
<p>Here is how to set up a direct connection in Supabase:</p>
<ol>
<li>Login to Supabase and navigate to the database settings page (<strong>Settings &gt; Configurations &gt; Database</strong>).</li>
<li>Disable the <strong>Use connection pooling</strong> option to show your direct connection credentials:</li>
<li>In Sequin, enter the direct connection details in the <strong>Database connection</strong> page, including the <strong>Host</strong>, <strong>Port</strong>, <strong>Database</strong>, <strong>User</strong>, and <strong>Password</strong> for your Supabase database. Turn <strong>SSL</strong> on.</li>
</ol>
<p>✓ Sequin will automatically detect that you&#x27;re using a Supabase database and check that your connection is configured correctly.</p>
<h3>2. Create a replication slot in Supabase</h3>
<p>With your Supabase database connected to Sequin, you&#x27;ll now create the replication slot that Sequin will use to detect changes in your database.</p>
<ol>
<li>
<p>In the Supabase SQL editor, run the following SQL command to create a replication slot:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>select pg_create_logical_replication_slot(&#x27;sequin_slot&#x27;, &#x27;pgoutput&#x27;);</span></div></div><br/></code></div></div>
</li>
</ol>
<p>This creates a replication slot named <code>sequin_slot</code>.</p>
<ol start="2">
<li>
<p>Next, you&#x27;ll create a publication to indicate which tables will publish changes to the replication slot.</p>
<p>In the SQL editor, run the following SQL command to create a publication:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>create publication sequin_pub for table table1, table2, table3;</span></div></div><br/></code></div></div>
<p>If you want to publish changes from all the tables in your database, you can also use the <code>for all tables</code> option:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>create publication sequin_pub for all tables;</span></div></div><br/></code></div></div>
</li>
<li>
<p>Back in the Sequin Console, enter the name of the replication slot (e.g. <code>sequin_slot</code>) and publication (e.g. <code>sequin_pub</code>) you just created. Then, name your database and click <strong>Create Database</strong>.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/sequin_io/sequin-stream-successful-database-connection.png" alt="Supabase connected to Sequin"/></p>
</li>
</ol>
<p>✓ Sequin will now capture rows and changes from your Supabase database.</p>
<h3>3. Add a consumer in Sequin</h3>
<p>Sequin and Supabase are now working together. Every change in your database will be captured and can be sent to your application, another service, or trigger a Supabase edge function.</p>
<p>You’ll add a consumer in Sequin to define what changes you want to process and where you want to send them.</p>
<ol>
<li>In the Sequin Console, navigate to the Consumers page and click Create Consumer.</li>
<li>Select your Supabase database and the table that contains the data you want to process in the consumer. Then, add filters to process just the rows and changes you need.</li>
<li>Set your consumer as an HTTP push consumer or HTTP pull consumer, and then configure the endpoints and headers as needed. Then save your consumer.</li>
</ol>
<p>✓ Make a change in your database, and see it appear in your consumer:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/sequin_io/sequin-stream-imgur.gif" alt="Supabase change to Sequin consumer"/></p>
<h2>Next steps</h2>
<p>You’ve now walked through the basics of connecting to Sequin, creating a consumer, and capturing changes. Now, you can write the business logic for your application to react to the changes.</p>
<p>From here, you might want to:</p>
<ul>
<li><a href="https://sequinstream.com/docs/guides/supabase#moving-supabase-webhooks-to-sequin">Move some Supabase webhooks</a> to Sequin.</li>
<li>Set up a Sequin consumer to <a href="https://sequinstream.com/docs/guides/supabase#triggering-a-supabase-edge-function-with-sequin">trigger a Supabase edge function</a>.</li>
<li>Use the <a href="https://github.com/sequinstream/sequin-js">Sequin javascript SDK</a> to pull messages from a consumer.</li>
<li>Dig into Sequin’s <a href="https://sequinstream.com/docs/core-concepts">core concepts</a> to tune your setup.</li>
</ul></div></div><div class="lg:col-span-3"><div class="sticky top-20 flex flex-col gap-4"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Details</h2><button class="w-full w-full"><div class="video-container overflow-hidden rounded hover:cursor-pointer"><div class=" absolute inset-0 z-10 text-white flex flex-col gap-3 items-center justify-center bg-alternative before:content[&#x27;&#x27;] before:absolute before:inset-0 before:bg-black before:opacity-30 before:-z-10 hover:before:opacity-50 before:transition-opacity "><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play w-5 h-5"><polygon points="6 3 20 12 6 21 6 3"></polygon></svg><p class="text-sm">Watch an introductory video</p></div><img alt="Video guide preview" loading="lazy" decoding="async" data-nimg="fill" class="absolute inset-0 object-cover blur-sm scale-105" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=3840&amp;q=75"/></div></button><div class="text-foreground divide-y"><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Developer</span><span class="text-foreground">Sequin</span></div><div class="flex items-center justify-between py-2"><span class="text-lighter">Category</span><a class="text-brand hover:underline transition-colors" href="../integrations.html#devtools">DevTools</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Website</span><a href="https://sequinstream.com" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors">sequinstream.com</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Documentation</span><a href="https://sequinstream.com/docs/introduction" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors"><span class="flex items-center space-x-1"><span>Learn</span><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></span></a></div></div><p class="text-foreground-light text-sm">Third-party integrations and docs are managed by Supabase partners.</p></div></div></div></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"partner":{"id":36,"slug":"sequin_io","type":"technology","category":"DevTools","developer":"Sequin","title":"Sequin","description":"Sequin adds async Postgres triggers to Supabase.","logo":"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/sequin_io/sequin_logo.png","images":["https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/sequin_io/sequin-stream-1.png","https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/sequin_io/sequin-stream-2.png","https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/sequin_io/sequin-stream-3.png","https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/sequin_io/sequin-stream-4.png"],"overview":"Sequin sends your Supabase rows and changes anywhere you want. It’s designed to never miss an `insert`, `update`, or `delete` and provide exactly-once process for all changes.\n\nYou can receive changes via HTTP push (webhooks) or pull (think SQS). You can easily send changes to your application, another service (i.e. trigger.dev, resend, ingest), or trigger a Supabase edge function.\n\nAt some point, almost every Supabase app needs to trigger side effects. Think of sending a new user a welcome email or fanning out Jobs when an order arrives. Sequin aims to give you the tools to make that happen.\n\n## Killer features\n\n* **Never miss a message:** Sequin persists messages until they are acknowledged (i.e. exactly once processing guarantees).\n* **SQL-based routing:** Filter and route messages using SQL `where` conditions\n* **Replays:** Rewind to any row on your table. Republish messages that match a SQL query.\n* **Backfills:** Queue up records from this moment forward or the entire table.\n* **Observability and debugging:** Sequin has a web console that adds the Supabase queue tooling you’ve been looking for.\n* **Skip the PL/pgsql:** Define business logic in the language of your choice and in your application.\n* **Transforms:** Transform message payloads with Lua functions.\n\n## Documentation\n\nThis guide steps you through connecting Sequin to your Supabase database to send database changes anywhere.\n\n### 1. Connect to Sequin with a direct connection\n\nSequin uses the Write Ahead Log (WAL) to capture changes from your Supabase database. Supabase's connection pooling does not support the WAL, so you need to connect to your Supabase database using a direct connection.\n\nHere is how to set up a direct connection in Supabase:\n\n1. Login to Supabase and navigate to the database settings page (**Settings \u003e Configurations \u003e Database**).\n2. Disable the **Use connection pooling** option to show your direct connection credentials:\n3. In Sequin, enter the direct connection details in the **Database connection** page, including the **Host**, **Port**, **Database**, **User**, and **Password** for your Supabase database. Turn **SSL** on.\n\n✓ Sequin will automatically detect that you're using a Supabase database and check that your connection is configured correctly.\n\n### 2. Create a replication slot in Supabase\n\nWith your Supabase database connected to Sequin, you'll now create the replication slot that Sequin will use to detect changes in your database.\n\n1. In the Supabase SQL editor, run the following SQL command to create a replication slot:\n\n   ```sql\n   select pg_create_logical_replication_slot('sequin_slot', 'pgoutput');\n   ```\n\nThis creates a replication slot named `sequin_slot`.\n\n2. Next, you'll create a publication to indicate which tables will publish changes to the replication slot.\n\n   In the SQL editor, run the following SQL command to create a publication:\n\n   ```sql\n   create publication sequin_pub for table table1, table2, table3;\n   ```\n\n   If you want to publish changes from all the tables in your database, you can also use the `for all tables` option:\n\n   ```sql\n   create publication sequin_pub for all tables;\n   ```\n\n3. Back in the Sequin Console, enter the name of the replication slot (e.g. `sequin_slot`) and publication (e.g. `sequin_pub`) you just created. Then, name your database and click **Create Database**.\n\n   ![Supabase connected to Sequin](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/sequin_io/sequin-stream-successful-database-connection.png)\n\n✓ Sequin will now capture rows and changes from your Supabase database.\n\n### 3. Add a consumer in Sequin\n\nSequin and Supabase are now working together. Every change in your database will be captured and can be sent to your application, another service, or trigger a Supabase edge function.\n\nYou’ll add a consumer in Sequin to define what changes you want to process and where you want to send them.\n\n1. In the Sequin Console, navigate to the Consumers page and click Create Consumer.\n2. Select your Supabase database and the table that contains the data you want to process in the consumer. Then, add filters to process just the rows and changes you need.\n3. Set your consumer as an HTTP push consumer or HTTP pull consumer, and then configure the endpoints and headers as needed. Then save your consumer.\n\n\n✓ Make a change in your database, and see it appear in your consumer:\n\n![Supabase change to Sequin consumer](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/sequin_io/sequin-stream-imgur.gif)\n\n## Next steps\n\nYou’ve now walked through the basics of connecting to Sequin, creating a consumer, and capturing changes. Now, you can write the business logic for your application to react to the changes.\n\nFrom here, you might want to:\n\n- [Move some Supabase webhooks](https://sequinstream.com/docs/guides/supabase#moving-supabase-webhooks-to-sequin) to Sequin.\n- Set up a Sequin consumer to [trigger a Supabase edge function](https://sequinstream.com/docs/guides/supabase#triggering-a-supabase-edge-function-with-sequin).\n- Use the [Sequin javascript SDK](https://github.com/sequinstream/sequin-js) to pull messages from a consumer.\n- Dig into Sequin’s [core concepts](https://sequinstream.com/docs/core-concepts) to tune your setup.","website":"https://sequinstream.com","docs":"https://sequinstream.com/docs/introduction","contact":89,"approved":true,"created_at":"2022-08-29T03:06:34+00:00","tsv":"'/docs/core-concepts)':777C '/docs/guides/supabase#moving-supabase-webhooks-to-sequin)':738C '/docs/guides/supabase#triggering-a-supabase-edge-function-with-sequin).':754C '/sequinstream/sequin-js)':762C '/storage/v1/object/public/images/integrations/sequin_io/sequin-stream-imgur.gif)':691C '/storage/v1/object/public/images/integrations/sequin_io/sequin-stream-successful-database-connection.png)':530C '1':227C,282C,387C,600C '2':296C,356C,421C,614C '3':309C,492C,542C,645C 'acknowledg':123C 'add':3B,177C,543C,580C,634C 'ahead':239C 'aim':101C 'almost':74C 'also':477C 'anoth':59C,570C 'anywher':16C,226C 'app':77C 'appear':680C 'applic':58C,202C,569C,720C 'arriv':99C 'async':4B 'automat':339C 'back':493C 'backfil':156C 'base':131C 'basic':700C 'busi':191C,716C 'captur':243C,534C,562C,709C 'chang':15C,38C,42C,55C,225C,244C,383C,434C,467C,537C,556C,588C,642C,673C,685C,710C,725C 'check':349C 'choic':198C 'click':521C,611C 'command':397C,447C 'concept':774C 'condit':140C 'configur':294C,354C,660C 'connect':216C,228C,234C,251C,262C,270C,279C,300C,307C,315C,320C,352C,367C,525C,702C 'consol':175C,497C,604C 'consum':545C,582C,608C,613C,632C,648C,653C,657C,670C,683C,688C,707C,745C,768C 'contain':623C 'core':773C 'correct':355C 'creat':357C,373C,399C,406C,414C,425C,449C,453C,485C,515C,522C,612C,705C 'credenti':308C 'data':625C 'databas':221C,224C,248C,266C,290C,295C,319C,326C,333C,347C,366C,386C,474C,519C,523C,541C,559C,618C,676C 'debug':170C 'defin':190C,586C 'delet':29C 'design':21C 'detail':316C 'detect':340C,382C 'devtool':782 'dig':769C 'direct':233C,269C,278C,306C,314C 'disabl':297C 'document':210C 'e.g':505C,510C 'easili':53C 'edg':69C,576C,750C 'editor':392C,442C 'effect':82C 'email':91C 'endpoint':662C 'enter':312C,498C 'entir':166C 'everi':75C,555C 'exact':33C,125C 'exactly-onc':32C 'fan':93C 'featur':112C 'filter':133C,635C 'follow':395C,445C 'forward':163C 'function':70C,209C,577C,751C 'github.com':761C 'github.com/sequinstream/sequin-js)':760C 'give':103C 'guarante':128C 'guid':212C 'happen':110C 'header':664C 'host':324C 'http':44C,651C,655C 'i.e':61C,124C 'includ':322C 'indic':429C 'ingest':64C 'insert':26C 'io':784 'javascript':758C 'job':95C 'killer':111C 'languag':195C 'll':371C,424C,579C 'log':240C 'logic':192C,407C,717C 'login':283C 'look':185C 'lua':208C 'make':108C,671C 'match':152C 'messag':116C,119C,136C,150C,205C,765C 'might':729C 'miss':24C,114C 'moment':162C 'move':732C 'name':418C,500C,517C 'navig':287C,605C 'need':78C,260C,644C,666C 'never':23C,113C 'new':87C 'next':422C,692C 'observ':168C 'obuldanrptloktxcffvn.supabase.co':529C,690C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/sequin_io/sequin-stream-imgur.gif)':689C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/sequin_io/sequin-stream-successful-database-connection.png)':528C 'option':302C,483C 'order':98C 'page':292C,321C,609C 'password':329C 'payload':206C 'persist':118C 'pg':405C 'pgoutput':412C 'pl/pgsql':189C 'point':73C 'pool':252C,301C 'port':325C 'postgr':5B 'process':35C,127C,592C,629C,637C 'provid':31C 'pub':456C,488C,512C 'public':427C,451C,454C,486C,509C 'publish':433C,466C 'pull':48C,656C,764C 'push':45C,652C 'queri':155C 'queue':157C,180C 're':343C 'react':722C 'receiv':41C 'record':159C 'replay':141C 'replic':359C,375C,401C,408C,416C,437C,503C 'republish':149C 'resend':63C 'rewind':142C 'rout':132C,135C 'row':13C,145C,535C,640C 'run':393C,443C 'save':668C 'sdk':759C 'see':678C 'select':404C,615C 'send':10C,54C,85C,223C,598C 'sent':566C 'sequin':1A,2B,9C,100C,117C,171C,217C,230C,235C,311C,337C,369C,378C,410C,419C,455C,487C,496C,506C,511C,527C,531C,547C,548C,584C,603C,687C,704C,740C,744C,757C,771C,783 'sequinstream.com':737C,753C,776C 'sequinstream.com/docs/core-concepts)':775C 'sequinstream.com/docs/guides/supabase#moving-supabase-webhooks-to-sequin)':736C 'sequinstream.com/docs/guides/supabase#triggering-a-supabase-edge-function-with-sequin).':752C 'servic':60C,571C 'set':275C,291C,293C,646C,741C 'setup':781C 'show':304C 'side':81C 'skip':187C 'slot':360C,376C,402C,409C,411C,417C,420C,438C,504C,507C 'sql':130C,138C,154C,391C,396C,403C,441C,446C,452C,484C 'sql-base':129C 'sqs':50C 'ssl':335C 'step':213C,693C 'supabas':8B,12C,68C,76C,179C,220C,247C,249C,265C,281C,285C,332C,346C,362C,365C,390C,524C,540C,550C,575C,617C,684C,734C,749C 'support':255C 'tabl':148C,167C,431C,458C,471C,482C,491C,621C 'table1':459C 'table2':460C 'table3':461C 'think':49C,83C 'togeth':554C 'tool':106C,181C 'transform':203C,204C 'trigger':6B,66C,80C,573C,747C 'trigger.dev':62C 'tune':779C 'turn':334C 'updat':27C 'use':137C,236C,267C,299C,344C,380C,478C,755C 'user':88C,327C 've':183C,695C 'via':43C 'wal':241C,257C 'walk':697C 'want':18C,464C,590C,596C,627C,730C 'web':174C 'webhook':46C,735C 'welcom':90C 'work':553C 'write':238C,714C","video":"mDPI4iq1dGM","call_to_action_link":null,"featured":false},"overview":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nconst chCodeConfig = {\n  \"staticMediaQuery\": \"not screen, (max-width: 768px)\",\n  \"lineNumbers\": true,\n  \"showCopyButton\": true,\n  \"themeName\": \"supabase\"\n};\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    code: \"code\",\n    h2: \"h2\",\n    ul: \"ul\",\n    li: \"li\",\n    strong: \"strong\",\n    h3: \"h3\",\n    ol: \"ol\",\n    img: \"img\",\n    a: \"a\"\n  }, _provideComponents(), props.components), {CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Sequin sends your Supabase rows and changes anywhere you want. It’s designed to never miss an \", _jsx(_components.code, {\n        children: \"insert\"\n      }), \", \", _jsx(_components.code, {\n        children: \"update\"\n      }), \", or \", _jsx(_components.code, {\n        children: \"delete\"\n      }), \" and provide exactly-once process for all changes.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You can receive changes via HTTP push (webhooks) or pull (think SQS). You can easily send changes to your application, another service (i.e. trigger.dev, resend, ingest), or trigger a Supabase edge function.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"At some point, almost every Supabase app needs to trigger side effects. Think of sending a new user a welcome email or fanning out Jobs when an order arrives. Sequin aims to give you the tools to make that happen.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Killer features\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Never miss a message:\"\n        }), \" Sequin persists messages until they are acknowledged (i.e. exactly once processing guarantees).\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"SQL-based routing:\"\n        }), \" Filter and route messages using SQL \", _jsx(_components.code, {\n          children: \"where\"\n        }), \" conditions\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Replays:\"\n        }), \" Rewind to any row on your table. Republish messages that match a SQL query.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Backfills:\"\n        }), \" Queue up records from this moment forward or the entire table.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Observability and debugging:\"\n        }), \" Sequin has a web console that adds the Supabase queue tooling you’ve been looking for.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Skip the PL/pgsql:\"\n        }), \" Define business logic in the language of your choice and in your application.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Transforms:\"\n        }), \" Transform message payloads with Lua functions.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Documentation\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This guide steps you through connecting Sequin to your Supabase database to send database changes anywhere.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"1. Connect to Sequin with a direct connection\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Sequin uses the Write Ahead Log (WAL) to capture changes from your Supabase database. Supabase's connection pooling does not support the WAL, so you need to connect to your Supabase database using a direct connection.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Here is how to set up a direct connection in Supabase:\"\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Login to Supabase and navigate to the database settings page (\", _jsx(_components.strong, {\n          children: \"Settings \u003e Configurations \u003e Database\"\n        }), \").\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Disable the \", _jsx(_components.strong, {\n          children: \"Use connection pooling\"\n        }), \" option to show your direct connection credentials:\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In Sequin, enter the direct connection details in the \", _jsx(_components.strong, {\n          children: \"Database connection\"\n        }), \" page, including the \", _jsx(_components.strong, {\n          children: \"Host\"\n        }), \", \", _jsx(_components.strong, {\n          children: \"Port\"\n        }), \", \", _jsx(_components.strong, {\n          children: \"Database\"\n        }), \", \", _jsx(_components.strong, {\n          children: \"User\"\n        }), \", and \", _jsx(_components.strong, {\n          children: \"Password\"\n        }), \" for your Supabase database. Turn \", _jsx(_components.strong, {\n          children: \"SSL\"\n        }), \" on.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"✓ Sequin will automatically detect that you're using a Supabase database and check that your connection is configured correctly.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"2. Create a replication slot in Supabase\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"With your Supabase database connected to Sequin, you'll now create the replication slot that Sequin will use to detect changes in your database.\"\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"\\n\", _jsx(_components.p, {\n          children: \"In the Supabase SQL editor, run the following SQL command to create a replication slot:\"\n        }), \"\\n\", _jsx(CH.Code, {\n          codeConfig: chCodeConfig,\n          northPanel: {\n            \"tabs\": [\"\"],\n            \"active\": \"\",\n            \"heightRatio\": 1\n          },\n          files: [{\n            \"name\": \"\",\n            \"focus\": \"\",\n            \"code\": {\n              \"lines\": [{\n                \"tokens\": [{\n                  \"content\": \"select\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-7)\"\n                    }\n                  }\n                }, {\n                  \"content\": \" pg_create_logical_replication_slot(\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }, {\n                  \"content\": \"'sequin_slot'\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-8)\"\n                    }\n                  }\n                }, {\n                  \"content\": \", \",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }, {\n                  \"content\": \"'pgoutput'\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-8)\"\n                    }\n                  }\n                }, {\n                  \"content\": \");\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }],\n              \"lang\": \"sql\"\n            },\n            \"annotations\": []\n          }]\n        }), \"\\n\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"This creates a replication slot named \", _jsx(_components.code, {\n        children: \"sequin_slot\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.ol, {\n      start: \"2\",\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"\\n\", _jsx(_components.p, {\n          children: \"Next, you'll create a publication to indicate which tables will publish changes to the replication slot.\"\n        }), \"\\n\", _jsx(_components.p, {\n          children: \"In the SQL editor, run the following SQL command to create a publication:\"\n        }), \"\\n\", _jsx(CH.Code, {\n          codeConfig: chCodeConfig,\n          northPanel: {\n            \"tabs\": [\"\"],\n            \"active\": \"\",\n            \"heightRatio\": 1\n          },\n          files: [{\n            \"name\": \"\",\n            \"focus\": \"\",\n            \"code\": {\n              \"lines\": [{\n                \"tokens\": [{\n                  \"content\": \"create\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-7)\"\n                    }\n                  }\n                }, {\n                  \"content\": \" publication sequin_pub for \",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }, {\n                  \"content\": \"table\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-7)\"\n                    }\n                  }\n                }, {\n                  \"content\": \" table1, table2, table3;\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }],\n              \"lang\": \"sql\"\n            },\n            \"annotations\": []\n          }]\n        }), \"\\n\", _jsxs(_components.p, {\n          children: [\"If you want to publish changes from all the tables in your database, you can also use the \", _jsx(_components.code, {\n            children: \"for all tables\"\n          }), \" option:\"]\n        }), \"\\n\", _jsx(CH.Code, {\n          codeConfig: chCodeConfig,\n          northPanel: {\n            \"tabs\": [\"\"],\n            \"active\": \"\",\n            \"heightRatio\": 1\n          },\n          files: [{\n            \"name\": \"\",\n            \"focus\": \"\",\n            \"code\": {\n              \"lines\": [{\n                \"tokens\": [{\n                  \"content\": \"create\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-7)\"\n                    }\n                  }\n                }, {\n                  \"content\": \" publication sequin_pub for all tables;\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }],\n              \"lang\": \"sql\"\n            },\n            \"annotations\": []\n          }]\n        }), \"\\n\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"\\n\", _jsxs(_components.p, {\n          children: [\"Back in the Sequin Console, enter the name of the replication slot (e.g. \", _jsx(_components.code, {\n            children: \"sequin_slot\"\n          }), \") and publication (e.g. \", _jsx(_components.code, {\n            children: \"sequin_pub\"\n          }), \") you just created. Then, name your database and click \", _jsx(_components.strong, {\n            children: \"Create Database\"\n          }), \".\"]\n        }), \"\\n\", _jsx(_components.p, {\n          children: _jsx(_components.img, {\n            src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/sequin_io/sequin-stream-successful-database-connection.png\",\n            alt: \"Supabase connected to Sequin\"\n          })\n        }), \"\\n\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"✓ Sequin will now capture rows and changes from your Supabase database.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"3. Add a consumer in Sequin\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Sequin and Supabase are now working together. Every change in your database will be captured and can be sent to your application, another service, or trigger a Supabase edge function.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You’ll add a consumer in Sequin to define what changes you want to process and where you want to send them.\"\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"In the Sequin Console, navigate to the Consumers page and click Create Consumer.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Select your Supabase database and the table that contains the data you want to process in the consumer. Then, add filters to process just the rows and changes you need.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Set your consumer as an HTTP push consumer or HTTP pull consumer, and then configure the endpoints and headers as needed. Then save your consumer.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"✓ Make a change in your database, and see it appear in your consumer:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/sequin_io/sequin-stream-imgur.gif\",\n        alt: \"Supabase change to Sequin consumer\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Next steps\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You’ve now walked through the basics of connecting to Sequin, creating a consumer, and capturing changes. Now, you can write the business logic for your application to react to the changes.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"From here, you might want to:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://sequinstream.com/docs/guides/supabase#moving-supabase-webhooks-to-sequin\",\n          children: \"Move some Supabase webhooks\"\n        }), \" to Sequin.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Set up a Sequin consumer to \", _jsx(_components.a, {\n          href: \"https://sequinstream.com/docs/guides/supabase#triggering-a-supabase-edge-function-with-sequin\",\n          children: \"trigger a Supabase edge function\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Use the \", _jsx(_components.a, {\n          href: \"https://github.com/sequinstream/sequin-js\",\n          children: \"Sequin javascript SDK\"\n        }), \" to pull messages from a consumer.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Dig into Sequin’s \", _jsx(_components.a, {\n          href: \"https://sequinstream.com/docs/core-concepts\",\n          children: \"core concepts\"\n        }), \" to tune your setup.\"]\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  chCodeConfig,\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{}}},"__N_SSG":true},"page":"/partners/integrations/[slug]","query":{"slug":"sequin_io"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>