<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../../rss.xml" data-next-head=""/><link rel="manifest" href="../../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:type" content="website" data-next-head=""/><meta property="og:image:alt" content="Supabase Og Image" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Stytch | Works With Supabase</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Stytch provides an all-in-one platform for passwordless auth." data-next-head=""/><meta property="og:title" content="Stytch | Works With Supabase" data-next-head=""/><meta property="og:description" content="Stytch provides an all-in-one platform for passwordless auth." data-next-head=""/><meta property="og:url" content="https://supabase.com/partners/integrations/stytch" data-next-head=""/><meta property="og:image" content="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/stytch-1.png" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2263-07e8ebb96cc2b014.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8658-639e1bb66549b52b.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/partners/integrations/%5Bslug%5D-cff8a94339d02c6e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqela6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R1eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R1eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R2eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R2eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R3eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R3eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav></div><main class="relative min-h-screen"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20"><div class="col-span-12 mx-auto mb-2 max-w-5xl space-y-10 lg:col-span-2"><a class="text-foreground hover:text-foreground-lighter flex cursor-pointer items-center transition-colors" href="../integrations.html"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left"><path d="m15 18-6-6 6-6"></path></svg>Back</a><div class="flex items-center space-x-4"><img alt="Stytch" loading="lazy" width="56" height="56" decoding="async" data-nimg="1" class="bg-surface-200 flex-shrink-f0 h-14 w-14 rounded-full" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-logo.png&amp;w=64&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-logo.png&amp;w=128&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-logo.png&amp;w=128&amp;q=75"/><h1 class="h1" style="margin-bottom:0">Stytch</h1></div><div class="bg-gradient-to-t from-background-alternative to-background border-b p-6 [&amp;_.swiper-container]:overflow-visible" style="margin-left:calc(50% - 50vw);margin-right:calc(50% - 50vw)"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0 !px-3 lg:!px-12 xl:!p-0 mx-auto max-w-5xl"><div class="swiper"><div class="swiper-wrapper"><div class="swiper-slide"><div class="relative block overflow-hidden rounded-md"><img alt="Stytch" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto;background-size:cover;background-position:50% 50%;background-repeat:no-repeat;background-image:url(&quot;data:image/svg+xml;charset=utf-8,%3Csvg xmlns=&#x27;http://www.w3.org/2000/svg&#x27; viewBox=&#x27;0 0 1460 960&#x27;%3E%3Cfilter id=&#x27;b&#x27; color-interpolation-filters=&#x27;sRGB&#x27;%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3CfeColorMatrix values=&#x27;1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1&#x27; result=&#x27;s&#x27;/%3E%3CfeFlood x=&#x27;0&#x27; y=&#x27;0&#x27; width=&#x27;100%25&#x27; height=&#x27;100%25&#x27;/%3E%3CfeComposite operator=&#x27;out&#x27; in=&#x27;s&#x27;/%3E%3CfeComposite in2=&#x27;SourceGraphic&#x27;/%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3C/filter%3E%3Cimage width=&#x27;100%25&#x27; height=&#x27;100%25&#x27; x=&#x27;0&#x27; y=&#x27;0&#x27; preserveAspectRatio=&#x27;none&#x27; style=&#x27;filter: url(%23b);&#x27; href=&#x27;/images/blur.png&#x27;/%3E%3C/svg%3E&quot;)" sizes="100vw" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-1.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-1.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-1.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-1.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-1.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-1.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-1.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-1.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-1.png&amp;w=3840&amp;q=75"/></div></div><div class="swiper-slide"><div class="relative block overflow-hidden rounded-md"><img alt="Stytch" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto;background-size:cover;background-position:50% 50%;background-repeat:no-repeat;background-image:url(&quot;data:image/svg+xml;charset=utf-8,%3Csvg xmlns=&#x27;http://www.w3.org/2000/svg&#x27; viewBox=&#x27;0 0 1460 960&#x27;%3E%3Cfilter id=&#x27;b&#x27; color-interpolation-filters=&#x27;sRGB&#x27;%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3CfeColorMatrix values=&#x27;1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1&#x27; result=&#x27;s&#x27;/%3E%3CfeFlood x=&#x27;0&#x27; y=&#x27;0&#x27; width=&#x27;100%25&#x27; height=&#x27;100%25&#x27;/%3E%3CfeComposite operator=&#x27;out&#x27; in=&#x27;s&#x27;/%3E%3CfeComposite in2=&#x27;SourceGraphic&#x27;/%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3C/filter%3E%3Cimage width=&#x27;100%25&#x27; height=&#x27;100%25&#x27; x=&#x27;0&#x27; y=&#x27;0&#x27; preserveAspectRatio=&#x27;none&#x27; style=&#x27;filter: url(%23b);&#x27; href=&#x27;/images/blur.png&#x27;/%3E%3C/svg%3E&quot;)" sizes="100vw" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-2.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-2.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-2.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-2.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-2.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-2.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-2.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-2.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-2.png&amp;w=3840&amp;q=75"/></div></div><div class="swiper-slide"><div class="relative block overflow-hidden rounded-md"><img alt="Stytch" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto;background-size:cover;background-position:50% 50%;background-repeat:no-repeat;background-image:url(&quot;data:image/svg+xml;charset=utf-8,%3Csvg xmlns=&#x27;http://www.w3.org/2000/svg&#x27; viewBox=&#x27;0 0 1460 960&#x27;%3E%3Cfilter id=&#x27;b&#x27; color-interpolation-filters=&#x27;sRGB&#x27;%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3CfeColorMatrix values=&#x27;1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1&#x27; result=&#x27;s&#x27;/%3E%3CfeFlood x=&#x27;0&#x27; y=&#x27;0&#x27; width=&#x27;100%25&#x27; height=&#x27;100%25&#x27;/%3E%3CfeComposite operator=&#x27;out&#x27; in=&#x27;s&#x27;/%3E%3CfeComposite in2=&#x27;SourceGraphic&#x27;/%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3C/filter%3E%3Cimage width=&#x27;100%25&#x27; height=&#x27;100%25&#x27; x=&#x27;0&#x27; y=&#x27;0&#x27; preserveAspectRatio=&#x27;none&#x27; style=&#x27;filter: url(%23b);&#x27; href=&#x27;/images/blur.png&#x27;/%3E%3C/svg%3E&quot;)" sizes="100vw" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-3.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-3.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-3.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-3.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-3.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-3.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-3.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-3.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fstytch%2Fstytch-3.png&amp;w=3840&amp;q=75"/></div></div></div></div></div></div><div class="grid gap-y-12 lg:grid-cols-8 lg:space-x-12"><div class="lg:col-span-5 overflow-hidden"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Overview</h2><div class="prose"><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p>Stytch provides an all-in-one platform for passwordless auth. Stytch makes it easy for you to embed passwordless solutions into your websites and apps for better security, better conversion rates, and a better end user experience. Their easy-to-use SDKs and direct API access allows for maximum control and customization. In this example we will use Email magic links to create and log in our users, and Session management. There is an additional, optional step to enable Google One Tap which is an especially high-converting Google OAuth sign-up and login flow.</p>
<h2>Documentation</h2>
<p>In this guide we will build a simple expense tracker web application using Stytch, Supabase, and Next.js.</p>
<p><a href="https://stytch.com?utm_source=supabase&amp;utm_medium=guide">Stytch</a> provides an all-in-one platform for passwordless auth. Stytch makes it easy for you to embed passwordless solutions into your websites and apps for better security, better conversion rates, and a better end user experience. Their easy-to-use SDKs and direct API access allows for maximum control and customization. In this example we will use <a href="https://stytch.com/products/email-magic-links?utm_source=supabase&amp;utm_medium=guide">Email magic links</a> to create and log in our users, and Session management. There is an additional, optional step to enable <a href="https://stytch.com/blog/improving-conversion-with-google-one-tap?utm_source=supabase&amp;utm_medium=guide">Google One Tap</a> which is an especially high-converting Google OAuth sign-up and login flow.</p>
<p>We will leverage Supabase to store and authorize access to user data. Supabase makes it simple to set up Row Level Security (RLS) policies which ensure users can only read and write data that they are authorized to do so. If you do not already have a Supabase account, you will need to create one.</p>
<p>This guide will use <a href="https://nextjs.org/">Next.js</a> which is a web application framework built on top of React. Stytch provides a <a href="https://github.com/stytchauth/stytch-node">Node.js library</a> and a <a href="https://stytch.com/docs/sdks/javascript-sdk">React library</a> which makes building Next.js apps super easy.</p>
<blockquote>
<p>Note: You can find a completed version of this project on <a href="https://github.com/stytchauth/stytch-nextjs-supabase">Github</a>.</p>
</blockquote>
<h2>Step 0: Create a Stytch Account</h2>
<p>If you already have a Stytch account you may skip this step.</p>
<p>Go to <a href="https://stytch.com?utm_source=supabase&amp;utm_medium=guide">Stytch</a>, and create an account. Note that Stytch provides two ways to create an account, either via Google OAuth, or through Email magic links.  This is the same user experience we will be building in this guide!</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/01.png" alt="Stytch redirect URL settings"/></p>
<h2>Step 1: Set up Stytch redirect URLs</h2>
<p>First we need to add the redirect URLs that will be used during the Email magic link flow. This step helps ensure bad actors cannot spoof your magic links and hijack redirects.</p>
<p>Navigate to your <a href="https://stytch.com/dashboard/redirect-urls?utm_source=supabase&amp;utm_medium=guide">redirect URL settings</a> in the Stytch dashboard, and under <strong>Test environment</strong> create an entry where the <strong>URL</strong> is <code>http://localhost:3000/api/authenticate</code> and the <strong>Type</strong> is <code>All</code>.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/02.png" alt="Edit Stytch redirect URL settings"/></p>
<p>After pressing <strong>Confirm</strong>, the redirect URLs dashboard will update to show your new entry. We will use this URL later on.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/03.png" alt="Stytch redirect URL settings"/></p>
<h2>Step 2: Create a Supabase project</h2>
<p>From your <a href="../../dashboard/org.html">Supabase dashboard</a>, click <strong>New project</strong>.</p>
<p>Enter a <code>Name</code> for your Supabase project.</p>
<p>Enter a secure <code>Database Password</code>.</p>
<p>Click <strong>Create new project</strong>. It may take a couple minutes for your project to be provisioned.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/04.png" alt="New Supabase project settings"/></p>
<h2>Step 3: Creating data in Supabase</h2>
<p>Once your Supabase project is provisioned, click Table editor, then New table. This tool is available from the sidebar menu in the <a href="../../dashboard/org.html">Supabase dashboard</a>.</p>
<p>Enter <code>expenses</code> as the <strong>Name</strong> field.</p>
<p>Select <code>Enable Row Level Security (RLS)</code>.</p>
<p>Add three new columns:</p>
<ul>
<li>
<p><code>user_id</code> as <code>text</code></p>
</li>
<li>
<p><code>title</code> as <code>text</code></p>
</li>
<li>
<p><code>value</code> as <code>float8</code></p>
</li>
</ul>
<p>Click <strong>Save</strong> to create the new table.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/05.png" alt="Creating a new table"/></p>
<p>From the Table editor view, select the expenses table and click <strong>Insert row</strong>.</p>
<p>Fill out the title and value fields (leave user_id blank for now) and click <strong>Save</strong>.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/06.png" alt="Creating a new row"/></p>
<p>Use <strong>Insert Row</strong> to further populate the table with expenses.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/07.png" alt="Multiple rows"/></p>
<h2>Step 4: Building a Next.js app</h2>
<p>Using a terminal, create a new Next.js project:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>npx create-next-app stytch-supabase-example</span></div></div><br/></code></div></div>
<p>Next, within <code>stytch-supabase-example</code> create a <code>.env.local</code> file and enter the following values:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>STYTCH_PROJECT_ENV=test</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>STYTCH_PROJECT_ID=GET_FROM_STYTCH_DASHBOARD</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>STYTCH_PUBLIC_TOKEN=GET_FROM_STYTCH_DASHBOARD</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>STYTCH_SECRET=GET_FROM_STYTCH_DASHBOARD</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>NEXT_PUBLIC_SUPABASE_URL=GET_FROM_SUPABASE_DASHBOARD</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>NEXT_PUBLIC_SUPABASE_KEY=GET_FROM_SUPABASE_DASHBOARD</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>SUPABASE_SIGNING_SECRET=GET_FROM_SUPABASE_DASHBOARD</span></div></div><br/></code></div></div>
<blockquote>
<p>Note: Stytch values can be found in the project <a href="https://stytch.com/dashboard/api-keys?utm_source=supabase&amp;utm_medium=guide">dashboard</a> under <strong>API Keys</strong>.</p>
</blockquote>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/08.png" alt="Stytch API keys"/></p>
<blockquote>
<p>Note: Supabase values can be found under <strong>Settings</strong> &gt; <strong>API</strong> for your project.</p>
</blockquote>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/09.png" alt="Supabase API keys"/></p>
<p>Start your Next.js development server to read in the new values from <code>.env.local</code>.</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>npm run dev</span></div></div><br/></code></div></div>
<p>You should have a running Next.js application on <code>localhost:3000</code>.</p>
<h2>Step 5: Build the Login Form</h2>
<p>Now we will replace the default Next.js home page with a login UI. We will use the Stytch React library.</p>
<blockquote>
<p>Note: Stytch provides direct API access for those that want to build login UI themselves</p>
</blockquote>
<p>Install the <code>@stytch/stytch-react</code> library.</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>npm install @stytch/stytch-react</span></div></div><br/></code></div></div>
<p>In the root directory, create a new folder named <code>components</code> and file in that folder named <code>/StytchLogin.js</code>. Within this file, paste the snippet below. This will configure, and style the Stytch React component to use Email magic links.</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>// components/StytchLogin.js</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>import React from &#x27;react&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>import { Stytch } from &#x27;@stytch/stytch-react&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>const stytchConfig = {</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>  loginOrSignupView: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>    products: [&#x27;emailMagicLinks&#x27;],</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>    emailMagicLinksOptions: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>      loginRedirectURL: &#x27;http://localhost:3000/api/authenticate&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>      loginExpirationMinutes: 30,</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>      signupRedirectURL: &#x27;http://localhost:3000/api/authenticate&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>      signupExpirationMinutes: 30,</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>      createUserAsPending: true,</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>    },</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>  },</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>  style: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>    fontFamily: &#x27;&quot;Helvetica New&quot;, Helvetica, sans-serif&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>    width: &#x27;321px&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>    primaryColor: &#x27;#0577CA&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>  },</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>const StytchLogin = ({ publicToken }) =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>  return (</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>    &lt;Stytch</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>      publicToken={publicToken}</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>      loginOrSignupView={stytchConfig.loginOrSignupView}</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>      style={stytchConfig.style}</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>    /&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>  )</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->33</span><div style="display:inline-block;margin-left:16px"><span>export default StytchLogin</span></div></div><br/></code></div></div>
<p>Additionally, create a profile component by creating a file called <code>Profile.js</code> in <code>/components</code>. We will use this component to render our expenses stored in Supabase later on.</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>// components/Profile.js</span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>import React from &#x27;react&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>import Link from &#x27;next/link&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>export default function Profile({ user }) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>  return (</span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>    &lt;div&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>      &lt;h1&gt;Welcome {user.userId}&lt;/h1&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>      &lt;h2&gt;Your expenses&lt;/h2&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>      {user.expenses?.length &gt; 0 ? (</span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>        user.expenses.map((expense) =&gt; (</span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>          &lt;p key={expense.id}&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>            {expense.title}: ${expense.value}</span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>          &lt;/p&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>        ))</span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>      ) : (</span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>        &lt;p&gt;You have no expenses!&lt;/p&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>      )}</span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>      &lt;Link href=&quot;/api/logout&quot; passHref&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>        &lt;button&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>          &lt;a&gt;Logout&lt;/a&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>        &lt;/button&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>      &lt;/Link&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>    &lt;/div&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>  )</span></div></div><div><span class="ch-code-line-number">_<!-- -->27</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<p>Finally, replace the contents of the file <code>/pages/index.js</code> to render our new <code>StytchLogin</code> and <code>Profile</code> components.</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>// pages/index.js</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>import styles from &#x27;../styles/Home.module.css&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>import Profile from &#x27;../components/Profile&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>import StytchLogin from &#x27;../components/StytchLogin&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>const Index = ({ user, publicToken }) =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>  let content</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>  if (user) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>    content = &lt;Profile user={user} /&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>  } else {</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>    content = &lt;StytchLogin publicToken={publicToken} /&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>  return &lt;div className={styles.main}&gt;{content}&lt;/div&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>export async function getServerSideProps({ req, res }) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>  const user = null // Will update later</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>  return {</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>    props: { user, publicToken: process.env.STYTCH_PUBLIC_TOKEN },</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->24</span><div style="display:inline-block;margin-left:16px"><span>export default Index</span></div></div><br/></code></div></div>
<p>On <code>localhost:3000</code> there is now a login form prompting for your email address.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/10.png" alt="Email login step one"/></p>
<p>Enter your email address and press <strong>Continue with email</strong>.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/11.png" alt="Email login step two"/></p>
<p>In your inbox you will find a login request from your app.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/12.png" alt="Email login step three"/></p>
<p>However, if you click the link in the email you will get a 404. We need to build an API route to handle the email magic link authentication.</p>
<h2>Step 6: Authenticate and start a session</h2>
<p>To make authentication easier we will use the Stytch Node.js library. Run</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>npm install stytch</span></div></div><br/></code></div></div>
<p>Additionally, we will need to store the authenticated session in a cookie. Run</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>npm install cookies-next</span></div></div><br/></code></div></div>
<p>Create a new folder named <code>utils</code> and inside a file named<code>stytchLogic.js</code> with the following contents</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>// utils/stytchLogic.js</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>import * as stytch from &#x27;stytch&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>import { getCookie, setCookies, removeCookies } from &#x27;cookies-next&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>export const SESSION_COOKIE = &#x27;stytch_cookie&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>let client</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>const loadStytch = () =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>  if (!client) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>    client = new stytch.Client({</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>      project_id: process.env.STYTCH_PROJECT_ID,</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>      secret: process.env.STYTCH_SECRET,</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>      env: process.env.STYTCH_PROJECT_ENV === &#x27;live&#x27; ? stytch.envs.live : stytch.envs.test,</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>    })</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>  return client</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>export const getAuthenticatedUserFromSession = async (req, res) =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>  const sessionToken = getCookie(SESSION_COOKIE, { req, res })</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>  if (!sessionToken) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>    return null</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>  try {</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>    const stytchClient = loadStytch()</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>    const resp = await stytchClient.sessions.authenticate({</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>      session_token: sessionToken,</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>    })</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>    return resp.session.user_id</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>  } catch (error) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>    console.log(error)</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>    return null</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>export const revokeAndClearSession = async (req, res) =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>  const sessionToken = getCookie(SESSION_COOKIE, { req, res })</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>  if (sessionToken) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>    try {</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>      const stytchClient = loadStytch()</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>      await stytchClient.sessions.revoke({</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>        session_token: sessionToken,</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>      })</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>    } catch (error) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>      console.log(error)</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>    }</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>    removeCookies(SESSION_COOKIE, { req, res })</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>  return res.redirect(&#x27;/&#x27;)</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>export const authenticateTokenStartSession = async (req, res) =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>  const { token, type } = req.query</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>  let sessionToken</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>  try {</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>    const stytchClient = loadStytch()</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>    const resp = await stytchClient.magicLinks.authenticate(token, {</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>      session_duration_minutes: 30,</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>    })</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>    sessionToken = resp.session_token</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>  } catch (error) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>    console.log(error)</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>    const errorString = JSON.stringify(error)</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>    return res.status(400).json({ errorString })</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>  setCookies(SESSION_COOKIE, sessionToken, {</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>    req,</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>    res,</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>    maxAge: 60 * 60 * 24,</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>    secure: true,</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>  })</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>  return res.redirect(&#x27;/&#x27;)</span></div></div><div><span class="ch-code-line-number">_<!-- -->79</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<p>This logic is responsible for setting up the Stytch client we will use to call the API. It provides functions we will use to login, logout, and validate user sessions.</p>
<p>In order to complete the email login flow, create a new file <code>pages/api/authenticate.js</code> with the contents:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>// pages/api/authenticate.js</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>import { authenticateTokenStartSession } from &#x27;../../utils/stytchLogic&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>export default async function handler(req, res) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  return authenticateTokenStartSession(req, res)</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<p>We will also create a logout API endpoint with similar contents. In <code>pages/api/logout.js</code> include the following:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>// pages/api/logout.js</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>import { revokeAndClearSession } from &#x27;../../utils/stytchLogic&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>export default async function handler(req, res) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  return revokeAndClearSession(req, res)</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<p>Finally, update <code>pages/index.js</code> by importing <code>getAuthenticatedUserFromSession</code>, and calling it to set the user variable in <code>getServerSideProps</code>.</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>// pages/index.js</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>import styles from &#x27;../styles/Home.module.css&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>import StytchLogin from &#x27;../components/StytchLogin&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>import Profile from &#x27;../components/Profile&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>import { getAuthenticatedUserFromSession } from &#x27;../utils/stytchLogic&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>const Index = ({ user, publicToken }) =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>  let content</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>  if (user) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>    content = &lt;Profile user={user} /&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>  } else {</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>    content = &lt;StytchLogin publicToken={publicToken} /&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>  return &lt;div className={styles.main}&gt;{content}&lt;/div&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>export async function getServerSideProps({ req, res }) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>  const userId = await getAuthenticatedUserFromSession(req, res)</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>  if (userId) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>    return {</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>      props: { user: { userId }, publicToken: process.env.STYTCH_PUBLIC_TOKEN },</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>    }</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>  return {</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>    props: { publicToken: process.env.STYTCH_PUBLIC_TOKEN },</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->31</span><div style="display:inline-block;margin-left:16px"><span>export default Index</span></div></div><br/></code></div></div>
<p>Return to <code>localhost:3000</code>, and login again by sending yourself a new email. Upon clicking through in the email you should be presented with “Welcome $USER_ID”. If you refresh the page, you should remain in an authenticated state. If you press <strong>Logout</strong> then you should return to the login screen.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/13.png" alt="Profile page"/></p>
<p>Now that we have a working login flow with persistent authentication it is time to pull in our expense data from Supabase.</p>
<h2>Step 7: Requesting user data from Supabase</h2>
<p>First, install the Supabase client:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>npm install @supabase/supabase-js</span></div></div><br/></code></div></div>
<p>In order to pass an authenticated <code>user_id</code> to Supabase we will package it within a JWT. Install jsonwebtoken:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>npm install jsonwebtoken</span></div></div><br/></code></div></div>
<p>Create a new file <code>utils/supabase.js</code> and add the following:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>// utils/supabase.js</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>import { createClient } from &#x27;@supabase/supabase-js&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>import jwt from &#x27;jsonwebtoken&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>const getSupabase = (userId) =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>  const supabase = createClient(</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>    process.env.NEXT_PUBLIC_SUPABASE_URL,</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>    process.env.NEXT_PUBLIC_SUPABASE_KEY</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>  )</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>  if (userId) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>    const payload = {</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>      userId,</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>      exp: Math.floor(Date.now() / 1000) + 60 * 60,</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>    }</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>    supabase.auth.session = () =&gt; ({</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>      access_token: jwt.sign(payload, process.env.SUPABASE_SIGNING_SECRET),</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>    })</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>  return supabase</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->25</span><div style="display:inline-block;margin-left:16px"><span>export { getSupabase }</span></div></div><br/></code></div></div>
<p>Our payload for the JWT will contain our user&#x27;s unique identifier from Stytch, their <code>user_id</code>. We are signing this JWT using Supabase&#x27;s signing secret, so Supabase will be able to validate it is authentic and hasn&#x27;t been tampered with in transit.</p>
<p>Let&#x27;s load our expenses from Supabase on the home page! Update <code>pages/index.js</code> a final time to make a request for expense data from Supabase.</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>import styles from &#x27;../styles/Home.module.css&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>import StytchLogin from &#x27;../components/StytchLogin&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>import Profile from &#x27;../components/Profile&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>import { getAuthenticatedUserFromSession } from &#x27;../utils/stytchLogic&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>import { getSupabase } from &#x27;../utils/supabase&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>const Index = ({ user, publicToken }) =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>  let content</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>  if (user) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>    content = &lt;Profile user={user} /&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>  } else {</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>    content = &lt;StytchLogin publicToken={publicToken} /&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>  return &lt;div className={styles.main}&gt;{content}&lt;/div&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>export async function getServerSideProps({ req, res }) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>  const userId = await getAuthenticatedUserFromSession(req, res)</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>  if (userId) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>    const supabase = getSupabase(userId)</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>    const { data: expenses } = await supabase.from(&#x27;expenses&#x27;).select(&#x27;*&#x27;)</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>    return {</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>      props: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>        user: { userId, expenses },</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>        publicToken: process.env.STYTCH_PUBLIC_TOKEN,</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>      },</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>    }</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>  } else {</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>    return {</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>      props: { publicToken: process.env.STYTCH_PUBLIC_TOKEN },</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>    }</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->39</span><div style="display:inline-block;margin-left:16px"><span>export default Index</span></div></div><br/></code></div></div>
<p>When we reload our application, we are still getting the empty state for expenses.</p>
<p>This is because we enabled Row Level Security, which blocks all requests by default and lets you granularly control access to the data in your database. To enable our user to select their expenses we need to write a RLS policy.</p>
<h2>Step 8: Write a policy to allow select</h2>
<p>Our policy will need to know who our currently logged in user is to determine whether or not they should have access. Let&#x27;s create a PostgreSQL function to extract the current user from our new JWT.</p>
<p>Navigate back to the Supabase dashboard, select SQL from the sidebar menu, and click <strong>New query</strong>. This will create a new query,, which will allow us to run any SQL against our Postgres database.</p>
<p>Write the following and click <strong>Run</strong>.</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>create or replace function auth.user_id() returns text as $$</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span> select nullif(current_setting(&#x27;request.jwt.claims&#x27;, true)::json-&gt;&gt;&#x27;userId&#x27;, &#x27;&#x27;)::text;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>$$ language sql stable;</span></div></div><br/></code></div></div>
<p>You should see the output <code>Success, no rows returned</code>. This created a function called <code>auth.user_id()</code>, which will inspect the <code>userId</code> field of our JWT payload.</p>
<blockquote>
<p>Note: To learn more about PostgreSQL functions, check out this <a href="https://www.youtube.com/watch?v=MJZCCpCYEqk">deep dive video</a>.</p>
</blockquote>
<p>Let&#x27;s create a policy that checks whether this user is the owner of an expense.</p>
<p>Select <strong>Authentication</strong> from the Supabase sidebar menu, click <strong>Policies</strong>, then <strong>New Policy</strong>.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/14.png" alt="Supabase authentication page"/></p>
<p>From the modal, select <strong>For full customization create a policy from scratch</strong> and add the following.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/15.png" alt="Supabase create policy page"/></p>
<p>This policy is calling the function we just created to get the currently logged in user&#x27;s <code>user_id</code> <code>auth.user_id()</code> and checking whether this matches the <code>user_id</code> column for the current expense. If it does, then it will allow the user to select it, otherwise it will continue to deny.</p>
<p>Click Review and then Save policy. After you&#x27;ve saved, click Enable RLS on the table to enable the policy we just created.</p>
<blockquote>
<p>Note: To learn more about RLS and policies, check out this <a href="https://www.youtube.com/watch?v=Ow_Uzedfohk">video</a>.</p>
</blockquote>
<p>The last thing we need to do is update the <code>user_id</code> columns for our existing expenses.</p>
<p>Head back to the Supabase dashboard, and select Table editor from the sidebar. You will notice each entry has <code>user_id</code> set to <code>NULL</code>. We need to update this value to the proper <code>user_id</code>.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/16.png" alt="Supabase null users in table"/></p>
<p>To get the <code>user_id</code> for our Stytch user, you can pull it from the welcome page in our example app (eg <code>user-test-61497d40-f957-45cd-a6c8-5408d22e93bc</code>).</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/17.png" alt="Get user_id"/></p>
<p>Update each row in Supabase to this <code>user_id</code>.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/18.png" alt="Populate user_id"/></p>
<p>Return to <code>localhost:3000</code>, and you will see your expenses listed.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/19.png" alt="Listed expenses"/></p>
<p>We now have a basic expense tracker application powered by Stytch, Supabase, and Next.js. From here you could add additional features like adding, editing, and organizing your expenses further.</p>
<blockquote>
<p>Note: You can find a completed version of this project on <a href="https://github.com/stytchauth/stytch-nextjs-supabase">Github</a>.</p>
</blockquote>
<h2>Optional: Add Google One Tap</h2>
<p>In this optional step, we will extend our application to allow users to login with Google One Tap in addition to Email magic links.</p>
<p>You will need to follow the first four steps of <a href="https://stytch.com/docs/oauth?utm_source=supabase&amp;utm_medium=guide#guides_google-sdk">this guide</a> to create a Google project, set up Google OAuth consent, and configure credentials and redirect URLs.</p>
<p>First, we will make some adjustments to the <code>StytchLogin</code> component. We will update the configuration, so that it uses both Google OAuth, and Email magic links.</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>// components/StytchLogin.js</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>import React from &#x27;react&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>import { Stytch } from &#x27;@stytch/stytch-react&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>const stytchConfig = {</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>  loginOrSignupView: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>    products: [&#x27;oauth&#x27;, &#x27;emailMagicLinks&#x27;],</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>    oauthOptions: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>      providers: [</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>        {</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>          type: &#x27;google&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>          one_tap: true,</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>          position: &#x27;embedded&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>        },</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>      ],</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>      loginRedirectURL: &#x27;http://localhost:3000/api/authenticate?type=oauth&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>      signupRedirectURL: &#x27;http://localhost:3000/api/authenticate?type=oauth&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>    },</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>    emailMagicLinksOptions: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>      loginRedirectURL: &#x27;http://localhost:3000/api/authenticate&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>      loginExpirationMinutes: 30,</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>      signupRedirectURL: &#x27;http://localhost:3000/api/authenticate&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>      signupExpirationMinutes: 30,</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>      createUserAsPending: true,</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>    },</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>  },</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>  style: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>    fontFamily: &#x27;&quot;Helvetica New&quot;, Helvetica, sans-serif&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>    width: &#x27;321px&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>    primaryColor: &#x27;#0577CA&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>  },</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>const StytchLogin = ({ publicToken }) =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>  return (</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>    &lt;Stytch</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>      publicToken={publicToken}</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>      loginOrSignupView={stytchConfig.loginOrSignupView}</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>      style={stytchConfig.style}</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>    /&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>  )</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->44</span><div style="display:inline-block;margin-left:16px"><span>export default StytchLogin</span></div></div><br/></code></div></div>
<p>We also need to make an adjustment to the function <code>authenticateTokenStartSession</code> in <code>stytchLogic.js</code>. Stytch has separate authentication endpoints for Email magic links and OAuth, so we need to route our token correctly.</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>// utils/stytchLogic.js</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>// leave the rest of the file contents as is</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>export const authenticateTokenStartSession = async (req, res) =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>  const { token, type } = req.query</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>  let sessionToken</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>  try {</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>    const stytchClient = loadStytch()</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>    if (type == &#x27;oauth&#x27;) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>      const resp = await stytchClient.oauth.authenticate(token, {</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>        session_duration_minutes: 30,</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>        session_management_type: &#x27;stytch&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>      })</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>      sessionToken = resp.session.stytch_session.session_token</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>    } else {</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>      const resp = await stytchClient.magicLinks.authenticate(token, {</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>        session_duration_minutes: 30,</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>      })</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>      sessionToken = resp.session_token</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>    }</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>  } catch (error) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>    console.log(error)</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>    const errorString = JSON.stringify(error)</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>    return res.status(400).json({ errorString })</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>  setCookies(SESSION_COOKIE, sessionToken, {</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>    req,</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>    res,</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>    maxAge: 60 * 60 * 24,</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>    secure: true,</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>  })</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>  return res.redirect(&#x27;/&#x27;)</span></div></div><div><span class="ch-code-line-number">_<!-- -->35</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<p>With these two changes you will now have a working Google One Tap authentication method along with email magic links.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/20.png" alt="Google One Tap"/></p>
<h2>Resources</h2>
<ul>
<li><a href="https://stytch.com/blog?utm_source=supabase&amp;utm_medium=guide">Stytch blog</a></li>
<li><a href="https://stytch.com/docs?utm_source=supabase&amp;utm_medium=guide">Stytch documentation</a></li>
</ul></div></div><div class="lg:col-span-3"><div class="sticky top-20 flex flex-col gap-4"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Details</h2><div class="text-foreground divide-y"><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Developer</span><span class="text-foreground">Stytch</span></div><div class="flex items-center justify-between py-2"><span class="text-lighter">Category</span><a class="text-brand hover:underline transition-colors" href="../integrations.html#auth">Auth</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Website</span><a href="https://stytch.com/?utm_source=supabase&amp;utm_medium=partner-gallery" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors">stytch.com</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Documentation</span><a href="https://stytch.com/?utm_source=supabase&amp;utm_medium=partner-gallery" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors"><span class="flex items-center space-x-1"><span>Learn</span><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></span></a></div></div><p class="text-foreground-light text-sm">Third-party integrations and docs are managed by Supabase partners.</p></div></div></div></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"partner":{"id":13,"slug":"stytch","type":"technology","category":"Auth","developer":"Stytch","title":"Stytch","description":"Stytch provides an all-in-one platform for passwordless auth.","logo":"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/stytch-logo.png","images":["https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/stytch-1.png","https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/stytch-2.png","https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/stytch-3.png"],"overview":"Stytch provides an all-in-one platform for passwordless auth. Stytch makes it easy for you to embed passwordless solutions into your websites and apps for better security, better conversion rates, and a better end user experience. Their easy-to-use SDKs and direct API access allows for maximum control and customization. In this example we will use Email magic links to create and log in our users, and Session management. There is an additional, optional step to enable Google One Tap which is an especially high-converting Google OAuth sign-up and login flow.\n\n## Documentation\n\nIn this guide we will build a simple expense tracker web application using Stytch, Supabase, and Next.js.\n\n[Stytch](https://stytch.com?utm_source=supabase\u0026utm_medium=guide) provides an all-in-one platform for passwordless auth. Stytch makes it easy for you to embed passwordless solutions into your websites and apps for better security, better conversion rates, and a better end user experience. Their easy-to-use SDKs and direct API access allows for maximum control and customization. In this example we will use [Email magic links](https://stytch.com/products/email-magic-links?utm_source=supabase\u0026utm_medium=guide) to create and log in our users, and Session management. There is an additional, optional step to enable [Google One Tap](https://stytch.com/blog/improving-conversion-with-google-one-tap?utm_source=supabase\u0026utm_medium=guide) which is an especially high-converting Google OAuth sign-up and login flow.\n\nWe will leverage Supabase to store and authorize access to user data. Supabase makes it simple to set up Row Level Security (RLS) policies which ensure users can only read and write data that they are authorized to do so. If you do not already have a Supabase account, you will need to create one.\n\nThis guide will use [Next.js](https://nextjs.org/) which is a web application framework built on top of React. Stytch provides a [Node.js library](https://github.com/stytchauth/stytch-node) and a [React library](https://stytch.com/docs/sdks/javascript-sdk) which makes building Next.js apps super easy.\n\n\u003e Note: You can find a completed version of this project on [Github](https://github.com/stytchauth/stytch-nextjs-supabase).\n\n## Step 0: Create a Stytch Account\n\nIf you already have a Stytch account you may skip this step.\n\nGo to [Stytch](https://stytch.com?utm_source=supabase\u0026utm_medium=guide), and create an account. Note that Stytch provides two ways to create an account, either via Google OAuth, or through Email magic links.  This is the same user experience we will be building in this guide!\n\n![Stytch redirect URL settings](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/01.png)\n\n## Step 1: Set up Stytch redirect URLs\n\nFirst we need to add the redirect URLs that will be used during the Email magic link flow. This step helps ensure bad actors cannot spoof your magic links and hijack redirects.\n\nNavigate to your [redirect URL settings](https://stytch.com/dashboard/redirect-urls?utm_source=supabase\u0026utm_medium=guide) in the Stytch dashboard, and under **Test environment** create an entry where the **URL** is `http://localhost:3000/api/authenticate` and the **Type** is `All`.\n\n![Edit Stytch redirect URL settings](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/02.png)\n\nAfter pressing **Confirm**, the redirect URLs dashboard will update to show your new entry. We will use this URL later on.\n\n![Stytch redirect URL settings](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/03.png)\n\n## Step 2: Create a Supabase project\n\nFrom your [Supabase dashboard](https://supabase.com/dashboard/), click **New project**.\n\nEnter a `Name` for your Supabase project.\n\nEnter a secure `Database Password`.\n\nClick **Create new project**. It may take a couple minutes for your project to be provisioned.\n\n![New Supabase project settings](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/04.png)\n\n## Step 3: Creating data in Supabase\n\nOnce your Supabase project is provisioned, click Table editor, then New table. This tool is available from the sidebar menu in the [Supabase dashboard](https://supabase.com/dashboard/).\n\nEnter `expenses` as the **Name** field.\n\nSelect `Enable Row Level Security (RLS)`.\n\nAdd three new columns:\n\n- `user_id` as `text`\n\n- `title` as `text`\n\n- `value` as `float8`\n\nClick **Save** to create the new table.\n\n![Creating a new table](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/05.png)\n\nFrom the Table editor view, select the expenses table and click **Insert row**.\n\nFill out the title and value fields (leave user_id blank for now) and click **Save**.\n\n![Creating a new row](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/06.png)\n\nUse **Insert Row** to further populate the table with expenses.\n\n![Multiple rows](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/07.png)\n\n## Step 4: Building a Next.js app\n\nUsing a terminal, create a new Next.js project:\n\n```bash\nnpx create-next-app stytch-supabase-example\n```\n\nNext, within `stytch-supabase-example` create a `.env.local` file and enter the following values:\n\n```\nSTYTCH_PROJECT_ENV=test\nSTYTCH_PROJECT_ID=GET_FROM_STYTCH_DASHBOARD\nSTYTCH_PUBLIC_TOKEN=GET_FROM_STYTCH_DASHBOARD\nSTYTCH_SECRET=GET_FROM_STYTCH_DASHBOARD\nNEXT_PUBLIC_SUPABASE_URL=GET_FROM_SUPABASE_DASHBOARD\nNEXT_PUBLIC_SUPABASE_KEY=GET_FROM_SUPABASE_DASHBOARD\nSUPABASE_SIGNING_SECRET=GET_FROM_SUPABASE_DASHBOARD\n```\n\n\u003e Note: Stytch values can be found in the project [dashboard](https://stytch.com/dashboard/api-keys?utm_source=supabase\u0026utm_medium=guide) under **API Keys**.\n\n![Stytch API keys](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/08.png)\n\n\u003e Note: Supabase values can be found under **Settings** \u003e **API** for your project.\n\n![Supabase API keys](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/09.png)\n\nStart your Next.js development server to read in the new values from `.env.local`.\n\n```bash\nnpm run dev\n```\n\nYou should have a running Next.js application on `localhost:3000`.\n\n## Step 5: Build the Login Form\n\nNow we will replace the default Next.js home page with a login UI. We will use the Stytch React library.\n\n\u003e Note: Stytch provides direct API access for those that want to build login UI themselves\n\nInstall the `@stytch/stytch-react` library.\n\n```bash\nnpm install @stytch/stytch-react\n```\n\nIn the root directory, create a new folder named `components` and file in that folder named `/StytchLogin.js`. Within this file, paste the snippet below. This will configure, and style the Stytch React component to use Email magic links.\n\n```jsx\n// components/StytchLogin.js\nimport React from 'react'\nimport { Stytch } from '@stytch/stytch-react'\n\nconst stytchConfig = {\n  loginOrSignupView: {\n    products: ['emailMagicLinks'],\n    emailMagicLinksOptions: {\n      loginRedirectURL: 'http://localhost:3000/api/authenticate',\n      loginExpirationMinutes: 30,\n      signupRedirectURL: 'http://localhost:3000/api/authenticate',\n      signupExpirationMinutes: 30,\n      createUserAsPending: true,\n    },\n  },\n  style: {\n    fontFamily: '\"Helvetica New\", Helvetica, sans-serif',\n    width: '321px',\n    primaryColor: '#0577CA',\n  },\n}\n\nconst StytchLogin = ({ publicToken }) =\u003e {\n  return (\n    \u003cStytch\n      publicToken={publicToken}\n      loginOrSignupView={stytchConfig.loginOrSignupView}\n      style={stytchConfig.style}\n    /\u003e\n  )\n}\n\nexport default StytchLogin\n```\n\nAdditionally, create a profile component by creating a file called `Profile.js` in `/components`. We will use this component to render our expenses stored in Supabase later on.\n\n```jsx\n// components/Profile.js\nimport React from 'react'\nimport Link from 'next/link'\n\nexport default function Profile({ user }) {\n  return (\n    \u003cdiv\u003e\n      \u003ch1\u003eWelcome {user.userId}\u003c/h1\u003e\n      \u003ch2\u003eYour expenses\u003c/h2\u003e\n      {user.expenses?.length \u003e 0 ? (\n        user.expenses.map((expense) =\u003e (\n          \u003cp key={expense.id}\u003e\n            {expense.title}: ${expense.value}\n          \u003c/p\u003e\n        ))\n      ) : (\n        \u003cp\u003eYou have no expenses!\u003c/p\u003e\n      )}\n\n      \u003cLink href=\"/api/logout\" passHref\u003e\n        \u003cbutton\u003e\n          \u003ca\u003eLogout\u003c/a\u003e\n        \u003c/button\u003e\n      \u003c/Link\u003e\n    \u003c/div\u003e\n  )\n}\n```\n\nFinally, replace the contents of the file `/pages/index.js` to render our new `StytchLogin` and `Profile` components.\n\n```jsx\n// pages/index.js\nimport styles from '../styles/Home.module.css'\nimport Profile from '../components/Profile'\nimport StytchLogin from '../components/StytchLogin'\n\nconst Index = ({ user, publicToken }) =\u003e {\n  let content\n  if (user) {\n    content = \u003cProfile user={user} /\u003e\n  } else {\n    content = \u003cStytchLogin publicToken={publicToken} /\u003e\n  }\n\n  return \u003cdiv className={styles.main}\u003e{content}\u003c/div\u003e\n}\n\nexport async function getServerSideProps({ req, res }) {\n  const user = null // Will update later\n  return {\n    props: { user, publicToken: process.env.STYTCH_PUBLIC_TOKEN },\n  }\n}\n\nexport default Index\n```\n\nOn `localhost:3000` there is now a login form prompting for your email address.\n\n![Email login step one](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/10.png)\n\nEnter your email address and press **Continue with email**.\n\n![Email login step two](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/11.png)\n\nIn your inbox you will find a login request from your app.\n\n![Email login step three](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/12.png)\n\nHowever, if you click the link in the email you will get a 404. We need to build an API route to handle the email magic link authentication.\n\n## Step 6: Authenticate and start a session\n\nTo make authentication easier we will use the Stytch Node.js library. Run\n\n```bash\nnpm install stytch\n```\n\nAdditionally, we will need to store the authenticated session in a cookie. Run\n\n```bash\nnpm install cookies-next\n```\n\nCreate a new folder named `utils` and inside a file named`stytchLogic.js` with the following contents\n\n```jsx\n// utils/stytchLogic.js\nimport * as stytch from 'stytch'\nimport { getCookie, setCookies, removeCookies } from 'cookies-next'\n\nexport const SESSION_COOKIE = 'stytch_cookie'\n\nlet client\nconst loadStytch = () =\u003e {\n  if (!client) {\n    client = new stytch.Client({\n      project_id: process.env.STYTCH_PROJECT_ID,\n      secret: process.env.STYTCH_SECRET,\n      env: process.env.STYTCH_PROJECT_ENV === 'live' ? stytch.envs.live : stytch.envs.test,\n    })\n  }\n\n  return client\n}\n\nexport const getAuthenticatedUserFromSession = async (req, res) =\u003e {\n  const sessionToken = getCookie(SESSION_COOKIE, { req, res })\n  if (!sessionToken) {\n    return null\n  }\n\n  try {\n    const stytchClient = loadStytch()\n    const resp = await stytchClient.sessions.authenticate({\n      session_token: sessionToken,\n    })\n    return resp.session.user_id\n  } catch (error) {\n    console.log(error)\n    return null\n  }\n}\n\nexport const revokeAndClearSession = async (req, res) =\u003e {\n  const sessionToken = getCookie(SESSION_COOKIE, { req, res })\n\n  if (sessionToken) {\n    try {\n      const stytchClient = loadStytch()\n      await stytchClient.sessions.revoke({\n        session_token: sessionToken,\n      })\n    } catch (error) {\n      console.log(error)\n    }\n    removeCookies(SESSION_COOKIE, { req, res })\n  }\n\n  return res.redirect('/')\n}\n\nexport const authenticateTokenStartSession = async (req, res) =\u003e {\n  const { token, type } = req.query\n  let sessionToken\n  try {\n    const stytchClient = loadStytch()\n    const resp = await stytchClient.magicLinks.authenticate(token, {\n      session_duration_minutes: 30,\n    })\n    sessionToken = resp.session_token\n  } catch (error) {\n    console.log(error)\n    const errorString = JSON.stringify(error)\n    return res.status(400).json({ errorString })\n  }\n\n  setCookies(SESSION_COOKIE, sessionToken, {\n    req,\n    res,\n    maxAge: 60 * 60 * 24,\n    secure: true,\n  })\n\n  return res.redirect('/')\n}\n```\n\nThis logic is responsible for setting up the Stytch client we will use to call the API. It provides functions we will use to login, logout, and validate user sessions.\n\nIn order to complete the email login flow, create a new file `pages/api/authenticate.js` with the contents:\n\n```jsx\n// pages/api/authenticate.js\nimport { authenticateTokenStartSession } from '../../utils/stytchLogic'\n\nexport default async function handler(req, res) {\n  return authenticateTokenStartSession(req, res)\n}\n```\n\nWe will also create a logout API endpoint with similar contents. In `pages/api/logout.js` include the following:\n\n```jsx\n// pages/api/logout.js\nimport { revokeAndClearSession } from '../../utils/stytchLogic'\n\nexport default async function handler(req, res) {\n  return revokeAndClearSession(req, res)\n}\n```\n\nFinally, update `pages/index.js` by importing `getAuthenticatedUserFromSession`, and calling it to set the user variable in `getServerSideProps`.\n\n```jsx\n// pages/index.js\nimport styles from '../styles/Home.module.css'\n\nimport StytchLogin from '../components/StytchLogin'\nimport Profile from '../components/Profile'\nimport { getAuthenticatedUserFromSession } from '../utils/stytchLogic'\n\nconst Index = ({ user, publicToken }) =\u003e {\n  let content\n  if (user) {\n    content = \u003cProfile user={user} /\u003e\n  } else {\n    content = \u003cStytchLogin publicToken={publicToken} /\u003e\n  }\n\n  return \u003cdiv className={styles.main}\u003e{content}\u003c/div\u003e\n}\n\nexport async function getServerSideProps({ req, res }) {\n  const userId = await getAuthenticatedUserFromSession(req, res)\n  if (userId) {\n    return {\n      props: { user: { userId }, publicToken: process.env.STYTCH_PUBLIC_TOKEN },\n    }\n  }\n  return {\n    props: { publicToken: process.env.STYTCH_PUBLIC_TOKEN },\n  }\n}\n\nexport default Index\n```\n\nReturn to `localhost:3000`, and login again by sending yourself a new email. Upon clicking through in the email you should be presented with “Welcome $USER_ID”. If you refresh the page, you should remain in an authenticated state. If you press **Logout** then you should return to the login screen.\n\n![Profile page](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/13.png)\n\nNow that we have a working login flow with persistent authentication it is time to pull in our expense data from Supabase.\n\n## Step 7: Requesting user data from Supabase\n\nFirst, install the Supabase client:\n\n```bash\nnpm install @supabase/supabase-js\n```\n\nIn order to pass an authenticated `user_id` to Supabase we will package it within a JWT. Install jsonwebtoken:\n\n```bash\nnpm install jsonwebtoken\n```\n\nCreate a new file `utils/supabase.js` and add the following:\n\n```jsx\n// utils/supabase.js\nimport { createClient } from '@supabase/supabase-js'\nimport jwt from 'jsonwebtoken'\n\nconst getSupabase = (userId) =\u003e {\n  const supabase = createClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL,\n    process.env.NEXT_PUBLIC_SUPABASE_KEY\n  )\n\n  if (userId) {\n    const payload = {\n      userId,\n      exp: Math.floor(Date.now() / 1000) + 60 * 60,\n    }\n\n    supabase.auth.session = () =\u003e ({\n      access_token: jwt.sign(payload, process.env.SUPABASE_SIGNING_SECRET),\n    })\n  }\n\n  return supabase\n}\n\nexport { getSupabase }\n```\n\nOur payload for the JWT will contain our user's unique identifier from Stytch, their `user_id`. We are signing this JWT using Supabase's signing secret, so Supabase will be able to validate it is authentic and hasn't been tampered with in transit.\n\nLet's load our expenses from Supabase on the home page! Update `pages/index.js` a final time to make a request for expense data from Supabase.\n\n```jsx\nimport styles from '../styles/Home.module.css'\n\nimport StytchLogin from '../components/StytchLogin'\nimport Profile from '../components/Profile'\nimport { getAuthenticatedUserFromSession } from '../utils/stytchLogic'\nimport { getSupabase } from '../utils/supabase'\n\nconst Index = ({ user, publicToken }) =\u003e {\n  let content\n  if (user) {\n    content = \u003cProfile user={user} /\u003e\n  } else {\n    content = \u003cStytchLogin publicToken={publicToken} /\u003e\n  }\n\n  return \u003cdiv className={styles.main}\u003e{content}\u003c/div\u003e\n}\n\nexport async function getServerSideProps({ req, res }) {\n  const userId = await getAuthenticatedUserFromSession(req, res)\n\n  if (userId) {\n    const supabase = getSupabase(userId)\n    const { data: expenses } = await supabase.from('expenses').select('*')\n\n    return {\n      props: {\n        user: { userId, expenses },\n        publicToken: process.env.STYTCH_PUBLIC_TOKEN,\n      },\n    }\n  } else {\n    return {\n      props: { publicToken: process.env.STYTCH_PUBLIC_TOKEN },\n    }\n  }\n}\n\nexport default Index\n```\n\nWhen we reload our application, we are still getting the empty state for expenses.\n\nThis is because we enabled Row Level Security, which blocks all requests by default and lets you granularly control access to the data in your database. To enable our user to select their expenses we need to write a RLS policy.\n\n## Step 8: Write a policy to allow select\n\nOur policy will need to know who our currently logged in user is to determine whether or not they should have access. Let's create a PostgreSQL function to extract the current user from our new JWT.\n\nNavigate back to the Supabase dashboard, select SQL from the sidebar menu, and click **New query**. This will create a new query,, which will allow us to run any SQL against our Postgres database.\n\nWrite the following and click **Run**.\n\n```sql\ncreate or replace function auth.user_id() returns text as $$\n select nullif(current_setting('request.jwt.claims', true)::json-\u003e\u003e'userId', '')::text;\n$$ language sql stable;\n```\n\nYou should see the output `Success, no rows returned`. This created a function called `auth.user_id()`, which will inspect the `userId` field of our JWT payload.\n\n\u003e Note: To learn more about PostgreSQL functions, check out this [deep dive video](https://www.youtube.com/watch?v=MJZCCpCYEqk).\n\nLet's create a policy that checks whether this user is the owner of an expense.\n\nSelect **Authentication** from the Supabase sidebar menu, click **Policies**, then **New Policy**.\n\n![Supabase authentication page](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/14.png)\n\nFrom the modal, select **For full customization create a policy from scratch** and add the following.\n\n![Supabase create policy page](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/15.png)\n\nThis policy is calling the function we just created to get the currently logged in user's `user_id` `auth.user_id()` and checking whether this matches the `user_id` column for the current expense. If it does, then it will allow the user to select it, otherwise it will continue to deny.\n\nClick Review and then Save policy. After you've saved, click Enable RLS on the table to enable the policy we just created.\n\n\u003e Note: To learn more about RLS and policies, check out this [video](https://www.youtube.com/watch?v=Ow_Uzedfohk).\n\nThe last thing we need to do is update the `user_id` columns for our existing expenses.\n\nHead back to the Supabase dashboard, and select Table editor from the sidebar. You will notice each entry has `user_id` set to `NULL`. We need to update this value to the proper `user_id`.\n\n![Supabase null users in table](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/16.png)\n\nTo get the `user_id` for our Stytch user, you can pull it from the welcome page in our example app (eg `user-test-61497d40-f957-45cd-a6c8-5408d22e93bc`).\n\n![Get user_id](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/17.png)\n\nUpdate each row in Supabase to this `user_id`.\n\n![Populate user_id](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/18.png)\n\nReturn to `localhost:3000`, and you will see your expenses listed.\n\n![Listed expenses](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/19.png)\n\nWe now have a basic expense tracker application powered by Stytch, Supabase, and Next.js. From here you could add additional features like adding, editing, and organizing your expenses further.\n\n\u003e Note: You can find a completed version of this project on [Github](https://github.com/stytchauth/stytch-nextjs-supabase).\n\n## Optional: Add Google One Tap\n\nIn this optional step, we will extend our application to allow users to login with Google One Tap in addition to Email magic links.\n\nYou will need to follow the first four steps of [this guide](https://stytch.com/docs/oauth?utm_source=supabase\u0026utm_medium=guide#guides_google-sdk) to create a Google project, set up Google OAuth consent, and configure credentials and redirect URLs.\n\nFirst, we will make some adjustments to the `StytchLogin` component. We will update the configuration, so that it uses both Google OAuth, and Email magic links.\n\n```jsx\n// components/StytchLogin.js\nimport React from 'react'\nimport { Stytch } from '@stytch/stytch-react'\n\nconst stytchConfig = {\n  loginOrSignupView: {\n    products: ['oauth', 'emailMagicLinks'],\n    oauthOptions: {\n      providers: [\n        {\n          type: 'google',\n          one_tap: true,\n          position: 'embedded',\n        },\n      ],\n      loginRedirectURL: 'http://localhost:3000/api/authenticate?type=oauth',\n      signupRedirectURL: 'http://localhost:3000/api/authenticate?type=oauth',\n    },\n    emailMagicLinksOptions: {\n      loginRedirectURL: 'http://localhost:3000/api/authenticate',\n      loginExpirationMinutes: 30,\n      signupRedirectURL: 'http://localhost:3000/api/authenticate',\n      signupExpirationMinutes: 30,\n      createUserAsPending: true,\n    },\n  },\n  style: {\n    fontFamily: '\"Helvetica New\", Helvetica, sans-serif',\n    width: '321px',\n    primaryColor: '#0577CA',\n  },\n}\n\nconst StytchLogin = ({ publicToken }) =\u003e {\n  return (\n    \u003cStytch\n      publicToken={publicToken}\n      loginOrSignupView={stytchConfig.loginOrSignupView}\n      style={stytchConfig.style}\n    /\u003e\n  )\n}\n\nexport default StytchLogin\n```\n\nWe also need to make an adjustment to the function `authenticateTokenStartSession` in `stytchLogic.js`. Stytch has separate authentication endpoints for Email magic links and OAuth, so we need to route our token correctly.\n\n```jsx\n// utils/stytchLogic.js\n\n// leave the rest of the file contents as is\nexport const authenticateTokenStartSession = async (req, res) =\u003e {\n  const { token, type } = req.query\n  let sessionToken\n  try {\n    const stytchClient = loadStytch()\n    if (type == 'oauth') {\n      const resp = await stytchClient.oauth.authenticate(token, {\n        session_duration_minutes: 30,\n        session_management_type: 'stytch',\n      })\n      sessionToken = resp.session.stytch_session.session_token\n    } else {\n      const resp = await stytchClient.magicLinks.authenticate(token, {\n        session_duration_minutes: 30,\n      })\n      sessionToken = resp.session_token\n    }\n  } catch (error) {\n    console.log(error)\n    const errorString = JSON.stringify(error)\n    return res.status(400).json({ errorString })\n  }\n\n  setCookies(SESSION_COOKIE, sessionToken, {\n    req,\n    res,\n    maxAge: 60 * 60 * 24,\n    secure: true,\n  })\n\n  return res.redirect('/')\n}\n```\n\nWith these two changes you will now have a working Google One Tap authentication method along with email magic links.\n\n![Google One Tap](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/20.png)\n\n## Resources\n\n- [Stytch blog](https://stytch.com/blog?utm_source=supabase\u0026utm_medium=guide)\n- [Stytch documentation](https://stytch.com/docs?utm_source=supabase\u0026utm_medium=guide)\n","website":"https://stytch.com/?utm_source=supabase\u0026utm_medium=partner-gallery","docs":"https://stytch.com/?utm_source=supabase\u0026utm_medium=partner-gallery","contact":26,"approved":true,"created_at":"2022-03-27T14:47:43.162405+00:00","tsv":"'/)':304C '/../utils/stytchlogic':1502C,1535C '/blog/improving-conversion-with-google-one-tap?utm_source=supabase\u0026utm_medium=guide)':226C '/blog?utm_source=supabase\u0026utm_medium=guide)':2795C '/components':1011C '/components/profile':1086C,1576C,1889C '/components/stytchlogin':1090C,1572C,1885C '/dashboard/),':542C '/dashboard/).':613C '/dashboard/api-keys?utm_source=supabase\u0026utm_medium=guide)':803C '/dashboard/redirect-urls?utm_source=supabase\u0026utm_medium=guide)':471C '/docs/oauth?utm_source=supabase\u0026utm_medium=guide#guides_google-sdk)':2529C '/docs/sdks/javascript-sdk)':330C '/docs?utm_source=supabase\u0026utm_medium=guide)':2800C '/pages/index.js':1068C '/products/email-magic-links?utm_source=supabase\u0026utm_medium=guide)':202C '/storage/v1/object/public/images/integrations/stytch/documentation/01.png)':423C '/storage/v1/object/public/images/integrations/stytch/documentation/02.png)':501C '/storage/v1/object/public/images/integrations/stytch/documentation/03.png)':529C '/storage/v1/object/public/images/integrations/stytch/documentation/04.png)':580C '/storage/v1/object/public/images/integrations/stytch/documentation/05.png)':653C '/storage/v1/object/public/images/integrations/stytch/documentation/06.png)':689C '/storage/v1/object/public/images/integrations/stytch/documentation/07.png)':704C '/storage/v1/object/public/images/integrations/stytch/documentation/08.png)':812C '/storage/v1/object/public/images/integrations/stytch/documentation/09.png)':830C '/storage/v1/object/public/images/integrations/stytch/documentation/10.png)':1155C '/storage/v1/object/public/images/integrations/stytch/documentation/11.png)':1171C '/storage/v1/object/public/images/integrations/stytch/documentation/12.png)':1190C '/storage/v1/object/public/images/integrations/stytch/documentation/13.png)':1689C '/storage/v1/object/public/images/integrations/stytch/documentation/14.png)':2201C '/storage/v1/object/public/images/integrations/stytch/documentation/15.png)':2224C '/storage/v1/object/public/images/integrations/stytch/documentation/16.png)':2374C '/storage/v1/object/public/images/integrations/stytch/documentation/17.png)':2410C '/storage/v1/object/public/images/integrations/stytch/documentation/18.png)':2425C '/storage/v1/object/public/images/integrations/stytch/documentation/19.png)':2441C '/storage/v1/object/public/images/integrations/stytch/documentation/20.png)':2789C '/styles/home.module.css':1082C,1568C,1881C '/stytchauth/stytch-nextjs-supabase).':352C,2485C '/stytchauth/stytch-node)':323C '/stytchlogin.js':923C '/utils/stytchlogic':1580C,1893C '/utils/supabase':1897C '/watch?v=mjzccpcyeqk).':2167C '/watch?v=ow_uzedfohk).':2314C '0':354C,1048C '0577ca':984C,2631C '1':425C '1000':1792C '2':531C '24':1446C,2759C '3':582C '30':965C,970C,1420C,2612C,2617C,2716C,2733C '3000':857C,1137C,1637C,2429C '3000/api/authenticate':488C,963C,968C,2599C,2604C,2610C,2615C '321px':982C,2629C '4':706C '400':1434C,2747C '404':1204C '45cd':2402C '5':859C '5408d22e93bc':2404C '6':1220C '60':1444C,1445C,1793C,1794C,2757C,2758C '61497d40':2400C '7':1713C '8':2020C 'a6c8':2403C 'abl':1838C 'access':60C,184C,250C,889C,1796C,1997C,2048C 'account':290C,358C,365C,384C,394C 'actor':454C 'ad':2464C 'add':435C,626C,1757C,2215C,2460C,2487C 'addit':89C,216C,999C,1242C,2461C,2510C 'address':1148C,1159C 'adjust':2551C,2652C 'all-in-on':5B,16C,140C 'allow':61C,185C,2025C,2088C,2265C,2501C 'along':2779C 'alreadi':286C,361C 'also':1516C,2647C 'api':59C,183C,805C,808C,821C,826C,888C,1210C,1467C,1520C 'app':38C,162C,335C,710C,724C,1183C,2395C 'applic':124C,309C,854C,1968C,2449C,2499C 'async':1114C,1327C,1364C,1399C,1505C,1538C,1604C,1921C,2692C 'auth':12B,23C,147C,2801 'auth.user':2109C,2140C,2244C 'authent':1218C,1221C,1228C,1249C,1671C,1700C,1733C,1843C,2185C,2197C,2662C,2777C 'authenticatetokenstartsess':1398C,1500C,1511C,2656C,2691C 'author':249C,278C 'avail':602C 'await':1347C,1380C,1414C,1611C,1928C,1941C,2710C,2727C 'back':2065C,2333C 'bad':453C 'bash':719C,844C,903C,1238C,1255C,1724C,1747C 'basic':2446C 'better':40C,42C,47C,164C,166C,171C 'blank':677C 'block':1987C 'blog':2792C 'build':118C,333C,413C,707C,860C,895C,1208C 'built':311C 'call':1008C,1465C,1554C,2139C,2228C 'cannot':455C 'catch':1355C,1385C,1424C,2737C 'chang':2767C 'check':2159C,2174C,2247C,2308C 'classnam':1110C,1600C,1917C 'click':543C,558C,593C,640C,664C,681C,1194C,1648C,2077C,2102C,2191C,2277C,2287C 'client':1299C,1303C,1304C,1323C,1460C,1723C 'column':629C,2254C,2327C 'complet':343C,1484C,2476C 'compon':916C,939C,1003C,1016C,1076C,2555C 'components/profile.js':1027C 'components/stytchlogin.js':946C,2573C 'configur':933C,2541C,2560C 'confirm':504C 'consent':2539C 'console.log':1357C,1387C,1426C,2739C 'const':955C,985C,1091C,1119C,1293C,1300C,1325C,1330C,1342C,1345C,1362C,1367C,1377C,1397C,1402C,1409C,1412C,1428C,1581C,1609C,1770C,1773C,1786C,1898C,1926C,1934C,1938C,2582C,2632C,2690C,2695C,2702C,2708C,2725C,2741C 'contain':1813C 'content':1064C,1096C,1099C,1104C,1112C,1276C,1496C,1524C,1586C,1589C,1594C,1602C,1903C,1906C,1911C,1919C,2686C 'continu':1162C,2274C 'control':64C,188C,1996C 'convers':43C,167C 'convert':103C,233C 'cooki':1253C,1259C,1290C,1295C,1297C,1334C,1371C,1391C,1439C,2752C 'cookies-next':1258C,1289C 'correct':2677C 'could':2459C 'coupl':566C 'creat':77C,204C,295C,355C,382C,392C,480C,532C,559C,583C,643C,647C,683C,714C,722C,735C,911C,1000C,1005C,1261C,1489C,1517C,1751C,2051C,2082C,2105C,2136C,2170C,2209C,2219C,2233C,2299C,2531C 'create-next-app':721C 'createcli':1763C,1775C 'createuseraspend':971C,2618C 'credenti':2542C 'current':2035C,2058C,2116C,2237C,2257C 'custom':66C,190C,2208C 'dashboard':475C,508C,539C,610C,754C,761C,767C,775C,783C,790C,800C,2069C,2337C 'data':253C,274C,584C,1709C,1716C,1874C,1939C,2000C 'databas':556C,2003C,2097C 'date.now':1791C 'deep':2162C 'default':869C,997C,1037C,1133C,1504C,1537C,1632C,1962C,1991C,2644C 'deni':2276C 'determin':2041C 'dev':847C 'develop':834C 'direct':58C,182C,887C 'directori':910C 'div':1109C,1599C,1916C 'dive':2163C 'document':112C,2797C 'durat':1418C,2714C,2731C 'easi':27C,53C,151C,177C,337C 'easier':1229C 'easy-to-us':52C,176C 'edit':494C,2465C 'editor':595C,657C,2341C 'eg':2396C 'either':395C 'els':1103C,1593C,1910C,1954C,2724C 'email':73C,197C,401C,445C,942C,1147C,1149C,1158C,1164C,1165C,1184C,1199C,1215C,1486C,1646C,1652C,2512C,2569C,2665C,2781C 'emailmagiclink':959C,2587C 'emailmagiclinksopt':960C,2607C 'emb':31C,155C 'embed':2596C 'empti':1974C 'enabl':93C,220C,621C,1982C,2005C,2288C,2294C 'end':48C,172C 'endpoint':1521C,2663C 'ensur':267C,452C 'enter':546C,553C,614C,740C,1156C 'entri':482C,515C,2349C 'env':746C,1315C,1318C 'env.local':737C,843C 'environ':479C 'error':1356C,1358C,1386C,1388C,1425C,1427C,1431C,2738C,2740C,2744C 'errorstr':1429C,1436C,2742C,2749C 'especi':100C,230C 'exampl':69C,193C,728C,734C,2394C 'exist':2330C 'exp':1789C 'expens':121C,615C,661C,699C,1020C,1045C,1050C,1059C,1708C,1856C,1873C,1940C,1943C,1949C,1977C,2011C,2183C,2258C,2331C,2435C,2438C,2447C,2469C 'expense.id':1053C 'expense.title':1054C 'expense.value':1055C 'experi':50C,174C,409C 'export':996C,1036C,1113C,1132C,1292C,1324C,1361C,1396C,1503C,1536C,1603C,1631C,1805C,1920C,1961C,2643C,2689C 'extend':2497C 'extract':2056C 'f957':2401C 'featur':2462C 'field':619C,673C,2147C 'file':738C,918C,926C,1007C,1067C,1270C,1492C,1754C,2685C 'fill':667C 'final':1061C,1547C,1866C 'find':341C,1177C,2474C 'first':431C,1719C,2521C,2546C 'float8':639C 'flow':111C,241C,448C,1488C,1697C 'folder':914C,921C,1264C 'follow':742C,1275C,1529C,1759C,2100C,2217C,2519C 'fontfamili':974C,2621C 'form':863C,1143C 'found':796C,818C 'four':2522C 'framework':310C 'full':2207C 'function':1038C,1115C,1470C,1506C,1539C,1605C,1922C,2054C,2108C,2138C,2158C,2230C,2655C 'get':751C,758C,764C,772C,780C,787C,1202C,1972C,2235C,2376C,2405C 'getauthenticateduserfromsess':1326C,1552C,1578C,1612C,1891C,1929C 'getcooki':1285C,1332C,1369C 'getserversideprop':1116C,1562C,1606C,1923C 'getsupabas':1771C,1806C,1895C,1936C 'github':349C,2482C 'github.com':322C,351C,2484C 'github.com/stytchauth/stytch-nextjs-supabase).':350C,2483C 'github.com/stytchauth/stytch-node)':321C 'go':371C 'googl':94C,104C,221C,234C,397C,2488C,2506C,2533C,2537C,2566C,2591C,2774C,2784C 'granular':1995C 'guid':115C,137C,298C,380C,416C,2526C 'handl':1213C 'handler':1507C,1540C 'hasn':1845C 'head':2332C 'help':451C 'helvetica':975C,977C,2622C,2624C 'high':102C,232C 'high-convert':101C,231C 'hijack':461C 'home':871C,1861C 'howev':1191C 'id':631C,676C,750C,1308C,1311C,1354C,1660C,1735C,1823C,2110C,2141C,2243C,2245C,2253C,2326C,2352C,2366C,2379C,2407C,2419C,2422C 'identifi':1818C 'import':947C,951C,1028C,1032C,1079C,1083C,1087C,1279C,1284C,1499C,1532C,1551C,1565C,1569C,1573C,1577C,1762C,1766C,1878C,1882C,1886C,1890C,1894C,2574C,2578C 'inbox':1174C 'includ':1527C 'index':1092C,1134C,1582C,1633C,1899C,1963C 'insert':665C,691C 'insid':1268C 'inspect':2144C 'instal':899C,905C,1240C,1257C,1720C,1726C,1745C,1749C 'json':1435C,2120C,2748C 'json.stringify':1430C,2743C 'jsonwebtoken':1746C,1750C,1769C 'jsx':945C,1026C,1077C,1277C,1497C,1530C,1563C,1760C,1877C,2572C,2678C 'jwt':1744C,1767C,1811C,1828C,2063C,2150C 'jwt.sign':1798C 'key':779C,806C,809C,827C,1052C,1783C 'know':2032C 'languag':2123C 'last':2316C 'later':521C,1024C,1124C 'learn':2154C,2302C 'leav':674C,2680C 'length':1047C 'let':1095C,1298C,1406C,1585C,1852C,1902C,1993C,2049C,2168C,2699C 'level':262C,623C,1984C 'leverag':244C 'librari':320C,327C,883C,902C,1236C 'like':2463C 'link':75C,199C,403C,447C,459C,944C,1033C,1196C,1217C,2514C,2571C,2667C,2783C 'list':2436C,2437C 'live':1319C 'load':1854C 'loadstytch':1301C,1344C,1379C,1411C,2704C 'localhost':487C,856C,962C,967C,1136C,1636C,2428C,2598C,2603C,2609C,2614C 'log':79C,206C,2036C,2238C 'logic':1452C 'login':110C,240C,862C,875C,896C,1142C,1150C,1166C,1179C,1185C,1475C,1487C,1639C,1683C,1696C,2504C 'loginexpirationminut':964C,2611C 'loginorsignupview':957C,992C,2584C,2639C 'loginredirecturl':961C,2597C,2608C 'logout':1060C,1476C,1519C,1676C 'magic':74C,198C,402C,446C,458C,943C,1216C,2513C,2570C,2666C,2782C 'make':25C,149C,255C,332C,1227C,1869C,2549C,2650C 'manag':85C,212C,2718C 'match':2250C 'math.floor':1790C 'maxag':1443C,2756C 'maximum':63C,187C 'may':367C,563C 'medium':136C,379C 'menu':606C,2075C,2190C 'method':2778C 'minut':567C,1419C,2715C,2732C 'modal':2204C 'multipl':700C 'name':548C,618C,915C,922C,1265C,1271C 'navig':463C,2064C 'need':293C,433C,1206C,1245C,2013C,2030C,2319C,2357C,2517C,2648C,2672C 'new':514C,544C,560C,574C,597C,628C,645C,649C,685C,716C,840C,913C,976C,1072C,1263C,1305C,1491C,1645C,1753C,2062C,2078C,2084C,2194C,2623C 'next':723C,729C,768C,776C,1260C,1291C 'next.js':129C,301C,334C,709C,717C,833C,853C,870C,2455C 'next/link':1035C 'nextjs.org':303C 'nextjs.org/)':302C 'node.js':319C,1235C 'note':338C,385C,791C,813C,884C,2152C,2300C,2471C 'notic':2347C 'npm':845C,904C,1239C,1256C,1725C,1748C 'npx':720C 'null':1121C,1340C,1360C,2355C,2368C 'nullif':2115C 'oauth':105C,235C,398C,2538C,2567C,2586C,2601C,2606C,2669C,2707C 'oauthopt':2588C 'obuldanrptloktxcffvn.supabase.co':422C,500C,528C,579C,652C,688C,703C,811C,829C,1154C,1170C,1189C,1688C,2200C,2223C,2373C,2409C,2424C,2440C,2788C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/01.png)':421C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/02.png)':499C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/03.png)':527C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/04.png)':578C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/05.png)':651C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/06.png)':687C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/07.png)':702C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/08.png)':810C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/09.png)':828C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/10.png)':1153C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/11.png)':1169C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/12.png)':1188C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/13.png)':1687C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/14.png)':2199C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/15.png)':2222C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/16.png)':2372C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/17.png)':2408C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/18.png)':2423C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/19.png)':2439C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/20.png)':2787C 'one':8B,19C,95C,143C,222C,296C,1152C,2489C,2507C,2592C,2775C,2785C 'option':90C,217C,2486C,2493C 'order':1482C,1729C 'organ':2467C 'otherwis':2271C 'output':2130C 'owner':2180C 'p':1051C 'packag':1740C 'page':872C,1665C,1686C,1862C,2198C,2221C,2391C 'pages/api/authenticate.js':1493C,1498C 'pages/api/logout.js':1526C,1531C 'pages/index.js':1078C,1549C,1564C,1864C 'pass':1731C 'password':557C 'passwordless':11B,22C,32C,146C,156C 'past':927C 'payload':1787C,1799C,1808C,2151C 'persist':1699C 'platform':9B,20C,144C 'polici':265C,2018C,2023C,2028C,2172C,2192C,2195C,2211C,2220C,2226C,2282C,2296C,2307C 'popul':695C,2420C 'posit':2595C 'postgr':2096C 'postgresql':2053C,2157C 'power':2450C 'present':1656C 'press':503C,1161C,1675C 'primarycolor':983C,2630C 'process.env.next':1776C,1780C 'process.env.stytch':1129C,1309C,1313C,1316C,1622C,1628C,1951C,1958C 'process.env.supabase':1800C 'product':958C,2585C 'profil':1002C,1039C,1075C,1084C,1100C,1574C,1590C,1685C,1887C,1907C 'profile.js':1009C 'project':347C,535C,545C,552C,561C,570C,576C,590C,718C,745C,749C,799C,824C,1307C,1310C,1317C,2480C,2534C 'prompt':1144C 'prop':1126C,1618C,1626C,1946C,1956C 'proper':2364C 'provid':3B,14C,138C,317C,388C,886C,1469C,2589C 'provis':573C,592C 'public':756C,769C,777C,1130C,1623C,1629C,1777C,1781C,1952C,1959C 'publictoken':987C,990C,991C,1094C,1106C,1107C,1128C,1584C,1596C,1597C,1621C,1627C,1901C,1913C,1914C,1950C,1957C,2634C,2637C,2638C 'pull':1705C,2386C 'queri':2079C,2085C 'rate':44C,168C 'react':315C,326C,882C,938C,948C,950C,1029C,1031C,2575C,2577C 'read':271C,837C 'redirect':418C,429C,437C,462C,466C,496C,506C,524C,2544C 'refresh':1663C 'reload':1966C 'remain':1668C 'removecooki':1287C,1389C 'render':1018C,1070C 'replac':867C,1062C,2107C 'req':1117C,1328C,1335C,1365C,1372C,1392C,1400C,1441C,1508C,1512C,1541C,1545C,1607C,1613C,1924C,1930C,2693C,2754C 'req.query':1405C,2698C 'request':1180C,1714C,1871C,1989C 'request.jwt.claims':2118C 'res':1118C,1329C,1336C,1366C,1373C,1393C,1401C,1442C,1509C,1513C,1542C,1546C,1608C,1614C,1925C,1931C,2694C,2755C 'res.redirect':1395C,1450C,2763C 'res.status':1433C,2746C 'resourc':2790C 'resp':1346C,1413C,2709C,2726C 'resp.session':1422C,2735C 'resp.session.stytch_session.session':2722C 'resp.session.user':1353C 'respons':1454C 'rest':2682C 'return':988C,1041C,1108C,1125C,1322C,1339C,1352C,1359C,1394C,1432C,1449C,1510C,1543C,1598C,1617C,1625C,1634C,1680C,1803C,1915C,1945C,1955C,2111C,2134C,2426C,2635C,2745C,2762C 'review':2278C 'revokeandclearsess':1363C,1533C,1544C 'rls':264C,625C,2017C,2289C,2305C 'root':909C 'rout':1211C,2674C 'row':261C,622C,666C,686C,692C,701C,1983C,2133C,2413C 'run':846C,852C,1237C,1254C,2091C,2103C 'san':979C,2626C 'sans-serif':978C,2625C 'save':641C,682C,2281C,2286C 'scratch':2213C 'screen':1684C 'sdks':56C,180C 'secret':763C,786C,1312C,1314C,1802C,1833C 'secur':41C,165C,263C,555C,624C,1447C,1985C,2760C 'see':2128C,2433C 'select':620C,659C,1944C,2009C,2026C,2070C,2114C,2184C,2205C,2269C,2339C 'send':1642C 'separ':2661C 'serif':980C,2627C 'server':835C 'session':84C,211C,1225C,1250C,1294C,1333C,1349C,1370C,1382C,1390C,1417C,1438C,1480C,2713C,2717C,2730C,2751C 'sessiontoken':1331C,1338C,1351C,1368C,1375C,1384C,1407C,1421C,1440C,2700C,2721C,2734C,2753C 'set':259C,420C,426C,468C,498C,526C,577C,820C,1456C,1557C,2117C,2353C,2535C 'setcooki':1286C,1437C,2750C 'show':512C 'sidebar':605C,2074C,2189C,2344C 'sign':107C,237C,785C,1801C,1826C,1832C 'sign-up':106C,236C 'signupexpirationminut':969C,2616C 'signupredirecturl':966C,2602C,2613C 'similar':1523C 'simpl':120C,257C 'skip':368C 'snippet':929C 'solut':33C,157C 'sourc':133C,376C 'spoof':456C 'sql':2071C,2093C,2104C,2124C 'stabl':2125C 'start':831C,1223C 'state':1672C,1975C 'step':91C,218C,353C,370C,424C,450C,530C,581C,705C,858C,1151C,1167C,1186C,1219C,1712C,2019C,2494C,2523C 'still':1971C 'store':247C,1021C,1247C 'style':935C,973C,994C,1080C,1566C,1879C,2620C,2641C 'styles.main':1111C,1601C,1918C 'stytch':1A,2B,13C,24C,126C,130C,148C,316C,357C,364C,373C,387C,417C,428C,474C,495C,523C,726C,732C,744C,748C,753C,755C,760C,762C,766C,792C,807C,881C,885C,937C,952C,989C,1234C,1241C,1281C,1283C,1296C,1459C,1820C,2382C,2452C,2579C,2636C,2659C,2720C,2791C,2796C,2802 'stytch-supabase-exampl':725C,731C 'stytch.client':1306C 'stytch.com':131C,201C,225C,329C,374C,470C,802C,2528C,2794C,2799C 'stytch.com/blog/improving-conversion-with-google-one-tap?utm_source=supabase\u0026utm_medium=guide)':224C 'stytch.com/blog?utm_source=supabase\u0026utm_medium=guide)':2793C 'stytch.com/dashboard/api-keys?utm_source=supabase\u0026utm_medium=guide)':801C 'stytch.com/dashboard/redirect-urls?utm_source=supabase\u0026utm_medium=guide)':469C 'stytch.com/docs/oauth?utm_source=supabase\u0026utm_medium=guide#guides_google-sdk)':2527C 'stytch.com/docs/sdks/javascript-sdk)':328C 'stytch.com/docs?utm_source=supabase\u0026utm_medium=guide)':2798C 'stytch.com/products/email-magic-links?utm_source=supabase\u0026utm_medium=guide)':200C 'stytch.envs.live':1320C 'stytch.envs.test':1321C 'stytch/stytch-react':901C,906C,954C,2581C 'stytchclient':1343C,1378C,1410C,2703C 'stytchclient.magiclinks.authenticate':1415C,2728C 'stytchclient.oauth.authenticate':2711C 'stytchclient.sessions.authenticate':1348C 'stytchclient.sessions.revoke':1381C 'stytchconfig':956C,2583C 'stytchconfig.loginorsignupview':993C,2640C 'stytchconfig.style':995C,2642C 'stytchlogic.js':1272C,2658C 'stytchlogin':986C,998C,1073C,1088C,1105C,1570C,1595C,1883C,1912C,2554C,2633C,2645C 'success':2131C 'supabas':127C,134C,245C,254C,289C,377C,534C,538C,551C,575C,586C,589C,609C,727C,733C,770C,774C,778C,782C,784C,789C,814C,825C,1023C,1711C,1718C,1722C,1737C,1774C,1778C,1782C,1804C,1830C,1835C,1858C,1876C,1935C,2068C,2188C,2196C,2218C,2336C,2367C,2415C,2453C 'supabase.auth.session':1795C 'supabase.com':541C,612C 'supabase.com/dashboard/),':540C 'supabase.com/dashboard/).':611C 'supabase.from':1942C 'supabase/supabase-js':1727C,1765C 'super':336C 'tabl':594C,598C,646C,650C,656C,662C,697C,2292C,2340C,2371C 'take':564C 'tamper':1848C 'tap':96C,223C,2490C,2508C,2593C,2776C,2786C 'termin':713C 'test':478C,747C,2399C 'text':633C,636C,2112C,2122C 'thing':2317C 'three':627C,1187C 'time':1703C,1867C 'titl':634C,670C 'token':757C,1131C,1350C,1383C,1403C,1416C,1423C,1624C,1630C,1797C,1953C,1960C,2676C,2696C,2712C,2723C,2729C,2736C 'tool':600C 'top':313C 'tracker':122C,2448C 'transit':1851C 'tri':1341C,1376C,1408C,2701C 'true':972C,1448C,2119C,2594C,2619C,2761C 'two':389C,1168C,2766C 'type':491C,1404C,2590C,2600C,2605C,2697C,2706C,2719C 'ui':876C,897C 'uniqu':1817C 'updat':510C,1123C,1548C,1863C,2323C,2359C,2411C,2558C 'upon':1647C 'url':419C,430C,438C,467C,485C,497C,507C,520C,525C,771C,1779C,2545C 'us':2089C 'use':55C,72C,125C,179C,196C,300C,442C,518C,690C,711C,879C,941C,1014C,1232C,1463C,1473C,1829C,2564C 'user':49C,82C,173C,209C,252C,268C,408C,630C,675C,1040C,1093C,1098C,1101C,1102C,1120C,1127C,1479C,1559C,1583C,1588C,1591C,1592C,1619C,1659C,1715C,1734C,1815C,1822C,1900C,1905C,1908C,1909C,1947C,2007C,2038C,2059C,2177C,2240C,2242C,2252C,2267C,2325C,2351C,2365C,2369C,2378C,2383C,2398C,2406C,2418C,2421C,2502C 'user-test-61497d40-f957-45cd-a6c8-5408d22e93bc':2397C 'user.expenses':1046C 'user.expenses.map':1049C 'user.userid':1043C 'userid':1610C,1616C,1620C,1772C,1785C,1788C,1927C,1933C,1937C,1948C,2121C,2146C 'util':1266C 'utils/stytchlogic.js':1278C,2679C 'utils/supabase.js':1755C,1761C 'utm':132C,135C,375C,378C 'valid':1478C,1840C 'valu':637C,672C,743C,793C,815C,841C,2361C 'variabl':1560C 've':2285C 'version':344C,2477C 'via':396C 'video':2164C,2311C 'view':658C 'want':893C 'way':390C 'web':123C,308C 'websit':36C,160C 'welcom':1042C,1658C,2390C 'whether':2042C,2175C,2248C 'width':981C,2628C 'within':730C,924C,1742C 'work':1695C,2773C 'write':273C,2015C,2021C,2098C 'www.youtube.com':2166C,2313C 'www.youtube.com/watch?v=mjzccpcyeqk).':2165C 'www.youtube.com/watch?v=ow_uzedfohk).':2312C","video":null,"call_to_action_link":null,"featured":false},"overview":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nconst chCodeConfig = {\n  \"staticMediaQuery\": \"not screen, (max-width: 768px)\",\n  \"lineNumbers\": true,\n  \"showCopyButton\": true,\n  \"themeName\": \"supabase\"\n};\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    h2: \"h2\",\n    a: \"a\",\n    blockquote: \"blockquote\",\n    img: \"img\",\n    strong: \"strong\",\n    code: \"code\",\n    ul: \"ul\",\n    li: \"li\"\n  }, _provideComponents(), props.components), {CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Stytch provides an all-in-one platform for passwordless auth. Stytch makes it easy for you to embed passwordless solutions into your websites and apps for better security, better conversion rates, and a better end user experience. Their easy-to-use SDKs and direct API access allows for maximum control and customization. In this example we will use Email magic links to create and log in our users, and Session management. There is an additional, optional step to enable Google One Tap which is an especially high-converting Google OAuth sign-up and login flow.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Documentation\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In this guide we will build a simple expense tracker web application using Stytch, Supabase, and Next.js.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://stytch.com?utm_source=supabase\u0026utm_medium=guide\",\n        children: \"Stytch\"\n      }), \" provides an all-in-one platform for passwordless auth. Stytch makes it easy for you to embed passwordless solutions into your websites and apps for better security, better conversion rates, and a better end user experience. Their easy-to-use SDKs and direct API access allows for maximum control and customization. In this example we will use \", _jsx(_components.a, {\n        href: \"https://stytch.com/products/email-magic-links?utm_source=supabase\u0026utm_medium=guide\",\n        children: \"Email magic links\"\n      }), \" to create and log in our users, and Session management. There is an additional, optional step to enable \", _jsx(_components.a, {\n        href: \"https://stytch.com/blog/improving-conversion-with-google-one-tap?utm_source=supabase\u0026utm_medium=guide\",\n        children: \"Google One Tap\"\n      }), \" which is an especially high-converting Google OAuth sign-up and login flow.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We will leverage Supabase to store and authorize access to user data. Supabase makes it simple to set up Row Level Security (RLS) policies which ensure users can only read and write data that they are authorized to do so. If you do not already have a Supabase account, you will need to create one.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"This guide will use \", _jsx(_components.a, {\n        href: \"https://nextjs.org/\",\n        children: \"Next.js\"\n      }), \" which is a web application framework built on top of React. Stytch provides a \", _jsx(_components.a, {\n        href: \"https://github.com/stytchauth/stytch-node\",\n        children: \"Node.js library\"\n      }), \" and a \", _jsx(_components.a, {\n        href: \"https://stytch.com/docs/sdks/javascript-sdk\",\n        children: \"React library\"\n      }), \" which makes building Next.js apps super easy.\"]\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsxs(_components.p, {\n        children: [\"Note: You can find a completed version of this project on \", _jsx(_components.a, {\n          href: \"https://github.com/stytchauth/stytch-nextjs-supabase\",\n          children: \"Github\"\n        }), \".\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 0: Create a Stytch Account\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"If you already have a Stytch account you may skip this step.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Go to \", _jsx(_components.a, {\n        href: \"https://stytch.com?utm_source=supabase\u0026utm_medium=guide\",\n        children: \"Stytch\"\n      }), \", and create an account. Note that Stytch provides two ways to create an account, either via Google OAuth, or through Email magic links.  This is the same user experience we will be building in this guide!\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/01.png\",\n        alt: \"Stytch redirect URL settings\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 1: Set up Stytch redirect URLs\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"First we need to add the redirect URLs that will be used during the Email magic link flow. This step helps ensure bad actors cannot spoof your magic links and hijack redirects.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Navigate to your \", _jsx(_components.a, {\n        href: \"https://stytch.com/dashboard/redirect-urls?utm_source=supabase\u0026utm_medium=guide\",\n        children: \"redirect URL settings\"\n      }), \" in the Stytch dashboard, and under \", _jsx(_components.strong, {\n        children: \"Test environment\"\n      }), \" create an entry where the \", _jsx(_components.strong, {\n        children: \"URL\"\n      }), \" is \", _jsx(_components.code, {\n        children: \"http://localhost:3000/api/authenticate\"\n      }), \" and the \", _jsx(_components.strong, {\n        children: \"Type\"\n      }), \" is \", _jsx(_components.code, {\n        children: \"All\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/02.png\",\n        alt: \"Edit Stytch redirect URL settings\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"After pressing \", _jsx(_components.strong, {\n        children: \"Confirm\"\n      }), \", the redirect URLs dashboard will update to show your new entry. We will use this URL later on.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/03.png\",\n        alt: \"Stytch redirect URL settings\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 2: Create a Supabase project\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"From your \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard/\",\n        children: \"Supabase dashboard\"\n      }), \", click \", _jsx(_components.strong, {\n        children: \"New project\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Enter a \", _jsx(_components.code, {\n        children: \"Name\"\n      }), \" for your Supabase project.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Enter a secure \", _jsx(_components.code, {\n        children: \"Database Password\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Click \", _jsx(_components.strong, {\n        children: \"Create new project\"\n      }), \". It may take a couple minutes for your project to be provisioned.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/04.png\",\n        alt: \"New Supabase project settings\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 3: Creating data in Supabase\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Once your Supabase project is provisioned, click Table editor, then New table. This tool is available from the sidebar menu in the \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard/\",\n        children: \"Supabase dashboard\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Enter \", _jsx(_components.code, {\n        children: \"expenses\"\n      }), \" as the \", _jsx(_components.strong, {\n        children: \"Name\"\n      }), \" field.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Select \", _jsx(_components.code, {\n        children: \"Enable Row Level Security (RLS)\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Add three new columns:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"\\n\", _jsxs(_components.p, {\n          children: [_jsx(_components.code, {\n            children: \"user_id\"\n          }), \" as \", _jsx(_components.code, {\n            children: \"text\"\n          })]\n        }), \"\\n\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"\\n\", _jsxs(_components.p, {\n          children: [_jsx(_components.code, {\n            children: \"title\"\n          }), \" as \", _jsx(_components.code, {\n            children: \"text\"\n          })]\n        }), \"\\n\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"\\n\", _jsxs(_components.p, {\n          children: [_jsx(_components.code, {\n            children: \"value\"\n          }), \" as \", _jsx(_components.code, {\n            children: \"float8\"\n          })]\n        }), \"\\n\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Click \", _jsx(_components.strong, {\n        children: \"Save\"\n      }), \" to create the new table.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/05.png\",\n        alt: \"Creating a new table\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"From the Table editor view, select the expenses table and click \", _jsx(_components.strong, {\n        children: \"Insert row\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Fill out the title and value fields (leave user_id blank for now) and click \", _jsx(_components.strong, {\n        children: \"Save\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/06.png\",\n        alt: \"Creating a new row\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Use \", _jsx(_components.strong, {\n        children: \"Insert Row\"\n      }), \" to further populate the table with expenses.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/07.png\",\n        alt: \"Multiple rows\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 4: Building a Next.js app\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Using a terminal, create a new Next.js project:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"npx \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"create-next-app stytch-supabase-example\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Next, within \", _jsx(_components.code, {\n        children: \"stytch-supabase-example\"\n      }), \" create a \", _jsx(_components.code, {\n        children: \".env.local\"\n      }), \" file and enter the following values:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"STYTCH_PROJECT_ENV=test\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"STYTCH_PROJECT_ID=GET_FROM_STYTCH_DASHBOARD\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"STYTCH_PUBLIC_TOKEN=GET_FROM_STYTCH_DASHBOARD\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"STYTCH_SECRET=GET_FROM_STYTCH_DASHBOARD\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"NEXT_PUBLIC_SUPABASE_URL=GET_FROM_SUPABASE_DASHBOARD\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"NEXT_PUBLIC_SUPABASE_KEY=GET_FROM_SUPABASE_DASHBOARD\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"SUPABASE_SIGNING_SECRET=GET_FROM_SUPABASE_DASHBOARD\",\n              \"props\": {}\n            }]\n          }],\n          \"lang\": \"text\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsxs(_components.p, {\n        children: [\"Note: Stytch values can be found in the project \", _jsx(_components.a, {\n          href: \"https://stytch.com/dashboard/api-keys?utm_source=supabase\u0026utm_medium=guide\",\n          children: \"dashboard\"\n        }), \" under \", _jsx(_components.strong, {\n          children: \"API Keys\"\n        }), \".\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/08.png\",\n        alt: \"Stytch API keys\"\n      })\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsxs(_components.p, {\n        children: [\"Note: Supabase values can be found under \", _jsx(_components.strong, {\n          children: \"Settings\"\n        }), \" \u003e \", _jsx(_components.strong, {\n          children: \"API\"\n        }), \" for your project.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/09.png\",\n        alt: \"Supabase API keys\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Start your Next.js development server to read in the new values from \", _jsx(_components.code, {\n        children: \".env.local\"\n      }), \".\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"npm \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"run dev\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You should have a running Next.js application on \", _jsx(_components.code, {\n        children: \"localhost:3000\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 5: Build the Login Form\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Now we will replace the default Next.js home page with a login UI. We will use the Stytch React library.\"\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsx(_components.p, {\n        children: \"Note: Stytch provides direct API access for those that want to build login UI themselves\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Install the \", _jsx(_components.code, {\n        children: \"@stytch/stytch-react\"\n      }), \" library.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"npm \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"install @stytch/stytch-react\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In the root directory, create a new folder named \", _jsx(_components.code, {\n        children: \"components\"\n      }), \" and file in that folder named \", _jsx(_components.code, {\n        children: \"/StytchLogin.js\"\n      }), \". Within this file, paste the snippet below. This will configure, and style the Stytch React component to use Email magic links.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// components/StytchLogin.js\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" React \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'react'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { Stytch } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'@stytch/stytch-react'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"stytchConfig \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  loginOrSignupView: {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    products: [\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'emailMagicLinks'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"],\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    emailMagicLinksOptions: {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      loginRedirectURL: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'http://localhost:3000/api/authenticate'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      loginExpirationMinutes: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"30\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      signupRedirectURL: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'http://localhost:3000/api/authenticate'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      signupExpirationMinutes: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"30\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      createUserAsPending: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"true\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  style: {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    fontFamily: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'\\\"Helvetica New\\\", Helvetica, sans-serif'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    width: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'321px'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    primaryColor: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'#0577CA'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"StytchLogin \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" ({ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"publicToken\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \" }) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Stytch\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      publicToken\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"publicToken\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      loginOrSignupView\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"stytchConfig.loginOrSignupView\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      style\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"stytchConfig.style\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    /\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  )\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export default \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"StytchLogin\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"jsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Additionally, create a profile component by creating a file called \", _jsx(_components.code, {\n        children: \"Profile.js\"\n      }), \" in \", _jsx(_components.code, {\n        children: \"/components\"\n      }), \". We will use this component to render our expenses stored in Supabase later on.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// components/Profile.js\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" React \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'react'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" Link \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'next/link'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export default function \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"Profile\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({ user }) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"div\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"h1\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003eWelcome \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"user.userId\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"h1\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"h2\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003eYour expenses\u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"h2\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"user.expenses?.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"length \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"0 \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"?\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        user.expenses.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"map\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"((\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"expense\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"key\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"expense.id\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"            {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"expense.title\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \": $\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"expense.value\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          \u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        ))\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      ) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003eYou have no expenses!\u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      )\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Link \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"href\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"/api/logout\\\" \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"passHref\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"button\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"a\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003eLogout\u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"a\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"button\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Link\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"div\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  )\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"jsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Finally, replace the contents of the file \", _jsx(_components.code, {\n        children: \"/pages/index.js\"\n      }), \" to render our new \", _jsx(_components.code, {\n        children: \"StytchLogin\"\n      }), \" and \", _jsx(_components.code, {\n        children: \"Profile\"\n      }), \" components.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// pages/index.js\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" styles \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'../styles/Home.module.css'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" Profile \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'../components/Profile'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" StytchLogin \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'../components/StytchLogin'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"Index \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" ({ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"user\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"publicToken\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \" }) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  let\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" content\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (user) {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    content \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Profile \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"user\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"user\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" /\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"else\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    content \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"StytchLogin \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"publicToken\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"publicToken\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" /\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"div \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"className\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"styles.main\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"content\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"div\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export async function \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"getServerSideProps\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"req\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"res\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \" }) {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"user \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"null \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"// Will update later\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    props: { user, publicToken: process.env.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"STYTCH_PUBLIC_TOKEN\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export default \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"Index\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"jsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"On \", _jsx(_components.code, {\n        children: \"localhost:3000\"\n      }), \" there is now a login form prompting for your email address.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/10.png\",\n        alt: \"Email login step one\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Enter your email address and press \", _jsx(_components.strong, {\n        children: \"Continue with email\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/11.png\",\n        alt: \"Email login step two\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In your inbox you will find a login request from your app.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/12.png\",\n        alt: \"Email login step three\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"However, if you click the link in the email you will get a 404. We need to build an API route to handle the email magic link authentication.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 6: Authenticate and start a session\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To make authentication easier we will use the Stytch Node.js library. Run\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"npm \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"install stytch\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Additionally, we will need to store the authenticated session in a cookie. Run\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"npm \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"install cookies-next\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Create a new folder named \", _jsx(_components.code, {\n        children: \"utils\"\n      }), \" and inside a file named\", _jsx(_components.code, {\n        children: \"stytchLogic.js\"\n      }), \" with the following contents\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// utils/stytchLogic.js\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"* \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"as\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" stytch \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'stytch'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { getCookie, setCookies, removeCookies } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'cookies-next'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"SESSION_COOKIE \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'stytch_cookie'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"let\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" client\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"loadStytch \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" () \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"!\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"client) {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    client \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= new\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" stytch.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Client\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      project_id: process.env.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"STYTCH_PROJECT_ID\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      secret: process.env.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"STYTCH_SECRET\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      env: process.env.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"STYTCH_PROJECT_ENV \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"=== \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'live' \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"?\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" stytch.envs.live \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \":\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" stytch.envs.test,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" client\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"getAuthenticatedUserFromSession \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"= async\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"req\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"res\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"sessionToken \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"getCookie\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"SESSION_COOKIE\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", { req, res })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"!\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"sessionToken) {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    return \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"null\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  try\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"stytchClient \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"loadStytch\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"resp \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" stytchClient.sessions.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"authenticate\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      session_token: sessionToken,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" resp.session.user_id\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"catch\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (error) {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    console.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"log\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(error)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    return \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"null\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"revokeAndClearSession \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"= async\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"req\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"res\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"sessionToken \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"getCookie\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"SESSION_COOKIE\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", { req, res })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (sessionToken) {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    try\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"stytchClient \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"loadStytch\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" stytchClient.sessions.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"revoke\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        session_token: sessionToken,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"catch\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (error) {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      console.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"log\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(error)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    removeCookies\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"SESSION_COOKIE\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", { req, res })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" res.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"redirect\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'/'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"authenticateTokenStartSession \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"= async\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"req\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"res\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"token\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"type\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" req.query\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  let\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" sessionToken\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  try\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"stytchClient \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"loadStytch\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"resp \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" stytchClient.magicLinks.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"authenticate\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(token, {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      session_duration_minutes: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"30\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    sessionToken \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" resp.session_token\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"catch\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (error) {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    console.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"log\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(error)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"errorString \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"JSON\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"stringify\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(error)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" res.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"status\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"400\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \").\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"json\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({ errorString })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  setCookies\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"SESSION_COOKIE\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", sessionToken, {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    req,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    res,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    maxAge: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"60 \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"* \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"60 \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"* \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"24\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    secure: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"true\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" res.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"redirect\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'/'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"jsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This logic is responsible for setting up the Stytch client we will use to call the API. It provides functions we will use to login, logout, and validate user sessions.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In order to complete the email login flow, create a new file \", _jsx(_components.code, {\n        children: \"pages/api/authenticate.js\"\n      }), \" with the contents:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// pages/api/authenticate.js\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { authenticateTokenStartSession } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'../../utils/stytchLogic'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export default async function \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"handler\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(req, res) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"authenticateTokenStartSession\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(req, res)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"jsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We will also create a logout API endpoint with similar contents. In \", _jsx(_components.code, {\n        children: \"pages/api/logout.js\"\n      }), \" include the following:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// pages/api/logout.js\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { revokeAndClearSession } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'../../utils/stytchLogic'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export default async function \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"handler\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(req, res) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"revokeAndClearSession\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(req, res)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"jsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Finally, update \", _jsx(_components.code, {\n        children: \"pages/index.js\"\n      }), \" by importing \", _jsx(_components.code, {\n        children: \"getAuthenticatedUserFromSession\"\n      }), \", and calling it to set the user variable in \", _jsx(_components.code, {\n        children: \"getServerSideProps\"\n      }), \".\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// pages/index.js\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" styles \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'../styles/Home.module.css'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" StytchLogin \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'../components/StytchLogin'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" Profile \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'../components/Profile'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { getAuthenticatedUserFromSession } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'../utils/stytchLogic'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"Index \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" ({ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"user\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"publicToken\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \" }) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  let\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" content\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (user) {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    content \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Profile \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"user\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"user\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" /\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"else\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    content \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"StytchLogin \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"publicToken\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"publicToken\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" /\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"div \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"className\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"styles.main\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"content\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"div\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export async function \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"getServerSideProps\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"req\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"res\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \" }) {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"userId \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= await \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"getAuthenticatedUserFromSession\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(req, res)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (userId) {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      props: { user: { userId }, publicToken: process.env.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"STYTCH_PUBLIC_TOKEN\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    props: { publicToken: process.env.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"STYTCH_PUBLIC_TOKEN\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export default \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"Index\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"jsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Return to \", _jsx(_components.code, {\n        children: \"localhost:3000\"\n      }), \", and login again by sending yourself a new email. Upon clicking through in the email you should be presented with “Welcome $USER_ID”. If you refresh the page, you should remain in an authenticated state. If you press \", _jsx(_components.strong, {\n        children: \"Logout\"\n      }), \" then you should return to the login screen.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/13.png\",\n        alt: \"Profile page\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Now that we have a working login flow with persistent authentication it is time to pull in our expense data from Supabase.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 7: Requesting user data from Supabase\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"First, install the Supabase client:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"npm \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"install @supabase/supabase-js\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In order to pass an authenticated \", _jsx(_components.code, {\n        children: \"user_id\"\n      }), \" to Supabase we will package it within a JWT. Install jsonwebtoken:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"npm \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"install jsonwebtoken\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"bash\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Create a new file \", _jsx(_components.code, {\n        children: \"utils/supabase.js\"\n      }), \" and add the following:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// utils/supabase.js\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { createClient } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'@supabase/supabase-js'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" jwt \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'jsonwebtoken'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"getSupabase \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"userId\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"supabase \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"createClient\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    process.env.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"NEXT_PUBLIC_SUPABASE_URL\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    process.env.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"NEXT_PUBLIC_SUPABASE_KEY\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  )\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (userId) {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"payload \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      userId,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      exp: Math.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"floor\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(Date.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"now\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"() \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"/ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"1000\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"+ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"60 \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"* \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"60\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    supabase.auth.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"session \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" () \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" ({\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      access_token: jwt.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"sign\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(payload, process.env.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"SUPABASE_SIGNING_SECRET\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"),\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { getSupabase }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"jsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Our payload for the JWT will contain our user's unique identifier from Stytch, their \", _jsx(_components.code, {\n        children: \"user_id\"\n      }), \". We are signing this JWT using Supabase's signing secret, so Supabase will be able to validate it is authentic and hasn't been tampered with in transit.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Let's load our expenses from Supabase on the home page! Update \", _jsx(_components.code, {\n        children: \"pages/index.js\"\n      }), \" a final time to make a request for expense data from Supabase.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" styles \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'../styles/Home.module.css'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" StytchLogin \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'../components/StytchLogin'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" Profile \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'../components/Profile'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { getAuthenticatedUserFromSession } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'../utils/stytchLogic'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { getSupabase } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'../utils/supabase'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"Index \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" ({ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"user\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"publicToken\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \" }) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  let\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" content\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (user) {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    content \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Profile \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"user\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"user\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" /\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"else\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    content \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"StytchLogin \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"publicToken\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"publicToken\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" /\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"div \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"className\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"styles.main\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"content\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"div\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export async function \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"getServerSideProps\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"req\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"res\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \" }) {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"userId \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= await \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"getAuthenticatedUserFromSession\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(req, res)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (userId) {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"supabase \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"getSupabase\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(userId)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"data\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"expenses\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'expenses'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \").\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"select\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'*'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      props: {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        user: { userId, expenses },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        publicToken: process.env.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"STYTCH_PUBLIC_TOKEN\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"else\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      props: { publicToken: process.env.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"STYTCH_PUBLIC_TOKEN\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export default \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"Index\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"jsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"When we reload our application, we are still getting the empty state for expenses.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This is because we enabled Row Level Security, which blocks all requests by default and lets you granularly control access to the data in your database. To enable our user to select their expenses we need to write a RLS policy.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 8: Write a policy to allow select\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Our policy will need to know who our currently logged in user is to determine whether or not they should have access. Let's create a PostgreSQL function to extract the current user from our new JWT.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Navigate back to the Supabase dashboard, select SQL from the sidebar menu, and click \", _jsx(_components.strong, {\n        children: \"New query\"\n      }), \". This will create a new query,, which will allow us to run any SQL against our Postgres database.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Write the following and click \", _jsx(_components.strong, {\n        children: \"Run\"\n      }), \".\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"create or replace function \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"auth\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"user_id\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"() \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"returns text as\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" $$\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \" select \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"nullif\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"(current_setting(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'request.jwt.claims'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", true)::\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"json-\u003e\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'userId'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"''\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")::\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"text\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"$$ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"language sql\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" stable;\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You should see the output \", _jsx(_components.code, {\n        children: \"Success, no rows returned\"\n      }), \". This created a function called \", _jsx(_components.code, {\n        children: \"auth.user_id()\"\n      }), \", which will inspect the \", _jsx(_components.code, {\n        children: \"userId\"\n      }), \" field of our JWT payload.\"]\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsxs(_components.p, {\n        children: [\"Note: To learn more about PostgreSQL functions, check out this \", _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=MJZCCpCYEqk\",\n          children: \"deep dive video\"\n        }), \".\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Let's create a policy that checks whether this user is the owner of an expense.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Select \", _jsx(_components.strong, {\n        children: \"Authentication\"\n      }), \" from the Supabase sidebar menu, click \", _jsx(_components.strong, {\n        children: \"Policies\"\n      }), \", then \", _jsx(_components.strong, {\n        children: \"New Policy\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/14.png\",\n        alt: \"Supabase authentication page\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"From the modal, select \", _jsx(_components.strong, {\n        children: \"For full customization create a policy from scratch\"\n      }), \" and add the following.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/15.png\",\n        alt: \"Supabase create policy page\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"This policy is calling the function we just created to get the currently logged in user's \", _jsx(_components.code, {\n        children: \"user_id\"\n      }), \" \", _jsx(_components.code, {\n        children: \"auth.user_id()\"\n      }), \" and checking whether this matches the \", _jsx(_components.code, {\n        children: \"user_id\"\n      }), \" column for the current expense. If it does, then it will allow the user to select it, otherwise it will continue to deny.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Click Review and then Save policy. After you've saved, click Enable RLS on the table to enable the policy we just created.\"\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsxs(_components.p, {\n        children: [\"Note: To learn more about RLS and policies, check out this \", _jsx(_components.a, {\n          href: \"https://www.youtube.com/watch?v=Ow_Uzedfohk\",\n          children: \"video\"\n        }), \".\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The last thing we need to do is update the \", _jsx(_components.code, {\n        children: \"user_id\"\n      }), \" columns for our existing expenses.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Head back to the Supabase dashboard, and select Table editor from the sidebar. You will notice each entry has \", _jsx(_components.code, {\n        children: \"user_id\"\n      }), \" set to \", _jsx(_components.code, {\n        children: \"NULL\"\n      }), \". We need to update this value to the proper \", _jsx(_components.code, {\n        children: \"user_id\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/16.png\",\n        alt: \"Supabase null users in table\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To get the \", _jsx(_components.code, {\n        children: \"user_id\"\n      }), \" for our Stytch user, you can pull it from the welcome page in our example app (eg \", _jsx(_components.code, {\n        children: \"user-test-61497d40-f957-45cd-a6c8-5408d22e93bc\"\n      }), \").\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/17.png\",\n        alt: \"Get user_id\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Update each row in Supabase to this \", _jsx(_components.code, {\n        children: \"user_id\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/18.png\",\n        alt: \"Populate user_id\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Return to \", _jsx(_components.code, {\n        children: \"localhost:3000\"\n      }), \", and you will see your expenses listed.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/19.png\",\n        alt: \"Listed expenses\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We now have a basic expense tracker application powered by Stytch, Supabase, and Next.js. From here you could add additional features like adding, editing, and organizing your expenses further.\"\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsxs(_components.p, {\n        children: [\"Note: You can find a completed version of this project on \", _jsx(_components.a, {\n          href: \"https://github.com/stytchauth/stytch-nextjs-supabase\",\n          children: \"Github\"\n        }), \".\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Optional: Add Google One Tap\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In this optional step, we will extend our application to allow users to login with Google One Tap in addition to Email magic links.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You will need to follow the first four steps of \", _jsx(_components.a, {\n        href: \"https://stytch.com/docs/oauth?utm_source=supabase\u0026utm_medium=guide#guides_google-sdk\",\n        children: \"this guide\"\n      }), \" to create a Google project, set up Google OAuth consent, and configure credentials and redirect URLs.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"First, we will make some adjustments to the \", _jsx(_components.code, {\n        children: \"StytchLogin\"\n      }), \" component. We will update the configuration, so that it uses both Google OAuth, and Email magic links.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// components/StytchLogin.js\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" React \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'react'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { Stytch } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'@stytch/stytch-react'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"stytchConfig \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  loginOrSignupView: {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    products: [\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'oauth'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'emailMagicLinks'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"],\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    oauthOptions: {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      providers: [\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          type: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'google'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          one_tap: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"true\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          position: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'embedded'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      ],\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      loginRedirectURL: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'http://localhost:3000/api/authenticate?type=oauth'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      signupRedirectURL: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'http://localhost:3000/api/authenticate?type=oauth'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    emailMagicLinksOptions: {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      loginRedirectURL: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'http://localhost:3000/api/authenticate'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      loginExpirationMinutes: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"30\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      signupRedirectURL: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'http://localhost:3000/api/authenticate'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      signupExpirationMinutes: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"30\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      createUserAsPending: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"true\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  style: {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    fontFamily: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'\\\"Helvetica New\\\", Helvetica, sans-serif'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    width: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'321px'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    primaryColor: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'#0577CA'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"StytchLogin \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" ({ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"publicToken\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \" }) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Stytch\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      publicToken\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"publicToken\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      loginOrSignupView\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"stytchConfig.loginOrSignupView\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      style\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"stytchConfig.style\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    /\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  )\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export default \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"StytchLogin\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"jsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We also need to make an adjustment to the function \", _jsx(_components.code, {\n        children: \"authenticateTokenStartSession\"\n      }), \" in \", _jsx(_components.code, {\n        children: \"stytchLogic.js\"\n      }), \". Stytch has separate authentication endpoints for Email magic links and OAuth, so we need to route our token correctly.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// utils/stytchLogic.js\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"// leave the rest of the file contents as is\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"authenticateTokenStartSession \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"= async\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"req\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"res\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"token\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"type\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" req.query\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  let\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" sessionToken\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  try\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"stytchClient \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"loadStytch\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (type \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"== \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'oauth'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \") {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"resp \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" stytchClient.oauth.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"authenticate\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(token, {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        session_duration_minutes: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"30\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        session_management_type: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'stytch'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      sessionToken \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" resp.session.stytch_session.session_token\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"else\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"resp \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" stytchClient.magicLinks.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"authenticate\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(token, {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        session_duration_minutes: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"30\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      sessionToken \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" resp.session_token\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"catch\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (error) {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    console.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"log\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(error)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"errorString \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"JSON\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"stringify\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(error)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" res.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"status\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"400\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \").\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"json\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({ errorString })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  setCookies\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"SESSION_COOKIE\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", sessionToken, {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    req,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    res,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    maxAge: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"60 \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"* \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"60 \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"* \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"24\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    secure: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"true\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" res.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"redirect\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'/'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"jsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"With these two changes you will now have a working Google One Tap authentication method along with email magic links.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/stytch/documentation/20.png\",\n        alt: \"Google One Tap\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Resources\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://stytch.com/blog?utm_source=supabase\u0026utm_medium=guide\",\n          children: \"Stytch blog\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://stytch.com/docs?utm_source=supabase\u0026utm_medium=guide\",\n          children: \"Stytch documentation\"\n        })\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  chCodeConfig,\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{}}},"__N_SSG":true},"page":"/partners/integrations/[slug]","query":{"slug":"stytch"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"isExperimentalCompile":false,"gsp":true,"scriptLoader":[]}</script></body></html>