<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../../rss.xml" data-next-head=""/><link rel="manifest" href="../../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:type" content="website" data-next-head=""/><meta property="og:image:alt" content="Supabase Og Image" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">WeWeb | Works With Supabase</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="WeWeb is a no-code tool that allows you to build user interfaces on top of existing databases." data-next-head=""/><meta property="og:title" content="WeWeb | Works With Supabase" data-next-head=""/><meta property="og:description" content="WeWeb is a no-code tool that allows you to build user interfaces on top of existing databases." data-next-head=""/><meta property="og:url" content="https://supabase.com/partners/integrations/weweb" data-next-head=""/><meta property="og:image" content="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/weweb_og.png" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2263-07e8ebb96cc2b014.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8658-639e1bb66549b52b.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/partners/integrations/%5Bslug%5D-cff8a94339d02c6e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqela6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R1eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R1eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R2eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R2eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R3eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R3eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav></div><main class="relative min-h-screen"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20"><div class="col-span-12 mx-auto mb-2 max-w-5xl space-y-10 lg:col-span-2"><a class="text-foreground hover:text-foreground-lighter flex cursor-pointer items-center transition-colors" href="../integrations.html"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left"><path d="m15 18-6-6 6-6"></path></svg>Back</a><div class="flex items-center space-x-4"><img alt="WeWeb" loading="lazy" width="56" height="56" decoding="async" data-nimg="1" class="bg-surface-200 flex-shrink-f0 h-14 w-14 rounded-full" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fweweb%2Fweweb_logo.jpeg&amp;w=64&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fweweb%2Fweweb_logo.jpeg&amp;w=128&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fweweb%2Fweweb_logo.jpeg&amp;w=128&amp;q=75"/><h1 class="h1" style="margin-bottom:0">WeWeb</h1></div><div class="bg-gradient-to-t from-background-alternative to-background border-b p-6 [&amp;_.swiper-container]:overflow-visible" style="margin-left:calc(50% - 50vw);margin-right:calc(50% - 50vw)"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0 !px-3 lg:!px-12 xl:!p-0 mx-auto max-w-5xl"><div class="swiper"><div class="swiper-wrapper"><div class="swiper-slide"><div class="relative block overflow-hidden rounded-md"><img alt="WeWeb" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto;background-size:cover;background-position:50% 50%;background-repeat:no-repeat;background-image:url(&quot;data:image/svg+xml;charset=utf-8,%3Csvg xmlns=&#x27;http://www.w3.org/2000/svg&#x27; viewBox=&#x27;0 0 1460 960&#x27;%3E%3Cfilter id=&#x27;b&#x27; color-interpolation-filters=&#x27;sRGB&#x27;%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3CfeColorMatrix values=&#x27;1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1&#x27; result=&#x27;s&#x27;/%3E%3CfeFlood x=&#x27;0&#x27; y=&#x27;0&#x27; width=&#x27;100%25&#x27; height=&#x27;100%25&#x27;/%3E%3CfeComposite operator=&#x27;out&#x27; in=&#x27;s&#x27;/%3E%3CfeComposite in2=&#x27;SourceGraphic&#x27;/%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3C/filter%3E%3Cimage width=&#x27;100%25&#x27; height=&#x27;100%25&#x27; x=&#x27;0&#x27; y=&#x27;0&#x27; preserveAspectRatio=&#x27;none&#x27; style=&#x27;filter: url(%23b);&#x27; href=&#x27;/images/blur.png&#x27;/%3E%3C/svg%3E&quot;)" sizes="100vw" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fweweb%2Fweweb_og.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fweweb%2Fweweb_og.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fweweb%2Fweweb_og.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fweweb%2Fweweb_og.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fweweb%2Fweweb_og.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fweweb%2Fweweb_og.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fweweb%2Fweweb_og.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fweweb%2Fweweb_og.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fweweb%2Fweweb_og.png&amp;w=3840&amp;q=75"/></div></div></div></div></div></div><div class="grid gap-y-12 lg:grid-cols-8 lg:space-x-12"><div class="lg:col-span-5 overflow-hidden"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Overview</h2><div class="prose"><p>WeWeb is a no-code tool that allows you to build user interfaces on top of existing databases.</p>
<p>It allies the short learning curve of no-code with the freedom of code.</p>
<p>You can build complete frontends with drag-and-drop UI elements and no-code CSS properties but have the option to add custom code or import your own Vue.js components.</p>
<p>You can follow development best practices by building global workflows and re-usable functions with no-code triggers and formulas but also have the option to add custom JS where needed.</p>
<p>WeWeb generates a standard PWA-ready Single Page Application and allows you to deploy your projects on your own infrastructure. Build in no-code, host on-premise.</p>
<h2>Documentation</h2>
<p>This guide explains how to connect a Supabase back-end to a WeWeb front-end and then configure all the CRUD operations necessary to build an Admin Portal with user authentication, roles, and permissions.</p>
<p><a href="https://dashboard.weweb.io/sign-up">WeWeb</a> is a low-code front-end builder that allies the short learning curve of no-code with the freedom of code.</p>
<p>It connects to Supabase via two native integrations:</p>
<ul>
<li>one for data manipulation, and</li>
<li>another for user authentication.</li>
</ul>
<p>If you don&#x27;t have an WeWeb account, you can create one <a href="https://dashboard.weweb.io/sign-up">here</a>.</p>
<p>Let&#x27;s get started!</p>
<h2>Step 1: Add the Supabase Data Source Plugin in WeWeb</h2>
<p>In order to read Supabase data in WeWeb, you&#x27;ll first need to add the Supabase Data Source Plugin:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p>Once you&#x27;ve added it, you will be invited to share your Supabase project URL and public API key:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p>In Supabase, you can find both your project URL and public key in the <code>Settings</code> &gt; <code>API</code> menu:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p>Once you have added both to WeWeb, you will have the option to enable realtime tables if you wish to do so:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p><strong>🚨 Warning 🚨</strong></p>
<blockquote>
<p>Realtime is disabled by default in Supabase for better database performance and security. Learn more about <a href="../../docs/guides/realtime.html">realtime functionalities</a>.</p>
</blockquote>
<h2>Step 2: GET Data from Supabase</h2>
<p>Once you click on <code>Add a Collection</code>, you will be invited to give your Collection a name and choose Supabase as a Data source:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p>You will then be able to select the Table from which you want to pull data:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/CleanShot-2022-08-09-at-11.57.09.gif" alt=""/></p>
<p>Notice that this gives you access to 2 separate modes to access the fields in the table:</p>
<ol>
<li>a &quot;Guided&quot; mode, and</li>
<li>an &quot;Advanced&quot; mode.</li>
</ol>
<h3>Guided mode</h3>
<p>By default, the &quot;Guided&quot; mode returns the data from all the fields.</p>
<p>In the example below, we decide to exclude the data from the <code>created_at</code> field in our <code>vehicles</code> table:</p>
<p><em><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></em></p>
<p>As a result, WeWeb does not fetch the <code>created_at</code> field.</p>
<p>This is helpful because we can exclude data that we don&#x27;t want to load in the frontend, either because we don&#x27;t need it or because it&#x27;s confidential.</p>
<h3>Advanced mode</h3>
<p>In our database, we created 2 separate tables for vehicles and locations.</p>
<p>In the <code>vehicles</code> table, we made a reference to the <code>locations</code> table in our <code>location_id</code> field so we know where each car is:</p>
<p><em><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></em></p>
<p>The problem is, the link only gives us the id of the location in the <code>locations</code> table.</p>
<p>If you choose the &quot;Advanced&quot; mode, you will be able to get the <code>name</code> field of the location instead of the <code>id</code>.</p>
<p>How?</p>
<p>By <a href="https://supabase.com/docs/reference/javascript/select">making custom queries to Supabase</a>:</p>
<p><em><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/CleanShot-2022-08-18-at-12.06.17.gif" alt=""/></em></p>
<p>In the example above, we are telling Supabase:</p>
<ul>
<li>from the table selected in the Collection – in this case the <code>vehicles</code> table – please send me the data in the <code>id</code>, <code>model</code>, and <code>mileage</code> fields</li>
<li>look for the <code>location_id</code> in the <code>vehicles</code> table in the <code>locations</code> table and send me the data in the corresponding <code>name</code> field</li>
</ul>
<p>If we only ask for the data from the <code>location</code> field of the <code>vehicles</code> table, Supabase will only return the <code>id</code>:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p><strong>🚨 Warning 🚨</strong></p>
<blockquote>
<p>If you have enabled Row-Level Security in Supabase, make sure you have also <a href="https://supabase.com/docs/learn/auth-deep-dive/auth-policies">added a Policy</a> that allows users to read the data in the table. Otherwise, WeWeb won&#x27;t be able to get the data.</p>
</blockquote>
<h2>Step 3: Display Supabase Data in WeWeb</h2>
<p>Assuming you were able to fetch data from Supabase in a WeWeb Collection, you&#x27;ll be able to bind the data from that Collection on your WeWeb pages.</p>
<p>In the example below, we chose to display the car model and mileage in the <a href="https://docs.weweb.io/add-elements/elements/data-grid.html">Data Grid element</a> that comes out-of-the-box in WeWeb:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p>We chose this element because it includes a built-in inline editing mode we&#x27;ll want to use later for our CRUD operations.</p>
<h4>🔥 Pro Tip 🔥</h4>
<blockquote>
<p>In WeWeb, you can <a href="https://docs.weweb.io/binding-filtering/display-data.html">bind arrays of data to any Container</a>. Just bear in mind that the first child of the Container you bind the Collection to will be the repeated item. With that in mind, you might want the first child Element to be another Container with a number of items inside like a title, description, button or image.</p>
</blockquote>
<h2>Step 4: Update a record in Supabase</h2>
<p>Once you&#x27;ve added a Supabase Collection of data to WeWeb, you might want to allow users to manipulate the data in that Collection.</p>
<p>In order to do so, you&#x27;ll need to create a Workflow in WeWeb.</p>
<p>In the example below, we are using the &quot;Update row&quot; Workflow that comes by default with WeWeb&#x27;s Data Grid Element.</p>
<p>The trigger is <code>On Row update</code>.</p>
<p>Since we added the Supabase Data Source Plugin above, we have access to all the CRUD actions available in Supabase:</p>
<ul>
<li>Select</li>
<li>Insert</li>
<li>Update</li>
<li>Upsert</li>
<li>Delete</li>
</ul>
<p>In this case, we choose the &quot;Update&quot; action:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p>Then, in our &quot;Update&quot; action, we select the <code>vehicles</code> table and map the <code>id</code> to the id of the Workflow Event:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p>Finally, we tell WeWeb we want to update the <code>mileage</code> field in our Supabase table, and send the value in the <code>mileage</code> column of our Data Grid:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p>And that&#x27;s it!</p>
<p>If you switch to Preview mode, you will be update your Supabase table from your WeWeb Data Grid:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/CleanShot-2022-10-13-at-17.35.20.gif" alt=""/></p>
<p><strong>🔥 Pro Tip 🔥</strong></p>
<blockquote>
<p>By default, the fields in the Data Grid Element are Text fields but you can change the input type to Number if you need to send numerical data to your database:</p>
</blockquote>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<h2>Restrict who can modify a record in Supabase</h2>
<p>By default, all the data in the tables that are in the <code>public</code> schema of your Supabase project can be read, updated, or deleted.</p>
<p>Supabase allows you to <a href="https://supabase.com/docs/learn/auth-deep-dive/auth-row-level-security">enable Row-Level Security</a> for each of your tables:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p>If you want to restrict certain actions to specific users or roles, you&#x27;ll need to:</p>
<ul>
<li>add Supabase authentication to your WeWeb project, and</li>
<li><a href="https://supabase.com/docs/learn/auth-deep-dive/auth-policies">write SQL policies in Supabase</a>.</li>
</ul>
<p>We provide a number of policy templates to get you started:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p>In the example below, we say that users can:</p>
<ol>
<li>update a record</li>
<li>in the &quot;locations&quot; table of the &quot;public&quot; schema</li>
<li>if they are authenticated</li>
</ol>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p><strong>🔥 Pro Tip 🔥</strong></p>
<blockquote>
<p>Once you enable RLS on a Supabase table, you won&#x27;t be able to access the data in a WeWeb Collection unless you&#x27;ve added a policy.</p>
</blockquote>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/CleanShot-2022-10-13-at-16.28.51.gif" alt=""/></p>
<h2>Step 4: Add User Authentication</h2>
<p>Once you are able to display Supabase data in WeWeb, you might want to restrict access to certain users or display specific data based on a user&#x27;s role.</p>
<p>In order to do that, you&#x27;ll need to add WeWeb&#x27;s Supabase Auth Plugin.</p>
<h3>Add Supabase Auth Plugin in WeWeb</h3>
<p>Supabase comes with an in-built authentication system which you can use in WeWeb.</p>
<p>To add the Supabase Auth Plugin in WeWeb, go to <code>Plugins</code> &gt; <code>Authentication</code>:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p>Assuming you have already provided your Supabase project URL and public API key when setting up the Supabase Data source, the only thing left to do will be to add your private API key:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p>In Supabase, you can find your private API key in <code>Settings</code> &gt; <code>API</code>:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p><strong>🚨 Warning 🚨</strong></p>
<blockquote>
<p>As the name suggests, you&#x27;ll want to keep this API key secret! Assuming you copy it properly in the &quot;Private API key&quot; field of the Supabase Auth Plugin and don&#x27;t use it anywhere else in your Weweb project, Weweb will never make it public.</p>
</blockquote>
<p>You will then be invited to choose a page to redirect <em>unauthenticated</em> users, i.e. users who are NOT signed-in:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p><strong>🚨 Warning 🚨</strong></p>
<blockquote>
<p>When you setup your Login Workflow, make sure you don&#x27;t redirect unauthenticated users to a page that is only accessible to authenticated users. Otherwise, you&#x27;ll be creating an <strong>infinite loop</strong> and your app will crash.</p>
</blockquote>
<h3>Create User Sign Up and Sign In Workflows</h3>
<p>In the <code>Add</code> &gt; <code>UI kit</code> menu of WeWeb, you can find ready-made Sign in and Sign up Forms:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p>Once you&#x27;ve added a Form to the Canvas, you&#x27;ll be able to style it whichever way you want.</p>
<p>In the example below, we added an image with the logo of our project to a Sign up Form and changed the background color of the <code>Create Form</code> Container:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p>To allow users to sign up, you&#x27;ll need to create a Sign up Workflow on the Form Container:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p><strong>🔥 Pro Tip 🔥</strong></p>
<blockquote>
<p>It&#x27;s important that the Workflow is on the Form Container and not the Sign up Button because we want to validate the fields of the Form when users submit it.</p>
</blockquote>
<p>In the Workflow, you will choose the <code>On submit</code> trigger and add the Supabase <code>Sign up</code> Action:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/CleanShot-2022-08-19-at-11.58.23.gif" alt=""/></p>
<p>Then, you&#x27;ll want to map the email, password, and metadata information in the Form to the email, password, and metadata in Supabase before choosing what page the new user should be redirected to:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/CleanShot-2022-08-19-at-12.02.32.gif" alt=""/></p>
<p>In the example above, we made sure to add the user&#x27;s name as an item in that user&#x27;s metadata.</p>
<p>In Supabase, you can find the user&#x27;s metadata in JSON format in a dedicated field of the <code>users</code> table, named <code>raw_user_meta_data</code>:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p>The same logic will apply to any Supabase Action you want to trigger.</p>
<h2>Adding User Roles &amp; Permissions</h2>
<p>Now let&#x27;s say we want to gate content and set different permissions based on a user&#x27;s role.</p>
<h3>Adding Roles in Supabase</h3>
<p>In Supabase, we&#x27;ll need to create a <code>roles</code> table with a list of roles and a join table that links the <code>roles</code> table with our <code>users</code> table.</p>
<p>First, let&#x27;s create a <code>roles</code> table with three roles and make sure that each role had a UUID and a <code>name</code>:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p><strong>🚨 Warning 🚨</strong></p>
<blockquote>
<p>In order for the integration to work with the Users tab in WeWeb, it is crucial that the role title is a text field named <code>name</code>.</p>
</blockquote>
<h3>Joining Roles and Users in Supabase</h3>
<p>Second, let&#x27;s create a <code>userRoles</code> join table:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p>In the join table above, you can see we have an <code>id</code> field that is uniquely identifiable thanks to a UUID.</p>
<p>This unique <code>id</code> is linked to a <code>userId</code>, which is also a UUID, more specifically, it is the UUID we find in the <code>id</code> field of the <code>users</code> table in the <code>auth</code> schema:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p>Each row in our <code>userRoles</code> table is also linked to a <code>roleId</code> which is the UUID we find in the <code>id</code> field of the <code>roles</code> table in the <code>public</code> schema:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/CleanShot-2022-08-19-at-18.52.57.gif" alt=""/></p>
<h3>Linking Users in WeWeb to Roles and Users in Supabase</h3>
<p>Once we&#x27;ve added our list of roles in Supabase and created an empty join table to link our roles with users, it&#x27;s time to go to WeWeb.</p>
<p>In <code>Plugins</code> &gt; <code>Supabase Auth</code> &gt; <code>3. Roles table</code>, we&#x27;ll click <code>refresh</code> and select the relevant Supabase tables we just created:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<p>Once you&#x27;ve told WeWeb where to find the <code>roles</code> and the join table in Supabase, you&#x27;ll be able to easily view and maintain user roles in the <code>Users</code> tab in WeWeb:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/CleanShot-2022-08-19-at-19.03.13.gif" alt=""/></p>
<p>When you make a change to a User in WeWeb, it will automatically be updated in Supabase.</p>
<h2>Users vs Profiles</h2>
<p>So far, we&#x27;ve showed you how to work with the default <code>users</code> table that Supabase generates in the <code>auth</code> schema when you create a new project.</p>
<p>Note that, for security purposes, the information in that <code>users</code> table is not exposed on the auto-generated API.</p>
<p>How does this affect your project in WeWeb?</p>
<h3>Let users update their information</h3>
<p>Let&#x27;s say you want to let authenticated users update their information, then you don&#x27;t need to set up anything else in Supabase.</p>
<p>You could simply create a user profile page in WeWeb and display their information when they sign in, based on the data you have in the <code>user</code> Variable:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>" alt=""/></p>
<h3>Display other users&#x27; information</h3>
<p>In some use cases, you might want to display <em>other</em> users&#x27; information.</p>
<p>For example, if you&#x27;re building an HR portal in WeWeb, you might want HR employees to have access to a list of applicants and their user profiles.</p>
<p>You wouldn&#x27;t be able to do that with the <code>users</code> table in the <code>auth</code> schema because each user&#x27;s information is only available to them.</p>
<p>For such a use case, we recommend creating a <code>profiles</code> table in the <code>public</code> schema to store user data that you want to access via the API.</p>
<p>In WeWeb, you would then be able to create a Collection to get data from the <code>profiles</code> table.</p>
<p>Learn more about <a href="https://supabase.com/docs/guides/auth/managing-user-data">managing user data in Supabase</a>.</p></div></div><div class="lg:col-span-3"><div class="sticky top-20 flex flex-col gap-4"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Details</h2><button class="w-full w-full"><div class="video-container overflow-hidden rounded hover:cursor-pointer"><div class=" absolute inset-0 z-10 text-white flex flex-col gap-3 items-center justify-center bg-alternative before:content[&#x27;&#x27;] before:absolute before:inset-0 before:bg-black before:opacity-30 before:-z-10 hover:before:opacity-50 before:transition-opacity "><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play w-5 h-5"><polygon points="6 3 20 12 6 21 6 3"></polygon></svg><p class="text-sm">Watch an introductory video</p></div><img alt="Video guide preview" loading="lazy" decoding="async" data-nimg="fill" class="absolute inset-0 object-cover blur-sm scale-105" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=3840&amp;q=75"/></div></button><div class="text-foreground divide-y"><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Developer</span><span class="text-foreground">WeWeb</span></div><div class="flex items-center justify-between py-2"><span class="text-lighter">Category</span><a class="text-brand hover:underline transition-colors" href="../integrations.html#low-code">Low-Code</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Website</span><a href="https://www.weweb.io/" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors">www.weweb.io</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Documentation</span><a href="https://www.weweb.io/" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors"><span class="flex items-center space-x-1"><span>Learn</span><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></span></a></div></div><p class="text-foreground-light text-sm">Third-party integrations and docs are managed by Supabase partners.</p></div></div></div></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"partner":{"id":42,"slug":"weweb","type":"technology","category":"Low-Code","developer":"WeWeb","title":"WeWeb","description":"WeWeb is a no-code tool that allows you to build user interfaces on top of existing databases.","logo":"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/weweb_logo.jpeg","images":["https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/weweb_og.png"],"overview":"WeWeb is a no-code tool that allows you to build user interfaces on top of existing databases.\n\nIt allies the short learning curve of no-code with the freedom of code.\n\nYou can build complete frontends with drag-and-drop UI elements and no-code CSS properties but have the option to add custom code or import your own Vue.js components.\n\nYou can follow development best practices by building global workflows and re-usable functions with no-code triggers and formulas but also have the option to add custom JS where needed.\n\nWeWeb generates a standard PWA-ready Single Page Application and allows you to deploy your projects on your own infrastructure. Build in no-code, host on-premise.\n\n## Documentation\n\nThis guide explains how to connect a Supabase back-end to a WeWeb front-end and then configure all the CRUD operations necessary to build an Admin Portal with user authentication, roles, and permissions.\n\n[WeWeb](https://dashboard.weweb.io/sign-up) is a low-code front-end builder that allies the short learning curve of no-code with the freedom of code.\n\nIt connects to Supabase via two native integrations:\n\n- one for data manipulation, and\n- another for user authentication.\n\nIf you don't have an WeWeb account, you can create one [here](https://dashboard.weweb.io/sign-up).\n\nLet's get started!\n\n## Step 1: Add the Supabase Data Source Plugin in WeWeb\n\nIn order to read Supabase data in WeWeb, you'll first need to add the Supabase Data Source Plugin:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\nOnce you've added it, you will be invited to share your Supabase project URL and public API key:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\nIn Supabase, you can find both your project URL and public key in the `Settings` \u003e `API` menu:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\nOnce you have added both to WeWeb, you will have the option to enable realtime tables if you wish to do so:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\n**🚨 Warning 🚨**\n\n\u003e Realtime is disabled by default in Supabase for better database performance and security. Learn more about [realtime functionalities](/docs/guides/realtime).\n\n## Step 2: GET Data from Supabase\n\nOnce you click on `Add a Collection`, you will be invited to give your Collection a name and choose Supabase as a Data source:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\nYou will then be able to select the Table from which you want to pull data:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/CleanShot-2022-08-09-at-11.57.09.gif)\n\nNotice that this gives you access to 2 separate modes to access the fields in the table:\n\n1.  a \"Guided\" mode, and\n2.  an \"Advanced\" mode.\n\n### Guided mode\n\nBy default, the \"Guided\" mode returns the data from all the fields.\n\nIn the example below, we decide to exclude the data from the `created_at` field in our `vehicles` table:\n\n_![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)_\n\nAs a result, WeWeb does not fetch the `created_at` field.\n\nThis is helpful because we can exclude data that we don't want to load in the frontend, either because we don't need it or because it's confidential.\n\n### Advanced mode\n\nIn our database, we created 2 separate tables for vehicles and locations.\n\nIn the `vehicles` table, we made a reference to the `locations` table in our `location_id` field so we know where each car is:\n\n_![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)_\n\nThe problem is, the link only gives us the id of the location in the `locations` table.\n\nIf you choose the \"Advanced\" mode, you will be able to get the `name` field of the location instead of the `id`.\n\nHow?\n\nBy [making custom queries to Supabase](https://supabase.com/docs/reference/javascript/select):\n\n_![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/CleanShot-2022-08-18-at-12.06.17.gif)_\n\nIn the example above, we are telling Supabase:\n\n- from the table selected in the Collection – in this case the `vehicles` table – please send me the data in the `id`, `model`, and `mileage` fields\n- look for the `location_id` in the `vehicles` table in the `locations` table and send me the data in the corresponding `name` field\n\nIf we only ask for the data from the `location` field of the `vehicles` table, Supabase will only return the `id`:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\n**🚨 Warning 🚨**\n\n\u003e If you have enabled Row-Level Security in Supabase, make sure you have also [added a Policy](https://supabase.com/docs/learn/auth-deep-dive/auth-policies) that allows users to read the data in the table. Otherwise, WeWeb won't be able to get the data.\n\n## Step 3: Display Supabase Data in WeWeb\n\nAssuming you were able to fetch data from Supabase in a WeWeb Collection, you'll be able to bind the data from that Collection on your WeWeb pages.\n\nIn the example below, we chose to display the car model and mileage in the [Data Grid element](https://docs.weweb.io/add-elements/elements/data-grid.html) that comes out-of-the-box in WeWeb:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\nWe chose this element because it includes a built-in inline editing mode we'll want to use later for our CRUD operations.\n\n#### 🔥 Pro Tip 🔥\n\n\u003e In WeWeb, you can [bind arrays of data to any Container](https://docs.weweb.io/binding-filtering/display-data.html). Just bear in mind that the first child of the Container you bind the Collection to will be the repeated item. With that in mind, you might want the first child Element to be another Container with a number of items inside like a title, description, button or image.\n\n## Step 4: Update a record in Supabase\n\nOnce you've added a Supabase Collection of data to WeWeb, you might want to allow users to manipulate the data in that Collection.\n\nIn order to do so, you'll need to create a Workflow in WeWeb.\n\nIn the example below, we are using the \"Update row\" Workflow that comes by default with WeWeb's Data Grid Element.\n\nThe trigger is `On Row update`.\n\nSince we added the Supabase Data Source Plugin above, we have access to all the CRUD actions available in Supabase:\n\n- Select\n- Insert\n- Update\n- Upsert\n- Delete\n\nIn this case, we choose the \"Update\" action:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\nThen, in our \"Update\" action, we select the `vehicles` table and map the `id` to the id of the Workflow Event:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\nFinally, we tell WeWeb we want to update the `mileage` field in our Supabase table, and send the value in the `mileage` column of our Data Grid:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\nAnd that's it!\n\nIf you switch to Preview mode, you will be update your Supabase table from your WeWeb Data Grid:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/CleanShot-2022-10-13-at-17.35.20.gif)\n\n**🔥 Pro Tip 🔥**\n\n\u003e By default, the fields in the Data Grid Element are Text fields but you can change the input type to Number if you need to send numerical data to your database:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\n## Restrict who can modify a record in Supabase\n\nBy default, all the data in the tables that are in the `public` schema of your Supabase project can be read, updated, or deleted.\n\nSupabase allows you to [enable Row-Level Security](https://supabase.com/docs/learn/auth-deep-dive/auth-row-level-security) for each of your tables:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\nIf you want to restrict certain actions to specific users or roles, you'll need to:\n\n- add Supabase authentication to your WeWeb project, and\n- [write SQL policies in Supabase](https://supabase.com/docs/learn/auth-deep-dive/auth-policies).\n\nWe provide a number of policy templates to get you started:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\nIn the example below, we say that users can:\n\n1.  update a record\n2.  in the \"locations\" table of the \"public\" schema\n3.  if they are authenticated\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\n**🔥 Pro Tip 🔥**\n\n\u003e Once you enable RLS on a Supabase table, you won't be able to access the data in a WeWeb Collection unless you've added a policy.\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/CleanShot-2022-10-13-at-16.28.51.gif)\n\n## Step 4: Add User Authentication\n\nOnce you are able to display Supabase data in WeWeb, you might want to restrict access to certain users or display specific data based on a user's role.\n\nIn order to do that, you'll need to add WeWeb's Supabase Auth Plugin.\n\n### Add Supabase Auth Plugin in WeWeb\n\nSupabase comes with an in-built authentication system which you can use in WeWeb.\n\nTo add the Supabase Auth Plugin in WeWeb, go to `Plugins` \u003e `Authentication`:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\nAssuming you have already provided your Supabase project URL and public API key when setting up the Supabase Data source, the only thing left to do will be to add your private API key:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\nIn Supabase, you can find your private API key in `Settings` \u003e `API`:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\n**🚨 Warning 🚨**\n\n\u003e As the name suggests, you'll want to keep this API key secret! Assuming you copy it properly in the \"Private API key\" field of the Supabase Auth Plugin and don't use it anywhere else in your Weweb project, Weweb will never make it public.\n\nYou will then be invited to choose a page to redirect _unauthenticated_ users, i.e. users who are NOT signed-in:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\n**🚨 Warning 🚨**\n\n\u003e When you setup your Login Workflow, make sure you don't redirect unauthenticated users to a page that is only accessible to authenticated users. Otherwise, you'll be creating an **infinite loop** and your app will crash.\n\n### Create User Sign Up and Sign In Workflows\n\nIn the `Add` \u003e `UI kit` menu of WeWeb, you can find ready-made Sign in and Sign up Forms:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\nOnce you've added a Form to the Canvas, you'll be able to style it whichever way you want.\n\nIn the example below, we added an image with the logo of our project to a Sign up Form and changed the background color of the `Create Form` Container:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\nTo allow users to sign up, you'll need to create a Sign up Workflow on the Form Container:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\n**🔥 Pro Tip 🔥**\n\n\u003e It's important that the Workflow is on the Form Container and not the Sign up Button because we want to validate the fields of the Form when users submit it.\n\nIn the Workflow, you will choose the `On submit` trigger and add the Supabase `Sign up` Action:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/CleanShot-2022-08-19-at-11.58.23.gif)\n\nThen, you'll want to map the email, password, and metadata information in the Form to the email, password, and metadata in Supabase before choosing what page the new user should be redirected to:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/CleanShot-2022-08-19-at-12.02.32.gif)\n\nIn the example above, we made sure to add the user's name as an item in that user's metadata.\n\nIn Supabase, you can find the user's metadata in JSON format in a dedicated field of the `users` table, named `raw_user_meta_data`:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\nThe same logic will apply to any Supabase Action you want to trigger.\n\n## Adding User Roles \u0026 Permissions\n\nNow let's say we want to gate content and set different permissions based on a user's role.\n\n### Adding Roles in Supabase\n\nIn Supabase, we'll need to create a `roles` table with a list of roles and a join table that links the `roles` table with our `users` table.\n\nFirst, let's create a `roles` table with three roles and make sure that each role had a UUID and a `name`:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\n**🚨 Warning 🚨**\n\n\u003e In order for the integration to work with the Users tab in WeWeb, it is crucial that the role title is a text field named `name`.\n\n### Joining Roles and Users in Supabase\n\nSecond, let's create a `userRoles` join table:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\nIn the join table above, you can see we have an `id` field that is uniquely identifiable thanks to a UUID.\n\nThis unique `id` is linked to a `userId`, which is also a UUID, more specifically, it is the UUID we find in the `id` field of the `users` table in the `auth` schema:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\nEach row in our `userRoles` table is also linked to a `roleId` which is the UUID we find in the `id` field of the `roles` table in the `public` schema:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/CleanShot-2022-08-19-at-18.52.57.gif)\n\n### Linking Users in WeWeb to Roles and Users in Supabase\n\nOnce we've added our list of roles in Supabase and created an empty join table to link our roles with users, it's time to go to WeWeb.\n\nIn `Plugins` \u003e `Supabase Auth` \u003e `3. Roles table`, we'll click `refresh` and select the relevant Supabase tables we just created:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\nOnce you've told WeWeb where to find the `roles` and the join table in Supabase, you'll be able to easily view and maintain user roles in the `Users` tab in WeWeb:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/CleanShot-2022-08-19-at-19.03.13.gif)\n\nWhen you make a change to a User in WeWeb, it will automatically be updated in Supabase.\n\n## Users vs Profiles\n\nSo far, we've showed you how to work with the default `users` table that Supabase generates in the `auth` schema when you create a new project.\n\nNote that, for security purposes, the information in that `users` table is not exposed on the auto-generated API.\n\nHow does this affect your project in WeWeb?\n\n### Let users update their information\n\nLet's say you want to let authenticated users update their information, then you don't need to set up anything else in Supabase.\n\nYou could simply create a user profile page in WeWeb and display their information when they sign in, based on the data you have in the `user` Variable:\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)\n\n### Display other users' information\n\nIn some use cases, you might want to display _other_ users' information.\n\nFor example, if you're building an HR portal in WeWeb, you might want HR employees to have access to a list of applicants and their user profiles.\n\nYou wouldn't be able to do that with the `users` table in the `auth` schema because each user's information is only available to them.\n\nFor such a use case, we recommend creating a `profiles` table in the `public` schema to store user data that you want to access via the API.\n\nIn WeWeb, you would then be able to create a Collection to get data from the `profiles` table.\n\nLearn more about [managing user data in Supabase](https://supabase.com/docs/guides/auth/managing-user-data).\n","website":"https://www.weweb.io/","docs":"https://www.weweb.io/","contact":121,"approved":true,"created_at":"2022-11-02T09:10:49.596443+00:00","tsv":"'/add-elements/elements/data-grid.html)':796C '/binding-filtering/display-data.html).':848C '/docs/guides/auth/managing-user-data).':2356C '/docs/guides/realtime':370C '/docs/learn/auth-deep-dive/auth-policies)':720C '/docs/learn/auth-deep-dive/auth-policies).':1204C '/docs/learn/auth-deep-dive/auth-row-level-security)':1164C '/docs/reference/javascript/select):':615C '/sign-up)':190C '/sign-up).':247C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':283C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':305C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':325C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':403C '/storage/v1/object/public/images/integrations/weweb/documentation/cleanshot-2022-08-09-at-11.57.09.gif)':422C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)_':484C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)_':566C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':698C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1365C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1402C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1417C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1488C '/storage/v1/object/public/images/integrations/weweb/documentation/cleanshot-2022-08-18-at-12.06.17.gif)_':618C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':350C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1557C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1609C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1631C '/storage/v1/object/public/images/integrations/weweb/documentation/cleanshot-2022-08-19-at-11.58.23.gif)':1684C '/storage/v1/object/public/images/integrations/weweb/documentation/cleanshot-2022-08-19-at-12.02.32.gif)':1721C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1770C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1863C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1907C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1964C '/storage/v1/object/public/images/integrations/weweb/documentation/cleanshot-2022-08-19-at-18.52.57.gif)':1997C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':2059C '/storage/v1/object/public/images/integrations/weweb/documentation/cleanshot-2022-08-19-at-19.03.13.gif)':2095C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':2230C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1005C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1172C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1218C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1248C '/storage/v1/object/public/images/integrations/weweb/documentation/cleanshot-2022-10-13-at-16.28.51.gif)':1280C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1029C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1059C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1120C '/storage/v1/object/public/images/integrations/weweb/documentation/cleanshot-2022-10-13-at-17.35.20.gif)':1084C '/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':808C '1':253C,440C,1228C '2':372C,430C,445C,533C,1232C '3':742C,1241C,2041C '4':899C,1282C 'abl':408C,593C,736C,751C,764C,1263C,1289C,1570C,2079C,2279C,2334C 'access':428C,434C,981C,1265C,1301C,1510C,2265C,2324C 'account':239C 'action':986C,1002C,1010C,1179C,1681C,1779C 'ad':287C,329C,715C,908C,972C,1275C,1561C,1583C,1784C,1807C,2011C 'add':78C,115C,254C,275C,381C,1189C,1283C,1324C,1330C,1352C,1395C,1537C,1676C,1730C 'admin':179C 'advanc':447C,526C,588C 'affect':2166C 'alli':41C,201C 'allow':10B,29C,131C,722C,920C,1154C,1611C 'alreadi':1369C 'also':110C,714C,1939C,1972C 'anoth':228C,883C 'anyth':2196C 'anywher':1453C 'api':301C,321C,1377C,1398C,1410C,1414C,1429C,1440C,2162C,2327C 'app':1524C 'appli':1775C 'applic':129C,2270C 'array':840C 'ask':678C 'assum':748C,1366C,1432C 'auth':1328C,1332C,1355C,1446C,1960C,2040C,2135C,2289C 'authent':183C,231C,1191C,1245C,1285C,1343C,1362C,1512C,2183C 'auto':2160C 'auto-gener':2159C 'automat':2108C 'avail':987C,2298C 'back':160C 'back-end':159C 'background':1600C 'base':1309C,1801C,2218C 'bear':850C 'best':91C 'better':360C 'bind':766C,839C,861C 'box':803C 'build':13B,32C,57C,94C,141C,177C,2252C 'builder':199C 'built':818C,1342C 'built-in':817C 'button':895C,1650C 'canva':1566C 'car':562C,785C 'case':636C,997C,2238C,2305C 'certain':1178C,1303C 'chang':1102C,1598C,2100C 'child':856C,879C 'choos':395C,586C,999C,1471C,1670C,1709C 'chose':781C,810C 'click':379C,2046C 'code':7B,26C,49C,54C,70C,80C,105C,145C,195C,209C,214C,2359 'collect':383C,391C,633C,760C,771C,863C,911C,928C,1271C,2338C 'color':1601C 'column':1052C 'come':798C,955C,1337C 'complet':58C 'compon':86C 'confidenti':525C 'configur':170C 'connect':156C,216C 'contain':845C,859C,884C,1606C,1628C,1644C 'content':1796C 'copi':1434C 'correspond':672C 'could':2201C 'crash':1526C 'creat':242C,475C,493C,532C,938C,1518C,1527C,1604C,1620C,1817C,1842C,1900C,2019C,2056C,2139C,2203C,2308C,2336C 'crucial':1880C 'crud':173C,831C,985C 'css':71C 'curv':45C,205C 'custom':79C,116C,609C 'dashboard.weweb.io':189C,246C 'dashboard.weweb.io/sign-up)':188C 'dashboard.weweb.io/sign-up).':245C 'data':225C,257C,267C,278C,374C,399C,419C,458C,472C,503C,644C,669C,681C,727C,740C,745C,754C,768C,791C,842C,913C,925C,961C,975C,1055C,1080C,1093C,1114C,1133C,1267C,1293C,1308C,1384C,1767C,2221C,2319C,2341C,2351C 'databas':20B,39C,361C,530C,1117C 'decid':468C 'dedic':1757C 'default':356C,452C,957C,1088C,1130C,2127C 'delet':994C,1152C 'deploy':134C 'descript':894C 'develop':90C 'differ':1799C 'disabl':354C 'display':743C,783C,1291C,1306C,2211C,2231C,2243C 'docs.weweb.io':795C,847C 'docs.weweb.io/add-elements/elements/data-grid.html)':794C 'docs.weweb.io/binding-filtering/display-data.html).':846C 'document':150C 'drag':62C 'drag-and-drop':61C 'drop':64C 'easili':2081C 'edit':821C 'either':514C 'element':66C,793C,812C,880C,963C,1095C 'els':1454C,2197C 'email':1692C,1702C 'employe':2262C 'empti':2021C 'enabl':339C,703C,1157C,1253C 'end':161C,167C,198C 'event':1026C 'exampl':465C,621C,778C,945C,1221C,1580C,1724C,2248C 'exclud':470C,502C 'exist':19B,38C 'explain':153C 'expos':2156C 'far':2117C 'fetch':491C,753C 'field':436C,462C,477C,495C,556C,598C,651C,674C,685C,1040C,1090C,1098C,1442C,1657C,1758C,1888C,1920C,1953C,1986C 'final':1030C 'find':310C,1407C,1545C,1747C,1949C,1982C,2067C 'first':272C,855C,878C,1839C 'follow':89C 'form':1554C,1563C,1596C,1605C,1627C,1643C,1660C,1699C 'format':1754C 'formula':108C 'freedom':52C,212C 'front':166C,197C 'front-end':165C,196C 'frontend':59C,513C 'function':101C,369C 'gate':1795C 'generat':121C,2132C,2161C 'get':250C,373C,595C,738C,1213C,2340C 'give':389C,426C,573C 'global':95C 'go':1359C,2034C 'grid':792C,962C,1056C,1081C,1094C 'guid':152C,442C,449C,454C 'help':498C 'host':146C 'hr':2254C,2261C 'i.e':1478C 'id':555C,576C,605C,647C,656C,695C,1019C,1022C,1919C,1931C,1952C,1985C 'identifi':1924C 'imag':897C,1585C 'import':82C,1636C 'in-built':1340C 'includ':815C 'infinit':1520C 'inform':1696C,2149C,2175C,2187C,2213C,2234C,2246C,2295C 'infrastructur':140C 'inlin':820C 'input':1104C 'insert':991C 'insid':890C 'instead':602C 'integr':222C,1869C 'interfac':15B,34C 'invit':292C,387C,1469C 'item':869C,889C,1737C 'join':1828C,1891C,1903C,1910C,2022C,2072C 'js':117C 'json':1753C 'keep':1427C 'key':302C,317C,1378C,1399C,1411C,1430C,1441C 'kit':1539C 'know':559C 'later':828C 'learn':44C,204C,365C,2346C 'left':1389C 'let':248C,1789C,1840C,1898C,2171C,2176C,2182C 'level':706C,1160C 'like':891C 'link':571C,1831C,1933C,1973C,1998C,2025C 'list':1823C,2013C,2268C 'll':271C,762C,824C,935C,1186C,1321C,1424C,1516C,1568C,1617C,1687C,1814C,2045C,2077C 'load':510C 'locat':539C,550C,554C,579C,582C,601C,655C,663C,684C,1235C 'logic':1773C 'login':1494C 'logo':1588C 'look':652C 'loop':1521C 'low':194C,2358 'low-cod':193C,2357 'made':545C,1548C,1727C 'maintain':2084C 'make':608C,710C,1462C,1496C,1850C,2098C 'manag':2349C 'manipul':226C,923C 'map':1017C,1690C 'menu':322C,1540C 'meta':1766C 'metadata':1695C,1705C,1742C,1751C 'might':875C,917C,1297C,2240C,2259C 'mileag':650C,788C,1039C,1051C 'mind':852C,873C 'mode':432C,443C,448C,450C,455C,527C,589C,822C,1069C 'model':648C,786C 'modifi':1124C 'name':393C,597C,673C,1421C,1734C,1763C,1860C,1889C,1890C 'nativ':221C 'necessari':175C 'need':119C,273C,519C,936C,1110C,1187C,1322C,1618C,1815C,2192C 'never':1461C 'new':1713C,2141C 'no-cod':5B,24C,47C,68C,103C,143C,207C 'note':2143C 'notic':423C 'number':887C,1107C,1208C 'numer':1113C 'obuldanrptloktxcffvn.supabase.co':282C,304C,324C,349C,402C,421C,483C,565C,617C,697C,807C,1004C,1028C,1058C,1083C,1119C,1171C,1217C,1247C,1279C,1364C,1401C,1416C,1487C,1556C,1608C,1630C,1683C,1720C,1769C,1862C,1906C,1963C,1996C,2058C,2094C,2229C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':281C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':303C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':323C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':401C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/cleanshot-2022-08-09-at-11.57.09.gif)':420C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)_':482C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)_':564C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':696C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1363C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1400C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1415C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1486C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/cleanshot-2022-08-18-at-12.06.17.gif)_':616C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':348C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1555C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1607C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1629C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/cleanshot-2022-08-19-at-11.58.23.gif)':1682C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/cleanshot-2022-08-19-at-12.02.32.gif)':1719C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1768C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1861C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1905C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1962C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/cleanshot-2022-08-19-at-18.52.57.gif)':1995C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':2057C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/cleanshot-2022-08-19-at-19.03.13.gif)':2093C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':2228C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1003C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1170C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1216C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1246C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/cleanshot-2022-10-13-at-16.28.51.gif)':1278C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1027C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1057C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':1118C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/cleanshot-2022-10-13-at-17.35.20.gif)':1082C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>)':806C 'on-premis':147C 'one':223C,243C 'oper':174C,832C 'option':76C,113C,337C 'order':263C,930C,1316C,1866C 'otherwis':731C,1514C 'out-of-the-box':799C 'page':128C,775C,1473C,1506C,1711C,2207C 'password':1693C,1703C 'perform':362C 'permiss':186C,1787C,1800C 'pleas':640C 'plugin':259C,280C,977C,1329C,1333C,1356C,1361C,1447C,2038C 'polici':717C,1199C,1210C,1277C 'portal':180C,2255C 'practic':92C 'premis':149C 'preview':1068C 'privat':1397C,1409C,1439C 'pro':833C,1085C,1249C,1632C 'problem':568C 'profil':2115C,2206C,2274C,2310C,2344C 'project':136C,297C,313C,1146C,1195C,1373C,1458C,1591C,2142C,2168C 'proper':1436C 'properti':72C 'provid':1206C,1370C 'public':300C,316C,1141C,1239C,1376C,1464C,1993C,2314C 'pull':418C 'purpos':2147C 'pwa':125C 'pwa-readi':124C 'queri':610C 'raw':1764C 're':99C,2251C 're-us':98C 'read':265C,725C,1149C 'readi':126C,1547C 'ready-mad':1546C 'realtim':340C,352C,368C 'recommend':2307C 'record':902C,1126C,1231C 'redirect':1475C,1501C,1717C 'refer':547C 'refresh':2047C 'relev':2051C 'repeat':868C 'restrict':1121C,1177C,1300C 'result':487C 'return':456C,693C 'rls':1254C 'role':184C,1184C,1314C,1786C,1806C,1808C,1819C,1825C,1833C,1844C,1848C,1854C,1883C,1892C,1989C,2003C,2015C,2027C,2042C,2069C,2086C 'roleid':1976C 'row':705C,952C,968C,1159C,1966C 'row-level':704C,1158C 'say':1224C,1791C,2178C 'schema':1142C,1240C,1961C,1994C,2136C,2290C,2315C 'second':1897C 'secret':1431C 'secur':364C,707C,1161C,2146C 'see':1915C 'select':410C,630C,990C,1012C,2049C 'send':641C,666C,1046C,1112C 'separ':431C,534C 'set':320C,1380C,1413C,1798C,2194C 'setup':1492C 'share':294C 'short':43C,203C 'show':2120C 'sign':1484C,1529C,1532C,1549C,1552C,1594C,1614C,1622C,1648C,1679C,2216C 'signed-in':1483C 'simpli':2202C 'sinc':970C 'singl':127C 'sourc':258C,279C,400C,976C,1385C 'specif':1181C,1307C,1943C 'sql':1198C 'standard':123C 'start':251C,1215C 'step':252C,371C,741C,898C,1281C 'store':2317C 'style':1572C 'submit':1663C,1673C 'suggest':1422C 'supabas':158C,218C,256C,266C,277C,296C,307C,358C,376C,396C,612C,626C,690C,709C,744C,756C,904C,910C,974C,989C,1043C,1075C,1128C,1145C,1153C,1190C,1201C,1257C,1292C,1327C,1331C,1336C,1354C,1372C,1383C,1404C,1445C,1678C,1707C,1744C,1778C,1810C,1812C,1896C,2007C,2017C,2039C,2052C,2075C,2112C,2131C,2199C,2353C 'supabase.com':614C,719C,1163C,1203C,2355C 'supabase.com/docs/guides/auth/managing-user-data).':2354C 'supabase.com/docs/learn/auth-deep-dive/auth-policies)':718C 'supabase.com/docs/learn/auth-deep-dive/auth-policies).':1202C 'supabase.com/docs/learn/auth-deep-dive/auth-row-level-security)':1162C 'supabase.com/docs/reference/javascript/select):':613C 'sure':711C,1497C,1728C,1851C 'switch':1066C 'system':1344C 'tab':1875C,2090C 'tabl':341C,412C,439C,481C,535C,543C,551C,583C,629C,639C,660C,664C,689C,730C,1015C,1044C,1076C,1136C,1169C,1236C,1258C,1762C,1820C,1829C,1834C,1838C,1845C,1904C,1911C,1957C,1970C,1990C,2023C,2043C,2053C,2073C,2129C,2153C,2286C,2311C,2345C 'tell':625C,1032C 'templat':1211C 'text':1097C,1887C 'thank':1925C 'thing':1388C 'three':1847C 'time':2032C 'tip':834C,1086C,1250C,1633C 'titl':893C,1884C 'told':2063C 'tool':8B,27C 'top':17B,36C 'trigger':106C,965C,1674C,1783C 'two':220C 'type':1105C 'ui':65C,1538C 'unauthent':1476C,1502C 'uniqu':1923C,1930C 'unless':1272C 'updat':900C,951C,969C,992C,1001C,1009C,1037C,1073C,1150C,1229C,2110C,2173C,2185C 'upsert':993C 'url':298C,314C,1374C 'us':574C 'usabl':100C 'use':827C,949C,1348C,1451C,2237C,2304C 'user':14B,33C,182C,230C,723C,921C,1182C,1226C,1284C,1304C,1312C,1477C,1479C,1503C,1513C,1528C,1612C,1662C,1714C,1732C,1740C,1749C,1761C,1765C,1785C,1804C,1837C,1874C,1894C,1956C,1999C,2005C,2029C,2085C,2089C,2103C,2113C,2128C,2152C,2172C,2184C,2205C,2226C,2233C,2245C,2273C,2285C,2293C,2318C,2350C 'userid':1936C 'userrol':1902C,1969C 'uuid':1857C,1928C,1941C,1947C,1980C 'valid':1655C 'valu':1048C 'variabl':2227C 've':286C,907C,1274C,1560C,2010C,2062C,2119C 'vehicl':480C,537C,542C,638C,659C,688C,1014C 'via':219C,2325C 'view':2082C 'vs':2114C 'vue.js':85C 'want':416C,508C,825C,876C,918C,1035C,1175C,1298C,1425C,1577C,1653C,1688C,1781C,1793C,2180C,2241C,2260C,2322C 'warn':351C,699C,1418C,1489C,1864C 'way':1575C 'weweb':1A,2B,21C,120C,164C,187C,238C,261C,269C,332C,488C,732C,747C,759C,774C,805C,836C,915C,942C,959C,1033C,1079C,1194C,1270C,1295C,1325C,1335C,1350C,1358C,1457C,1459C,1542C,1877C,2001C,2036C,2064C,2092C,2105C,2170C,2209C,2257C,2329C,2360 'whichev':1574C 'wish':344C 'won':733C,1260C 'work':1871C,2124C 'workflow':96C,940C,953C,1025C,1495C,1534C,1624C,1639C,1667C 'would':2331C 'wouldn':2276C 'write':1197C","video":"brs4u19BMOo","call_to_action_link":null,"featured":false},"overview":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    h2: \"h2\",\n    a: \"a\",\n    ul: \"ul\",\n    li: \"li\",\n    img: \"img\",\n    code: \"code\",\n    strong: \"strong\",\n    blockquote: \"blockquote\",\n    ol: \"ol\",\n    h3: \"h3\",\n    em: \"em\",\n    h4: \"h4\"\n  }, _provideComponents(), props.components);\n  return _jsxs(_Fragment, {\n    children: [_jsx(_components.p, {\n      children: \"WeWeb is a no-code tool that allows you to build user interfaces on top of existing databases.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"It allies the short learning curve of no-code with the freedom of code.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You can build complete frontends with drag-and-drop UI elements and no-code CSS properties but have the option to add custom code or import your own Vue.js components.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You can follow development best practices by building global workflows and re-usable functions with no-code triggers and formulas but also have the option to add custom JS where needed.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"WeWeb generates a standard PWA-ready Single Page Application and allows you to deploy your projects on your own infrastructure. Build in no-code, host on-premise.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Documentation\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This guide explains how to connect a Supabase back-end to a WeWeb front-end and then configure all the CRUD operations necessary to build an Admin Portal with user authentication, roles, and permissions.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://dashboard.weweb.io/sign-up\",\n        children: \"WeWeb\"\n      }), \" is a low-code front-end builder that allies the short learning curve of no-code with the freedom of code.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"It connects to Supabase via two native integrations:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"one for data manipulation, and\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"another for user authentication.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you don't have an WeWeb account, you can create one \", _jsx(_components.a, {\n        href: \"https://dashboard.weweb.io/sign-up\",\n        children: \"here\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Let's get started!\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 1: Add the Supabase Data Source Plugin in WeWeb\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In order to read Supabase data in WeWeb, you'll first need to add the Supabase Data Source Plugin:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Once you've added it, you will be invited to share your Supabase project URL and public API key:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In Supabase, you can find both your project URL and public key in the \", _jsx(_components.code, {\n        children: \"Settings\"\n      }), \" \u003e \", _jsx(_components.code, {\n        children: \"API\"\n      }), \" menu:\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Once you have added both to WeWeb, you will have the option to enable realtime tables if you wish to do so:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.strong, {\n        children: \"🚨 Warning 🚨\"\n      })\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsxs(_components.p, {\n        children: [\"Realtime is disabled by default in Supabase for better database performance and security. Learn more about \", _jsx(_components.a, {\n          href: \"/docs/guides/realtime\",\n          children: \"realtime functionalities\"\n        }), \".\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 2: GET Data from Supabase\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Once you click on \", _jsx(_components.code, {\n        children: \"Add a Collection\"\n      }), \", you will be invited to give your Collection a name and choose Supabase as a Data source:\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You will then be able to select the Table from which you want to pull data:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/CleanShot-2022-08-09-at-11.57.09.gif\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Notice that this gives you access to 2 separate modes to access the fields in the table:\"\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"a \\\"Guided\\\" mode, and\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"an \\\"Advanced\\\" mode.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Guided mode\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"By default, the \\\"Guided\\\" mode returns the data from all the fields.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In the example below, we decide to exclude the data from the \", _jsx(_components.code, {\n        children: \"created_at\"\n      }), \" field in our \", _jsx(_components.code, {\n        children: \"vehicles\"\n      }), \" table:\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.em, {\n        children: _jsx(_components.img, {\n          src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n          alt: \"\"\n        })\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"As a result, WeWeb does not fetch the \", _jsx(_components.code, {\n        children: \"created_at\"\n      }), \" field.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This is helpful because we can exclude data that we don't want to load in the frontend, either because we don't need it or because it's confidential.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Advanced mode\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In our database, we created 2 separate tables for vehicles and locations.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In the \", _jsx(_components.code, {\n        children: \"vehicles\"\n      }), \" table, we made a reference to the \", _jsx(_components.code, {\n        children: \"locations\"\n      }), \" table in our \", _jsx(_components.code, {\n        children: \"location_id\"\n      }), \" field so we know where each car is:\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.em, {\n        children: _jsx(_components.img, {\n          src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n          alt: \"\"\n        })\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The problem is, the link only gives us the id of the location in the \", _jsx(_components.code, {\n        children: \"locations\"\n      }), \" table.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you choose the \\\"Advanced\\\" mode, you will be able to get the \", _jsx(_components.code, {\n        children: \"name\"\n      }), \" field of the location instead of the \", _jsx(_components.code, {\n        children: \"id\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"How?\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"By \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/reference/javascript/select\",\n        children: \"making custom queries to Supabase\"\n      }), \":\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.em, {\n        children: _jsx(_components.img, {\n          src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/CleanShot-2022-08-18-at-12.06.17.gif\",\n          alt: \"\"\n        })\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In the example above, we are telling Supabase:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"from the table selected in the Collection – in this case the \", _jsx(_components.code, {\n          children: \"vehicles\"\n        }), \" table – please send me the data in the \", _jsx(_components.code, {\n          children: \"id\"\n        }), \", \", _jsx(_components.code, {\n          children: \"model\"\n        }), \", and \", _jsx(_components.code, {\n          children: \"mileage\"\n        }), \" fields\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"look for the \", _jsx(_components.code, {\n          children: \"location_id\"\n        }), \" in the \", _jsx(_components.code, {\n          children: \"vehicles\"\n        }), \" table in the \", _jsx(_components.code, {\n          children: \"locations\"\n        }), \" table and send me the data in the corresponding \", _jsx(_components.code, {\n          children: \"name\"\n        }), \" field\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If we only ask for the data from the \", _jsx(_components.code, {\n        children: \"location\"\n      }), \" field of the \", _jsx(_components.code, {\n        children: \"vehicles\"\n      }), \" table, Supabase will only return the \", _jsx(_components.code, {\n        children: \"id\"\n      }), \":\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.strong, {\n        children: \"🚨 Warning 🚨\"\n      })\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsxs(_components.p, {\n        children: [\"If you have enabled Row-Level Security in Supabase, make sure you have also \", _jsx(_components.a, {\n          href: \"https://supabase.com/docs/learn/auth-deep-dive/auth-policies\",\n          children: \"added a Policy\"\n        }), \" that allows users to read the data in the table. Otherwise, WeWeb won't be able to get the data.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 3: Display Supabase Data in WeWeb\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Assuming you were able to fetch data from Supabase in a WeWeb Collection, you'll be able to bind the data from that Collection on your WeWeb pages.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In the example below, we chose to display the car model and mileage in the \", _jsx(_components.a, {\n        href: \"https://docs.weweb.io/add-elements/elements/data-grid.html\",\n        children: \"Data Grid element\"\n      }), \" that comes out-of-the-box in WeWeb:\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We chose this element because it includes a built-in inline editing mode we'll want to use later for our CRUD operations.\"\n    }), \"\\n\", _jsx(_components.h4, {\n      children: \"🔥 Pro Tip 🔥\"\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsxs(_components.p, {\n        children: [\"In WeWeb, you can \", _jsx(_components.a, {\n          href: \"https://docs.weweb.io/binding-filtering/display-data.html\",\n          children: \"bind arrays of data to any Container\"\n        }), \". Just bear in mind that the first child of the Container you bind the Collection to will be the repeated item. With that in mind, you might want the first child Element to be another Container with a number of items inside like a title, description, button or image.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 4: Update a record in Supabase\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Once you've added a Supabase Collection of data to WeWeb, you might want to allow users to manipulate the data in that Collection.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In order to do so, you'll need to create a Workflow in WeWeb.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In the example below, we are using the \\\"Update row\\\" Workflow that comes by default with WeWeb's Data Grid Element.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The trigger is \", _jsx(_components.code, {\n        children: \"On Row update\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Since we added the Supabase Data Source Plugin above, we have access to all the CRUD actions available in Supabase:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Select\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Insert\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Update\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Upsert\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Delete\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In this case, we choose the \\\"Update\\\" action:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Then, in our \\\"Update\\\" action, we select the \", _jsx(_components.code, {\n        children: \"vehicles\"\n      }), \" table and map the \", _jsx(_components.code, {\n        children: \"id\"\n      }), \" to the id of the Workflow Event:\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Finally, we tell WeWeb we want to update the \", _jsx(_components.code, {\n        children: \"mileage\"\n      }), \" field in our Supabase table, and send the value in the \", _jsx(_components.code, {\n        children: \"mileage\"\n      }), \" column of our Data Grid:\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"And that's it!\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"If you switch to Preview mode, you will be update your Supabase table from your WeWeb Data Grid:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/CleanShot-2022-10-13-at-17.35.20.gif\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.strong, {\n        children: \"🔥 Pro Tip 🔥\"\n      })\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsx(_components.p, {\n        children: \"By default, the fields in the Data Grid Element are Text fields but you can change the input type to Number if you need to send numerical data to your database:\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Restrict who can modify a record in Supabase\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"By default, all the data in the tables that are in the \", _jsx(_components.code, {\n        children: \"public\"\n      }), \" schema of your Supabase project can be read, updated, or deleted.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Supabase allows you to \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/learn/auth-deep-dive/auth-row-level-security\",\n        children: \"enable Row-Level Security\"\n      }), \" for each of your tables:\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"If you want to restrict certain actions to specific users or roles, you'll need to:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"add Supabase authentication to your WeWeb project, and\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://supabase.com/docs/learn/auth-deep-dive/auth-policies\",\n          children: \"write SQL policies in Supabase\"\n        }), \".\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We provide a number of policy templates to get you started:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In the example below, we say that users can:\"\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"update a record\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"in the \\\"locations\\\" table of the \\\"public\\\" schema\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"if they are authenticated\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.strong, {\n        children: \"🔥 Pro Tip 🔥\"\n      })\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsx(_components.p, {\n        children: \"Once you enable RLS on a Supabase table, you won't be able to access the data in a WeWeb Collection unless you've added a policy.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/CleanShot-2022-10-13-at-16.28.51.gif\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 4: Add User Authentication\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Once you are able to display Supabase data in WeWeb, you might want to restrict access to certain users or display specific data based on a user's role.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In order to do that, you'll need to add WeWeb's Supabase Auth Plugin.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Add Supabase Auth Plugin in WeWeb\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Supabase comes with an in-built authentication system which you can use in WeWeb.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To add the Supabase Auth Plugin in WeWeb, go to \", _jsx(_components.code, {\n        children: \"Plugins\"\n      }), \" \u003e \", _jsx(_components.code, {\n        children: \"Authentication\"\n      }), \":\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Assuming you have already provided your Supabase project URL and public API key when setting up the Supabase Data source, the only thing left to do will be to add your private API key:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In Supabase, you can find your private API key in \", _jsx(_components.code, {\n        children: \"Settings\"\n      }), \" \u003e \", _jsx(_components.code, {\n        children: \"API\"\n      }), \":\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.strong, {\n        children: \"🚨 Warning 🚨\"\n      })\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsx(_components.p, {\n        children: \"As the name suggests, you'll want to keep this API key secret! Assuming you copy it properly in the \\\"Private API key\\\" field of the Supabase Auth Plugin and don't use it anywhere else in your Weweb project, Weweb will never make it public.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You will then be invited to choose a page to redirect \", _jsx(_components.em, {\n        children: \"unauthenticated\"\n      }), \" users, i.e. users who are NOT signed-in:\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.strong, {\n        children: \"🚨 Warning 🚨\"\n      })\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsxs(_components.p, {\n        children: [\"When you setup your Login Workflow, make sure you don't redirect unauthenticated users to a page that is only accessible to authenticated users. Otherwise, you'll be creating an \", _jsx(_components.strong, {\n          children: \"infinite loop\"\n        }), \" and your app will crash.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Create User Sign Up and Sign In Workflows\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In the \", _jsx(_components.code, {\n        children: \"Add\"\n      }), \" \u003e \", _jsx(_components.code, {\n        children: \"UI kit\"\n      }), \" menu of WeWeb, you can find ready-made Sign in and Sign up Forms:\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Once you've added a Form to the Canvas, you'll be able to style it whichever way you want.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In the example below, we added an image with the logo of our project to a Sign up Form and changed the background color of the \", _jsx(_components.code, {\n        children: \"Create Form\"\n      }), \" Container:\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To allow users to sign up, you'll need to create a Sign up Workflow on the Form Container:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.strong, {\n        children: \"🔥 Pro Tip 🔥\"\n      })\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsx(_components.p, {\n        children: \"It's important that the Workflow is on the Form Container and not the Sign up Button because we want to validate the fields of the Form when users submit it.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In the Workflow, you will choose the \", _jsx(_components.code, {\n        children: \"On submit\"\n      }), \" trigger and add the Supabase \", _jsx(_components.code, {\n        children: \"Sign up\"\n      }), \" Action:\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/CleanShot-2022-08-19-at-11.58.23.gif\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Then, you'll want to map the email, password, and metadata information in the Form to the email, password, and metadata in Supabase before choosing what page the new user should be redirected to:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/CleanShot-2022-08-19-at-12.02.32.gif\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In the example above, we made sure to add the user's name as an item in that user's metadata.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In Supabase, you can find the user's metadata in JSON format in a dedicated field of the \", _jsx(_components.code, {\n        children: \"users\"\n      }), \" table, named \", _jsx(_components.code, {\n        children: \"raw_user_meta_data\"\n      }), \":\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The same logic will apply to any Supabase Action you want to trigger.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Adding User Roles \u0026 Permissions\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Now let's say we want to gate content and set different permissions based on a user's role.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Adding Roles in Supabase\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In Supabase, we'll need to create a \", _jsx(_components.code, {\n        children: \"roles\"\n      }), \" table with a list of roles and a join table that links the \", _jsx(_components.code, {\n        children: \"roles\"\n      }), \" table with our \", _jsx(_components.code, {\n        children: \"users\"\n      }), \" table.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"First, let's create a \", _jsx(_components.code, {\n        children: \"roles\"\n      }), \" table with three roles and make sure that each role had a UUID and a \", _jsx(_components.code, {\n        children: \"name\"\n      }), \":\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.strong, {\n        children: \"🚨 Warning 🚨\"\n      })\n    }), \"\\n\", _jsxs(_components.blockquote, {\n      children: [\"\\n\", _jsxs(_components.p, {\n        children: [\"In order for the integration to work with the Users tab in WeWeb, it is crucial that the role title is a text field named \", _jsx(_components.code, {\n          children: \"name\"\n        }), \".\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Joining Roles and Users in Supabase\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Second, let's create a \", _jsx(_components.code, {\n        children: \"userRoles\"\n      }), \" join table:\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In the join table above, you can see we have an \", _jsx(_components.code, {\n        children: \"id\"\n      }), \" field that is uniquely identifiable thanks to a UUID.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"This unique \", _jsx(_components.code, {\n        children: \"id\"\n      }), \" is linked to a \", _jsx(_components.code, {\n        children: \"userId\"\n      }), \", which is also a UUID, more specifically, it is the UUID we find in the \", _jsx(_components.code, {\n        children: \"id\"\n      }), \" field of the \", _jsx(_components.code, {\n        children: \"users\"\n      }), \" table in the \", _jsx(_components.code, {\n        children: \"auth\"\n      }), \" schema:\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Each row in our \", _jsx(_components.code, {\n        children: \"userRoles\"\n      }), \" table is also linked to a \", _jsx(_components.code, {\n        children: \"roleId\"\n      }), \" which is the UUID we find in the \", _jsx(_components.code, {\n        children: \"id\"\n      }), \" field of the \", _jsx(_components.code, {\n        children: \"roles\"\n      }), \" table in the \", _jsx(_components.code, {\n        children: \"public\"\n      }), \" schema:\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/CleanShot-2022-08-19-at-18.52.57.gif\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Linking Users in WeWeb to Roles and Users in Supabase\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Once we've added our list of roles in Supabase and created an empty join table to link our roles with users, it's time to go to WeWeb.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In \", _jsx(_components.code, {\n        children: \"Plugins\"\n      }), \" \u003e \", _jsx(_components.code, {\n        children: \"Supabase Auth\"\n      }), \" \u003e \", _jsx(_components.code, {\n        children: \"3. Roles table\"\n      }), \", we'll click \", _jsx(_components.code, {\n        children: \"refresh\"\n      }), \" and select the relevant Supabase tables we just created:\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Once you've told WeWeb where to find the \", _jsx(_components.code, {\n        children: \"roles\"\n      }), \" and the join table in Supabase, you'll be able to easily view and maintain user roles in the \", _jsx(_components.code, {\n        children: \"Users\"\n      }), \" tab in WeWeb:\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/CleanShot-2022-08-19-at-19.03.13.gif\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"When you make a change to a User in WeWeb, it will automatically be updated in Supabase.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Users vs Profiles\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"So far, we've showed you how to work with the default \", _jsx(_components.code, {\n        children: \"users\"\n      }), \" table that Supabase generates in the \", _jsx(_components.code, {\n        children: \"auth\"\n      }), \" schema when you create a new project.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Note that, for security purposes, the information in that \", _jsx(_components.code, {\n        children: \"users\"\n      }), \" table is not exposed on the auto-generated API.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"How does this affect your project in WeWeb?\"\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Let users update their information\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Let's say you want to let authenticated users update their information, then you don't need to set up anything else in Supabase.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You could simply create a user profile page in WeWeb and display their information when they sign in, based on the data you have in the \", _jsx(_components.code, {\n        children: \"user\"\n      }), \" Variable:\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/weweb/documentation/<EMAIL>\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Display other users' information\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In some use cases, you might want to display \", _jsx(_components.em, {\n        children: \"other\"\n      }), \" users' information.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"For example, if you're building an HR portal in WeWeb, you might want HR employees to have access to a list of applicants and their user profiles.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You wouldn't be able to do that with the \", _jsx(_components.code, {\n        children: \"users\"\n      }), \" table in the \", _jsx(_components.code, {\n        children: \"auth\"\n      }), \" schema because each user's information is only available to them.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"For such a use case, we recommend creating a \", _jsx(_components.code, {\n        children: \"profiles\"\n      }), \" table in the \", _jsx(_components.code, {\n        children: \"public\"\n      }), \" schema to store user data that you want to access via the API.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In WeWeb, you would then be able to create a Collection to get data from the \", _jsx(_components.code, {\n        children: \"profiles\"\n      }), \" table.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Learn more about \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/auth/managing-user-data\",\n        children: \"managing user data in Supabase\"\n      }), \".\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\n","frontmatter":{},"scope":{}}},"__N_SSG":true},"page":"/partners/integrations/[slug]","query":{"slug":"weweb"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"isExperimentalCompile":false,"gsp":true,"scriptLoader":[]}</script></body></html>