<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../../rss.xml" data-next-head=""/><link rel="manifest" href="../../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:type" content="website" data-next-head=""/><meta property="og:image:alt" content="Supabase Og Image" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Clerk | Works With Supabase</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Authentication for Next.js, React, and the Modern Web." data-next-head=""/><meta property="og:title" content="Clerk | Works With Supabase" data-next-head=""/><meta property="og:description" content="Authentication for Next.js, React, and the Modern Web." data-next-head=""/><meta property="og:url" content="https://supabase.com/partners/integrations/clerk" data-next-head=""/><meta property="og:image" content="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/clerk/clerk-1.png" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2263-07e8ebb96cc2b014.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8658-639e1bb66549b52b.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/partners/integrations/%5Bslug%5D-cff8a94339d02c6e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqela6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R1eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R1eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R2eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R2eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R3eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R3eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav></div><main class="relative min-h-screen"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20"><div class="col-span-12 mx-auto mb-2 max-w-5xl space-y-10 lg:col-span-2"><a class="text-foreground hover:text-foreground-lighter flex cursor-pointer items-center transition-colors" href="../integrations.html"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left"><path d="m15 18-6-6 6-6"></path></svg>Back</a><div class="flex items-center space-x-4"><img alt="Clerk" loading="lazy" width="56" height="56" decoding="async" data-nimg="1" class="bg-surface-200 flex-shrink-f0 h-14 w-14 rounded-full" style="color:transparent" src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/clerk/clerk-icon-new.svg"/><h1 class="h1" style="margin-bottom:0">Clerk</h1></div><div class="bg-gradient-to-t from-background-alternative to-background border-b p-6 [&amp;_.swiper-container]:overflow-visible" style="margin-left:calc(50% - 50vw);margin-right:calc(50% - 50vw)"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0 !px-3 lg:!px-12 xl:!p-0 mx-auto max-w-5xl"><div class="swiper"><div class="swiper-wrapper"><div class="swiper-slide"><div class="relative block overflow-hidden rounded-md"><img alt="Clerk" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto;background-size:cover;background-position:50% 50%;background-repeat:no-repeat;background-image:url(&quot;data:image/svg+xml;charset=utf-8,%3Csvg xmlns=&#x27;http://www.w3.org/2000/svg&#x27; viewBox=&#x27;0 0 1460 960&#x27;%3E%3Cfilter id=&#x27;b&#x27; color-interpolation-filters=&#x27;sRGB&#x27;%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3CfeColorMatrix values=&#x27;1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1&#x27; result=&#x27;s&#x27;/%3E%3CfeFlood x=&#x27;0&#x27; y=&#x27;0&#x27; width=&#x27;100%25&#x27; height=&#x27;100%25&#x27;/%3E%3CfeComposite operator=&#x27;out&#x27; in=&#x27;s&#x27;/%3E%3CfeComposite in2=&#x27;SourceGraphic&#x27;/%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3C/filter%3E%3Cimage width=&#x27;100%25&#x27; height=&#x27;100%25&#x27; x=&#x27;0&#x27; y=&#x27;0&#x27; preserveAspectRatio=&#x27;none&#x27; style=&#x27;filter: url(%23b);&#x27; href=&#x27;/images/blur.png&#x27;/%3E%3C/svg%3E&quot;)" sizes="100vw" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-1.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-1.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-1.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-1.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-1.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-1.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-1.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-1.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-1.png&amp;w=3840&amp;q=75"/></div></div><div class="swiper-slide"><div class="relative block overflow-hidden rounded-md"><img alt="Clerk" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto;background-size:cover;background-position:50% 50%;background-repeat:no-repeat;background-image:url(&quot;data:image/svg+xml;charset=utf-8,%3Csvg xmlns=&#x27;http://www.w3.org/2000/svg&#x27; viewBox=&#x27;0 0 1460 960&#x27;%3E%3Cfilter id=&#x27;b&#x27; color-interpolation-filters=&#x27;sRGB&#x27;%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3CfeColorMatrix values=&#x27;1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1&#x27; result=&#x27;s&#x27;/%3E%3CfeFlood x=&#x27;0&#x27; y=&#x27;0&#x27; width=&#x27;100%25&#x27; height=&#x27;100%25&#x27;/%3E%3CfeComposite operator=&#x27;out&#x27; in=&#x27;s&#x27;/%3E%3CfeComposite in2=&#x27;SourceGraphic&#x27;/%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3C/filter%3E%3Cimage width=&#x27;100%25&#x27; height=&#x27;100%25&#x27; x=&#x27;0&#x27; y=&#x27;0&#x27; preserveAspectRatio=&#x27;none&#x27; style=&#x27;filter: url(%23b);&#x27; href=&#x27;/images/blur.png&#x27;/%3E%3C/svg%3E&quot;)" sizes="100vw" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-2.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-2.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-2.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-2.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-2.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-2.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-2.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-2.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-2.png&amp;w=3840&amp;q=75"/></div></div><div class="swiper-slide"><div class="relative block overflow-hidden rounded-md"><img alt="Clerk" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto;background-size:cover;background-position:50% 50%;background-repeat:no-repeat;background-image:url(&quot;data:image/svg+xml;charset=utf-8,%3Csvg xmlns=&#x27;http://www.w3.org/2000/svg&#x27; viewBox=&#x27;0 0 1460 960&#x27;%3E%3Cfilter id=&#x27;b&#x27; color-interpolation-filters=&#x27;sRGB&#x27;%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3CfeColorMatrix values=&#x27;1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1&#x27; result=&#x27;s&#x27;/%3E%3CfeFlood x=&#x27;0&#x27; y=&#x27;0&#x27; width=&#x27;100%25&#x27; height=&#x27;100%25&#x27;/%3E%3CfeComposite operator=&#x27;out&#x27; in=&#x27;s&#x27;/%3E%3CfeComposite in2=&#x27;SourceGraphic&#x27;/%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3C/filter%3E%3Cimage width=&#x27;100%25&#x27; height=&#x27;100%25&#x27; x=&#x27;0&#x27; y=&#x27;0&#x27; preserveAspectRatio=&#x27;none&#x27; style=&#x27;filter: url(%23b);&#x27; href=&#x27;/images/blur.png&#x27;/%3E%3C/svg%3E&quot;)" sizes="100vw" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-3.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-3.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-3.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-3.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-3.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-3.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-3.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-3.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-3.png&amp;w=3840&amp;q=75"/></div></div><div class="swiper-slide"><div class="relative block overflow-hidden rounded-md"><img alt="Clerk" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto;background-size:cover;background-position:50% 50%;background-repeat:no-repeat;background-image:url(&quot;data:image/svg+xml;charset=utf-8,%3Csvg xmlns=&#x27;http://www.w3.org/2000/svg&#x27; viewBox=&#x27;0 0 1460 960&#x27;%3E%3Cfilter id=&#x27;b&#x27; color-interpolation-filters=&#x27;sRGB&#x27;%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3CfeColorMatrix values=&#x27;1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1&#x27; result=&#x27;s&#x27;/%3E%3CfeFlood x=&#x27;0&#x27; y=&#x27;0&#x27; width=&#x27;100%25&#x27; height=&#x27;100%25&#x27;/%3E%3CfeComposite operator=&#x27;out&#x27; in=&#x27;s&#x27;/%3E%3CfeComposite in2=&#x27;SourceGraphic&#x27;/%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3C/filter%3E%3Cimage width=&#x27;100%25&#x27; height=&#x27;100%25&#x27; x=&#x27;0&#x27; y=&#x27;0&#x27; preserveAspectRatio=&#x27;none&#x27; style=&#x27;filter: url(%23b);&#x27; href=&#x27;/images/blur.png&#x27;/%3E%3C/svg%3E&quot;)" sizes="100vw" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-4.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-4.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-4.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-4.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-4.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-4.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-4.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-4.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-4.png&amp;w=3840&amp;q=75"/></div></div><div class="swiper-slide"><div class="relative block overflow-hidden rounded-md"><img alt="Clerk" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto;background-size:cover;background-position:50% 50%;background-repeat:no-repeat;background-image:url(&quot;data:image/svg+xml;charset=utf-8,%3Csvg xmlns=&#x27;http://www.w3.org/2000/svg&#x27; viewBox=&#x27;0 0 1460 960&#x27;%3E%3Cfilter id=&#x27;b&#x27; color-interpolation-filters=&#x27;sRGB&#x27;%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3CfeColorMatrix values=&#x27;1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1&#x27; result=&#x27;s&#x27;/%3E%3CfeFlood x=&#x27;0&#x27; y=&#x27;0&#x27; width=&#x27;100%25&#x27; height=&#x27;100%25&#x27;/%3E%3CfeComposite operator=&#x27;out&#x27; in=&#x27;s&#x27;/%3E%3CfeComposite in2=&#x27;SourceGraphic&#x27;/%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3C/filter%3E%3Cimage width=&#x27;100%25&#x27; height=&#x27;100%25&#x27; x=&#x27;0&#x27; y=&#x27;0&#x27; preserveAspectRatio=&#x27;none&#x27; style=&#x27;filter: url(%23b);&#x27; href=&#x27;/images/blur.png&#x27;/%3E%3C/svg%3E&quot;)" sizes="100vw" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-5.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-5.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-5.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-5.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-5.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-5.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-5.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-5.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fclerk%2Fclerk-5.png&amp;w=3840&amp;q=75"/></div></div></div></div></div></div><div class="grid gap-y-12 lg:grid-cols-8 lg:space-x-12"><div class="lg:col-span-5 overflow-hidden"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Overview</h2><div class="prose"><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<h2>Deprecation</h2>
<p>This integration is being deprecated on 1 April 2025, and replaced with the new Clerk third-party integration. See the <a href="https://supabase.com/docs/guides/auth/third-party/clerk">Supabase with Clerk docs</a> for more information.</p>
<p>You can still use this integration unofficially since the primitives on both the Supabase and Clerk sides remain functional. Limited support will be provided.</p>
<h2>Overview</h2>
<p>Clerk authenticates users, manages session tokens, and provides user management functionality that can be used in combination with the authorization logic available in Supabase through PostgreSQL Row Level Security (RLS) policies.</p>
<h2>Documentation</h2>
<p>This guide explains how to connect your Supabase database with <a href="https://clerk.com/">Clerk</a>.</p>
<p>Clerk is a user management platform that provides beautifully designed, drop-in UI components to quickly add authentication capabilities to your application. Clerk supports numerous sign-in strategies such as social providers, email links, and passkeys, as well as a suite of B2B SaaS tools and APIs to build your own authentication flows.</p>
<p>The Clerk integration uses the authorization logic available in Supabase through PostgreSQL Row Level Security (RLS) policies.</p>
<p>This guide assumes you have a Supabase account and database project already set up.</p>
<p>If you don&#x27;t have a Clerk account, you can <a href="https://dashboard.clerk.com/sign-up">create an account for free</a>.</p>
<h2>How the integration works</h2>
<p>RLS works by validating database queries according to the restrictions defined in the RLS policies applied to the table. This guide will show you how to create RLS policies that restrict access to data based on the user&#x27;s Clerk ID. This way, users can only access data that belongs to them.</p>
<p>To set this up, you will:</p>
<ul>
<li>Create a function in Supabase to parse the Clerk user ID from the authentication token.</li>
<li>Create a <code>user_id</code> column that defaults to the Clerk user&#x27;s ID when new records are created.</li>
<li>Create policies to restrict what data can be read and inserted.</li>
<li>Use the Clerk Supabase integration helper in your code to authenticate with Supabase and execute queries.</li>
</ul>
<h2>1: Create a function to check the incoming user ID</h2>
<p>Create a function named <code>requesting_user_id()</code> that will parse the Clerk user ID from the authentication token. This function will be used to set the default value of <code>user_id</code> in a table and in the RLS policies to ensure the user can only access their data.</p>
<ol>
<li>
<p>In the sidebar of your <a href="https://supabase.com/dashboard/projects">Supabase dashboard</a>, navigate to <strong>Database</strong> &gt; <strong>Functions</strong>.</p>
</li>
<li>
<p>Select <strong>Create a new function</strong>.</p>
</li>
<li>
<p>In the <strong>Add a new function</strong> sheet, make the following changes:</p>
<ul>
<li>Set <strong>Name of function</strong> to <code>requesting_user_id</code>.</li>
<li>Set <strong>Return type</strong> to <code>text</code>.</li>
<li>Toggle <strong>Show advanced settings</strong> on.</li>
<li>Set <strong>Language</strong> to <code>sql</code>.</li>
<li>Populate the <strong>Definition</strong> with the following sql:</li>
</ul>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>SELECT NULLIF(</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    current_setting(&#x27;request.jwt.claims&#x27;, true)::json-&gt;&gt;&#x27;sub&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    &#x27;&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>)::text;</span></div></div><br/></code></div></div>
<ul>
<li>Select <strong>Confirm</strong>.</li>
</ul>
</li>
</ol>
<h2>2: Create a <code>user_id</code> column</h2>
<div role="alert" class="relative w-full text-sm rounded-lg p-4 [&amp;&gt;svg~*]:pl-10 [&amp;&gt;svg+div]:translate-y-[-3px] [&amp;&gt;svg]:absolute [&amp;&gt;svg]:left-4 [&amp;&gt;svg]:top-4 [&amp;&gt;svg]:w-[23px] [&amp;&gt;svg]:h-[23px] [&amp;&gt;svg]:p-1 [&amp;&gt;svg]:flex [&amp;&gt;svg]:rounded text-foreground bg-alternative border [&amp;&gt;svg]:text-background [&amp;&gt;svg]:bg-foreground mb-2"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 21 20" class="w-6 h-6" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M0.625 9.8252C0.625 4.44043 4.99023 0.0751953 10.375 0.0751953C15.7598 0.0751953 20.125 4.44043 20.125 9.8252C20.125 15.21 15.7598 19.5752 10.375 19.5752C4.99023 19.5752 0.625 15.21 0.625 9.8252ZM9.3584 4.38135C9.45117 4.28857 9.55518 4.20996 9.66699 4.14648C9.88086 4.02539 10.1245 3.96045 10.375 3.96045C10.5845 3.96045 10.7896 4.00586 10.9766 4.09229C11.1294 4.1626 11.2705 4.26025 11.3916 4.38135C11.6611 4.65088 11.8125 5.0166 11.8125 5.39795C11.8125 5.5249 11.7959 5.6499 11.7637 5.77002C11.6987 6.01172 11.5718 6.23438 11.3916 6.41455C11.1221 6.68408 10.7563 6.83545 10.375 6.83545C9.99365 6.83545 9.62793 6.68408 9.3584 6.41455C9.08887 6.14502 8.9375 5.7793 8.9375 5.39795C8.9375 5.29492 8.94873 5.19287 8.97021 5.09375C9.02783 4.82568 9.16162 4.57812 9.3584 4.38135ZM10.375 15.6899C10.0933 15.6899 9.82275 15.5781 9.62354 15.3789C9.42432 15.1797 9.3125 14.9092 9.3125 14.6274V9.31494C9.3125 9.0332 9.42432 8.7627 9.62354 8.56348C9.82275 8.36426 10.0933 8.25244 10.375 8.25244C10.6567 8.25244 10.9272 8.36426 11.1265 8.56348C11.3257 8.7627 11.4375 9.0332 11.4375 9.31494V14.6274C11.4375 14.7944 11.3979 14.9575 11.3242 15.104C11.2739 15.2046 11.2075 15.2979 11.1265 15.3789C10.9272 15.5781 10.6567 15.6899 10.375 15.6899Z"></path></svg><div class="text mt [&amp;_p]:mb-1.5 [&amp;_p]:mt-0 mt-0.5 [&amp;_p:last-child]:mb-0"><p>💡 The following steps will need to be performed on any tables you wish to secure.</p></div></div>
<p>Next, you’ll create a <code>user_id</code> column in the table you wish to secure. This column will be used in the RLS policies to only return or modify records scoped to the user&#x27;s account and it will use the <code>requesting_user_id()</code> function you just created as its default value.</p>
<ol>
<li>Navigate to the sidebar on the left and select <strong>Table Editor.</strong></li>
<li>Select the table you wish to secure.</li>
<li>In the table, select the <strong>+</strong> column to add a new column.</li>
<li>Set the <strong>Name</strong> to <strong>user_id</strong>.</li>
<li>Set <strong>Type</strong> to <strong>text</strong>.</li>
<li>Set <strong>Default Value</strong> to <code>requesting_user_id()</code>.</li>
<li>Select <strong>Save</strong> to create the column.</li>
</ol>
<h2>3: Enable RLS on your table and create the policies</h2>
<p>To enable RLS on your table:</p>
<ol>
<li>In the top bar above the table, select <strong>RLS disabled</strong> and then <strong>Enable RLS for this table</strong>.</li>
<li>In the modal that appears, select <strong>Enable RLS</strong>.</li>
<li>Select the <strong>Add RLS policy</strong> button (which has replaced <strong>RLS disabled).</strong></li>
</ol>
<p>Create two policies: one to enforce that the data returned has a <code>user_id</code> value that matches the requestor, and another to automatically insert records with the ID of the requestor.</p>
<ol>
<li>Select <strong>Create policy</strong> to create the <code>SELECT</code> policy:<!-- -->
<ul>
<li>
<p>Provide a name for the policy.</p>
</li>
<li>
<p>For <strong>Policy Command</strong>, select <strong>SELECT</strong>.</p>
</li>
<li>
<p>For <strong>Target roles</strong>, select <strong>authenticated</strong>.</p>
</li>
<li>
<p>Replace the &quot;-- Provide a SQL expression for the using statement&quot; with the following:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>requesting_user_id() = user_id</span></div></div><br/></code></div></div>
</li>
<li>
<p>Select <strong>Save policy</strong>.</p>
</li>
</ul>
</li>
<li>Select <strong>Create policy</strong> to create the <code>INSERT</code> policy:<!-- -->
<ul>
<li>
<p>Provide a name for the policy.</p>
</li>
<li>
<p>For <strong>Policy Command</strong>, select <strong>INSERT</strong>.</p>
</li>
<li>
<p>For <strong>Target roles</strong>, select <strong>authenticated</strong>.</p>
</li>
<li>
<p>Replace the &quot;-- Provide a SQL expression for the with check statement&quot; with the following:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>requesting_user_id() = user_id</span></div></div><br/></code></div></div>
</li>
<li>
<p>Select <strong>Save policy</strong>.</p>
</li>
</ul>
</li>
</ol>
<h2>4: Get your Supabase JWT secret key</h2>
<p>To give users access to your data, Supabase&#x27;s API requires an authentication token. Your Clerk project can generate these authentication tokens, but it needs your Supabase project&#x27;s JWT secret key first.</p>
<p>To find the JWT secret key:</p>
<ol>
<li>In the sidebar of your <a href="https://supabase.com/dashboard/projects">Supabase dashboard</a>, navigate to <strong>Project Settings &gt; API</strong>.</li>
<li>Under the <strong>JWT Settings</strong> section, save the value in the <strong>JWT Secret</strong> field somewhere secure. This value will be used in the next step.</li>
</ol>
<h2>5: Create a Supabase JWT template</h2>
<p>Clerk&#x27;s JWT templates allow you to generate a new valid Supabase authentication token for each signed-in user. These tokens allow authenticated users to access your data with Supabase&#x27;s API.</p>
<p>To create a Clerk JWT template for Supabase:</p>
<ol>
<li>Navigate to the <a href="https://dashboard.clerk.com/last-active?path=jwt-templates">Clerk Dashboard</a>.</li>
<li>In the navigation sidebar, select <strong>JWT Templates</strong>.</li>
<li>Select the <strong>New template</strong> button, then select <strong>Supabase</strong> from the list of options.</li>
<li>Configure your template:<!-- -->
<ul>
<li>The value of the <strong>Name</strong> field will be required when using the template in your code. For this tutorial, name it <code>supabase</code>.</li>
<li><strong>Signing algorithm</strong> will be <code>HS256</code> by default. This algorithm is required to use JWTs with Supabase. <a href="https://supabase.com/docs/guides/resources/glossary#jwt-signing-secret">Learn more in the Supabase docs</a>.</li>
<li>Under <strong>Signing key</strong>, add the value of your Supabase <strong>JWT secret key</strong> from the previous step.</li>
<li>You can leave all other fields at their default settings or customize them to your needs. See the <a href="https://clerk.com/docs/backend-requests/making/jwt-templates#creating-a-template">Clerk JWT template guide</a> to learn more about these settings.</li>
<li>Select <strong>Save</strong> from the notification bubble to complete setup.</li>
</ul>
</li>
</ol>
<h2>6: Configure your application</h2>
<p>The next step is to configure your client. Supabase provides an official <a href="https://github.com/supabase/supabase-js">JavaScript/TypeScript client library</a> and there are <a href="https://supabase.com/docs/reference/javascript/installing">libraries in other languages</a> built by the community.</p>
<p>This guide will use a Next.js project with the JS client as an example, but the mechanism of setting the authentication token should be similar to other libraries and frameworks.</p>
<h3>Set up Clerk</h3>
<p>To configure Clerk in your Next.js application, follow the <a href="https://clerk.com/docs/quickstarts/nextjs">Next.js Quickstart</a> available in the <a href="https://clerk.com/docs">Clerk docs</a>. The linked guide will walk you through the basics of configuring Clerk by adding sign-up and sign-in functionality to your application.</p>
<h3>Configure the Supabase client</h3>
<p>Next, the Supabase client used to query and modify data in your Supabase database needs to be modified to use the Clerk token as part of the request headers. This can be done by customizing the <code>fetch</code> that is used by Supabase like so:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>import { useSession, useUser } from &#x27;@clerk/nextjs&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>import { createClient } from &#x27;@supabase/supabase-js&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>export default function Home() {</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	// The `useSession()` hook will be used to get the Clerk `session` object</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	const { session } = useSession()</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	// Create a custom supabase client that injects the Clerk Supabase token into the request headers</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	function createClerkSupabaseClient() {</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	  return createClient(</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	    process.env.NEXT_PUBLIC_SUPABASE_URL!,</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	    process.env.NEXT_PUBLIC_SUPABASE_KEY!,</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	    {</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	      global: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	        // Get the custom Supabase token from Clerk</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	        fetch: async (url, options = {}) =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>		        // The Clerk `session` object has the getToken() method      </span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	          const clerkToken = await session?.getToken({</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>		          // Pass the name of the JWT template you created in the Clerk Dashboard</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>		          // For this tutorial, you named it &#x27;supabase&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	            template: &#x27;supabase&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	          })</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	          </span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	          // Insert the Clerk Supabase token into the headers</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>		        const headers = new Headers(options?.headers)</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	          headers.set(&#x27;Authorization&#x27;, `Bearer ${clerkToken}`)</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	          </span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	          // Call the default fetch</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	          return fetch(url, {</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	            ...options,</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	            headers,</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	          })</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	        },</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	      },</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	    },</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	  )</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	}</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>	//... The rest of the code is removed for brevity</span></div></div><div><span class="ch-code-line-number">_<!-- -->40</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<p>Then the client can be created and used throughout the application:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>const client = createClerkSupabaseClient()</span></div></div><br/></code></div></div>
<p>If you have previously followed the Supabase Next.js guide, you’d replace any use of the <code>createClient</code> function with the one above.</p>
<h3>Example</h3>
<p>The following example demonstrates how this technique is used in a to-do application that queries data from and inserts data into a <code>tasks</code> table, which will be secured with the RLS policies created in previous steps:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>&#x27;use client&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>import { useEffect, useState } from &#x27;react&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>import { useSession, useUser } from &#x27;@clerk/nextjs&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>import { createClient } from &#x27;@supabase/supabase-js&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>export default function Home() {</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>  const [tasks, setTasks] = useState&lt;any[]&gt;([])</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>  const [loading, setLoading] = useState(true)</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>  const [name, setName] = useState(&#x27;&#x27;)</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>  // The `useUser()` hook will be used to ensure that Clerk has loaded data about the logged in user</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>  const { user } = useUser()</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>  // The `useSession()` hook will be used to get the Clerk `session` object</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>  const { session } = useSession()</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>  // Create a custom supabase client that injects the Clerk Supabase token into the request headers</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>  function createClerkSupabaseClient() {</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>    return createClient(</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>      process.env.NEXT_PUBLIC_SUPABASE_URL!,</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>      process.env.NEXT_PUBLIC_SUPABASE_KEY!,</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>      {</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>        global: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>          // Get the custom Supabase token from Clerk</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>          fetch: async (url, options = {}) =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>            const clerkToken = await session?.getToken({</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>              template: &#x27;supabase&#x27;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>            })</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>            // Insert the Clerk Supabase token into the headers</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>            const headers = new Headers(options?.headers)</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>            headers.set(&#x27;Authorization&#x27;, `Bearer ${clerkToken}`)</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>            // Call the default fetch</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>            return fetch(url, {</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>              ...options,</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>              headers,</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>            })</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>          },</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>        },</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>      },</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>    )</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>  // Create a `client` object for accessing Supabase data using the Clerk token</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>  const client = createClerkSupabaseClient()</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>  // This `useEffect` will wait for the `user` object to be loaded before requesting</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>  // the tasks for the logged in user</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>  useEffect(() =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>    if (!user) return</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>    async function loadTasks() {</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>      setLoading(true)</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>      const { data, error } = await client.from(&#x27;tasks&#x27;).select()</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>      if (!error) setTasks(data)</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>      setLoading(false)</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>    }</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>    loadTasks()</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>  }, [user])</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>  async function createTask(e: React.FormEvent&lt;HTMLFormElement&gt;) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>    e.preventDefault()</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>    // Insert task into the &quot;tasks&quot; database</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>    await client.from(&#x27;tasks&#x27;).insert({</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>      name,</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>    })</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>    window.location.reload()</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>  return (</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>    &lt;div&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>      &lt;h1&gt;Tasks&lt;/h1&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>      {loading &amp;&amp; &lt;p&gt;Loading...&lt;/p&gt;}</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>      {!loading &amp;&amp; tasks.length &gt; 0 &amp;&amp; tasks.map((task: any) =&gt; &lt;p&gt;{task.name}&lt;/p&gt;)}</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>      {!loading &amp;&amp; tasks.length === 0 &amp;&amp; &lt;p&gt;No tasks found&lt;/p&gt;}</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>      &lt;form onSubmit={createTask}&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>        &lt;input</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>          autoFocus</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>          type=&quot;text&quot;</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>          name=&quot;name&quot;</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>          placeholder=&quot;Enter new task&quot;</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>          onChange={(e) =&gt; setName(e.target.value)}</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>          value={name}</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>        /&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>        &lt;button type=&quot;submit&quot;&gt;Add&lt;/button&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>      &lt;/form&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>    &lt;/div&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>  )</span></div></div><div><span class="ch-code-line-number">_<!-- -->93</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div></div></div><div class="lg:col-span-3"><div class="sticky top-20 flex flex-col gap-4"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Details</h2><button class="w-full w-full"><div class="video-container overflow-hidden rounded hover:cursor-pointer"><div class=" absolute inset-0 z-10 text-white flex flex-col gap-3 items-center justify-center bg-alternative before:content[&#x27;&#x27;] before:absolute before:inset-0 before:bg-black before:opacity-30 before:-z-10 hover:before:opacity-50 before:transition-opacity "><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play w-5 h-5"><polygon points="6 3 20 12 6 21 6 3"></polygon></svg><p class="text-sm">Watch an introductory video</p></div><img alt="Video guide preview" loading="lazy" decoding="async" data-nimg="fill" class="absolute inset-0 object-cover blur-sm scale-105" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=3840&amp;q=75"/></div></button><div class="text-foreground divide-y"><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Developer</span><span class="text-foreground">Clerk</span></div><div class="flex items-center justify-between py-2"><span class="text-lighter">Category</span><a class="text-brand hover:underline transition-colors" href="../integrations.html#auth">Auth</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Website</span><a href="https://clerk.com/" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors">clerk.com</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Documentation</span><a href="https://clerk.com/" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors"><span class="flex items-center space-x-1"><span>Learn</span><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></span></a></div></div><p class="text-foreground-light text-sm">Third-party integrations and docs are managed by Supabase partners.</p></div></div></div></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"partner":{"id":8,"slug":"clerk","type":"technology","category":"Auth","developer":"Clerk","title":"Clerk","description":"Authentication for Next.js, React, and the Modern Web.","logo":"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/clerk/clerk-icon-new.svg","images":["https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/clerk/clerk-1.png","https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/clerk/clerk-2.png","https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/clerk/clerk-3.png","https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/clerk/clerk-4.png","https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/clerk/clerk-5.png"],"overview":"## Deprecation\n\nThis integration is being deprecated on 1 April 2025, and replaced with the new Clerk third-party integration. See the [Supabase with Clerk docs](https://supabase.com/docs/guides/auth/third-party/clerk) for more information.\n\nYou can still use this integration unofficially since the primitives on both the Supabase and Clerk sides remain functional. Limited support will be provided.\n\n## Overview\n\nClerk authenticates users, manages session tokens, and provides user management functionality that can be used in combination with the authorization logic available in Supabase through PostgreSQL Row Level Security (RLS) policies.\n\n## Documentation\n\nThis guide explains how to connect your Supabase database with [Clerk](https://clerk.com/).\n\nClerk is a user management platform that provides beautifully designed, drop-in UI components to quickly add authentication capabilities to your application. Clerk supports numerous sign-in strategies such as social providers, email links, and passkeys, as well as a suite of B2B SaaS tools and APIs to build your own authentication flows.\n\nThe Clerk integration uses the authorization logic available in Supabase through PostgreSQL Row Level Security (RLS) policies.\n\nThis guide assumes you have a Supabase account and database project already set up.\n\nIf you don't have a Clerk account, you can [create an account for free](https://dashboard.clerk.com/sign-up).\n\n## How the integration works\n\nRLS works by validating database queries according to the restrictions defined in the RLS policies applied to the table. This guide will show you how to create RLS policies that restrict access to data based on the user's Clerk ID. This way, users can only access data that belongs to them. \n\nTo set this up, you will:\n\n- Create a function in Supabase to parse the Clerk user ID from the authentication token.\n- Create a `user_id` column that defaults to the Clerk user's ID when new records are created.\n- Create policies to restrict what data can be read and inserted.\n- Use the Clerk Supabase integration helper in your code to authenticate with Supabase and execute queries.\n\n## 1: Create a function to check the incoming user ID\n\nCreate a function named `requesting_user_id()` that will parse the Clerk user ID from the authentication token. This function will be used to set the default value of `user_id` in a table and in the RLS policies to ensure the user can only access their data.\n\n1. In the sidebar of your [Supabase dashboard](https://supabase.com/dashboard/projects), navigate to **Database** \u003e **Functions**.\n2. Select **Create a new function**.\n3. In the **Add a new function** sheet, make the following changes:\n    - Set **Name of function** to `requesting_user_id`.\n    - Set **Return type** to `text`.\n    - Toggle **Show advanced settings** on.\n    - Set **Language** to `sql`.\n    - Populate the **Definition** with the following sql:\n    \n    ```jsx\n    SELECT NULLIF(\n        current_setting('request.jwt.claims', true)::json-\u003e\u003e'sub',\n        ''\n    )::text;\n    ```\n    \n    - Select **Confirm**.\n\n## 2: Create a `user_id` column\n\n\u003cAdmonition type=\"info\"\u003e\n\n💡 The following steps will need to be performed on any tables you wish to secure.\n\n\u003c/Admonition\u003e\n\nNext, you’ll create a `user_id` column in the table you wish to secure. This column will be used in the RLS policies to only return or modify records scoped to the user's account and it will use the `requesting_user_id()` function you just created as its default value. \n\n1. Navigate to the sidebar on the left and select **Table Editor.**\n2. Select the table you wish to secure. \n3. In the table, select the **+** column to add a new column.\n4. Set the **Name** to **user_id**.\n5. Set **Type** to **text**.\n6. Set **Default Value** to `requesting_user_id()`.\n7. Select **Save** to create the column.\n\n## 3: Enable RLS on your table and create the policies\n\nTo enable RLS on your table:\n\n1. In the top bar above the table, select **RLS disabled** and then **Enable RLS for this table**. \n2. In the modal that appears, select **Enable RLS**. \n3. Select the **Add RLS policy** button (which has replaced **RLS disabled).**\n\nCreate two policies: one to enforce that the data returned has a `user_id` value that matches the requestor, and another to automatically insert records with the ID of the requestor.\n\n1. Select **Create policy** to create the `SELECT` policy:\n    - Provide a name for the policy.\n    - For **Policy Command**, select **SELECT**.\n    - For **Target roles**, select **authenticated**.\n    - Replace the \"-- Provide a SQL expression for the using statement\" with the following:\n        \n        ```jsx\n        requesting_user_id() = user_id\n        ```\n        \n    - Select **Save policy**.\n2. Select **Create policy** to create the `INSERT` policy:\n    - Provide a name for the policy.\n    - For **Policy Command**, select **INSERT**.\n    - For **Target roles**, select **authenticated**.\n    - Replace the \"-- Provide a SQL expression for the with check statement\" with the following:\n        \n        ```jsx\n        requesting_user_id() = user_id\n        ```\n        \n    - Select **Save policy**.\n\n## 4: Get your Supabase JWT secret key\n\nTo give users access to your data, Supabase's API requires an authentication token. Your Clerk project can generate these authentication tokens, but it needs your Supabase project's JWT secret key first.\n\nTo find the JWT secret key:\n\n1. In the sidebar of your [Supabase dashboard](https://supabase.com/dashboard/projects), navigate to **Project Settings \u003e API**.\n2. Under the **JWT Settings** section, save the value in the **JWT Secret** field somewhere secure. This value will be used in the next step.\n\n## 5: Create a Supabase JWT template\n\nClerk's JWT templates allow you to generate a new valid Supabase authentication token for each signed-in user. These tokens allow authenticated users to access your data with Supabase's API.\n\nTo create a Clerk JWT template for Supabase:\n\n1. Navigate to the [Clerk Dashboard](https://dashboard.clerk.com/last-active?path=jwt-templates).\n2. In the navigation sidebar, select **JWT Templates**.\n3. Select the **New template** button, then select **Supabase** from the list of options.\n4. Configure your template:\n    - The value of the **Name** field will be required when using the template in your code. For this tutorial, name it `supabase`.\n    - **Signing algorithm** will be `HS256` by default. This algorithm is required to use JWTs with Supabase. [Learn more in the Supabase docs](https://supabase.com/docs/guides/resources/glossary#jwt-signing-secret).\n    - Under **Signing key**, add the value of your Supabase **JWT secret key** from the previous step.\n    - You can leave all other fields at their default settings or customize them to your needs. See the [Clerk JWT template guide](https://clerk.com/docs/backend-requests/making/jwt-templates#creating-a-template) to learn more about these settings.\n    - Select **Save** from the notification bubble to complete setup.\n\n## 6: Configure your application\n\nThe next step is to configure your client. Supabase provides an official [JavaScript/TypeScript client library](https://github.com/supabase/supabase-js) and there are [libraries in other languages](https://supabase.com/docs/reference/javascript/installing) built by the community.\n\nThis guide will use a Next.js project with the JS client as an example, but the mechanism of setting the authentication token should be similar to other libraries and frameworks.\n\n### Set up Clerk\n\nTo configure Clerk in your Next.js application, follow the [Next.js Quickstart](https://clerk.com/docs/quickstarts/nextjs) available in the [Clerk docs](https://clerk.com/docs). The linked guide will walk you through the basics of configuring Clerk by adding sign-up and sign-in functionality to your application.\n\n### Configure the Supabase client\n\nNext, the Supabase client used to query and modify data in your Supabase database needs to be modified to use the Clerk token as part of the request headers. This can be done by customizing the `fetch` that is used by Supabase like so:\n\n```tsx\nimport { useSession, useUser } from '@clerk/nextjs'\nimport { createClient } from '@supabase/supabase-js'\n\nexport default function Home() {\n\t// The `useSession()` hook will be used to get the Clerk `session` object\n\tconst { session } = useSession()\n\t\n\t// Create a custom supabase client that injects the Clerk Supabase token into the request headers\n\tfunction createClerkSupabaseClient() {\n\t  return createClient(\n\t    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n\t    process.env.NEXT_PUBLIC_SUPABASE_KEY!,\n\t    {\n\t      global: {\n\t        // Get the custom Supabase token from Clerk\n\t        fetch: async (url, options = {}) =\u003e {\n\t\t        // The Clerk `session` object has the getToken() method      \n\t          const clerkToken = await session?.getToken({\n\t\t          // Pass the name of the JWT template you created in the Clerk Dashboard\n\t\t          // For this tutorial, you named it 'supabase'\n\t            template: 'supabase',\n\t          })\n\t          \n\t          // Insert the Clerk Supabase token into the headers\n\t\t        const headers = new Headers(options?.headers)\n\t          headers.set('Authorization', `Bearer ${clerkToken}`)\n\t          \n\t          // Call the default fetch\n\t          return fetch(url, {\n\t            ...options,\n\t            headers,\n\t          })\n\t        },\n\t      },\n\t    },\n\t  )\n\t}\n\t\n\t//... The rest of the code is removed for brevity\n}\n```\n\nThen the client can be created and used throughout the application:\n\n```tsx\nconst client = createClerkSupabaseClient()\n```\n\nIf you have previously followed the Supabase Next.js guide, you’d replace any use of the `createClient` function with the one above.\n\n### Example\n\nThe following example demonstrates how this technique is used in a to-do application that queries data from and inserts data into a `tasks` table, which will be secured with the RLS policies created in previous steps:\n\n```tsx\n'use client'\nimport { useEffect, useState } from 'react'\nimport { useSession, useUser } from '@clerk/nextjs'\nimport { createClient } from '@supabase/supabase-js'\n\nexport default function Home() {\n  const [tasks, setTasks] = useState\u003cany[]\u003e([])\n  const [loading, setLoading] = useState(true)\n  const [name, setName] = useState('')\n  // The `useUser()` hook will be used to ensure that Clerk has loaded data about the logged in user\n  const { user } = useUser()\n  // The `useSession()` hook will be used to get the Clerk `session` object\n  const { session } = useSession()\n\n  // Create a custom supabase client that injects the Clerk Supabase token into the request headers\n  function createClerkSupabaseClient() {\n    return createClient(\n      process.env.NEXT_PUBLIC_SUPABASE_URL!,\n      process.env.NEXT_PUBLIC_SUPABASE_KEY!,\n      {\n        global: {\n          // Get the custom Supabase token from Clerk\n          fetch: async (url, options = {}) =\u003e {\n            const clerkToken = await session?.getToken({\n              template: 'supabase',\n            })\n\n            // Insert the Clerk Supabase token into the headers\n            const headers = new Headers(options?.headers)\n            headers.set('Authorization', `Bearer ${clerkToken}`)\n\n            // Call the default fetch\n            return fetch(url, {\n              ...options,\n              headers,\n            })\n          },\n        },\n      },\n    )\n  }\n\n  // Create a `client` object for accessing Supabase data using the Clerk token\n  const client = createClerkSupabaseClient()\n\n  // This `useEffect` will wait for the `user` object to be loaded before requesting\n  // the tasks for the logged in user\n  useEffect(() =\u003e {\n    if (!user) return\n\n    async function loadTasks() {\n      setLoading(true)\n      const { data, error } = await client.from('tasks').select()\n      if (!error) setTasks(data)\n      setLoading(false)\n    }\n\n    loadTasks()\n  }, [user])\n\n  async function createTask(e: React.FormEvent\u003cHTMLFormElement\u003e) {\n    e.preventDefault()\n    // Insert task into the \"tasks\" database\n    await client.from('tasks').insert({\n      name,\n    })\n    window.location.reload()\n  }\n\n  return (\n    \u003cdiv\u003e\n      \u003ch1\u003eTasks\u003c/h1\u003e\n\n      {loading \u0026\u0026 \u003cp\u003eLoading...\u003c/p\u003e}\n\n      {!loading \u0026\u0026 tasks.length \u003e 0 \u0026\u0026 tasks.map((task: any) =\u003e \u003cp\u003e{task.name}\u003c/p\u003e)}\n\n      {!loading \u0026\u0026 tasks.length === 0 \u0026\u0026 \u003cp\u003eNo tasks found\u003c/p\u003e}\n\n      \u003cform onSubmit={createTask}\u003e\n        \u003cinput\n          autoFocus\n          type=\"text\"\n          name=\"name\"\n          placeholder=\"Enter new task\"\n          onChange={(e) =\u003e setName(e.target.value)}\n          value={name}\n        /\u003e\n        \u003cbutton type=\"submit\"\u003eAdd\u003c/button\u003e\n      \u003c/form\u003e\n    \u003c/div\u003e\n  )\n}\n```","website":"https://clerk.com/","docs":"https://clerk.com/","contact":32,"approved":true,"created_at":"2022-03-27T14:15:58.982685+00:00","tsv":"'/).':112C '/dashboard/projects),':407C,840C '/docs).':1146C '/docs/backend-requests/making/jwt-templates#creating-a-template)':1040C '/docs/guides/auth/third-party/clerk)':38C '/docs/guides/resources/glossary#jwt-signing-secret).':999C '/docs/quickstarts/nextjs)':1138C '/docs/reference/javascript/installing)':1087C '/last-active?path=jwt-templates).':926C '/sign-up).':216C '/supabase/supabase-js)':1077C '0':1662C,1669C '1':17C,339C,397C,544C,619C,689C,830C,918C '2':412C,471C,556C,637C,736C,846C,927C '2025':19C '3':418C,564C,603C,646C,935C '4':576C,784C,949C '5':583C,871C '6':588C,1056C '7':596C 'access':252C,267C,394C,794C,903C,1584C 'accord':227C 'account':192C,206C,211C,527C 'ad':1160C 'add':130C,421C,572C,649C,1003C,1692C 'advanc':445C 'algorithm':976C,983C 'allow':881C,899C 'alreadi':196C 'anoth':678C 'api':161C,800C,845C,909C 'appear':642C 'appli':236C 'applic':135C,1059C,1131C,1171C,1369C,1411C 'april':18C 'assum':187C 'async':1285C,1542C,1618C,1638C 'auth':1693 'authent':2B,68C,131C,166C,292C,333C,365C,713C,760C,803C,811C,889C,900C,1112C 'author':86C,173C,1338C,1567C 'autofocus':1677C 'automat':680C 'avail':88C,175C,1139C 'await':1298C,1547C,1626C,1650C 'b2b':157C 'bar':623C 'base':255C 'basic':1155C 'bearer':1339C,1568C 'beauti':121C 'belong':270C 'breviti':1358C 'bubbl':1052C 'build':163C 'built':1088C 'button':652C,940C 'call':1341C,1570C 'capabl':132C 'chang':429C 'check':344C,770C 'clerk':1A,25C,34C,57C,67C,109C,113C,136C,169C,205C,260C,287C,303C,325C,360C,806C,877C,913C,922C,1034C,1124C,1127C,1142C,1158C,1197C,1243C,1257C,1283C,1289C,1312C,1325C,1479C,1500C,1514C,1540C,1554C,1589C,1694 'clerk.com':111C,1039C,1137C,1145C 'clerk.com/).':110C 'clerk.com/docs).':1144C 'clerk.com/docs/backend-requests/making/jwt-templates#creating-a-template)':1038C 'clerk.com/docs/quickstarts/nextjs)':1136C 'clerk/nextjs':1225C,1447C 'clerktoken':1297C,1340C,1546C,1569C 'client':1067C,1073C,1102C,1175C,1179C,1253C,1361C,1372C,1437C,1510C,1581C,1592C 'client.from':1627C,1651C 'code':331C,968C,1354C 'column':298C,476C,499C,508C,570C,575C,602C 'combin':83C 'command':706C,753C 'communiti':1091C 'complet':1054C 'compon':127C 'configur':950C,1057C,1065C,1126C,1157C,1172C 'confirm':470C 'connect':104C 'const':1246C,1296C,1331C,1371C,1456C,1461C,1466C,1488C,1503C,1545C,1560C,1591C,1623C 'creat':209C,247C,279C,294C,311C,312C,340C,349C,414C,472C,495C,539C,600C,610C,658C,691C,694C,738C,741C,872C,911C,1249C,1309C,1364C,1431C,1506C,1579C 'createclerksupabasecli':1265C,1373C,1522C,1593C 'createcli':1227C,1267C,1390C,1449C,1524C 'createtask':1640C,1675C 'current':462C 'custom':1027C,1210C,1251C,1279C,1508C,1536C 'd':1384C 'dashboard':404C,837C,923C,1313C 'dashboard.clerk.com':215C,925C 'dashboard.clerk.com/last-active?path=jwt-templates).':924C 'dashboard.clerk.com/sign-up).':214C 'data':254C,268C,317C,396C,666C,797C,905C,1185C,1414C,1418C,1482C,1586C,1624C,1633C 'databas':107C,194C,225C,410C,1189C,1649C 'default':300C,375C,542C,590C,981C,1024C,1231C,1343C,1453C,1572C 'defin':231C 'definit':454C 'demonstr':1400C 'deprec':10C,15C 'design':122C 'disabl':629C,657C 'doc':35C,996C,1143C 'document':98C 'done':1208C 'drop':124C 'drop-in':123C 'e':1641C,1687C 'e.preventdefault':1643C 'e.target.value':1689C 'editor':555C 'email':147C 'enabl':604C,614C,632C,644C 'enforc':663C 'ensur':389C,1477C 'enter':1683C 'error':1625C,1631C 'exampl':1105C,1396C,1399C 'execut':337C 'explain':101C 'export':1230C,1452C 'express':719C,766C 'fals':1635C 'fetch':1212C,1284C,1344C,1346C,1541C,1573C,1575C 'field':859C,958C,1021C 'find':825C 'first':823C 'flow':167C 'follow':428C,457C,478C,726C,774C,1132C,1378C,1398C 'form':1673C 'found':1672C 'framework':1121C 'free':213C 'function':60C,77C,281C,342C,351C,368C,411C,417C,424C,433C,536C,1168C,1232C,1264C,1391C,1454C,1521C,1619C,1639C 'generat':809C,884C 'get':785C,1241C,1277C,1498C,1534C 'gettoken':1294C,1300C,1549C 'github.com':1076C 'github.com/supabase/supabase-js)':1075C 'give':792C 'global':1276C,1533C 'guid':100C,186C,241C,1037C,1093C,1149C,1382C 'header':1204C,1263C,1330C,1332C,1334C,1336C,1349C,1520C,1559C,1561C,1563C,1565C,1578C 'headers.set':1337C,1566C 'helper':328C 'home':1233C,1455C 'hook':1236C,1472C,1493C 'hs256':979C 'id':261C,289C,297C,306C,348C,355C,362C,379C,437C,475C,498C,535C,582C,595C,671C,685C,730C,732C,778C,780C 'import':1221C,1226C,1438C,1443C,1448C 'incom':346C 'inform':41C 'inject':1255C,1512C 'input':1676C 'insert':322C,681C,743C,755C,1323C,1417C,1552C,1644C,1653C 'integr':12C,29C,47C,170C,219C,327C 'javascript/typescript':1072C 'js':1101C 'json':466C 'jsx':459C,727C,775C 'jwt':788C,820C,827C,849C,857C,875C,879C,914C,933C,1009C,1035C,1306C 'jwts':988C 'key':790C,822C,829C,1002C,1011C,1275C,1532C 'languag':449C,1084C 'learn':991C,1042C 'leav':1018C 'left':551C 'level':94C,181C 'librari':1074C,1081C,1119C 'like':1218C 'limit':61C 'link':148C,1148C 'list':946C 'll':494C 'load':1462C,1481C,1604C,1658C,1659C,1660C,1667C 'loadtask':1620C,1636C 'log':1485C,1611C 'logic':87C,174C 'make':426C 'manag':70C,76C,117C 'match':674C 'mechan':1108C 'method':1295C 'modal':640C 'modern':8B 'modifi':520C,1184C,1193C 'name':352C,431C,579C,700C,747C,957C,972C,1303C,1318C,1467C,1654C,1680C,1681C,1691C 'navig':408C,545C,841C,919C,930C 'need':481C,815C,1031C,1190C 'new':24C,308C,416C,423C,574C,886C,938C,1333C,1562C,1684C 'next':492C,869C,1061C,1176C 'next.js':4B,1097C,1130C,1134C,1381C 'notif':1051C 'nullif':461C 'numer':138C 'object':1245C,1291C,1502C,1582C,1601C 'offici':1071C 'onchang':1686C 'one':661C,1394C 'onsubmit':1674C 'option':948C,1287C,1335C,1348C,1544C,1564C,1577C 'overview':66C 'pars':285C,358C 'part':1200C 'parti':28C 'pass':1301C 'passkey':150C 'perform':484C 'placehold':1682C 'platform':118C 'polici':97C,184C,235C,249C,313C,387C,515C,612C,651C,660C,692C,697C,703C,705C,735C,739C,744C,750C,752C,783C,1430C 'popul':452C 'postgresql':92C,179C 'previous':1014C,1377C,1433C 'primit':51C 'process.env.next':1268C,1272C,1525C,1529C 'project':195C,807C,818C,843C,1098C 'provid':65C,74C,120C,146C,698C,716C,745C,763C,1069C 'public':1269C,1273C,1526C,1530C 'queri':226C,338C,1182C,1413C 'quick':129C 'quickstart':1135C 'react':5B,1442C 'react.formevent':1642C 'read':320C 'record':309C,521C,682C 'remain':59C 'remov':1356C 'replac':21C,655C,714C,761C,1385C 'request':353C,435C,533C,593C,728C,776C,1203C,1262C,1519C,1606C 'request.jwt.claims':464C 'requestor':676C,688C 'requir':801C,961C,985C 'rest':1351C 'restrict':230C,251C,315C 'return':439C,518C,667C,1266C,1345C,1523C,1574C,1617C,1656C 'rls':96C,183C,221C,234C,248C,386C,514C,605C,615C,628C,633C,645C,650C,656C,1429C 'role':711C,758C 'row':93C,180C 'saa':158C 'save':598C,734C,782C,852C,1048C 'scope':522C 'secret':789C,821C,828C,858C,1010C 'section':851C 'secur':95C,182C,491C,506C,563C,861C,1426C 'see':30C,1032C 'select':413C,460C,469C,553C,557C,568C,597C,627C,643C,647C,690C,696C,707C,708C,712C,733C,737C,754C,759C,781C,932C,936C,942C,1047C,1629C 'session':71C,1244C,1247C,1290C,1299C,1501C,1504C,1548C 'set':197C,274C,373C,430C,438C,446C,448C,463C,577C,584C,589C,844C,850C,1025C,1046C,1110C,1122C 'setload':1463C,1621C,1634C 'setnam':1468C,1688C 'settask':1458C,1632C 'setup':1055C 'sheet':425C 'show':243C,444C 'side':58C 'sidebar':400C,548C,833C,931C 'sign':140C,894C,975C,1001C,1162C,1166C 'sign-in':139C,1165C 'sign-up':1161C 'signed-in':893C 'similar':1116C 'sinc':49C 'social':145C 'somewher':860C 'sql':451C,458C,718C,765C 'statement':723C,771C 'step':479C,870C,1015C,1062C,1434C 'still':44C 'strategi':142C 'sub':467C 'suit':155C 'supabas':32C,55C,90C,106C,177C,191C,283C,326C,335C,403C,787C,798C,817C,836C,874C,888C,907C,917C,943C,974C,990C,995C,1008C,1068C,1174C,1178C,1188C,1217C,1252C,1258C,1270C,1274C,1280C,1320C,1322C,1326C,1380C,1509C,1515C,1527C,1531C,1537C,1551C,1555C,1585C 'supabase.com':37C,406C,839C,998C,1086C 'supabase.com/dashboard/projects),':405C,838C 'supabase.com/docs/guides/auth/third-party/clerk)':36C 'supabase.com/docs/guides/resources/glossary#jwt-signing-secret).':997C 'supabase.com/docs/reference/javascript/installing)':1085C 'supabase/supabase-js':1229C,1451C 'support':62C,137C 'tabl':239C,382C,487C,502C,554C,559C,567C,608C,618C,626C,636C,1422C 'target':710C,757C 'task':1421C,1457C,1608C,1628C,1645C,1648C,1652C,1657C,1664C,1671C,1685C 'task.name':1666C 'tasks.length':1661C,1668C 'tasks.map':1663C 'techniqu':1403C 'templat':876C,880C,915C,934C,939C,952C,965C,1036C,1307C,1321C,1550C 'text':442C,468C,587C,1679C 'third':27C 'third-parti':26C 'throughout':1367C 'to-do':1408C 'toggl':443C 'token':72C,293C,366C,804C,812C,890C,898C,1113C,1198C,1259C,1281C,1327C,1516C,1538C,1556C,1590C 'tool':159C 'top':622C 'true':465C,1465C,1622C 'tsx':1220C,1370C,1435C 'tutori':971C,1316C 'two':659C 'type':440C,585C,1678C 'ui':126C 'unoffici':48C 'url':1271C,1286C,1347C,1528C,1543C,1576C 'use':45C,81C,171C,323C,371C,511C,531C,722C,866C,963C,987C,1095C,1180C,1195C,1215C,1239C,1366C,1387C,1405C,1436C,1475C,1496C,1587C 'useeffect':1439C,1595C,1614C 'user':69C,75C,116C,258C,264C,288C,296C,304C,347C,354C,361C,378C,391C,436C,474C,497C,525C,534C,581C,594C,670C,729C,731C,777C,779C,793C,896C,901C,1487C,1489C,1600C,1613C,1616C,1637C 'usesess':1222C,1235C,1248C,1444C,1492C,1505C 'usest':1440C,1459C,1464C,1469C 'useus':1223C,1445C,1471C,1490C 'valid':224C,887C 'valu':376C,543C,591C,672C,854C,863C,954C,1005C,1690C 'wait':1597C 'walk':1151C 'way':263C 'web':9B 'well':152C 'window.location.reload':1655C 'wish':489C,504C,561C 'work':220C,222C","video":"cqD01r4SyzA","call_to_action_link":null,"featured":false},"overview":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nconst chCodeConfig = {\n  \"staticMediaQuery\": \"not screen, (max-width: 768px)\",\n  \"lineNumbers\": true,\n  \"showCopyButton\": true,\n  \"themeName\": \"supabase\"\n};\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    h2: \"h2\",\n    p: \"p\",\n    a: \"a\",\n    ul: \"ul\",\n    li: \"li\",\n    code: \"code\",\n    ol: \"ol\",\n    strong: \"strong\",\n    h3: \"h3\"\n  }, _provideComponents(), props.components), {Admonition, CH} = _components;\n  if (!Admonition) _missingMdxReference(\"Admonition\", true);\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Deprecation\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"This integration is being deprecated on 1 April 2025, and replaced with the new Clerk third-party integration. See the \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/guides/auth/third-party/clerk\",\n        children: \"Supabase with Clerk docs\"\n      }), \" for more information.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You can still use this integration unofficially since the primitives on both the Supabase and Clerk sides remain functional. Limited support will be provided.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Overview\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Clerk authenticates users, manages session tokens, and provides user management functionality that can be used in combination with the authorization logic available in Supabase through PostgreSQL Row Level Security (RLS) policies.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Documentation\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"This guide explains how to connect your Supabase database with \", _jsx(_components.a, {\n        href: \"https://clerk.com/\",\n        children: \"Clerk\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Clerk is a user management platform that provides beautifully designed, drop-in UI components to quickly add authentication capabilities to your application. Clerk supports numerous sign-in strategies such as social providers, email links, and passkeys, as well as a suite of B2B SaaS tools and APIs to build your own authentication flows.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The Clerk integration uses the authorization logic available in Supabase through PostgreSQL Row Level Security (RLS) policies.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This guide assumes you have a Supabase account and database project already set up.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you don't have a Clerk account, you can \", _jsx(_components.a, {\n        href: \"https://dashboard.clerk.com/sign-up\",\n        children: \"create an account for free\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"How the integration works\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"RLS works by validating database queries according to the restrictions defined in the RLS policies applied to the table. This guide will show you how to create RLS policies that restrict access to data based on the user's Clerk ID. This way, users can only access data that belongs to them.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To set this up, you will:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Create a function in Supabase to parse the Clerk user ID from the authentication token.\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Create a \", _jsx(_components.code, {\n          children: \"user_id\"\n        }), \" column that defaults to the Clerk user's ID when new records are created.\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Create policies to restrict what data can be read and inserted.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Use the Clerk Supabase integration helper in your code to authenticate with Supabase and execute queries.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"1: Create a function to check the incoming user ID\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Create a function named \", _jsx(_components.code, {\n        children: \"requesting_user_id()\"\n      }), \" that will parse the Clerk user ID from the authentication token. This function will be used to set the default value of \", _jsx(_components.code, {\n        children: \"user_id\"\n      }), \" in a table and in the RLS policies to ensure the user can only access their data.\"]\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"\\n\", _jsxs(_components.p, {\n          children: [\"In the sidebar of your \", _jsx(_components.a, {\n            href: \"https://supabase.com/dashboard/projects\",\n            children: \"Supabase dashboard\"\n          }), \", navigate to \", _jsx(_components.strong, {\n            children: \"Database\"\n          }), \" \u003e \", _jsx(_components.strong, {\n            children: \"Functions\"\n          }), \".\"]\n        }), \"\\n\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"\\n\", _jsxs(_components.p, {\n          children: [\"Select \", _jsx(_components.strong, {\n            children: \"Create a new function\"\n          }), \".\"]\n        }), \"\\n\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"\\n\", _jsxs(_components.p, {\n          children: [\"In the \", _jsx(_components.strong, {\n            children: \"Add a new function\"\n          }), \" sheet, make the following changes:\"]\n        }), \"\\n\", _jsxs(_components.ul, {\n          children: [\"\\n\", _jsxs(_components.li, {\n            children: [\"Set \", _jsx(_components.strong, {\n              children: \"Name of function\"\n            }), \" to \", _jsx(_components.code, {\n              children: \"requesting_user_id\"\n            }), \".\"]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"Set \", _jsx(_components.strong, {\n              children: \"Return type\"\n            }), \" to \", _jsx(_components.code, {\n              children: \"text\"\n            }), \".\"]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"Toggle \", _jsx(_components.strong, {\n              children: \"Show advanced settings\"\n            }), \" on.\"]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"Set \", _jsx(_components.strong, {\n              children: \"Language\"\n            }), \" to \", _jsx(_components.code, {\n              children: \"sql\"\n            }), \".\"]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"Populate the \", _jsx(_components.strong, {\n              children: \"Definition\"\n            }), \" with the following sql:\"]\n          }), \"\\n\"]\n        }), \"\\n\", _jsx(CH.Code, {\n          codeConfig: chCodeConfig,\n          northPanel: {\n            \"tabs\": [\"\"],\n            \"active\": \"\",\n            \"heightRatio\": 1\n          },\n          files: [{\n            \"name\": \"\",\n            \"focus\": \"\",\n            \"code\": {\n              \"lines\": [{\n                \"tokens\": [{\n                  \"content\": \"SELECT \",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-2)\"\n                    }\n                  }\n                }, {\n                  \"content\": \"NULLIF\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-5)\"\n                    }\n                  }\n                }, {\n                  \"content\": \"(\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"    current_setting\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-5)\"\n                    }\n                  }\n                }, {\n                  \"content\": \"(\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }, {\n                  \"content\": \"'request.jwt.claims'\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-8)\"\n                    }\n                  }\n                }, {\n                  \"content\": \", \",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }, {\n                  \"content\": \"true\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-2)\"\n                    }\n                  }\n                }, {\n                  \"content\": \")::json\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }, {\n                  \"content\": \"-\u003e\u003e\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-7)\"\n                    }\n                  }\n                }, {\n                  \"content\": \"'sub'\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-8)\"\n                    }\n                  }\n                }, {\n                  \"content\": \",\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"    ''\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-8)\"\n                    }\n                  }\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \")::text;\",\n                  \"props\": {\n                    \"style\": {\n                      \"color\": \"var(--ch-4)\"\n                    }\n                  }\n                }]\n              }],\n              \"lang\": \"jsx\"\n            },\n            \"annotations\": []\n          }]\n        }), \"\\n\", _jsxs(_components.ul, {\n          children: [\"\\n\", _jsxs(_components.li, {\n            children: [\"Select \", _jsx(_components.strong, {\n              children: \"Confirm\"\n            }), \".\"]\n          }), \"\\n\"]\n        }), \"\\n\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.h2, {\n      children: [\"2: Create a \", _jsx(_components.code, {\n        children: \"user_id\"\n      }), \" column\"]\n    }), \"\\n\", _jsx(Admonition, {\n      type: \"info\",\n      children: _jsx(_components.p, {\n        children: \"💡 The following steps will need to be performed on any tables you wish to secure.\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Next, you’ll create a \", _jsx(_components.code, {\n        children: \"user_id\"\n      }), \" column in the table you wish to secure. This column will be used in the RLS policies to only return or modify records scoped to the user's account and it will use the \", _jsx(_components.code, {\n        children: \"requesting_user_id()\"\n      }), \" function you just created as its default value.\"]\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Navigate to the sidebar on the left and select \", _jsx(_components.strong, {\n          children: \"Table Editor.\"\n        })]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Select the table you wish to secure.\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In the table, select the \", _jsx(_components.strong, {\n          children: \"+\"\n        }), \" column to add a new column.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Set the \", _jsx(_components.strong, {\n          children: \"Name\"\n        }), \" to \", _jsx(_components.strong, {\n          children: \"user_id\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Set \", _jsx(_components.strong, {\n          children: \"Type\"\n        }), \" to \", _jsx(_components.strong, {\n          children: \"text\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Set \", _jsx(_components.strong, {\n          children: \"Default Value\"\n        }), \" to \", _jsx(_components.code, {\n          children: \"requesting_user_id()\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Select \", _jsx(_components.strong, {\n          children: \"Save\"\n        }), \" to create the column.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"3: Enable RLS on your table and create the policies\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To enable RLS on your table:\"\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"In the top bar above the table, select \", _jsx(_components.strong, {\n          children: \"RLS disabled\"\n        }), \" and then \", _jsx(_components.strong, {\n          children: \"Enable RLS for this table\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In the modal that appears, select \", _jsx(_components.strong, {\n          children: \"Enable RLS\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Select the \", _jsx(_components.strong, {\n          children: \"Add RLS policy\"\n        }), \" button (which has replaced \", _jsx(_components.strong, {\n          children: \"RLS disabled).\"\n        })]\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Create two policies: one to enforce that the data returned has a \", _jsx(_components.code, {\n        children: \"user_id\"\n      }), \" value that matches the requestor, and another to automatically insert records with the ID of the requestor.\"]\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Select \", _jsx(_components.strong, {\n          children: \"Create policy\"\n        }), \" to create the \", _jsx(_components.code, {\n          children: \"SELECT\"\n        }), \" policy:\", \"\\n\", _jsxs(_components.ul, {\n          children: [\"\\n\", _jsxs(_components.li, {\n            children: [\"\\n\", _jsx(_components.p, {\n              children: \"Provide a name for the policy.\"\n            }), \"\\n\"]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"\\n\", _jsxs(_components.p, {\n              children: [\"For \", _jsx(_components.strong, {\n                children: \"Policy Command\"\n              }), \", select \", _jsx(_components.strong, {\n                children: \"SELECT\"\n              }), \".\"]\n            }), \"\\n\"]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"\\n\", _jsxs(_components.p, {\n              children: [\"For \", _jsx(_components.strong, {\n                children: \"Target roles\"\n              }), \", select \", _jsx(_components.strong, {\n                children: \"authenticated\"\n              }), \".\"]\n            }), \"\\n\"]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"\\n\", _jsx(_components.p, {\n              children: \"Replace the \\\"-- Provide a SQL expression for the using statement\\\" with the following:\"\n            }), \"\\n\", _jsx(CH.Code, {\n              codeConfig: chCodeConfig,\n              northPanel: {\n                \"tabs\": [\"\"],\n                \"active\": \"\",\n                \"heightRatio\": 1\n              },\n              files: [{\n                \"name\": \"\",\n                \"focus\": \"\",\n                \"code\": {\n                  \"lines\": [{\n                    \"tokens\": [{\n                      \"content\": \"requesting_user_id\",\n                      \"props\": {\n                        \"style\": {\n                          \"color\": \"var(--ch-5)\"\n                        }\n                      }\n                    }, {\n                      \"content\": \"() \",\n                      \"props\": {\n                        \"style\": {\n                          \"color\": \"var(--ch-4)\"\n                        }\n                      }\n                    }, {\n                      \"content\": \"=\",\n                      \"props\": {\n                        \"style\": {\n                          \"color\": \"var(--ch-7)\"\n                        }\n                      }\n                    }, {\n                      \"content\": \" user_id\",\n                      \"props\": {\n                        \"style\": {\n                          \"color\": \"var(--ch-4)\"\n                        }\n                      }\n                    }]\n                  }],\n                  \"lang\": \"jsx\"\n                },\n                \"annotations\": []\n              }]\n            }), \"\\n\"]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"\\n\", _jsxs(_components.p, {\n              children: [\"Select \", _jsx(_components.strong, {\n                children: \"Save policy\"\n              }), \".\"]\n            }), \"\\n\"]\n          }), \"\\n\"]\n        }), \"\\n\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Select \", _jsx(_components.strong, {\n          children: \"Create policy\"\n        }), \" to create the \", _jsx(_components.code, {\n          children: \"INSERT\"\n        }), \" policy:\", \"\\n\", _jsxs(_components.ul, {\n          children: [\"\\n\", _jsxs(_components.li, {\n            children: [\"\\n\", _jsx(_components.p, {\n              children: \"Provide a name for the policy.\"\n            }), \"\\n\"]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"\\n\", _jsxs(_components.p, {\n              children: [\"For \", _jsx(_components.strong, {\n                children: \"Policy Command\"\n              }), \", select \", _jsx(_components.strong, {\n                children: \"INSERT\"\n              }), \".\"]\n            }), \"\\n\"]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"\\n\", _jsxs(_components.p, {\n              children: [\"For \", _jsx(_components.strong, {\n                children: \"Target roles\"\n              }), \", select \", _jsx(_components.strong, {\n                children: \"authenticated\"\n              }), \".\"]\n            }), \"\\n\"]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"\\n\", _jsx(_components.p, {\n              children: \"Replace the \\\"-- Provide a SQL expression for the with check statement\\\" with the following:\"\n            }), \"\\n\", _jsx(CH.Code, {\n              codeConfig: chCodeConfig,\n              northPanel: {\n                \"tabs\": [\"\"],\n                \"active\": \"\",\n                \"heightRatio\": 1\n              },\n              files: [{\n                \"name\": \"\",\n                \"focus\": \"\",\n                \"code\": {\n                  \"lines\": [{\n                    \"tokens\": [{\n                      \"content\": \"requesting_user_id\",\n                      \"props\": {\n                        \"style\": {\n                          \"color\": \"var(--ch-5)\"\n                        }\n                      }\n                    }, {\n                      \"content\": \"() \",\n                      \"props\": {\n                        \"style\": {\n                          \"color\": \"var(--ch-4)\"\n                        }\n                      }\n                    }, {\n                      \"content\": \"=\",\n                      \"props\": {\n                        \"style\": {\n                          \"color\": \"var(--ch-7)\"\n                        }\n                      }\n                    }, {\n                      \"content\": \" user_id\",\n                      \"props\": {\n                        \"style\": {\n                          \"color\": \"var(--ch-4)\"\n                        }\n                      }\n                    }]\n                  }],\n                  \"lang\": \"jsx\"\n                },\n                \"annotations\": []\n              }]\n            }), \"\\n\"]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"\\n\", _jsxs(_components.p, {\n              children: [\"Select \", _jsx(_components.strong, {\n                children: \"Save policy\"\n              }), \".\"]\n            }), \"\\n\"]\n          }), \"\\n\"]\n        }), \"\\n\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"4: Get your Supabase JWT secret key\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To give users access to your data, Supabase's API requires an authentication token. Your Clerk project can generate these authentication tokens, but it needs your Supabase project's JWT secret key first.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To find the JWT secret key:\"\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"In the sidebar of your \", _jsx(_components.a, {\n          href: \"https://supabase.com/dashboard/projects\",\n          children: \"Supabase dashboard\"\n        }), \", navigate to \", _jsx(_components.strong, {\n          children: \"Project Settings \u003e API\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Under the \", _jsx(_components.strong, {\n          children: \"JWT Settings\"\n        }), \" section, save the value in the \", _jsx(_components.strong, {\n          children: \"JWT Secret\"\n        }), \" field somewhere secure. This value will be used in the next step.\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"5: Create a Supabase JWT template\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Clerk's JWT templates allow you to generate a new valid Supabase authentication token for each signed-in user. These tokens allow authenticated users to access your data with Supabase's API.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To create a Clerk JWT template for Supabase:\"\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Navigate to the \", _jsx(_components.a, {\n          href: \"https://dashboard.clerk.com/last-active?path=jwt-templates\",\n          children: \"Clerk Dashboard\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"In the navigation sidebar, select \", _jsx(_components.strong, {\n          children: \"JWT Templates\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Select the \", _jsx(_components.strong, {\n          children: \"New template\"\n        }), \" button, then select \", _jsx(_components.strong, {\n          children: \"Supabase\"\n        }), \" from the list of options.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Configure your template:\", \"\\n\", _jsxs(_components.ul, {\n          children: [\"\\n\", _jsxs(_components.li, {\n            children: [\"The value of the \", _jsx(_components.strong, {\n              children: \"Name\"\n            }), \" field will be required when using the template in your code. For this tutorial, name it \", _jsx(_components.code, {\n              children: \"supabase\"\n            }), \".\"]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [_jsx(_components.strong, {\n              children: \"Signing algorithm\"\n            }), \" will be \", _jsx(_components.code, {\n              children: \"HS256\"\n            }), \" by default. This algorithm is required to use JWTs with Supabase. \", _jsx(_components.a, {\n              href: \"https://supabase.com/docs/guides/resources/glossary#jwt-signing-secret\",\n              children: \"Learn more in the Supabase docs\"\n            }), \".\"]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"Under \", _jsx(_components.strong, {\n              children: \"Signing key\"\n            }), \", add the value of your Supabase \", _jsx(_components.strong, {\n              children: \"JWT secret key\"\n            }), \" from the previous step.\"]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"You can leave all other fields at their default settings or customize them to your needs. See the \", _jsx(_components.a, {\n              href: \"https://clerk.com/docs/backend-requests/making/jwt-templates#creating-a-template\",\n              children: \"Clerk JWT template guide\"\n            }), \" to learn more about these settings.\"]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"Select \", _jsx(_components.strong, {\n              children: \"Save\"\n            }), \" from the notification bubble to complete setup.\"]\n          }), \"\\n\"]\n        }), \"\\n\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"6: Configure your application\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The next step is to configure your client. Supabase provides an official \", _jsx(_components.a, {\n        href: \"https://github.com/supabase/supabase-js\",\n        children: \"JavaScript/TypeScript client library\"\n      }), \" and there are \", _jsx(_components.a, {\n        href: \"https://supabase.com/docs/reference/javascript/installing\",\n        children: \"libraries in other languages\"\n      }), \" built by the community.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This guide will use a Next.js project with the JS client as an example, but the mechanism of setting the authentication token should be similar to other libraries and frameworks.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Set up Clerk\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To configure Clerk in your Next.js application, follow the \", _jsx(_components.a, {\n        href: \"https://clerk.com/docs/quickstarts/nextjs\",\n        children: \"Next.js Quickstart\"\n      }), \" available in the \", _jsx(_components.a, {\n        href: \"https://clerk.com/docs\",\n        children: \"Clerk docs\"\n      }), \". The linked guide will walk you through the basics of configuring Clerk by adding sign-up and sign-in functionality to your application.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Configure the Supabase client\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Next, the Supabase client used to query and modify data in your Supabase database needs to be modified to use the Clerk token as part of the request headers. This can be done by customizing the \", _jsx(_components.code, {\n        children: \"fetch\"\n      }), \" that is used by Supabase like so:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { useSession, useUser } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'@clerk/nextjs'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { createClient } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'@supabase/supabase-js'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export default function \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"Home\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"() \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t// The `useSession()` hook will be used to get the Clerk `session` object\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\tconst\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"session\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"useSession\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t// Create a custom supabase client that injects the Clerk Supabase token into the request headers\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\tfunction \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"createClerkSupabaseClient\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"() {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t  return \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"createClient\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t    process.env.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"NEXT_PUBLIC_SUPABASE_URL\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"!\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t    process.env.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"NEXT_PUBLIC_SUPABASE_KEY\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"!\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t    {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t      global: {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t        // Get the custom Supabase token from Clerk\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t        fetch\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"async\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"url\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"options \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {}) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t\\t        // The Clerk `session` object has the getToken() method      \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t          const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"clerkToken \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" session?.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"getToken\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t\\t          // Pass the name of the JWT template you created in the Clerk Dashboard\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t\\t          // For this tutorial, you named it 'supabase'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t            template: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'supabase'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t          })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t          \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t          // Insert the Clerk Supabase token into the headers\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t\\t        const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"headers \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= new \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"Headers\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(options?.headers)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t          headers.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"set\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'Authorization'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"`Bearer ${\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"clerkToken\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}`\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t          \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t          // Call the default fetch\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t          return \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"fetch\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(url, {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t            ...\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"options,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t            headers,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t          })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t        },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t      },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t    },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t  )\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\\t//... The rest of the code is removed for brevity\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"tsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Then the client can be created and used throughout the application:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"client \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"createClerkSupabaseClient\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"tsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you have previously followed the Supabase Next.js guide, you’d replace any use of the \", _jsx(_components.code, {\n        children: \"createClient\"\n      }), \" function with the one above.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Example\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"The following example demonstrates how this technique is used in a to-do application that queries data from and inserts data into a \", _jsx(_components.code, {\n        children: \"tasks\"\n      }), \" table, which will be secured with the RLS policies created in previous steps:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"'use client'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { useEffect, useState } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'react'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { useSession, useUser } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'@clerk/nextjs'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { createClient } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'@supabase/supabase-js'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export default function \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"Home\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"() \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" [\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"tasks\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"setTasks\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"] \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"useState\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"any\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"[]\u003e([])\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" [\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"loading\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"setLoading\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"] \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"useState\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"true\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" [\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"name\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"setName\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"] \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"useState\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"''\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  // The `useUser()` hook will be used to ensure that Clerk has loaded data about the logged in user\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"user\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"useUser\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  // The `useSession()` hook will be used to get the Clerk `session` object\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"session\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"useSession\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  // Create a custom supabase client that injects the Clerk Supabase token into the request headers\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  function \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"createClerkSupabaseClient\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"() {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    return \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"createClient\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      process.env.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"NEXT_PUBLIC_SUPABASE_URL\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"!\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      process.env.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"NEXT_PUBLIC_SUPABASE_KEY\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"!\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        global: {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          // Get the custom Supabase token from Clerk\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          fetch\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"async\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"url\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"options \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {}) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"            const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"clerkToken \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" session?.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"getToken\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"              template: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'supabase'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"            })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"            // Insert the Clerk Supabase token into the headers\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"            const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"headers \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= new \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"Headers\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(options?.headers)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"            headers.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"set\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'Authorization'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"`Bearer ${\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"clerkToken\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}`\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"            // Call the default fetch\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"            return \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"fetch\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(url, {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"              ...\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"options,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"              headers,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"            })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    )\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  // Create a `client` object for accessing Supabase data using the Clerk token\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"client \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"createClerkSupabaseClient\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  // This `useEffect` will wait for the `user` object to be loaded before requesting\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  // the tasks for the logged in user\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  useEffect\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(() \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"!\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"user) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    async function \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"loadTasks\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"() {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      setLoading\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"true\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"data\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"error\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" client.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'tasks'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \").\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"select\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"!\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"error) \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"setTasks\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(data)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      setLoading\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"false\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    loadTasks\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }, [user])\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  async function \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"createTask\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"React\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"FormEvent\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"HTMLFormElement\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e) {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    e.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"preventDefault\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    // Insert task into the \\\"tasks\\\" database\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" client.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'tasks'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \").\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"insert\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      name,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    window.location.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"reload\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"div\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"h1\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003eTasks\u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"h1\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"loading \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\u0026\u0026\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003eLoading...\u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      {!\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"loading \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\u0026\u0026\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" tasks.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"length \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"0 \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"\u0026\u0026\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" tasks.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"map\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"((\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"task\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"any\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"task.name\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      {!\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"loading \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\u0026\u0026\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" tasks.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"length \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"=== \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"0 \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"\u0026\u0026\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003eNo tasks found\u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"form \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"onSubmit\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"createTask\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"input\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          autoFocus\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          type\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"text\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          name\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"name\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          placeholder\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"Enter new task\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          onChange\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"setName\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(e.target.value)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          value\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"name\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        /\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"button \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"type\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"submit\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003eAdd\u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"button\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"form\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"div\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  )\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"tsx\"\n        },\n        \"annotations\": []\n      }]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  chCodeConfig,\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{}}},"__N_SSG":true},"page":"/partners/integrations/[slug]","query":{"slug":"clerk"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"isExperimentalCompile":false,"gsp":true,"scriptLoader":[]}</script></body></html>