<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../../rss.xml" data-next-head=""/><link rel="manifest" href="../../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:type" content="website" data-next-head=""/><meta property="og:image:alt" content="Supabase Og Image" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Zuplo | Works With Supabase</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="API Management you actually want to use." data-next-head=""/><meta property="og:title" content="Zuplo | Works With Supabase" data-next-head=""/><meta property="og:description" content="API Management you actually want to use." data-next-head=""/><meta property="og:url" content="https://supabase.com/partners/integrations/zuplo" data-next-head=""/><meta property="og:image" content="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/zuplo_og.avif" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2263-07e8ebb96cc2b014.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8658-639e1bb66549b52b.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/partners/integrations/%5Bslug%5D-cff8a94339d02c6e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqela6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R1eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R1eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R2eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R2eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R3eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R3eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav></div><main class="relative min-h-screen"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20"><div class="col-span-12 mx-auto mb-2 max-w-5xl space-y-10 lg:col-span-2"><a class="text-foreground hover:text-foreground-lighter flex cursor-pointer items-center transition-colors" href="../integrations.html"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left"><path d="m15 18-6-6 6-6"></path></svg>Back</a><div class="flex items-center space-x-4"><img alt="Zuplo" loading="lazy" width="56" height="56" decoding="async" data-nimg="1" class="bg-surface-200 flex-shrink-f0 h-14 w-14 rounded-full" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fzuplo%2Fzuplo_logo.png&amp;w=64&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fzuplo%2Fzuplo_logo.png&amp;w=128&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fzuplo%2Fzuplo_logo.png&amp;w=128&amp;q=75"/><h1 class="h1" style="margin-bottom:0">Zuplo</h1></div><div class="bg-gradient-to-t from-background-alternative to-background border-b p-6 [&amp;_.swiper-container]:overflow-visible" style="margin-left:calc(50% - 50vw);margin-right:calc(50% - 50vw)"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0 !px-3 lg:!px-12 xl:!p-0 mx-auto max-w-5xl"><div class="swiper"><div class="swiper-wrapper"><div class="swiper-slide"><div class="relative block overflow-hidden rounded-md"><img alt="Zuplo" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto;background-size:cover;background-position:50% 50%;background-repeat:no-repeat;background-image:url(&quot;data:image/svg+xml;charset=utf-8,%3Csvg xmlns=&#x27;http://www.w3.org/2000/svg&#x27; viewBox=&#x27;0 0 1460 960&#x27;%3E%3Cfilter id=&#x27;b&#x27; color-interpolation-filters=&#x27;sRGB&#x27;%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3CfeColorMatrix values=&#x27;1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1&#x27; result=&#x27;s&#x27;/%3E%3CfeFlood x=&#x27;0&#x27; y=&#x27;0&#x27; width=&#x27;100%25&#x27; height=&#x27;100%25&#x27;/%3E%3CfeComposite operator=&#x27;out&#x27; in=&#x27;s&#x27;/%3E%3CfeComposite in2=&#x27;SourceGraphic&#x27;/%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3C/filter%3E%3Cimage width=&#x27;100%25&#x27; height=&#x27;100%25&#x27; x=&#x27;0&#x27; y=&#x27;0&#x27; preserveAspectRatio=&#x27;none&#x27; style=&#x27;filter: url(%23b);&#x27; href=&#x27;/images/blur.png&#x27;/%3E%3C/svg%3E&quot;)" sizes="100vw" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fzuplo%2Fzuplo_og.avif&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fzuplo%2Fzuplo_og.avif&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fzuplo%2Fzuplo_og.avif&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fzuplo%2Fzuplo_og.avif&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fzuplo%2Fzuplo_og.avif&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fzuplo%2Fzuplo_og.avif&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fzuplo%2Fzuplo_og.avif&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fzuplo%2Fzuplo_og.avif&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fzuplo%2Fzuplo_og.avif&amp;w=3840&amp;q=75"/></div></div></div></div></div></div><div class="grid gap-y-12 lg:grid-cols-8 lg:space-x-12"><div class="lg:col-span-5 overflow-hidden"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Overview</h2><div class="prose"><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p><a href="https://zuplo.com/">Zuplo</a> is a fully-managed API gateway that offers the easiest way to securely and safely share your API. In this guide we look at how you can combine Zuplo and Supabase to create a public API with rate-limiting, a self-serve developer portal, and API-key authentication and much more.</p>
<h2>Documentation</h2>
<p>There is an <a href="https://www.youtube.com/watch?v=GJSkbxMnWxE">accompanying video for this article</a>.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/arch.png" alt="zuplo layout"/></p>
<p>In this example we&#x27;re going to work with a simple table that allows people to read and write entries to a Supabase table that contains some reviews of skis. Because this is an API for developers, we have to assume that they may be calling it from another backend service and can&#x27;t login as a user using the standard Supabase method. In this scenario, API keys are often a better choice - see <a href="https://zuplo.com/blog/2022/05/03/you-should-be-using-api-keys/">Wait, you&#x27;re not using API keys?</a>.</p>
<p>We&#x27;ll allow people, with a valid API key, to read data from the ski results table and to create new records. Hopefully it&#x27;s obvious that there are many ways that you can extend this example to add more behavior like roles based access, with custom policies, custom handlers and more.</p>
<h2>Setting up Supabase</h2>
<p>If you haven&#x27;t already, create a new project in Supabase and create a table called ski-reviews with the following columns:</p>
<ul>
<li>id (int8)</li>
<li>created_at (timestamptz)</li>
<li>make (text)</li>
<li>model (text)</li>
<li>year (int8)</li>
<li>rating (int2)</li>
<li>author (text)</li>
</ul>
<p>Manually enter a couple of rows of data, so that we have something to read from the DB.</p>
<h2>The <code>Get all</code> reviews route in Zuplo</h2>
<p>Login to Zuplo at <a href="https://portal.zuplo.com">portal.zuplo.com</a> and create a new project in Zuplo - I went with <code>supabase-ski-reviews</code>.</p>
<p>Select the <strong>File</strong> tab and choose <strong>Routes</strong>. Add your first route with the following settings:</p>
<ul>
<li>method: <code>GET</code></li>
<li>path: <code>/reviews</code></li>
<li>summary: <code>Get all reviews</code></li>
<li>version: <code>v1</code></li>
<li>CORS: <code>Anything goes</code></li>
</ul>
<p>And in the request handler section, paste the <code>READ ALL ROWS</code> URL of your Supabase backend (you can get to this in the <strong>API docs</strong> section of Supabase)</p>
<ul>
<li>URL Rewrite: <code>https://YOUR_SUPABASE_URL.supabase.co/rest/v1/ski-reviews?select=*</code></li>
<li>Forward Search: <code>unchecked</code></li>
</ul>
<p>In order to call the Supabase backend I need to add some authentication headers to the outgoing request.</p>
<p>Expand the <strong>Policies</strong> section of your route. Click <strong>Add policy</strong> on the <strong>Request</strong> pipeline.</p>
<p>We don&#x27;t want to forward any headers that the client sends us to Supabase, so find the <strong>Clear Headers Policy</strong> and add that to your inbound pipeline. Note, that we will allow the <code>content-type</code> header to flow through, so this should be your policy config.</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>{</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  &quot;export&quot;: &quot;ClearHeadersInboundPolicy&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  &quot;module&quot;: &quot;$import(@zuplo/runtime)&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  &quot;options&quot;: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    &quot;exclude&quot;: [&quot;content-type&quot;]</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<p>Next, we need to add the credentials to the outgoing request. We&#x27;ll need to get the JWT token from supabase - you&#x27;ll find it in <strong>Settings</strong> &gt; <strong>API</strong> as shown below:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/secret-role.png" alt="secret_role jwt"/></p>
<p>Once you&#x27;ve got your service_role JWT, click <strong>Add Policy</strong> again on the <strong>Request</strong> pipeline and choose the <strong>Add/Set Headers Policy</strong> and configure it as follows:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>{</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>  &quot;export&quot;: &quot;SetHeadersInboundPolicy&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>  &quot;module&quot;: &quot;$import(@zuplo/runtime)&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>  &quot;options&quot;: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>    &quot;headers&quot;: [</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>      {</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>        &quot;name&quot;: &quot;apikey&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>        &quot;value&quot;: &quot;$env(SUPABASE_API_KEY)&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>        &quot;overwrite&quot;: true</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>      },</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>      {</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>        &quot;name&quot;: &quot;authorization&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>        &quot;value&quot;: &quot;$env(SUPABASE_AUTHZ_HEADER)&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>        &quot;overwrite&quot;: true</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>      }</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>    ]</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<p>Save your changes.</p>
<p>Next, create two secret <a href="https://zuplo.com/docs/deployments/environment-variables">environment variables</a> as follows:</p>
<ul>
<li>SUPABASE_API_KEY: <code>&quot;YOUR_SUPABASE_SECRET_ROLE_JWT&quot;</code></li>
<li>SUPABASE_AUTHZ_HEADER: <code>&quot;Bearer YOUR_SUPABASE_SECRET_ROLE_JWT&quot;</code></li>
</ul>
<p>Obviously, in both instances replace <code>YOUR_SUPABASE_SECRET_ROLE_JWT</code> with your service_role JWT from Supabase.</p>
<p>You are now ready to invoke your API gateway and see data flow through from your Supabase backend!</p>
<p>Click on the <strong>open in browser</strong> button shown below and you should see the JSON, flowing from Supabase in your browser 👏.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/open-in-browser.png" alt="open in browser"/></p>
<h2>Adding authentication</h2>
<p>At this point, that route is wide open to the world so we need to secure it. We&#x27;ll do this using API keys. You can follow this guide <a href="https://zuplo.com/docs/quickstarts/add-api-key-auth">Add API key Authentication</a>. Be sure to drag the API Key authentication policy to the very top of your <strong>Request</strong> pipeline. Come back here when you&#x27;re done.</p>
<p>Welcome back! You&#x27;ve now learned how to secure your API with API-Keys.</p>
<h2>Adding a Create route</h2>
<p>Next we&#x27;ll add a route that allows somebody to create a review. Add another route with the following settings</p>
<ul>
<li>method: <code>POST</code></li>
<li>path: <code>/reviews</code></li>
<li>summary: <code>Create a new review</code></li>
<li>version: <code>v1</code></li>
<li>CORS: <code>Anything goes</code></li>
</ul>
<p>And the request handler as follows:</p>
<ul>
<li>URL Rewrite: <code>https://YOUR_SUPABASE_URL.supabase.co/rest/v1/ski-reviews</code></li>
<li>Forward Search: <code>unchecked</code></li>
</ul>
<p>Expand the policies section and add the same policies (note you can reuse policies by picking from the existing policies at the top of the library)</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/existing-policies.png" alt="existing policies"/></p>
<ul>
<li>api-key-auth-inbound</li>
<li>clear-headers-inbound</li>
<li>set-headers-inbound</li>
</ul>
<p>Now your <strong>create</strong> route is secured and will automatically set the right headers before calling Supabase. That was easy.</p>
<p>You can test this out by using the <strong>API Test Console</strong> to invoke your new endpoint. Go to the <strong>API Test Console</strong> and create a new test called <code>create-review.json</code>.</p>
<ul>
<li>Method: <code>POST</code></li>
<li>Path: <code>/v1/reviews</code></li>
<li>Headers:<!-- -->
<ul>
<li><code>content-type</code>: <code>application/json</code></li>
<li><code>authorization</code>: <code>Bearer YOUR_ZUPLO_API_KEY</code></li>
</ul>
</li>
<li>Body:</li>
</ul>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>{</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  &quot;make&quot;: &quot;Rossignol&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  &quot;model&quot;: &quot;Soul HD7&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  &quot;rating&quot;: 5,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  &quot;year&quot;: 2019</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/test-console.png" alt="Test console"/></p>
<p>If you invoke your API by clicking <code>Test</code> you should see that you get a <strong>201 Created</strong> - congratulations!</p>
<h2>Add validation to your post</h2>
<p>To make your API more usable and more secure it is good practice to validate incoming requests. In this case we will add a JSON Schema document and use it to validate the incoming body to our POST.</p>
<p>Create a new schema document called <code>new-review.json</code>.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/new-schema.png" alt="new schema"/></p>
<p>This example fits the ski-reviews table we described above</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>{</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>  &quot;$id&quot;: &quot;http://example.com/example.json&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>  &quot;type&quot;: &quot;object&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>  &quot;default&quot;: {},</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>  &quot;title&quot;: &quot;Root Schema&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>  &quot;required&quot;: [&quot;make&quot;, &quot;model&quot;, &quot;rating&quot;, &quot;year&quot;],</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>  &quot;additionalProperties&quot;: false,</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>  &quot;properties&quot;: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>    &quot;make&quot;: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>      &quot;type&quot;: &quot;string&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>      &quot;default&quot;: &quot;&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>      &quot;title&quot;: &quot;The make Schema&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>      &quot;examples&quot;: [&quot;DPS&quot;]</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>    },</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>    &quot;model&quot;: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>      &quot;type&quot;: &quot;string&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>      &quot;default&quot;: &quot;&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>      &quot;title&quot;: &quot;The model Schema&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>      &quot;examples&quot;: [&quot;Pagoda&quot;]</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>    },</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>    &quot;rating&quot;: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>      &quot;type&quot;: &quot;integer&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>      &quot;default&quot;: 0,</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>      &quot;title&quot;: &quot;The rating Schema&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>      &quot;examples&quot;: [5]</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>    },</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>    &quot;year&quot;: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>      &quot;type&quot;: &quot;integer&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>      &quot;default&quot;: 0,</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>      &quot;title&quot;: &quot;The year Schema&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>      &quot;examples&quot;: [2018]</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>    }</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>  },</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>  &quot;examples&quot;: [</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>    {</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>      &quot;make&quot;: &quot;DPS&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>      &quot;model&quot;: &quot;Pagoda&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>      &quot;rating&quot;: 5,</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>      &quot;year&quot;: 2018,</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>      &quot;author&quot;: &quot;Josh&quot;</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>    }</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>  ]</span></div></div><div><span class="ch-code-line-number">_<!-- -->43</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<p>Now add a new policy to <strong>request</strong> pipeline for your <code>Create new review</code> route. Choose the <strong>JSON Body Validation</strong> policy and configure it to use your newly created JSON schema document:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>{</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  &quot;export&quot;: &quot;ValidateJsonSchemaInbound&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  &quot;module&quot;: &quot;$import(@zuplo/runtime)&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  &quot;options&quot;: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    &quot;validator&quot;: &quot;$import(./schemas/new-review.json)&quot;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<p>This policy can be dragged to the first position in your pipeline.</p>
<p>Now to test this is working, go back to your API test console and change the body of your <code>create-review.json</code> test to be invalid (add a new property for example). You should find that you get a <code>400 Bad Request</code> response.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/400-bad.png" alt="400 Bad Request"/></p>
<p>Finally, lean back and marvel at your beautiful Developer Portal that took almost zero effort to get this far, wow! Hopefully you already found the link for this when adding API key support :)</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/dev-portal.png" alt="Developer Portal"/></p>
<p>Zuplo can also be used to handle Supabase JWT tokens for any API, learn more at <a href="https://zuplo.com/blog/2022/11/15/api-authentication-with-supabase-jwt">API Authentication with Supabase JWT Tokens</a></p></div></div><div class="lg:col-span-3"><div class="sticky top-20 flex flex-col gap-4"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Details</h2><button class="w-full w-full"><div class="video-container overflow-hidden rounded hover:cursor-pointer"><div class=" absolute inset-0 z-10 text-white flex flex-col gap-3 items-center justify-center bg-alternative before:content[&#x27;&#x27;] before:absolute before:inset-0 before:bg-black before:opacity-30 before:-z-10 hover:before:opacity-50 before:transition-opacity "><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play w-5 h-5"><polygon points="6 3 20 12 6 21 6 3"></polygon></svg><p class="text-sm">Watch an introductory video</p></div><img alt="Video guide preview" loading="lazy" decoding="async" data-nimg="fill" class="absolute inset-0 object-cover blur-sm scale-105" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=3840&amp;q=75"/></div></button><div class="text-foreground divide-y"><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Developer</span><span class="text-foreground">Zuplo</span></div><div class="flex items-center justify-between py-2"><span class="text-lighter">Category</span><a class="text-brand hover:underline transition-colors" href="../integrations.html#api">API</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Website</span><a href="https://zuplo.com/" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors">zuplo.com</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Documentation</span><a href="https://zuplo.com/" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors"><span class="flex items-center space-x-1"><span>Learn</span><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></span></a></div></div><p class="text-foreground-light text-sm">Third-party integrations and docs are managed by Supabase partners.</p></div></div></div></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"partner":{"id":60,"slug":"zuplo","type":"technology","category":"API","developer":"Zuplo","title":"Zuplo","description":"API Management you actually want to use.","logo":"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/zuplo_logo.png","images":["https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/zuplo_og.avif"],"overview":"[Zuplo](https://zuplo.com/) is a fully-managed API gateway that offers the easiest way to securely and safely share your API. In this guide we look at how you can combine Zuplo and Supabase to create a public API with rate-limiting, a self-serve developer portal, and API-key authentication and much more.\n\n## Documentation\n\nThere is an [accompanying video for this article](https://www.youtube.com/watch?v=GJSkbxMnWxE).\n\n![zuplo layout](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/arch.png)\n\nIn this example we're going to work with a simple table that allows people to read and write entries to a Supabase table that contains some reviews of skis. Because this is an API for developers, we have to assume that they may be calling it from another backend service and can't login as a user using the standard Supabase method. In this scenario, API keys are often a better choice - see [Wait, you're not using API keys?](https://zuplo.com/blog/2022/05/03/you-should-be-using-api-keys/).\n\nWe'll allow people, with a valid API key, to read data from the ski results table and to create new records. Hopefully it's obvious that there are many ways that you can extend this example to add more behavior like roles based access, with custom policies, custom handlers and more.\n\n## Setting up Supabase\n\nIf you haven't already, create a new project in Supabase and create a table called ski-reviews with the following columns:\n\n- id (int8)\n- created_at (timestamptz)\n- make (text)\n- model (text)\n- year (int8)\n- rating (int2)\n- author (text)\n\nManually enter a couple of rows of data, so that we have something to read from the DB.\n\n## The `Get all` reviews route in Zuplo\n\nLogin to Zuplo at [portal.zuplo.com](https://portal.zuplo.com) and create a new project in Zuplo - I went with `supabase-ski-reviews`.\n\nSelect the **File** tab and choose **Routes**. Add your first route with the following settings:\n\n- method: `GET`\n- path: `/reviews`\n- summary: `Get all reviews`\n- version: `v1`\n- CORS: `Anything goes`\n\nAnd in the request handler section, paste the `READ ALL ROWS` URL of your Supabase backend (you can get to this in the **API docs** section of Supabase)\n\n- URL Rewrite: `https://YOUR_SUPABASE_URL.supabase.co/rest/v1/ski-reviews?select=*`\n- Forward Search: `unchecked`\n\nIn order to call the Supabase backend I need to add some authentication headers to the outgoing request.\n\nExpand the **Policies** section of your route. Click **Add policy** on the **Request** pipeline.\n\nWe don't want to forward any headers that the client sends us to Supabase, so find the **Clear Headers Policy** and add that to your inbound pipeline. Note, that we will allow the `content-type` header to flow through, so this should be your policy config.\n\n```json\n{\n  \"export\": \"ClearHeadersInboundPolicy\",\n  \"module\": \"$import(@zuplo/runtime)\",\n  \"options\": {\n    \"exclude\": [\"content-type\"]\n  }\n}\n```\n\nNext, we need to add the credentials to the outgoing request. We'll need to get the JWT token from supabase - you'll find it in **Settings** \u003e **API** as shown below:\n\n![secret_role jwt](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/secret-role.png)\n\nOnce you've got your service_role JWT, click **Add Policy** again on the **Request** pipeline and choose the **Add/Set Headers Policy** and configure it as follows:\n\n```json\n{\n  \"export\": \"SetHeadersInboundPolicy\",\n  \"module\": \"$import(@zuplo/runtime)\",\n  \"options\": {\n    \"headers\": [\n      {\n        \"name\": \"apikey\",\n        \"value\": \"$env(SUPABASE_API_KEY)\",\n        \"overwrite\": true\n      },\n      {\n        \"name\": \"authorization\",\n        \"value\": \"$env(SUPABASE_AUTHZ_HEADER)\",\n        \"overwrite\": true\n      }\n    ]\n  }\n}\n```\n\nSave your changes.\n\nNext, create two secret [environment variables](https://zuplo.com/docs/deployments/environment-variables) as follows:\n\n- SUPABASE_API_KEY: `\"YOUR_SUPABASE_SECRET_ROLE_JWT\"`\n- SUPABASE_AUTHZ_HEADER: `\"Bearer YOUR_SUPABASE_SECRET_ROLE_JWT\"`\n\nObviously, in both instances replace `YOUR_SUPABASE_SECRET_ROLE_JWT` with your service_role JWT from Supabase.\n\nYou are now ready to invoke your API gateway and see data flow through from your Supabase backend!\n\nClick on the **open in browser** button shown below and you should see the JSON, flowing from Supabase in your browser 👏.\n\n![open in browser](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/open-in-browser.png)\n\n## Adding authentication\n\nAt this point, that route is wide open to the world so we need to secure it. We'll do this using API keys. You can follow this guide [Add API key Authentication](https://zuplo.com/docs/quickstarts/add-api-key-auth). Be sure to drag the API Key authentication policy to the very top of your **Request** pipeline. Come back here when you're done.\n\nWelcome back! You've now learned how to secure your API with API-Keys.\n\n## Adding a Create route\n\nNext we'll add a route that allows somebody to create a review. Add another route with the following settings\n\n- method: `POST`\n- path: `/reviews`\n- summary: `Create a new review`\n- version: `v1`\n- CORS: `Anything goes`\n\nAnd the request handler as follows:\n\n- URL Rewrite: `https://YOUR_SUPABASE_URL.supabase.co/rest/v1/ski-reviews`\n- Forward Search: `unchecked`\n\nExpand the policies section and add the same policies (note you can reuse policies by picking from the existing policies at the top of the library)\n\n![existing policies](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/existing-policies.png)\n\n- api-key-auth-inbound\n- clear-headers-inbound\n- set-headers-inbound\n\nNow your **create** route is secured and will automatically set the right headers before calling Supabase. That was easy.\n\nYou can test this out by using the **API Test Console** to invoke your new endpoint. Go to the **API Test Console** and create a new test called `create-review.json`.\n\n- Method: `POST`\n- Path: `/v1/reviews`\n- Headers:\n  - `content-type`: `application/json`\n  - `authorization`: `Bearer YOUR_ZUPLO_API_KEY`\n- Body:\n\n```json\n{\n  \"make\": \"Rossignol\",\n  \"model\": \"Soul HD7\",\n  \"rating\": 5,\n  \"year\": 2019\n}\n```\n\n![Test console](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/test-console.png)\n\nIf you invoke your API by clicking `Test` you should see that you get a **201 Created** - congratulations!\n\n## Add validation to your post\n\nTo make your API more usable and more secure it is good practice to validate incoming requests. In this case we will add a JSON Schema document and use it to validate the incoming body to our POST.\n\nCreate a new schema document called `new-review.json`.\n\n![new schema](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/new-schema.png)\n\nThis example fits the ski-reviews table we described above\n\n```json\n{\n  \"$id\": \"http://example.com/example.json\",\n  \"type\": \"object\",\n  \"default\": {},\n  \"title\": \"Root Schema\",\n  \"required\": [\"make\", \"model\", \"rating\", \"year\"],\n  \"additionalProperties\": false,\n  \"properties\": {\n    \"make\": {\n      \"type\": \"string\",\n      \"default\": \"\",\n      \"title\": \"The make Schema\",\n      \"examples\": [\"DPS\"]\n    },\n    \"model\": {\n      \"type\": \"string\",\n      \"default\": \"\",\n      \"title\": \"The model Schema\",\n      \"examples\": [\"Pagoda\"]\n    },\n    \"rating\": {\n      \"type\": \"integer\",\n      \"default\": 0,\n      \"title\": \"The rating Schema\",\n      \"examples\": [5]\n    },\n    \"year\": {\n      \"type\": \"integer\",\n      \"default\": 0,\n      \"title\": \"The year Schema\",\n      \"examples\": [2018]\n    }\n  },\n  \"examples\": [\n    {\n      \"make\": \"DPS\",\n      \"model\": \"Pagoda\",\n      \"rating\": 5,\n      \"year\": 2018,\n      \"author\": \"Josh\"\n    }\n  ]\n}\n```\n\nNow add a new policy to **request** pipeline for your `Create new review` route. Choose the **JSON Body Validation** policy and configure it to use your newly created JSON schema document:\n\n```json\n{\n  \"export\": \"ValidateJsonSchemaInbound\",\n  \"module\": \"$import(@zuplo/runtime)\",\n  \"options\": {\n    \"validator\": \"$import(./schemas/new-review.json)\"\n  }\n}\n```\n\nThis policy can be dragged to the first position in your pipeline.\n\nNow to test this is working, go back to your API test console and change the body of your `create-review.json` test to be invalid (add a new property for example). You should find that you get a `400 Bad Request` response.\n\n![400 Bad Request](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/400-bad.png)\n\nFinally, lean back and marvel at your beautiful Developer Portal that took almost zero effort to get this far, wow! Hopefully you already found the link for this when adding API key support :)\n\n![Developer Portal](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/dev-portal.png)\n\nZuplo can also be used to handle Supabase JWT tokens for any API, learn more at [API Authentication with Supabase JWT Tokens](https://zuplo.com/blog/2022/11/15/api-authentication-with-supabase-jwt)\n","website":"https://zuplo.com/","docs":"https://zuplo.com/","contact":1,"approved":true,"created_at":"2023-02-14T07:01:00.538635+00:00","tsv":"'/)':12C '/blog/2022/05/03/you-should-be-using-api-keys/).':168C '/blog/2022/11/15/api-authentication-with-supabase-jwt)':1215C '/docs/deployments/environment-variables)':563C '/docs/quickstarts/add-api-key-auth).':682C '/example.json':985C '/rest/v1/ski-reviews':770C '/rest/v1/ski-reviews?select=*':367C '/reviews':325C,749C '/schemas/new-review.json':1093C '/storage/v1/object/public/images/integrations/zuplo/documentation/400-bad.png)':1152C '/storage/v1/object/public/images/integrations/zuplo/documentation/arch.png)':84C '/storage/v1/object/public/images/integrations/zuplo/documentation/dev-portal.png)':1190C '/storage/v1/object/public/images/integrations/zuplo/documentation/existing-policies.png)':804C '/storage/v1/object/public/images/integrations/zuplo/documentation/new-schema.png)':969C '/storage/v1/object/public/images/integrations/zuplo/documentation/open-in-browser.png)':644C '/storage/v1/object/public/images/integrations/zuplo/documentation/secret-role.png)':498C '/storage/v1/object/public/images/integrations/zuplo/documentation/test-console.png)':896C '/v1/reviews':869C '/watch?v=gjskbxmnwxe).':79C '0':1024C,1035C '201':912C '2018':1041C,1050C '2019':891C '400':1143C,1147C '5':889C,1030C,1048C 'access':213C 'accompani':72C 'actual':5B 'ad':645C,722C,1182C 'add':207C,314C,381C,397C,425C,466C,508C,676C,729C,739C,779C,915C,942C,1054C,1130C 'add/set':518C 'additionalproperti':997C 'allow':98C,171C,435C,733C 'almost':1165C 'alreadi':228C,1175C 'also':1193C 'anoth':133C,740C 'anyth':333C,758C 'api':2B,18C,31C,49C,62C,119C,151C,164C,176C,358C,489C,539C,567C,607C,669C,677C,688C,717C,720C,806C,845C,856C,879C,901C,923C,1116C,1183C,1203C,1207C,1216 'api-key':61C,719C 'api-key-auth-inbound':805C 'apikey':535C 'application/json':874C 'articl':76C 'assum':125C 'auth':808C 'authent':64C,383C,646C,679C,690C,1208C 'author':260C,544C,875C,1051C 'authz':548C,575C 'automat':826C 'back':701C,708C,1113C,1155C 'backend':134C,350C,377C,617C 'bad':1144C,1148C 'base':212C 'bearer':577C,876C 'beauti':1160C 'behavior':209C 'better':156C 'bodi':881C,954C,1070C,1122C 'browser':623C,638C,641C 'button':624C 'call':130C,239C,374C,832C,864C,963C 'case':939C 'chang':554C,1120C 'choic':157C 'choos':312C,516C,1067C 'clear':421C,811C 'clear-headers-inbound':810C 'clearheadersinboundpolici':453C 'click':396C,507C,618C,903C 'client':413C 'column':246C 'combin':41C 'come':700C 'config':450C 'configur':522C,1074C 'congratul':914C 'consol':847C,858C,893C,1118C 'contain':110C 'content':438C,460C,872C 'content-typ':437C,459C,871C 'cor':332C,757C 'coupl':265C 'creat':46C,188C,229C,236C,249C,294C,556C,724C,736C,751C,820C,860C,913C,958C,1063C,1080C 'create-review.json':865C,1125C 'credenti':468C 'custom':215C,217C 'data':180C,269C,611C 'db':279C 'default':988C,1003C,1013C,1023C,1034C 'describ':979C 'develop':58C,121C,1161C,1186C 'doc':359C 'document':68C,946C,962C,1083C 'done':706C 'dps':1009C,1044C 'drag':686C,1098C 'easi':836C 'easiest':23C 'effort':1167C 'endpoint':852C 'enter':263C 'entri':104C 'env':537C,546C 'environ':559C 'exampl':87C,205C,971C,1008C,1018C,1029C,1040C,1042C,1135C 'example.com':984C 'example.com/example.json':983C 'exclud':458C 'exist':792C,800C 'expand':389C,774C 'export':452C,527C,1085C 'extend':203C 'fals':998C 'far':1171C 'file':309C 'final':1153C 'find':419C,485C,1138C 'first':316C,1101C 'fit':972C 'flow':442C,612C,633C 'follow':245C,320C,525C,565C,673C,744C,765C 'forward':368C,408C,771C 'found':1176C 'fulli':16C 'fully-manag':15C 'gateway':19C,608C 'get':281C,323C,327C,353C,477C,910C,1141C,1169C 'go':90C,853C,1112C 'goe':334C,759C 'good':931C 'got':502C 'guid':34C,675C 'handl':1197C 'handler':218C,339C,763C 'haven':226C 'hd7':887C 'header':384C,410C,422C,440C,519C,533C,549C,576C,812C,816C,830C,870C 'hope':191C,1173C 'id':247C,982C 'import':455C,530C,1088C,1092C 'inbound':429C,809C,813C,817C 'incom':935C,953C 'instanc':586C 'int2':259C 'int8':248C,257C 'integ':1022C,1033C 'invalid':1129C 'invok':605C,849C,899C 'josh':1052C 'json':451C,526C,632C,882C,944C,981C,1069C,1081C,1084C 'jwt':479C,495C,506C,573C,582C,592C,597C,1199C,1211C 'key':63C,152C,165C,177C,540C,568C,670C,678C,689C,721C,807C,880C,1184C 'layout':81C 'lean':1154C 'learn':712C,1204C 'librari':799C 'like':210C 'limit':53C 'link':1178C 'll':170C,474C,484C,665C,728C 'login':139C,287C 'look':36C 'make':252C,883C,921C,993C,1000C,1006C,1043C 'manag':3B,17C 'mani':198C 'manual':262C 'marvel':1157C 'may':128C 'method':147C,322C,746C,866C 'model':254C,885C,994C,1010C,1016C,1045C 'modul':454C,529C,1087C 'much':66C 'name':534C,543C 'need':379C,464C,475C,660C 'new':189C,231C,296C,753C,851C,862C,960C,965C,1056C,1064C,1132C 'new-review.json':964C 'newli':1079C 'next':462C,555C,726C 'note':431C,783C 'object':987C 'obuldanrptloktxcffvn.supabase.co':83C,497C,643C,803C,895C,968C,1151C,1189C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/400-bad.png)':1150C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/arch.png)':82C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/dev-portal.png)':1188C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/existing-policies.png)':802C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/new-schema.png)':967C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/open-in-browser.png)':642C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/secret-role.png)':496C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/test-console.png)':894C 'obvious':194C,583C 'offer':21C 'often':154C 'open':621C,639C,654C 'option':457C,532C,1090C 'order':372C 'outgo':387C,471C 'overwrit':541C,550C 'pagoda':1019C,1046C 'past':341C 'path':324C,748C,868C 'peopl':99C,172C 'pick':789C 'pipelin':402C,430C,514C,699C,1060C,1105C 'point':649C 'polici':216C,391C,398C,423C,449C,509C,520C,691C,776C,782C,787C,793C,801C,1057C,1072C,1095C 'portal':59C,1162C,1187C 'portal.zuplo.com':291C,292C 'posit':1102C 'post':747C,867C,919C,957C 'practic':932C 'project':232C,297C 'properti':999C,1133C 'public':48C 'rate':52C,258C,888C,995C,1020C,1027C,1047C 'rate-limit':51C 're':89C,161C,705C 'read':101C,179C,276C,343C 'readi':603C 'record':190C 'replac':587C 'request':338C,388C,401C,472C,513C,698C,762C,936C,1059C,1145C,1149C 'requir':992C 'respons':1146C 'result':184C 'reus':786C 'review':112C,242C,283C,306C,329C,738C,754C,976C,1065C 'rewrit':364C,767C 'right':829C 'role':211C,494C,505C,572C,581C,591C,596C 'root':990C 'rossignol':884C 'rout':284C,313C,317C,395C,651C,725C,731C,741C,821C,1066C 'row':267C,345C 'safe':28C 'save':552C 'scenario':150C 'schema':945C,961C,966C,991C,1007C,1017C,1028C,1039C,1082C 'search':369C,772C 'secret':493C,558C,571C,580C,590C 'section':340C,360C,392C,777C 'secur':26C,662C,715C,823C,928C 'see':158C,610C,630C,907C 'select':307C 'self':56C 'self-serv':55C 'send':414C 'serv':57C 'servic':135C,504C,595C 'set':221C,321C,488C,745C,815C,827C 'set-headers-inbound':814C 'setheadersinboundpolici':528C 'share':29C 'shown':491C,625C 'simpl':95C 'ski':114C,183C,241C,305C,975C 'ski-review':240C,974C 'somebodi':734C 'someth':274C 'soul':886C 'standard':145C 'string':1002C,1012C 'summari':326C,750C 'supabas':44C,107C,146C,223C,234C,304C,349C,362C,376C,417C,482C,538C,547C,566C,570C,574C,579C,589C,599C,616C,635C,833C,1198C,1210C 'supabase-ski-review':303C 'support':1185C 'sure':684C 'tab':310C 'tabl':96C,108C,185C,238C,977C 'test':839C,846C,857C,863C,892C,904C,1108C,1117C,1126C 'text':253C,255C,261C 'timestamptz':251C 'titl':989C,1004C,1014C,1025C,1036C 'token':480C,1200C,1212C 'took':1164C 'top':695C,796C 'true':542C,551C 'two':557C 'type':439C,461C,873C,986C,1001C,1011C,1021C,1032C 'uncheck':370C,773C 'url':346C,363C,766C 'us':415C 'usabl':925C 'use':8B,143C,163C,668C,843C,948C,1077C,1195C 'user':142C 'v1':331C,756C 'valid':175C,916C,934C,951C,1071C,1091C 'validatejsonschemainbound':1086C 'valu':536C,545C 'variabl':560C 've':501C,710C 'version':330C,755C 'video':73C 'wait':159C 'want':6B,406C 'way':24C,199C 'welcom':707C 'went':301C 'wide':653C 'work':92C,1111C 'world':657C 'wow':1172C 'write':103C 'www.youtube.com':78C 'www.youtube.com/watch?v=gjskbxmnwxe).':77C 'year':256C,890C,996C,1031C,1038C,1049C 'your_supabase_url.supabase.co':366C,769C 'your_supabase_url.supabase.co/rest/v1/ski-reviews':768C 'your_supabase_url.supabase.co/rest/v1/ski-reviews?select=*':365C 'zero':1166C 'zuplo':1A,9C,42C,80C,286C,289C,299C,878C,1191C,1217 'zuplo.com':11C,167C,562C,681C,1214C 'zuplo.com/)':10C 'zuplo.com/blog/2022/05/03/you-should-be-using-api-keys/).':166C 'zuplo.com/blog/2022/11/15/api-authentication-with-supabase-jwt)':1213C 'zuplo.com/docs/deployments/environment-variables)':561C 'zuplo.com/docs/quickstarts/add-api-key-auth).':680C 'zuplo/runtime':456C,531C,1089C","video":"GJSkbxMnWxE","call_to_action_link":null,"featured":false},"overview":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nconst chCodeConfig = {\n  \"staticMediaQuery\": \"not screen, (max-width: 768px)\",\n  \"lineNumbers\": true,\n  \"showCopyButton\": true,\n  \"themeName\": \"supabase\"\n};\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    a: \"a\",\n    h2: \"h2\",\n    img: \"img\",\n    ul: \"ul\",\n    li: \"li\",\n    code: \"code\",\n    strong: \"strong\"\n  }, _provideComponents(), props.components), {CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://zuplo.com/\",\n        children: \"Zuplo\"\n      }), \" is a fully-managed API gateway that offers the easiest way to securely and safely share your API. In this guide we look at how you can combine Zuplo and Supabase to create a public API with rate-limiting, a self-serve developer portal, and API-key authentication and much more.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Documentation\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"There is an \", _jsx(_components.a, {\n        href: \"https://www.youtube.com/watch?v=GJSkbxMnWxE\",\n        children: \"accompanying video for this article\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/arch.png\",\n        alt: \"zuplo layout\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In this example we're going to work with a simple table that allows people to read and write entries to a Supabase table that contains some reviews of skis. Because this is an API for developers, we have to assume that they may be calling it from another backend service and can't login as a user using the standard Supabase method. In this scenario, API keys are often a better choice - see \", _jsx(_components.a, {\n        href: \"https://zuplo.com/blog/2022/05/03/you-should-be-using-api-keys/\",\n        children: \"Wait, you're not using API keys?\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We'll allow people, with a valid API key, to read data from the ski results table and to create new records. Hopefully it's obvious that there are many ways that you can extend this example to add more behavior like roles based access, with custom policies, custom handlers and more.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Setting up Supabase\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"If you haven't already, create a new project in Supabase and create a table called ski-reviews with the following columns:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"id (int8)\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"created_at (timestamptz)\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"make (text)\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"model (text)\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"year (int8)\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"rating (int2)\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"author (text)\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Manually enter a couple of rows of data, so that we have something to read from the DB.\"\n    }), \"\\n\", _jsxs(_components.h2, {\n      children: [\"The \", _jsx(_components.code, {\n        children: \"Get all\"\n      }), \" reviews route in Zuplo\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Login to Zuplo at \", _jsx(_components.a, {\n        href: \"https://portal.zuplo.com\",\n        children: \"portal.zuplo.com\"\n      }), \" and create a new project in Zuplo - I went with \", _jsx(_components.code, {\n        children: \"supabase-ski-reviews\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Select the \", _jsx(_components.strong, {\n        children: \"File\"\n      }), \" tab and choose \", _jsx(_components.strong, {\n        children: \"Routes\"\n      }), \". Add your first route with the following settings:\"]\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"method: \", _jsx(_components.code, {\n          children: \"GET\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"path: \", _jsx(_components.code, {\n          children: \"/reviews\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"summary: \", _jsx(_components.code, {\n          children: \"Get all reviews\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"version: \", _jsx(_components.code, {\n          children: \"v1\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"CORS: \", _jsx(_components.code, {\n          children: \"Anything goes\"\n        })]\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"And in the request handler section, paste the \", _jsx(_components.code, {\n        children: \"READ ALL ROWS\"\n      }), \" URL of your Supabase backend (you can get to this in the \", _jsx(_components.strong, {\n        children: \"API docs\"\n      }), \" section of Supabase)\"]\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"URL Rewrite: \", _jsx(_components.code, {\n          children: \"https://YOUR_SUPABASE_URL.supabase.co/rest/v1/ski-reviews?select=*\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Forward Search: \", _jsx(_components.code, {\n          children: \"unchecked\"\n        })]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In order to call the Supabase backend I need to add some authentication headers to the outgoing request.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Expand the \", _jsx(_components.strong, {\n        children: \"Policies\"\n      }), \" section of your route. Click \", _jsx(_components.strong, {\n        children: \"Add policy\"\n      }), \" on the \", _jsx(_components.strong, {\n        children: \"Request\"\n      }), \" pipeline.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We don't want to forward any headers that the client sends us to Supabase, so find the \", _jsx(_components.strong, {\n        children: \"Clear Headers Policy\"\n      }), \" and add that to your inbound pipeline. Note, that we will allow the \", _jsx(_components.code, {\n        children: \"content-type\"\n      }), \" header to flow through, so this should be your policy config.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"export\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"ClearHeadersInboundPolicy\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"module\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"$import(@zuplo/runtime)\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"options\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \\\"exclude\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": [\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"content-type\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"]\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"json\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Next, we need to add the credentials to the outgoing request. We'll need to get the JWT token from supabase - you'll find it in \", _jsx(_components.strong, {\n        children: \"Settings\"\n      }), \" \u003e \", _jsx(_components.strong, {\n        children: \"API\"\n      }), \" as shown below:\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/secret-role.png\",\n        alt: \"secret_role jwt\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Once you've got your service_role JWT, click \", _jsx(_components.strong, {\n        children: \"Add Policy\"\n      }), \" again on the \", _jsx(_components.strong, {\n        children: \"Request\"\n      }), \" pipeline and choose the \", _jsx(_components.strong, {\n        children: \"Add/Set Headers Policy\"\n      }), \" and configure it as follows:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"export\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"SetHeadersInboundPolicy\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"module\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"$import(@zuplo/runtime)\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"options\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \\\"headers\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": [\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \\\"name\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"apikey\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \\\"value\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"$env(SUPABASE_API_KEY)\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \\\"overwrite\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"true\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \\\"name\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"authorization\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \\\"value\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"$env(SUPABASE_AUTHZ_HEADER)\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \\\"overwrite\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"true\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    ]\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"json\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Save your changes.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Next, create two secret \", _jsx(_components.a, {\n        href: \"https://zuplo.com/docs/deployments/environment-variables\",\n        children: \"environment variables\"\n      }), \" as follows:\"]\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"SUPABASE_API_KEY: \", _jsx(_components.code, {\n          children: \"\\\"YOUR_SUPABASE_SECRET_ROLE_JWT\\\"\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"SUPABASE_AUTHZ_HEADER: \", _jsx(_components.code, {\n          children: \"\\\"Bearer YOUR_SUPABASE_SECRET_ROLE_JWT\\\"\"\n        })]\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Obviously, in both instances replace \", _jsx(_components.code, {\n        children: \"YOUR_SUPABASE_SECRET_ROLE_JWT\"\n      }), \" with your service_role JWT from Supabase.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You are now ready to invoke your API gateway and see data flow through from your Supabase backend!\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Click on the \", _jsx(_components.strong, {\n        children: \"open in browser\"\n      }), \" button shown below and you should see the JSON, flowing from Supabase in your browser 👏.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/open-in-browser.png\",\n        alt: \"open in browser\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Adding authentication\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"At this point, that route is wide open to the world so we need to secure it. We'll do this using API keys. You can follow this guide \", _jsx(_components.a, {\n        href: \"https://zuplo.com/docs/quickstarts/add-api-key-auth\",\n        children: \"Add API key Authentication\"\n      }), \". Be sure to drag the API Key authentication policy to the very top of your \", _jsx(_components.strong, {\n        children: \"Request\"\n      }), \" pipeline. Come back here when you're done.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Welcome back! You've now learned how to secure your API with API-Keys.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Adding a Create route\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Next we'll add a route that allows somebody to create a review. Add another route with the following settings\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"method: \", _jsx(_components.code, {\n          children: \"POST\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"path: \", _jsx(_components.code, {\n          children: \"/reviews\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"summary: \", _jsx(_components.code, {\n          children: \"Create a new review\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"version: \", _jsx(_components.code, {\n          children: \"v1\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"CORS: \", _jsx(_components.code, {\n          children: \"Anything goes\"\n        })]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"And the request handler as follows:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"URL Rewrite: \", _jsx(_components.code, {\n          children: \"https://YOUR_SUPABASE_URL.supabase.co/rest/v1/ski-reviews\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Forward Search: \", _jsx(_components.code, {\n          children: \"unchecked\"\n        })]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Expand the policies section and add the same policies (note you can reuse policies by picking from the existing policies at the top of the library)\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/existing-policies.png\",\n        alt: \"existing policies\"\n      })\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"api-key-auth-inbound\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"clear-headers-inbound\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"set-headers-inbound\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Now your \", _jsx(_components.strong, {\n        children: \"create\"\n      }), \" route is secured and will automatically set the right headers before calling Supabase. That was easy.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You can test this out by using the \", _jsx(_components.strong, {\n        children: \"API Test Console\"\n      }), \" to invoke your new endpoint. Go to the \", _jsx(_components.strong, {\n        children: \"API Test Console\"\n      }), \" and create a new test called \", _jsx(_components.code, {\n        children: \"create-review.json\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Method: \", _jsx(_components.code, {\n          children: \"POST\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Path: \", _jsx(_components.code, {\n          children: \"/v1/reviews\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Headers:\", \"\\n\", _jsxs(_components.ul, {\n          children: [\"\\n\", _jsxs(_components.li, {\n            children: [_jsx(_components.code, {\n              children: \"content-type\"\n            }), \": \", _jsx(_components.code, {\n              children: \"application/json\"\n            })]\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [_jsx(_components.code, {\n              children: \"authorization\"\n            }), \": \", _jsx(_components.code, {\n              children: \"Bearer YOUR_ZUPLO_API_KEY\"\n            })]\n          }), \"\\n\"]\n        }), \"\\n\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Body:\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"make\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"Rossignol\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"model\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"Soul HD7\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"rating\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"5\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"year\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"2019\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"json\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/test-console.png\",\n        alt: \"Test console\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you invoke your API by clicking \", _jsx(_components.code, {\n        children: \"Test\"\n      }), \" you should see that you get a \", _jsx(_components.strong, {\n        children: \"201 Created\"\n      }), \" - congratulations!\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Add validation to your post\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To make your API more usable and more secure it is good practice to validate incoming requests. In this case we will add a JSON Schema document and use it to validate the incoming body to our POST.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Create a new schema document called \", _jsx(_components.code, {\n        children: \"new-review.json\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/new-schema.png\",\n        alt: \"new schema\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This example fits the ski-reviews table we described above\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"$id\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"http://example.com/example.json\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"type\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"object\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"default\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": {},\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"title\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"Root Schema\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"required\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": [\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"make\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"model\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"rating\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"year\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"],\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"additionalProperties\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"false\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"properties\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \\\"make\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"type\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"string\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"default\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"title\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"The make Schema\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"examples\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": [\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"DPS\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"]\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \\\"model\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"type\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"string\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"default\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"title\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"The model Schema\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"examples\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": [\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"Pagoda\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"]\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \\\"rating\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"type\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"integer\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"default\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"0\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"title\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"The rating Schema\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"examples\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": [\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"5\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"]\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \\\"year\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"type\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"integer\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"default\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"0\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"title\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"The year Schema\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"examples\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": [\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"2018\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"]\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"examples\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": [\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"make\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"DPS\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"model\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"Pagoda\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"rating\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"5\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"year\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"2018\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \\\"author\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"Josh\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  ]\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"json\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Now add a new policy to \", _jsx(_components.strong, {\n        children: \"request\"\n      }), \" pipeline for your \", _jsx(_components.code, {\n        children: \"Create new review\"\n      }), \" route. Choose the \", _jsx(_components.strong, {\n        children: \"JSON Body Validation\"\n      }), \" policy and configure it to use your newly created JSON schema document:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"export\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"ValidateJsonSchemaInbound\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"module\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"$import(@zuplo/runtime)\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"options\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \\\"validator\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"$import(./schemas/new-review.json)\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"json\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This policy can be dragged to the first position in your pipeline.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Now to test this is working, go back to your API test console and change the body of your \", _jsx(_components.code, {\n        children: \"create-review.json\"\n      }), \" test to be invalid (add a new property for example). You should find that you get a \", _jsx(_components.code, {\n        children: \"400 Bad Request\"\n      }), \" response.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/400-bad.png\",\n        alt: \"400 Bad Request\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Finally, lean back and marvel at your beautiful Developer Portal that took almost zero effort to get this far, wow! Hopefully you already found the link for this when adding API key support :)\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/zuplo/documentation/dev-portal.png\",\n        alt: \"Developer Portal\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Zuplo can also be used to handle Supabase JWT tokens for any API, learn more at \", _jsx(_components.a, {\n        href: \"https://zuplo.com/blog/2022/11/15/api-authentication-with-supabase-jwt\",\n        children: \"API Authentication with Supabase JWT Tokens\"\n      })]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  chCodeConfig,\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{}}},"__N_SSG":true},"page":"/partners/integrations/[slug]","query":{"slug":"zuplo"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"isExperimentalCompile":false,"gsp":true,"scriptLoader":[]}</script></body></html>