<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../../rss.xml" data-next-head=""/><link rel="manifest" href="../../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:type" content="website" data-next-head=""/><meta property="og:image:alt" content="Supabase Og Image" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Estuary | Works With Supabase</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Analytics for your Supabase data. Seamlessly replicate data from Supabase to your warehouse with low latency." data-next-head=""/><meta property="og:title" content="Estuary | Works With Supabase" data-next-head=""/><meta property="og:description" content="Analytics for your Supabase data. Seamlessly replicate data from Supabase to your warehouse with low latency." data-next-head=""/><meta property="og:url" content="https://supabase.com/partners/integrations/estuary" data-next-head=""/><meta property="og:image" content="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/estuary/estuary_updated.png" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2263-07e8ebb96cc2b014.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8658-639e1bb66549b52b.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/partners/integrations/%5Bslug%5D-cff8a94339d02c6e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqela6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R1eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R1eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R2eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R2eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R3eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R3eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav></div><main class="relative min-h-screen"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20"><div class="col-span-12 mx-auto mb-2 max-w-5xl space-y-10 lg:col-span-2"><a class="text-foreground hover:text-foreground-lighter flex cursor-pointer items-center transition-colors" href="../integrations.html"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left"><path d="m15 18-6-6 6-6"></path></svg>Back</a><div class="flex items-center space-x-4"><img alt="Estuary" loading="lazy" width="56" height="56" decoding="async" data-nimg="1" class="bg-surface-200 flex-shrink-f0 h-14 w-14 rounded-full" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Festuary%2Festuary_logo.jpeg&amp;w=64&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Festuary%2Festuary_logo.jpeg&amp;w=128&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Festuary%2Festuary_logo.jpeg&amp;w=128&amp;q=75"/><h1 class="h1" style="margin-bottom:0">Estuary</h1></div><div class="bg-gradient-to-t from-background-alternative to-background border-b p-6 [&amp;_.swiper-container]:overflow-visible" style="margin-left:calc(50% - 50vw);margin-right:calc(50% - 50vw)"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0 !px-3 lg:!px-12 xl:!p-0 mx-auto max-w-5xl"><div class="swiper"><div class="swiper-wrapper"><div class="swiper-slide"><div class="relative block overflow-hidden rounded-md"><img alt="Estuary" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto;background-size:cover;background-position:50% 50%;background-repeat:no-repeat;background-image:url(&quot;data:image/svg+xml;charset=utf-8,%3Csvg xmlns=&#x27;http://www.w3.org/2000/svg&#x27; viewBox=&#x27;0 0 1460 960&#x27;%3E%3Cfilter id=&#x27;b&#x27; color-interpolation-filters=&#x27;sRGB&#x27;%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3CfeColorMatrix values=&#x27;1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1&#x27; result=&#x27;s&#x27;/%3E%3CfeFlood x=&#x27;0&#x27; y=&#x27;0&#x27; width=&#x27;100%25&#x27; height=&#x27;100%25&#x27;/%3E%3CfeComposite operator=&#x27;out&#x27; in=&#x27;s&#x27;/%3E%3CfeComposite in2=&#x27;SourceGraphic&#x27;/%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3C/filter%3E%3Cimage width=&#x27;100%25&#x27; height=&#x27;100%25&#x27; x=&#x27;0&#x27; y=&#x27;0&#x27; preserveAspectRatio=&#x27;none&#x27; style=&#x27;filter: url(%23b);&#x27; href=&#x27;/images/blur.png&#x27;/%3E%3C/svg%3E&quot;)" sizes="100vw" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Festuary%2Festuary_updated.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Festuary%2Festuary_updated.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Festuary%2Festuary_updated.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Festuary%2Festuary_updated.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Festuary%2Festuary_updated.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Festuary%2Festuary_updated.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Festuary%2Festuary_updated.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Festuary%2Festuary_updated.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Festuary%2Festuary_updated.png&amp;w=3840&amp;q=75"/></div></div></div></div></div></div><div class="grid gap-y-12 lg:grid-cols-8 lg:space-x-12"><div class="lg:col-span-5 overflow-hidden"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Overview</h2><div class="prose"><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p>Analytics for your Supabase data. Estuary helps seamlessly migrate your data from Firestore to a Supabase Postgres database.</p>
<div style="position:relative;padding-bottom:57.**************%;height:0"><iframe src="https://www.loom.com/embed/a8eb6fa3bece4f89835c778011637a6c" frameborder="0" style="position:absolute;top:0;left:0;width:100%;height:100%"></iframe></div>
<p>Estuary is a real-time data pipeline platform, enabling seamless capture from sources, historical backfill and real-time synchronization between sources and destinations. This makes it simple to continuously extract data from Supabase for analytics or creating data products.</p>
<h2>Benefits</h2>
<p>As a real-time platform, Estuary continuously extracts data from sources like Supabase using CDC.  This has a few main benefits:</p>
<ul>
<li>Access real-time analytics in your warehouse or destination of choice</li>
<li>Least impact on your Supabase DB</li>
<li>The ability to push to any destination that either Estuary or Kafka Connect and push to</li>
</ul>
<h2>Overview</h2>
<p>Details on how to use Estuary&#x27;s Supabase specific connector can be found <a href="https://docs.estuary.dev/reference/Connectors/capture-connectors/PostgreSQL/Supabase/">here</a>.  Setting up Estuary is straightforward and has a few main requirements:</p>
<ol>
<li>Create a free account <a href="https://dashboard.estuary.dev/register">here</a>.  Note that Estuary provides lifetime free service for anyone whose monthly usage is less than 10 GB and 2 connectors.  A 30 day free trial is automatically applied for accounts that exceed that usage.</li>
<li>Capture data from your Supabse instance using the <a href="https://docs.estuary.dev/reference/Connectors/capture-connectors/PostgreSQL/Supabase/">relevant documentation</a>.  Note that you will need to create a dedicated IPV4 address to use as your hostname within Estuary.</li>
<li>Push that data to your destination of choice.  Example docs for Snowflake can be found <a href="https://docs.estuary.dev/reference/Connectors/materialization-connectors/Snowflake/">here</a>.</li>
</ol>
<h2>Detailed Documentation</h2>
<h3>Prerequisites</h3>
<p>You&#x27;ll need a Supabase PostgreSQL database setup with the following:</p>
<ul>
<li>A Supabase IPV4 address. This can be configured under &quot;Project Settings&quot; -&gt; &quot;Add ons&quot; within Supabase&#x27;s UI.</li>
<li><a href="https://www.postgresql.org/docs/current/runtime-config-wal.html">Logical replication enabled</a> - <code>wal_level=logical</code></li>
<li><a href="https://www.postgresql.org/docs/current/sql-createrole.html">User role</a> with <code>REPLICATION</code> attribute</li>
<li>A <a href="https://www.postgresql.org/docs/current/warm-standby.html#STREAMING-REPLICATION-SLOTS">replication slot</a>. This represents a &quot;cursor&quot; into the PostgreSQL write-ahead log from which change events can be read.<!-- -->
<ul>
<li>Optional; if none exist, one will be created by the connector.</li>
<li>If you wish to run multiple captures from the same database, each must have its own slot. You can create these slots yourself, or by specifying a name other than the default in the advanced <a href="https://docs.estuary.dev/reference/Connectors/capture-connectors/PostgreSQL/Supabase/#configuration">configuration</a>.</li>
</ul>
</li>
<li>A <a href="https://www.postgresql.org/docs/current/sql-createpublication.html">publication</a>. This represents the set of tables for which change events will be reported.<!-- -->
<ul>
<li>In more restricted setups, this must be created manually, but can be created automatically if the connector has suitable permissions.</li>
</ul>
</li>
<li>A watermarks table. The watermarks table is a small &quot;scratch space&quot; to which the connector occasionally writes a small amount of data to ensure accuracy when backfilling preexisting table contents.<!-- -->
<ul>
<li>In more restricted setups, this must be created manually, but can be created automatically if the connector has suitable permissions.</li>
</ul>
</li>
</ul>
<h4>Configuration Tip</h4>
<p>To configure this connector to capture data from databases hosted on your internal network, you must set up SSH tunneling. For more specific instructions on setup, see <a href="https://docs.estuary.dev/guides/connect-network/">configure connections with SSH tunneling</a>.</p>
<h3>Setup</h3>
<p>The simplest way to meet the above prerequisites is to change the WAL level and have the connector use a database superuser role.</p>
<p>For a more restricted setup, create a new user with just the required permissions as detailed in the following steps:</p>
<ol>
<li>
<p>Connect to your instance and create a new user and password:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>CREATE USER flow_capture WITH PASSWORD &#x27;secret&#x27; REPLICATION;</span></div></div><br/></code></div></div>
</li>
<li>
<p>Assign the appropriate role.</p>
<p>a.  If using PostgreSQL v14 or later:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>GRANT pg_read_all_data TO flow_capture;</span></div></div><br/></code></div></div>
<p>b.  If using an earlier version:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES to flow_capture;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>GRANT SELECT ON ALL TABLES IN SCHEMA public, &lt;other_schema&gt; TO flow_capture;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>GRANT SELECT ON ALL TABLES IN SCHEMA information_schema, pg_catalog TO flow_capture;</span></div></div><br/></code></div></div>
<p>where <code>&lt;other_schema&gt;</code> lists all schemas that will be captured from.</p>
<p>INFO</p>
<p>If an even more restricted set of permissions is desired, you can also grant SELECT on just the specific table(s) which should be captured from. The &#x27;information_schema&#x27; and &#x27;pg_catalog&#x27; access is required for stream auto-discovery, but not for capturing already configured streams.</p>
</li>
<li>
<p>Create the watermarks table, grant privileges, and create publication:</p>
</li>
</ol>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>CREATE TABLE IF NOT EXISTS public.flow_watermarks (slot TEXT PRIMARY KEY, watermark TEXT);</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>GRANT ALL PRIVILEGES ON TABLE public.flow_watermarks TO flow_capture;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>CREATE PUBLICATION flow_publication;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>ALTER PUBLICATION flow_publication SET (publish_via_partition_root = true);</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>ALTER PUBLICATION flow_publication ADD TABLE public.flow_watermarks, &lt;other_tables&gt;;</span></div></div><br/></code></div></div>
<p>where <code>&lt;other_tables&gt;</code> lists all tables that will be captured from. The <code>publish_via_partition_root</code> setting is recommended (because most users will want changes to a partitioned table to be captured under the name of the root table) but is not required.</p>
<ol start="4">
<li>Set WAL level to logical:</li>
</ol>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>ALTER SYSTEM SET wal_level = logical;</span></div></div><br/></code></div></div>
<ol start="5">
<li>Restart PostgreSQL to allow the WAL level change to take effect.</li>
</ol>
<h2>Resources</h2>
<p>For more information, visit the <a href="https://docs.estuary.dev/">Flow docs</a>. In particular:</p>
<ul>
<li><a href="https://docs.estuary.dev/guides/create-dataflow/">Guide to create a Data Flow</a></li>
<li><a href="https://docs.estuary.dev/reference/Connectors/capture-connectors/google-firestore/">Firestore capture connector</a></li>
<li><a href="https://docs.estuary.dev/reference/Connectors/materialization-connectors/PostgreSQL/">Postgres materializaiton connector</a></li>
</ul></div></div><div class="lg:col-span-3"><div class="sticky top-20 flex flex-col gap-4"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Details</h2><div class="text-foreground divide-y"><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Developer</span><span class="text-foreground">Estuary</span></div><div class="flex items-center justify-between py-2"><span class="text-lighter">Category</span><a class="text-brand hover:underline transition-colors" href="../integrations.html#devtools">DevTools</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Website</span><a href="https://www.estuary.dev/" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors">www.estuary.dev</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Documentation</span><a href="https://www.estuary.dev/" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors"><span class="flex items-center space-x-1"><span>Learn</span><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></span></a></div></div><p class="text-foreground-light text-sm">Third-party integrations and docs are managed by Supabase partners.</p></div></div></div></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"partner":{"id":47,"slug":"estuary","type":"technology","category":"DevTools","developer":"Estuary","title":"Estuary","description":"Analytics for your Supabase data. Seamlessly replicate data from Supabase to your warehouse with low latency.","logo":"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/estuary/estuary_logo.jpeg","images":["https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/estuary/estuary_updated.png"],"overview":"Analytics for your Supabase data. Estuary helps seamlessly migrate your data from Firestore to a Supabase Postgres database.\n\n\u003cdiv style={{position: 'relative', paddingBottom: '57.**************%', height: 0 }}\u003e\n  \u003ciframe\n    src=\"https://www.loom.com/embed/a8eb6fa3bece4f89835c778011637a6c\"\n    frameborder=\"0\"\n    allowfullscreen\n    style={{position: 'absolute', top: 0, left: 0, width: '100%', height: '100%'}}\n  \u003e\u003c/iframe\u003e\n\u003c/div\u003e\n\nEstuary is a real-time data pipeline platform, enabling seamless capture from sources, historical backfill and real-time synchronization between sources and destinations. This makes it simple to continuously extract data from Supabase for analytics or creating data products.\n\n## Benefits\n\nAs a real-time platform, Estuary continuously extracts data from sources like Supabase using CDC.  This has a few main benefits:\n\n- Access real-time analytics in your warehouse or destination of choice\n- Least impact on your Supabase DB\n- The ability to push to any destination that either Estuary or Kafka Connect and push to\n\n## Overview\n\nDetails on how to use Estuary's Supabase specific connector can be found [here](https://docs.estuary.dev/reference/Connectors/capture-connectors/PostgreSQL/Supabase/).  Setting up Estuary is straightforward and has a few main requirements:\n1. Create a free account [here](https://dashboard.estuary.dev/register).  Note that Estuary provides lifetime free service for anyone whose monthly usage is less than 10 GB and 2 connectors.  A 30 day free trial is automatically applied for accounts that exceed that usage.\n2. Capture data from your Supabse instance using the [relevant documentation](https://docs.estuary.dev/reference/Connectors/capture-connectors/PostgreSQL/Supabase/).  Note that you will need to create a dedicated IPV4 address to use as your hostname within Estuary.\n3. Push that data to your destination of choice.  Example docs for Snowflake can be found [here](https://docs.estuary.dev/reference/Connectors/materialization-connectors/Snowflake/).\n\n## Detailed Documentation\n\n### Prerequisites\n\nYou'll need a Supabase PostgreSQL database setup with the following:\n- A Supabase IPV4 address. This can be configured under \"Project Settings\" -\u003e \"Add ons\" within Supabase's UI.\n- [Logical replication enabled](https://www.postgresql.org/docs/current/runtime-config-wal.html) - `wal_level=logical`\n- [User role](https://www.postgresql.org/docs/current/sql-createrole.html) with `REPLICATION` attribute\n- A [replication slot](https://www.postgresql.org/docs/current/warm-standby.html#STREAMING-REPLICATION-SLOTS). This represents a \"cursor\" into the PostgreSQL write-ahead log from which change events can be read.\n    - Optional; if none exist, one will be created by the connector.\n    - If you wish to run multiple captures from the same database, each must have its own slot. You can create these slots yourself, or by specifying a name other than the default in the advanced [configuration](https://docs.estuary.dev/reference/Connectors/capture-connectors/PostgreSQL/Supabase/#configuration).\n- A [publication](https://www.postgresql.org/docs/current/sql-createpublication.html). This represents the set of tables for which change events will be reported.\n    - In more restricted setups, this must be created manually, but can be created automatically if the connector has suitable permissions.\n- A watermarks table. The watermarks table is a small \"scratch space\" to which the connector occasionally writes a small amount of data to ensure accuracy when backfilling preexisting table contents.\n    - In more restricted setups, this must be created manually, but can be created automatically if the connector has suitable permissions.\n\n#### Configuration Tip\n\nTo configure this connector to capture data from databases hosted on your internal network, you must set up SSH tunneling. For more specific instructions on setup, see [configure connections with SSH tunneling](https://docs.estuary.dev/guides/connect-network/).\n\n### Setup\n\nThe simplest way to meet the above prerequisites is to change the WAL level and have the connector use a database superuser role.\n\nFor a more restricted setup, create a new user with just the required permissions as detailed in the following steps:\n\n1.  Connect to your instance and create a new user and password:\n\n    ```\n    CREATE USER flow_capture WITH PASSWORD 'secret' REPLICATION;\n    ```\n\n2.  Assign the appropriate role.\n\n    a.  If using PostgreSQL v14 or later:\n\n    ```\n    GRANT pg_read_all_data TO flow_capture;\n    ```\n\n    b.  If using an earlier version:\n\n    ```\n    ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES to flow_capture;\n    GRANT SELECT ON ALL TABLES IN SCHEMA public, \u003cother_schema\u003e TO flow_capture;\n    GRANT SELECT ON ALL TABLES IN SCHEMA information_schema, pg_catalog TO flow_capture;\n    ```\n\n    where `\u003cother_schema\u003e` lists all schemas that will be captured from.\n\n    INFO\n\n    If an even more restricted set of permissions is desired, you can also grant SELECT on just the specific table(s) which should be captured from. The 'information_schema' and 'pg_catalog' access is required for stream auto-discovery, but not for capturing already configured streams.\n\n3.  Create the watermarks table, grant privileges, and create publication:\n\n```\nCREATE TABLE IF NOT EXISTS public.flow_watermarks (slot TEXT PRIMARY KEY, watermark TEXT);\nGRANT ALL PRIVILEGES ON TABLE public.flow_watermarks TO flow_capture;\nCREATE PUBLICATION flow_publication;\nALTER PUBLICATION flow_publication SET (publish_via_partition_root = true);\nALTER PUBLICATION flow_publication ADD TABLE public.flow_watermarks, \u003cother_tables\u003e;\n```\n\nwhere `\u003cother_tables\u003e` lists all tables that will be captured from. The `publish_via_partition_root` setting is recommended (because most users will want changes to a partitioned table to be captured under the name of the root table) but is not required.\n\n4.  Set WAL level to logical:\n\n```\nALTER SYSTEM SET wal_level = logical;\n```\n\n5.  Restart PostgreSQL to allow the WAL level change to take effect.\n\n## Resources\n\nFor more information, visit the [Flow docs](https://docs.estuary.dev/). In particular:\n\n- [Guide to create a Data Flow](https://docs.estuary.dev/guides/create-dataflow/)\n- [Firestore capture connector](https://docs.estuary.dev/reference/Connectors/capture-connectors/google-firestore/)\n- [Postgres materializaiton connector](https://docs.estuary.dev/reference/Connectors/materialization-connectors/PostgreSQL/)\n","website":"https://www.estuary.dev/","docs":"https://www.estuary.dev/","contact":137,"approved":true,"created_at":"2022-11-23T07:50:35+00:00","tsv":"'/).':847C '/docs/current/runtime-config-wal.html)':321C '/docs/current/sql-createpublication.html).':411C '/docs/current/sql-createrole.html)':329C '/docs/current/warm-standby.html#streaming-replication-slots).':338C '/embed/a8eb6fa3bece4f89835c778011637a6c':48C '/guides/connect-network/).':531C '/guides/create-dataflow/)':858C '/reference/connectors/capture-connectors/google-firestore/)':864C '/reference/connectors/capture-connectors/postgresql/supabase/#configuration).':406C '/reference/connectors/capture-connectors/postgresql/supabase/).':178C,246C '/reference/connectors/materialization-connectors/postgresql/)':870C '/reference/connectors/materialization-connectors/snowflake/).':284C '/register).':198C '0':43C,50C,56C,58C '1':190C,576C '10':214C '100':60C,62C '2':217C,233C,596C '3':265C,717C '30':220C '4':813C '5':825C '57.**************':41C 'abil':146C 'absolut':54C 'access':127C,702C 'account':194C,228C 'accuraci':469C 'add':310C,768C 'address':257C,302C 'advanc':402C 'ahead':348C 'allow':829C 'allowfullscreen':51C 'alreadi':714C 'also':682C 'alter':622C,754C,764C,819C 'amount':464C 'analyt':2B,18C,99C,131C 'anyon':207C 'appli':226C 'appropri':599C 'assign':597C 'attribut':332C 'auto':708C 'auto-discoveri':707C 'automat':225C,438C,488C 'b':616C 'backfil':78C,471C 'benefit':104C,126C 'captur':74C,234C,374C,502C,591C,615C,634C,645C,659C,667C,694C,713C,749C,779C,801C,860C 'catalog':656C,701C 'cdc':120C 'chang':352C,420C,543C,794C,833C 'choic':138C,273C 'configur':306C,403C,495C,498C,524C,715C 'connect':157C,525C,577C 'connector':171C,218C,367C,441C,459C,491C,500C,550C,861C,867C 'content':474C 'continu':93C,112C 'creat':101C,191C,253C,364C,387C,432C,437C,482C,487C,561C,582C,588C,718C,725C,727C,750C,852C 'cursor':342C 'dashboard.estuary.dev':197C 'dashboard.estuary.dev/register).':196C 'data':6B,9B,22C,28C,69C,95C,102C,114C,235C,268C,466C,503C,612C,854C 'databas':35C,294C,378C,505C,553C 'day':221C 'db':144C 'dedic':255C 'default':399C,623C 'desir':679C 'destin':87C,136C,151C,271C 'detail':162C,285C,571C 'devtool':871 'discoveri':709C 'div':36C 'doc':275C,844C 'docs.estuary.dev':177C,245C,283C,405C,530C,846C,857C,863C,869C 'docs.estuary.dev/).':845C 'docs.estuary.dev/guides/connect-network/).':529C 'docs.estuary.dev/guides/create-dataflow/)':856C 'docs.estuary.dev/reference/connectors/capture-connectors/google-firestore/)':862C 'docs.estuary.dev/reference/connectors/capture-connectors/postgresql/supabase/#configuration).':404C 'docs.estuary.dev/reference/connectors/capture-connectors/postgresql/supabase/).':176C,244C 'docs.estuary.dev/reference/connectors/materialization-connectors/postgresql/)':868C 'docs.estuary.dev/reference/connectors/materialization-connectors/snowflake/).':282C 'document':243C,286C 'earlier':620C 'effect':836C 'either':153C 'enabl':72C,318C 'ensur':468C 'estuari':1A,23C,63C,111C,154C,167C,181C,201C,264C,872 'even':672C 'event':353C,421C 'exampl':274C 'exceed':230C 'exist':360C,731C 'extract':94C,113C 'firestor':30C,859C 'flow':590C,614C,633C,644C,658C,748C,752C,756C,766C,843C,855C 'follow':298C,574C 'found':174C,280C 'framebord':49C 'free':193C,204C,222C 'gb':215C 'grant':608C,628C,635C,646C,683C,722C,740C 'guid':850C 'height':42C,61C 'help':24C 'histor':77C 'host':506C 'hostnam':262C 'ifram':44C 'impact':140C 'info':669C 'inform':653C,697C,840C 'instanc':239C,580C 'instruct':520C 'intern':509C 'ipv4':256C,301C 'kafka':156C 'key':737C 'latenc':17B 'later':607C 'least':139C 'left':57C 'less':212C 'level':323C,546C,816C,823C,832C 'lifetim':203C 'like':117C 'list':661C,773C 'll':289C 'log':349C 'logic':316C,324C,818C,824C 'low':16B 'main':125C,188C 'make':89C 'manual':433C,483C 'materializaiton':866C 'meet':537C 'migrat':26C 'month':209C 'multipl':373C 'must':380C,430C,480C,512C 'name':395C,804C 'need':251C,290C 'network':510C 'new':563C,584C 'none':359C 'note':199C,247C 'occasion':460C 'on':311C 'one':361C 'option':357C 'overview':161C 'paddingbottom':40C 'particular':849C 'partit':761C,784C,797C 'password':587C,593C 'permiss':444C,494C,569C,677C 'pg':609C,655C,700C 'pipelin':70C 'platform':71C,110C 'posit':38C,53C 'postgr':34C,865C 'postgresql':293C,345C,604C,827C 'preexist':472C 'prerequisit':287C,540C 'primari':736C 'privileg':624C,723C,742C 'product':103C 'project':308C 'provid':202C 'public':408C,627C,642C,726C,751C,753C,755C,757C,765C,767C 'public.flow':732C,745C,770C 'publish':759C,782C 'push':148C,159C,266C 'read':356C,610C 'real':67C,81C,108C,129C 'real-tim':66C,80C,107C,128C 'recommend':788C 'relat':39C 'relev':242C 'replic':8B,317C,331C,334C,595C 'report':424C 'repres':340C,413C 'requir':189C,568C,704C,812C 'resourc':837C 'restart':826C 'restrict':427C,477C,559C,674C 'role':326C,555C,600C 'root':762C,785C,807C 'run':372C 'schema':626C,641C,652C,654C,663C,698C 'scratch':454C 'seamless':7B,25C,73C 'secret':594C 'see':523C 'select':629C,636C,647C,684C 'servic':205C 'set':179C,309C,415C,513C,675C,758C,786C,814C,821C 'setup':295C,428C,478C,522C,532C,560C 'simpl':91C 'simplest':534C 'slot':335C,384C,389C,734C 'small':453C,463C 'snowflak':277C 'sourc':76C,85C,116C 'space':455C 'specif':170C,519C,688C 'specifi':393C 'src':45C 'ssh':515C,527C 'step':575C 'straightforward':183C 'stream':706C,716C 'style':37C,52C 'suitabl':443C,493C 'supabas':5B,11B,21C,33C,97C,118C,143C,169C,292C,300C,313C 'supabs':238C 'superus':554C 'synchron':83C 'system':820C 'tabl':417C,447C,450C,473C,631C,639C,650C,689C,721C,728C,744C,769C,775C,798C,808C 'take':835C 'text':735C,739C 'time':68C,82C,109C,130C 'tip':496C 'top':55C 'trial':223C 'true':763C 'tunnel':516C,528C 'ui':315C 'usag':210C,232C 'use':119C,166C,240C,259C,551C,603C,618C 'user':325C,564C,585C,589C,791C 'v14':605C 'version':621C 'via':760C,783C 'visit':841C 'wal':322C,545C,815C,822C,831C 'want':793C 'warehous':14B,134C 'watermark':446C,449C,720C,733C,738C,746C,771C 'way':535C 'whose':208C 'width':59C 'wish':370C 'within':263C,312C 'write':347C,461C 'write-ahead':346C 'www.loom.com':47C 'www.loom.com/embed/a8eb6fa3bece4f89835c778011637a6c':46C 'www.postgresql.org':320C,328C,337C,410C 'www.postgresql.org/docs/current/runtime-config-wal.html)':319C 'www.postgresql.org/docs/current/sql-createpublication.html).':409C 'www.postgresql.org/docs/current/sql-createrole.html)':327C 'www.postgresql.org/docs/current/warm-standby.html#streaming-replication-slots).':336C","video":null,"call_to_action_link":null,"featured":false},"overview":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nconst chCodeConfig = {\n  \"staticMediaQuery\": \"not screen, (max-width: 768px)\",\n  \"lineNumbers\": true,\n  \"showCopyButton\": true,\n  \"themeName\": \"supabase\"\n};\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    h2: \"h2\",\n    ul: \"ul\",\n    li: \"li\",\n    a: \"a\",\n    ol: \"ol\",\n    h3: \"h3\",\n    code: \"code\",\n    h4: \"h4\"\n  }, _provideComponents(), props.components), {CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Analytics for your Supabase data. Estuary helps seamlessly migrate your data from Firestore to a Supabase Postgres database.\"\n    }), \"\\n\", _jsx(\"div\", {\n      style: {\n        position: 'relative',\n        paddingBottom: '57.**************%',\n        height: 0\n      },\n      children: _jsx(\"iframe\", {\n        src: \"https://www.loom.com/embed/a8eb6fa3bece4f89835c778011637a6c\",\n        frameborder: \"0\",\n        allowfullscreen: true,\n        style: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%'\n        }\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Estuary is a real-time data pipeline platform, enabling seamless capture from sources, historical backfill and real-time synchronization between sources and destinations. This makes it simple to continuously extract data from Supabase for analytics or creating data products.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Benefits\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"As a real-time platform, Estuary continuously extracts data from sources like Supabase using CDC.  This has a few main benefits:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Access real-time analytics in your warehouse or destination of choice\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Least impact on your Supabase DB\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"The ability to push to any destination that either Estuary or Kafka Connect and push to\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Overview\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Details on how to use Estuary's Supabase specific connector can be found \", _jsx(_components.a, {\n        href: \"https://docs.estuary.dev/reference/Connectors/capture-connectors/PostgreSQL/Supabase/\",\n        children: \"here\"\n      }), \".  Setting up Estuary is straightforward and has a few main requirements:\"]\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Create a free account \", _jsx(_components.a, {\n          href: \"https://dashboard.estuary.dev/register\",\n          children: \"here\"\n        }), \".  Note that Estuary provides lifetime free service for anyone whose monthly usage is less than 10 GB and 2 connectors.  A 30 day free trial is automatically applied for accounts that exceed that usage.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Capture data from your Supabse instance using the \", _jsx(_components.a, {\n          href: \"https://docs.estuary.dev/reference/Connectors/capture-connectors/PostgreSQL/Supabase/\",\n          children: \"relevant documentation\"\n        }), \".  Note that you will need to create a dedicated IPV4 address to use as your hostname within Estuary.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Push that data to your destination of choice.  Example docs for Snowflake can be found \", _jsx(_components.a, {\n          href: \"https://docs.estuary.dev/reference/Connectors/materialization-connectors/Snowflake/\",\n          children: \"here\"\n        }), \".\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Detailed Documentation\"\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Prerequisites\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You'll need a Supabase PostgreSQL database setup with the following:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"A Supabase IPV4 address. This can be configured under \\\"Project Settings\\\" -\u003e \\\"Add ons\\\" within Supabase's UI.\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://www.postgresql.org/docs/current/runtime-config-wal.html\",\n          children: \"Logical replication enabled\"\n        }), \" - \", _jsx(_components.code, {\n          children: \"wal_level=logical\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://www.postgresql.org/docs/current/sql-createrole.html\",\n          children: \"User role\"\n        }), \" with \", _jsx(_components.code, {\n          children: \"REPLICATION\"\n        }), \" attribute\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"A \", _jsx(_components.a, {\n          href: \"https://www.postgresql.org/docs/current/warm-standby.html#STREAMING-REPLICATION-SLOTS\",\n          children: \"replication slot\"\n        }), \". This represents a \\\"cursor\\\" into the PostgreSQL write-ahead log from which change events can be read.\", \"\\n\", _jsxs(_components.ul, {\n          children: [\"\\n\", _jsx(_components.li, {\n            children: \"Optional; if none exist, one will be created by the connector.\"\n          }), \"\\n\", _jsxs(_components.li, {\n            children: [\"If you wish to run multiple captures from the same database, each must have its own slot. You can create these slots yourself, or by specifying a name other than the default in the advanced \", _jsx(_components.a, {\n              href: \"https://docs.estuary.dev/reference/Connectors/capture-connectors/PostgreSQL/Supabase/#configuration\",\n              children: \"configuration\"\n            }), \".\"]\n          }), \"\\n\"]\n        }), \"\\n\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"A \", _jsx(_components.a, {\n          href: \"https://www.postgresql.org/docs/current/sql-createpublication.html\",\n          children: \"publication\"\n        }), \". This represents the set of tables for which change events will be reported.\", \"\\n\", _jsxs(_components.ul, {\n          children: [\"\\n\", _jsx(_components.li, {\n            children: \"In more restricted setups, this must be created manually, but can be created automatically if the connector has suitable permissions.\"\n          }), \"\\n\"]\n        }), \"\\n\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"A watermarks table. The watermarks table is a small \\\"scratch space\\\" to which the connector occasionally writes a small amount of data to ensure accuracy when backfilling preexisting table contents.\", \"\\n\", _jsxs(_components.ul, {\n          children: [\"\\n\", _jsx(_components.li, {\n            children: \"In more restricted setups, this must be created manually, but can be created automatically if the connector has suitable permissions.\"\n          }), \"\\n\"]\n        }), \"\\n\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h4, {\n      children: \"Configuration Tip\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To configure this connector to capture data from databases hosted on your internal network, you must set up SSH tunneling. For more specific instructions on setup, see \", _jsx(_components.a, {\n        href: \"https://docs.estuary.dev/guides/connect-network/\",\n        children: \"configure connections with SSH tunneling\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Setup\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The simplest way to meet the above prerequisites is to change the WAL level and have the connector use a database superuser role.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"For a more restricted setup, create a new user with just the required permissions as detailed in the following steps:\"\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"\\n\", _jsx(_components.p, {\n          children: \"Connect to your instance and create a new user and password:\"\n        }), \"\\n\", _jsx(CH.Code, {\n          codeConfig: chCodeConfig,\n          northPanel: {\n            \"tabs\": [\"\"],\n            \"active\": \"\",\n            \"heightRatio\": 1\n          },\n          files: [{\n            \"name\": \"\",\n            \"focus\": \"\",\n            \"code\": {\n              \"lines\": [{\n                \"tokens\": [{\n                  \"content\": \"CREATE USER flow_capture WITH PASSWORD 'secret' REPLICATION;\",\n                  \"props\": {}\n                }]\n              }],\n              \"lang\": \"text\"\n            },\n            \"annotations\": []\n          }]\n        }), \"\\n\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"\\n\", _jsx(_components.p, {\n          children: \"Assign the appropriate role.\"\n        }), \"\\n\", _jsx(_components.p, {\n          children: \"a.  If using PostgreSQL v14 or later:\"\n        }), \"\\n\", _jsx(CH.Code, {\n          codeConfig: chCodeConfig,\n          northPanel: {\n            \"tabs\": [\"\"],\n            \"active\": \"\",\n            \"heightRatio\": 1\n          },\n          files: [{\n            \"name\": \"\",\n            \"focus\": \"\",\n            \"code\": {\n              \"lines\": [{\n                \"tokens\": [{\n                  \"content\": \"GRANT pg_read_all_data TO flow_capture;\",\n                  \"props\": {}\n                }]\n              }],\n              \"lang\": \"text\"\n            },\n            \"annotations\": []\n          }]\n        }), \"\\n\", _jsx(_components.p, {\n          children: \"b.  If using an earlier version:\"\n        }), \"\\n\", _jsx(CH.Code, {\n          codeConfig: chCodeConfig,\n          northPanel: {\n            \"tabs\": [\"\"],\n            \"active\": \"\",\n            \"heightRatio\": 1\n          },\n          files: [{\n            \"name\": \"\",\n            \"focus\": \"\",\n            \"code\": {\n              \"lines\": [{\n                \"tokens\": [{\n                  \"content\": \"ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES to flow_capture;\",\n                  \"props\": {}\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"GRANT SELECT ON ALL TABLES IN SCHEMA public, \u003cother_schema\u003e TO flow_capture;\",\n                  \"props\": {}\n                }]\n              }, {\n                \"tokens\": [{\n                  \"content\": \"GRANT SELECT ON ALL TABLES IN SCHEMA information_schema, pg_catalog TO flow_capture;\",\n                  \"props\": {}\n                }]\n              }],\n              \"lang\": \"text\"\n            },\n            \"annotations\": []\n          }]\n        }), \"\\n\", _jsxs(_components.p, {\n          children: [\"where \", _jsx(_components.code, {\n            children: \"\u003cother_schema\u003e\"\n          }), \" lists all schemas that will be captured from.\"]\n        }), \"\\n\", _jsx(_components.p, {\n          children: \"INFO\"\n        }), \"\\n\", _jsx(_components.p, {\n          children: \"If an even more restricted set of permissions is desired, you can also grant SELECT on just the specific table(s) which should be captured from. The 'information_schema' and 'pg_catalog' access is required for stream auto-discovery, but not for capturing already configured streams.\"\n        }), \"\\n\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"\\n\", _jsx(_components.p, {\n          children: \"Create the watermarks table, grant privileges, and create publication:\"\n        }), \"\\n\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"CREATE TABLE IF NOT EXISTS public.flow_watermarks (slot TEXT PRIMARY KEY, watermark TEXT);\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"GRANT ALL PRIVILEGES ON TABLE public.flow_watermarks TO flow_capture;\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"CREATE PUBLICATION flow_publication;\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"ALTER PUBLICATION flow_publication SET (publish_via_partition_root = true);\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"ALTER PUBLICATION flow_publication ADD TABLE public.flow_watermarks, \u003cother_tables\u003e;\",\n              \"props\": {}\n            }]\n          }],\n          \"lang\": \"text\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"where \", _jsx(_components.code, {\n        children: \"\u003cother_tables\u003e\"\n      }), \" lists all tables that will be captured from. The \", _jsx(_components.code, {\n        children: \"publish_via_partition_root\"\n      }), \" setting is recommended (because most users will want changes to a partitioned table to be captured under the name of the root table) but is not required.\"]\n    }), \"\\n\", _jsxs(_components.ol, {\n      start: \"4\",\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Set WAL level to logical:\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"ALTER SYSTEM SET wal_level = logical;\",\n              \"props\": {}\n            }]\n          }],\n          \"lang\": \"text\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.ol, {\n      start: \"5\",\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Restart PostgreSQL to allow the WAL level change to take effect.\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Resources\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"For more information, visit the \", _jsx(_components.a, {\n        href: \"https://docs.estuary.dev/\",\n        children: \"Flow docs\"\n      }), \". In particular:\"]\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://docs.estuary.dev/guides/create-dataflow/\",\n          children: \"Guide to create a Data Flow\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://docs.estuary.dev/reference/Connectors/capture-connectors/google-firestore/\",\n          children: \"Firestore capture connector\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://docs.estuary.dev/reference/Connectors/materialization-connectors/PostgreSQL/\",\n          children: \"Postgres materializaiton connector\"\n        })\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  chCodeConfig,\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{}}},"__N_SSG":true},"page":"/partners/integrations/[slug]","query":{"slug":"estuary"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"gsp":true,"scriptLoader":[]}</script></body></html>