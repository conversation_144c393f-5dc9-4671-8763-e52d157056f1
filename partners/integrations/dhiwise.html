<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../../rss.xml" data-next-head=""/><link rel="manifest" href="../../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:type" content="website" data-next-head=""/><meta property="og:image:alt" content="Supabase Og Image" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">DhiWise | Works With Supabase</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Visual programming platform to develop enterprise-grade applications in low-code and pro-code simultaneously." data-next-head=""/><meta property="og:title" content="DhiWise | Works With Supabase" data-next-head=""/><meta property="og:description" content="Visual programming platform to develop enterprise-grade applications in low-code and pro-code simultaneously." data-next-head=""/><meta property="og:url" content="https://supabase.com/partners/integrations/dhiwise" data-next-head=""/><meta property="og:image" content="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/dhiwise/dhiwise_og.jpeg?t=2022-05-30T15%3A33%3A34.530Z" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2263-07e8ebb96cc2b014.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8658-639e1bb66549b52b.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/partners/integrations/%5Bslug%5D-cff8a94339d02c6e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqela6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R1eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R1eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R2eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R2eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R3eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R3eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav></div><main class="relative min-h-screen"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20"><div class="col-span-12 mx-auto mb-2 max-w-5xl space-y-10 lg:col-span-2"><a class="text-foreground hover:text-foreground-lighter flex cursor-pointer items-center transition-colors" href="../integrations.html"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left"><path d="m15 18-6-6 6-6"></path></svg>Back</a><div class="flex items-center space-x-4"><img alt="DhiWise" loading="lazy" width="56" height="56" decoding="async" data-nimg="1" class="bg-surface-200 flex-shrink-f0 h-14 w-14 rounded-full" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdhiwise%2Fdhiwise_logo.jpeg%3Ft%3D2022-05-30T15%253A31%253A10.340Z&amp;w=64&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdhiwise%2Fdhiwise_logo.jpeg%3Ft%3D2022-05-30T15%253A31%253A10.340Z&amp;w=128&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdhiwise%2Fdhiwise_logo.jpeg%3Ft%3D2022-05-30T15%253A31%253A10.340Z&amp;w=128&amp;q=75"/><h1 class="h1" style="margin-bottom:0">DhiWise</h1></div><div class="bg-gradient-to-t from-background-alternative to-background border-b p-6 [&amp;_.swiper-container]:overflow-visible" style="margin-left:calc(50% - 50vw);margin-right:calc(50% - 50vw)"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0 !px-3 lg:!px-12 xl:!p-0 mx-auto max-w-5xl"><div class="swiper"><div class="swiper-wrapper"><div class="swiper-slide"><div class="relative block overflow-hidden rounded-md"><img alt="DhiWise" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto;background-size:cover;background-position:50% 50%;background-repeat:no-repeat;background-image:url(&quot;data:image/svg+xml;charset=utf-8,%3Csvg xmlns=&#x27;http://www.w3.org/2000/svg&#x27; viewBox=&#x27;0 0 1460 960&#x27;%3E%3Cfilter id=&#x27;b&#x27; color-interpolation-filters=&#x27;sRGB&#x27;%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3CfeColorMatrix values=&#x27;1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1&#x27; result=&#x27;s&#x27;/%3E%3CfeFlood x=&#x27;0&#x27; y=&#x27;0&#x27; width=&#x27;100%25&#x27; height=&#x27;100%25&#x27;/%3E%3CfeComposite operator=&#x27;out&#x27; in=&#x27;s&#x27;/%3E%3CfeComposite in2=&#x27;SourceGraphic&#x27;/%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3C/filter%3E%3Cimage width=&#x27;100%25&#x27; height=&#x27;100%25&#x27; x=&#x27;0&#x27; y=&#x27;0&#x27; preserveAspectRatio=&#x27;none&#x27; style=&#x27;filter: url(%23b);&#x27; href=&#x27;/images/blur.png&#x27;/%3E%3C/svg%3E&quot;)" sizes="100vw" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdhiwise%2Fdhiwise_og.jpeg%3Ft%3D2022-05-30T15%253A33%253A34.530Z&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdhiwise%2Fdhiwise_og.jpeg%3Ft%3D2022-05-30T15%253A33%253A34.530Z&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdhiwise%2Fdhiwise_og.jpeg%3Ft%3D2022-05-30T15%253A33%253A34.530Z&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdhiwise%2Fdhiwise_og.jpeg%3Ft%3D2022-05-30T15%253A33%253A34.530Z&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdhiwise%2Fdhiwise_og.jpeg%3Ft%3D2022-05-30T15%253A33%253A34.530Z&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdhiwise%2Fdhiwise_og.jpeg%3Ft%3D2022-05-30T15%253A33%253A34.530Z&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdhiwise%2Fdhiwise_og.jpeg%3Ft%3D2022-05-30T15%253A33%253A34.530Z&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdhiwise%2Fdhiwise_og.jpeg%3Ft%3D2022-05-30T15%253A33%253A34.530Z&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fdhiwise%2Fdhiwise_og.jpeg%3Ft%3D2022-05-30T15%253A33%253A34.530Z&amp;w=3840&amp;q=75"/></div></div></div></div></div></div><div class="grid gap-y-12 lg:grid-cols-8 lg:space-x-12"><div class="lg:col-span-5 overflow-hidden"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Overview</h2><div class="prose"><p>With the help of DhiWise, now you can easily integrate data from Supabase to DhiWise in 5 simple steps. Thereafter, you can create your application easily and effectively on DhiWise by mapping out all the essential data to their respective view components.</p>
<h2>Documentation</h2>
<p>This guide explains how to connect Supabase backend to DhiWise Flutter application quickly.</p>
<p><a href="https://www.dhiwise.com/">DhiWise</a> is a Developer tool to convert Figma designs into React and Flutter applications. It lets you quickly integrate Databases and APIs into your React and Flutter Apps.</p>
<p>If you don&#x27;t have a DhiWise account, create one <a href="https://app.dhiwise.com">here</a>.</p>
<p>DhiWise supports easy Supabase Integration in just five steps.</p>
<p>Let&#x27;s get started!</p>
<h2>Step 1: SignIn to Supabase</h2>
<p>Go to <a href="../../index.html">Supabase</a>, Click <code>Sign In</code>, and create a new account by authenticating with <strong>GitHub</strong>. If you already have an account, you will be logged in.</p>
<h2>Step 2: Create a new project in Supabase</h2>
<p>Click on <code>New project</code> from the Dashboard and select an organization. If you don&#x27;t have an organization, create one using <code>+ New organization.</code></p>
<ul>
<li>Give your Supabase project a <code>name.</code></li>
<li>Enter a secure <code>Database Password.</code></li>
<li>Choose the <code>region</code> where your app&#x27;s backend is hosted.</li>
<li>Click <code>Create new project.</code></li>
</ul>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/dhiwise/documentation/newProj.png" alt="New Project"/></p>
<h2>Step 3: Find the API key and URL</h2>
<p>Once your project is created, you can access the API Key and URL string, Or if you already have an account go to your <code>organization-&gt; app-&gt; settings-&gt; API</code>.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/dhiwise/documentation/authKeys.png" alt="auth keys"/></p>
<h2>Step 4: Integrations</h2>
<p>There are two ways you can integrate Supabase into your DhiWise Flutter applications.</p>
<h3>Authentication</h3>
<p>You can integrate <code>Supabase Email/Password SignUp</code> or <code>Supabase Email/Password SignIn</code> on your components.</p>
<ul>
<li>Open the screen of your flutter application</li>
<li>Go to the component on which you want to add authentication</li>
<li>on the <code>onClick</code> method - select <code>authentication</code></li>
<li>From the list, If you want SignUp - select <code>SignUp with Email/Password</code>; otherwise, select <code>SignIn with Email/Password</code> from Supabase Auth section</li>
</ul>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/dhiwise/documentation/auth.gif" alt="Auth"/></p>
<p>And that&#x27;s it. Supabase authentication will be added to the selected component.</p>
<p>After downloading the application source code,</p>
<ol>
<li>Add Supabase URL and Supabase public key inside <strong><em>lib/core/utils/initial_bindings</em></strong> file.</li>
<li>For additional details, refer <em><strong><a href="https://supabase.com/docs/guides/with-flutter">https://supabase.com/docs/guides/with-flutter</a></strong></em></li>
</ol>
<h3>Working with Data</h3>
<p>When you first integrate Supabase in your DhiWise Flutter application, You will be asked to add <a href="#%23step-3-find-the-api-key-and-url">Supabase auth key and URL</a>. When you add them, all the tables available in your Supabase project will be synced in DhiWise. You can integrate Select and Create queries on your Flutter screen for a particular table in DhiWise.</p>
<h3>Select records</h3>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/dhiwise/documentation/select.png" alt="Create"/></p>
<h4><strong>Step 1:</strong></h4>
<p>Select the screen from the screen list where you want to integrate Supabase.</p>
<h4><strong>Step 2:</strong></h4>
<p>Next, go to the view where you want to add Integration, and from the suggestion box for the <code>onClick</code> property, choose <code>Supabase integration,</code> which will take you to the Integration screen. Where you will be asked to <code>Enter function name.</code> Enter the name of your function and click <code>Submit.</code></p>
<h4><strong>Step 3:</strong></h4>
<p>After submitting the function name, you will be asked to select a type of Supabase integration. To retrieve data from Supabase, choose <code>select.</code></p>
<h4><strong>Step 4:</strong></h4>
<p>Next, select the table from which you want to fetch records from the listed Tables.</p>
<h4><strong>Step 5:</strong></h4>
<p>Select the type of integration</p>
<table><thead><tr><th>Type</th><th>Description</th></tr></thead><tbody><tr><td><strong>Single</strong></td><td>Used to fetch a single record from the database.</td></tr><tr><td><strong>Multiple</strong></td><td>Used to fetch multiple records from the database.</td></tr></tbody></table>
<div role="alert" class="relative w-full text-sm rounded-lg p-4 [&amp;&gt;svg~*]:pl-10 [&amp;&gt;svg+div]:translate-y-[-3px] [&amp;&gt;svg]:absolute [&amp;&gt;svg]:left-4 [&amp;&gt;svg]:top-4 [&amp;&gt;svg]:w-[23px] [&amp;&gt;svg]:h-[23px] [&amp;&gt;svg]:p-1 [&amp;&gt;svg]:flex [&amp;&gt;svg]:rounded text-foreground bg-alternative border [&amp;&gt;svg]:text-background [&amp;&gt;svg]:bg-foreground mb-2"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 21 20" class="w-6 h-6" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M0.625 9.8252C0.625 4.44043 4.99023 0.0751953 10.375 0.0751953C15.7598 0.0751953 20.125 4.44043 20.125 9.8252C20.125 15.21 15.7598 19.5752 10.375 19.5752C4.99023 19.5752 0.625 15.21 0.625 9.8252ZM9.3584 4.38135C9.45117 4.28857 9.55518 4.20996 9.66699 4.14648C9.88086 4.02539 10.1245 3.96045 10.375 3.96045C10.5845 3.96045 10.7896 4.00586 10.9766 4.09229C11.1294 4.1626 11.2705 4.26025 11.3916 4.38135C11.6611 4.65088 11.8125 5.0166 11.8125 5.39795C11.8125 5.5249 11.7959 5.6499 11.7637 5.77002C11.6987 6.01172 11.5718 6.23438 11.3916 6.41455C11.1221 6.68408 10.7563 6.83545 10.375 6.83545C9.99365 6.83545 9.62793 6.68408 9.3584 6.41455C9.08887 6.14502 8.9375 5.7793 8.9375 5.39795C8.9375 5.29492 8.94873 5.19287 8.97021 5.09375C9.02783 4.82568 9.16162 4.57812 9.3584 4.38135ZM10.375 15.6899C10.0933 15.6899 9.82275 15.5781 9.62354 15.3789C9.42432 15.1797 9.3125 14.9092 9.3125 14.6274V9.31494C9.3125 9.0332 9.42432 8.7627 9.62354 8.56348C9.82275 8.36426 10.0933 8.25244 10.375 8.25244C10.6567 8.25244 10.9272 8.36426 11.1265 8.56348C11.3257 8.7627 11.4375 9.0332 11.4375 9.31494V14.6274C11.4375 14.7944 11.3979 14.9575 11.3242 15.104C11.2739 15.2046 11.2075 15.2979 11.1265 15.3789C10.9272 15.5781 10.6567 15.6899 10.375 15.6899Z"></path></svg><div class="text mt [&amp;_p]:mb-1.5 [&amp;_p]:mt-0 mt-0.5 [&amp;_p:last-child]:mb-0"><p>For Multiple types, you need to set <code>data limit,</code> <code>order by, and </code>order.`</p></div></div>
<h4><strong>Step 6:</strong></h4>
<p>You will be redirected to the API Integration screen, where you can set request and response.</p>
<p>For request binding, the below types are supported. Also, Select the operator for comparison before moving forward.</p>
<table><thead><tr><th>Type</th><th>Description</th></tr></thead><tbody><tr><td><strong>View</strong></td><td>Select any component from your screen.</td></tr><tr><td><strong>Constant</strong></td><td>Select a constant you&#x27;ve created in your app.</td></tr><tr><td><strong>Get from preference</strong></td><td>Select the key you want to fetch from preference.</td></tr><tr><td><strong>Navigation argument</strong></td><td>Select data that&#x27;s been passed from one screen to another.</td></tr></tbody></table>
<p>For response binding, the below types are supported.</p>
<table><thead><tr><th>Type</th><th>Description</th></tr></thead><tbody><tr><td><strong>View</strong></td><td>Select any component from the screen.</td></tr><tr><td><strong>Save to preference</strong></td><td>Storing the data to preference.</td></tr></tbody></table>
<h4><strong>Select 7:</strong></h4>
<p><code>Handle action</code> - Select the action you wish to take once the Supabase call has either been accepted successfully or refused due to an error.</p>
<p>Available action for On success and On error are,</p>
<ol>
<li><a href="https://docs.dhiwise.com/docs/flutter/show-alert">Show Alert</a></li>
<li><a href="https://docs.dhiwise.com/docs/flutter/navigation">Navigation</a></li>
</ol>
<h4><strong>Step 8:</strong></h4>
<p>Finally, you have added Supabase to your application to fetch records on your screen!</p>
<div role="alert" class="relative w-full text-sm rounded-lg p-4 [&amp;&gt;svg~*]:pl-10 [&amp;&gt;svg+div]:translate-y-[-3px] [&amp;&gt;svg]:absolute [&amp;&gt;svg]:left-4 [&amp;&gt;svg]:top-4 [&amp;&gt;svg]:w-[23px] [&amp;&gt;svg]:h-[23px] [&amp;&gt;svg]:p-1 [&amp;&gt;svg]:flex [&amp;&gt;svg]:rounded text-foreground [&amp;&gt;svg]:text-background mb-2 [&amp;&gt;svg]:bg-foreground-muted bg-surface-200/25 border border-default"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 21 20" class="w-6 h-6" fill="currentColor"><path fill-rule="evenodd" clip-rule="evenodd" d="M0.625 9.8252C0.625 4.44043 4.99023 0.0751953 10.375 0.0751953C15.7598 0.0751953 20.125 4.44043 20.125 9.8252C20.125 15.21 15.7598 19.5752 10.375 19.5752C4.99023 19.5752 0.625 15.21 0.625 9.8252ZM9.3584 4.38135C9.45117 4.28857 9.55518 4.20996 9.66699 4.14648C9.88086 4.02539 10.1245 3.96045 10.375 3.96045C10.5845 3.96045 10.7896 4.00586 10.9766 4.09229C11.1294 4.1626 11.2705 4.26025 11.3916 4.38135C11.6611 4.65088 11.8125 5.0166 11.8125 5.39795C11.8125 5.5249 11.7959 5.6499 11.7637 5.77002C11.6987 6.01172 11.5718 6.23438 11.3916 6.41455C11.1221 6.68408 10.7563 6.83545 10.375 6.83545C9.99365 6.83545 9.62793 6.68408 9.3584 6.41455C9.08887 6.14502 8.9375 5.7793 8.9375 5.39795C8.9375 5.29492 8.94873 5.19287 8.97021 5.09375C9.02783 4.82568 9.16162 4.57812 9.3584 4.38135ZM10.375 15.6899C10.0933 15.6899 9.82275 15.5781 9.62354 15.3789C9.42432 15.1797 9.3125 14.9092 9.3125 14.6274V9.31494C9.3125 9.0332 9.42432 8.7627 9.62354 8.56348C9.82275 8.36426 10.0933 8.25244 10.375 8.25244C10.6567 8.25244 10.9272 8.36426 11.1265 8.56348C11.3257 8.7627 11.4375 9.0332 11.4375 9.31494V14.6274C11.4375 14.7944 11.3979 14.9575 11.3242 15.104C11.2739 15.2046 11.2075 15.2979 11.1265 15.3789C10.9272 15.5781 10.6567 15.6899 10.375 15.6899Z"></path></svg><h5 class="mb-1 text mt-0.5 flex gap-3 text-sm [&amp;_p]:mb-1.5 [&amp;_p]:mt-0">Example</h5><div class="text-sm [&amp;_p]:leading-relaxed text-foreground-light font-normal [&amp;_p]:mb-1.5 [&amp;_p]:mt-0"><p>Suppose you want to fetch records from Supabase and populate the item list on your screen. You can integrate Supabase as discussed above and bind the response with your list view.</p></div></div>
<h3> Create records </h3>
<h4><strong>Step 1:</strong></h4>
<p>Choose the screen you wish to integrate Supabase for from the list of screens.</p>
<h4><strong>Step 2:</strong></h4>
<p>Next, switch to the component you want to add Integration, and on the <code>onClick</code> property, choose <code>Supabase integration,</code> which will take you to its integration screen, where you will be asked to <strong>Enter function name</strong>, which will be used in generated code. Enter the name for it and click <code>Submit</code></p>
<h4><strong>Step 3:</strong></h4>
<p>After submitting the function name, you will be asked to select a type of Supabase integration. For example, to create a record in Supabase, choose <code>Create.</code></p>
<h4><strong>Step 4:</strong></h4>
<p>Next, select the table where you want to create a record from the listed Tables.</p>
<h4><strong>Step 5:</strong></h4>
<p>If you want to create a Single record, Select <strong>Select</strong>. Otherwise, <strong>Multiple</strong>.</p>
<h4><strong>Step 6:</strong></h4>
<p>Now, you will be redirected to the API Integration screen, where you can set request and response.</p>
<p>For request binding, the below types are supported.</p>
<table><thead><tr><th>Type</th><th>Description</th></tr></thead><tbody><tr><td><strong>View</strong></td><td>Select any component from the screen</td></tr><tr><td><strong>Constant</strong></td><td>Select a constant you&#x27;ve created in your app.</td></tr><tr><td><strong>Get from preference</strong></td><td>Select the key you want to fetch from preference.</td></tr><tr><td><strong>Navigation argument</strong></td><td>Select data that&#x27;s been passed from one screen to another.</td></tr></tbody></table>
<p>For response binding, the below types are supported.</p>
<table><thead><tr><th>Type</th><th>Description</th></tr></thead><tbody><tr><td><strong>View</strong></td><td>Select any component from the screen</td></tr><tr><td><strong>Save to preference</strong></td><td>Storing the data to preference.</td></tr></tbody></table>
<h4><strong>Select 7:</strong></h4>
<p><code>Handle action</code> - Select the action you wish to take once the Supabase call has either been accepted successfully or refused due to an error.</p>
<p>Available action for On success and On error are,</p>
<ol>
<li><a href="https://docs.dhiwise.com/docs/flutter/show-alert">Show Alert</a></li>
<li><a href="https://docs.dhiwise.com/docs/flutter/navigation">Navigation</a></li>
</ol>
<h4><strong>Step 9:</strong></h4>
<p>Finally, you have added Supabase to your application to create records from your screen data!</p>
<h2>Resources</h2>
<ul>
<li><a href="https://dhiwise.com">DhiWise Official Website</a></li>
<li><a href="https://docs.dhiwise.com">DhiWise Documentation</a></li>
</ul></div></div><div class="lg:col-span-3"><div class="sticky top-20 flex flex-col gap-4"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Details</h2><button class="w-full w-full"><div class="video-container overflow-hidden rounded hover:cursor-pointer"><div class=" absolute inset-0 z-10 text-white flex flex-col gap-3 items-center justify-center bg-alternative before:content[&#x27;&#x27;] before:absolute before:inset-0 before:bg-black before:opacity-30 before:-z-10 hover:before:opacity-50 before:transition-opacity "><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-play w-5 h-5"><polygon points="6 3 20 12 6 21 6 3"></polygon></svg><p class="text-sm">Watch an introductory video</p></div><img alt="Video guide preview" loading="lazy" decoding="async" data-nimg="fill" class="absolute inset-0 object-cover blur-sm scale-105" style="position:absolute;height:100%;width:100%;left:0;top:0;right:0;bottom:0;color:transparent" sizes="100%" srcSet="https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=16&amp;q=75 16w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=32&amp;q=75 32w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=48&amp;q=75 48w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=64&amp;q=75 64w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=96&amp;q=75 96w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=128&amp;q=75 128w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=256&amp;q=75 256w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=384&amp;q=75 384w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=%2Fimages%2Fblur.png&amp;w=3840&amp;q=75"/></div></button><div class="text-foreground divide-y"><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Developer</span><span class="text-foreground">DhiWise</span></div><div class="flex items-center justify-between py-2"><span class="text-lighter">Category</span><a class="text-brand hover:underline transition-colors" href="../integrations.html#devtools">DevTools</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Website</span><a href="https://www.dhiwise.com/" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors">www.dhiwise.com</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Documentation</span><a href="https://docs.dhiwise.com/docs/flutter/supabase-integration" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors"><span class="flex items-center space-x-1"><span>Learn</span><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></span></a></div></div><p class="text-foreground-light text-sm">Third-party integrations and docs are managed by Supabase partners.</p></div></div></div></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"partner":{"id":20,"slug":"dhiwise","type":"technology","category":"DevTools","developer":"DhiWise","title":"DhiWise","description":"Visual programming platform to develop enterprise-grade applications in low-code and pro-code simultaneously.","logo":"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/dhiwise/dhiwise_logo.jpeg?t=2022-05-30T15%3A31%3A10.340Z","images":["https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/dhiwise/dhiwise_og.jpeg?t=2022-05-30T15%3A33%3A34.530Z"],"overview":"With the help of DhiWise, now you can easily integrate data from Supabase to DhiWise in 5 simple steps. Thereafter, you can create your application easily and effectively on DhiWise by mapping out all the essential data to their respective view components.\n\n## Documentation\n\nThis guide explains how to connect Supabase backend to DhiWise Flutter application quickly.\n\n[DhiWise](https://www.dhiwise.com/) is a Developer tool to convert Figma designs into React and Flutter applications. It lets you quickly integrate Databases and APIs into your React and Flutter Apps.\n\nIf you don't have a DhiWise account, create one [here](https://app.dhiwise.com).\n\nDhiWise supports easy Supabase Integration in just five steps.\n\nLet's get started!\n\n## Step 1: SignIn to Supabase\n\nGo to [Supabase](https://supabase.com/), Click `Sign In`, and create a new account by authenticating with **GitHub**. If you already have an account, you will be logged in.\n\n## Step 2: Create a new project in Supabase\n\nClick on `New project` from the Dashboard and select an organization. If you don't have an organization, create one using `+ New organization.`\n\n- Give your Supabase project a `name.`\n- Enter a secure `Database Password.`\n- Choose the `region` where your app's backend is hosted.\n- Click `Create new project.`\n\n![New Project](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/dhiwise/documentation/newProj.png)\n\n## Step 3: Find the API key and URL\n\nOnce your project is created, you can access the API Key and URL string, Or if you already have an account go to your `organization-\u003e app-\u003e settings-\u003e API`.\n\n![auth keys](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/dhiwise/documentation/authKeys.png)\n\n## Step 4: Integrations\n\nThere are two ways you can integrate Supabase into your DhiWise Flutter applications.\n\n### Authentication\n\nYou can integrate `Supabase Email/Password SignUp` or `Supabase Email/Password SignIn` on your components.\n\n- Open the screen of your flutter application\n- Go to the component on which you want to add authentication\n- on the `onClick` method - select `authentication`\n- From the list, If you want SignUp - select `SignUp with Email/Password`; otherwise, select `SignIn with Email/Password` from Supabase Auth section\n\n![Auth](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/dhiwise/documentation/auth.gif)\n\nAnd that's it. Supabase authentication will be added to the selected component.\n\nAfter downloading the application source code,\n\n1. Add Supabase URL and Supabase public key inside **_lib/core/utils/initial_bindings_** file.\n2. For additional details, refer ***https://supabase.com/docs/guides/with-flutter***\n\n### Working with Data\n\nWhen you first integrate Supabase in your DhiWise Flutter application, You will be asked to add [Supabase auth key and URL](##step-3-find-the-api-key-and-url). When you add them, all the tables available in your Supabase project will be synced in DhiWise. You can integrate Select and Create queries on your Flutter screen for a particular table in DhiWise.\n\n### Select records\n\n![Create](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/dhiwise/documentation/select.png)\n\n#### **Step 1:**\n\nSelect the screen from the screen list where you want to integrate Supabase.\n\n#### **Step 2:**\n\nNext, go to the view where you want to add Integration, and from the suggestion box for the `onClick` property, choose `Supabase integration,` which will take you to the Integration screen. Where you will be asked to `Enter function name.` Enter the name of your function and click `Submit.`\n\n#### **Step 3:**\n\nAfter submitting the function name, you will be asked to select a type of Supabase integration. To retrieve data from Supabase, choose `select.`\n\n#### **Step 4:**\n\nNext, select the table from which you want to fetch records from the listed Tables.\n\n#### **Step 5:**\n\nSelect the type of integration\n\n| Type         | Description                                       |\n| ------------ | ------------------------------------------------- |\n| **Single**   | Used to fetch a single record from the database.  |\n| **Multiple** | Used to fetch multiple records from the database. |\n\n\u003cAdmonition type=\"info\"\u003e\n\nFor Multiple types, you need to set `data limit,` `order by, and `order.`\n\n\u003c/Admonition\u003e\n\n#### **Step 6:**\n\nYou will be redirected to the API Integration screen, where you can set request and response.\n\nFor request binding, the below types are supported. Also, Select the operator for comparison before moving forward.\n\n| Type                    | Description                                                |\n| ----------------------- | ---------------------------------------------------------- |\n| **View**                | Select any component from your screen.                     |\n| **Constant**            | Select a constant you've created in your app.              |\n| **Get from preference** | Select the key you want to fetch from preference.          |\n| **Navigation argument** | Select data that's been passed from one screen to another. |\n\nFor response binding, the below types are supported.\n\n| Type                   | Description                           |\n| ---------------------- | ------------------------------------- |\n| **View**               | Select any component from the screen. |\n| **Save to preference** | Storing the data to preference.       |\n\n#### **Select 7:**\n\n`Handle action` - Select the action you wish to take once the Supabase call has either been accepted successfully or refused due to an error.\n\nAvailable action for On success and On error are,\n\n1. [Show Alert](https://docs.dhiwise.com/docs/flutter/show-alert)\n2. [Navigation](https://docs.dhiwise.com/docs/flutter/navigation)\n\n#### **Step 8:**\n\nFinally, you have added Supabase to your application to fetch records on your screen!\n\n\u003cAdmonition type=\"tip\" label=\"Example\"\u003e\n\nSuppose you want to fetch records from Supabase and populate the item list on your screen. You can integrate Supabase as discussed above and bind the response with your list view.\n\n\u003c/Admonition\u003e\n\n\u003ch3\u003e Create records \u003c/h3\u003e\n\n#### **Step 1:**\n\nChoose the screen you wish to integrate Supabase for from the list of screens.\n\n#### **Step 2:**\n\nNext, switch to the component you want to add Integration, and on the `onClick` property, choose `Supabase integration,` which will take you to its integration screen, where you will be asked to **Enter function name**, which will be used in generated code. Enter the name for it and click `Submit`\n\n#### **Step 3:**\n\nAfter submitting the function name, you will be asked to select a type of Supabase integration. For example, to create a record in Supabase, choose `Create.`\n\n#### **Step 4:**\n\nNext, select the table where you want to create a record from the listed Tables.\n\n#### **Step 5:**\n\nIf you want to create a Single record, Select **Select**. Otherwise, **Multiple**.\n\n#### **Step 6:**\n\nNow, you will be redirected to the API Integration screen, where you can set request and response.\n\nFor request binding, the below types are supported.\n\n| Type                    | Description                                                |\n| ----------------------- | ---------------------------------------------------------- |\n| **View**                | Select any component from the screen                       |\n| **Constant**            | Select a constant you've created in your app.              |\n| **Get from preference** | Select the key you want to fetch from preference.          |\n| **Navigation argument** | Select data that's been passed from one screen to another. |\n\nFor response binding, the below types are supported.\n\n| Type                   | Description                          |\n| ---------------------- | ------------------------------------ |\n| **View**               | Select any component from the screen |\n| **Save to preference** | Storing the data to preference.      |\n\n#### **Select 7:**\n\n`Handle action` - Select the action you wish to take once the Supabase call has either been accepted successfully or refused due to an error.\n\nAvailable action for On success and On error are,\n\n1. [Show Alert](https://docs.dhiwise.com/docs/flutter/show-alert)\n2. [Navigation](https://docs.dhiwise.com/docs/flutter/navigation)\n\n#### **Step 9:**\n\nFinally, you have added Supabase to your application to create records from your screen data!\n\n## Resources\n\n- [DhiWise Official Website](https://dhiwise.com)\n- [DhiWise Documentation](https://docs.dhiwise.com)\n","website":"https://www.dhiwise.com/","docs":"https://docs.dhiwise.com/docs/flutter/supabase-integration","contact":60,"approved":true,"created_at":"2022-05-30T15:29:50+00:00","tsv":"'-3':409C '/)':79C '/),':142C '/docs/flutter/navigation)':755C,1073C '/docs/flutter/show-alert)':750C,1068C '/docs/guides/with-flutter***':383C '/storage/v1/object/public/images/integrations/dhiwise/documentation/auth.gif)':345C '/storage/v1/object/public/images/integrations/dhiwise/documentation/authkeys.png)':267C '/storage/v1/object/public/images/integrations/dhiwise/documentation/newproj.png)':226C '/storage/v1/object/public/images/integrations/dhiwise/documentation/select.png)':456C '1':133C,365C,458C,745C,806C,1063C '2':167C,376C,473C,751C,822C,1069C '3':228C,524C,874C '4':269C,549C,902C '5':36C,566C,919C '6':607C,933C '7':711C,1029C '8':757C '9':1075C 'accept':728C,1046C 'access':242C 'account':114C,150C,160C,255C 'action':713C,716C,737C,1031C,1034C,1055C 'ad':354C,761C,1079C 'add':314C,366C,402C,419C,483C,831C 'addit':378C 'alert':747C,1065C 'alreadi':157C,252C 'also':632C 'anoth':684C,1002C 'api':100C,231C,244C,262C,413C,614C,941C 'app':106C,213C,260C,659C,977C 'app.dhiwise.com':118C 'applic':10B,44C,74C,92C,283C,304C,362C,396C,765C,1083C 'argument':673C,991C 'ask':400C,509C,533C,853C,883C 'auth':263C,340C,342C,404C 'authent':152C,284C,315C,321C,351C 'avail':424C,736C,1054C 'backend':70C,215C 'bind':626C,687C,796C,953C,1005C 'box':489C 'call':724C,1042C 'choos':208C,494C,546C,807C,838C,899C 'click':143C,174C,218C,521C,871C 'code':14B,18B,364C,864C 'comparison':637C 'compon':61C,297C,308C,358C,646C,698C,827C,964C,1016C 'connect':68C 'constant':650C,653C,968C,971C 'convert':85C 'creat':42C,115C,147C,168C,192C,219C,239C,439C,453C,656C,803C,894C,900C,911C,924C,974C,1085C 'dashboard':180C 'data':30C,56C,386C,543C,600C,675C,707C,993C,1025C,1090C 'databas':98C,206C,583C,592C 'descript':573C,642C,694C,960C,1012C 'design':87C 'detail':379C 'develop':6B,82C 'devtool':1099 'dhiwis':1A,24C,34C,49C,72C,76C,113C,119C,281C,394C,433C,450C,1092C,1096C,1100 'dhiwise.com':1095C 'discuss':793C 'docs.dhiwise.com':749C,754C,1067C,1072C,1098C 'docs.dhiwise.com/docs/flutter/navigation)':753C,1071C 'docs.dhiwise.com/docs/flutter/show-alert)':748C,1066C 'document':62C,1097C 'download':360C 'due':732C,1050C 'easi':121C 'easili':28C,45C 'effect':47C 'either':726C,1044C 'email/password':289C,293C,332C,337C 'enter':203C,511C,514C,855C,865C 'enterpris':8B 'enterprise-grad':7B 'error':735C,743C,1053C,1061C 'essenti':55C 'exampl':892C 'explain':65C 'fetch':559C,577C,587C,669C,767C,776C,987C 'figma':86C 'file':375C 'final':758C,1076C 'find':229C,411C 'find-the-api-key-and-url':410C 'first':389C 'five':126C 'flutter':73C,91C,105C,282C,303C,395C,443C 'forward':640C 'function':512C,519C,528C,856C,878C 'generat':863C 'get':130C,660C,978C 'github':154C 'give':197C 'go':137C,256C,305C,475C 'grade':9B 'guid':64C 'handl':712C,1030C 'help':22C 'host':217C 'insid':373C 'integr':29C,97C,123C,270C,277C,287C,390C,436C,470C,484C,496C,503C,540C,571C,615C,790C,813C,832C,840C,847C,890C,942C 'item':783C 'key':232C,245C,264C,372C,405C,414C,665C,983C 'let':94C,128C 'lib/core/utils/initial_bindings_':374C 'limit':601C 'list':324C,465C,563C,784C,801C,818C,916C 'log':164C 'low':13B 'low-cod':12B 'map':51C 'method':319C 'move':639C 'multipl':584C,588C,594C,931C 'name':202C,513C,516C,529C,857C,867C,879C 'navig':672C,752C,990C,1070C 'need':597C 'new':149C,170C,176C,195C,220C,222C 'next':474C,550C,823C,903C 'obuldanrptloktxcffvn.supabase.co':225C,266C,344C,455C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/dhiwise/documentation/auth.gif)':343C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/dhiwise/documentation/authkeys.png)':265C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/dhiwise/documentation/newproj.png)':224C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/dhiwise/documentation/select.png)':454C 'offici':1093C 'onclick':318C,492C,836C 'one':116C,193C,681C,999C 'open':298C 'oper':635C 'order':602C,605C 'organ':184C,191C,196C,259C 'otherwis':333C,930C 'particular':447C 'pass':679C,997C 'password':207C 'platform':4B 'popul':781C 'prefer':662C,671C,704C,709C,980C,989C,1022C,1027C 'pro':17B 'pro-cod':16B 'program':3B 'project':171C,177C,200C,221C,223C,237C,428C 'properti':493C,837C 'public':371C 'queri':440C 'quick':75C,96C 'react':89C,103C 'record':452C,560C,580C,589C,768C,777C,804C,896C,913C,927C,1086C 'redirect':611C,938C 'refer':380C 'refus':731C,1049C 'region':210C 'request':621C,625C,948C,952C 'resourc':1091C 'respect':59C 'respons':623C,686C,798C,950C,1004C 'retriev':542C 'save':702C,1020C 'screen':300C,444C,461C,464C,504C,616C,649C,682C,701C,771C,787C,809C,820C,848C,943C,967C,1000C,1019C,1089C 'section':341C 'secur':205C 'select':182C,320C,329C,334C,357C,437C,451C,459C,535C,547C,551C,567C,633C,644C,651C,663C,674C,696C,710C,714C,885C,904C,928C,929C,962C,969C,981C,992C,1014C,1028C,1032C 'set':261C,599C,620C,947C 'show':746C,1064C 'sign':144C 'signin':134C,294C,335C 'signup':290C,328C,330C 'simpl':37C 'simultan':19B 'singl':574C,579C,926C 'sourc':363C 'start':131C 'step':38C,127C,132C,166C,227C,268C,408C,457C,472C,523C,548C,565C,606C,756C,805C,821C,873C,901C,918C,932C,1074C 'store':705C,1023C 'string':248C 'submit':522C,526C,872C,876C 'success':729C,740C,1047C,1058C 'suggest':488C 'supabas':32C,69C,122C,136C,139C,173C,199C,278C,288C,292C,339C,350C,367C,370C,391C,403C,427C,471C,495C,539C,545C,723C,762C,779C,791C,814C,839C,889C,898C,1041C,1080C 'supabase.com':141C,382C 'supabase.com/),':140C 'supabase.com/docs/guides/with-flutter***':381C 'support':120C,631C,692C,958C,1010C 'suppos':772C 'switch':824C 'sync':431C 'tabl':423C,448C,553C,564C,906C,917C 'take':499C,720C,843C,1038C 'thereaft':39C 'tool':83C 'two':273C 'type':537C,569C,572C,595C,629C,641C,690C,693C,887C,956C,959C,1008C,1011C 'url':234C,247C,368C,407C,416C 'use':194C,575C,585C,861C 've':655C,973C 'view':60C,478C,643C,695C,802C,961C,1013C 'visual':2B 'want':312C,327C,468C,481C,557C,667C,774C,829C,909C,922C,985C 'way':274C 'websit':1094C 'wish':718C,811C,1036C 'work':384C 'www.dhiwise.com':78C 'www.dhiwise.com/)':77C","video":"1G-TjP721Cs","call_to_action_link":null,"featured":false},"overview":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    h2: \"h2\",\n    a: \"a\",\n    code: \"code\",\n    strong: \"strong\",\n    ul: \"ul\",\n    li: \"li\",\n    img: \"img\",\n    h3: \"h3\",\n    ol: \"ol\",\n    em: \"em\",\n    h4: \"h4\",\n    table: \"table\",\n    thead: \"thead\",\n    tr: \"tr\",\n    th: \"th\",\n    tbody: \"tbody\",\n    td: \"td\"\n  }, _provideComponents(), props.components), {Admonition} = _components;\n  if (!Admonition) _missingMdxReference(\"Admonition\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(_components.p, {\n      children: \"With the help of DhiWise, now you can easily integrate data from Supabase to DhiWise in 5 simple steps. Thereafter, you can create your application easily and effectively on DhiWise by mapping out all the essential data to their respective view components.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Documentation\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This guide explains how to connect Supabase backend to DhiWise Flutter application quickly.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://www.dhiwise.com/\",\n        children: \"DhiWise\"\n      }), \" is a Developer tool to convert Figma designs into React and Flutter applications. It lets you quickly integrate Databases and APIs into your React and Flutter Apps.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you don't have a DhiWise account, create one \", _jsx(_components.a, {\n        href: \"https://app.dhiwise.com\",\n        children: \"here\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"DhiWise supports easy Supabase Integration in just five steps.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Let's get started!\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 1: SignIn to Supabase\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Go to \", _jsx(_components.a, {\n        href: \"https://supabase.com/\",\n        children: \"Supabase\"\n      }), \", Click \", _jsx(_components.code, {\n        children: \"Sign In\"\n      }), \", and create a new account by authenticating with \", _jsx(_components.strong, {\n        children: \"GitHub\"\n      }), \". If you already have an account, you will be logged in.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 2: Create a new project in Supabase\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Click on \", _jsx(_components.code, {\n        children: \"New project\"\n      }), \" from the Dashboard and select an organization. If you don't have an organization, create one using \", _jsx(_components.code, {\n        children: \"+ New organization.\"\n      })]\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Give your Supabase project a \", _jsx(_components.code, {\n          children: \"name.\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Enter a secure \", _jsx(_components.code, {\n          children: \"Database Password.\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Choose the \", _jsx(_components.code, {\n          children: \"region\"\n        }), \" where your app's backend is hosted.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"Click \", _jsx(_components.code, {\n          children: \"Create new project.\"\n        })]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/dhiwise/documentation/newProj.png\",\n        alt: \"New Project\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 3: Find the API key and URL\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Once your project is created, you can access the API Key and URL string, Or if you already have an account go to your \", _jsx(_components.code, {\n        children: \"organization-\u003e app-\u003e settings-\u003e API\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/dhiwise/documentation/authKeys.png\",\n        alt: \"auth keys\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 4: Integrations\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"There are two ways you can integrate Supabase into your DhiWise Flutter applications.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Authentication\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You can integrate \", _jsx(_components.code, {\n        children: \"Supabase Email/Password SignUp\"\n      }), \" or \", _jsx(_components.code, {\n        children: \"Supabase Email/Password SignIn\"\n      }), \" on your components.\"]\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Open the screen of your flutter application\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Go to the component on which you want to add authentication\"\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"on the \", _jsx(_components.code, {\n          children: \"onClick\"\n        }), \" method - select \", _jsx(_components.code, {\n          children: \"authentication\"\n        })]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"From the list, If you want SignUp - select \", _jsx(_components.code, {\n          children: \"SignUp with Email/Password\"\n        }), \"; otherwise, select \", _jsx(_components.code, {\n          children: \"SignIn with Email/Password\"\n        }), \" from Supabase Auth section\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/dhiwise/documentation/auth.gif\",\n        alt: \"Auth\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"And that's it. Supabase authentication will be added to the selected component.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"After downloading the application source code,\"\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"Add Supabase URL and Supabase public key inside \", _jsx(_components.strong, {\n          children: _jsx(_components.em, {\n            children: \"lib/core/utils/initial_bindings\"\n          })\n        }), \" file.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [\"For additional details, refer \", _jsx(_components.em, {\n          children: _jsx(_components.strong, {\n            children: _jsx(_components.a, {\n              href: \"https://supabase.com/docs/guides/with-flutter\",\n              children: \"https://supabase.com/docs/guides/with-flutter\"\n            })\n          })\n        })]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Working with Data\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"When you first integrate Supabase in your DhiWise Flutter application, You will be asked to add \", _jsx(_components.a, {\n        href: \"##step-3-find-the-api-key-and-url\",\n        children: \"Supabase auth key and URL\"\n      }), \". When you add them, all the tables available in your Supabase project will be synced in DhiWise. You can integrate Select and Create queries on your Flutter screen for a particular table in DhiWise.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Select records\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/dhiwise/documentation/select.png\",\n        alt: \"Create\"\n      })\n    }), \"\\n\", _jsx(_components.h4, {\n      children: _jsx(_components.strong, {\n        children: \"Step 1:\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Select the screen from the screen list where you want to integrate Supabase.\"\n    }), \"\\n\", _jsx(_components.h4, {\n      children: _jsx(_components.strong, {\n        children: \"Step 2:\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Next, go to the view where you want to add Integration, and from the suggestion box for the \", _jsx(_components.code, {\n        children: \"onClick\"\n      }), \" property, choose \", _jsx(_components.code, {\n        children: \"Supabase integration,\"\n      }), \" which will take you to the Integration screen. Where you will be asked to \", _jsx(_components.code, {\n        children: \"Enter function name.\"\n      }), \" Enter the name of your function and click \", _jsx(_components.code, {\n        children: \"Submit.\"\n      })]\n    }), \"\\n\", _jsx(_components.h4, {\n      children: _jsx(_components.strong, {\n        children: \"Step 3:\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"After submitting the function name, you will be asked to select a type of Supabase integration. To retrieve data from Supabase, choose \", _jsx(_components.code, {\n        children: \"select.\"\n      })]\n    }), \"\\n\", _jsx(_components.h4, {\n      children: _jsx(_components.strong, {\n        children: \"Step 4:\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Next, select the table from which you want to fetch records from the listed Tables.\"\n    }), \"\\n\", _jsx(_components.h4, {\n      children: _jsx(_components.strong, {\n        children: \"Step 5:\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Select the type of integration\"\n    }), \"\\n\", _jsxs(_components.table, {\n      children: [_jsx(_components.thead, {\n        children: _jsxs(_components.tr, {\n          children: [_jsx(_components.th, {\n            children: \"Type\"\n          }), _jsx(_components.th, {\n            children: \"Description\"\n          })]\n        })\n      }), _jsxs(_components.tbody, {\n        children: [_jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: _jsx(_components.strong, {\n              children: \"Single\"\n            })\n          }), _jsx(_components.td, {\n            children: \"Used to fetch a single record from the database.\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: _jsx(_components.strong, {\n              children: \"Multiple\"\n            })\n          }), _jsx(_components.td, {\n            children: \"Used to fetch multiple records from the database.\"\n          })]\n        })]\n      })]\n    }), \"\\n\", _jsx(Admonition, {\n      type: \"info\",\n      children: _jsxs(_components.p, {\n        children: [\"For Multiple types, you need to set \", _jsx(_components.code, {\n          children: \"data limit,\"\n        }), \" \", _jsx(_components.code, {\n          children: \"order by, and \"\n        }), \"order.`\"]\n      })\n    }), \"\\n\", _jsx(_components.h4, {\n      children: _jsx(_components.strong, {\n        children: \"Step 6:\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You will be redirected to the API Integration screen, where you can set request and response.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"For request binding, the below types are supported. Also, Select the operator for comparison before moving forward.\"\n    }), \"\\n\", _jsxs(_components.table, {\n      children: [_jsx(_components.thead, {\n        children: _jsxs(_components.tr, {\n          children: [_jsx(_components.th, {\n            children: \"Type\"\n          }), _jsx(_components.th, {\n            children: \"Description\"\n          })]\n        })\n      }), _jsxs(_components.tbody, {\n        children: [_jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: _jsx(_components.strong, {\n              children: \"View\"\n            })\n          }), _jsx(_components.td, {\n            children: \"Select any component from your screen.\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: _jsx(_components.strong, {\n              children: \"Constant\"\n            })\n          }), _jsx(_components.td, {\n            children: \"Select a constant you've created in your app.\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: _jsx(_components.strong, {\n              children: \"Get from preference\"\n            })\n          }), _jsx(_components.td, {\n            children: \"Select the key you want to fetch from preference.\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: _jsx(_components.strong, {\n              children: \"Navigation argument\"\n            })\n          }), _jsx(_components.td, {\n            children: \"Select data that's been passed from one screen to another.\"\n          })]\n        })]\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"For response binding, the below types are supported.\"\n    }), \"\\n\", _jsxs(_components.table, {\n      children: [_jsx(_components.thead, {\n        children: _jsxs(_components.tr, {\n          children: [_jsx(_components.th, {\n            children: \"Type\"\n          }), _jsx(_components.th, {\n            children: \"Description\"\n          })]\n        })\n      }), _jsxs(_components.tbody, {\n        children: [_jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: _jsx(_components.strong, {\n              children: \"View\"\n            })\n          }), _jsx(_components.td, {\n            children: \"Select any component from the screen.\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: _jsx(_components.strong, {\n              children: \"Save to preference\"\n            })\n          }), _jsx(_components.td, {\n            children: \"Storing the data to preference.\"\n          })]\n        })]\n      })]\n    }), \"\\n\", _jsx(_components.h4, {\n      children: _jsx(_components.strong, {\n        children: \"Select 7:\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.code, {\n        children: \"Handle action\"\n      }), \" - Select the action you wish to take once the Supabase call has either been accepted successfully or refused due to an error.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Available action for On success and On error are,\"\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://docs.dhiwise.com/docs/flutter/show-alert\",\n          children: \"Show Alert\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://docs.dhiwise.com/docs/flutter/navigation\",\n          children: \"Navigation\"\n        })\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h4, {\n      children: _jsx(_components.strong, {\n        children: \"Step 8:\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Finally, you have added Supabase to your application to fetch records on your screen!\"\n    }), \"\\n\", _jsx(Admonition, {\n      type: \"tip\",\n      label: \"Example\",\n      children: _jsx(_components.p, {\n        children: \"Suppose you want to fetch records from Supabase and populate the item list on your screen. You can integrate Supabase as discussed above and bind the response with your list view.\"\n      })\n    }), \"\\n\", _jsx(\"h3\", {\n      children: \" Create records \"\n    }), \"\\n\", _jsx(_components.h4, {\n      children: _jsx(_components.strong, {\n        children: \"Step 1:\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Choose the screen you wish to integrate Supabase for from the list of screens.\"\n    }), \"\\n\", _jsx(_components.h4, {\n      children: _jsx(_components.strong, {\n        children: \"Step 2:\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Next, switch to the component you want to add Integration, and on the \", _jsx(_components.code, {\n        children: \"onClick\"\n      }), \" property, choose \", _jsx(_components.code, {\n        children: \"Supabase integration,\"\n      }), \" which will take you to its integration screen, where you will be asked to \", _jsx(_components.strong, {\n        children: \"Enter function name\"\n      }), \", which will be used in generated code. Enter the name for it and click \", _jsx(_components.code, {\n        children: \"Submit\"\n      })]\n    }), \"\\n\", _jsx(_components.h4, {\n      children: _jsx(_components.strong, {\n        children: \"Step 3:\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"After submitting the function name, you will be asked to select a type of Supabase integration. For example, to create a record in Supabase, choose \", _jsx(_components.code, {\n        children: \"Create.\"\n      })]\n    }), \"\\n\", _jsx(_components.h4, {\n      children: _jsx(_components.strong, {\n        children: \"Step 4:\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Next, select the table where you want to create a record from the listed Tables.\"\n    }), \"\\n\", _jsx(_components.h4, {\n      children: _jsx(_components.strong, {\n        children: \"Step 5:\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you want to create a Single record, Select \", _jsx(_components.strong, {\n        children: \"Select\"\n      }), \". Otherwise, \", _jsx(_components.strong, {\n        children: \"Multiple\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h4, {\n      children: _jsx(_components.strong, {\n        children: \"Step 6:\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Now, you will be redirected to the API Integration screen, where you can set request and response.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"For request binding, the below types are supported.\"\n    }), \"\\n\", _jsxs(_components.table, {\n      children: [_jsx(_components.thead, {\n        children: _jsxs(_components.tr, {\n          children: [_jsx(_components.th, {\n            children: \"Type\"\n          }), _jsx(_components.th, {\n            children: \"Description\"\n          })]\n        })\n      }), _jsxs(_components.tbody, {\n        children: [_jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: _jsx(_components.strong, {\n              children: \"View\"\n            })\n          }), _jsx(_components.td, {\n            children: \"Select any component from the screen\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: _jsx(_components.strong, {\n              children: \"Constant\"\n            })\n          }), _jsx(_components.td, {\n            children: \"Select a constant you've created in your app.\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: _jsx(_components.strong, {\n              children: \"Get from preference\"\n            })\n          }), _jsx(_components.td, {\n            children: \"Select the key you want to fetch from preference.\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: _jsx(_components.strong, {\n              children: \"Navigation argument\"\n            })\n          }), _jsx(_components.td, {\n            children: \"Select data that's been passed from one screen to another.\"\n          })]\n        })]\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"For response binding, the below types are supported.\"\n    }), \"\\n\", _jsxs(_components.table, {\n      children: [_jsx(_components.thead, {\n        children: _jsxs(_components.tr, {\n          children: [_jsx(_components.th, {\n            children: \"Type\"\n          }), _jsx(_components.th, {\n            children: \"Description\"\n          })]\n        })\n      }), _jsxs(_components.tbody, {\n        children: [_jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: _jsx(_components.strong, {\n              children: \"View\"\n            })\n          }), _jsx(_components.td, {\n            children: \"Select any component from the screen\"\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: _jsx(_components.strong, {\n              children: \"Save to preference\"\n            })\n          }), _jsx(_components.td, {\n            children: \"Storing the data to preference.\"\n          })]\n        })]\n      })]\n    }), \"\\n\", _jsx(_components.h4, {\n      children: _jsx(_components.strong, {\n        children: \"Select 7:\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.code, {\n        children: \"Handle action\"\n      }), \" - Select the action you wish to take once the Supabase call has either been accepted successfully or refused due to an error.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Available action for On success and On error are,\"\n    }), \"\\n\", _jsxs(_components.ol, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://docs.dhiwise.com/docs/flutter/show-alert\",\n          children: \"Show Alert\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://docs.dhiwise.com/docs/flutter/navigation\",\n          children: \"Navigation\"\n        })\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h4, {\n      children: _jsx(_components.strong, {\n        children: \"Step 9:\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Finally, you have added Supabase to your application to create records from your screen data!\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Resources\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://dhiwise.com\",\n          children: \"DhiWise Official Website\"\n        })\n      }), \"\\n\", _jsx(_components.li, {\n        children: _jsx(_components.a, {\n          href: \"https://docs.dhiwise.com\",\n          children: \"DhiWise Documentation\"\n        })\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{}}},"__N_SSG":true},"page":"/partners/integrations/[slug]","query":{"slug":"dhiwise"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"isExperimentalCompile":false,"gsp":true,"scriptLoader":[]}</script></body></html>