<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../../rss.xml" data-next-head=""/><link rel="manifest" href="../../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:type" content="website" data-next-head=""/><meta property="og:image:alt" content="Supabase Og Image" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">SuperTokens | Works With Supabase</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="SuperTokens is an open source authentication solution." data-next-head=""/><meta property="og:title" content="SuperTokens | Works With Supabase" data-next-head=""/><meta property="og:description" content="SuperTokens is an open source authentication solution." data-next-head=""/><meta property="og:url" content="https://supabase.com/partners/integrations/supertokens" data-next-head=""/><meta property="og:image" content="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/super_tokens_og.png" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2263-07e8ebb96cc2b014.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8658-639e1bb66549b52b.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/partners/integrations/%5Bslug%5D-cff8a94339d02c6e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqela6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R1eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R1eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R2eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R2eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R3eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R3eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav></div><main class="relative min-h-screen"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20"><div class="col-span-12 mx-auto mb-2 max-w-5xl space-y-10 lg:col-span-2"><a class="text-foreground hover:text-foreground-lighter flex cursor-pointer items-center transition-colors" href="../integrations.html"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left"><path d="m15 18-6-6 6-6"></path></svg>Back</a><div class="flex items-center space-x-4"><img alt="SuperTokens" loading="lazy" width="56" height="56" decoding="async" data-nimg="1" class="bg-surface-200 flex-shrink-f0 h-14 w-14 rounded-full" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsupertokens%2Fsuper_tokens_logo.png&amp;w=64&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsupertokens%2Fsuper_tokens_logo.png&amp;w=128&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsupertokens%2Fsuper_tokens_logo.png&amp;w=128&amp;q=75"/><h1 class="h1" style="margin-bottom:0">SuperTokens</h1></div><div class="bg-gradient-to-t from-background-alternative to-background border-b p-6 [&amp;_.swiper-container]:overflow-visible" style="margin-left:calc(50% - 50vw);margin-right:calc(50% - 50vw)"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0 !px-3 lg:!px-12 xl:!p-0 mx-auto max-w-5xl"><div class="swiper"><div class="swiper-wrapper"><div class="swiper-slide"><div class="relative block overflow-hidden rounded-md"><img alt="SuperTokens" loading="lazy" width="1460" height="960" decoding="async" data-nimg="1" style="color:transparent;width:100%;height:auto;background-size:cover;background-position:50% 50%;background-repeat:no-repeat;background-image:url(&quot;data:image/svg+xml;charset=utf-8,%3Csvg xmlns=&#x27;http://www.w3.org/2000/svg&#x27; viewBox=&#x27;0 0 1460 960&#x27;%3E%3Cfilter id=&#x27;b&#x27; color-interpolation-filters=&#x27;sRGB&#x27;%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3CfeColorMatrix values=&#x27;1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1&#x27; result=&#x27;s&#x27;/%3E%3CfeFlood x=&#x27;0&#x27; y=&#x27;0&#x27; width=&#x27;100%25&#x27; height=&#x27;100%25&#x27;/%3E%3CfeComposite operator=&#x27;out&#x27; in=&#x27;s&#x27;/%3E%3CfeComposite in2=&#x27;SourceGraphic&#x27;/%3E%3CfeGaussianBlur stdDeviation=&#x27;20&#x27;/%3E%3C/filter%3E%3Cimage width=&#x27;100%25&#x27; height=&#x27;100%25&#x27; x=&#x27;0&#x27; y=&#x27;0&#x27; preserveAspectRatio=&#x27;none&#x27; style=&#x27;filter: url(%23b);&#x27; href=&#x27;/images/blur.png&#x27;/%3E%3C/svg%3E&quot;)" sizes="100vw" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsupertokens%2Fsuper_tokens_og.png&amp;w=640&amp;q=75 640w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsupertokens%2Fsuper_tokens_og.png&amp;w=750&amp;q=75 750w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsupertokens%2Fsuper_tokens_og.png&amp;w=828&amp;q=75 828w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsupertokens%2Fsuper_tokens_og.png&amp;w=1080&amp;q=75 1080w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsupertokens%2Fsuper_tokens_og.png&amp;w=1200&amp;q=75 1200w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsupertokens%2Fsuper_tokens_og.png&amp;w=1920&amp;q=75 1920w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsupertokens%2Fsuper_tokens_og.png&amp;w=2048&amp;q=75 2048w, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsupertokens%2Fsuper_tokens_og.png&amp;w=3840&amp;q=75 3840w" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Fsupertokens%2Fsuper_tokens_og.png&amp;w=3840&amp;q=75"/></div></div></div></div></div></div><div class="grid gap-y-12 lg:grid-cols-8 lg:space-x-12"><div class="lg:col-span-5 overflow-hidden"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Overview</h2><div class="prose"><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p>SuperTokens is an open source authentication solution which provides many stratergies for authenticating and managing users. You can use the managed service for easy setup or you can self host the solution to have complete control over your data.</p>
<p>With SuperTokens, Supabase can be used to store and authorize access to user data. Supabase makes it simple to setup Row Level Security(RLS) policies which ensure users can only read and write data that belongs to them.</p>
<h2>Documentation</h2>
<p><a href="https://www.supertokens.com">SuperTokens</a> is an open source authentication solution which provides many stratergies for authenticating and managing users. You can use the managed service for easy setup or you can self host the solution to have complete control over your data.</p>
<p>In this guide we will build a simple web application using SuperTokens, Supabase, and Next.js. You will be able to sign up using SuperTokens and your email and user ID will be stored in Supabase. Once authenticated the frontend will be able to query Supabase and retrieve the user&#x27;s email. Our example app will be using the <a href="https://supertokens.com/docs/thirdpartyemailpassword/introduction">Email-Password and Social Login</a> recipe for authentication and session management.</p>
<p>We will use Supabase to store and authorize access to user data. Supabase makes it simple to setup Row Level Security(RLS) policies which ensure users can only read and write data that belongs to them.</p>
<h3>Demo App</h3>
<p>You can find a demo app using SuperTokens, Supabase and Nexts.js on <a href="https://github.com/supertokens/supertokens-auth-react/tree/master/examples/with-supabase">Github</a></p>
<h2>Step 1: Create a new Supabase project</h2>
<p>From your <a href="../../dashboard/org.html">Supabase dashboard</a>, click <code>New project</code>.</p>
<p>Enter a <code>Name</code> for your Supabase project.</p>
<p>Enter a secure <code>Database Password</code>.</p>
<p>Select the same <code>Region</code> you host your app&#x27;s backend in.</p>
<p>Click <code>Create new project</code>.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/supabase_dashboard_create.png" alt="New Supabase project settings"/></p>
<h2>Step 2: Creating tables in Supabase</h2>
<p>From the sidebar menu in the <a href="../../dashboard/org.html">Supabase dashboard</a>, click <code>Table editor</code>, then <code>New table</code>.</p>
<p>Enter <code>users</code> as the <code>Name</code> field.</p>
<p>Select <code>Enable Row Level Security (RLS)</code>.</p>
<p>Remove the default columns</p>
<p>Create two new columns:</p>
<ul>
<li><code>user_id</code> as <code>text</code> as primary key</li>
<li><code>email</code> as <code>text</code></li>
</ul>
<p>Click <code>Save</code> to create the new table.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/supabase_table_create.png" alt="Users table"/></p>
<h2>Step 3: Setup your Next.js App with SuperTokens.</h2>
<p>Since the scope of this guide is limited to the integration between SuperTokens and Supabase, you can refer to the SuperTokens website to see <a href="https://supertokens.com/docs/thirdpartyemailpassword/nextjs/about">how to setup your Next.js app with SuperTokens</a>.</p>
<p>Once you finish setting up your app, you will be greeted with the following screen</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/supertokens_thirdpartyemailpassword_auth_screen.png" alt="SuperTokens Auth Screen"/></p>
<h2>Step 4: Creating a Supabase JWT to access Supabase</h2>
<p>In our Nextjs app when a user signs up, we want to store the user&#x27;s email in Supabase. We would then retrieve this email from Supabase and display it on our frontend.</p>
<p>To use the Supabase client to query the database we will need to create a JWT signed with your Supabase app&#x27;s signing secret. This JWT will also need to contain the user&#x27;s userId so Supabase knows an authenticated user is making the request.</p>
<p>To create this flow we will need to modify SuperTokens so that, when a user signs up or signs in, a JWT signed with Supabase&#x27;s signing secret is created and attached to the user&#x27;s session. Attaching the JWT to the user&#x27;s session will allow us to retrieve the Supabase JWT on the frontend and backend (post session verification), using which we can query Supabase.</p>
<p>We want to create a Supabase JWT when we are creating a SuperTokens&#x27; session. This can be done by overriding the <code>createNewSession</code> function in your backend config.</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>// config/backendConfig.ts</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>import ThirdPartyEmailPasswordNode from &quot;supertokens-node/recipe/thirdpartyemailpassword&quot;;</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>import SessionNode from &quot;supertokens-node/recipe/session&quot;;</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>import { TypeInput } from &quot;supertokens-node/lib/build/types&quot;;</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>import { appInfo } from &quot;./appInfo&quot;;</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>import jwt from &quot;jsonwebtoken&quot;;</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>let backendConfig = (): TypeInput =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>    return {</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>        framework: &quot;express&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>        supertokens: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>            connectionURI: &quot;https://try.supertokens.com&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>        },</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>        appInfo,</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>        recipeList: [</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>            ThirdPartyEmailPasswordNode.init({...}),</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>            SessionNode.init({</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>                override: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>                    functions: (originalImplementation) =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>                        return {</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>                            ...originalImplementation,</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>                            // We want to create a JWT which contains the users userId signed with Supabase&#x27;s secret so</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>                            // it can be used by Supabase to validate the user when retrieving user data from their service.</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>                            // We store this token in the accessTokenPayload so it can be accessed on the frontend and on the backend.</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>                            createNewSession: async function (input) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>                                const payload = {</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>                                    userId: input.userId,</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>                                    exp: Math.floor(Date.now() / 1000) + 60 * 60,</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>                                };</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>                                const supabase_jwt_token = jwt.sign(payload, process.env.SUPABASE_SIGNING_SECRET);</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>                                input.accessTokenPayload = {</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>                                    ...input.accessTokenPayload,</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>                                    supabase_token: supabase_jwt_token,</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>                                };</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>                                return await originalImplementation.createNewSession(input);</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>                            },</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>                        };</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>                    },</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>                },</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>            }),</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>        ],</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>        isInServerlessEnv: true,</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>    };</span></div></div><div><span class="ch-code-line-number">_<!-- -->48</span><div style="display:inline-block;margin-left:16px"><span>};</span></div></div><br/></code></div></div>
<p>As seen above, we will be using the <code>jsonwebtoken</code> library to create a JWT signed with Supabase&#x27;s signing secret whose payload contains the user&#x27;s userId.</p>
<p>We will be storing this token in the <code>accessTokenPayload</code> which will essentially allow us to access the <code>supabase_token</code> on the frontend and backend whilst the user is logged in.</p>
<h2>Step 5: Creating a Supabase client</h2>
<p>Create a new file called <code>utils/supabase.ts</code> and add the following:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>// utils/supabase.ts</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>import { createClient } from &#x27;@supabase/supabase-js&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>const getSupabase = (access_token) =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>  const supabase = createClient(</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>    process.env.NEXT_PUBLIC_SUPABASE_URL,</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>    process.env.NEXT_PUBLIC_SUPABASE_KEY</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>  )</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>  supabase.auth.session = () =&gt; ({</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>    access_token,</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>  })</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>  return supabase</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->18</span><div style="display:inline-block;margin-left:16px"><span>export { getSupabase }</span></div></div><br/></code></div></div>
<p>This will be our client for talking to Supabase. We can pass it an <code>access_token</code> and it will be attached to our request. This <code>access_token</code> is the same as the <code>supabase_token</code> we had created earlier.</p>
<h2>Step 6: Inserting users into Supabase when they sign up:</h2>
<p>In our example app there are two ways for signing up a user. Email-Password and Social Login based authentication. We will need to override both these APIs such that when a user signs up, their email mapped to their userId is stored in Supabase.</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>// config/backendConfig.ts</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>import ThirdPartyEmailPasswordNode from &quot;supertokens-node/recipe/thirdpartyemailpassword&quot;;</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>import SessionNode from &quot;supertokens-node/recipe/session&quot;;</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>import { TypeInput } from &quot;supertokens-node/lib/build/types&quot;;</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>import { appInfo } from &quot;./appInfo&quot;;</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>import jwt from &quot;jsonwebtoken&quot;;</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>import { getSupabase } from &quot;../utils/supabase&quot;;</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>let backendConfig = (): TypeInput =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>    return {</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>        framework: &quot;express&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>        supertokens: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>            connectionURI: &quot;https://try.supertokens.com&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>        },</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>        appInfo,</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>        recipeList: [</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>            ThirdPartyEmailPasswordNode.init({</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                providers: [...],</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                override: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                    apis: (originalImplementation) =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                        return {</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                            ...originalImplementation,</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                            // the thirdPartySignInUpPost function handles sign up/in via Social login</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                            thirdPartySignInUpPOST: async function (input) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                if (originalImplementation.thirdPartySignInUpPOST === undefined) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                    throw Error(&quot;Should never come here&quot;);</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                }</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                // call the sign up/in api for social login</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                let response = await originalImplementation.thirdPartySignInUpPOST(input);</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                // check that there is no issue with sign up and that a new user is created</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                if (response.status === &quot;OK&quot; &amp;&amp; response.createdNewUser) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                    // retrieve the accessTokenPayload from the user&#x27;s session</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                    const accessTokenPayload = response.session.getAccessTokenPayload();</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                    // create a supabase client with the supabase_token from the accessTokenPayload</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                    const supabase = getSupabase(accessTokenPayload.supabase_token);</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                    // store the user&#x27;s email mapped to their userId in Supabase</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                    const { error } = await supabase</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                        .from(&quot;users&quot;)</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                        .insert({ email: response.user.email, user_id: response.user.id });</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                    if (error !== null) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                        throw error;</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                    }</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                }</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                return response;</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                            },</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                            // the emailPasswordSignUpPOST function handles sign up via Email-Password</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                            emailPasswordSignUpPOST: async function (input) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                if (originalImplementation.emailPasswordSignUpPOST === undefined) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                    throw Error(&quot;Should never come here&quot;);</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                }</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                let response = await originalImplementation.emailPasswordSignUpPOST(input);</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                if (response.status === &quot;OK&quot;) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                    // retrieve the accessTokenPayload from the user&#x27;s session</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                    const accessTokenPayload = response.session.getAccessTokenPayload();</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                    // create a supabase client with the supabase_token from the accessTokenPayload</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                    const supabase = getSupabase(accessTokenPayload.supabase_token);</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                    // store the user&#x27;s email mapped to their userId in Supabase</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                    const { error } = await supabase</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                        .from(&quot;users&quot;)</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                        .insert({ email: response.user.email, user_id: response.user.id });</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                    if (error !== null) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                        throw error;</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                    }</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                }</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                                return response;</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                            },</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                        };</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                    },</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>                },</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>            }),</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>            SessionNode.init({...}),</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>        ],</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>        isInServerlessEnv: true,</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>    };</span></div></div><div><span class="ch-code-line-number">_<!-- -->92</span><div style="display:inline-block;margin-left:16px"><span>};</span></div></div><br/></code></div></div>
<p>As seen above, we will be overriding the <code>emailPasswordSignUpPOST</code> and <code>thirdPartySignInUpPOST</code> APIs such that if a user signs up, we retrieve the Supabase JWT (which we created in the <code>createNewSession</code> function) from the user&#x27;s accessTokenPayload and send a request to Supabase to insert the email-userid mapping.</p>
<h2>Step 7: Retrieving the user&#x27;s email on the frontend</h2>
<p>Now that our backend is setup we can modify our frontend to retrieve the user&#x27;s email from Supabase.</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>// pages/index.tsx</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>import React, { useState, useEffect } from &#x27;react&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>import Head from &#x27;next/head&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>import styles from &#x27;../styles/Home.module.css&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>import ThirdPartyEmailPassword, {</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>  ThirdPartyEmailPasswordAuth,</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>} from &#x27;supertokens-auth-react/recipe/thirdpartyemailpassword&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>import dynamic from &#x27;next/dynamic&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>import { useSessionContext } from &#x27;supertokens-auth-react/recipe/session&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>import { getSupabase } from &#x27;../utils/supabase&#x27;</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>export default function Home() {</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>  return (</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>    // We will wrap the ProtectedPage component with ThirdPartyEmailPasswordAuth so only an</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>    // authenticated user can access it. This will also allow us to access the users session information</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>    // within the component.</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>    &lt;ThirdPartyEmailPasswordAuth&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>      &lt;ProtectedPage /&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>    &lt;/ThirdPartyEmailPasswordAuth&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>  )</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>function ProtectedPage() {</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>  // retrieve the authenticated user&#x27;s accessTokenPayload and userId from the sessionContext</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>  const { accessTokenPayload, userId } = useSessionContext()</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>  if (sessionContext.loading === true) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>    return null</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>  const [userEmail, setEmail] = useState(&#x27;&#x27;)</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>  useEffect(() =&gt; {</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>    async function getUserEmail() {</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>      // retrieve the supabase client who&#x27;s JWT contains users userId, this will be</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>      // used by supabase to check that the user can only access table entries which contain their own userId</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>      const supabase = getSupabase(accessTokenPayload.supabase_token)</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>      // retrieve the user&#x27;s name from the users table whose email matches the email in the JWT</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>      const { data } = await supabase.from(&#x27;users&#x27;).select(&#x27;email&#x27;).eq(&#x27;user_id&#x27;, userId)</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>      if (data.length &gt; 0) {</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>        setEmail(data[0].email)</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>      }</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>    }</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>    getUserEmail()</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>  }, [])</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>  return (</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>    &lt;div className={styles.container}&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>      &lt;Head&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>        &lt;title&gt;SuperTokens 💫&lt;/title&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>        &lt;link rel=&quot;icon&quot; href=&quot;/favicon.ico&quot; /&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>      &lt;/Head&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span></span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>      &lt;main className={styles.main}&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>        &lt;p className={styles.description}&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>          You are authenticated with SuperTokens! (UserId: {userId})</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>          &lt;br /&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>          Your email retrieved from Supabase: {userEmail}</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>        &lt;/p&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>      &lt;/main&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>    &lt;/div&gt;</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>  )</span></div></div><div><span class="ch-code-line-number">_<!-- -->65</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<p>As seen above we will be using SuperTokens <code>useSessionContext</code> hook to retrieve the authenticated user&#x27;s <code>userId</code> and <code>accessTokenPayload</code>. Using React&#x27;s <code>useEffect</code> hook we can use the Supabase client to retrieve the user&#x27;s email from Supabase using the JWT retrieved from the user&#x27;s <code>accessTokenPayload</code> and their <code>userId</code>.</p>
<h2>Step 8: Create Policies to enforce Row Level Security for Select and Insert requests</h2>
<p>To enforce Row Level Security for the <code>Users</code> table we will need to create policies for Select and Insert requests.</p>
<p>These polices will retrieve the userId from the JWT and check if it matches the userId in the Supabase table</p>
<p>To do this we will need a PostgreSQL function to extract the userId from the JWT.</p>
<p>The payload in the JWT will have the following structure:</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>// JWT payload</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>{</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    userId,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    exp</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<p>To create the PostgreSQL function, lets navigate back to the Supabase dashboard, select <code>SQL</code> from the sidebar menu, and click <code>New query</code>. This will create a new query called <code>new sql snippet</code>, which will allow us to run any SQL against our Postgres database.</p>
<p>Write the following and click <code>Run</code>.</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>create or replace function auth.user_id() returns text as $$</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  select nullif(current_setting(&#x27;request.jwt.claims&#x27;, true)::json-&gt;&gt;&#x27;userId&#x27;, &#x27;&#x27;)::text;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>$$ language sql stable;</span></div></div><br/></code></div></div>
<p>This will create a function called <code>auth.user_id()</code>, which will inspect the <code>userId</code> field of our JWT payload.</p>
<h3>SELECT query policy</h3>
<p>Our first policy will check whether the user is the owner of the email.</p>
<p>Select <code>Authentication</code> from the Supabase sidebar menu, click <code>Policies</code>, and then <code>New Policy</code> on the <code>Users</code> table.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/create_policy.png" alt="Create new policy"/></p>
<p>From the modal, select <code>Create a policy from scratch</code> and add the following.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/policy_config_select.png" alt="Policy settings for SELECT"/></p>
<p>This policy is calling the PostgreSQL function we just created to get the currently logged in user&#x27;s ID <code>auth.user_id()</code> and checking whether this matches the <code>user_id</code> column for the current <code>email</code>. If it does, then it will allow the user to select it, otherwise it will continue to deny.</p>
<p>Click <code>Review</code> and then <code>Save policy</code>.</p>
<h3>INSERT query policy</h3>
<p>Our second policy will check whether the <code>user_id</code> being inserted is the same as the <code>userId</code> in the JWT.</p>
<p>Create another policy and add the following:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/policy_config_insert.png" alt="Policy settings for INSERT"/></p>
<p>Similar to the previous policy we are calling the PostgreSQL function we created to get the currently logged in user&#x27;s ID <code>auth.user_id()</code> and check whether this matches the <code>user_id</code> column for the row we are trying to insert. If it does, then it will allow the user to insert the row, otherwise it will continue to deny.</p>
<p>Click <code>Review</code> and then <code>Save policy</code>.</p>
<h2>Step 9: Test your changes</h2>
<p>You can now sign up and you should see the following screen:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/supabase_app_authenticated_screen.png" alt="SuperTokens App Authenticated"/></p>
<p>If you navigate to your table you should see a new row with the user&#x27;s <code>user_id</code> and <code>email</code>.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/table_with_user.png" alt="Supabase Users table"/></p>
<h2>Resources</h2>
<ul>
<li><a href="https://supertokens.com/">SuperTokens</a> official website.</li>
<li><a href="https://supertokens.com/discord">SuperTokens community</a>.</li>
<li><a href="https://supertokens.com/docs/guides">SuperTokens documentation</a>.</li>
</ul></div></div><div class="lg:col-span-3"><div class="sticky top-20 flex flex-col gap-4"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Details</h2><div class="text-foreground divide-y"><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Developer</span><span class="text-foreground">SuperTokens</span></div><div class="flex items-center justify-between py-2"><span class="text-lighter">Category</span><a class="text-brand hover:underline transition-colors" href="../integrations.html#auth">Auth</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Website</span><a href="https://supertokens.com/" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors">supertokens.com</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Documentation</span><a href="https://supertokens.com/" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors"><span class="flex items-center space-x-1"><span>Learn</span><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></span></a></div></div><p class="text-foreground-light text-sm">Third-party integrations and docs are managed by Supabase partners.</p></div></div></div></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"partner":{"id":32,"slug":"supertokens","type":"technology","category":"Auth","developer":"SuperTokens","title":"SuperTokens","description":"SuperTokens is an open source authentication solution.","logo":"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/super_tokens_logo.png","images":["https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/super_tokens_og.png"],"overview":"SuperTokens is an open source authentication solution which provides many stratergies for authenticating and managing users. You can use the managed service for easy setup or you can self host the solution to have complete control over your data.\n\nWith SuperTokens, Supabase can be used to store and authorize access to user data. Supabase makes it simple to setup Row Level Security(RLS) policies which ensure users can only read and write data that belongs to them.\n\n## Documentation\n\n[SuperTokens](https://www.supertokens.com) is an open source authentication solution which provides many stratergies for authenticating and managing users. You can use the managed service for easy setup or you can self host the solution to have complete control over your data.\n\nIn this guide we will build a simple web application using SuperTokens, Supabase, and Next.js. You will be able to sign up using SuperTokens and your email and user ID will be stored in Supabase. Once authenticated the frontend will be able to query Supabase and retrieve the user's email. Our example app will be using the [Email-Password and Social Login](https://supertokens.com/docs/thirdpartyemailpassword/introduction) recipe for authentication and session management.\n\nWe will use Supabase to store and authorize access to user data. Supabase makes it simple to setup Row Level Security(RLS) policies which ensure users can only read and write data that belongs to them.\n\n### Demo App\n\nYou can find a demo app using SuperTokens, Supabase and Nexts.js on [Github](https://github.com/supertokens/supertokens-auth-react/tree/master/examples/with-supabase)\n\n## Step 1: Create a new Supabase project\n\nFrom your [Supabase dashboard](https://supabase.com/dashboard/), click `New project`.\n\nEnter a `Name` for your Supabase project.\n\nEnter a secure `Database Password`.\n\nSelect the same `Region` you host your app's backend in.\n\nClick `Create new project`.\n\n![New Supabase project settings](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/supabase_dashboard_create.png)\n\n## Step 2: Creating tables in Supabase\n\nFrom the sidebar menu in the [Supabase dashboard](https://supabase.com/dashboard/), click `Table editor`, then `New table`.\n\nEnter `users` as the `Name` field.\n\nSelect `Enable Row Level Security (RLS)`.\n\nRemove the default columns\n\nCreate two new columns:\n\n- `user_id` as `text` as primary key\n- `email` as `text`\n\nClick `Save` to create the new table.\n\n![Users table](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/supabase_table_create.png)\n\n## Step 3: Setup your Next.js App with SuperTokens.\n\nSince the scope of this guide is limited to the integration between SuperTokens and Supabase, you can refer to the SuperTokens website to see [how to setup your Next.js app with SuperTokens](https://supertokens.com/docs/thirdpartyemailpassword/nextjs/about).\n\nOnce you finish setting up your app, you will be greeted with the following screen\n\n![SuperTokens Auth Screen](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/supertokens_thirdpartyemailpassword_auth_screen.png)\n\n## Step 4: Creating a Supabase JWT to access Supabase\n\nIn our Nextjs app when a user signs up, we want to store the user's email in Supabase. We would then retrieve this email from Supabase and display it on our frontend.\n\nTo use the Supabase client to query the database we will need to create a JWT signed with your Supabase app's signing secret. This JWT will also need to contain the user's userId so Supabase knows an authenticated user is making the request.\n\nTo create this flow we will need to modify SuperTokens so that, when a user signs up or signs in, a JWT signed with Supabase's signing secret is created and attached to the user's session. Attaching the JWT to the user's session will allow us to retrieve the Supabase JWT on the frontend and backend (post session verification), using which we can query Supabase.\n\nWe want to create a Supabase JWT when we are creating a SuperTokens' session. This can be done by overriding the `createNewSession` function in your backend config.\n\n```ts\n// config/backendConfig.ts\n\nimport ThirdPartyEmailPasswordNode from \"supertokens-node/recipe/thirdpartyemailpassword\";\nimport SessionNode from \"supertokens-node/recipe/session\";\nimport { TypeInput } from \"supertokens-node/lib/build/types\";\nimport { appInfo } from \"./appInfo\";\nimport jwt from \"jsonwebtoken\";\n\nlet backendConfig = (): TypeInput =\u003e {\n    return {\n        framework: \"express\",\n        supertokens: {\n            connectionURI: \"https://try.supertokens.com\",\n        },\n        appInfo,\n        recipeList: [\n            ThirdPartyEmailPasswordNode.init({...}),\n            SessionNode.init({\n                override: {\n                    functions: (originalImplementation) =\u003e {\n                        return {\n                            ...originalImplementation,\n                            // We want to create a JWT which contains the users userId signed with Supabase's secret so\n                            // it can be used by Supabase to validate the user when retrieving user data from their service.\n                            // We store this token in the accessTokenPayload so it can be accessed on the frontend and on the backend.\n                            createNewSession: async function (input) {\n                                const payload = {\n                                    userId: input.userId,\n                                    exp: Math.floor(Date.now() / 1000) + 60 * 60,\n                                };\n\n                                const supabase_jwt_token = jwt.sign(payload, process.env.SUPABASE_SIGNING_SECRET);\n\n                                input.accessTokenPayload = {\n                                    ...input.accessTokenPayload,\n                                    supabase_token: supabase_jwt_token,\n                                };\n\n                                return await originalImplementation.createNewSession(input);\n                            },\n                        };\n                    },\n                },\n            }),\n        ],\n        isInServerlessEnv: true,\n    };\n};\n\n```\n\nAs seen above, we will be using the `jsonwebtoken` library to create a JWT signed with Supabase's signing secret whose payload contains the user's userId.\n\nWe will be storing this token in the `accessTokenPayload` which will essentially allow us to access the `supabase_token` on the frontend and backend whilst the user is logged in.\n\n## Step 5: Creating a Supabase client\n\nCreate a new file called `utils/supabase.ts` and add the following:\n\n```ts\n// utils/supabase.ts\n\nimport { createClient } from '@supabase/supabase-js'\n\nconst getSupabase = (access_token) =\u003e {\n  const supabase = createClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL,\n    process.env.NEXT_PUBLIC_SUPABASE_KEY\n  )\n\n  supabase.auth.session = () =\u003e ({\n    access_token,\n  })\n\n  return supabase\n}\n\nexport { getSupabase }\n```\n\nThis will be our client for talking to Supabase. We can pass it an `access_token` and it will be attached to our request. This `access_token` is the same as the `supabase_token` we had created earlier.\n\n## Step 6: Inserting users into Supabase when they sign up:\n\nIn our example app there are two ways for signing up a user. Email-Password and Social Login based authentication. We will need to override both these APIs such that when a user signs up, their email mapped to their userId is stored in Supabase.\n\n```ts\n// config/backendConfig.ts\n\nimport ThirdPartyEmailPasswordNode from \"supertokens-node/recipe/thirdpartyemailpassword\";\nimport SessionNode from \"supertokens-node/recipe/session\";\nimport { TypeInput } from \"supertokens-node/lib/build/types\";\nimport { appInfo } from \"./appInfo\";\nimport jwt from \"jsonwebtoken\";\nimport { getSupabase } from \"../utils/supabase\";\n\nlet backendConfig = (): TypeInput =\u003e {\n    return {\n        framework: \"express\",\n        supertokens: {\n            connectionURI: \"https://try.supertokens.com\",\n        },\n        appInfo,\n        recipeList: [\n            ThirdPartyEmailPasswordNode.init({\n                providers: [...],\n                override: {\n                    apis: (originalImplementation) =\u003e {\n                        return {\n                            ...originalImplementation,\n                            // the thirdPartySignInUpPost function handles sign up/in via Social login\n                            thirdPartySignInUpPOST: async function (input) {\n                                if (originalImplementation.thirdPartySignInUpPOST === undefined) {\n                                    throw Error(\"Should never come here\");\n                                }\n\n                                // call the sign up/in api for social login\n                                let response = await originalImplementation.thirdPartySignInUpPOST(input);\n\n                                // check that there is no issue with sign up and that a new user is created\n                                if (response.status === \"OK\" \u0026\u0026 response.createdNewUser) {\n\n                                    // retrieve the accessTokenPayload from the user's session\n                                    const accessTokenPayload = response.session.getAccessTokenPayload();\n\n                                    // create a supabase client with the supabase_token from the accessTokenPayload\n                                    const supabase = getSupabase(accessTokenPayload.supabase_token);\n\n                                    // store the user's email mapped to their userId in Supabase\n                                    const { error } = await supabase\n                                        .from(\"users\")\n                                        .insert({ email: response.user.email, user_id: response.user.id });\n\n                                    if (error !== null) {\n\n                                        throw error;\n                                    }\n                                }\n\n                                return response;\n                            },\n                            // the emailPasswordSignUpPOST function handles sign up via Email-Password\n                            emailPasswordSignUpPOST: async function (input) {\n                                if (originalImplementation.emailPasswordSignUpPOST === undefined) {\n                                    throw Error(\"Should never come here\");\n                                }\n\n                                let response = await originalImplementation.emailPasswordSignUpPOST(input);\n\n                                if (response.status === \"OK\") {\n\n                                    // retrieve the accessTokenPayload from the user's session\n                                    const accessTokenPayload = response.session.getAccessTokenPayload();\n\n                                    // create a supabase client with the supabase_token from the accessTokenPayload\n                                    const supabase = getSupabase(accessTokenPayload.supabase_token);\n\n                                    // store the user's email mapped to their userId in Supabase\n                                    const { error } = await supabase\n                                        .from(\"users\")\n                                        .insert({ email: response.user.email, user_id: response.user.id });\n\n                                    if (error !== null) {\n\n                                        throw error;\n                                    }\n                                }\n\n                                return response;\n                            },\n                        };\n                    },\n                },\n            }),\n            SessionNode.init({...}),\n        ],\n        isInServerlessEnv: true,\n    };\n};\n\n```\n\nAs seen above, we will be overriding the `emailPasswordSignUpPOST` and `thirdPartySignInUpPOST` APIs such that if a user signs up, we retrieve the Supabase JWT (which we created in the `createNewSession` function) from the user's accessTokenPayload and send a request to Supabase to insert the email-userid mapping.\n\n## Step 7: Retrieving the user's email on the frontend\n\nNow that our backend is setup we can modify our frontend to retrieve the user's email from Supabase.\n\n```tsx\n// pages/index.tsx\n\nimport React, { useState, useEffect } from 'react'\nimport Head from 'next/head'\nimport styles from '../styles/Home.module.css'\nimport ThirdPartyEmailPassword, {\n  ThirdPartyEmailPasswordAuth,\n} from 'supertokens-auth-react/recipe/thirdpartyemailpassword'\nimport dynamic from 'next/dynamic'\nimport { useSessionContext } from 'supertokens-auth-react/recipe/session'\nimport { getSupabase } from '../utils/supabase'\n\nexport default function Home() {\n  return (\n    // We will wrap the ProtectedPage component with ThirdPartyEmailPasswordAuth so only an\n    // authenticated user can access it. This will also allow us to access the users session information\n    // within the component.\n    \u003cThirdPartyEmailPasswordAuth\u003e\n      \u003cProtectedPage /\u003e\n    \u003c/ThirdPartyEmailPasswordAuth\u003e\n  )\n}\n\nfunction ProtectedPage() {\n  // retrieve the authenticated user's accessTokenPayload and userId from the sessionContext\n  const { accessTokenPayload, userId } = useSessionContext()\n\n  if (sessionContext.loading === true) {\n    return null\n  }\n\n  const [userEmail, setEmail] = useState('')\n  useEffect(() =\u003e {\n    async function getUserEmail() {\n      // retrieve the supabase client who's JWT contains users userId, this will be\n      // used by supabase to check that the user can only access table entries which contain their own userId\n      const supabase = getSupabase(accessTokenPayload.supabase_token)\n\n      // retrieve the user's name from the users table whose email matches the email in the JWT\n      const { data } = await supabase.from('users').select('email').eq('user_id', userId)\n\n      if (data.length \u003e 0) {\n        setEmail(data[0].email)\n      }\n    }\n    getUserEmail()\n  }, [])\n\n  return (\n    \u003cdiv className={styles.container}\u003e\n      \u003cHead\u003e\n        \u003ctitle\u003eSuperTokens 💫\u003c/title\u003e\n        \u003clink rel=\"icon\" href=\"/favicon.ico\" /\u003e\n      \u003c/Head\u003e\n\n      \u003cmain className={styles.main}\u003e\n        \u003cp className={styles.description}\u003e\n          You are authenticated with SuperTokens! (UserId: {userId})\n          \u003cbr /\u003e\n          Your email retrieved from Supabase: {userEmail}\n        \u003c/p\u003e\n      \u003c/main\u003e\n    \u003c/div\u003e\n  )\n}\n```\n\nAs seen above we will be using SuperTokens `useSessionContext` hook to retrieve the authenticated user's `userId` and `accessTokenPayload`. Using React's `useEffect` hook we can use the Supabase client to retrieve the user's email from Supabase using the JWT retrieved from the user's `accessTokenPayload` and their `userId`.\n\n## Step 8: Create Policies to enforce Row Level Security for Select and Insert requests\n\nTo enforce Row Level Security for the `Users` table we will need to create policies for Select and Insert requests.\n\nThese polices will retrieve the userId from the JWT and check if it matches the userId in the Supabase table\n\nTo do this we will need a PostgreSQL function to extract the userId from the JWT.\n\nThe payload in the JWT will have the following structure:\n\n```\n// JWT payload\n{\n    userId,\n    exp\n}\n```\n\nTo create the PostgreSQL function, lets navigate back to the Supabase dashboard, select `SQL` from the sidebar menu, and click `New query`. This will create a new query called `new sql snippet`, which will allow us to run any SQL against our Postgres database.\n\nWrite the following and click `Run`.\n\n```sql\ncreate or replace function auth.user_id() returns text as $$\n  select nullif(current_setting('request.jwt.claims', true)::json-\u003e\u003e'userId', '')::text;\n$$ language sql stable;\n```\n\nThis will create a function called `auth.user_id()`, which will inspect the `userId` field of our JWT payload.\n\n### SELECT query policy\n\nOur first policy will check whether the user is the owner of the email.\n\nSelect `Authentication` from the Supabase sidebar menu, click `Policies`, and then `New Policy` on the `Users` table.\n\n![Create new policy](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/create_policy.png)\n\nFrom the modal, select `Create a policy from scratch` and add the following.\n\n![Policy settings for SELECT](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/policy_config_select.png)\n\nThis policy is calling the PostgreSQL function we just created to get the currently logged in user's ID `auth.user_id()` and checking whether this matches the `user_id` column for the current `email`. If it does, then it will allow the user to select it, otherwise it will continue to deny.\n\nClick `Review` and then `Save policy`.\n\n### INSERT query policy\n\nOur second policy will check whether the `user_id` being inserted is the same as the `userId` in the JWT.\n\nCreate another policy and add the following:\n\n![Policy settings for INSERT](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/policy_config_insert.png)\n\nSimilar to the previous policy we are calling the PostgreSQL function we created to get the currently logged in user's ID `auth.user_id()` and check whether this matches the `user_id` column for the row we are trying to insert. If it does, then it will allow the user to insert the row, otherwise it will continue to deny.\n\nClick `Review` and then `Save policy`.\n\n## Step 9: Test your changes\n\nYou can now sign up and you should see the following screen:\n\n![SuperTokens App Authenticated](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/supabase_app_authenticated_screen.png)\n\nIf you navigate to your table you should see a new row with the user's `user_id` and `email`.\n\n![Supabase Users table](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/table_with_user.png)\n\n## Resources\n\n- [SuperTokens](https://supertokens.com/) official website.\n- [SuperTokens community](https://supertokens.com/discord).\n- [SuperTokens documentation](https://supertokens.com/docs/guides).\n","website":"https://supertokens.com/","docs":"https://supertokens.com/","contact":79,"approved":true,"created_at":"2022-08-16T14:41:02+00:00","tsv":"'/)':1982C '/appinfo':641C,974C '/dashboard/),':267C,321C '/discord).':1989C '/docs/guides).':1994C '/docs/thirdpartyemailpassword/introduction)':193C '/docs/thirdpartyemailpassword/nextjs/about).':412C '/lib/build/types':637C,970C '/recipe/session':630C,963C,1318C '/recipe/thirdpartyemailpassword':623C,956C,1306C '/storage/v1/object/public/images/integrations/supertokens/documentation/create_policy.png)':1747C '/storage/v1/object/public/images/integrations/supertokens/documentation/policy_config_insert.png)':1862C '/storage/v1/object/public/images/integrations/supertokens/documentation/policy_config_select.png)':1767C '/storage/v1/object/public/images/integrations/supertokens/documentation/supabase_app_authenticated_screen.png)':1951C '/storage/v1/object/public/images/integrations/supertokens/documentation/supabase_dashboard_create.png)':304C '/storage/v1/object/public/images/integrations/supertokens/documentation/supabase_table_create.png)':369C '/storage/v1/object/public/images/integrations/supertokens/documentation/supertokens_thirdpartyemailpassword_auth_screen.png)':433C '/storage/v1/object/public/images/integrations/supertokens/documentation/table_with_user.png)':1977C '/styles/home.module.css':1297C '/supertokens/supertokens-auth-react/tree/master/examples/with-supabase)':253C '/utils/supabase':982C,1322C '0':1454C,1457C '1':255C '1000':728C '2':306C '3':371C '4':435C '5':811C '6':893C '60':729C,730C '7':1254C '8':1535C '9':1930C 'abl':145C,168C 'access':58C,208C,441C,709C,795C,834C,848C,868C,879C,1342C,1350C,1411C 'accesstokenpayload':704C,788C,1058C,1065C,1077C,1146C,1153C,1165C,1239C,1365C,1372C,1502C,1530C 'accesstokenpayload.supabase':1081C,1169C,1422C 'add':823C,1758C,1853C 'allow':567C,792C,1347C,1652C,1808C,1910C 'also':503C,1346C 'anoth':1850C 'api':930C,997C,1027C,1215C 'app':180C,237C,243C,290C,375C,407C,419C,446C,496C,905C,1947C 'appinfo':639C,655C,972C,992C 'applic':136C 'async':718C,1011C,1124C,1385C 'attach':552C,558C,874C 'auth':429C,1304C,1316C,1995 'auth.user':1673C,1696C,1787C,1885C 'authent':7B,14C,21C,93C,100C,163C,196C,515C,922C,1339C,1362C,1473C,1497C,1726C,1948C 'author':57C,207C 'await':748C,1033C,1096C,1138C,1184C,1443C 'back':1625C 'backend':292C,578C,613C,716C,803C,1266C 'backendconfig':647C,984C 'base':921C 'belong':83C,233C 'build':132C 'call':820C,1023C,1646C,1695C,1771C,1870C 'chang':1933C 'check':1036C,1405C,1578C,1715C,1790C,1833C,1888C 'classnam':1462C,1466C,1469C 'click':268C,294C,322C,358C,1637C,1666C,1732C,1820C,1923C 'client':480C,815C,858C,1070C,1158C,1391C,1513C 'column':343C,347C,1797C,1895C 'come':1021C,1134C 'communiti':1986C 'complet':43C,122C 'compon':1333C,1357C 'config':614C 'config/backendconfig.ts':616C,949C 'connectionuri':653C,990C 'const':721C,731C,832C,836C,1064C,1078C,1094C,1152C,1166C,1182C,1371C,1380C,1419C,1441C 'contain':506C,671C,775C,1395C,1415C 'continu':1817C,1920C 'control':44C,123C 'creat':256C,295C,307C,344C,361C,436C,489C,522C,550C,591C,598C,667C,764C,812C,816C,890C,1051C,1067C,1155C,1230C,1536C,1561C,1619C,1642C,1669C,1692C,1742C,1752C,1777C,1849C,1875C 'createcli':829C,838C 'createnewsess':609C,717C,1233C 'current':1680C,1781C,1800C,1879C 'dashboard':264C,318C,1629C 'data':47C,61C,81C,126C,211C,231C,694C,1442C,1456C 'data.length':1453C 'databas':281C,484C,1661C 'date.now':727C 'default':342C,1324C 'demo':236C,242C 'deni':1819C,1922C 'display':471C 'div':1461C 'document':86C,1991C 'done':605C 'dynam':1308C 'earlier':891C 'easi':32C,111C 'editor':324C 'email':153C,177C,186C,355C,459C,467C,916C,939C,1087C,1101C,1121C,1175C,1189C,1250C,1259C,1279C,1434C,1437C,1447C,1458C,1479C,1519C,1724C,1801C,1971C 'email-password':185C,915C,1120C 'email-userid':1249C 'emailpasswordsignuppost':1114C,1123C,1212C 'enabl':335C 'enforc':1539C,1549C 'ensur':74C,224C 'enter':271C,278C,328C 'entri':1413C 'eq':1448C 'error':1018C,1095C,1107C,1110C,1131C,1183C,1195C,1198C 'essenti':791C 'exampl':179C,904C 'exp':725C,1617C 'export':852C,1323C 'express':651C,988C 'extract':1598C 'field':333C,1703C 'file':819C 'find':240C 'finish':415C 'first':1712C 'flow':524C 'follow':426C,825C,1612C,1664C,1760C,1855C,1944C 'framework':650C,987C 'frontend':165C,475C,576C,712C,801C,1262C,1273C 'function':610C,660C,719C,1003C,1012C,1115C,1125C,1234C,1325C,1358C,1386C,1596C,1622C,1672C,1694C,1774C,1873C 'get':1779C,1877C 'getsupabas':833C,853C,980C,1080C,1168C,1320C,1421C 'getuseremail':1387C,1459C 'github':250C 'github.com':252C 'github.com/supertokens/supertokens-auth-react/tree/master/examples/with-supabase)':251C 'greet':423C 'guid':129C,383C 'handl':1004C,1116C 'head':1291C 'home':1326C 'hook':1493C,1507C 'host':38C,117C,288C 'id':156C,349C,1104C,1192C,1450C,1674C,1697C,1786C,1788C,1796C,1837C,1884C,1886C,1894C,1969C 'import':617C,624C,631C,638C,642C,828C,950C,957C,964C,971C,975C,979C,1284C,1290C,1294C,1298C,1307C,1311C,1319C 'inform':1354C 'input':720C,750C,1013C,1035C,1126C,1140C 'input.accesstokenpayload':740C,741C 'input.userid':724C 'insert':894C,1100C,1188C,1247C,1546C,1566C,1826C,1839C,1859C,1903C,1914C 'inspect':1700C 'integr':388C 'isinserverlessenv':751C,1202C 'issu':1041C 'json':1684C 'jsonwebtoken':645C,761C,978C 'jwt':439C,491C,501C,542C,560C,573C,594C,643C,669C,733C,745C,766C,976C,1227C,1394C,1440C,1524C,1576C,1603C,1608C,1614C,1706C,1848C 'jwt.sign':735C 'key':354C,846C 'know':513C 'languag':1687C 'let':646C,983C,1031C,1136C,1623C 'level':69C,219C,337C,1541C,1551C 'librari':762C 'limit':385C 'log':808C,1782C,1880C 'login':190C,920C,1009C,1030C 'main':1465C 'make':63C,213C,518C 'manag':23C,29C,102C,108C,199C 'mani':18C,97C 'map':940C,1088C,1176C,1252C 'match':1435C,1581C,1793C,1891C 'math.floor':726C 'menu':314C,1635C,1731C 'modal':1750C 'modifi':529C,1271C 'name':273C,332C,1428C 'navig':1624C,1954C 'need':487C,504C,527C,925C,1559C,1593C 'never':1020C,1133C 'new':258C,269C,296C,298C,326C,346C,363C,818C,1048C,1638C,1644C,1647C,1736C,1743C,1962C 'next.js':141C,374C,406C 'next/dynamic':1310C 'next/head':1293C 'nextj':445C 'nexts.js':248C 'node':622C,629C,636C,955C,962C,969C 'null':1108C,1196C,1379C 'nullif':1679C 'obuldanrptloktxcffvn.supabase.co':303C,368C,432C,1746C,1766C,1861C,1950C,1976C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/create_policy.png)':1745C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/policy_config_insert.png)':1860C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/policy_config_select.png)':1765C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/supabase_app_authenticated_screen.png)':1949C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/supabase_dashboard_create.png)':302C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/supabase_table_create.png)':367C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/supertokens_thirdpartyemailpassword_auth_screen.png)':431C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/table_with_user.png)':1975C 'offici':1983C 'ok':1054C,1143C 'open':5B,12C,91C 'originalimplement':661C,663C,998C,1000C 'originalimplementation.createnewsession':749C 'originalimplementation.emailpasswordsignuppost':1128C,1139C 'originalimplementation.thirdpartysigninuppost':1015C,1034C 'otherwis':1814C,1917C 'overrid':607C,659C,927C,996C,1210C 'owner':1721C 'p':1468C 'pages/index.tsx':1283C 'pass':865C 'password':187C,282C,917C,1122C 'payload':722C,736C,774C,1605C,1615C,1707C 'polic':1569C 'polici':72C,222C,1537C,1562C,1710C,1713C,1733C,1737C,1744C,1754C,1761C,1769C,1825C,1828C,1831C,1851C,1856C,1867C,1928C 'post':579C 'postgr':1660C 'postgresql':1595C,1621C,1773C,1872C 'previous':1866C 'primari':353C 'process.env.next':839C,843C 'process.env.supabase':737C 'project':260C,270C,277C,297C,300C 'protectedpag':1332C,1359C 'provid':17C,96C,995C 'public':840C,844C 'queri':170C,482C,586C,1639C,1645C,1709C,1827C 'react':1285C,1289C,1305C,1317C,1504C 'read':78C,228C 'recip':194C 'recipelist':656C,993C 'refer':395C 'region':286C 'remov':340C 'replac':1671C 'request':520C,877C,1243C,1547C,1567C 'request.jwt.claims':1682C 'resourc':1978C 'respons':1032C,1112C,1137C,1200C 'response.creatednewuser':1055C 'response.session.getaccesstokenpayload':1066C,1154C 'response.status':1053C,1142C 'response.user.email':1102C,1190C 'response.user.id':1105C,1193C 'retriev':173C,465C,570C,692C,1056C,1144C,1224C,1255C,1275C,1360C,1388C,1424C,1480C,1495C,1515C,1525C,1571C 'return':649C,662C,747C,850C,986C,999C,1111C,1199C,1327C,1378C,1460C,1675C 'review':1821C,1924C 'rls':71C,221C,339C 'row':68C,218C,336C,1540C,1550C,1898C,1916C,1963C 'run':1655C,1667C 'save':359C,1824C,1927C 'scope':380C 'scratch':1756C 'screen':427C,430C,1945C 'second':1830C 'secret':499C,548C,679C,739C,772C 'secur':70C,220C,280C,338C,1542C,1552C 'see':401C,1942C,1960C 'seen':754C,1205C,1485C 'select':283C,334C,1446C,1544C,1564C,1630C,1678C,1708C,1725C,1751C,1764C,1812C 'self':37C,116C 'send':1241C 'servic':30C,109C,697C 'session':198C,557C,565C,580C,601C,1063C,1151C,1353C 'sessioncontext':1370C 'sessioncontext.loading':1376C 'sessionnod':625C,958C 'sessionnode.init':658C,1201C 'set':301C,416C,1681C,1762C,1857C 'setemail':1382C,1455C 'setup':33C,67C,112C,217C,372C,404C,1268C 'sidebar':313C,1634C,1730C 'sign':147C,450C,492C,498C,536C,539C,543C,547C,675C,738C,767C,771C,900C,911C,936C,1005C,1025C,1043C,1117C,1221C,1937C 'similar':1863C 'simpl':65C,134C,215C 'sinc':378C 'snippet':1649C 'social':189C,919C,1008C,1029C 'solut':8B,15C,40C,94C,119C 'sourc':6B,13C,92C 'sql':1631C,1648C,1657C,1668C,1688C 'stabl':1689C 'step':254C,305C,370C,434C,810C,892C,1253C,1534C,1929C 'store':55C,159C,205C,455C,699C,783C,945C,1083C,1171C 'stratergi':19C,98C 'structur':1613C 'style':1295C 'styles.container':1463C 'styles.description':1470C 'styles.main':1467C 'supabas':50C,62C,139C,161C,171C,203C,212C,246C,259C,263C,276C,299C,310C,317C,392C,438C,442C,461C,469C,479C,495C,512C,545C,572C,587C,593C,677C,686C,732C,742C,744C,769C,797C,814C,837C,841C,845C,851C,862C,886C,897C,947C,1069C,1073C,1079C,1093C,1097C,1157C,1161C,1167C,1181C,1185C,1226C,1245C,1281C,1390C,1403C,1420C,1482C,1512C,1521C,1586C,1628C,1729C,1972C 'supabase.auth.session':847C 'supabase.com':266C,320C 'supabase.com/dashboard/),':265C,319C 'supabase.from':1444C 'supabase/supabase-js':831C 'supertoken':1A,2B,9C,49C,87C,138C,150C,245C,377C,390C,398C,409C,428C,530C,600C,621C,628C,635C,652C,954C,961C,968C,989C,1303C,1315C,1464C,1475C,1491C,1946C,1979C,1985C,1990C,1996 'supertokens-auth-react':1302C,1314C 'supertokens-nod':620C,627C,634C,953C,960C,967C 'supertokens.com':192C,411C,1981C,1988C,1993C 'supertokens.com/)':1980C 'supertokens.com/discord).':1987C 'supertokens.com/docs/guides).':1992C 'supertokens.com/docs/thirdpartyemailpassword/introduction)':191C 'supertokens.com/docs/thirdpartyemailpassword/nextjs/about).':410C 'tabl':308C,323C,327C,364C,366C,1412C,1432C,1556C,1587C,1741C,1957C,1974C 'talk':860C 'test':1931C 'text':351C,357C,1676C,1686C 'thirdpartyemailpassword':1299C 'thirdpartyemailpasswordauth':1300C,1335C 'thirdpartyemailpasswordnod':618C,951C 'thirdpartyemailpasswordnode.init':657C,994C 'thirdpartysigninuppost':1002C,1010C,1214C 'throw':1017C,1109C,1130C,1197C 'token':701C,734C,743C,746C,785C,798C,835C,849C,869C,880C,887C,1074C,1082C,1162C,1170C,1423C 'tri':1901C 'true':752C,1203C,1377C,1683C 'try.supertokens.com':654C,991C 'ts':615C,826C,948C 'tsx':1282C 'two':345C,908C 'typeinput':632C,648C,965C,985C 'undefin':1016C,1129C 'up/in':1006C,1026C 'url':842C 'us':568C,793C,1348C,1653C 'use':27C,53C,106C,137C,149C,183C,202C,244C,477C,582C,684C,759C,1401C,1490C,1503C,1510C,1522C 'useeffect':1287C,1384C,1506C 'user':24C,60C,75C,103C,155C,175C,210C,225C,329C,348C,365C,449C,457C,508C,516C,535C,555C,563C,673C,690C,693C,777C,806C,895C,914C,935C,1049C,1061C,1085C,1099C,1103C,1149C,1173C,1187C,1191C,1220C,1237C,1257C,1277C,1340C,1352C,1363C,1396C,1408C,1426C,1431C,1445C,1449C,1498C,1517C,1528C,1555C,1718C,1740C,1784C,1795C,1810C,1836C,1882C,1893C,1912C,1966C,1968C,1973C 'useremail':1381C,1483C 'userid':510C,674C,723C,779C,943C,1091C,1179C,1251C,1367C,1373C,1397C,1418C,1451C,1476C,1477C,1500C,1533C,1573C,1583C,1600C,1616C,1685C,1702C,1845C 'usesessioncontext':1312C,1374C,1492C 'usest':1286C,1383C 'utils/supabase.ts':821C,827C 'valid':688C 'verif':581C 'via':1007C,1119C 'want':453C,589C,665C 'way':909C 'web':135C 'websit':399C,1984C 'whether':1716C,1791C,1834C,1889C 'whilst':804C 'whose':773C,1433C 'within':1355C 'would':463C 'wrap':1330C 'write':80C,230C,1662C 'www.supertokens.com':88C","video":null,"call_to_action_link":null,"featured":false},"overview":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nconst chCodeConfig = {\n  \"staticMediaQuery\": \"not screen, (max-width: 768px)\",\n  \"lineNumbers\": true,\n  \"showCopyButton\": true,\n  \"themeName\": \"supabase\"\n};\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    h2: \"h2\",\n    a: \"a\",\n    h3: \"h3\",\n    code: \"code\",\n    img: \"img\",\n    ul: \"ul\",\n    li: \"li\"\n  }, _provideComponents(), props.components), {CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"SuperTokens is an open source authentication solution which provides many stratergies for authenticating and managing users. You can use the managed service for easy setup or you can self host the solution to have complete control over your data.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"With SuperTokens, Supabase can be used to store and authorize access to user data. Supabase makes it simple to setup Row Level Security(RLS) policies which ensure users can only read and write data that belongs to them.\"\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Documentation\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://www.supertokens.com\",\n        children: \"SuperTokens\"\n      }), \" is an open source authentication solution which provides many stratergies for authenticating and managing users. You can use the managed service for easy setup or you can self host the solution to have complete control over your data.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In this guide we will build a simple web application using SuperTokens, Supabase, and Next.js. You will be able to sign up using SuperTokens and your email and user ID will be stored in Supabase. Once authenticated the frontend will be able to query Supabase and retrieve the user's email. Our example app will be using the \", _jsx(_components.a, {\n        href: \"https://supertokens.com/docs/thirdpartyemailpassword/introduction\",\n        children: \"Email-Password and Social Login\"\n      }), \" recipe for authentication and session management.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"We will use Supabase to store and authorize access to user data. Supabase makes it simple to setup Row Level Security(RLS) policies which ensure users can only read and write data that belongs to them.\"\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"Demo App\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You can find a demo app using SuperTokens, Supabase and Nexts.js on \", _jsx(_components.a, {\n        href: \"https://github.com/supertokens/supertokens-auth-react/tree/master/examples/with-supabase\",\n        children: \"Github\"\n      })]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 1: Create a new Supabase project\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"From your \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard/\",\n        children: \"Supabase dashboard\"\n      }), \", click \", _jsx(_components.code, {\n        children: \"New project\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Enter a \", _jsx(_components.code, {\n        children: \"Name\"\n      }), \" for your Supabase project.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Enter a secure \", _jsx(_components.code, {\n        children: \"Database Password\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Select the same \", _jsx(_components.code, {\n        children: \"Region\"\n      }), \" you host your app's backend in.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Click \", _jsx(_components.code, {\n        children: \"Create new project\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/supabase_dashboard_create.png\",\n        alt: \"New Supabase project settings\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 2: Creating tables in Supabase\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"From the sidebar menu in the \", _jsx(_components.a, {\n        href: \"https://supabase.com/dashboard/\",\n        children: \"Supabase dashboard\"\n      }), \", click \", _jsx(_components.code, {\n        children: \"Table editor\"\n      }), \", then \", _jsx(_components.code, {\n        children: \"New table\"\n      }), \".\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Enter \", _jsx(_components.code, {\n        children: \"users\"\n      }), \" as the \", _jsx(_components.code, {\n        children: \"Name\"\n      }), \" field.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Select \", _jsx(_components.code, {\n        children: \"Enable Row Level Security (RLS)\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Remove the default columns\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Create two new columns:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.code, {\n          children: \"user_id\"\n        }), \" as \", _jsx(_components.code, {\n          children: \"text\"\n        }), \" as primary key\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.code, {\n          children: \"email\"\n        }), \" as \", _jsx(_components.code, {\n          children: \"text\"\n        })]\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Click \", _jsx(_components.code, {\n        children: \"Save\"\n      }), \" to create the new table.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/supabase_table_create.png\",\n        alt: \"Users table\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 3: Setup your Next.js App with SuperTokens.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Since the scope of this guide is limited to the integration between SuperTokens and Supabase, you can refer to the SuperTokens website to see \", _jsx(_components.a, {\n        href: \"https://supertokens.com/docs/thirdpartyemailpassword/nextjs/about\",\n        children: \"how to setup your Next.js app with SuperTokens\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Once you finish setting up your app, you will be greeted with the following screen\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/supertokens_thirdpartyemailpassword_auth_screen.png\",\n        alt: \"SuperTokens Auth Screen\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 4: Creating a Supabase JWT to access Supabase\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In our Nextjs app when a user signs up, we want to store the user's email in Supabase. We would then retrieve this email from Supabase and display it on our frontend.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To use the Supabase client to query the database we will need to create a JWT signed with your Supabase app's signing secret. This JWT will also need to contain the user's userId so Supabase knows an authenticated user is making the request.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To create this flow we will need to modify SuperTokens so that, when a user signs up or signs in, a JWT signed with Supabase's signing secret is created and attached to the user's session. Attaching the JWT to the user's session will allow us to retrieve the Supabase JWT on the frontend and backend (post session verification), using which we can query Supabase.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We want to create a Supabase JWT when we are creating a SuperTokens' session. This can be done by overriding the \", _jsx(_components.code, {\n        children: \"createNewSession\"\n      }), \" function in your backend config.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// config/backendConfig.ts\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" ThirdPartyEmailPasswordNode \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"supertokens-node/recipe/thirdpartyemailpassword\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" SessionNode \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"supertokens-node/recipe/session\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { TypeInput } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"supertokens-node/lib/build/types\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { appInfo } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"./appInfo\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" jwt \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"jsonwebtoken\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"let \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"backendConfig \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" ()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"TypeInput \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        framework: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"express\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        supertokens: {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"            connectionURI: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"https://try.supertokens.com\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        appInfo,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        recipeList: [\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"            ThirdPartyEmailPasswordNode.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"init\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"...\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"}),\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"            SessionNode.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"init\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                override: {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                    functions\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \": (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"originalImplementation\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                        return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                            ...\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"originalImplementation,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                            // We want to create a JWT which contains the users userId signed with Supabase's secret so\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                            // it can be used by Supabase to validate the user when retrieving user data from their service.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                            // We store this token in the accessTokenPayload so it can be accessed on the frontend and on the backend.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                            createNewSession\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"async function\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"input\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \") {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"payload \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                    userId: input.userId,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                    exp: Math.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"floor\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(Date.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"now\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"() \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"/ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"1000\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"+ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"60 \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"* \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"60\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                };\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"supabase_jwt_token \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" jwt.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"sign\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(payload, process.env.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"SUPABASE_SIGNING_SECRET\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \");\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                input.accessTokenPayload \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                    ...\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"input.accessTokenPayload,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                    supabase_token: supabase_jwt_token,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                };\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                return await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" originalImplementation.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"createNewSession\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(input);\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                            },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                        };\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                    },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"            }),\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        ],\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        isInServerlessEnv: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"true\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    };\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"};\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"ts\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"As seen above, we will be using the \", _jsx(_components.code, {\n        children: \"jsonwebtoken\"\n      }), \" library to create a JWT signed with Supabase's signing secret whose payload contains the user's userId.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"We will be storing this token in the \", _jsx(_components.code, {\n        children: \"accessTokenPayload\"\n      }), \" which will essentially allow us to access the \", _jsx(_components.code, {\n        children: \"supabase_token\"\n      }), \" on the frontend and backend whilst the user is logged in.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 5: Creating a Supabase client\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Create a new file called \", _jsx(_components.code, {\n        children: \"utils/supabase.ts\"\n      }), \" and add the following:\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// utils/supabase.ts\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { createClient } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'@supabase/supabase-js'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"getSupabase \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"access_token\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"supabase \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"createClient\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    process.env.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"NEXT_PUBLIC_SUPABASE_URL\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    process.env.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"NEXT_PUBLIC_SUPABASE_KEY\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  )\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  supabase.auth.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"session \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" () \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" ({\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    access_token,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  })\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { getSupabase }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"ts\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"This will be our client for talking to Supabase. We can pass it an \", _jsx(_components.code, {\n        children: \"access_token\"\n      }), \" and it will be attached to our request. This \", _jsx(_components.code, {\n        children: \"access_token\"\n      }), \" is the same as the \", _jsx(_components.code, {\n        children: \"supabase_token\"\n      }), \" we had created earlier.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 6: Inserting users into Supabase when they sign up:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"In our example app there are two ways for signing up a user. Email-Password and Social Login based authentication. We will need to override both these APIs such that when a user signs up, their email mapped to their userId is stored in Supabase.\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// config/backendConfig.ts\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" ThirdPartyEmailPasswordNode \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"supertokens-node/recipe/thirdpartyemailpassword\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" SessionNode \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"supertokens-node/recipe/session\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { TypeInput } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"supertokens-node/lib/build/types\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { appInfo } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"./appInfo\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" jwt \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"jsonwebtoken\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { getSupabase } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"../utils/supabase\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"let \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"backendConfig \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" ()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"TypeInput \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        framework: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"express\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        supertokens: {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"            connectionURI: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"https://try.supertokens.com\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        appInfo,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        recipeList: [\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"            ThirdPartyEmailPasswordNode.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"init\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                providers: [\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"...\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"],\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                override: {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                    apis\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \": (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"originalImplementation\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \") \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                        return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                            ...\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"originalImplementation,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                            // the thirdPartySignInUpPost function handles sign up/in via Social login\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                            thirdPartySignInUpPOST\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"async function\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"input\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \") {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (originalImplementation.thirdPartySignInUpPOST \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=== \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"undefined\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \") {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                    throw \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"Error\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"Should never come here\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \");\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                // call the sign up/in api for social login\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                let\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" response \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" originalImplementation.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"thirdPartySignInUpPOST\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(input);\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                // check that there is no issue with sign up and that a new user is created\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (response.status \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=== \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"OK\\\" \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"\u0026\u0026\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" response.createdNewUser) {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                    // retrieve the accessTokenPayload from the user's session\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                    const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"accessTokenPayload \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" response.session.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"getAccessTokenPayload\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"();\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                    // create a supabase client with the supabase_token from the accessTokenPayload\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                    const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"supabase \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"getSupabase\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(accessTokenPayload.supabase_token);\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                    // store the user's email mapped to their userId in Supabase\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                    const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"error\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                        .\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"users\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                        .\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"insert\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({ email: response.user.email, user_id: response.user.id });\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                    if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (error \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"!== \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"null\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \") {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                        throw\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" error;\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                    }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" response;\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                            },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                            // the emailPasswordSignUpPOST function handles sign up via Email-Password\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                            emailPasswordSignUpPOST\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"async function\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"input\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \") {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (originalImplementation.emailPasswordSignUpPOST \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=== \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"undefined\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \") {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                    throw \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"Error\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"Should never come here\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \");\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                let\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" response \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" originalImplementation.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"emailPasswordSignUpPOST\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(input);\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (response.status \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=== \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"OK\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \") {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                    // retrieve the accessTokenPayload from the user's session\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                    const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"accessTokenPayload \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" response.session.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"getAccessTokenPayload\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"();\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                    // create a supabase client with the supabase_token from the accessTokenPayload\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                    const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"supabase \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"getSupabase\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(accessTokenPayload.supabase_token);\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                    // store the user's email mapped to their userId in Supabase\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                    const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"error\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                        .\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"users\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                        .\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"insert\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({ email: response.user.email, user_id: response.user.id });\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                    if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (error \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"!== \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"null\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \") {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                        throw\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" error;\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                    }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                                return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" response;\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                            },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                        };\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                    },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"                },\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"            }),\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"            SessionNode.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"init\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"({\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"...\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"}),\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        ],\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        isInServerlessEnv: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"true\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    };\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"};\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"ts\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"As seen above, we will be overriding the \", _jsx(_components.code, {\n        children: \"emailPasswordSignUpPOST\"\n      }), \" and \", _jsx(_components.code, {\n        children: \"thirdPartySignInUpPOST\"\n      }), \" APIs such that if a user signs up, we retrieve the Supabase JWT (which we created in the \", _jsx(_components.code, {\n        children: \"createNewSession\"\n      }), \" function) from the user's accessTokenPayload and send a request to Supabase to insert the email-userid mapping.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 7: Retrieving the user's email on the frontend\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Now that our backend is setup we can modify our frontend to retrieve the user's email from Supabase.\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// pages/index.tsx\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" React, { useState, useEffect } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'react'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" Head \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'next/head'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" styles \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'../styles/Home.module.css'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" ThirdPartyEmailPassword, {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  ThirdPartyEmailPasswordAuth,\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"} \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'supertokens-auth-react/recipe/thirdpartyemailpassword'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" dynamic \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'next/dynamic'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { useSessionContext } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'supertokens-auth-react/recipe/session'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"import\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { getSupabase } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'../utils/supabase'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"export default function \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"Home\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"() \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-3)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    // We will wrap the ProtectedPage component with ThirdPartyEmailPasswordAuth so only an\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    // authenticated user can access it. This will also allow us to access the users session information\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    // within the component.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"ThirdPartyEmailPasswordAuth\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"ProtectedPage\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \" /\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"ThirdPartyEmailPasswordAuth\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  )\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"function \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"ProtectedPage\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"() {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  // retrieve the authenticated user's accessTokenPayload and userId from the sessionContext\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"accessTokenPayload\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"userId\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"useSessionContext\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (sessionContext.loading \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=== \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"true\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \") {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    return \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"null\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" [\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"userEmail\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"setEmail\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"] \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"useState\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"''\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  useEffect\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(() \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"=\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    async function \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"getUserEmail\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"() {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      // retrieve the supabase client who's JWT contains users userId, this will be\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      // used by supabase to check that the user can only access table entries which contain their own userId\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      const \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"supabase \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"= \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"getSupabase\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(accessTokenPayload.supabase_token)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      // retrieve the user's name from the users table whose email matches the email in the JWT\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-1)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      const\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" { \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"data\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \" } \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"= await\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" supabase.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"from\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'users'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \").\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"select\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'email'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \").\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"eq\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'user_id'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", userId)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      if\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (data.\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"length \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"0\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \") {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        setEmail\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"(data[\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"0\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"].email)\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    getUserEmail\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \"()\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }, [])\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  return\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" (\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"div \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"className\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"styles.container\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Head\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"title\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003eSuperTokens 💫\u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"title\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"link \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"rel\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"icon\\\" \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \"href\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"=\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"/favicon.ico\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \" /\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"Head\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"main \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"className\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"styles.main\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"className\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"={\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"styles.description\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          You are authenticated with SuperTokens! (UserId: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"userId\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \")\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          \u003c\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"br\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \" /\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"          Your email retrieved from Supabase: \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"userEmail\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"        \u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"p\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"      \u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"main\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \u003c/\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"div\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \"\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  )\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"tsx\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"As seen above we will be using SuperTokens \", _jsx(_components.code, {\n        children: \"useSessionContext\"\n      }), \" hook to retrieve the authenticated user's \", _jsx(_components.code, {\n        children: \"userId\"\n      }), \" and \", _jsx(_components.code, {\n        children: \"accessTokenPayload\"\n      }), \". Using React's \", _jsx(_components.code, {\n        children: \"useEffect\"\n      }), \" hook we can use the Supabase client to retrieve the user's email from Supabase using the JWT retrieved from the user's \", _jsx(_components.code, {\n        children: \"accessTokenPayload\"\n      }), \" and their \", _jsx(_components.code, {\n        children: \"userId\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 8: Create Policies to enforce Row Level Security for Select and Insert requests\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To enforce Row Level Security for the \", _jsx(_components.code, {\n        children: \"Users\"\n      }), \" table we will need to create policies for Select and Insert requests.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"These polices will retrieve the userId from the JWT and check if it matches the userId in the Supabase table\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"To do this we will need a PostgreSQL function to extract the userId from the JWT.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The payload in the JWT will have the following structure:\"\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"// JWT payload\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"{\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    userId,\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    exp\",\n              \"props\": {}\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {}\n            }]\n          }],\n          \"lang\": \"text\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To create the PostgreSQL function, lets navigate back to the Supabase dashboard, select \", _jsx(_components.code, {\n        children: \"SQL\"\n      }), \" from the sidebar menu, and click \", _jsx(_components.code, {\n        children: \"New query\"\n      }), \". This will create a new query called \", _jsx(_components.code, {\n        children: \"new sql snippet\"\n      }), \", which will allow us to run any SQL against our Postgres database.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Write the following and click \", _jsx(_components.code, {\n        children: \"Run\"\n      }), \".\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"create or replace function \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"auth\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-5)\"\n                }\n              }\n            }, {\n              \"content\": \".\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"user_id\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"() \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"returns text as\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" $$\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  select \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"nullif\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-2)\"\n                }\n              }\n            }, {\n              \"content\": \"(current_setting(\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"'request.jwt.claims'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", true)::\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"json-\u003e\u003e\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \"'userId'\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \", \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"''\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \")::\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"text\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \";\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"$$ \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"language sql\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-7)\"\n                }\n              }\n            }, {\n              \"content\": \" stable;\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"sql\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"This will create a function called \", _jsx(_components.code, {\n        children: \"auth.user_id()\"\n      }), \", which will inspect the \", _jsx(_components.code, {\n        children: \"userId\"\n      }), \" field of our JWT payload.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"SELECT query policy\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Our first policy will check whether the user is the owner of the email.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Select \", _jsx(_components.code, {\n        children: \"Authentication\"\n      }), \" from the Supabase sidebar menu, click \", _jsx(_components.code, {\n        children: \"Policies\"\n      }), \", and then \", _jsx(_components.code, {\n        children: \"New Policy\"\n      }), \" on the \", _jsx(_components.code, {\n        children: \"Users\"\n      }), \" table.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/create_policy.png\",\n        alt: \"Create new policy\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"From the modal, select \", _jsx(_components.code, {\n        children: \"Create a policy from scratch\"\n      }), \" and add the following.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/policy_config_select.png\",\n        alt: \"Policy settings for SELECT\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"This policy is calling the PostgreSQL function we just created to get the currently logged in user's ID \", _jsx(_components.code, {\n        children: \"auth.user_id()\"\n      }), \" and checking whether this matches the \", _jsx(_components.code, {\n        children: \"user_id\"\n      }), \" column for the current \", _jsx(_components.code, {\n        children: \"email\"\n      }), \". If it does, then it will allow the user to select it, otherwise it will continue to deny.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Click \", _jsx(_components.code, {\n        children: \"Review\"\n      }), \" and then \", _jsx(_components.code, {\n        children: \"Save policy\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"INSERT query policy\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Our second policy will check whether the \", _jsx(_components.code, {\n        children: \"user_id\"\n      }), \" being inserted is the same as the \", _jsx(_components.code, {\n        children: \"userId\"\n      }), \" in the JWT.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Create another policy and add the following:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/policy_config_insert.png\",\n        alt: \"Policy settings for INSERT\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Similar to the previous policy we are calling the PostgreSQL function we created to get the currently logged in user's ID \", _jsx(_components.code, {\n        children: \"auth.user_id()\"\n      }), \" and check whether this matches the \", _jsx(_components.code, {\n        children: \"user_id\"\n      }), \" column for the row we are trying to insert. If it does, then it will allow the user to insert the row, otherwise it will continue to deny.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Click \", _jsx(_components.code, {\n        children: \"Review\"\n      }), \" and then \", _jsx(_components.code, {\n        children: \"Save policy\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Step 9: Test your changes\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"You can now sign up and you should see the following screen:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/supabase_app_authenticated_screen.png\",\n        alt: \"SuperTokens App Authenticated\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"If you navigate to your table you should see a new row with the user's \", _jsx(_components.code, {\n        children: \"user_id\"\n      }), \" and \", _jsx(_components.code, {\n        children: \"email\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/supertokens/documentation/table_with_user.png\",\n        alt: \"Supabase Users table\"\n      })\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Resources\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://supertokens.com/\",\n          children: \"SuperTokens\"\n        }), \" official website.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://supertokens.com/discord\",\n          children: \"SuperTokens community\"\n        }), \".\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.a, {\n          href: \"https://supertokens.com/docs/guides\",\n          children: \"SuperTokens documentation\"\n        }), \".\"]\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  chCodeConfig,\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{}}},"__N_SSG":true},"page":"/partners/integrations/[slug]","query":{"slug":"supertokens"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"isExperimentalCompile":false,"gsp":true,"scriptLoader":[]}</script></body></html>