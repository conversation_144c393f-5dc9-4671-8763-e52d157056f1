<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8" data-next-head=""/><meta name="viewport" content="width=device-width, initial-scale=1.0" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="57x57" href="../../favicon/apple-icon-57x57.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="60x60" href="../../favicon/apple-icon-60x60.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="72x72" href="../../favicon/apple-icon-72x72.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="76x76" href="../../favicon/apple-icon-76x76.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="114x114" href="../../favicon/apple-icon-114x114.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="120x120" href="../../favicon/apple-icon-120x120.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="144x144" href="../../favicon/apple-icon-144x144.png" data-next-head=""/><link rel="apple-touch-icon-precomposed" sizes="152x152" href="../../favicon/apple-icon-152x152.png" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-16x16.png" sizes="16x16" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-32x32.png" sizes="32x32" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-48x48.png" sizes="48x48" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-96x96.png" sizes="96x96" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-128.png" sizes="128x128" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-180x180.png" sizes="180x180" data-next-head=""/><link rel="icon" type="image/png" href="../../favicon/favicon-196x196.png" sizes="196x196" data-next-head=""/><meta name="application-name" content="Supabase" data-next-head=""/><meta name="msapplication-TileColor" content="#1E1E1E" data-next-head=""/><meta name="msapplication-TileImage" content="/favicon/mstile-144x144.png" data-next-head=""/><meta name="msapplication-square70x70logo" content="/favicon/mstile-70x70.png" data-next-head=""/><meta name="msapplication-square150x150logo" content="/favicon/mstile-150x150.png" data-next-head=""/><meta name="msapplication-wide310x150logo" content="/favicon/mstile-310x150.png" data-next-head=""/><meta name="msapplication-square310x310logo" content="/favicon/mstile-310x310.png" data-next-head=""/><meta name="theme-color" content="#1E1E1E" data-next-head=""/><link rel="shortcut icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="icon" type="image/x-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="apple-touch-icon" href="../../favicon/favicon.ico" data-next-head=""/><link rel="alternate" type="application/rss+xml" href="../../rss.xml" data-next-head=""/><link rel="manifest" href="../../favicon/manifest.json" data-next-head=""/><meta name="msapplication-config" content="/favicon/browserconfig.xml" data-next-head=""/><meta name="twitter:card" content="summary_large_image" data-next-head=""/><meta name="twitter:site" content="@supabase" data-next-head=""/><meta name="twitter:creator" content="@supabase" data-next-head=""/><meta property="og:type" content="website" data-next-head=""/><meta property="og:image:alt" content="Supabase Og Image" data-next-head=""/><meta property="og:image:width" content="800" data-next-head=""/><meta property="og:image:height" content="600" data-next-head=""/><meta property="og:site_name" content="Supabase" data-next-head=""/><title data-next-head="">Loops | Works With Supabase</title><meta name="robots" content="index,follow" data-next-head=""/><meta name="description" content="Configure your Supabase account to send authentication emails with Loops." data-next-head=""/><meta property="og:title" content="Loops | Works With Supabase" data-next-head=""/><meta property="og:description" content="Configure your Supabase account to send authentication emails with Loops." data-next-head=""/><meta property="og:url" content="https://supabase.com/partners/integrations/loops" data-next-head=""/><meta property="og:image" content="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/logo.png?t=2024-11-18T09%3A19%3A41.390Z" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=128&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--light.daaeffd3.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link rel="preload" as="image" imageSrcSet="/_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=256&amp;q=75 1x, /_next/image?url=https%3A%2F%2Ffrontend-assets.supabase.com%2Fwww%2F0c0be801ae1d%2F_next%2Fstatic%2Fmedia%2Fsupabase-logo-wordmark--dark.b36ebb5f.png&amp;w=384&amp;q=75 2x" data-next-head=""/><link data-next-font="" rel="preconnect" href="../../index.html"/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/209a8cfde55b93a5.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" data-n-g=""/><link rel="preload" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" as="style"/><link rel="stylesheet" href="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/css/3cd83cfe34ca397f.css?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/polyfills-42372ed130431b0a.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf"></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/webpack-b68dbddf15a43db4.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/framework-1951ad69a698e1a3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/main-13fad21dfbeaeda1.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/_app-48a4cb2f7ca79c98.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/afdc307b-c3b7a0c5072a7097.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/3249-ea10c73f6f8c2a09.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6426-b8fe008ccfbb4bfe.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/6926-6f64fd9d29a7f9f2.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2202-ee750f2911d03c44.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/2263-07e8ebb96cc2b014.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/905-eaab19e781f25bb3.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/419-f7964414173f0ef7.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/8658-639e1bb66549b52b.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/chunks/pages/partners/integrations/%5Bslug%5D-cff8a94339d02c6e.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_buildManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script><script src="https://frontend-assets.supabase.com/www/0c0be801ae1d/_next/static/kQ-hlwlsb4A882RHzOZEr/_ssgManifest.js?dpl=dpl_2EGqSeqqYfyoVQfWw4bcheDwZGhf" defer=""></script></head><body><div id="__next"><script type="application/json" data-flag-values="true">{}</script><script>!function(){try{var d=document.documentElement,n='data-theme',s='setAttribute';var e=localStorage.getItem('theme');if('system'===e||(!e&&false)){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){d.style.colorScheme = 'dark';d[s](n,'dark')}else{d.style.colorScheme = 'light';d[s](n,'light')}}else if(e){d[s](n,e|| '')}else{d[s](n,'dark')}if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'dark'}catch(e){}}()</script><div class="sticky top-0 z-40 transform" style="transform:translate3d(0,0,999px)"><div class="absolute inset-0 h-full w-full bg-background/90 dark:bg-background/95 !opacity-100 transition-opacity"></div><nav class="relative z-40 border-default border-b backdrop-blur-sm transition-opacity"><div class="relative flex justify-between h-16 mx-auto lg:container lg:px-16 xl:px-20"><div class="flex items-center px-6 lg:px-0 flex-1 sm:items-stretch justify-between"><div class="flex items-center"><div class="flex items-center flex-shrink-0"><a class="block w-auto h-6 focus-visible:ring-2 focus-visible:outline-none focus-visible:ring-foreground-lighter focus-visible:ring-offset-4 focus-visible:ring-offset-background-alternative focus-visible:rounded-sm" type="button" id="radix-:R1aqela6:" aria-haspopup="menu" aria-expanded="false" data-state="closed" href="../../index.html"><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="124" height="24" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a></div><nav aria-label="Main" data-orientation="horizontal" dir="ltr" class="relative z-10 flex-1 items-center justify-center hidden pl-8 sm:space-x-4 lg:flex h-16"><div style="position:relative"><ul data-orientation="horizontal" class="group flex flex-1 list-none items-center justify-center space-x-1" dir="ltr"><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R1eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R1eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Product<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R2eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R2eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Developers<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><button id="radix-:R2aqela6:-trigger-radix-:R3eaqela6:" data-state="closed" aria-expanded="false" aria-controls="radix-:R2aqela6:-content-radix-:R3eaqela6:" class="group relative justify-center cursor-pointer inline-flex items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-sm leading-4 py-2 !bg-transparent hover:text-brand-link data-[state=open]:!text-brand-link data-[radix-collection-item]:focus-visible:ring-2 data-[radix-collection-item]:focus-visible:ring-foreground-lighter data-[radix-collection-item]:focus-visible:text-foreground px-2 h-auto" data-radix-collection-item="">Solutions<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180" aria-hidden="true"><path d="m6 9 6 6 6-6"></path></svg></button></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../pricing.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Pricing</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../docs.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Docs</p></div></div></a></li><li class="text-sm font-medium"><a class="group/menu-item flex items-center text-sm hover:text-foreground select-none gap-3 rounded-md p-2 leading-none no-underline outline-none focus-visible:ring-2 focus-visible:ring-foreground-lighter group-hover:bg-transparent text-foreground focus-visible:text-brand-link" data-radix-collection-item="" href="../../blog.html"><div class="flex flex-col justify-center"><div class="flex items-center gap-1"><p class="leading-snug text-foreground group-hover/menu-item:text-brand-link">Blog</p></div></div></a></li></ul></div><div class="absolute left-0 top-full flex justify-center"></div></nav></div><div class="flex items-center gap-2 opacity-0 animate-fade-in !scale-100 delay-300"><a href="https://github.com/supabase/supabase" target="_blank" data-size="tiny" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border hover:bg-surface-300 shadow-none focus-visible:outline-border-strong data-[state=open]:bg-surface-300 data-[state=open]:outline-border-strong border-transparent text-xs px-2.5 py-1 h-[26px] hidden group lg:flex text-foreground-light hover:text-foreground"><span class="truncate"><span class="flex items-center gap-1"><svg class="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.5 2.22168C5.23312 2.22168 2.58496 4.87398 2.58496 8.14677C2.58496 10.7642 4.27962 12.9853 6.63026 13.7684C6.92601 13.8228 7.03366 13.6401 7.03366 13.4827C7.03366 13.3425 7.02893 12.9693 7.02597 12.4754C5.38041 12.8333 5.0332 11.681 5.0332 11.681C4.76465 10.996 4.37663 10.8139 4.37663 10.8139C3.83954 10.4471 4.41744 10.4542 4.41744 10.4542C5.01072 10.4956 5.32303 11.0647 5.32303 11.0647C5.85065 11.9697 6.70774 11.7082 7.04431 11.5568C7.09873 11.1741 7.25134 10.9132 7.42051 10.7654C6.10737 10.6157 4.72621 10.107 4.72621 7.83683C4.72621 7.19031 4.95689 6.66092 5.33486 6.24686C5.27394 6.09721 5.07105 5.49447 5.39283 4.67938C5.39283 4.67938 5.88969 4.51967 7.01947 5.28626C7.502 5.15466 7.99985 5.08763 8.5 5.08692C9.00278 5.08929 9.50851 5.15495 9.98113 5.28626C11.1103 4.51967 11.606 4.67879 11.606 4.67879C11.9289 5.49447 11.7255 6.09721 11.6651 6.24686C12.0437 6.66092 12.2732 7.19031 12.2732 7.83683C12.2732 10.1129 10.8897 10.6139 9.5724 10.7606C9.78475 10.9434 9.97344 11.3048 9.97344 11.8579C9.97344 12.6493 9.96634 13.2887 9.96634 13.4827C9.96634 13.6413 10.0728 13.8258 10.3733 13.7678C11.5512 13.3728 12.5751 12.6175 13.3003 11.6089C14.0256 10.6002 14.4155 9.38912 14.415 8.14677C14.415 4.87398 11.7663 2.22168 8.5 2.22168Z" fill="currentColor"></path></svg>86.4K</span></span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border text-foreground bg-alternative dark:bg-muted hover:bg-selection border-strong hover:border-stronger focus-visible:outline-brand-600 data-[state=open]:bg-selection data-[state=open]:outline-brand-600 data-[state=open]:border-button-hover text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Sign in</span></a><a data-size="tiny" type="button" class="relative justify-center cursor-pointer items-center space-x-2 text-center font-regular ease-out duration-200 rounded-md outline-none transition-all outline-0 focus-visible:outline-4 focus-visible:outline-offset-1 border bg-brand-400 dark:bg-brand-500 hover:bg-brand/80 dark:hover:bg-brand/50 text-foreground border-brand-500/75 dark:border-brand/30 hover:border-brand-600 dark:hover:border-brand focus-visible:outline-brand-600 data-[state=open]:bg-brand-400/80 dark:data-[state=open]:bg-brand-500/80 data-[state=open]:outline-brand-600 text-xs px-2.5 py-1 h-[26px] hidden lg:block" href="../../dashboard/org.html"><span class="truncate">Start your project</span></a></div></div><div class="inset-y-0 flex mr-2 items-center px-4 lg:hidden"><button class="text-foreground-lighter focus:ring-brand bg-transparent hover:text-foreground-light transition-colors hover:bg-overlay inline-flex items-center justify-center rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-inset" aria-expanded="false"><span class="sr-only">Open main menu</span><svg class="block w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg><svg class="hidden w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg></button></div></div></nav></div><main class="relative min-h-screen"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20"><div class="col-span-12 mx-auto mb-2 max-w-5xl space-y-10 lg:col-span-2"><a class="text-foreground hover:text-foreground-lighter flex cursor-pointer items-center transition-colors" href="../integrations.html"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-left"><path d="m15 18-6-6 6-6"></path></svg>Back</a><div class="flex items-center space-x-4"><img alt="Loops" loading="lazy" width="56" height="56" decoding="async" data-nimg="1" class="bg-surface-200 flex-shrink-f0 h-14 w-14 rounded-full" style="color:transparent" srcSet="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Floops%2Flogo.png%3Ft%3D2024-11-18T09%253A19%253A41.390Z&amp;w=64&amp;q=75 1x, https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Floops%2Flogo.png%3Ft%3D2024-11-18T09%253A19%253A41.390Z&amp;w=128&amp;q=75 2x" src="https://supabase.com/_next/image?url=https%3A%2F%2Fobuldanrptloktxcffvn.supabase.co%2Fstorage%2Fv1%2Fobject%2Fpublic%2Fimages%2Fintegrations%2Floops%2Flogo.png%3Ft%3D2024-11-18T09%253A19%253A41.390Z&amp;w=128&amp;q=75"/><h1 class="h1" style="margin-bottom:0">Loops</h1></div><div class="bg-gradient-to-t from-background-alternative to-background border-b p-6 [&amp;_.swiper-container]:overflow-visible" style="margin-left:calc(50% - 50vw);margin-right:calc(50% - 50vw)"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 !py-0 !px-3 lg:!px-12 xl:!p-0 mx-auto max-w-5xl"><div class="swiper"><div class="swiper-wrapper"></div></div></div></div><div class="grid gap-y-12 lg:grid-cols-8 lg:space-x-12"><div class="lg:col-span-5 overflow-hidden"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Overview</h2><div class="prose"><style>[data-ch-theme="supabase"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }</style>
<p>Loops is an email platform for software companies letting you send your product, marketing, and transactional email from one simple interface.</p>
<p>This integration allows you to send Supabase Authentication emails over Loops&#x27; SMTP service.</p>
<p>There are two big benefits to using Loops for sending your Supabase emails:</p>
<ul>
<li><strong>More control over design</strong>: You can use <a href="https://loops.so/docs/creating-emails/editor">Loops&#x27; design editor</a> to create (and then easily edit) beautiful transactional emails instead of having to code them with HTML.</li>
<li><strong>Better visibility of sent email</strong>: You get full visibility on which emails are being sent, when, and to whom in your Loops account (something not offered in Supabase).</li>
</ul>
<h3>1. Set up Loops SMTP in Supabase</h3>
<p>Go to your Authentication settings in Supabase (<strong>Project Settings -&gt; Authentication</strong>) to tell Supabase to send emails using Loops&#x27; SMTP service.</p>
<p>Scroll down and toggle the <strong>Enable Custom SMTP</strong> option on.</p>
<p>In the Sender details section, you will need to enter some values into the &quot;Sender email&quot; and &quot;Sender name&quot; fields. However, <em>these values will always be overwritten by the values set in your Loops templates</em> from the next step.</p>
<p>In the <strong>SMTP Provider Settings</strong> section enter the following data:</p>
<table><thead><tr><th>Field</th><th>Value</th></tr></thead><tbody><tr><td>Host</td><td><code>smtp.loops.so</code></td></tr><tr><td>Port number</td><td><code>587</code></td></tr><tr><td>Username</td><td><code>loops</code></td></tr><tr><td>Password</td><td>An API key copied from your <a href="https://app.loops.so/settings?page=api">API settings</a> in Loops</td></tr></tbody></table>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/supabase-smtp-settings.png" alt=""/></p>
<p>Note that for the interval and rate limit settings, you will be bound by Loops&#x27; <a href="https://loops.so/docs/api-reference/intro#rate-limiting">API rate limit</a> of 10 requests per second.</p>
<p>One final step is to check that the &quot;Confirm email&quot; toggle is turned on in the Email section in <strong>Authentication -&gt; Providers</strong>.</p>
<h3>2. Create Transactional emails in Loops</h3>
<p>Next, create new transactional emails for the emails listed in Supabase (<strong>Authentication -&gt; Email Templates</strong>). You need to create both <strong>Confirm signup</strong> and <strong>Magic Link</strong> emails to be able to properly set up the integration.</p>
<ul>
<li>Confirm signup (required)</li>
<li>Invite user</li>
<li>Magic Link (required)</li>
<li>Change Email Address</li>
<li>Reset Password</li>
</ul>
<p>Note that if a Supabase user has not previously confirmed their email, they will be sent a <strong>Confirm signup</strong> email when you request a <strong>Magic Link</strong> email.</p>
<p>To create new transactional emails, go to the <a href="https://app.loops.so/transactional">Transactional page</a> in Loops and click <strong>New</strong>. Alternatively, you can select one of our many ready-made templates from the <a href="https://app.loops.so/templates">Templates page</a>.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/supabase-template.png" alt="Supabase template in the editor"/></p>
<p>You can then use <a href="https://loops.so/docs/creating-emails/editor">the Loops editor</a> to create nicely-designed templates or make them as simple as you like.</p>
<p>You can even <a href="https://loops.so/docs/creating-emails/styles#saved-styles">save styles</a> so you can easily apply consistent branding to all of your emails.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/supabase-editor.png" alt="Saved styles"/></p>
<p>For each Loops template you create, you need to <a href="https://loops.so/docs/creating-emails/personalizing-emails#add-dynamic-content-to-emails">add data variables</a>, which allow data from Supabase to be inserted into each email.</p>
<p>For example, you could add a <code>confirmationUrl</code> data variable that you can map to the <code>{{ .ConfirmationURL }}</code> value from Supabase.</p>
<p>You can also build URLs by including values like <code>{{ .SiteUrl }}</code> or add in a confirmation code using <code>{{ .Token }}</code>.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/supabase-values.png" alt="Supabase values"/></p>
<p>Once you&#x27;re done creating the email and adding data variables, click <strong>Next</strong>. On the next page, click the <strong>Show payload</strong> button to view the API payload for your template. You will need this for the next step.</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/supabase-payload.png" alt="Email payload"/></p>
<p>Make sure to also publish your email! It won&#x27;t send unless it&#x27;s published.</p>
<p><a href="https://loops.so/docs/transactional/guide">Read our detailed guide</a> for sending transactional emails.</p>
<h3>3. Configure email templates in Supabase</h3>
<p>The final step is to make sure your emails in Supabase are configured to send the correct data to Loops.</p>
<p>Loops SMTP integrations work a bit differently than most. Instead of sending a text or HTML email body, you set them up to send API-like data.</p>
<p>In Supabase, go to <strong>Authentication -&gt; Email Templates</strong>, then edit each template to contain the payload as shown in the previous step (you can click the clipboard icon in Loops to copy the full payload).</p>
<p>Once pasted into the Message body, you need to add the Supabase message variables into the payload.</p>
<p>Important: Make sure you set up at least the <strong>Confirm signup</strong> and <strong>Magic Link</strong> templates in Supabase, otherwise emails will not be sent.</p>
<p>Also, any variables added in the <strong>Confirm signup</strong> template need to also be available in <strong>Magic link</strong> email, because Supabase will send a <strong>Confirm signup</strong> email instead of a <strong>Magic Link</strong> email if a user hasn&#x27;t confirmed their email address.</p>
<p>Here is an example <strong>Confirm signup</strong> email template. This payload was copied from the template&#x27;s Publish page in Loops, then the <code>{{ .Email }}</code> and <code>{{ .ConfirmationURL }}</code> Supabase variables were added.</p>
<div class="ch-codeblock not-prose " data-ch-theme="supabase"><div class="ch-code-wrapper ch-code" data-ch-measured="false"><code class="ch-code-scroll-parent"><br/><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>{</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  &quot;transactionalId&quot;: &quot;clvmzp39u035tl50pw7wrl0ri&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  &quot;email&quot;: &quot;{{ .Email }}&quot;,</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  &quot;dataVariables&quot;: {</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>    &quot;confirmationUrl&quot;: &quot;{{ .ConfirmationURL }}&quot;</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>  }</span></div></div><div><span class="ch-code-line-number">_<!-- -->10</span><div style="display:inline-block;margin-left:16px"><span>}</span></div></div><br/></code></div></div>
<p>Here&#x27;s how it looks in the Supabase editor:</p>
<p><img src="https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/supabase-email.png" alt="Supabase editor"/></p>
<p>With the integration now all set up, your Supabase authentication emails will be sent via Loops, giving you more visibility on your email sends and the great addition of being able to build beautiful and easy-to-update emails in the Loops editor.</p>
<p>To view all sends of your transactional emails, click through to the email from the <a href="https://app.loops.so/transactional">Transactional</a> page in Loops, where you&#x27;ll find the Metrics page containing a table showing all sends and some statistics.</p>
<h2>Important notes</h2>
<ul>
<li>You need to add a template in Loops and set up the email in Supabase for at least the <strong>Confirm signup</strong> and <strong>Magic Link</strong> templates.</li>
<li>The subject in Supabase templates is always overwritten by the subject added to the corresponding template in Loops.</li>
<li>The sender name and sender email configured in your Supabase SMTP settings are always overwritten by the sender details added to your templates in Loops.</li>
<li>Any Supabase email not set up with the correct API-like payload will fail to send.</li>
</ul></div></div><div class="lg:col-span-3"><div class="sticky top-20 flex flex-col gap-4"><h2 class="text-foreground" style="font-size:1.5rem;margin-bottom:1rem">Details</h2><div class="text-foreground divide-y"><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Developer</span><span class="text-foreground">Loops</span></div><div class="flex items-center justify-between py-2"><span class="text-lighter">Category</span><a class="text-brand hover:underline transition-colors" href="../integrations.html#messaging">Messaging</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Website</span><a href="https://loops.so" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors">loops.so</a></div><div class="flex items-center justify-between py-2"><span class="text-foreground-lighter">Documentation</span><a href="https://loops.so/docs/integrations/supabase" target="_blank" rel="noreferrer" class="text-brand hover:underline transition-colors"><span class="flex items-center space-x-1"><span>Learn</span><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link"><path d="M15 3h6v6"></path><path d="M10 14 21 3"></path><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path></svg></span></a></div></div><p class="text-foreground-light text-sm">Third-party integrations and docs are managed by Supabase partners.</p></div></div></div></div></div></main><footer class="bg-alternative" aria-labelledby="footerHeading"><h2 id="footerHeading" class="sr-only">Footer</h2><div class="w-full !py-0"><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 grid grid-cols-2 md:flex items-center justify-between text-foreground md:justify-center gap-8 md:gap-16 xl:gap-28 !py-6 md:!py-10 text-sm"><div class="flex flex-col md:flex-row gap-2 md:items-center">We protect your data.<a class="text-brand hover:underline" href="../../security.html">More on Security</a></div><ul class="flex flex-col md:flex-row gap-2 md:gap-8 justify-center md:items-center"><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> SOC2 Type 2<!-- --> <span class="text-foreground-lighter hidden sm:inline">Certified</span></li><li class="flex items-center gap-2 whitespace-nowrap flex-nowrap"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true" class="w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"></path></svg> HIPAA<!-- --> <span class="text-foreground-lighter hidden sm:inline">Compliant</span></li></ul></div><div class="w-full h-px bg-gradient-to-r from-transparent via-border to-transparent"></div></div><div class="sm:py-18 container relative mx-auto px-6 py-16 md:py-24 lg:px-16 lg:py-24 xl:px-20 py-8"><div class="xl:grid xl:grid-cols-7 xl:gap-4"><div class="space-y-8 xl:col-span-2"><a class="w-40" href="../../index.html"><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="dark:hidden" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--light.png 1x, ../../_next/supabase-logo-wordmark--light.png 2x" src="../../_next/supabase-logo-wordmark--light.png"/><img alt="Supabase Logo" width="160" height="30" decoding="async" data-nimg="1" class="hidden dark:block" style="color:transparent" srcSet="../../_next/supabase-logo-wordmark--dark.png 1x, ../../_next/supabase-logo-wordmark--dark.png 2x" src="../../_next/supabase-logo-wordmark--dark.png"/></a><div class="flex space-x-5"><a href="https://twitter.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Twitter</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill="currentColor" d="M12.6009 0.903908H15.0544L9.69434 7.03008L16 15.3664H11.0627L7.19566 10.3105L2.77087 15.3664H0.31595L6.04904 8.81379L0 0.903908H5.06262L8.55811 5.52524L12.6009 0.903908ZM11.7399 13.8979H13.0993L4.32392 2.29528H2.86506L11.7399 13.8979Z"></path></svg></div></a><a href="https://github.com/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">GitHub</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.5816 0 0 3.58719 0 8.01357C0 11.5535 2.292 14.5575 5.4712 15.6167C5.8712 15.6903 6.0168 15.4431 6.0168 15.2303C6.0168 15.0407 6.0104 14.5359 6.0064 13.8679C3.7808 14.3519 3.3112 12.7935 3.3112 12.7935C2.948 11.8671 2.4232 11.6207 2.4232 11.6207C1.6968 11.1247 2.4784 11.1343 2.4784 11.1343C3.2808 11.1903 3.7032 11.9599 3.7032 11.9599C4.4168 13.1839 5.576 12.8303 6.0312 12.6255C6.1048 12.1079 6.3112 11.7551 6.54 11.5551C4.764 11.3527 2.896 10.6647 2.896 7.59438C2.896 6.71998 3.208 6.00398 3.7192 5.44398C3.6368 5.24158 3.3624 4.42639 3.7976 3.32399C3.7976 3.32399 4.4696 3.10799 5.9976 4.14479C6.65022 3.9668 7.32355 3.87614 8 3.87519C8.68 3.87839 9.364 3.96719 10.0032 4.14479C11.5304 3.10799 12.2008 3.32319 12.2008 3.32319C12.6376 4.42639 12.3624 5.24158 12.2808 5.44398C12.7928 6.00398 13.1032 6.71998 13.1032 7.59438C13.1032 10.6727 11.232 11.3503 9.4504 11.5487C9.73762 11.7959 9.99282 12.2847 9.99282 13.0327C9.99282 14.1031 9.98322 14.9679 9.98322 15.2303C9.98322 15.4447 10.1272 15.6943 10.5336 15.6159C12.1266 15.0816 13.5115 14.0602 14.4924 12.696C15.4733 11.3318 16.0007 9.69382 16 8.01357C16 3.58719 12.4176 0 8 0Z" fill="currentColor"></path></svg></div></a><a href="https://discord.supabase.com/" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Discord</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5447 3.01094C12.5249 2.54302 11.4313 2.19828 10.2879 2.00083C10.2671 1.99702 10.2463 2.00654 10.2356 2.02559C10.0949 2.27573 9.93921 2.60206 9.83011 2.85856C8.60028 2.67444 7.3768 2.67444 6.17222 2.85856C6.06311 2.59636 5.90166 2.27573 5.76038 2.02559C5.74966 2.00717 5.72887 1.99765 5.70803 2.00083C4.56527 2.19764 3.47171 2.54239 2.45129 3.01094C2.44246 3.01475 2.43488 3.0211 2.42986 3.02935C0.355594 6.12826 -0.212633 9.151 0.06612 12.1362C0.067381 12.1508 0.0755799 12.1648 0.0869319 12.1737C1.45547 13.1787 2.78114 13.7889 4.08219 14.1933C4.10301 14.1996 4.12507 14.192 4.13832 14.1749C4.44608 13.7546 4.72043 13.3114 4.95565 12.8454C4.96953 12.8181 4.95628 12.7857 4.92791 12.7749C4.49275 12.6099 4.0784 12.4086 3.67982 12.18C3.64829 12.1616 3.64577 12.1165 3.67477 12.095C3.75865 12.0321 3.84255 11.9667 3.92264 11.9007C3.93713 11.8886 3.95732 11.8861 3.97435 11.8937C6.59287 13.0892 9.42771 13.0892 12.0153 11.8937C12.0323 11.8854 12.0525 11.888 12.0677 11.9C12.1478 11.9661 12.2316 12.0321 12.3161 12.095C12.3451 12.1165 12.3433 12.1616 12.3117 12.18C11.9131 12.413 11.4988 12.6099 11.063 12.7743C11.0346 12.7851 11.022 12.8181 11.0359 12.8454C11.2762 13.3108 11.5505 13.7539 11.8526 14.1742C11.8652 14.192 11.8879 14.1996 11.9087 14.1933C13.2161 13.7889 14.5417 13.1787 15.9103 12.1737C15.9223 12.1648 15.9298 12.1515 15.9311 12.1369C16.2647 8.6856 15.3723 5.68765 13.5655 3.02998C13.5611 3.0211 13.5535 3.01475 13.5447 3.01094ZM5.34668 10.3185C4.55833 10.3185 3.90876 9.59478 3.90876 8.70593C3.90876 7.81707 4.54574 7.09331 5.34668 7.09331C6.15393 7.09331 6.79722 7.82342 6.7846 8.70593C6.7846 9.59478 6.14762 10.3185 5.34668 10.3185ZM10.6632 10.3185C9.87481 10.3185 9.22527 9.59478 9.22527 8.70593C9.22527 7.81707 9.86221 7.09331 10.6632 7.09331C11.4704 7.09331 12.1137 7.82342 12.1011 8.70593C12.1011 9.59478 11.4704 10.3185 10.6632 10.3185Z" fill="currentColor"></path></svg></div></a><a href="https://youtube.com/c/supabase" class="text-foreground-lighter hover:text-foreground transition"><span class="sr-only">Youtube</span><div class="relative" style="width:22px;height:22px"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="none" stroke="none" class="sbui-icon" width="100%" height="100%"><path fill-rule="evenodd" clip-rule="evenodd" d="M15.6657 4.13526C15.4817 3.44239 14.9395 2.8967 14.2511 2.71152C13.0033 2.37502 8 2.37502 8 2.37502C8 2.37502 2.99669 2.37502 1.74891 2.71152C1.06052 2.89673 0.518351 3.44239 0.334337 4.13526C1.90735e-06 5.39112 1.90735e-06 8.01137 1.90735e-06 8.01137C1.90735e-06 8.01137 1.90735e-06 10.6316 0.334337 11.8875C0.518351 12.5804 1.06052 13.1033 1.74891 13.2885C2.99669 13.625 8 13.625 8 13.625C8 13.625 13.0033 13.625 14.2511 13.2885C14.9395 13.1033 15.4817 12.5804 15.6657 11.8875C16 10.6316 16 8.01137 16 8.01137C16 8.01137 16 5.39112 15.6657 4.13526ZM6.36363 10.3904V5.63238L10.5454 8.01143L6.36363 10.3904Z" fill="currentColor"></path></svg></div></a></div></div><div class="mt-12 grid grid-cols-1 gap-8 xl:col-span-5 xl:mt-0"><div class="grid grid-cols-2 gap-4 md:grid-cols-5"><div><h6 class="text-foreground overwrite text-base">Product</h6><ul class="mt-4 space-y-2"><li><a href="../../database.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Database</div></a></li><li><a href="../../auth.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Auth</div></a></li><li><a href="../../edge-functions.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Functions</div></a></li><li><a href="../../realtime.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Realtime</div></a></li><li><a href="../../storage.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Storage</div></a></li><li><a href="../../modules/vector.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Vector</div></a></li><li><a href="../../modules/cron.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Cron</div></a></li><li><a href="../../pricing.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Pricing</div></a></li><li><a href="../../launch-week.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Launch Week</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Solutions</h6><ul class="mt-4 space-y-2"><li><a href="../../solutions/ai-builders.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">AI Builders</div></a></li><li><a href="../../solutions/no-code.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">No Code</div></a></li><li><a href="../../solutions/beginners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Beginners</div></a></li><li><a href="../../solutions/developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Developers</div></a></li><li><a href="../../solutions/postgres-developers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Postgres Devs</div></a></li><li><a href="../../solutions/switch-from-neon.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Switch From Neon</div></a></li><li><a href="../../solutions/startups.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Startups</div></a></li><li><a href="../../solutions/enterprise.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Enterprise</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Resources</h6><ul class="mt-4 space-y-2"><li><a href="../../blog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Blog</div></a></li><li><a href="../../support.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support</div></a></li><li><a href="https://status.supabase.com/"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">System Status</div></a></li><li><a href="../../partners.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Become a Partner</div></a></li><li><a href="../integrations.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Integrations</div></a></li><li><a href="../../brand-assets.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Brand Assets</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security &amp; Compliance</div></a></li><li><a href="../../legal/dpa.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DPA</div></a></li><li><a href="../../security.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SOC2</div></a></li><li><a href="https://forms.supabase.com/hipaa2"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">HIPAA</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Developers</h6><ul class="mt-4 space-y-2"><li><a href="../../docs.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Documentation</div></a></li><li><a href="../../ui.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Supabase UI</div></a></li><li><a href="../../changelog.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Changelog</div></a></li><li><a href="../../careers.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Careers</div></a></li><li><a href="https://github.com/supabase/supabase/blob/master/CONTRIBUTING.md"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Contributing</div></a></li><li><a href="../../open-source.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Open Source</div></a></li><li><a href="../../open-source/contributing/supasquad.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">SupaSquad</div></a></li><li><a href="https://dev.to/supabase"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">DevTo</div></a></li><li><a href="../../rss.xml"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">RSS</div></a></li></ul></div><div><h6 class="text-foreground overwrite text-base">Company</h6><ul class="mt-4 space-y-2"><li><a href="../../company.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Company</div></a></li><li><a href="../../ga.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">General Availability</div></a></li><li><a href="../../terms.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Terms of Service</div></a></li><li><a href="../../privacy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Policy</div></a></li><li><button><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Privacy Settings</div></button></li><li><a href="../../aup.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Acceptable Use Policy</div></a></li><li><a href="../../support-policy.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Support Policy</div></a></li><li><a href="../../sla.html"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Service Level Agreement</div></a></li><li><a href="../../humans.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Humans.txt</div></a></li><li><a href="../../lawyers.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Lawyers.txt</div></a></li><li><a href="../../well-known/security.txt"><div class="text-sm transition-colors text-foreground-lighter hover:text-foreground ">Security.txt</div></a></li></ul></div></div></div></div><div class="border-default mt-32 flex justify-between border-t pt-8"><small class="small">© Supabase Inc</small><div class=""></div></div></div></footer></div><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"partner":{"id":126,"slug":"loops","type":"technology","category":"Messaging","developer":"Loops","title":"Loops","description":"Configure your Supabase account to send authentication emails with Loops.","logo":"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/logo.png?t=2024-11-18T09%3A19%3A41.390Z","images":null,"overview":"Loops is an email platform for software companies letting you send your product, marketing, and transactional email from one simple interface.\n\nThis integration allows you to send Supabase Authentication emails over Loops' SMTP service.\n\nThere are two big benefits to using Loops for sending your Supabase emails:\n\n- **More control over design**: You can use [Loops' design editor](https://loops.so/docs/creating-emails/editor) to create (and then easily edit) beautiful transactional emails instead of having to code them with HTML.\n- **Better visibility of sent email**: You get full visibility on which emails are being sent, when, and to whom in your Loops account (something not offered in Supabase).\n\n### 1. Set up Loops SMTP in Supabase\n\nGo to your Authentication settings in Supabase (**Project Settings -\u003e Authentication**) to tell Supabase to send emails using Loops' SMTP service.\n\nScroll down and toggle the **Enable Custom SMTP** option on.\n\nIn the Sender details section, you will need to enter some values into the \"Sender email\" and \"Sender name\" fields. However, _these values will always be overwritten by the values set in your Loops templates_ from the next step.\n\nIn the **SMTP Provider Settings** section enter the following data:\n\n| Field       | Value                                                                                       |\n| ----------- | ------------------------------------------------------------------------------------------- |\n| Host        | `smtp.loops.so`                                                                             |\n| Port number | `587`                                                                                       |\n| Username    | `loops`                                                                                     |\n| Password    | An API key copied from your [API settings](https://app.loops.so/settings?page=api) in Loops |\n\n![](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/supabase-smtp-settings.png)\n\nNote that for the interval and rate limit settings, you will be bound by Loops' [API rate limit](https://loops.so/docs/api-reference/intro#rate-limiting) of 10 requests per second.\n\nOne final step is to check that the \"Confirm email\" toggle is turned on in the Email section in **Authentication -\u003e Providers**.\n\n### 2. Create Transactional emails in Loops\n\nNext, create new transactional emails for the emails listed in Supabase (**Authentication -\u003e Email Templates**). You need to create both **Confirm signup** and **Magic Link** emails to be able to properly set up the integration.\n\n- Confirm signup (required)\n- Invite user\n- Magic Link (required)\n- Change Email Address\n- Reset Password\n\nNote that if a Supabase user has not previously confirmed their email, they will be sent a **Confirm signup** email when you request a **Magic Link** email.\n\nTo create new transactional emails, go to the [Transactional page](https://app.loops.so/transactional) in Loops and click **New**. Alternatively, you can select one of our many ready-made templates from the [Templates page](https://app.loops.so/templates).\n\n![Supabase template in the editor](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/supabase-template.png)\n\nYou can then use [the Loops editor](https://loops.so/docs/creating-emails/editor) to create nicely-designed templates or make them as simple as you like.\n\nYou can even [save styles](https://loops.so/docs/creating-emails/styles#saved-styles) so you can easily apply consistent branding to all of your emails.\n\n![Saved styles](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/supabase-editor.png)\n\nFor each Loops template you create, you need to [add data variables](https://loops.so/docs/creating-emails/personalizing-emails#add-dynamic-content-to-emails), which allow data from Supabase to be inserted into each email.\n\nFor example, you could add a `confirmationUrl` data variable that you can map to the `{{ .ConfirmationURL }}` value from Supabase.\n\nYou can also build URLs by including values like `{{ .SiteUrl }}` or add in a confirmation code using `{{ .Token }}`.\n\n![Supabase values](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/supabase-values.png)\n\nOnce you're done creating the email and adding data variables, click **Next**. On the next page, click the **Show payload** button to view the API payload for your template. You will need this for the next step.\n\n![Email payload](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/supabase-payload.png)\n\nMake sure to also publish your email! It won't send unless it's published.\n\n[Read our detailed guide](https://loops.so/docs/transactional/guide) for sending transactional emails.\n\n### 3. Configure email templates in Supabase\n\nThe final step is to make sure your emails in Supabase are configured to send the correct data to Loops.\n\nLoops SMTP integrations work a bit differently than most. Instead of sending a text or HTML email body, you set them up to send API-like data.\n\nIn Supabase, go to **Authentication -\u003e Email Templates**, then edit each template to contain the payload as shown in the previous step (you can click the clipboard icon in Loops to copy the full payload).\n\nOnce pasted into the Message body, you need to add the Supabase message variables into the payload.\n\nImportant: Make sure you set up at least the **Confirm signup** and **Magic Link** templates in Supabase, otherwise emails will not be sent.\n\nAlso, any variables added in the **Confirm signup** template need to also be available in **Magic link** email, because Supabase will send a **Confirm signup** email instead of a **Magic Link** email if a user hasn't confirmed their email address.\n\nHere is an example **Confirm signup** email template. This payload was copied from the template's Publish page in Loops, then the `{{ .Email }}` and `{{ .ConfirmationURL }}` Supabase variables were added.\n\n```json\n{\n  \"transactionalId\": \"clvmzp39u035tl50pw7wrl0ri\",\n  \"email\": \"{{ .Email }}\",\n  \"dataVariables\": {\n    \"confirmationUrl\": \"{{ .ConfirmationURL }}\"\n  }\n}\n```\n\nHere's how it looks in the Supabase editor:\n\n![Supabase editor](https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/supabase-email.png)\n\nWith the integration now all set up, your Supabase authentication emails will be sent via Loops, giving you more visibility on your email sends and the great addition of being able to build beautiful and easy-to-update emails in the Loops editor.\n\nTo view all sends of your transactional emails, click through to the email from the [Transactional](https://app.loops.so/transactional) page in Loops, where you'll find the Metrics page containing a table showing all sends and some statistics.\n\n## Important notes\n\n- You need to add a template in Loops and set up the email in Supabase for at least the **Confirm signup** and **Magic Link** templates.\n- The subject in Supabase templates is always overwritten by the subject added to the corresponding template in Loops.\n- The sender name and sender email configured in your Supabase SMTP settings are always overwritten by the sender details added to your templates in Loops.\n- Any Supabase email not set up with the correct API-like payload will fail to send.\n","website":"https://loops.so","docs":"https://loops.so/docs/integrations/supabase","contact":1,"approved":true,"created_at":"2024-11-15T12:26:32.639216+00:00","tsv":"'/docs/api-reference/intro#rate-limiting)':249C '/docs/creating-emails/editor)':71C,410C '/docs/creating-emails/personalizing-emails#add-dynamic-content-to-emails),':464C '/docs/creating-emails/styles#saved-styles)':432C '/docs/transactional/guide)':582C '/settings?page=api)':223C '/storage/v1/object/public/images/integrations/loops/supabase-editor.png)':449C '/storage/v1/object/public/images/integrations/loops/supabase-email.png)':806C '/storage/v1/object/public/images/integrations/loops/supabase-payload.png)':560C '/storage/v1/object/public/images/integrations/loops/supabase-smtp-settings.png)':228C '/storage/v1/object/public/images/integrations/loops/supabase-template.png)':400C '/storage/v1/object/public/images/integrations/loops/supabase-values.png)':517C '/templates).':392C '/transactional)':368C,869C '1':117C '10':251C '2':276C '3':587C '587':209C 'abl':309C,837C 'account':5B,111C 'ad':526C,718C,784C,927C,953C 'add':459C,480C,506C,684C,894C 'addit':834C 'address':326C,755C 'allow':35C,466C 'also':497C,564C,715C,726C 'altern':374C 'alway':178C,922C,947C 'api':214C,219C,244C,543C,638C,969C 'api-lik':637C,968C 'app.loops.so':222C,367C,391C,868C 'app.loops.so/settings?page=api)':221C 'app.loops.so/templates).':390C 'app.loops.so/transactional)':366C,867C 'appli':437C 'authent':8B,40C,127C,133C,274C,293C,645C,816C 'avail':728C 'beauti':78C,840C 'benefit':50C 'better':89C 'big':49C 'bit':618C 'bodi':630C,680C 'bound':241C 'brand':439C 'build':498C,839C 'button':539C 'chang':324C 'check':260C 'click':372C,529C,535C,664C,859C 'clipboard':666C 'clvmzp39u035tl50pw7wrl0ri':787C 'code':85C,510C 'compani':19C 'configur':2B,588C,605C,940C 'confirm':263C,301C,316C,338C,346C,509C,701C,721C,738C,752C,760C,910C 'confirmationurl':482C,491C,780C,791C,792C 'consist':438C 'contain':653C,880C 'control':60C 'copi':216C,671C,767C 'correct':609C,967C 'correspond':930C 'could':479C 'creat':73C,277C,283C,299C,357C,412C,455C,522C 'custom':150C 'data':202C,460C,467C,483C,527C,610C,640C 'datavari':790C 'design':62C,67C,415C 'detail':157C,578C,952C 'differ':619C 'done':521C 'easi':843C 'easili':76C,436C 'easy-to-upd':842C 'edit':77C,649C 'editor':68C,397C,407C,801C,803C,850C 'email':9B,15C,28C,41C,58C,80C,93C,100C,139C,169C,264C,271C,279C,286C,289C,294C,306C,325C,340C,348C,355C,360C,444C,475C,524C,556C,567C,586C,589C,601C,629C,646C,710C,732C,740C,746C,754C,762C,778C,788C,789C,817C,829C,846C,858C,863C,903C,939C,961C 'enabl':149C 'enter':163C,199C 'even':427C 'exampl':477C,759C 'fail':973C 'field':173C,203C 'final':256C,594C 'find':876C 'follow':201C 'full':96C,673C 'get':95C 'give':823C 'go':124C,361C,643C 'great':833C 'guid':579C 'hasn':750C 'host':205C 'howev':174C 'html':88C,628C 'icon':667C 'import':692C,889C 'includ':501C 'insert':472C 'instead':81C,622C,741C 'integr':34C,315C,615C,809C 'interfac':32C 'interv':233C 'invit':319C 'json':785C 'key':215C 'least':699C,908C 'let':20C 'like':424C,503C,639C,970C 'limit':236C,246C 'link':305C,322C,354C,705C,731C,745C,914C 'list':290C 'll':875C 'look':797C 'loop':1A,11B,12C,43C,53C,66C,110C,120C,141C,187C,211C,225C,243C,281C,370C,406C,452C,612C,613C,669C,775C,822C,849C,872C,898C,933C,958C,977 'loops.so':70C,248C,409C,431C,463C,581C 'loops.so/docs/api-reference/intro#rate-limiting)':247C 'loops.so/docs/creating-emails/editor)':69C,408C 'loops.so/docs/creating-emails/personalizing-emails#add-dynamic-content-to-emails),':462C 'loops.so/docs/creating-emails/styles#saved-styles)':430C 'loops.so/docs/transactional/guide)':580C 'made':384C 'magic':304C,321C,353C,704C,730C,744C,913C 'make':418C,561C,598C,693C 'mani':381C 'map':488C 'market':25C 'messag':679C,687C,976 'metric':878C 'name':172C,936C 'need':161C,297C,457C,550C,682C,724C,892C 'new':284C,358C,373C 'next':191C,282C,530C,533C,554C 'nice':414C 'nicely-design':413C 'note':229C,329C,890C 'number':208C 'obuldanrptloktxcffvn.supabase.co':227C,399C,448C,516C,559C,805C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/supabase-editor.png)':447C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/supabase-email.png)':804C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/supabase-payload.png)':558C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/supabase-smtp-settings.png)':226C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/supabase-template.png)':398C 'obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/supabase-values.png)':515C 'offer':114C 'one':30C,255C,378C 'option':152C 'otherwis':709C 'overwritten':180C,923C,948C 'page':365C,389C,534C,773C,870C,879C 'password':212C,328C 'past':676C 'payload':538C,544C,557C,655C,674C,691C,765C,971C 'per':253C 'platform':16C 'port':207C 'previous':337C,660C 'product':24C 'project':131C 'proper':311C 'provid':196C,275C 'publish':565C,575C,772C 'rate':235C,245C 're':520C 'read':576C 'readi':383C 'ready-mad':382C 'request':252C,351C 'requir':318C,323C 'reset':327C 'save':428C,445C 'scroll':144C 'second':254C 'section':158C,198C,272C 'select':377C 'send':7B,22C,38C,55C,138C,571C,584C,607C,624C,636C,736C,830C,854C,885C,975C 'sender':156C,168C,171C,935C,938C,951C 'sent':92C,103C,344C,714C,820C 'servic':45C,143C 'set':118C,128C,132C,184C,197C,220C,237C,312C,632C,696C,812C,900C,945C,963C 'show':537C,883C 'shown':657C 'signup':302C,317C,347C,702C,722C,739C,761C,911C 'simpl':31C,421C 'siteurl':504C 'smtp':44C,121C,142C,151C,195C,614C,944C 'smtp.loops.so':206C 'softwar':18C 'someth':112C 'statist':888C 'step':192C,257C,555C,595C,661C 'style':429C,446C 'subject':917C,926C 'supabas':4B,39C,57C,116C,123C,130C,136C,292C,333C,393C,469C,494C,513C,592C,603C,642C,686C,708C,734C,781C,800C,802C,815C,905C,919C,943C,960C 'sure':562C,599C,694C 'tabl':882C 'tell':135C 'templat':188C,295C,385C,388C,394C,416C,453C,547C,590C,647C,651C,706C,723C,763C,770C,896C,915C,920C,931C,956C 'text':626C 'toggl':147C,265C 'token':512C 'transact':27C,79C,278C,285C,359C,364C,585C,857C,866C 'transactionalid':786C 'turn':267C 'two':48C 'unless':572C 'updat':845C 'url':499C 'use':52C,65C,140C,404C,511C 'user':320C,334C,749C 'usernam':210C 'valu':165C,176C,183C,204C,492C,502C,514C 'variabl':461C,484C,528C,688C,717C,782C 'via':821C 'view':541C,852C 'visibl':90C,97C,826C 'won':569C 'work':616C","video":null,"call_to_action_link":null,"featured":false},"overview":{"compiledSource":"/*@jsxRuntime automatic @jsxImportSource react*/\nconst {Fragment: _Fragment, jsx: _jsx, jsxs: _jsxs} = arguments[0];\nconst {useMDXComponents: _provideComponents} = arguments[0];\nconst chCodeConfig = {\n  \"staticMediaQuery\": \"not screen, (max-width: 768px)\",\n  \"lineNumbers\": true,\n  \"showCopyButton\": true,\n  \"themeName\": \"supabase\"\n};\nfunction _createMdxContent(props) {\n  const _components = Object.assign({\n    p: \"p\",\n    ul: \"ul\",\n    li: \"li\",\n    strong: \"strong\",\n    a: \"a\",\n    h3: \"h3\",\n    em: \"em\",\n    table: \"table\",\n    thead: \"thead\",\n    tr: \"tr\",\n    th: \"th\",\n    tbody: \"tbody\",\n    td: \"td\",\n    code: \"code\",\n    img: \"img\",\n    h2: \"h2\"\n  }, _provideComponents(), props.components), {CH} = _components;\n  if (!CH) _missingMdxReference(\"CH\", false);\n  if (!CH.Code) _missingMdxReference(\"CH.Code\", true);\n  return _jsxs(_Fragment, {\n    children: [_jsx(\"style\", {\n      dangerouslySetInnerHTML: {\n        __html: \"[data-ch-theme=\\\"supabase\\\"] {  --ch-t-colorScheme: var(--ch-0);--ch-t-foreground: var(--ch-4);--ch-t-background: var(--ch-16);--ch-t-editor-background: var(--ch-16);--ch-t-editor-foreground: var(--ch-4);--ch-t-editor-rangeHighlightBackground: var(--ch-19);--ch-t-editor-infoForeground: var(--ch-18);--ch-t-editor-selectionBackground: var(--ch-17);--ch-t-tab-activeBackground: var(--ch-16);--ch-t-tab-activeForeground: var(--ch-4);--ch-t-tab-inactiveBackground: var(--ch-21);--ch-t-tab-inactiveForeground: var(--ch-15);--ch-t-tab-border: var(--ch-22);--ch-t-tab-activeBorder: var(--ch-16);--ch-t-editorGroupHeader-tabsBackground: var(--ch-21);--ch-t-editorLineNumber-foreground: var(--ch-20);--ch-t-input-foreground: var(--ch-4);--ch-t-sideBar-foreground: var(--ch-4);--ch-t-list-hoverBackground: var(--ch-25);--ch-t-list-hoverForeground: var(--ch-4); }\"\n      }\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Loops is an email platform for software companies letting you send your product, marketing, and transactional email from one simple interface.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"This integration allows you to send Supabase Authentication emails over Loops' SMTP service.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"There are two big benefits to using Loops for sending your Supabase emails:\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"More control over design\"\n        }), \": You can use \", _jsx(_components.a, {\n          href: \"https://loops.so/docs/creating-emails/editor\",\n          children: \"Loops' design editor\"\n        }), \" to create (and then easily edit) beautiful transactional emails instead of having to code them with HTML.\"]\n      }), \"\\n\", _jsxs(_components.li, {\n        children: [_jsx(_components.strong, {\n          children: \"Better visibility of sent email\"\n        }), \": You get full visibility on which emails are being sent, when, and to whom in your Loops account (something not offered in Supabase).\"]\n      }), \"\\n\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"1. Set up Loops SMTP in Supabase\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Go to your Authentication settings in Supabase (\", _jsx(_components.strong, {\n        children: \"Project Settings -\u003e Authentication\"\n      }), \") to tell Supabase to send emails using Loops' SMTP service.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Scroll down and toggle the \", _jsx(_components.strong, {\n        children: \"Enable Custom SMTP\"\n      }), \" option on.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In the Sender details section, you will need to enter some values into the \\\"Sender email\\\" and \\\"Sender name\\\" fields. However, \", _jsx(_components.em, {\n        children: \"these values will always be overwritten by the values set in your Loops templates\"\n      }), \" from the next step.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In the \", _jsx(_components.strong, {\n        children: \"SMTP Provider Settings\"\n      }), \" section enter the following data:\"]\n    }), \"\\n\", _jsxs(_components.table, {\n      children: [_jsx(_components.thead, {\n        children: _jsxs(_components.tr, {\n          children: [_jsx(_components.th, {\n            children: \"Field\"\n          }), _jsx(_components.th, {\n            children: \"Value\"\n          })]\n        })\n      }), _jsxs(_components.tbody, {\n        children: [_jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"Host\"\n          }), _jsx(_components.td, {\n            children: _jsx(_components.code, {\n              children: \"smtp.loops.so\"\n            })\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"Port number\"\n          }), _jsx(_components.td, {\n            children: _jsx(_components.code, {\n              children: \"587\"\n            })\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"Username\"\n          }), _jsx(_components.td, {\n            children: _jsx(_components.code, {\n              children: \"loops\"\n            })\n          })]\n        }), _jsxs(_components.tr, {\n          children: [_jsx(_components.td, {\n            children: \"Password\"\n          }), _jsxs(_components.td, {\n            children: [\"An API key copied from your \", _jsx(_components.a, {\n              href: \"https://app.loops.so/settings?page=api\",\n              children: \"API settings\"\n            }), \" in Loops\"]\n          })]\n        })]\n      })]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/supabase-smtp-settings.png\",\n        alt: \"\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Note that for the interval and rate limit settings, you will be bound by Loops' \", _jsx(_components.a, {\n        href: \"https://loops.so/docs/api-reference/intro#rate-limiting\",\n        children: \"API rate limit\"\n      }), \" of 10 requests per second.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"One final step is to check that the \\\"Confirm email\\\" toggle is turned on in the Email section in \", _jsx(_components.strong, {\n        children: \"Authentication -\u003e Providers\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"2. Create Transactional emails in Loops\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Next, create new transactional emails for the emails listed in Supabase (\", _jsx(_components.strong, {\n        children: \"Authentication -\u003e Email Templates\"\n      }), \"). You need to create both \", _jsx(_components.strong, {\n        children: \"Confirm signup\"\n      }), \" and \", _jsx(_components.strong, {\n        children: \"Magic Link\"\n      }), \" emails to be able to properly set up the integration.\"]\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsx(_components.li, {\n        children: \"Confirm signup (required)\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Invite user\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Magic Link (required)\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Change Email Address\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Reset Password\"\n      }), \"\\n\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Note that if a Supabase user has not previously confirmed their email, they will be sent a \", _jsx(_components.strong, {\n        children: \"Confirm signup\"\n      }), \" email when you request a \", _jsx(_components.strong, {\n        children: \"Magic Link\"\n      }), \" email.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To create new transactional emails, go to the \", _jsx(_components.a, {\n        href: \"https://app.loops.so/transactional\",\n        children: \"Transactional page\"\n      }), \" in Loops and click \", _jsx(_components.strong, {\n        children: \"New\"\n      }), \". Alternatively, you can select one of our many ready-made templates from the \", _jsx(_components.a, {\n        href: \"https://app.loops.so/templates\",\n        children: \"Templates page\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/supabase-template.png\",\n        alt: \"Supabase template in the editor\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You can then use \", _jsx(_components.a, {\n        href: \"https://loops.so/docs/creating-emails/editor\",\n        children: \"the Loops editor\"\n      }), \" to create nicely-designed templates or make them as simple as you like.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You can even \", _jsx(_components.a, {\n        href: \"https://loops.so/docs/creating-emails/styles#saved-styles\",\n        children: \"save styles\"\n      }), \" so you can easily apply consistent branding to all of your emails.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/supabase-editor.png\",\n        alt: \"Saved styles\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"For each Loops template you create, you need to \", _jsx(_components.a, {\n        href: \"https://loops.so/docs/creating-emails/personalizing-emails#add-dynamic-content-to-emails\",\n        children: \"add data variables\"\n      }), \", which allow data from Supabase to be inserted into each email.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"For example, you could add a \", _jsx(_components.code, {\n        children: \"confirmationUrl\"\n      }), \" data variable that you can map to the \", _jsx(_components.code, {\n        children: \"{{ .ConfirmationURL }}\"\n      }), \" value from Supabase.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"You can also build URLs by including values like \", _jsx(_components.code, {\n        children: \"{{ .SiteUrl }}\"\n      }), \" or add in a confirmation code using \", _jsx(_components.code, {\n        children: \"{{ .Token }}\"\n      }), \".\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/supabase-values.png\",\n        alt: \"Supabase values\"\n      })\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Once you're done creating the email and adding data variables, click \", _jsx(_components.strong, {\n        children: \"Next\"\n      }), \". On the next page, click the \", _jsx(_components.strong, {\n        children: \"Show payload\"\n      }), \" button to view the API payload for your template. You will need this for the next step.\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/supabase-payload.png\",\n        alt: \"Email payload\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Make sure to also publish your email! It won't send unless it's published.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [_jsx(_components.a, {\n        href: \"https://loops.so/docs/transactional/guide\",\n        children: \"Read our detailed guide\"\n      }), \" for sending transactional emails.\"]\n    }), \"\\n\", _jsx(_components.h3, {\n      children: \"3. Configure email templates in Supabase\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"The final step is to make sure your emails in Supabase are configured to send the correct data to Loops.\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Loops SMTP integrations work a bit differently than most. Instead of sending a text or HTML email body, you set them up to send API-like data.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"In Supabase, go to \", _jsx(_components.strong, {\n        children: \"Authentication -\u003e Email Templates\"\n      }), \", then edit each template to contain the payload as shown in the previous step (you can click the clipboard icon in Loops to copy the full payload).\"]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Once pasted into the Message body, you need to add the Supabase message variables into the payload.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Important: Make sure you set up at least the \", _jsx(_components.strong, {\n        children: \"Confirm signup\"\n      }), \" and \", _jsx(_components.strong, {\n        children: \"Magic Link\"\n      }), \" templates in Supabase, otherwise emails will not be sent.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Also, any variables added in the \", _jsx(_components.strong, {\n        children: \"Confirm signup\"\n      }), \" template need to also be available in \", _jsx(_components.strong, {\n        children: \"Magic link\"\n      }), \" email, because Supabase will send a \", _jsx(_components.strong, {\n        children: \"Confirm signup\"\n      }), \" email instead of a \", _jsx(_components.strong, {\n        children: \"Magic Link\"\n      }), \" email if a user hasn't confirmed their email address.\"]\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"Here is an example \", _jsx(_components.strong, {\n        children: \"Confirm signup\"\n      }), \" email template. This payload was copied from the template's Publish page in Loops, then the \", _jsx(_components.code, {\n        children: \"{{ .Email }}\"\n      }), \" and \", _jsx(_components.code, {\n        children: \"{{ .ConfirmationURL }}\"\n      }), \" Supabase variables were added.\"]\n    }), \"\\n\", _jsx(CH.Code, {\n      codeConfig: chCodeConfig,\n      northPanel: {\n        \"tabs\": [\"\"],\n        \"active\": \"\",\n        \"heightRatio\": 1\n      },\n      files: [{\n        \"name\": \"\",\n        \"focus\": \"\",\n        \"code\": {\n          \"lines\": [{\n            \"tokens\": [{\n              \"content\": \"{\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"transactionalId\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"clvmzp39u035tl50pw7wrl0ri\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"email\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"{{ .Email }}\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }, {\n              \"content\": \",\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  \\\"dataVariables\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": {\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"    \\\"confirmationUrl\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-6)\"\n                }\n              }\n            }, {\n              \"content\": \": \",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }, {\n              \"content\": \"\\\"{{ .ConfirmationURL }}\\\"\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-8)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"  }\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }, {\n            \"tokens\": [{\n              \"content\": \"}\",\n              \"props\": {\n                \"style\": {\n                  \"color\": \"var(--ch-4)\"\n                }\n              }\n            }]\n          }],\n          \"lang\": \"json\"\n        },\n        \"annotations\": []\n      }]\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"Here's how it looks in the Supabase editor:\"\n    }), \"\\n\", _jsx(_components.p, {\n      children: _jsx(_components.img, {\n        src: \"https://obuldanrptloktxcffvn.supabase.co/storage/v1/object/public/images/integrations/loops/supabase-email.png\",\n        alt: \"Supabase editor\"\n      })\n    }), \"\\n\", _jsx(_components.p, {\n      children: \"With the integration now all set up, your Supabase authentication emails will be sent via Loops, giving you more visibility on your email sends and the great addition of being able to build beautiful and easy-to-update emails in the Loops editor.\"\n    }), \"\\n\", _jsxs(_components.p, {\n      children: [\"To view all sends of your transactional emails, click through to the email from the \", _jsx(_components.a, {\n        href: \"https://app.loops.so/transactional\",\n        children: \"Transactional\"\n      }), \" page in Loops, where you'll find the Metrics page containing a table showing all sends and some statistics.\"]\n    }), \"\\n\", _jsx(_components.h2, {\n      children: \"Important notes\"\n    }), \"\\n\", _jsxs(_components.ul, {\n      children: [\"\\n\", _jsxs(_components.li, {\n        children: [\"You need to add a template in Loops and set up the email in Supabase for at least the \", _jsx(_components.strong, {\n          children: \"Confirm signup\"\n        }), \" and \", _jsx(_components.strong, {\n          children: \"Magic Link\"\n        }), \" templates.\"]\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"The subject in Supabase templates is always overwritten by the subject added to the corresponding template in Loops.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"The sender name and sender email configured in your Supabase SMTP settings are always overwritten by the sender details added to your templates in Loops.\"\n      }), \"\\n\", _jsx(_components.li, {\n        children: \"Any Supabase email not set up with the correct API-like payload will fail to send.\"\n      }), \"\\n\"]\n    })]\n  });\n}\nfunction MDXContent(props = {}) {\n  const {wrapper: MDXLayout} = Object.assign({}, _provideComponents(), props.components);\n  return MDXLayout ? _jsx(MDXLayout, Object.assign({}, props, {\n    children: _jsx(_createMdxContent, props)\n  })) : _createMdxContent(props);\n}\nreturn {\n  chCodeConfig,\n  default: MDXContent\n};\nfunction _missingMdxReference(id, component) {\n  throw new Error(\"Expected \" + (component ? \"component\" : \"object\") + \" `\" + id + \"` to be defined: you likely forgot to import, pass, or provide it.\");\n}\n","frontmatter":{},"scope":{}}},"__N_SSG":true},"page":"/partners/integrations/[slug]","query":{"slug":"loops"},"buildId":"kQ-hlwlsb4A882RHzOZEr","assetPrefix":"https://frontend-assets.supabase.com/www/0c0be801ae1d","isFallback":false,"isExperimentalCompile":false,"gsp":true,"scriptLoader":[]}</script></body></html>